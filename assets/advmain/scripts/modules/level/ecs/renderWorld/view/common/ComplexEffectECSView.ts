import { ECSViewBase, IECSViewConfig } from '../../ECSViewBase';

/**特效视图配置 */
export interface IEffectViewConfig extends IECSViewConfig {
    /**特效包名 */
    readonly effectBundleName: string;
    /**特效路径 */
    readonly effectPath: string;
    /**特效启动动作名 */
    readonly startAnimationName: string;
    /**特效结束动作名 */
    readonly endAnimationName: string;
    /**特效动作名 */
    readonly animationName: string;
    /**播放次数 0为无限循环 */
    readonly playTimes: number;
}
/**特效视图-用于特效的渲染 */
export class ComplexEffectECSView extends ECSViewBase<IEffectViewConfig> {
    public effect: dragonBones.ArmatureDisplay = null;
    protected async initView(): Promise<void> {
        await this.loadEffect();
        if (this.config.startAnimationName) {
            this.effect.playAnimation(this.config.startAnimationName, 1);
        } else if (this.config.animationName) {
            this.effect.playAnimation(this.config.animationName, this.config.playTimes);
        } else {
            console.error(`特效视图配置错误，没有配置动画名:ecsViewId:${this.config.ecsViewId}`);
        }
    }
    protected addEvents(): void {
        this.effect.addEventListener(dragonBones.EventObject.COMPLETE, this.__playComplete, this);
    }
    protected removeEvents(): void {
        this.effect.removeEventListener(dragonBones.EventObject.COMPLETE, this.__playComplete, this);
    }
    private async loadEffect() {
        const res: dragonBones.DragonBonesAsset = await this.renderWorld.loadRes(
            this.config.effectBundleName,
            this.config.effectPath,
            dragonBones.DragonBonesAsset,
        );
        this.effect = this.node.getComponent(dragonBones.ArmatureDisplay);
        this.effect.dragonAsset = res;
    }
    private __playComplete() {
        if (this.effect.animationName === this.config.startAnimationName) {
            this.effect.playAnimation(this.config.animationName, this.config.playTimes);
        } else if (this.effect.animationName === this.config.endAnimationName && this.config.endAnimationName) {
            this.effect.playAnimation(this.config.endAnimationName, 1);
        } else {
            this.renderWorld.destroyECSView(this);
        }
    }
    protected disposeView() {
        console.log('特效视图已经被销毁');
    }
}
