import { ECSViewBase } from '../../ECSViewBase';

export default class DiamondECSView extends ECSViewBase {
    protected initView() {
        this.fresh();
    }
    protected addEvents() {
        // this.logicWorld.onComponentChange(this.logicEntityId, HPComponent, this.onHpChange, this);
    }
    protected removeEvents() {
        // this.logicWorld.offComponentChange(this.logicEntityId, HPComponent, this.onHpChange, this);
    }
    protected disposeView() {}
    private fresh() {
        // this.renderWorld
        //     .loadRes('level', `cell/120/img/icon${this.logicWorld.getComponent(this.logicEntityId, HPComponent).hp}`, cc.SpriteFrame)
        //     .then((spf: cc.SpriteFrame) => {
        //         if (!cc.isValid(this.node)) return;
        //         this.ui.icon.getComponent(cc.Sprite).spriteFrame = spf;
        //     });
    }
}
