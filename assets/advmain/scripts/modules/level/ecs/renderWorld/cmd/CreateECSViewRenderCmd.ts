import { ECSViewBase } from '../ECSViewBase';
import { IRenderCmdConfig, RenderCmdBase } from '../RenderCmdBase';
/**ECS视图渲染命令配置 */
export interface IEcsViewRenderCmdConfig extends IRenderCmdConfig {
    
}
/**创建ECS视图渲染命令 */
export class CreateECSViewRenderCmd extends RenderCmdBase<IEcsViewRenderCmdConfig> {
    async do() {
        const view: ECSViewBase = await this.renderWorld.createECSView(this.ecsViewId, this.entityId, this.parentEntityId, this.children);
        if (!view) {
            return;
        }
        view.data = this.data;
    }
}
