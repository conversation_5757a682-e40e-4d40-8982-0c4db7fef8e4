import { BoardScene } from '../../components/board/BoardScene';
import ReviveComponent from '../../components/ReviveComponent';
import ProduceBlockAlgorithmComponent from '../../components/special/ProduceBlockAlgorithmComponent';
import { ECSExecuteContext, ECSRuleBase, IECSRuleConfig } from '../core/ECSRuleBase';
import { ECSEvent } from '../../GameEvent';
import { IProduceBlockEnd } from '../../GameEventData';
import { AlgoClassTypeConst } from '../../../../algorithm/list/algoSDK/core/puzzlecore';

export interface IRevivePreviewBlocksRuleConfig extends IECSRuleConfig {
    /**算法ID */
    algorithmId: number;
}

/**
 * 复活预览块规则
 * 在复活弹窗显示前，先计算复活块并缓存结果
 */
export class RevivePreviewBlocksRule extends ECSRuleBase<IRevivePreviewBlocksRuleConfig> {

    canExecute(): boolean {
        // 检查复活条件
        const world = this.world;
        const sceneEntity = world.utilCenter.boardUtil.getSceneEntity();
        if (!sceneEntity) return false;

        const reviveComp = world.getComponent(sceneEntity, ReviveComponent);
        if (!reviveComp || reviveComp.nCount <= 0) {
            return false;
        }

        // 检查是否已经有缓存的复活块数据，避免重复计算
        return !reviveComp.cachedReviveBlocks;
    }

    execute(): void {
        const world = this.world;

        try {
            // 标记这是复活预览出块
            const sceneEntity = world.utilCenter.boardUtil.getSceneEntity();
            const reviveComp = world.getComponent(sceneEntity, ReviveComponent);
            reviveComp.isPreviewMode = true;

            // 创建算法组件，触发现有的出块系统
            const entityId = world.createEntity();
            const algorithmId = this.config.algorithmId || AlgoClassTypeConst.algo_random_no_death;
            world.addComponent(entityId, ProduceBlockAlgorithmComponent, algorithmId);

            // 监听出块完成事件，缓存结果
            world.eventBus.once(ECSEvent.BaseEvent.PRODUCE_BLOCK_END, this.onPreviewBlocksGenerated, this);

            console.log('RevivePreviewBlocksRule: 开始预览复活块计算', algorithmId);

        } catch (error) {
            console.error('RevivePreviewBlocksRule: 执行失败', error);

            // 出错时重置预览模式
            const sceneEntity = world.utilCenter.boardUtil.getSceneEntity();
            const reviveComp = world.getComponent(sceneEntity, ReviveComponent);
            if (reviveComp) {
                reviveComp.isPreviewMode = false;
            }
        }
    }

    private onPreviewBlocksGenerated(result: IProduceBlockEnd): void {
        const world = this.world;
        const sceneEntity = world.utilCenter.boardUtil.getSceneEntity();
        const reviveComp = world.getComponent(sceneEntity, ReviveComponent);

        if (!reviveComp) {
            console.error('RevivePreviewBlocksRule: 找不到ReviveComponent');
            return;
        }

        // 缓存复活块数据
        reviveComp.cachedReviveBlocks = result;
        reviveComp.isPreviewMode = false;

        // 触发预览完成事件
        world.eventBus.emit(ECSEvent.GameEvent.REVIVE_PREVIEW_COMPLETE, result);

        console.log('RevivePreviewBlocksRule: 复活块预览完成', result.blockIds);
    }
}
