import { LevelMasterType } from "../../../base/LevelLoader";
import { PrefabConfig } from "../../prefab/PrefabConfig";
import { ECSEvent } from "../ecs/GameEvent";
import { E_Levels_EnterLevel } from "../events/E_Levels_EnterLevel";
import { E_Levels_ProduceBlockEnd } from "../events/E_Levels_ProduceBlockEnd";
import { game } from "../Game";
import { LevelRoot } from '../component/LevelRoot';
import { SubGameBridge } from "../../../base/SubGameBridge";
import { Loader } from "../../../base/Loader";

export class Level_Proxy extends falcon.Proxy {
    registerEvents(): Array<new (...args: any[]) => falcon.ModuleEvent> | null {
        return [
            E_Levels_EnterLevel,
            E_Levels_ProduceBlockEnd
        ];

    }
    receivedEvents($event: falcon.ModuleEvent): void {
        switch ($event.getClass()) {
            case E_Levels_EnterLevel:
                this.enterLevel(($event as E_Levels_EnterLevel).levelId);
                break;
            case E_Levels_ProduceBlockEnd:
                this.produceBlockEnd(($event as E_Levels_ProduceBlockEnd).result);
                break;
        }
    }
    async enterLevel(levelId: string): Promise<void> {
        //临时写法
        const levelConfig = { masterType: LevelMasterType.ECSLevel };
        // const levelConfig = await LevelLoader.getLevelConfig(levelId);
        switch (levelConfig.masterType) {
            case LevelMasterType.ECSLevel:
                await Loader.showUI(PrefabConfig.LevelsRoot);
                const com = Cinst(LevelRoot);
                com.setState({ levelId: +levelId });
                break;
        }
    }

    async produceBlockEnd(result: any): Promise<void> {
        const world = game['_world'];
        if (world) {
            console.log('yjf__produceBlockEnd', result);
            world.eventBus.emit(ECSEvent.GameEvent.PRODUCE_BLOCK_END, result);
        }
    }
}

