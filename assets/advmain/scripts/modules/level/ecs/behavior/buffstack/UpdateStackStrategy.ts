// import { BuffData } from "../../../component/BuffComponent";
// import { BuffStackFormula, IBaseStackConfig } from "../../../../config/BuffConfig";
// import { IBuffAddParams, IBuffAddResult, IBuffRemoveParams, IBuffRemoveResult, StackBehaviorBase, StackResult } from "./StackBehaviorBase";


// /**
//  * 更新堆叠行为
//  * 
//  * 工作原理：
//  * - 同一时间只保留一个主要的buff实例
//  * - 当新buff添加时，更新主要buff的属性（时间、值、层数等）
//  * - 根据配置的叠加公式计算新的效果值
//  * - 所有更新操作都是就地修改，不创建新的buff实例
//  * 
//  * 适用场景：
//  * - 可叠加的buff效果（如攻击力提升buff可以刷新时间）
//  * - 层数递增的buff（如毒效果可以叠加层数增强伤害）
//  * - 需要保持连续性的buff（如持续施法状态）
//  */

// export class UpdateStackStrategy extends StackBehaviorBase<IUpdateStackConfig> {

//     handleBuffAdd(params: IBuffAddParams): IBuffAddResult {
//         const existingBuffs = params.existingBuffs;
//         const newBuffConfig = params.newBuffConfig;

//         // 没有现有buff，直接创建
//         if (existingBuffs.length === 0) {
//             return StackResult.CREATE_AND_ACTIVATE;
//         }

//         // 更新策略下应该只有一个buff，取第一个作为主buff
//         const mainBuff = existingBuffs[0];
//         // 如果有多个buff，打印警告（理论上不应该发生）
//         if (existingBuffs.length > 1) {
//             console.warn(
//                 `[UpdateStackStrategy] 更新策略下发现多个buff实例: entityId=${mainBuff.ownerEntityId}, stackId=${this.config.stackId}, count=${existingBuffs.length}`
//             );
//         }

//         // 检查是否超出最大层数限制
//         if (mainBuff.stackCount >= this.config.maxStack) {
//             console.warn(
//                 `[UpdateStackStrategy] 已达到最大层数限制: stackId=${this.config.stackId}, current=${mainBuff.stackCount}, max=${this.config.maxStack}`
//             );
//             return StackResult.NO_CREATE;
//         }

//         // 更新主buff的属性
//         this.updateMainBuff(mainBuff, newBuffConfig);

//         // 不创建新buff，使用现有的
//         return StackResult.NO_CREATE;
//     }

//     handleBuffDestroy(params: IBuffRemoveParams): IBuffRemoveResult {
//         // 更新策略下，销毁buff不需要特殊处理
//         // 因为只有一个主buff，销毁就是简单移除
//         return StackResult.NO_CREATE;
//     }

//     /**
//      * 更新主buff的属性
//      * 包括层数、时间、效果值等
//      */
//     private updateMainBuff(mainBuff: BuffData, newBuffConfig: any): void {
//         // 增加层数
//         mainBuff.stackCount++;

//         // 重置持续时间为新buff的时间
//         mainBuff.remainingTime = newBuffConfig.duration;

//         // 根据叠加公式计算新的效果值
//         const newValue = this.calculateNewValue(mainBuff, newBuffConfig);

//         // 更新效果值
//         this.world.handlerCenter.buffHandler.handleValueChanged(mainBuff,newValue);

//         // 重新激活buff以确保效果更新
//         if (!mainBuff.isActive) {
//             this.world.handlerCenter.buffHandler.activateBuff(mainBuff);
//         }
//     }

//     /**
//      * 根据配置的叠加公式计算新的效果值
//      */
//     private calculateNewValue(mainBuff: BuffData, newBuffConfig: any): number {
//         switch (this.config.stackFormula) {
//             case BuffStackFormula.LINEAR:
//                 return mainBuff.currentValue + newBuffConfig.value;
//             case BuffStackFormula.MAX:
//                 return Math.max(mainBuff.currentValue, newBuffConfig.value);
//             case BuffStackFormula.STACK:
//                 return newBuffConfig.value * mainBuff.stackCount;
//             case BuffStackFormula.OVERRIDE:
//             default:
//                 return newBuffConfig.value;
//         }
//     }
// }
// /**
//  * 更新堆叠配置
//  * 用于更新堆叠策略
//  */
// export interface IUpdateStackConfig extends IBaseStackConfig {
//     stackFormula: BuffStackFormula;
// }