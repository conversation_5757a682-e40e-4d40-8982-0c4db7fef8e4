import { ExecutionContext } from '../types';
import { BaseFlow } from './BaseFlow';

/**
 * 选择执行流 - 执行第一个条件满足的规则或第一个非规则执行单元
 * 
 * 选择流的执行策略：
 * 1. 对于规则节点，只有当条件满足时才执行并返回
 * 2. 对于执行流节点，执行后检查其结果（未来可扩展）
 */
export class SelectorFlow extends BaseFlow {
  run(): boolean {
    for (const child of this.children) {
      // 处理规则节点 - 条件满足才执行
      child.setContext(this.context);
      if (this.isRule(child) && child.canRun() && child.invalidateExecuteContext()) {
        child.run();
        return true;
      }
      // 处理执行流节点 - 可以添加流结果检查逻辑
      if (this.isFlow(child) && child.canRun() && child.invalidateExecuteContext()) {
        if(child.run()){
          return true;
        }
      }
    }
    return false;
  }
} 