
import { ActionHandler } from '../../logicHandler/ActionHandler';
import BoardHandler from '../../logicHandler/BoardHandler';
import { CellHandler } from '../../logicHandler/CellHandler';
import { World } from '../World';

/**通用或者工具函数处理中心 */
export class HandlerCenter {
    /**棋盘处理器 */
    boardHandler: BoardHandler;
    /**方块处理器 */
    cellHandler: CellHandler;
    /**行动意图处理器 */
    actionHandler: ActionHandler;

    constructor(world: World) {
        this.boardHandler = new BoardHandler(world);
        this.cellHandler = new CellHandler(world);
        this.actionHandler = new ActionHandler(world);
    }
}   
