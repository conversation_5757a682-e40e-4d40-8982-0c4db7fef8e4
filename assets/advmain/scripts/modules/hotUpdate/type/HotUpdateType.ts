
/** 业务 热更新状态 */
export enum HotUpdateStateType {
  ERROR_NO_LOCAL_MANIFEST,
  ERROR_DOWNLOAD_MANIFEST,
  ERROR_PARSE_MANIFEST,
  NEW_VERSION_FOUND,
  ALREADY_UP_TO_DATE,
  UPDATE_PROGRESSION,
  ASSET_UPDATED,
  ERROR_UPDATING,
  SUCCE<PERSON>,
  UPDATE_FAILED,
  ERROR_DECOMPRESS,
  STOP_WHENUPDATE,
  START,
  FAIL,

  // 自定义状态
  REQUEST_REMOTE_CONFIG_TIMEOUT = 100, //获取远程版本配置超时
  REMOTE_CONFIG_DATA_ERROR,
}
