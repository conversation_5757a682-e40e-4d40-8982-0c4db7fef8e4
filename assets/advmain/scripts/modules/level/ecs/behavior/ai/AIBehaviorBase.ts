import { EntityBehaviorBase, IBehaviorConfig, IBehaviorData } from "../../combat/core/BehaviorBase";
/**AI行为ID */
export type AIBehaviorId = string;
/**AI配置 */
export interface IAIBehaviorConfig extends IBehaviorConfig {
    aiBehaviorId: AIBehaviorId;
}
/**AI行为数据 */
export interface IAIBehaviorData extends IBehaviorData{
    
}

export abstract class AIBehaviorBase<TConfig extends IAIBehaviorConfig=IAIBehaviorConfig> extends EntityBehaviorBase<TConfig>{
    /**初始化AI行为 */
    abstract initAI(entityId: number): void;
    /**销毁AI行为 */
    abstract disposeAI(entityId: number): void;
    /**更新AI行为 */
    updateAI?(entityId: number,dt: number): void;
}

