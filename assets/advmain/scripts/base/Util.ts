/**通用工具类 */
export default class Util {
    /**快速获取节点 */
    public static getNodes(node: cc.Node) {
        const nodes: { [k: string]: cc.Node } = {};
        this.loopDo(node, (n) => (nodes[n.name] = n));
        return nodes;
    }

    /**深度处理节点 */
    private static loopDo(node: cc.Node, callback: (n: cc.Node) => void) {
        callback(node);
        for (const n of node.children) this.loopDo(n, callback);
    }

    /**绑定点击事件
     * @param node 绑定节点
     * @param callback 点击回调
     * @param lock 可选参数：点击锁定
     * @param lockSec 可选参数：锁定后恢复秒数
     */
    static bindTap(node: cc.Node, callback: (e: cc.Event.EventTouch) => void, lock = false, lockSec = 0) {
        if (!node['_tap']) {
            node.on(cc.Node.EventType.TOUCH_END, (e: cc.Event.EventTouch) => {
                const p = e.getLocation();
                const sp = e.getStartLocation();
                //判断点击操作
                if (Math.abs(p.x - sp.x) > 10 || Math.abs(p.y - sp.y) > 10) {
                    return;
                }
                //锁定判断
                const btn = node.getComponent(cc.Button);
                if (btn) {
                    if (!btn.interactable) {
                        return;
                    }
                    if (lock) {
                        btn.interactable = false;
                        if (lockSec) {
                            btn.scheduleOnce(() => (btn.interactable = true), lockSec);
                        }
                    }
                }
                node['_tap'](e);
                e.bubbles = false;
            });
        }
        node['_tap'] = callback;
    }
    /**
     * 深度合并对象
     * @param baseObj 基础对象
     * @param overrideObj 重写对象
     * @returns 合并后的对象
     */
    static deepMerge(baseObj: any, overrideObj: any): any {
        if (!overrideObj) return baseObj;
        if (!baseObj) return overrideObj;

        const result = baseObj;

        for (const key in overrideObj) {
            if (overrideObj.hasOwnProperty(key)) {
                const baseValue = baseObj[key];
                const overrideValue = overrideObj[key];

                // 如果重写值是对象且基础值也是对象，则递归合并
                if (
                    overrideValue &&
                    typeof overrideValue === 'object' &&
                    !Array.isArray(overrideValue) &&
                    baseValue &&
                    typeof baseValue === 'object' &&
                    !Array.isArray(baseValue)
                ) {
                    result[key] = this.deepMerge(baseValue, overrideValue);
                } else {
                    // 否则直接替换
                    result[key] = overrideValue;
                }
            }
        }
        return result;
    }

    /**封装schedule，代替setTimeout */
    static scheduleOnce(func: Function, time: number, thisObj: any) {
        const scheduler = cc.director.getScheduler();
        const obj = thisObj || cc.director.getScene();
        scheduler.enableForTarget(obj);
        scheduler.schedule(func, obj, time, 0, 0, false);
    }

    /**封装unschedule，代替clearTimeout */
    static unschedule(func: Function, thisObj: any) {
        const scheduler = cc.director.getScheduler();
        const obj = thisObj || cc.director.getScene();
        scheduler.enableForTarget(obj);
        scheduler.unschedule(func, obj);
    }
}
