import { BehaviorBase, IBehaviorConfig } from '../../combat/core/BehaviorBase';
import { ProjectileComponent } from '../../components/combat/ProjectileComponent';
import { IPoint } from '../../define/EcsDefine';

/**
 * 路径计算参数
 */
export interface PathCalculationData {
    startPos: IPoint; // 起始位置
    targetPos: IPoint; // 目标位置
    elapsedTime: number; // 已经过的时间（秒）
    deltaTime: number; // 时间增量（秒）
    speed: number; // 移动速度（单位/秒）
    moveTime: number; // 移动时间（秒）
}

/**
 * 路径计算结果
 */
export interface PathCalculationResult {
    position: IPoint; // 计算出的位置
    isComplete: boolean; // 路径是否完成
}

/**
 * 移动参数接口
 */
export interface IMoveParams {
    /** 移动速度（单位/秒） */
    speed?: number;
    /** 移动时间（秒） */
    moveTime?: number;
}
export interface IProjectilePathBehaviorConfig extends IBehaviorConfig {}
/**投射物路径行为 */
export abstract class ProjectilePathBehaviorBase<T extends IProjectilePathBehaviorConfig = IProjectilePathBehaviorConfig> extends BehaviorBase<T> {
    protected readonly resultCache: PathCalculationResult = {
        position: { x: 0, y: 0 },
        isComplete: false,
    };
    /**
     * 计算给定时间点的位置
     * @param params 路径计算参数（包含已计算好的速度）
     * @param projectile 投射物组件
     * @returns 路径计算结果
     */
    abstract calculatePath(params: PathCalculationData, projectile: ProjectileComponent): PathCalculationResult;
}
