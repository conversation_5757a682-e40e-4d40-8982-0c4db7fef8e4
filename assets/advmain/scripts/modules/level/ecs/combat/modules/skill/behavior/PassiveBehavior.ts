import { SkillData } from "../../../component/Skill";
import { SkillBehaviorBase } from "../core/ISkillBehavior";
import { IBaseSkillConfig } from "../../../../config/SkillConfig";
import { IBehaviorData } from "../../../core/BehaviorBase";
import { ECSEvent } from "../../../../GameEvent";

/**
 * 被动技能配置
 */
export interface IPassiveConfig extends IBaseSkillConfig {
    buffIds: string[];  // 要添加的buff ID列表
}

/**
 * 被动技能行为数据
 */
interface PassiveBehaviorData extends IBehaviorData {
    addedInsIds: number[];  // 已添加的buff数据列表，用于移除时清理
}

/**
 * 被动技能行为
 * 被动技能的简化设计：主要功能是添加和移除buff
 * 
 * 特点：
 * 1. 不能通过onCast主动使用
 * 2. onAdd时添加配置中的所有buff
 * 3. onRemove时移除之前添加的所有buff
 * 4. 与现有buff系统联动
 * 5. 状态始终为Inactive
 */
export class PassiveBehavior extends SkillBehaviorBase<IPassiveConfig> {

    onAdd(skillData: SkillData<PassiveBehaviorData>): void {
        if (!skillData.behaviorData) return;

        skillData.behaviorData.addedInsIds = [];

        const addedInsIds = skillData.behaviorData.addedInsIds

        console.log(`被动技能已添加: ${skillData.skillId}`);

        if (!this.config || !this.config.buffIds || !skillData.behaviorData) return;

        const targetEntityId = skillData.casterEntityId; // 被动技能通常作用于自己

        console.log(`开始添加被动技能buff: ${skillData.skillId}, buff数量: ${this.config.buffIds.length}`);

        for (const buffId of this.config.buffIds) {
            // 通过事件系统添加buff
            this.world.eventBus.emit(ECSEvent.GameEvent.ADD_BUFF, {
                casterEntityId: skillData.casterEntityId,
                ownerEntityId: targetEntityId,
                buffBehaviorId: buffId
            });
            // 注意：由于使用事件系统，无法直接获取insId，这里暂时不记录
            // 后续可以通过其他方式跟踪添加的buff
        }

        console.log(`被动技能buff添加完成: ${skillData.skillId}, 成功添加: ${this.config.buffIds.length}/${this.config.buffIds.length}`);

    }

    onRemove(skillData: SkillData<PassiveBehaviorData>): void {
        if (!skillData.behaviorData) return;

        console.log(`被动技能移除中: ${skillData.skillId}`);
        const behaviorData = skillData.behaviorData;

        console.log(`开始移除被动技能buff: ${skillData.skillId}, buff数量: ${behaviorData.addedInsIds.length}`);

        const casterEntityId = skillData.casterEntityId;
        // 移除之前成功添加的buff
        for (const insId of behaviorData.addedInsIds) {
            // 由于没有REMOVE_BUFF事件，这里暂时注释掉
            // 后续可以通过其他方式移除buff
            console.warn(`需要移除buff insId: ${insId}，但REMOVE_BUFF事件未实现`);
        }
        // 清空已添加的buff列表
        behaviorData.addedInsIds = [];

        console.log(`被动技能buff移除完成: ${skillData.skillId}`);
    }
} 