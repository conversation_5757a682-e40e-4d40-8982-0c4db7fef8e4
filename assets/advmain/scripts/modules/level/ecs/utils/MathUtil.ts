export class MathUtil {
    /**
     * 从数组中随机选取指定数量的元素（不重复）
     * @param arr 原始数组
     * @param count 需要选取的元素数量
     * @returns 新数组
     */
    static getRandomElements<T>(arr: T[], count: number): T[] {
        // 防御性检查
        if (count <= 0) return [];
        if (count >= arr.length) return [...arr].sort(() => Math.random() - 0.5);

        const clone = [...arr]; // 克隆数组避免修改原数组
        const result: T[] = [];

        for (let i = 0; i < count; i++) {
            const randomIndex = Math.floor(Math.random() * clone.length);
            // 将随机选中的元素交换到数组末尾并弹出
            [clone[randomIndex], clone[clone.length - 1]] = [clone[clone.length - 1], clone[randomIndex]];
            result.push(clone.pop()!);
        }
        return result;
    }

    /**随机数，最小值不传默认0 */
    static randomInt(max: number, min = 0) {
        return Math.floor(min + Math.random() * (max - min + 1));
    }

    /**
     * 从数组中随机选择一个元素
     * @param array 目标数组
     * @returns 随机元素（如果数组为空则返回 undefined）
     */
    static getRandomElement<T>(array: T[]): T | undefined {
        if (array.length === 0) return undefined;
        const randomIndex = Math.floor(Math.random() * array.length);
        return array[randomIndex];
    }

    /**
     * 计算水平均匀排列的元素位置
     * @param containerWidth 容器总宽度
     * @param itemWidth 单个元素宽度
     * @param gap 元素之间的固定间距
     * @param itemCount 元素数量
     * @returns 元素起始位置数组（单位与输入参数一致）
     */
    static calculateHorizontalPositions(containerWidth: number, itemWidth: number, gap: number, itemCount: number): number[] {
        // 处理无效输入情况
        if (itemCount <= 0) return [];
        if (containerWidth <= 0 || itemWidth <= 0 || gap < 0) {
            throw new Error('Invalid input parameters');
        }
        // 计算总占用宽度（所有元素宽度 + 所有间隙）
        const totalContentWidth = itemCount * itemWidth + (itemCount - 1) * gap;

        // 计算两侧的边距（使内容居中）
        const sideMargin = (containerWidth - totalContentWidth) / 2;
        // 生成位置数组
        return Array.from({ length: itemCount }, (_, index) => {
            return sideMargin + index * (itemWidth + gap);
        });
    }
}
