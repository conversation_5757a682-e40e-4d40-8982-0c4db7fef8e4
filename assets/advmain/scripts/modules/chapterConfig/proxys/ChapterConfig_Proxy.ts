import { Loader } from '../../../base/Loader';
import { SubGameBridge } from '../../../base/SubGameBridge';
import { E_Game_InitComplete } from '../../game/events/E_Game_InitComplete';
import { E_ChapterConfig_Load } from '../events/E_ChapterConfig_Load';
import { chapterConfigInfo } from '../vo/ChapterConfigInfo';

@classId('ChapterConfig_Proxy')
export class ChapterConfig_Proxy extends falcon.Proxy implements IShareTraitTarget {
    shareTraitTarget: ITraitTarget;
    //保存当前配置表的关卡期数
    private _curChapterPeriods: number = -1;

    registerEvents(): { new(...args: any): falcon.ModuleEvent }[] | null {
        return [E_ChapterConfig_Load, E_Game_InitComplete];
    }

    receivedEvents($event: falcon.ModuleEvent): void {
        switch ($event.getClass()) {
            case E_ChapterConfig_Load:
                this.onConfigLoad($event as E_ChapterConfig_Load);
                break;

            case E_Game_InitComplete:
                this.loadChapterConfig(null);
                break;
        }
    }

    // 加载关卡配置
    onConfigLoad(event: E_ChapterConfig_Load) {
        this.loadChapterConfig(event);
    }

    // 加载关卡配置数据
    async loadChapterConfig(event?: E_ChapterConfig_Load) {
        chapterConfigInfo['_isForceDraw'] = false;

        // 关卡期数
        let stage = falcon.storage.getItem('chapterPeriodsIndex', 1);
        // 是否存在下一期
        if (this.isExistNextPeriods()) {
            stage += 1;
            // 期数+1
            falcon.storage.setItem('chapterPeriodsIndex', stage);
            // 清除数据
            this.clearChapterData();
        }
        // 内存已经存在数据，不需要二次加载
        if (this._curChapterPeriods == falcon.storage.getItem('chapterPeriodsIndex', 1) && chapterConfigInfo.chapterListCfg.length != 0) {
            event?.callback();
            return;
        }

        chapterConfigInfo['_chapterDatas'] = [];

        const paths = await Promise.all([
            // 马赛克界面
            this.getChapterListPath('configs/chapterCfg/NoTheme96Config'),
        ]);

        // 强制绘制界面
        chapterConfigInfo['_isForceDraw'] = true;

        const loadResource = (path: string): Promise<void> => {
            return new Promise((resolve, reject) => {
                console.log('path ==========', path);
                Loader.loadByBundle('advres', path, cc.JsonAsset, (err, loadedResource: cc.JsonAsset) => {
                    if (!err) {
                        if (path === paths[0]) {
                            if (stage == 1) chapterConfigInfo.setListData(loadedResource.json)
                            else chapterConfigInfo.setListData(loadedResource.json.arr)
                        }
                        resolve();
                    } else {
                        console.error('配置加载失败 == ', path);
                        reject(err);
                    }
                });
            });
        };

        Promise.all(paths.map(loadResource))
            .then(() => {
                this._curChapterPeriods = stage;
                console.log('所有资源加载完成');

                // 调用回调函数
                if (typeof event?.callback === 'function') {
                    event.callback();
                }
            })
            .catch((err) => {
                console.error('资源加载失败', err);
            });
    }

    // 清除数据
    clearChapterData() {
        falcon.storage.setItem(`chapterNum`, 0);
        falcon.storage.setItem(`lastChapterNum`, 0);
    }

    // 是否存在下一期
    isExistNextPeriods() {
        if (chapterConfigInfo.chapterListCfg.length != 0) {
            const lastChapterNum = falcon.storage.getItem(`lastChapterNum`, 0);
            const chapterNum = falcon.storage.getItem(`chapterNum`, 0);
            if (lastChapterNum != chapterNum) {
                // 没有观看过关动画
                return false;
            }
            if (chapterNum >= chapterConfigInfo.chapterAllNum) {
                // 通关
                if (this.isExistNextData()) {
                    return true;
                }
            }
        }
        return false;
    }

    // 是否存在下一期数据
    isExistNextData(isExist?: boolean) {
        if (isExist) {
            return true;
        }
        return false;
    }

    /// 获取关卡显示的数据路径
    private getChapterListPath(path: string): string {
        return path;
    }
}
