import { RenderTemplate, RenderTemplateMustArgs, RenderTemplateOptionalArgs } from './RenderTemplate';
/** UI模板 */
export class UITemplete extends RenderTemplate<RenderTemplateMustArgs, RenderTemplateOptionalArgs> {
    getDefaultOptionalArgs(mustArgs: RenderTemplateMustArgs): RenderTemplateOptionalArgs {
        return {
            renderParam: {
                isBreakLink: true
            }
        };
    }
    createTemplete(mustArgs: RenderTemplateMustArgs, optionArgs: RenderTemplateOptionalArgs): number {
        const entity = super.createTemplete(mustArgs, optionArgs);
        this.world.destroyEntity(entity);
        return entity;
    }
}
