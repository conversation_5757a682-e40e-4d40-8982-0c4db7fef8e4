/**
 * Falcon
 * 
 * 核心架构:
 * 1. Module(模块) - 业务模块的基本单元
 * 2. Proxy(代理) - 处理具体业务逻辑的执行者
 * 3. Event(事件) - 模块间通信的载体
 * 4. EventManager(事件管理器) - 统一管理事件的分发
 */

declare module falcon {

    // 基础类型定义
    export type StorageKey = string;
    /** 
     * 模块类型
     * - common: 通用模块，任何场景都可以使用
     * - class: 无尽场景专用模块
     * - journey: 关卡场景专用模块
     */
    export type ModuleType = 'common';

    export const ANDROID_ACTIVE = "org/cocos2dx/javascript/AppActivity";
    /////////////////////////////////// base interface //////////////////////////////////////
    export interface IDragonbonePlayConfig {
        bundleName?: string;
        dragonAssetUrl: string;
        dragonAtlasAssetUrl: string;
        frameSplitting?: boolean;
    }
    export interface IAudioInfoOption {
        /** 音频资源路径 */
        url: string;
        /** 音量大小 */
        volume: number;
        /** 音频类型 */
        type: AudioType;
        /** 资源包名称 */
        bundleName?: BundleName;
    }
    export interface ICacheRenderOption<T> {
        prefabUrl: string;
        bundleName?: BundleName;
        count: number;
        typeOrClassName: { new(): T } | string;
        parent: cc.Node;
    }
    export interface IProjectManifest {
        url1: string;
        url2: string;
        url3: string;
        version: string;
        assets: { [key: string]: { size: number; md5: string } };
        searchPaths: string[];
    }

    export interface IVersionManifest {
        url1: string;
        url2: string;
        url3: string;
        version: string;
    }

    export interface IHotUpdateEvent {
        eventCode: number;
        msg?: string;
        jsbEvent?: jsb.EventAssetsManager;
    }

    export enum EventCodeEnum {
        ERROR_NO_LOCAL_MANIFEST,
        ERROR_DOWNLOAD_MANIFEST,
        ERROR_PARSE_MANIFEST,
        NEW_VERSION_FOUND,
        ALREADY_UP_TO_DATE,
        UPDATE_PROGRESSION,
        ASSET_UPDATED,
        ERROR_UPDATING,
        UPDATE_FINISHED,
        UPDATE_FAILED,
        ERROR_DECOMPRESS,
        STOP_WHENUPDATE,
        START,
        FAIL,
    }


    /** 层级配置接口 */
    export interface ILayerConfig {
        /** 层级类型(字符串标识) */
        type: string;
        /** 层级名称 */
        name: string;
        /** 层级zIndex */
        zIndex: number;
        /** 是否默认隐藏 */
        hidden?: boolean;
        /** 初始透明度 */
        opacity?: number;
    }

    export interface ICrypto {
        encrypt(data: string): string;
        decrypt(data: string): string;
    }
    export interface Storage {
        /**
         * 初始化前缀
         * @param prefix 
         */
        initPrefix(prefix: string): void;
        /** 存储数据 */
        setItem<T>(key: StorageKey, value: T): void;
        /** 获取数据 */
        getItem<T>(key: StorageKey, defaultValue?: T): T | undefined;
        /** 删除数据 */
        remove(key: StorageKey): void;
        /** 清空所有数据 */
        clear(): void;
    }

    export interface IObjectPoolOption {
        size: number;
        args: any[];
        async: boolean;
        onCreateComplete: (err: Error) => void;
    }


    export interface PrefabConfigType {
        /** 名称 */
        name: string;
        /** 预制体地址 */
        url: string;
        /** 预制体所在的 bundle 名 */
        bundleName?: BundleName;
        /** 预制体所在的 bundle 版本 */
        version?: string;
        /** 打开时缓动动画 */
        ease?: new () => IBaseEase;
        /** 是否为模态，有半透明背景 */
        modal?: boolean;
        /**
         * 是否是 page 页面，page 页面有以下几个特点：
         * 1. 打开 page 页面的时候，会关闭上一个 page 页面
         * 2. 通常情况下 page 页面都没有自我关闭，否则，就会形成黑屏
         * 3. page 页面一般都是全屏的
         */
        page?: boolean;
    }

    /** 预制体类型 */
    export type PrefabType<T extends cc.Component> = {
        url: string;
        comp: { prototype: T };
    }

    export interface IHttpOption {
        /** 请求类型 */
        type?: HttpType;
        /** 请求头 */
        contentType?: ContentType;
        /** 尝试次数 */
        retryTimes?: number;
        /** 超时时间 */
        timeout?: number;
        /** 加密算法 */
        crypto?: ICrypto;
    }


    export interface HM_AB_DATA {
        conditions: [];
        features: [];
        mark: number;
        abWayNum: number;
        abWay: string;
        activeDays: number;
        hotState: boolean;
        resVersion: string;
        abWayPlanNum: number;
        abWayFilters: number[];
        abWayIncludes: number[];
    }

    export interface Feature {
        [traitName: string]: Array<{
            id: number;
            param: { [key: string]: any };
        }>;
    }


    ///////////////////////////////////  falcon //////////////////////////////////////

    // Module 相关类型定义
    /** Proxy类的构造器类型 */
    export type ProxyInstance<T> = { new(module: Module): T };

    /**
     * 模块基类
     * 负责管理一组相关的Proxy，处理特定业务领域的功能
     */
    export class Module {
        /** 获取代理类映射表 */
        get proxyClassMap(): Map<ProxyInstance<Proxy>, any>;
        /** 获取模块类型 */
        get moduleType(): ModuleType;

        /** 注册当前模块的代理类列表 */
        registerProxys(): ProxyInstance<Proxy>[];
        /** 获取指定代理类的实例 */
        getProxy<T extends Proxy>($proxyClass: ProxyInstance<T>): T;
        /** 启动模块的所有代理 */
        startProxy(moduleType: ModuleType): void;
    }

    /** 模块管理器 */
    export const ModuleManager: {
        /** 获取当前的模块类型 */
        readonly moduleType: ModuleType;
        /** 设置当前运行时的模块类型 */
        setCurrentModuleType(moduleType: ModuleType): void;
        /** 获取所在的模块 */
        readonly moduleList: Module[];
        /** 注册模块 */
        resigerModule($modules: { new(): Module }[]): void;
        /** 启动各个模块 */
        startModule(moduleType: ModuleType): void;
        /** 重置模块管理器状态 */
        reset(): void;
        /** 重新启动模块 */
        restart(moduleType: ModuleType): void;
        /** 获取模块实例数量 */
        readonly moduleCount: number;
        /** 检查模块是否已初始化 */
        readonly isInitialized: boolean;
    };

    // --- Event 相关类型定义
    /**
     * 模块事件基类
     * 模块间通信的载体
     */
    export class ModuleEvent<T = any> {
        get callback(): () => void;
        getClass(): { new(...args: any): ModuleEvent };
    }

    /**
     * 事件数据对象
     * 包含事件相关的元数据
     */
    export class EventVo {
        proxy: Proxy | undefined;
        eventClass: { new(): ModuleEvent } | undefined;
        netClass: any;
        schemeClass: { new(): any } | undefined;
        constructor();
        get id(): number;
    }

    /**
     * 事件管理器
     * 统一管理事件的注册和分发
     */
    export class EventManager {
        static get events(): Map<{ new(): ModuleEvent }, EventVo[]>;

        /** 注册事件 */
        static registerEvent(eventVo: EventVo): void;
        /** 同步分发事件 */
        static dispatchModuleEvent(event: ModuleEvent): void;
        /** 异步分发事件 */
        static dispatchModuleEventAsync(event: ModuleEvent): Promise<void>;
        /** 监听事件完成 */
        static onEventAllCompleted<T extends ModuleEvent>(
            moduleType: ModuleType,
            eventConstructor: { new(...args: any): T },
            callback: (event: T) => void
        ): void;
    }

    ///////////////////////////////////  Proxy 相关类型定义 //////////////////////////////////////
    /**
     * 代理基类
     * 处理具体业务逻辑的执行者
     */
    export class Proxy {
        constructor($module: Module);

        get moduleType(): ModuleType;

        /** 注册当前代理关心的事件列表 */
        registerEvents(): Array<new (...args: any[]) => ModuleEvent> | null;
        /** 获取代理类 */
        getClass(): new (module: Module) => Proxy;
        /** 获取所属模块 */
        getModule(): Module;
        /** 接收并处理事件 */
        receivedEvents($event: ModuleEvent): void;
        /** 启动事件监听 */
        startEvents(): void;
        /** 同步分发事件 */
        dispatchModuleEvent($event: ModuleEvent): void;
        /** 异步分发事件 */
        dispatchModuleEventAsync($event: ModuleEvent): Promise<void>;
        /** 初始化回调 */
        protected onInit(): void;
    }

    ///////////////////////////////////  base //////////////////////////////////////

    /**
     * 基础功能模块 - 提供框架底层能力
     */

    /** 
     * 适配器模块 - 处理屏幕适配相关功能
     */
    export namespace adapter {
        function applyAdapterFringe(): void;
    }

    /**
     * 龙骨动画类
     */
    export class DragonBonesAnim {
        /**
         * 播放龙骨动画
         * @param parentNode 父节点
         * @param armatureName 骨架名称
         * @param animationName 动画名称
         * @param playTimes 播放次数
         * @param config 配置信息
         * @returns 动画节点
         */
        play(parentNode: cc.Node, armatureName: string, animationName: string, playTimes: number, config: Partial<IDragonbonePlayConfig>): cc.Node;
    }

    export const dragonbonesAnim: DragonBonesAnim;

    /**
     * 数组工具模块 - 提供数组操作的工具函数
     */
    export namespace arrays {
        /** 检查两个数组是否包含相同的元素 */
        function arraysHaveSameElements<T>(arr1: T[], arr2: T[]): boolean;
        /** 随机打乱数组 */
        function shuffleArray<T>(array: T[]): T[];
        /** 检查两个数组是否完全相等 */
        function arraysEqual<T>(arr1: T[], arr2: T[]): boolean;
        /** 确保数组长度不超过最大值 */
        function ensureMaxLength<T>(arr: T[], maxLength: number): T[];
    }

    /**
     * 异步工具模块 - 提供异步操作的工具类
     */
    export namespace async {
        /** 
         * 屏障类 - 用于同步多个异步操作
         */
        class Barrier {
            constructor();
            /** 屏障是否打开 */
            get isOpen(): boolean;
            /** 重置屏障 */
            reset(): void;
            /** 打开屏障 */
            open(): void;
            /** 等待屏障打开 */
            wait(): Promise<boolean>;
        }

        /**
         * 超时屏障类 - 在指定时间后自动打开的屏障
         */
        class TimeoutBarrier extends Barrier {
            constructor(autoOpenTimeMs: number);
            /** 打开屏障并清除超时计时器 */
            override open(): void;
        }

        /**
         * 延迟Promise类 - 支持外部resolve/reject的Promise
         */
        class DeferredPromise<T> {
            promise: Promise<T>;
            resolve: (value: T) => void;
            reject: (reason?: any) => void;
            constructor();
            wait(): Promise<T>;
        }

        /** 返回第一个完成的Promise */
        function first<T>(promiseFactories: (() => Promise<T>)[], shouldStop: (t: T) => boolean): Promise<T | null>;
        function firstParallel<T>(promiseList: Promise<T>[], shouldStop: (t: T) => boolean): Promise<T | null>;

        /**
         * 限流器类 - 控制并发异步操作数量
         */
        class Limiter<T> {
            constructor(maxDegreeOfParalellism: number);
            /** 获取排空事件 */
            get onDrained(): Event<void>;
            /** 获取当前队列大小 */
            get size(): number;
            /** 添加任务到队列 */
            queue(factory: () => Promise<T>): Promise<T>;
            /** 清空队列 */
            clear(): void;
            /** 释放资源 */
            dispose(): void;
        }

        /**
         * 按顺序执行Promise数组
         */
        function sequence<T>(tasks: (() => Promise<T>)[], interruptCondition?: () => Promise<boolean>): Promise<T[]>;

        /**
         * 等待条件满足
         */
        namespace waitFor {
            function start(key: string): void;
            function end(key: string): void;
            function wait(key: string, options?: { timeout?: number }): Promise<void>;
        }
    }

    /**
     * 音频类型枚举
     */
    export enum AudioType {
        /** 声音 */
        SOUND,
        /** 音效 */
        EFFECT
    }
    /**
     * 音频信息类 - 处理音频播放
     */
    export const audioInfo: {
        /**
         * 播放声音或者音效
         * @param option 音频播放选项
         */
        play(option: Partial<IAudioInfoOption>): void;

        /**
         * 停止播放声音或者所有音效
         * @param option 音频停止选项
         */
        stop(option: Partial<IAudioInfoOption>): void;
    };

    /**
     * 缓存渲染类 - 优化渲染性能
     */
    export const cacheRender: {
        createOrUpdateCacheListComponents<T>(options: ICacheRenderOption<T>): Promise<T[]>;
    };

    /**
     * 组件缓存命名空间 - 管理组件实例的缓存
     */
    export namespace cacheComponents {
        function componentEnabledIntercept(): void;
        function Cinst<T extends cc.Component>(CompClass: { new(): T } | string): T;
        function CinstAsync<T extends cc.Component>(CompClass: { new(): T } | string): Promise<T>;
    }

    /**
     * 组件基类 - 提供状态管理和生命周期
     */
    export class Component<S = {}> extends cc.Component {
        private _state: S;
        get state(): S;
        setState<K extends keyof S>(state?: S, replace?: boolean, callback?: () => void): void;
        componentWillMount(): void;
        componentDidMount(): void;
        componentWillUnmount(): void;
        shouldComponentUpdate(nextState: Readonly<S>): boolean;
        componentWillUpdate(nextState: Readonly<S>): void;
        componentDidUpdate(prevState: Readonly<S>): void;
        render(): void;
    }

    /**
     * 深拷贝工具模块 - 提供对象深拷贝功能
     */
    export namespace copy {
        /** 深拷贝对象 */
        function deepCopy<T>(obj: T): T;
        /** 深拷贝数组 */
        function deepCopyArrayFrom<T>(arr: T[]): T[];
        /** 深拷贝数组片段 */
        function deepCopySlice<T>(arr: T[], start?: number, end?: number): T[];
        /** 循环方式深拷贝 */
        function deepCopyLoop<T>(obj: T): T;
        /** 固定深度深拷贝 */
        function deepCopyFixed<T>(obj: T, length?: number): T;
    }

    /**
     * URL加密类 - 处理URL加密解密
     */
    export class UrlCrypto {
        static encrypt(content: string): string;
        static decrypt(content: string): string;
    }

    /**
     * 日期工具模块 - 提供日期操作功能
     */
    export namespace date {
        /** 获取最近几天的日期 */
        function getLastSomeDays(days: number): Date[];
        /** 获取今天的日期 */
        function getTodayDate(): Date;
        /** 计算两个日期之间的天数差 */
        function getDiffDays(date1: number | Date, date2: number | Date): number;
    }

    export const decoratorTraitsClassNameMap: { [className: string]: Trait } = {};


    /**
     * 装饰器模块 - 提供各种功能装饰器
     */
    /** 适配器装饰器 */
    export function adapterFringe<T extends Component>(...nodeNames: (keyof T)[]): MethodDecorator;

    /** 节流装饰器 */
    export function throttle(delay?: number): MethodDecorator;

    /** 防抖装饰器 */
    export function debounce(wait?: number): MethodDecorator;

    /** 性能测量装饰器 */
    export function measure(target: any, propertyKey: string, descriptor: PropertyDescriptor): PropertyDescriptor;

    /** 缓存装饰器 */
    export function memoize(target: any, key: any, descriptor: any): PropertyDescriptor;

    /** 调试装饰器 */
    export function Debug(target: any, propertyKey: string, descriptor: PropertyDescriptor): PropertyDescriptor;

    /** 最大列表长度装饰器 */
    export function MaxListLength(maxLength: number): PropertyDecorator;

    /** 类ID装饰器 */
    export function classId(className: string): ClassDecorator;

    /** 获取类名 */
    export function getClassName(objOrCtor: any | Function): string;

    /** 特性装饰器 */
    export function trait<T extends Trait = Trait>(traitClass: new () => T, desc?: string): MethodDecorator;

    /** 模板特性装饰器 */
    export function templateTrait<T extends Trait = Trait>(traitClassNameList: string[]): ClassDecorator;
    /** 屏幕适配装饰器 */
    export function ScreenAdapter(): ClassDecorator & MethodDecorator;

    // templateTrait(target: any): void;
    /**
     * 打点模块 - 处理数据埋点
     */
    export enum DotUploadType {
        /** 数数 */
        SHUSHU,
        /** 数仓 */
        SHUCANG
    };
    /**
     * 打点模块 - 处理数据埋点
     * @param uploadType 上传类型
     * @param eventKey 事件键值
     * @param params 事件参数
     * @param traits 特性数组
     * @param callback 回调函数
     */
    export function createDotData<T extends EventIDType>(
        uploadType: DotUploadType,
        eventKey: T,
        params?: EventElementType<any>[T],
        traits?: (TraitDot<EventElementType<any>[T]>)[],
        callback?: (data: EventElementType<any>[T] & { [key: string]: any }) => void
    ): void;

    /**
     * 枚举工具模块 - 提供枚举操作功能
     */
    export namespace enumUtils {
        /** 根据值获取枚举键 */
        function getKeyByValue<T>(enumObj: T, value: any): keyof T | undefined;
        /** 检查值是否在枚举中 */
        function isValueInEnum<T>(enumObj: T, value: any): boolean;
    }

    /**
     * 相等性比较模块
     */
    export namespace equalUtils {
        /** 深度比较两个值是否相等 */
        function equal(a: any, b: any): boolean;
    }

    /**
     * 热更新模块 - 处理游戏热更新
     */
    export class HotUpdate {
        onHotUpdateState(callback: (state: string, event?: IHotUpdateEvent) => void): void;
        startUpdate(projectManifest: IProjectManifest, remoteVersionManifest: IVersionManifest): void;
        versionCompareHandle(versionA: string, versionB: string): number;
        log(...args: any[]): void;
        error(...args: any[]): void;
        readonly storagePath: string;
    }

    export const EventCodeMap: Record<EventCodeEnum, string>;

    export const hotUpdate: HotUpdate;

    /**
     * HTTP模块 - 提供HTTP请求功能
     */
    export enum HttpType {
        GET = "GET",
        POST = "POST"
    }

    export type ContentType = "application/json" | "multipart/form-data" | "text/plain;charset=UTF-8" | "application/x-www-form-urlencoded";

    export enum CrytoType {
        Url = "Url"
    }

    export const http: {
        /**
         * 请求（promise)
         * @param url 请求地址
         * @param data 请求数据（GET 与 POST 格式都为对象）
         * @param option 请求选项
         */
        requestAsync<Req extends object, Resp>(url: string, data: Req, option?: Partial<IHttpOption>): Promise<Resp>;

        /**
         * 请求
         * @param url 请求地址
         * @param data 请求数据（GET 与 POST 格式都为对象）
         * @param success 请求成功后
         * @param fail 请求失败
         * @param option 请求选项
         */
        request<Req extends object, Resp>(url: string, data: Req, success: (data: Resp) => void, fail?: (err: string) => void, option?: Partial<IHttpOption>): void;
    };

    /**
     * 定时器模块 - 提供定时执行功能
     */
    export namespace intervalUtils {
        /** 定时执行函数 */
        function interval(callback: Function, delay: number): void;
    }

    export enum BundleName {
        class = "class",
        chapter = "chapter",
        mainTraits = "mainTraits",
        tool = "tool"
    }

    /** Bundle配置类型 */
    export type BundleConfig = {
        bundleName: string;
        version?: string;  // version 是可选的
    }

    /** Bundle名称类型（支持字符串或配置对象） */
    export type BundleNameType = BundleName | string | BundleConfig;
    /**
     * 资源加载器 - 统一管理资源加载
     */
    export class ResLoader {
        /** 添加资源加载事件监听 */
        static addEventListener(event: 'load', callback: (bundleName: string, path: string) => void): void;

        /** 获取已加载的资源 */
        static getAsset<T extends cc.Asset>(url: string): T;

        /** 加载资源 */
        static load<T extends cc.Asset>(paths: string, type: typeof cc.Asset, onComplete?: (error: Error, assets: T) => void): void;

        /** 异步加载资源 */
        static asyncLoad<T extends cc.Asset>(paths: string, type: typeof cc.Asset): Promise<T>;

        /** 从指定Bundle加载资源（支持版本配置） */
        static loadByBundle<T extends cc.Asset>(bundleNameType: BundleNameType, paths: string, type?: typeof cc.Asset, onComplete?: (error: Error, asset: T) => void): void;

        /** 异步从指定Bundle加载资源（支持版本配置） */
        static asyncLoadByBundle<T extends cc.Asset>(bundleNameType: BundleNameType, paths: string, type?: typeof cc.Asset): Promise<T>;

        /** 加载Bundle（支持版本配置） */
        static loadBundle(bundleNameType: BundleNameType, onComplete: (err: Error, bundle: cc.AssetManager.Bundle) => void): void;

        /** 预加载场景 */
        static bundlePreloadScene(bundle: cc.AssetManager.Bundle, sceneName: string, onComplete?: (error: Error) => void): void;

        /** 预加载资源 */
        static bundlePreload(bundle: cc.AssetManager.Bundle, paths: string | string[], type: typeof cc.Asset, onComplete?: (error: Error) => void): void;

        /** 渲染精灵 */
        static renderSprite(sprite: cc.Sprite, url: string): void;

        /** 从Bundle渲染精灵 */
        static renderSpriteByBundle(sprite: cc.Sprite, url: string, bundleNameType: BundleNameType): void;

        /** 渲染龙骨动画 */
        static renderDragonbones(
            dragonBonesArmatureDisplay: dragonBones.ArmatureDisplay,
            dragonAssetUrl: string,
            dragonAssetAtlasUrl: string,
            armatureName: string,
            animName: string,
            playtimes?: number
        ): void;

        /** 从Bundle渲染龙骨动画 */
        static renderDragonbonesByBundle(
            bundleName: BundleNameType,
            dragonBonesArmatureDisplay: dragonBones.ArmatureDisplay,
            dragonAssetUrl: string,
            dragonAssetAtlasUrl: string,
            armatureName: string,
            animName: string,
            playtimes?: number,
            completeRemove?: boolean
        ): void;
    }

    // number
    export namespace numbers {
        /** 生成指定范围内的随机整数 */
        function randomInt(min: number, max: number): number;
        /** 生成指定范围内的随机浮点数 */
        function randomFloat(min: number, max: number): number;
    }

    /**
     * 性能优化模块
     */
    export namespace performance {
        /** 执行渲染优化 */
        export function executeRenderingOptimize(): void;
    }

    /**
     * 对象池类 - 管理对象的复用
     */
    export class ObjectPool<T> {
        constructor(
            _create: (...args: any[]) => Promise<T>,
            _reset: (data: T) => void,
            _option?: Partial<IObjectPoolOption>
        );

        /** 获取池大小 */
        get poolSize(): number;
        /** 检查对象是否在使用中 */
        isInUsed(obj: T): boolean;
        /** 获取对象 */
        get(...args: any[]): Promise<T>;
        /** 异步获取对象 */
        asyncGet(...args: any[]): Promise<T>;
        /** 释放对象回池 */
        release(obj: T): void;
        /** 清空对象池 */
        clear(): void;
    }

    /**
     * 随机工具模块
     */
    export namespace random {
        /** 从列表中随机选择指定数量的元素 */
        function randomList(list: number[], count: number, repeated?: boolean): number[];
    }


    /** 存储实例 */
    export const storage: Storage;

    /**
     * 超时工具模块
     */
    export namespace timeoutUtils {
        /** 延时指定毫秒数 */
        function timeout(ms: number): Promise<void>;
    }

    /** 基础层级配置 */
    export const BASE_LAYER_CONFIGS: ILayerConfig[];


    /** 层级管理器实例 */
    export const layerManager: {
        init(): void;
        layerScene: cc.Node | null;
        clear(): void;
        addLayer(config: ILayerConfig): cc.Node;
        setLayerNode(type: string, node: cc.Node): void;
        removeLayer(type: string): boolean;
        getLayer(type: string): cc.Node | undefined;
        showLayer(type: string): void;
        hideLayer(type: string): void;
        setLayerOpacity(type: string, opacity: number): void;
        getLayerTypes(): string[];
    };

    export namespace layer {
        /** 基础游戏UI层级 */
        export const gameUiLayer: cc.Node;

        /** 获取游戏UI层级节点，如果无效则重新创建 */
        function getGameUiLayer(): cc.Node;

        /** 手动重置 gameUiLayer，强制在下次访问时重新创建 */
        function resetGameUiLayer(): void;

        /** 初始化节点配置 */
        function initNodeConfig(node: cc.Node, config: ILayerConfig, scene: cc.Node): void;

        /** 添加Widget组件到节点 */
        function addWidget(node: cc.Node): void;
    }

    /**
     * 定时器模块
     */
    export namespace timer {
        /** 等待下一帧 */
        function nextFrame(): Promise<void>;
    }

    /**
     * 特性基类 - 实现特性系统
     */
    export class Trait<T = any> {
        /** 创建特性实例 */
        static create<T>(): Trait<T>;

        // /** 激活特性 */
        // onActive<U>(target: U): Promise<void>;
    }

    /**
     * 任务管理器
     */
    export const task: {
        /**
         * 运行任务
         * @param taskHandler 任务处理函数
         * @param args 任务参数
         */
        run(taskHandler: Function, ...args: any[]): void;
    };
    /**
     * UI管理类 - 统一管理UI界面
     */
    export class UI {
        /** 设置模态预制体 */
        static setModalPrefab(config: PrefabConfigType): void;
        /** 隐藏所有UI */
        static hideAll(): void;
        /** 清理所有UI缓存和状态（用于子游戏重置） */
        static clearAllCache(): void;
        /** UI是否处于激活状态*/
        static activeState(url: string): boolean;
        /** 取消加载后的操作*/
        static cancelLoad(config: { url: string }): void;
        /** 添加面板创建、打开、关闭监听*/
        static addEventListener(event: `create` | `open` | `close`, callback: (event: PrefabConfigType) => void): void;
        static removeEventListener(event: `create` | `open` | `close`, callback: (event: PrefabConfigType) => void): void;
        /** 清除缓存 */
        static clearCache(url: string): void;
        /** 显示UI */
        static show(config: PrefabConfigType, parent?: cc.Node): Promise<cc.Node>;
        /** 添加UI缓存 */
        static addUICache(config: { url: string }, cacheUI: cc.Prefab, parent: cc.Node): void;
        /** 获取组件 */
        static getComponent<T extends cc.Component>(config: PrefabType<T>, parent?: cc.Node): Promise<T>;
        /** 隐藏 ui */
        static hide<T extends cc.Component | cc.Node>(target: T): void;
        static hideUI(config: PrefabConfigType): void;
        static dispatchClose(config: PrefabConfigType): void;
        static hideLayer(layer: cc.Node): void;
    }

    /**
     * URL工具模块
     */
    export namespace url {
        /** 获取URL参数值 */
        function getUrlParameterValue(name: string): string | null;
    }

    /**
     * 监听工具模块 - 提供数据监听功能
     */
    export namespace watchUtils {
        /** 创建响应式对象 */
        function reactive<T extends object>(obj: T): T;
        /** 监听对象变化 */
        function watch<T>(obj: T, callback: (newVal: T, oldVal: T) => void): void;
    }

    export class TraitConfigInfo {
        get traitsClassNameMap(): { [traitClassName: string]: { id: number, param: any } };

        initialize(cfg: any): void;
        createUseTraits(config: any): void;
        createActiveTraits(config: any): void;
        createActiveTraitClassNameMaps(): void;
        createWhiteTraitClassNameMaps(whiteList: number[]): void;
        loadDynamicTrait(dynamicTraitList: number[]): void;
        traitData<T extends keyof TraitType>(traitName: T, id?: number): TraitType[T][number]['param'];
        traitActiveUserData<T extends keyof TraitType>(key: T, id?: number): TraitType[T][number]['param'];
    }

    export const traitConfigInfo: TraitConfigInfo;

    export type TraitType = Feature;
    export namespace typeUtils {
        /**
         * 检查是否为字符串
         * @param str 要检查的值
         * @returns 是否为字符串
         */
        function isString(str: unknown): str is string;

        /**
         * 检查是否为字符串数组
         * @param value 要检查的值
         * @returns 是否为字符串数组
         */
        function isStringArray(value: unknown): value is string[];

        /**
         * 检查是否为对象（不包括 null、数组、正则表达式和日期）
         * @param obj 要检查的值
         * @returns 是否为对象
         */
        function isObject(obj: unknown): obj is Object;

        /**
         * 检查是否为 TypedArray 类型
         * @param obj 要检查的值
         * @returns 是否为 TypedArray
         */
        function isTypedArray(obj: unknown): obj is Object;

        /**
         * 检查是否为数字
         * @param obj 要检查的值
         * @returns 是否为数字
         */
        function isNumber(obj: unknown): obj is number;

        /**
         * 检查是否为可迭代对象
         * @param obj 要检查的值
         * @returns 是否为可迭代对象
         */
        function isIterable<T>(obj: unknown): obj is Iterable<T>;

        /**
         * 检查是否为布尔值
         * @param obj 要检查的值
         * @returns 是否为布尔值
         */
        function isBoolean(obj: unknown): obj is boolean;

        /**
         * 检查是否为 undefined
         * @param obj 要检查的值
         * @returns 是否为 undefined
         */
        function isUndefined(obj: unknown): obj is undefined;

        /**
         * 检查是否已定义（不为 undefined 和 null）
         * @param arg 要检查的值
         * @returns 是否已定义
         */
        function isDefined<T>(arg: T | null | undefined): arg is T;

        /**
         * 检查是否为 undefined 或 null
         * @param obj 要检查的值
         * @returns 是否为 undefined 或 null
         */
        function isUndefinedOrNull(obj: unknown): obj is undefined | null;

        /**
         * 检查是否为函数
         * @param obj 要检查的值
         * @returns 是否为函数
         */
        function isFunction(obj: unknown): obj is Function;

        /**
         * 获取数据的真实类型
         * @param data 要检查的数据
         * @returns 数据类型字符串
         */
        function getType<T>(data: T): 'object' | 'number' | 'string' | 'boolean' | 'array' | 'undefined' | 'null' | 'date' | 'regExp' | 'function';
    }

    /**
     * 原生桥接类 - 处理与原生平台的交互
     */
    declare namespace NativeBridge {
        /**
         * 是否为原生平台
         */
        export function isNative(): boolean;

        /**
         * 发送给原生
         * @param methodName 
         * @param data 
         * @param methodSignature
         */
        export function send<T, RETURN>(methodName: string, data: T, methodSignature: string, className: string = ANDROID_ACTIVE): RETURN;
    }

    /** 预制体实例化功能 */
    export const prefabInfo: {
        getOrCreatePrefabInstantiate(prefab: any): any;
    };

    /** INSTANTIATE 函数 - 提供与Main.ts中相同的全局函数 */
    export function INSTANTIATE(prefab: any): any;

    /** 游戏状态管理器 */
    export const gameStateManager: {
        /** 注册可重置的对象 */
        registerResettable(key: string, resettable: IResettable): void;
        /** 注销可重置对象 */
        unregisterResettable(key: string): void;
        /** 游戏退出时的清理逻辑 */
        onGameExit(): void;
        /** 游戏重新进入时的初始化逻辑 */
        onGameEnter(): void;
        /** 获取初始化状态 */
        readonly initialized: boolean;
        /** 强制重置初始化状态 */
        forceReset(): void;
    };

    /** 可重置对象接口 */
    export interface IResettable {
        /** 重置对象状态 */
        reset(): void;
        /** 重新初始化对象（可选） */
        reinitialize?(): void;
    }
}
