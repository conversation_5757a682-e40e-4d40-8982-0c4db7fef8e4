import BoardComponent from '../components/board/BoardComponent';
import ShapeComponent from '../components/board/ShapeComponent';
import SlotComponent from '../components/board/SlotComponent';
import { ECSEvent } from '../GameEvent';
import { RelationName } from '../cores/center/EntityCenter/WorldRelation';
import { ECSRuleBase, IECSRuleConfig } from './core/ECSRuleBase';
import BoardHandler from '../logicHandler/BoardHandler';
import { IBoardConfig } from '../config/conf/BoardConfig';

export interface IAdaptivePreviewRuleConfig extends IECSRuleConfig {
    /** 容错范围距离（格子数） */
    toleranceMultiplier: number;
    /** 触发容错的最小空闲空间数量 */
    minFreeSpacesThreshold: number;
}

/**
 * 自适应预览规则
 *
 * 功能：在拖动过程中遇到不可放的区域时，增加预览区域的容错性
 *
 * 工作机制：
 * 1. 可放置区域 → 正常显示预览，记住位置
 * 2. 不可放置区域（容错范围内） → 保持在上一个可放置位置
 * 3. 超出容错范围 → 预览消失
 * 4. 边界外 → 在附近搜索可放置位置
 */
export class AdaptivePreviewRule extends ECSRuleBase<
    IAdaptivePreviewRuleConfig,
    ECSEvent.GameEvent.DRAG_BLOCK_START,
    BoardHandler['convertToGridCoord']
> {
    /** 记住最后一个成功的可放置位置 */
    private lastValidPositionCache: { boardId: string; gridX: number; gridY: number } | null = null;
    bindEventData() {
        return ECSEvent.GameEvent.DRAG_BLOCK_START;
    }

    replaceHandler() {
        return {
            handler: this.world.handlerCenter.boardHandler,
            methodName: 'convertToGridCoord',
            replacementFunction: this.enhancedConvertToGridCoord,
        };
    }

    canExecute(): boolean {
        return true;
    }

    execute(): void {
        // 拖动开始时重置位置记忆
        this.lastValidPositionCache = null;
    }

    /** 增强版的网格坐标转换，智能容错 */
    private enhancedConvertToGridCoord(
        screenPos: { x: number; y: number },
        boards: IBoardConfig[],
        shapeId: number,
    ): { boardId: string; gridX: number; gridY: number } | null {
        // 先尝试原始方法
        const originalResult = this.handlerReplacer.originalFunction(screenPos, boards, shapeId);
        if (originalResult) {
            // 原始方法成功，检查该位置是否真的可以放置
            if (this.canPlaceAtPosition(originalResult, boards, shapeId)) {
                // 记住这个有效位置并返回
                this.lastValidPositionCache = originalResult;
                return originalResult;
            } else {
                // 原始位置不可放置，检查距离后决定是否返回上一个有效位置
                if (this.lastValidPositionCache && this.isWithinToleranceRange(originalResult, this.lastValidPositionCache)) {
                    return this.lastValidPositionCache;
                } else {
                    // 没有记住的位置或超出容错范围
                    return this.lastValidPositionCache ? null : this.findTolerancePosition(screenPos, boards, shapeId);
                }
            }
        }

        // 原始方法失败，尝试边界容错搜索
        return this.findTolerancePosition(screenPos, boards, shapeId);
    }

    /** 检查当前位置是否在容错范围内 */
    private isWithinToleranceRange(currentPos: any, lastValidPos: any): boolean {
        if (!currentPos || !lastValidPos || currentPos.boardId !== lastValidPos.boardId) {
            return false;
        }

        const dx = Math.abs(currentPos.gridX - lastValidPos.gridX);
        const dy = Math.abs(currentPos.gridY - lastValidPos.gridY);
        const distance = dx + dy;

        const maxToleranceDistance = this.config.toleranceMultiplier || 1;

        return distance <= maxToleranceDistance;
    }

    /** 检查指定位置是否可以放置方块 */
    private canPlaceAtPosition(result: any, boards: any[], shapeId: number): boolean {
        const shapeComp = this.world.getComponent(shapeId, ShapeComponent);
        const board = boards.find((b) => b.id === result.boardId);
        if (!board) return false;

        // 找到对应的棋盘实体
        const boardEntity = this.world.query([BoardComponent]).find((eid) => this.world.getComponent(eid, BoardComponent).boardId === result.boardId);
        if (boardEntity === undefined) return false;

        const boardComp = this.world.getComponent(boardEntity, BoardComponent);

        // 使用与PreviewSystem相同的验证逻辑
        for (const rc of shapeComp.shape.shape) {
            const r = result.gridY + rc.r;
            const c = result.gridX + rc.c;

            // 边界检查
            if (r < 0 || r >= boardComp.rCount || c < 0 || c >= boardComp.cCount) {
                return false;
            }

            // 获取slot并检查是否被占用
            const slotId = this.world.getTargets(boardComp.entity, RelationName.PARENT_CHILD, `${r}_${c}`)[0];
            if (!slotId) return false;

            const slotComp = this.world.getComponent(slotId, SlotComponent);
            if (this.world.handlerCenter.boardHandler.getSlotOccupy(slotComp)) {
                return false;
            }
        }

        return true;
    }

    /** 寻找容错位置：在边界外或无记忆位置时使用 */
    private findTolerancePosition(screenPos: { x: number; y: number }, boards: any[], shapeId: number): any {
        const shapeComp = this.world.getComponent(shapeId, ShapeComponent);

        for (const board of boards) {
            const result = this.convertToGridCoordWithTolerance(screenPos, board, shapeComp);
            if (result) {
                return result;
            }
        }

        return null;
    }

    /** 带容错的坐标转换：在原始位置附近寻找可放置位置 */
    private convertToGridCoordWithTolerance(screenPos: { x: number; y: number }, board: any, shape: ShapeComponent): any {
        // 计算基础网格位置（使用原始算法）
        const w = board.cols * board.cellSize;
        const h = board.rows * board.cellSize;
        const shapeW = shape.shape.width * board.cellSize;
        const shapeH = shape.shape.height * board.cellSize;

        const adjX = screenPos.x - shapeW / 2 + board.cellSize / 2;
        const adjY = screenPos.y + shapeH / 2 - board.cellSize / 2;
        const boardLeftBottomX = board.startPos.x - w / 2;
        const boardLeftBottomY = board.startPos.y - h / 2;

        const localX = adjX - boardLeftBottomX;
        const localY = adjY - boardLeftBottomY;

        const baseGx = Math.floor(localX / board.cellSize);
        const baseGy = board.rows - 1 - Math.floor(localY / board.cellSize);

        // 在基础位置周围搜索可放置位置
        const toleranceRange = this.config.toleranceMultiplier || 1;
        const candidates: Array<{ gx: number; gy: number; distance: number }> = [];

        for (let dx = -toleranceRange; dx <= toleranceRange; dx++) {
            for (let dy = -toleranceRange; dy <= toleranceRange; dy++) {
                const gx = baseGx + dx;
                const gy = baseGy + dy;

                // 边界检查
                if (gx >= 0 && gx < board.cols && gy >= 0 && gy < board.rows) {
                    // 形状边界检查
                    if (this.isShapeWithinBounds(board, shape, gx, gy)) {
                        const distance = Math.abs(dx) + Math.abs(dy);
                        candidates.push({ gx, gy, distance });
                    }
                }
            }
        }

        // 按距离排序，优先返回最近的可放置位置
        candidates.sort((a, b) => a.distance - b.distance);

        if (candidates.length > 0) {
            return { boardId: board.id, gridX: candidates[0].gx, gridY: candidates[0].gy };
        }

        return null;
    }

    /** 检查形状是否完全在棋盘边界内 */
    private isShapeWithinBounds(board: any, shape: ShapeComponent, gx: number, gy: number): boolean {
        for (const rc of shape.shape.shape) {
            const r = gy + rc.r;
            const c = gx + rc.c;

            if (r < 0 || r >= board.rows || c < 0 || c >= board.cols) {
                return false;
            }
        }
        return true;
    }
}
