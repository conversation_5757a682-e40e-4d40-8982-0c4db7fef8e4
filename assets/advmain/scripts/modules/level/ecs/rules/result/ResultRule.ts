import { TempleteType } from '../../registry/templete/TempleteRegistry';
import { EcsViewId } from '../../renderWorld/ECSViewBase';
import { ECSRuleBase, IECSRuleConfig } from '../core/ECSRuleBase';

export interface IResultRuleConfig extends IECSRuleConfig {
    /**结算视图id */
    ecsViewId: EcsViewId;
}

/**
 * 复活规则
 */
export class ResultRule extends ECSRuleBase<IResultRuleConfig> {
    canExecute(): boolean {
        return true;
    }

    execute(): void {
        const world = this.world;
        const effectEntity = world.templeteCenter.createTempleteEntity(
            TempleteType.Render,
            {
                parentEntity: world.ECSLayerType.EffectLayerEntity,
                ecsViewId: this.config.ecsViewId,
            },
            {
                renderParam: { isBreakLink: true },
            },
        );
        world.destroyEntity(effectEntity);
    }
}
