import { CellComponent } from '../../../components/board/CellComponent';
import { ECSViewBase, IECSViewConfig } from '../../ECSViewBase';

export default class PlaneECSView extends ECSViewBase<IECSViewConfig> {
    protected initView() {
        this.renderWorld
            .loadRes(
                '',
                `textures/level/boardBase/icon_1_${this.logicWorld.getComponent(this.logicEntityId, CellComponent).temp.color}`,
                cc.SpriteFrame,
            )
            .then((spf: cc.SpriteFrame) => {
                if (!cc.isValid(this.node)) return;
                this.node.getComponent(cc.Sprite).spriteFrame = spf;
            });
        this.renderWorld
            .loadRes('level', `cell/110/img/icon${this.logicWorld.getComponent(this.logicEntityId, CellComponent).temp.color}`, cc.SpriteFrame)
            .then((spf: cc.SpriteFrame) => {
                if (!cc.isValid(this.node)) return;
                this.node.getChildByName('icon').getComponent(cc.Sprite).spriteFrame = spf;
            });
    }
    protected addEvents() {}
    protected removeEvents() {}
    protected disposeView() {}
}
