import { IFlow, IExecutable, ExecutionContext, IRule, IFlowConfig} from '../types';

/**
 * 执行流基类
 */
export abstract class BaseFlow implements IFlow {
  /**执行流配置 */
  config: IFlowConfig;
  /**执行上下文 */
  context: ExecutionContext;
  readonly execType = 'flow';
  readonly children: (IExecutable|BaseFlow)[] = [];
  /**
   * 执行流程 - 由子类实现具体逻辑
   */
  abstract run(): boolean;
  /**
   * 设置执行上下文
   */
  setContext(context: ExecutionContext): void {
    this.context = context;
  }
  /**
   * 添加子执行单元
   */
  addChild(child: IExecutable): void {
    this.children.push(child);
  }
  canRun(): boolean {
    return true;
  }
  invalidateExecuteContext(): boolean {
    return true;
  }
  isFlow(obj: IExecutable): obj is BaseFlow {
    return obj && obj.execType === 'flow';
  }
  isRule(obj: IExecutable): obj is IRule {
    return obj && obj.execType === 'rule';
  }
}