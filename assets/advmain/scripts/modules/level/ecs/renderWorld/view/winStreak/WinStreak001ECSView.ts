import { ECSViewBase, IECSViewConfig } from "../../ECSViewBase";
export interface IWinStreak001ViewConfig extends IECSViewConfig { 
    winNum: number;
    timestamp: number;
}
/**
 * 连胜001视图
 * 负责连胜功能的ECS视图层实现
 */
export class WinStreak001ECSView extends ECSViewBase<IWinStreak001ViewConfig> {
    /*-- 属性 --------------------------------------------------------------------*/
    DRAGON_ANIM_NAME = {
        IN: 'in',
    };
    /*-- 函数 --------------------------------------------------------------------*/
    /**
     * 初始化视图
     */
    protected initView(): void {
        console.log('WinStreak001ECSView initView', this.data);
        this.ui.anim.active = false;
        this.showPanelAnim(this.data.winNum);
    }
    /**
     * 添加事件监听
     */
    protected addEvents(): void { }
    /**
     * 移除事件监听
     */
    protected removeEvents(): void { }
    /**
     * 销毁视图
     */
    protected disposeView(): void { }
    getCurrentWinNum(): number {
        return this.data.winNum;
    }
    showPanelAnim(num?: number): void {
        this.ui.anim.active = true;
        this.playDragonAnimation(this.DRAGON_ANIM_NAME.IN, 1, () => {
            num && this.playNumChangeAnimation(num);
        });
    }
    /**
     * 播放数字变化动画
     * @param newNum 新数字
     */
    protected playNumChangeAnimation(newNum: number): void {
        if (this.ui.winNumLabel) {
            // 缩放动画表示数字变化
            this.ui.winNumLabel.stopAllActions();
            cc.tween(this.ui.winNumLabel)
                .to(0.1, { scale: 1.2 })
                .call(() => {
                    this.ui.winNumLabel.getComponent(cc.Label).string = newNum.toString();
                })
                .to(0.1, { scale: 1 })
                .start();
        }
    }
    /**
     * 播放DragonBones动画
     * @param animationName 动画名称
     * @param playTimes 播放次数，0表示无限循环，默认1次
     * @param callback 动画完成回调
     */
    protected playDragonAnimation(animationName: string, playTimes: number = 1, callback?: Function): void {
        if (!this.ui.anim) {
            console.warn(`[WinStreakBaseComp] animationNode 节点不存在，无法播放动画: ${animationName}`);
            return;
        }
        const armatureDisplay = this.ui.anim.getComponent('dragonBones.ArmatureDisplay') as any;
        if (!armatureDisplay) {
            console.warn(`[WinStreakBaseComp] DragonBones组件不存在: ${this.ui.anim.name}`);
            return;
        }
        try {
            // 播放动画
            armatureDisplay.playAnimation(animationName, playTimes);
            // 如果有回调且不是无限循环，则监听动画完成事件
            if (callback && playTimes !== 0) {
                const onAnimationComplete = () => {
                    armatureDisplay.off('complete', onAnimationComplete);
                    callback();
                };
                armatureDisplay.on('complete', onAnimationComplete);
            }
        } catch (error) {
            console.error(`[WinStreakBaseComp] 播放DragonBones动画失败: ${animationName}`, error);
        }
    }
}