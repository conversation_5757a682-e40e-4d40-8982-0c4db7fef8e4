import ProduceBlockListComponent from '../../../components/ProduceBlockListComponent';
import { BoardSceneColorsComponent } from '../../../components/special/BoardSceneColorsComponent';
import { NormalColor7 } from '../../../define/BoardDefine';
import { RcShape } from '../../../define/EcsConfig';
import { ECSEvent } from '../../../GameEvent';
import { MathUtil } from '../../../utils/MathUtil';
import { ECSRuleBase, IECSRuleConfig } from '../../core/ECSRuleBase';

export class ProduceBlockColorRule extends ECSRuleBase<IECSRuleConfig, ECSEvent.GameEvent.PRODUCE_BLOCK_END> {
    public bindEventData() {
        return ECSEvent.GameEvent.PRODUCE_BLOCK_END;
    }
    canExecute(): boolean {
        const produceBlockLists = this.world.query([ProduceBlockListComponent]);
        if (!produceBlockLists || produceBlockLists.length === 0) {
            console.warn('没有找到 ProduceBlockListComponent 实体，无法执行颜色规则');
            return false;
        }
        return true;
    }
    execute(): void {
        const info = this.eventData;
        const produceBlockListEntity = this.world.query([ProduceBlockListComponent])[0];
        const produceBlocks = this.world.getComponent(produceBlockListEntity, ProduceBlockListComponent).produceBlockList;
        for (const block of produceBlocks) {
            block.color = this.getColor();
        }
    }

    getColor() {
        const world = this.world;
        const cellColorsEntities = world.query([BoardSceneColorsComponent]);
        if (!cellColorsEntities?.length) {
            console.warn('没有找到 CellColorsComponent 实体，使用默认颜色');
            return NormalColor7[MathUtil.randomInt(NormalColor7.length - 1)];
        }

        const colors = world.getComponent(cellColorsEntities[0], BoardSceneColorsComponent).colors;
        if (!colors?.length) {
            console.warn('没有可用的颜色，使用默认颜色');
            return NormalColor7[MathUtil.randomInt(NormalColor7.length - 1)];
        }

        return colors[MathUtil.randomInt(colors.length - 1)];
    }
}
