import NodeComponent from '../components/NodeComponent';
import { RenderComponent } from '../components/RenderComponent';
import { RelationName } from '../cores/center/EntityCenter/WorldRelation';
import { TempleteBase } from '../cores/center/TempleteCenter';
import { EcsViewId } from '../renderWorld/ECSViewBase';

export interface RenderTemplateMustArgs {
    /**父实体 */
    parentEntity: number;
    /** 渲染组件ID */
    ecsViewId: EcsViewId;
}

export interface RenderTemplateOptionalArgs {
    /**node参数 */
    nodeParam?: ConstructorParameters<typeof NodeComponent>[0];
    /**渲染参数 */
    renderParam?: ConstructorParameters<typeof RenderComponent>[1];
}

/** 通用渲染模板 */
export class RenderTemplate<T extends RenderTemplateMustArgs, U extends RenderTemplateOptionalArgs> extends TempleteBase<T, U> {
    getDefaultOptionalArgs(mustArgs: T): U {
        return {} as U;
    }

    createTemplete(mustArgs: T, optionArgs: U): number {
        const { parentEntity } = mustArgs;
        const { nodeParam,renderParam } = optionArgs;
        const world = this.world;
        const entity = world.createEntity();
        world.addRelation(parentEntity, entity, RelationName.PARENT_CHILD);
        world.addComponent(entity, NodeComponent, nodeParam);
        world.addComponent(entity, RenderComponent, mustArgs.ecsViewId, renderParam);
        return entity;
    }
}
