import { NativePermissions } from '../../../native/NativePermissions';
import { BoardScene } from '../components/board/BoardScene';
import ReviveComponent from '../components/ReviveComponent';
import TargetComponent from '../components/special/TargetComponent';
import { UtilBase } from '../cores/UtilBase';

/**
 * 复活工具类
 * 提供统一的复活条件检查和状态管理
 */
export default class ReviveUtil extends UtilBase {
    
    /**
     * 检查是否满足复活的基础条件
     * @param config 配置参数
     * @returns 是否可以复活
     */
    canRevive(config?: { needLoad?: boolean, percent?: number }): boolean {
        const sceneEntity = this.getSceneEntity();
        if (!sceneEntity) return false;
        
        const reviveComp = this.world.getComponent(sceneEntity, ReviveComponent);
        if (!reviveComp || reviveComp.nCount <= 0) {
            return false;
        }
        
        // 广告加载检查
        if (config?.needLoad) {
            if (!NativePermissions.getReadyByAdType('reward')) {
                return false;
            }
        }
        
        // 进度要求检查
        if (config?.percent !== undefined) {
            const targetEntities = this.world.query([TargetComponent]);
            if (targetEntities.length === 0) {
                return true; // 没有目标要求时默认通过
            }
            
            const targets = this.world.getComponent(targetEntities[0], TargetComponent).targets;
            const total = targets.reduce((acc: number, target: any) => acc + target.to, 0);
            const current = targets.reduce((acc: number, target: any) => acc + target.current, 0);
            const percent = total > 0 ? current / total : 0;
            
            console.log('ReviveUtil: 进度检查', { current, total, percent, required: config.percent });
            
            if (percent < config.percent) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 检查是否已有预览数据
     * @returns 是否有预览数据
     */
    hasPreviewData(): boolean {
        const sceneEntity = this.getSceneEntity();
        if (!sceneEntity) return false;
        
        const reviveComp = this.world.getComponent(sceneEntity, ReviveComponent);
        return reviveComp && reviveComp.cachedReviveBlocks != null;
    }
    
    /**
     * 获取复活组件
     * @returns 复活组件或null
     */
    getReviveComponent(): ReviveComponent | null {
        const sceneEntity = this.getSceneEntity();
        if (!sceneEntity) return null;
        
        return this.world.getComponent(sceneEntity, ReviveComponent);
    }
    
    /**
     * 清除预览数据
     */
    clearPreviewData(): void {
        const reviveComp = this.getReviveComponent();
        if (reviveComp) {
            reviveComp.cachedReviveBlocks = null;
            reviveComp.isPreviewMode = false;
        }
    }
    
    /**
     * 获取剩余复活次数
     * @returns 剩余复活次数
     */
    getReviveCount(): number {
        const reviveComp = this.getReviveComponent();
        return reviveComp ? reviveComp.nCount : 0;
    }
    
    /**
     * 扣除复活次数
     * @returns 是否成功扣除
     */
    consumeReviveCount(): boolean {
        const reviveComp = this.getReviveComponent();
        if (reviveComp && reviveComp.nCount > 0) {
            reviveComp.nCount--;
            return true;
        }
        return false;
    }
    
    /**
     * 获取场景实体
     * @returns 场景实体ID
     */
    private getSceneEntity(): number | null {
        const sceneEntities = this.world.query([BoardScene]);
        return sceneEntities.length > 0 ? sceneEntities[0] : null;
    }
}
