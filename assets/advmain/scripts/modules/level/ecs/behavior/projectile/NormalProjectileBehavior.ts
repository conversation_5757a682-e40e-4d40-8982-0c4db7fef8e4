import { ProjectileComponent } from '../../components/combat/ProjectileComponent';
import NodeComponent from '../../components/NodeComponent';
import { BehaviorConst } from '../../cores/center/BehaviorCenter';
import { ProjectilePathBehaviorConst } from '../../registry/behavior/ProjectilePathBehaviorRegistry';
import { IProjectileBehaviorConfig, ProjectileBehaviorBase } from './ProjectileBehaviorBase';
import { PathCalculationData } from '../projectilePath/ProjectilePathBehaviorBase';

export interface INormalProjectileConfig extends IProjectileBehaviorConfig {
    /**子弹路径行为类型 */
    projectilePathBehaviorType: ProjectilePathBehaviorConst;
    /**命中效果 */
    onHitBuffs: string[];
}

/**普通投射物 */
export class NormalProjectileBehavior extends ProjectileBehaviorBase<INormalProjectileConfig> {
    private readonly pathParamsCache: PathCalculationData = {
        startPos: { x: 0, y: 0 },
        targetPos: { x: 0, y: 0 },
        elapsedTime: 0,
        deltaTime: 0,
        speed: 200,
        moveTime: 0,
    };

    onCreate(entityId: number, projectileComponent: ProjectileComponent): void {
        // 初始化速度管理数据
        this.resetSpeedManagement(projectileComponent);
    }

    onDestroy(entityId: number, projectileComponent: ProjectileComponent): void {
        // 清理速度管理数据
        delete projectileComponent.pathPrecomputedData.speedManagement;
    }

    onHit(entityId: number, projectileComponent: ProjectileComponent, targetEntityId: number): void {
        // 处理命中逻辑
        const config = this.config;
        if (config.onHitBuffs && config.onHitBuffs.length > 0) {
            console.log(`普通投射物 ${projectileComponent.projectileBehaviorId} 命中实体 ${targetEntityId}，应用buff:`, config.onHitBuffs);
        }
    }

    onMove(entityId: number, projectileComponent: ProjectileComponent, worldTime: number, deltaTime: number): boolean {
        const config = this.config;
        const actionContext = projectileComponent.context;

        // 获取路径行为
        const pathBehavior = this.world.behaviorCenter.getBehaviorByType(BehaviorConst.ProjectilePath, config.projectilePathBehaviorType);

        if (!pathBehavior) {
            console.error(`普通投射物 ${projectileComponent.projectileBehaviorId} 缺少路径行为: ${config.projectilePathBehaviorType}`);
            return false;
        }

        // 获取目标位置
        const targetPos = this.world.handlerCenter.actionHandler.getTargetPosition(actionContext);
        if (!targetPos) {
            console.error(`普通投射物 ${projectileComponent.projectileBehaviorId} 缺少目标位置`);
            return false;
        }

        // 使用智能速度计算
        const speed = this.getSmartSpeed(config.moveParams, projectileComponent.startPosition, targetPos, projectileComponent);

        // 计算路径
        const elapsedTime = worldTime - projectileComponent.createTime;
        const pathParamsCache = this.pathParamsCache;

        pathParamsCache.startPos.x = projectileComponent.startPosition.x;
        pathParamsCache.startPos.y = projectileComponent.startPosition.y;
        pathParamsCache.targetPos.x = targetPos.x;
        pathParamsCache.targetPos.y = targetPos.y;
        pathParamsCache.elapsedTime = elapsedTime;
        pathParamsCache.deltaTime = deltaTime;
        pathParamsCache.speed = speed;
        pathParamsCache.moveTime = config.moveParams.moveTime;

        const pathResult = pathBehavior.calculatePath(pathParamsCache, projectileComponent);

        // 检查路径是否完成
        if (pathResult.isComplete) {
            if (actionContext.targetEntityId) {
                console.log(`普通投射物 ${projectileComponent.projectileBehaviorId} 命中实体 ${actionContext.targetEntityId}`);
                this.onStartHit(entityId, projectileComponent, actionContext.targetEntityId);
            }
            return false; // 抵达目标，需要销毁
        }

        // 更新位置
        const positionComponent = this.world.getComponent(entityId, NodeComponent);
        if (positionComponent) {
            positionComponent.x = pathResult.position.x;
            positionComponent.y = pathResult.position.y;
        }
        return true; // 继续存活
    }
}
