import { RenderComponent } from '../components/RenderComponent';
import { DestroyTag } from '../components/tag/DestroyTag';
import { TempleteBase } from '../cores/center/TempleteCenter';
import { RelationName } from '../cores/center/EntityCenter/WorldRelation';

/**
 * 收集图标模板必传参数
 */
export interface CollectIconTempleteMustArgs {
    /** 元素类型 */
    type: number;
    /**起始位置实体 */
    startEntity: number;
    /**父实体 */
    parentEntity: number;
    /**目标参数 */
    targetParam: any;
}

/**
 * 收集图标模板可选参数
 */
export interface CollectIconTempleteOptionalArgs {}

/**
 * 收集图标模板
 */
export class CollectIconTemplete extends TempleteBase<CollectIconTempleteMustArgs, CollectIconTempleteOptionalArgs> {
    getDefaultOptionalArgs(mustArgs: CollectIconTempleteMustArgs): CollectIconTempleteOptionalArgs {
        return {};
    }
    createTemplete(mustArgs: CollectIconTempleteMustArgs, optionalArgs?: CollectIconTempleteOptionalArgs): number {
        const { type, startEntity, targetParam, parentEntity } = mustArgs;
        const world = this.world;
        const entity = world.createEntity();
        world.addRelation(parentEntity, entity, RelationName.PARENT_CHILD);
        world.addComponent(entity, RenderComponent, 'ecsview_init_Icon_collect', {
            data: { type, targetParam, startEntity },
            isBreakLink: true,
        });
        world.addComponent(entity, DestroyTag);
        return entity;
    }
}
