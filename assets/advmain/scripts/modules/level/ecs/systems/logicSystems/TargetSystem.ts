import TargetComponent from '../../components/special/TargetComponent';
import { System } from '../../cores/System';
import { ECSEvent, GameEventMap } from '../../GameEvent';

/**目标系统 */
export default class TargetSystem extends System {
    init(): void {
        this.world.eventBus.on(ECSEvent.GameEvent.TARGET_CHANGE, this.changeTarget, this);
    }
    dispose(): void {
        this.world.eventBus.off(ECSEvent.GameEvent.TARGET_CHANGE, this.changeTarget, this);
    }
    update(dt: number): void {}

    private changeTarget(data: GameEventMap[ECSEvent.GameEvent.TARGET_CHANGE]) {
        const targets = this.world.getComponent(this.world.query([TargetComponent])[0], TargetComponent).targets;
        const target = targets.find((t) => t.key === data.key);
        if (!target) return;
        target.current = Math.min(target.to, target.current + data.change);
        //结束判断
        if (target.current >= target.to) this.checkFinish(targets);
    }

    /**完成判断 */
    private checkFinish(targets: TargetComponent['targets']) {
        for (let i = 0, len = targets.length; i < len; i++) {
            const t = targets[i];
            if (t.current < t.to) return;
        }
        this.world.eventBus.emit(ECSEvent.GameEvent.GAME_WIN);
    }
}
