import { LayerType } from "../type/LayerType";

/** 业务层级配置 */
const BUSINESS_LAYER_CONFIGS: falcon.ILayerConfig[] = [
    { type: LayerType.PRELOAD, name: 'preloadLayer', zIndex: 0, opacity: 0 },
    { type: LayerType.SCENE, name: 'sceneLayer', zIndex: 1 },
    { type: LayerType.GAME_EFFECT, name: 'gameEffectLayer', zIndex: 3 },
    { type: LayerType.GUIDE, name: 'guideLayer', zIndex: 4 },
    { type: LayerType.LOADING, name: 'loadingLayer', zIndex: 5 },
    { type: LayerType.UI, name: 'uiLayer', zIndex: 6 },
    { type: LayerType.GAME_ALERT, name: 'gameAlertLayer', zIndex: 7 },
    { type: LayerType.ALERT, name: 'alertLayer', zIndex: 8 },
    { type: LayerType.EFFECT, name: 'effectLayer', zIndex: 9 },
    { type: LayerType.TIP, name: 'tipLayer', zIndex: 10 }
];

// 层级节点存储
const layerNodes: { [key: string]: cc.Node } = {};

// 初始化所有层级节点
function initLayerNodes() {
    layerNodes[LayerType.PRELOAD] = new cc.Node();
    layerNodes[LayerType.SCENE] = new cc.Node();
    layerNodes[LayerType.GAME_EFFECT] = new cc.Node();
    layerNodes[LayerType.GUIDE] = new cc.Node();
    layerNodes[LayerType.LOADING] = new cc.Node();
    layerNodes[LayerType.UI] = new cc.Node();
    layerNodes[LayerType.GAME_ALERT] = new cc.Node();
    layerNodes[LayerType.ALERT] = new cc.Node();
    layerNodes[LayerType.EFFECT] = new cc.Node();
    layerNodes[LayerType.TIP] = new cc.Node();
}

// 立即初始化
initLayerNodes();

// Getter 函数，确保总是返回最新的节点引用
function getPreloadLayer(): cc.Node {
    return layerNodes[LayerType.PRELOAD];
}

function getSceneLayer(): cc.Node {
    return layerNodes[LayerType.SCENE];
}

function getGameEffectLayer(): cc.Node {
    return layerNodes[LayerType.GAME_EFFECT];
}

function getGuideLayer(): cc.Node {
    return layerNodes[LayerType.GUIDE];
}

function getLoadingLayer(): cc.Node {
    return layerNodes[LayerType.LOADING];
}

function getUiLayer(): cc.Node {
    return layerNodes[LayerType.UI];
}

function getGameAlertLayer(): cc.Node {
    return layerNodes[LayerType.GAME_ALERT];
}

function getAlertLayer(): cc.Node {
    return layerNodes[LayerType.ALERT];
}

function getEffectLayer(): cc.Node {
    return layerNodes[LayerType.EFFECT];
}

function getTipLayer(): cc.Node {
    return layerNodes[LayerType.TIP];
}

/**
 * 获取指定类型的层级节点
 * @param layerType 层级类型
 */
export function getLayerNode(layerType: LayerType): cc.Node {
    return layerNodes[layerType];
}

// 使用 Proxy 确保 const 导出始终指向最新的节点

/** 预加载 */
export const preloadLayer: cc.Node = new Proxy({} as cc.Node, {
    get(target, prop) {
        const layer = getPreloadLayer();
        if (typeof layer[prop] === 'function') {
            return layer[prop].bind(layer);
        }
        return layer[prop];
    },
    set(target, prop, value) {
        const layer = getPreloadLayer();
        layer[prop] = value;
        return true;
    }
});

/** 场景 */
export const sceneLayer: cc.Node = new Proxy({} as cc.Node, {
    get(target, prop) {
        const layer = getSceneLayer();
        if (typeof layer[prop] === 'function') {
            return layer[prop].bind(layer);
        }
        return layer[prop];
    },
    set(target, prop, value) {
        const layer = getSceneLayer();
        layer[prop] = value;
        return true;
    }
});

/** 通用游戏效果 */
export const gameEffectLayer: cc.Node = new Proxy({} as cc.Node, {
    get(target, prop) {
        const layer = getGameEffectLayer();
        if (typeof layer[prop] === 'function') {
            return layer[prop].bind(layer);
        }
        return layer[prop];
    },
    set(target, prop, value) {
        const layer = getGameEffectLayer();
        layer[prop] = value;
        return true;
    }
});

/** 新手引导 */
export const guideLayer: cc.Node = new Proxy({} as cc.Node, {
    get(target, prop) {
        const layer = getGuideLayer();
        if (typeof layer[prop] === 'function') {
            return layer[prop].bind(layer);
        }
        return layer[prop];
    },
    set(target, prop, value) {
        const layer = getGuideLayer();
        layer[prop] = value;
        return true;
    }
});

/** loading */
export const loadingLayer: cc.Node = new Proxy({} as cc.Node, {
    get(target, prop) {
        const layer = getLoadingLayer();
        if (typeof layer[prop] === 'function') {
            return layer[prop].bind(layer);
        }
        return layer[prop];
    },
    set(target, prop, value) {
        const layer = getLoadingLayer();
        layer[prop] = value;
        return true;
    }
});

/** 所有的通用ui图层都放在这个图层中*/
export const uiLayer: cc.Node = new Proxy({} as cc.Node, {
    get(target, prop) {
        const layer = getUiLayer();
        if (typeof layer[prop] === 'function') {
            return layer[prop].bind(layer);
        }
        return layer[prop];
    },
    set(target, prop, value) {
        const layer = getUiLayer();
        layer[prop] = value;
        return true;
    }
});

/** 游戏弹窗 */
export const gameAlertLayer: cc.Node = new Proxy({} as cc.Node, {
    get(target, prop) {
        const layer = getGameAlertLayer();
        if (typeof layer[prop] === 'function') {
            return layer[prop].bind(layer);
        }
        return layer[prop];
    },
    set(target, prop, value) {
        const layer = getGameAlertLayer();
        layer[prop] = value;
        return true;
    }
});

/** 游戏弹窗 */
export const alertLayer: cc.Node = new Proxy({} as cc.Node, {
    get(target, prop) {
        const layer = getAlertLayer();
        if (typeof layer[prop] === 'function') {
            return layer[prop].bind(layer);
        }
        return layer[prop];
    },
    set(target, prop, value) {
        const layer = getAlertLayer();
        layer[prop] = value;
        return true;
    }
});

/** effectLayer */
export const effectLayer: cc.Node = new Proxy({} as cc.Node, {
    get(target, prop) {
        const layer = getEffectLayer();
        if (typeof layer[prop] === 'function') {
            return layer[prop].bind(layer);
        }
        return layer[prop];
    },
    set(target, prop, value) {
        const layer = getEffectLayer();
        layer[prop] = value;
        return true;
    }
});

/** tipLayer */
export const tipLayer: cc.Node = new Proxy({} as cc.Node, {
    get(target, prop) {
        const layer = getTipLayer();
        if (typeof layer[prop] === 'function') {
            return layer[prop].bind(layer);
        }
        return layer[prop];
    },
    set(target, prop, value) {
        const layer = getTipLayer();
        layer[prop] = value;
        return true;
    }
});

/**
 * 初始化游戏层级
 * @param configs 自定义层级配置
 */
export function initGameLayer(configs: falcon.ILayerConfig[] = BUSINESS_LAYER_CONFIGS) {
    falcon.layerManager.init();

    // 使用预定义的层级节点进行初始化
    configs.forEach(config => {
        // 检查现有节点是否有效，如果无效则重新创建
        let node = layerNodes[config.type];
        if (!node || !cc.isValid(node)) {
            node = new cc.Node();
            layerNodes[config.type] = node; // 更新引用
        }
        
        falcon.layer.initNodeConfig(node, config, falcon.layerManager.layerScene);
        falcon.layerManager.setLayerNode(config.type, node);
    });
}


