import { BoardScene } from '../../components/board/BoardScene';
import { TempleteType } from '../../registry/templete/TempleteRegistry';
import { EcsViewId } from '../../renderWorld/ECSViewBase';
import { ECSRuleBase, IECSRuleConfig } from '../core/ECSRuleBase';
/**
 * 连胜001规则配置
 */
export interface IWinStreak001RuleConfig extends IECSRuleConfig {
    /** 连胜视图ID */
    ecsViewId: EcsViewId;
    /** 初始连胜数 */
    initialWinNum?: number;
    /** 连胜字段key */
    winStreakCountKey: string;
}
/**
 * 连胜001规则
 * 负责创建和管理连胜功能的ECS视图
 */
export class WinStreak001Rule extends ECSRuleBase<IWinStreak001RuleConfig> {
    /**
     * 检查是否可以执行
     */
    canExecute(): boolean {
        // 检查是否有场景实体
        const sceneEntities = this.world.query([BoardScene]);
        if (sceneEntities.length === 0) {
            console.warn('WinStreak001Rule: 未找到BoardScene实体');
            return false;
        }
        // 检查配置是否有效
        if (!this.config.ecsViewId) {
            console.warn('WinStreak001Rule: 缺少ecsViewId配置');
            return false;
        }
        return true;
    }
    /**
     * 执行规则
     */
    execute(): void {
        console.log('WinStreak001Rule execute - 创建连胜视图');
        const world = this.world;
        const sceneEntities = world.query([BoardScene]);
        const parentEntity = sceneEntities[0];
        // 获取配置参数
        const { ecsViewId, initialWinNum = 0 } = this.config;
        const cacheCenter = world.cacheCenter;
        const winStreakCount = cacheCenter.__winStreakData__[this.config.winStreakCountKey] || 0;
        if (winStreakCount === 0) {
            return;
        }
        // 准备连胜数据
        const winStreakData = {
            winNum: winStreakCount,
            timestamp: Date.now(),
        };
        // 创建连胜视图实体
        const winStreakEntity = world.templeteCenter.createTempleteEntity(
            TempleteType.Render,
            {
                parentEntity: parentEntity,
                ecsViewId: ecsViewId,
            },
            {
                nodeParam: {
                    x: 0,
                    y: 0,
                    zIndex: 100, // 确保在较高层级显示
                },
                renderParam: {
                    data: winStreakData,
                    isBreakLink: true, // 保持与逻辑世界的连接
                },
            },
        );
        // 记录创建的实体ID，方便后续操作
        console.log(`WinStreak001Rule: 创建连胜视图实体 ID=${winStreakEntity}`);
    }
}
