/**
 * 全屏震动管理器
 * 用于在游戏中实现全屏震动效果
 */
export class FullScreenShakeManager {
    private _isShaking: boolean;

    /**
     * 是否在震动中
     */
    get isShaking() {
        return this._isShaking;
    }

    /**
     * 使用幅度的震动方式
     * @param duration 持续时间（秒）
     * @param amplitude 震动幅度（像素）
     * @param dt 抖动频率（秒）
     */
    startWithAmplitude(duration: number, amplitude: number, dt: number = 0.066): void {
        if (this._isShaking) return;

        const cameraNode = cc.director.getScene().getComponentInChildren(cc.Camera)?.node;
        if (!cameraNode || !cc.isValid(cameraNode)) return;

        this._isShaking = true;

        const originalPos = { x: cameraNode.x, y: cameraNode.y };

        // 简化震动路径
        cc.tween(cameraNode)
            .repeatForever(
                cc
                    .tween()
                    .to(dt, { x: originalPos.x + amplitude, y: originalPos.y - amplitude })
                    .to(dt, { x: originalPos.x - amplitude, y: originalPos.y + amplitude })
                    .to(dt, { x: originalPos.x - amplitude, y: originalPos.y - amplitude })
                    .to(dt, { x: originalPos.x + amplitude, y: originalPos.y + amplitude })
                    .to(dt, { x: originalPos.x, y: originalPos.y }),
            )
            .start();
        // 由于禁止setTimeout,所以使用cc.tween的delay方法来停止震动
        cc.tween(cameraNode)
            .delay(duration)
            .call(() => {
                if (cameraNode) {
                    cameraNode.stopAllActions();
                    if (originalPos && cameraNode['_trs']) cameraNode.setPosition(originalPos.x, originalPos.y); // 恢复原始位置
                }
                this._isShaking = false;
            })
            .start(); // 按照给定的持续时间停止震动
    }
}
