import HPComponent from '../../components/special/HPComponent';
import { ECSEvent } from '../../GameEvent';
import { IAIBehaviorConfig, AIBehaviorBase } from './AIBehaviorBase';

/**块AI，所有块的通用处理都在这里，其他块的特殊处理需要继承此类 */
export class CellAIBehavior extends AIBehaviorBase<IAIBehaviorConfig> {
    bindEntityEvent(): ECSEvent.EntityEvent[] {
        return [ECSEvent.EntityEvent.ELIMINATIONED];
    }
    receiveEntityEvent(entityId: number, event: ECSEvent.EntityEvent, eventData: any): void {
        switch (event) {
            case ECSEvent.EntityEvent.ELIMINATIONED:
                this.eliminationed(entityId);
                break;
        }
    }
    initAI(entityId: number): void {}
    disposeAI(entityId: number): void {}

    /**被消除 */
    protected eliminationed(entityId: number) {
        if (this.hpChange(entityId, -1)) return;
        this.removed(entityId);
    }

    /**被移除 */
    protected removed(entityId: number) {
        this.world.destroyEntity(entityId);
        this.world.eventBus.emit(ECSEvent.GameEvent.CELL_REMOVED, entityId);
    }

    /**血量变化 */
    protected hpChange(entityId: number, change: number) {
        const hpComponent = this.world.getComponent(entityId, HPComponent);
        return hpComponent && (hpComponent.hp += change) > 0;
    }
}
