/**并发执行器 */
export class ConcurrentRunner {
    /**默认最大并发数 */
    private static readonly DEFAULT_MAX_CONCURRENT: number = 3;

    /**并发执行任务 */
    public static async run<T>(tasks: (() => Promise<T>)[], maxConcurrent: number = this.DEFAULT_MAX_CONCURRENT): Promise<T[]> {
        const results: T[] = [];
        const executing = new Set<Promise<void>>();

        for (const task of tasks) {
            const promise = (async () => {
                const result = await task();
                results.push(result);
            })();
            executing.add(promise);
            promise.then(() => executing.delete(promise)).catch(() => executing.delete(promise));

            if (executing.size >= maxConcurrent) {
                await Promise.race(executing);
            }
        }

        await Promise.all(executing);
        return results;
    }
} 