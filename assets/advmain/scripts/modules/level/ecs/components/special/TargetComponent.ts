import { TargetParam } from '../../GameEventData';
import { Component } from '../../cores/Component';

export default class TargetComponent extends Component {
    /**目标 */
    public targets: TargetParam[];

    constructor(
        /**目标配置，[类型, 数量][] */
        targetList: number[][],
        /**当前完成计数 */
        currentList: number[] = [],
    ) {
        super();
        if (targetList) this.targets = targetList.map(([key, to], index) => ({ key, to, current: currentList[index] || 0 }));
    }
}
