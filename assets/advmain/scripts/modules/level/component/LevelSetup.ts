import { SubGameBridge } from '../../../base/SubGameBridge';
import { AudioConfig } from '../../audio/config/AudioConfig';
import { PrefabConfig } from '../../prefab/PrefabConfig';
import { enSetupKeys, ISetupInfoNodePos, SetupConfig } from '../config/LevelSetupConfig';
import { E_Levels_SetupClick } from '../events/E_Levels_SetupClick';

export interface ISetupState {
    /**顶部资源前缀*/
    topBtnPre?: string;
    /**底部资源前缀*/
    bottomBtnPre?: string;
    /**文本资源前缀*/
    txtImgPre?: string;
    /**顶部icon区域高度*/
    nodePosData?: ISetupInfoNodePos;
    keys: enSetupKeys[];
    /**数据状态 key对应的数据状态 */
    value: { [key: string]: boolean };
    // 红点开启状态
    redPoint: { [key: string]: boolean };
}

const { ccclass, property } = cc._decorator;
/**
 * 设置
 */
@ccclass
export default class LevelSetup extends falcon.Component<Partial<ISetupState>> {
    /**总信息节点 */
    @property(cc.Node)
    infoNode: cc.Node = null;

    /**title节点 */
    @property(cc.Node)
    titleNode: cc.Node = null;

    /**标题图 */
    @property(cc.Sprite)
    titleImg: cc.Sprite = null;

    /**顶部按钮挂点 */
    @property(cc.Node)
    topNode: cc.Node = null;

    /**分割线 */
    @property(cc.Sprite)
    line: cc.Sprite = null;

    /**底部按钮挂点 */
    @property(cc.Node)
    bottomNode: cc.Node = null;

    private currClickType = '';

    /**实例的节点 */
    private nodeList: { [key: string]: cc.Node } = {};

    protected onLoad(): void {
        this.currClickType = '';
    }

    render() {
        // 获取topKeys和bottomKeys
        const topKeys = this.state.keys.filter((v) => SetupConfig.topList.indexOf(v) >= 0);
        const bottomKeys = this.state.keys.filter((v) => SetupConfig.bottomList.indexOf(v) >= 0);
        // 分割线
        this.line.node.active = !!topKeys.length;
        // 设置位置 布局 对齐
        const nodePosData: ISetupInfoNodePos = this.state.nodePosData || SetupConfig.nodePosData;
        // top
        this.topNode.height = this.line.node.active ? nodePosData.topLayout.nodeHeight : 0;
        this.topNode.width = nodePosData.topLayout.spaceX * (topKeys.length - 1);
        // 计算topNode的宽度
        for (let i = 0; i < topKeys.length; i++) {
            this.topNode.width += SetupConfig.keysInstallSizes[topKeys[i]].width || 90;
        }
        // 分割线
        this.line.node.width = nodePosData.lineWidth;
        // 底部
        const botLay = nodePosData.bottomLayout;
        this.bottomNode.height = botLay.spaceY * (bottomKeys.length - 1) + botLay.bottom + botLay.top;
        // 计算bottomNode的高度
        for (let i = 0; i < bottomKeys.length; i++) {
            this.bottomNode.height += SetupConfig.keysInstallSizes[bottomKeys[i]].height || 148;
        }
        // 总节点
        this.infoNode.width = nodePosData.infoNodeWidth;
        this.infoNode.height = this.titleNode.height + this.topNode.height + this.line.node.height + this.bottomNode.height;
        // 设置y布局
        this.titleNode.y = this.infoNode.height / 2 - this.titleNode.height / 2;
        this.topNode.y = this.titleNode.y - this.titleNode.height / 2 - this.topNode.height / 2;
        this.line.node.y = this.topNode.y - this.topNode.height / 2 - this.line.node.height / 2;
        this.bottomNode.y = this.line.node.y - this.line.node.height / 2 - this.bottomNode.height / 2;

        this.initList(topKeys, bottomKeys);
    }

    /**列表刷新 */
    protected initList(topKeys: enSetupKeys[], bottomKeys: enSetupKeys[]) {
        // 更新后 删除没有的
        const insKeys = Object.keys(this.nodeList);
        const deleteKeys = insKeys.filter((v) => this.state.keys.indexOf(v as enSetupKeys) == -1);
        for (let i = 0; i < deleteKeys.length; i++) {
            if (this.nodeList[deleteKeys[i]]) {
                this.nodeList[deleteKeys[i]].destroy();
                delete this.nodeList[deleteKeys[i]];
            }
        }
        // 刷新
        const nodePosData: ISetupInfoNodePos = this.state.nodePosData || SetupConfig.nodePosData;
        const arr: { type: number; keys: enSetupKeys[]; path: string; parent: cc.Node; x: number; y: number }[] = [
            {
                type: 0,
                keys: topKeys,
                path: PrefabConfig.SetupIconItem.url,
                parent: this.topNode,
                x: -this.topNode.width / 2,
                y: nodePosData.topLayout.top,
            },
            {
                type: 1,
                keys: bottomKeys,
                path: PrefabConfig.SetupBtnItem.url,
                parent: this.bottomNode,
                x: 0,
                y: this.bottomNode.height / 2 - nodePosData.bottomLayout.top,
            },
        ];

        for (let i = 0; i < arr.length; i++) {
            let { x, y } = arr[i];
            for (let k = 0; k < arr[i].keys.length; k++) {
                const key = arr[i].keys[k];
                if (!this.nodeList[key]) {
                    this.loadResByPath(
                        arr[i].path,
                        (prefab) => {
                            if (!this.nodeList[key]) {
                                const node = cc.instantiate(prefab);
                                arr[i].parent.addChild(node);
                                [x, y] = this.refreshBtnState(node, key, arr[i].type, x, y);
                                this.nodeList[key] = node;
                            }
                        },
                        this,
                    );
                } else {
                    [x, y] = this.refreshBtnState(this.nodeList[key], key, arr[i].type, x, y);
                }
            }
        }
    }

    /**刷新按钮状态 */
    private refreshBtnState(node: cc.Node, key: enSetupKeys, type: number, x: number, y: number): [number, number] {
        const data = this.state.value?.[key] || false;
        const point = this.state.redPoint?.[key] || false;

        const nodePosData: ISetupInfoNodePos = this.state.nodePosData || SetupConfig.nodePosData;
        const com: falcon.Component = node.getComponent(node.name);
        if (com) {
            com.setState({
                key: key,
                value: data,
                redPoint: point,
                topBtnPre: this.state.topBtnPre,
                bottomBtnPre: this.state.bottomBtnPre,
                txtImgPre: this.state.txtImgPre,
            });
        }
        node.setContentSize(SetupConfig.keysInstallSizes[key].width, SetupConfig.keysInstallSizes[key].height);
        if (type == 0) {
            x += node.width / 2;
        } else {
            y -= node.height / 2;
        }
        node.setPosition(x, y);
        // node.name = key;
        if (type == 0) {
            x += node.width / 2 + (nodePosData.topLayout.spaceX || 0);
        } else {
            y -= node.height / 2 + (nodePosData.bottomLayout.spaceY || 0);
        }
        return [x, y];
    }

    /**加载资源 */
    protected loadResByPath(path: string, cb: (p: cc.Prefab) => void, cbObj: object) {
        SubGameBridge.loadByBundle('advres', path, cc.Prefab, (err, prefab) => {
            if (err) {
                console.error(err);
                return;
            }
            if (cb && cbObj) {
                cb.call(cbObj, prefab);
            }
        });
    }

    @falcon.throttle(300)
    onClick(event: cc.Event.EventTouch, data: any) {
        this.currClickType = data;
        falcon.EventManager.dispatchModuleEvent(new E_Levels_SetupClick(data));
    }

    protected onDisable(): void {
        //点击Module 关闭时 播放音效
        if (this.currClickType !== enSetupKeys.closeSelf) {
            falcon.audioInfo.play(AudioConfig.s_button);
        }
    }
}
