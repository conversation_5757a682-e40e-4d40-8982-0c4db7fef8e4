import { BoardScene } from '../components/board/BoardScene';
import { BoardSceneColorsComponent } from '../components/special/BoardSceneColorsComponent';
import { CellColor } from '../define/BoardDefine';
import { ECSRuleBase, IECSRuleConfig } from './core/ECSRuleBase';

export interface IInitBoardColorsRuleConfig extends IECSRuleConfig {
    /** 当前关卡可用颜色列表 */
    colors: CellColor[];
}

export class InitBoardSceneColorsRule extends ECSRuleBase<IInitBoardColorsRuleConfig> {
    canExecute(): boolean {
        return true;
    }
    execute(): void {
        // Initialize cell colors using the provided colors
        const world = this.world;
        const entityId = this.world.query([BoardScene])[0];
        world.addComponent(entityId, BoardSceneColorsComponent, this.config.colors);
    }
}
