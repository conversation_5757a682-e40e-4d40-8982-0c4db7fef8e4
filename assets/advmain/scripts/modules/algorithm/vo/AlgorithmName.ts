import { OFFER_TYPE } from "../type/AlgorithmType";

class AlgorithmName {
    // 算法预期的ID
    get algoExpectedId(): OFFER_TYPE {
        return falcon.storage.getItem(`chapterAlgoExpectedId`, OFFER_TYPE.NONE) as OFFER_TYPE;
    }

    setAlgoExpectedId(value: OFFER_TYPE) {
        if (value === OFFER_TYPE.NONE || algorithmName.algoExpectedId === OFFER_TYPE.NONE) {
            falcon.storage.setItem(`chapterAlgoExpectedId`, value);
        }
    }

    // 算法实际的名字
    get algoActualName(): string[] {
        return falcon.storage.getItem(`chapterAlgoActualName`, []) as string[];
    }

    setAlgoActualName(value: string[]) {
        falcon.storage.setItem(`chapterAlgoActualName`, value);
    }

    // 算法实际的ID
    get algoActualId(): OFFER_TYPE {
        return falcon.storage.getItem(`chapterAlgoActualId`, OFFER_TYPE.NONE) as OFFER_TYPE;
    }

    setAlgoActualId(value: OFFER_TYPE) {
        falcon.storage.setItem(`chapterAlgoActualId`, value);
    }

}

export const algorithmName = new AlgorithmName();

