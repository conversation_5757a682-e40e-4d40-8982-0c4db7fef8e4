import { RelationName } from '../../cores/center/EntityCenter/WorldRelation';
import { TempleteType } from '../../registry/templete/TempleteRegistry';
import { CellAIBehavior } from './CellAIBehavior';

/**玻璃块AI */
export class GlassAIBehavior extends CellAIBehavior {
    removed(entityId: number) {
        super.removed(entityId);
        //临时写法，等可以获取相对逻辑坐标后这里应该直接取entityId的坐标
        const position = this.world.utilCenter.boardUtil.getEntityRootPoint(this.world.getSources(entityId, RelationName.SLOT_CELL)[0]);
        this.world.templeteCenter.createTempleteEntity(
            TempleteType.Effect,
            { parentEntity: this.world.ECSLayerType.EffectLayerEntity, ecsViewId: 'ecsview_simple_effect' },
            {
                nodeParam: { position },
                renderParam: {
                    data: {
                        aniName: `gameplay_spine_glass_2_1`,
                        src: 'cell/120/gameplay_spine_glass',
                        bundleName: 'level',
                    },
                },
            },
        );
    }
}
