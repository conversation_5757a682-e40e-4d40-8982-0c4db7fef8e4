import { SkinType, SkinConfig } from '../define/SkinConfig';

// Trie 节点实现
class TrieNode {
    isEndOfWord: boolean = false;
    children: Map<string, TrieNode> = new Map();
    value: string | null = null;
}

// 前缀匹配器类
class PrefixMatcher {
    private root: TrieNode;

    constructor(strList?: string[]) {
        this.root = new TrieNode();
        if (strList) {
            this.build(strList);
        }
    }

    // 构建或重新构建 Trie
    build(strList: string[]) {
        // 重置树
        this.root = new TrieNode();

        for (const word of strList) {
            this.insert(word);
        }
    }

    // 插入单词
    private insert(word: string) {
        let currentNode = this.root;

        for (const char of word) {
            if (!currentNode.children.has(char)) {
                currentNode.children.set(char, new TrieNode());
            }
            currentNode = currentNode.children.get(char)!;
        }

        currentNode.isEndOfWord = true;
        currentNode.value = word;
    }

    // 查找匹配前缀
    findMatchingPrefix(str: string): string {
        let currentNode = this.root;
        let longestMatch = '';

        for (const char of str) {
            if (!currentNode.children.has(char)) {
                break;
            }

            currentNode = currentNode.children.get(char)!;

            if (currentNode.isEndOfWord && currentNode.value) {
                longestMatch = currentNode.value;
            }
        }

        return longestMatch;
    }
}

/**皮肤管理 */
export class SkinMgr {
    // 使用WeakMap自动管理内存
    private static matcherCache = new WeakMap<readonly string[], PrefixMatcher>();
    // 缓存资源路径
    private static skinCache: { [k: string]: { [key: string]: string } } = { chapter: {}, level: {} };

    /**获取真实资源路径 */
    public static realSrc(bunldeName: string, src: string, type: SkinType = 0) {
        let resultSrc = this.skinCache[bunldeName][src];
        if (resultSrc) return resultSrc;
        let skinList: readonly string[] = SkinConfig.res[bunldeName];
        //单个资源匹配到，直接增加皮肤后缀
        if (skinList?.includes(src)) {
            resultSrc = src + SkinType[type];
        } else {
            //单个目录匹配到，使用同级目录增加皮肤后缀
            let str: string;
            skinList = SkinConfig.src[bunldeName];
            if (skinList && (str = this.findMatchingPrefix(skinList, src))) {
                resultSrc = `${str}${SkinType[type]}${src.replace(str, '')}`;
            } else {
                //一级子目录匹配到，使用子目录增加皮肤后缀
                skinList = SkinConfig.child[bunldeName];
                if (skinList && (str = this.findMatchingPrefix(skinList, src))) {
                    let leftList = src.replace(str, '').split('/');
                    resultSrc = src.replace(`/${leftList[1]}/`, `/${leftList[1]}${SkinType[type]}/`);
                }
            }
        }
        if (!resultSrc) resultSrc = src;
        this.skinCache[bunldeName][src] = resultSrc;
        return resultSrc;
    }

    // 获取匹配器（高效复用）
    private static getMatcherForList(strList: readonly string[]): PrefixMatcher {
        // 尝试获取已存在的匹配器
        const existing = this.matcherCache.get(strList);
        if (existing) {
            return existing;
        }

        // 创建新的匹配器并缓存
        const matcher = new PrefixMatcher();
        matcher.build(Array.from(strList)); // 复制数组以防外部修改

        this.matcherCache.set(strList, matcher);
        return matcher;
    }

    // 便捷匹配方法
    private static findMatchingPrefix(strList: readonly string[], str: string): string {
        return this.getMatcherForList(strList).findMatchingPrefix(str);
    }
}
