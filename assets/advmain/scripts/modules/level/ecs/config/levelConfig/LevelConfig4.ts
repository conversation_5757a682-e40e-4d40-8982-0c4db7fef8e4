import { IOverrideLevelConfig } from '../../cores/OverrideConfigManager';
import { IInitBoardRuleConfig } from '../../rules/InitBoardRule';
import { IInitTargetRuleConfig } from '../../rules/InitTargetRule';

export const LevelConfig4: IOverrideLevelConfig = {
    /** 底板配置 */
    baseLevelId: 'BaseConfig10000',
    /** 关卡ID */
    levelId: 'LevelConfig4',
    /** 重写配置表参数 */
    overrideTableArgs: {
        RuleConfig: {
            rule_init_target:{
                target: [[101, 6], [105, 2]],
            } as IInitTargetRuleConfig,
            rule_init_board: {
                boardIdList: ["large_shape"],
            } as IInitBoardRuleConfig,
        },
    },
    overrideLevelConfig: {
        
    },
    /** 关闭系统 */
    closeSystems: [],
    /** 关闭规则 */
    closeRules: [],
    /** 插入系统 */
    insertSystems: [],
    /** 插入规则 */
    insertRules: [],
    /** 替换系统 */
    replaceSystems: [],
    /** 替换规则 */
    replaceRules: [],
};
