import { AIBehaviorId } from "../behavior/ai/AIBehaviorBase";
import { IBehaviorData } from "../combat/core/BehaviorBase";
import { Component } from "../cores/Component";
/**AI组件-用于驱动实体行为 */
export class AIComponent extends Component {
    /**AI行为Id */
    public aiBehaviorId: AIBehaviorId;
    /**AI行为数据(自定义) */
    public behaviorData: IBehaviorData;
    constructor(aiId:AIBehaviorId) {
        super();
        this.aiBehaviorId = aiId;
        this.behaviorData = {} as IBehaviorData;
    }
}