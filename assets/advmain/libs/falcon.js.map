{"version": 3, "file": "falcon.js", "mappings": "yDACAA,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQE,mBAOR,SAA4BC,GACxB,GAAKA,EAAL,CAGA,IAAIC,EAASD,EAAKE,aAAaC,GAAGC,QAClC,GAAKH,EAAL,CAGA,IAAII,EAAOF,GAAGG,KAAKC,eACfC,EAAQL,GAAGG,KAAKG,iBAWpB,GAVA,SAAkBJ,EAAMG,GACpB,QAAIH,EAAKK,MAAQL,EAAKM,OAAS,IAAa,MAGxCR,GAAGS,IAAIC,KAAOV,GAAGS,IAAIE,aAAcX,GAAGS,IAAIG,aAClCV,EAAKM,OAASN,EAAKK,MAAQM,GAC9BR,EAAMG,OAASH,EAAME,MAAQM,GAG1C,CACIC,CAASZ,EAAMG,GAAQ,CACvB,IAAIG,EAASO,KAAKC,IAAId,EAAKM,OAAQH,EAAMG,QACzCV,EAAOmB,KAAOC,EAA0BV,CAC5C,CAhBA,CAJA,CAqBJ,EA9BA,IAAIK,EAA8B,KAAO,IACrCK,EAA0B,I,iBCG9B1B,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQyB,qBAAkB,EAC1B,IAAIC,EAAc,EAAQ,MACtBC,EAAiC,WACjC,SAASA,IACLC,KAAKC,YAAc,CAAC,CACxB,CA0EA,OAhEAF,EAAgBG,UAAUC,KAAO,SAAUC,EAAYC,EAAcC,EAAeC,EAAWC,GAC3F,IAAIC,EAAQT,KACZ,GAAKQ,GAGAH,GAGAC,EAAL,MAGkBI,IAAdH,IACAA,EAAY,GAECC,EAAOG,WAAxB,IAAoCC,EAAiBJ,EAAOI,eAAgBC,EAAsBL,EAAOK,oBACzG,GAD+IL,EAAOM,oBAC/HJ,IAAnBE,GAAmD,KAAnBA,QAGRF,IAAxBG,GAA6D,KAAxBA,QAGnBH,IAAlBJ,GAAiD,KAAlBA,QAGdI,IAAjBL,GAA+C,KAAjBA,EAAlC,CASA,IAAI9B,EAAO,IAAIG,GAAGqC,KACdC,EAA6BzC,EAAK0C,aAAaC,YAAYC,iBAmB/D,OAlBAf,EAAWgB,SAAS7C,GAEpByC,EAA2BK,aAAc,EACzCL,EAA2BM,qBAAqBhB,GAChDU,EAA2BO,sBAAsBL,YAAYC,gBAAgBK,mBAAmBC,cAC5FT,IACIR,EAAOkB,cAAgBlB,EAAOmB,iBAC9BX,EAA2BY,iBAAiBV,YAAYW,YAAYC,SAAUtB,EAAOkB,aAAclB,EAAOmB,iBAE1GnB,EAAOM,eACPiB,YAAW,WACPtB,EAAMuB,MAAMxB,EAAQH,EAAcC,EAAeC,EAAWS,EAChE,GAAG,IAGHhB,KAAKgC,MAAMxB,EAAQH,EAAcC,EAAeC,EAAWS,IAG5DzC,CA3BP,CAhBA,CA4CJ,EACAwB,EAAgBG,UAAU8B,MAAQ,SAAUxB,EAAQH,EAAcC,EAAeC,EAAWS,GACxF,IAAIL,EAAaH,EAAOG,WAAYC,EAAiBJ,EAAOI,eAAgBC,EAAsBL,EAAOK,oBAAsCL,EAAOM,eAClJH,EACAb,EAAYmC,UAAUC,0BAA0BvB,EAAYK,EAA4BJ,EAAgBC,EAAqBR,EAAcC,EAAeC,GAG1JT,EAAYmC,UAAUE,kBAAkBnB,EAA4BJ,EAAgBC,EAAqBR,EAAcC,EAAeC,EAE9I,EACOR,CACX,CA9EoC,GA+EpC3B,EAAQyB,gBAAkB,IAAIE,C,qBCxF9B,IAAIqC,EAAUpC,MAAQA,KAAKoC,QAAW,SAAUC,EAAGC,GAC/C,IAAIC,EAAsB,mBAAXC,QAAyBH,EAAEG,OAAOC,UACjD,IAAKF,EAAG,OAAOF,EACf,IAAmBK,EAAYC,EAA3BC,EAAIL,EAAEM,KAAKR,GAAOS,EAAK,GAC3B,IACI,WAAc,IAANR,GAAgBA,KAAM,MAAQI,EAAIE,EAAEG,QAAQC,MAAMF,EAAGG,KAAKP,EAAErE,MACxE,CACA,MAAO6E,GAASP,EAAI,CAAEO,MAAOA,EAAS,CACtC,QACI,IACQR,IAAMA,EAAEM,OAAST,EAAIK,EAAU,SAAIL,EAAEM,KAAKD,EAClD,CACA,QAAU,GAAID,EAAG,MAAMA,EAAEO,KAAO,CACpC,CACA,OAAOJ,CACX,EACA5E,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQ+E,uBAKR,SAAgCC,EAAMC,GAClC,GAAID,EAAKE,SAAWD,EAAKC,OACrB,OAAO,EAKX,IAHA,IAAIC,EAAaH,EAAKI,QAAQC,OAC1BC,EAAaL,EAAKG,QAAQC,OAErBb,EAAI,EAAGA,EAAIW,EAAWD,OAAQV,IACnC,GAAIW,EAAWX,KAAOc,EAAWd,GAC7B,OAAO,EAGf,OAAO,CACX,EAjBAxE,EAAQuF,aAmBR,SAAsBC,GAIlB,IAHA,IAAIC,EAEAC,EAAgBF,EAAMJ,QACjBO,EAAMD,EAAcR,OAAS,EAAGS,EAAM,EAAGA,IAAO,CAErD,IAAIC,EAAIvE,KAAKwE,MAAMxE,KAAKyE,UAAYH,EAAM,IAE1CF,EAAKzB,EAAO,CAAC0B,EAAcE,GAAIF,EAAcC,IAAO,GAAID,EAAcC,GAAOF,EAAG,GAAIC,EAAcE,GAAKH,EAAG,EAC9G,CACA,OAAOC,CACX,EA7BA1F,EAAQ+F,YA+BR,SAAqBf,EAAMC,GACvB,GAAID,EAAKE,SAAWD,EAAKC,OACrB,OAAO,EAEX,IAAK,IAAIc,EAAM,EAAGA,EAAMhB,EAAKE,OAAQc,IACjC,GAAIhB,EAAKgB,KAASf,EAAKe,GACnB,OAAO,EAGf,OAAO,CACX,EAxCAhG,EAAQiG,gBA0CR,SAAyBC,EAAKC,EAAWC,GAKrC,OAJIF,EAAIhB,QAAUiB,GACdD,EAAIG,QAERH,EAAIrB,KAAKuB,GACFF,CACX,C,qBCpEA,IACQI,EADJC,EAAa3E,MAAQA,KAAK2E,YACtBD,EAAgB,SAAUE,EAAGC,GAI7B,OAHAH,EAAgBxG,OAAO4G,gBAClB,CAAEC,UAAW,cAAgBC,OAAS,SAAUJ,EAAGC,GAAKD,EAAEG,UAAYF,CAAG,GAC1E,SAAUD,EAAGC,GAAK,IAAK,IAAII,KAAKJ,EAAO3G,OAAOgC,UAAUgF,eAAerC,KAAKgC,EAAGI,KAAIL,EAAEK,GAAKJ,EAAEI,GAAI,EAC7FP,EAAcE,EAAGC,EAC5B,EACO,SAAUD,EAAGC,GAChB,GAAiB,mBAANA,GAA0B,OAANA,EAC3B,MAAM,IAAIM,UAAU,uBAAyBC,OAAOP,GAAK,iCAE7D,SAASQ,IAAOrF,KAAKsF,YAAcV,CAAG,CADtCF,EAAcE,EAAGC,GAEjBD,EAAE1E,UAAkB,OAAN2E,EAAa3G,OAAOqH,OAAOV,IAAMQ,EAAGnF,UAAY2E,EAAE3E,UAAW,IAAImF,EACnF,GAEJnH,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQoH,eAAiBpH,EAAQqH,aAAU,EAI3C,IAAIA,EAAyB,WACzB,SAASA,IACL,IAAIhF,EAAQT,KACZA,KAAK0F,SAAU,EACf1F,KAAK2F,SAAW,IAAIC,SAAQ,SAAUC,EAAGlD,GACrClC,EAAMqF,iBAAmBD,CAC7B,GACJ,CAsBA,OArBA3H,OAAOC,eAAesH,EAAQvF,UAAW,SAAU,CAE/C6F,IAAK,WACD,OAAO/F,KAAK0F,OAChB,EACAM,YAAY,EACZC,cAAc,IAGlBR,EAAQvF,UAAUgG,MAAQ,WACtBlG,KAAK0F,SAAU,CACnB,EAEAD,EAAQvF,UAAUiG,KAAO,WACrBnG,KAAK0F,SAAU,EACf1F,KAAK8F,kBAAiB,EAC1B,EAEAL,EAAQvF,UAAUkG,KAAO,WACrB,OAAOpG,KAAK2F,QAChB,EACOF,CACX,CA9B4B,GA+B5BrH,EAAQqH,QAAUA,EAIlB,IAAID,EAAgC,SAAUa,GAE1C,SAASb,EAAec,GACpB,IAAI7F,EAAQ4F,EAAOxD,KAAK7C,OAASA,KAEjC,OADAS,EAAM8F,SAAWxE,YAAW,WAAc,OAAOtB,EAAM0F,MAAQ,GAAGG,GAC3D7F,CACX,CAKA,OAVAkE,EAAUa,EAAgBa,GAM1Bb,EAAetF,UAAUiG,KAAO,WAC5BK,aAAaxG,KAAKuG,UAClBF,EAAOnG,UAAUiG,KAAKtD,KAAK7C,KAC/B,EACOwF,CACX,CAZmC,CAYjCC,GACFrH,EAAQoH,eAAiBA,C,eCpEzBtH,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQqI,qBAAkB,EAI1B,IAAIA,EAAiC,WACjC,SAASA,IACLzG,KAAK0G,WAAY,EACjB1G,KAAK2G,cAAgB,EACzB,CA+CA,OA1CAF,EAAgBvG,UAAUkG,KAAO,WAC7B,IAAI3F,EAAQT,KACZ,OAAO,IAAI4F,SAAQ,SAAUgB,EAASC,GAClC,GAAIpG,EAAMiG,UAIN,OAFAE,EAAQnG,EAAMqG,qBACdrG,EAAMiG,WAAY,GAGtBjG,EAAMkG,cAAc1D,KAAK,CAAE2D,QAASA,EAASC,OAAQA,GACzD,GACJ,EAKAJ,EAAgBvG,UAAU0G,QAAU,SAAUvI,GAER,IAA9B2B,KAAK2G,cAAcrD,QAAgBtD,KAAK0G,WACxCK,QAAQC,KAAK,kDAEjBhH,KAAK0G,WAAY,EACjB1G,KAAK8G,eAAiBzI,EAEtB,IADA,IAAI4I,EAAgBjH,KAAK2G,cAAcrD,OAChCtD,KAAK2G,cAAcrD,OAAS,IAE/BsD,EADc5G,KAAK2G,cAAclC,QAAQmC,SACjCvI,GAER4I,IACAjH,KAAK0G,WAAY,EAEzB,EAKAD,EAAgBvG,UAAU2G,OAAS,SAAUK,GACzC,KAAOlH,KAAK2G,cAAcrD,OAAS,IAE/BuD,EADa7G,KAAK2G,cAAclC,QAAQoC,QACjCK,EAEf,EACOT,CACX,CApDoC,GAqDpCrI,EAAQqI,gBAAkBA,C,qBC1D1B,IAAIU,EAAYnH,MAAQA,KAAKmH,UAAa,SAAS9E,GAC/C,IAAI+E,EAAsB,mBAAX5E,QAAyBA,OAAOC,SAAUF,EAAI6E,GAAK/E,EAAE+E,GAAIxE,EAAI,EAC5E,GAAIL,EAAG,OAAOA,EAAEM,KAAKR,GACrB,GAAIA,GAAyB,iBAAbA,EAAEiB,OAAqB,MAAO,CAC1CP,KAAM,WAEF,OADIV,GAAKO,GAAKP,EAAEiB,SAAQjB,OAAI,GACrB,CAAEhE,MAAOgE,GAAKA,EAAEO,KAAMI,MAAOX,EACxC,GAEJ,MAAM,IAAI8C,UAAUiC,EAAI,0BAA4B,kCACxD,EACAlJ,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQiJ,MASR,SAAeC,EAAkBC,EAAYC,QACtB,IAAfD,IAAyBA,EAAa,SAAUE,GAAK,QAASA,CAAG,QAChD,IAAjBD,IAA2BA,EAAe,MAC9C,IAAIE,EAAQ,EACRC,EAAML,EAAiBhE,OACvBsE,EAAO,WACP,GAAIF,GAASC,EACT,OAAO/B,QAAQgB,QAAQY,GAE3B,IAAIK,EAAUP,EAAiBI,KAE/B,OADc9B,QAAQgB,QAAQiB,KACfC,MAAK,SAAUC,GAC1B,OAAIR,EAAWQ,GACJnC,QAAQgB,QAAQmB,GAEpBH,GACX,GACJ,EACA,OAAOA,GACX,EA3BAxJ,EAAQ4J,cAmCR,SAAuBC,EAAaV,EAAYC,GAG5C,QAFmB,IAAfD,IAAyBA,EAAa,SAAUE,GAAK,QAASA,CAAG,QAChD,IAAjBD,IAA2BA,EAAe,MACnB,IAAvBS,EAAY3E,OACZ,OAAOsC,QAAQgB,QAAQY,GAE3B,IAAIU,EAAOD,EAAY3E,OACnB6E,EAAS,WACT,IAAIC,EAAKvE,EACLwE,EAAIC,EACRJ,GAAQ,EACR,IACI,IAAK,IAAIK,EAAgBpB,EAASc,GAAcO,EAAkBD,EAAcxF,QAASyF,EAAgBxF,KAAMwF,EAAkBD,EAAcxF,OAE1G,QAAhCuF,GAAMD,EADOG,EAAgBnK,OACToK,cAA2B,IAAPH,GAAyBA,EAAGzF,KAAKwF,EAElF,CACA,MAAOK,GAASN,EAAM,CAAElF,MAAOwF,EAAS,CACxC,QACI,IACQF,IAAoBA,EAAgBxF,OAASa,EAAK0E,EAAcI,SAAS9E,EAAGhB,KAAK0F,EACzF,CACA,QAAU,GAAIH,EAAK,MAAMA,EAAIlF,KAAO,CACxC,CACJ,EACA,OAAO,IAAI0C,SAAQ,SAAUgB,EAASC,GAClC,IAAI+B,EAAK/E,EACT,IACI,IAAK,IAAIgF,EAAgB1B,EAASc,GAAca,EAAkBD,EAAc9F,QAAS+F,EAAgB9F,KAAM8F,EAAkBD,EAAc9F,OAC7H+F,EAAgBzK,MACtByJ,MAAK,SAAUC,KACbG,GAAQ,GAAKX,EAAWQ,IAC1BI,IACAvB,EAAQmB,IAEM,IAATG,GACLtB,EAAQY,EAEhB,IACKuB,OAAM,SAAUC,KACXd,GAAQ,IACVC,IACAtB,EAAOmC,GAEf,GAER,CACA,MAAOC,GAASL,EAAM,CAAE1F,MAAO+F,EAAS,CACxC,QACI,IACQH,IAAoBA,EAAgB9F,OAASa,EAAKgF,EAAcF,SAAS9E,EAAGhB,KAAKgG,EACzF,CACA,QAAU,GAAID,EAAK,MAAMA,EAAI1F,KAAO,CACxC,CACJ,GACJ,C,iBCvGAhF,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQ8K,aAAU,EAClB,IAAIC,EAAW,EAAQ,MAKnBD,EAAyB,WACzB,SAASA,EAAQE,GACbpJ,KAAKqJ,MAAQ,EACbrJ,KAAKsJ,aAAc,EACnBtJ,KAAKoJ,uBAAyBA,EAC9BpJ,KAAKuJ,oBAAsB,GAC3BvJ,KAAKwJ,gBAAkB,EACvBxJ,KAAKyJ,WAAa,IAAIN,EAASO,OACnC,CA6DA,OA5DAxL,OAAOC,eAAe+K,EAAQhJ,UAAW,YAAa,CAClD6F,IAAK,WACD,OAAO/F,KAAKyJ,WAAWE,KAC3B,EACA3D,YAAY,EACZC,cAAc,IAElB/H,OAAOC,eAAe+K,EAAQhJ,UAAW,OAAQ,CAC7C6F,IAAK,WACD,OAAO/F,KAAKqJ,KAChB,EACArD,YAAY,EACZC,cAAc,IAElBiD,EAAQhJ,UAAU0J,MAAQ,SAAU/B,GAChC,IAAIpH,EAAQT,KACZ,GAAIA,KAAKsJ,YACL,MAAM,IAAIO,MAAM,4BAGpB,OADA7J,KAAKqJ,QACE,IAAIzD,SAAQ,SAAUC,EAAGlD,GAC5BlC,EAAM8I,oBAAoBtG,KAAK,CAAE4E,QAASA,EAAShC,EAAGA,EAAGlD,EAAGA,IAC5DlC,EAAMqJ,SACV,GACJ,EACAZ,EAAQhJ,UAAU4J,QAAU,WAExB,IADA,IAAIrJ,EAAQT,KACLA,KAAKuJ,oBAAoBjG,QAAUtD,KAAKwJ,gBAAkBxJ,KAAKoJ,wBAAwB,CAC1F,IAAIW,EAAe/J,KAAKuJ,oBAAoB9E,QAC5CzE,KAAKwJ,kBACL,IAAIQ,EAAUD,EAAalC,UAC3BmC,EAAQlC,KAAKiC,EAAalE,EAAGkE,EAAapH,GAC1CqH,EAAQlC,MAAK,WAAc,OAAOrH,EAAMwJ,UAAY,IAAG,WAAc,OAAOxJ,EAAMwJ,UAAY,GAClG,CACJ,EACAf,EAAQhJ,UAAU+J,SAAW,WACrBjK,KAAKsJ,cAGTtJ,KAAKwJ,kBACgB,KAAfxJ,KAAKqJ,OACPrJ,KAAKyJ,WAAWS,OAEhBlK,KAAKuJ,oBAAoBjG,OAAS,GAClCtD,KAAK8J,UAEb,EACAZ,EAAQhJ,UAAUiK,MAAQ,WACtB,GAAInK,KAAKsJ,YACL,MAAM,IAAIO,MAAM,4BAEpB7J,KAAKuJ,oBAAoBjG,OAAS,EAClCtD,KAAKqJ,MAAQrJ,KAAKwJ,eACtB,EACAN,EAAQhJ,UAAUkK,QAAU,WACxBpK,KAAKsJ,aAAc,EACnBtJ,KAAKuJ,oBAAoBjG,OAAS,EAClCtD,KAAKqJ,MAAQ,EACbrJ,KAAKyJ,WAAWW,SACpB,EACOlB,CACX,CAtE4B,GAuE5B9K,EAAQ8K,QAAUA,C,qBC9ElB,IAAImB,EAAarK,MAAQA,KAAKqK,WAAc,SAAUC,EAASC,EAAYC,EAAGC,GAE1E,OAAO,IAAKD,IAAMA,EAAI5E,WAAU,SAAUgB,EAASC,GAC/C,SAAS6D,EAAUrM,GAAS,IAAMsM,EAAKF,EAAU1H,KAAK1E,GAAS,CAAE,MAAOsE,GAAKkE,EAAOlE,EAAI,CAAE,CAC1F,SAASiI,EAASvM,GAAS,IAAMsM,EAAKF,EAAiB,MAAEpM,GAAS,CAAE,MAAOsE,GAAKkE,EAAOlE,EAAI,CAAE,CAC7F,SAASgI,EAAK5C,GAJlB,IAAe1J,EAIa0J,EAAO/E,KAAO4D,EAAQmB,EAAO1J,QAJ1CA,EAIyD0J,EAAO1J,MAJhDA,aAAiBmM,EAAInM,EAAQ,IAAImM,GAAE,SAAU5D,GAAWA,EAAQvI,EAAQ,KAIjByJ,KAAK4C,EAAWE,EAAW,CAC7GD,GAAMF,EAAYA,EAAUI,MAAMP,EAASC,GAAc,KAAKxH,OAClE,GACJ,EACI+H,EAAe9K,MAAQA,KAAK8K,aAAgB,SAAUR,EAASS,GAC/D,IAAsGC,EAAGC,EAAGxD,EAAxGyD,EAAI,CAAEC,MAAO,EAAGC,KAAM,WAAa,GAAW,EAAP3D,EAAE,GAAQ,MAAMA,EAAE,GAAI,OAAOA,EAAE,EAAI,EAAG4D,KAAM,GAAIC,IAAK,IAAeC,EAAIrN,OAAOqH,QAA4B,mBAAbiG,SAA0BA,SAAWtN,QAAQgC,WACtL,OAAOqL,EAAExI,KAAO0I,EAAK,GAAIF,EAAS,MAAIE,EAAK,GAAIF,EAAU,OAAIE,EAAK,GAAsB,mBAAXjJ,SAA0B+I,EAAE/I,OAAOC,UAAY,WAAa,OAAOzC,IAAM,GAAIuL,EAC1J,SAASE,EAAKnJ,GAAK,OAAO,SAAUoJ,GAAK,OACzC,SAAcC,GACV,GAAIX,EAAG,MAAM,IAAI7F,UAAU,mCAC3B,KAAOoG,IAAMA,EAAI,EAAGI,EAAG,KAAOT,EAAI,IAAKA,OACnC,GAAIF,EAAI,EAAGC,IAAMxD,EAAY,EAARkE,EAAG,GAASV,EAAU,OAAIU,EAAG,GAAKV,EAAS,SAAOxD,EAAIwD,EAAU,SAAMxD,EAAE5E,KAAKoI,GAAI,GAAKA,EAAElI,SAAW0E,EAAIA,EAAE5E,KAAKoI,EAAGU,EAAG,KAAK3I,KAAM,OAAOyE,EAE3J,OADIwD,EAAI,EAAGxD,IAAGkE,EAAK,CAAS,EAARA,EAAG,GAAQlE,EAAEpJ,QACzBsN,EAAG,IACP,KAAK,EAAG,KAAK,EAAGlE,EAAIkE,EAAI,MACxB,KAAK,EAAc,OAAXT,EAAEC,QAAgB,CAAE9M,MAAOsN,EAAG,GAAI3I,MAAM,GAChD,KAAK,EAAGkI,EAAEC,QAASF,EAAIU,EAAG,GAAIA,EAAK,CAAC,GAAI,SACxC,KAAK,EAAGA,EAAKT,EAAEI,IAAIM,MAAOV,EAAEG,KAAKO,MAAO,SACxC,QACI,MAAkBnE,GAAZA,EAAIyD,EAAEG,MAAY/H,OAAS,GAAKmE,EAAEA,EAAEnE,OAAS,KAAkB,IAAVqI,EAAG,IAAsB,IAAVA,EAAG,IAAW,CAAET,EAAI,EAAG,QAAU,CAC3G,GAAc,IAAVS,EAAG,MAAclE,GAAMkE,EAAG,GAAKlE,EAAE,IAAMkE,EAAG,GAAKlE,EAAE,IAAM,CAAEyD,EAAEC,MAAQQ,EAAG,GAAI,KAAO,CACrF,GAAc,IAAVA,EAAG,IAAYT,EAAEC,MAAQ1D,EAAE,GAAI,CAAEyD,EAAEC,MAAQ1D,EAAE,GAAIA,EAAIkE,EAAI,KAAO,CACpE,GAAIlE,GAAKyD,EAAEC,MAAQ1D,EAAE,GAAI,CAAEyD,EAAEC,MAAQ1D,EAAE,GAAIyD,EAAEI,IAAIrI,KAAK0I,GAAK,KAAO,CAC9DlE,EAAE,IAAIyD,EAAEI,IAAIM,MAChBV,EAAEG,KAAKO,MAAO,SAEtBD,EAAKZ,EAAKlI,KAAKyH,EAASY,EAC5B,CAAE,MAAOvI,GAAKgJ,EAAK,CAAC,EAAGhJ,GAAIsI,EAAI,CAAG,CAAE,QAAUD,EAAIvD,EAAI,CAAG,CACzD,GAAY,EAARkE,EAAG,GAAQ,MAAMA,EAAG,GAAI,MAAO,CAAEtN,MAAOsN,EAAG,GAAKA,EAAG,QAAK,EAAQ3I,MAAM,EAC9E,CAtBgD2H,CAAK,CAACrI,EAAGoJ,GAAK,CAAG,CAuBrE,EACItJ,EAAUpC,MAAQA,KAAKoC,QAAW,SAAUC,EAAGC,GAC/C,IAAIC,EAAsB,mBAAXC,QAAyBH,EAAEG,OAAOC,UACjD,IAAKF,EAAG,OAAOF,EACf,IAAmBK,EAAYC,EAA3BC,EAAIL,EAAEM,KAAKR,GAAOS,EAAK,GAC3B,IACI,WAAc,IAANR,GAAgBA,KAAM,MAAQI,EAAIE,EAAEG,QAAQC,MAAMF,EAAGG,KAAKP,EAAErE,MACxE,CACA,MAAO6E,GAASP,EAAI,CAAEO,MAAOA,EAAS,CACtC,QACI,IACQR,IAAMA,EAAEM,OAAST,EAAIK,EAAU,SAAIL,EAAEM,KAAKD,EAClD,CACA,QAAU,GAAID,EAAG,MAAMA,EAAEO,KAAO,CACpC,CACA,OAAOJ,CACX,EACI+I,EAAiB7L,MAAQA,KAAK6L,eAAkB,SAAUC,EAAIC,EAAMC,GACpE,GAAIA,GAA6B,IAArBC,UAAU3I,OAAc,IAAK,IAA4BR,EAAxBF,EAAI,EAAGsJ,EAAIH,EAAKzI,OAAYV,EAAIsJ,EAAGtJ,KACxEE,GAAQF,KAAKmJ,IACRjJ,IAAIA,EAAKkC,MAAM9E,UAAUsD,MAAMX,KAAKkJ,EAAM,EAAGnJ,IAClDE,EAAGF,GAAKmJ,EAAKnJ,IAGrB,OAAOkJ,EAAGK,OAAOrJ,GAAMkC,MAAM9E,UAAUsD,MAAMX,KAAKkJ,GACtD,EACA7N,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQgO,SAOR,SAAkB9E,EAAkB+E,GAChC,IAAIC,EAAU,GACV5E,EAAQ,EACRC,EAAML,EAAiBhE,OA8C3B,OAAOsC,QAAQgB,QAAQ,MAAMkB,MAzB7B,SAASyE,EAAYxE,GACjB,OAjBJ,WACI,OAAOsC,EAAUrK,UAAM,OAAQ,GAAQ,WAEnC,OAAO8K,EAAY9K,MAAM,SAAU6D,GAC/B,OAAQA,EAAGsH,OACP,KAAK,EACD,OAAKkB,EACE,CAAC,EAAaA,KADW,CAAC,EAAa,GAElD,KAAK,EAED,MAAO,CAAC,EADDxI,EAAGuH,QAEd,KAAK,EAAG,MAAO,CAAC,GAAc,GAEtC,GACJ,GACJ,CAEWoB,GAA0B1E,MAAK,SAAU2E,GAC5C,GAAIA,EAIA,OAHI9E,GAAO2E,EAAQhJ,QACfgJ,EAAQrJ,KAAK4H,MAAMyB,EAAST,EAAc,GAAIzJ,EAAO,IAAI4C,MAAM2C,EAAM2E,EAAQhJ,QAAQoJ,UAAKhM,KAAa,IAEpGkF,QAAQgB,QAAQ0F,GAEvBvE,SACAuE,EAAQrJ,KAAK8E,GAEjB,IAAIzF,EA7BDoF,EAAQC,EAAML,EAAiBI,OAAa,KA8B/C,OAAIpF,EACOA,EAAEwF,KAAKyE,GAAaxD,OAAM,SAAU7B,GAEvC,OAAIuF,GAGJH,EAAQrJ,KAAK4H,MAAMyB,EAAST,EAAc,GAAIzJ,EAAO,IAAI4C,MAAM2C,EAAM2E,EAAQhJ,QAAQoJ,UAAKhM,KAAa,IAF5FkF,QAAQgB,QAAQ0F,EAI/B,IAEG1G,QAAQgB,QAAQ0F,EAC3B,GACJ,GAEJ,C,eCvHApO,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQuO,aAAU,EAClB,IAAIC,EAAyB,WACzB,SAASA,IACL5M,KAAK6M,UAAY,CAAC,CACtB,CAiEA,OA5DAD,EAAQ1M,UAAU4M,MAAQ,SAAUC,GAChC,IAAIC,EAAUC,EACdjN,KAAK6M,UAAUE,GAAO,CAClBG,UAAWC,KAAKC,MAChBpD,QAAS,IAAIpE,SAAQ,SAAUgB,EAASC,GACpCmG,EAAWpG,EACXqG,EAAUpG,CACd,IACAwG,OAAO,GAEXrN,KAAK6M,UAAUE,GAAKnG,QAAUoG,EAC9BhN,KAAK6M,UAAUE,GAAKlG,OAASoG,CACjC,EAKAL,EAAQ1M,UAAUoN,IAAM,SAAUP,GAC9B,IAAIQ,EAAWvN,KAAK6M,UAAUE,GAC1BQ,GACAA,EAASC,QAAUL,KAAKC,MACxBG,EAASF,OAAQ,GAGjBtG,QAAQ7D,MAAM,WAAWiJ,OAAOY,EAAK,qBAE7C,EAKAH,EAAQ1M,UAAUkG,KAAO,SAAU2G,EAAKU,GACpC,IAAIF,EAAWvN,KAAK6M,UAAUE,GAC9B,GAAIQ,EAAU,CACV,IAAIG,EAAYH,EAAS3G,QAAS+G,EAAWJ,EAAS1G,OAuBtD,OAtBI0G,EAASF,MACLK,GACAA,EAAUH,EAASF,OAIvBE,EAASK,SAAWC,aAAY,WACxBN,EAASF,OACTS,cAAcP,EAASK,UACvBF,EAAUX,IAINU,IAAWM,MAAMN,EAAOO,UACpBb,KAAKC,MAAQG,EAASL,UAAYO,EAAOO,UACzCF,cAAcP,EAASK,UACvBD,EAAS,YAAYxB,OAAOY,EAAK,iBAIjD,GAAG,IAEAQ,EAASvD,OACpB,CACJ,EACO4C,CACX,CArE4B,GAsE5BxO,EAAQuO,QAAU,IAAIC,C,iBCxEtB1O,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQ6P,UAAY7P,EAAQ8P,eAAY,EACxC,IAMIA,EANApO,EAAc,EAAQ,MACtBqO,EAAU,EAAQ,KAClBC,EAAY,EAAQ,OAKxB,SAAWF,GAEPA,EAAUA,EAAiB,MAAI,GAAK,QAEpCA,EAAUA,EAAkB,OAAI,GAAK,QACxC,CALD,CAKGA,IAAc9P,EAAQ8P,UAAYA,EAAY,CAAC,IAIlD,IAAIG,EAA2B,WAC3B,SAASA,IACT,CAwEA,OAnEAA,EAAUnO,UAAUC,KAAO,SAAUsN,GACjC,IAAIhN,EAAQT,KACZ,GAAKyN,EAAL,CAMA,IAAIa,EAAMb,EAAOa,IAAKC,EAASd,EAAOc,OAAQC,EAAOf,EAAOe,KAAM7N,EAAa8M,EAAO9M,WACtF,GAAK2N,EAAL,EAGI,EAAIH,EAAQM,aAAaD,KACzBA,EAAON,EAAUQ,QAErB,IAAIC,EAAcP,EAAUQ,QAAQC,QAAQ,eAAe,GACvDC,EAAYV,EAAUQ,QAAQC,QAAQ,aAAa,GAEnDL,IAASN,EAAUQ,SAAWC,GAC3BH,IAASN,EAAUa,QAAUD,SAGrBpO,IAAX6N,IACAA,EAAS,GAER5N,EAMDb,EAAYmC,UAAU+M,aAAarO,EAAY2N,EAAK5P,GAAGuQ,WAAW,SAAUjG,EAAKkG,GAC7EzO,EAAMuB,MAAMwM,EAAMD,EAAQvF,EAAKkG,EACnC,IAPApP,EAAYmC,UAAUkN,KAAKb,EAAK5P,GAAGuQ,WAAW,SAAUjG,EAAKkG,GACzDzO,EAAMuB,MAAMwM,EAAMD,EAAQvF,EAAKkG,EACnC,IAjBJ,CAJA,MAJQE,UACArI,QAAQ7D,MAAM,kBA+B1B,EACAmL,EAAUnO,UAAU8B,MAAQ,SAAUwM,EAAMD,EAAQvF,EAAKkG,GACjDlG,IAGAwF,GAAQN,EAAUQ,OAClBhQ,GAAG2Q,YAAYlP,KAAK+O,GAAM,EAAOX,GAGjC7P,GAAG2Q,YAAYC,UAAUJ,GAAM,GAEvC,EAEAb,EAAUnO,UAAUqP,KAAO,SAAU9B,GACvBA,EAAOa,IAAcb,EAAOc,OAAtC,IAA8CC,EAAOf,EAAOe,MACxD,EAAIL,EAAQM,aAAaD,KACzBA,EAAON,EAAUQ,QAErB,IAAIC,EAAcP,EAAUQ,QAAQC,QAAQ,eAAe,GACvDC,EAAYV,EAAUQ,QAAQC,QAAQ,aAAa,GAEnDL,IAASN,EAAUQ,SAAWC,GAC3BH,IAASN,EAAUa,QAAUD,IAGhCN,GAAQN,EAAUQ,OAClBhQ,GAAG2Q,YAAYG,iBAGf9Q,GAAG2Q,YAAYI,YAEvB,EACOpB,CACX,CA3E8B,GA4E9BjQ,EAAQ6P,UAAY,IAAII,C,iBC9FxBnQ,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQsR,iBAAc,EACtB,IAAI5P,EAAc,EAAQ,MACtB6P,EAA6B,WAC7B,SAASA,IACL3P,KAAK4P,QAAU,CAAC,CACpB,CAqEA,OA5DAD,EAAYzP,UAAU2P,kCAAoC,SAAUpC,GAChE,IAAIhN,EAAQT,KACZ,GAAKyN,EAAL,CAGA,IAAIqC,EAAYrC,EAAOqC,UAAWnP,EAAa8M,EAAO9M,WAAYoP,EAAQtC,EAAOsC,MAAOC,EAAkBvC,EAAOuC,gBAAiBC,EAASxC,EAAOwC,OAClJ,KAAIF,GAAS,IAGRE,EAGL,OAAO,IAAIrK,SAAQ,SAAUC,EAAGlD,GAC5B,IAAIuN,EAAa,SAAUlH,EAAKmH,GAC5B,GAAInH,EACArG,EAAEqG,OADN,CAIA,IAAIoH,EAAS3P,EAAMmP,QACfS,EAAaD,EAAON,GACnBO,IACDD,EAAON,GAAa,GACpBO,EAAaD,EAAON,IAExB,IAAIQ,EAAWD,EAAW/M,OAE1B,GAAIgN,EAAWP,EACX,IAAK,IAAIhM,EAAMgM,EAAOhM,EAAMuM,EAAUvM,KAC9BwM,EAAYF,EAAWtM,IACjBxF,KAAKiS,QAAS,EAKhC,IADA,IAAIC,EAAQ,GACHrM,EAAM,EAAGA,EAAM2L,EAAO3L,IAAO,CAClC,IAAImM,EACJ,GADIA,EAAYF,EAAWjM,GAQvBmM,EAAUhS,KAAKiS,QAAS,EACpBD,EAAUhS,KAAK0R,SAAWA,IAC1BM,EAAUhS,KAAK0R,OAASA,GAE5BQ,EAAMrM,GAAOmM,MAXD,CACZ,IAAIhS,EAAOG,GAAGgS,YAAYP,GAC1BE,EAAWjM,GAAO7F,EAAKE,aAAauR,GACpCC,EAAO7O,SAAS7C,GAChBkS,EAAMrM,GAAOiM,EAAWjM,EAC5B,CAQJ,CACAyB,EAAE4K,EAjCF,CAkCJ,EACI9P,EACAb,EAAYmC,UAAU+M,aAAarO,EAAYmP,EAAWpR,GAAGiS,OAAQT,GAGrEpQ,EAAYmC,UAAUkN,KAAKW,EAAWpR,GAAGiS,OAAQT,EAEzD,GAtDA,CAuDJ,EACOP,CACX,CAzEgC,GA0EhCvR,EAAQsR,YAAc,IAAIC,C,eC7E1BzR,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQwS,qBAAkB,EAI1B,IAAIC,EAAiC,WACjC,SAASA,IACLA,EAAgBC,iBAAmB9Q,IACvC,CA8DA,OAzDA6Q,EAAgB3Q,UAAU6Q,0BAA4B,WAClD,IAAKF,EAAgBG,6BAA8B,CAC/CH,EAAgBG,8BAA+B,EAC/C,IAAIC,EAAmCvS,GAAGwS,SAAyB,eAAEC,WACrEzS,GAAGwS,SAAyB,eAAEC,WAAa,SAAUC,GACjDP,EAAgBQ,oBAAoBC,IAAIF,EAAK9L,YAAa8L,GAC1DH,EAAiCpG,MAAM7K,KAAM,CAACoR,IAC9C,IAAIG,EAAWV,EAAgBW,UAAUzL,IAAIqL,EAAK9L,aAClD,GAAIiM,EAAU,CACV,IAAK,IAAIxN,EAAM,EAAGA,EAAMwN,EAASjO,OAAQS,KAErC6C,EADc2K,EAASxN,IACfqN,GAEZG,EAASjO,OAAS,CACtB,CACJ,CACJ,CACJ,EAMAuN,EAAgB3Q,UAAUuR,MAAQ,SAAUC,GACxC,IAAIN,EAAOP,EAAgBQ,oBAAoBtL,IAAI2L,GACnD,GAAIN,EACA,OAAOA,CAGf,EAMAP,EAAgB3Q,UAAUyR,WAAa,SAAUD,GAC7C,OAAO,IAAI9L,SAAQ,SAAUgB,EAASjE,GAClC,IAAIyO,EAAOP,EAAgBC,iBAAiBW,MAAMC,GAClD,GAAIN,EACAxK,EAAQwK,OADZ,CAKI,IAAIQ,EAAWf,EAAgBW,UAAUzL,IAAI2L,GACxCE,EAKDA,EAAS3O,KAAK2D,IAJdgL,EAAW,CAAChL,GACZiK,EAAgBW,UAAUF,IAAII,EAAWE,IAK7Cf,EAAgBC,iBAAiBC,2BACrC,CACJ,GACJ,EACAF,EAAgBQ,oBAAsB,IAAIQ,IAC1ChB,EAAgBW,UAAY,IAAIK,IACzBhB,CACX,CAlEoC,GAmEpCzS,EAAQwS,gBAAkB,IAAIC,C,qBCxE9B,IACQnM,EADJC,EAAa3E,MAAQA,KAAK2E,YACtBD,EAAgB,SAAUE,EAAGC,GAI7B,OAHAH,EAAgBxG,OAAO4G,gBAClB,CAAEC,UAAW,cAAgBC,OAAS,SAAUJ,EAAGC,GAAKD,EAAEG,UAAYF,CAAG,GAC1E,SAAUD,EAAGC,GAAK,IAAK,IAAII,KAAKJ,EAAO3G,OAAOgC,UAAUgF,eAAerC,KAAKgC,EAAGI,KAAIL,EAAEK,GAAKJ,EAAEI,GAAI,EAC7FP,EAAcE,EAAGC,EAC5B,EACO,SAAUD,EAAGC,GAChB,GAAiB,mBAANA,GAA0B,OAANA,EAC3B,MAAM,IAAIM,UAAU,uBAAyBC,OAAOP,GAAK,iCAE7D,SAASQ,IAAOrF,KAAKsF,YAAcV,CAAG,CADtCF,EAAcE,EAAGC,GAEjBD,EAAE1E,UAAkB,OAAN2E,EAAa3G,OAAOqH,OAAOV,IAAMQ,EAAGnF,UAAY2E,EAAE3E,UAAW,IAAImF,EACnF,GAEAyM,EAAc9R,MAAQA,KAAK8R,YAAe,SAAUC,EAAYC,EAAQjF,EAAKkF,GAC7E,IAA2HrN,EAAvHiB,EAAIoG,UAAU3I,OAAQZ,EAAImD,EAAI,EAAImM,EAAkB,OAATC,EAAgBA,EAAO/T,OAAOgU,yBAAyBF,EAAQjF,GAAOkF,EACrH,GAAuB,iBAAZE,SAAoD,mBAArBA,QAAQC,SAAyB1P,EAAIyP,QAAQC,SAASL,EAAYC,EAAQjF,EAAKkF,QACpH,IAAK,IAAIrP,EAAImP,EAAWzO,OAAS,EAAGV,GAAK,EAAGA,KAASgC,EAAImN,EAAWnP,MAAIF,GAAKmD,EAAI,EAAIjB,EAAElC,GAAKmD,EAAI,EAAIjB,EAAEoN,EAAQjF,EAAKrK,GAAKkC,EAAEoN,EAAQjF,KAASrK,GAChJ,OAAOmD,EAAI,GAAKnD,GAAKxE,OAAOC,eAAe6T,EAAQjF,EAAKrK,GAAIA,CAChE,EACIN,EAAUpC,MAAQA,KAAKoC,QAAW,SAAUC,EAAGC,GAC/C,IAAIC,EAAsB,mBAAXC,QAAyBH,EAAEG,OAAOC,UACjD,IAAKF,EAAG,OAAOF,EACf,IAAmBK,EAAYC,EAA3BC,EAAIL,EAAEM,KAAKR,GAAOS,EAAK,GAC3B,IACI,WAAc,IAANR,GAAgBA,KAAM,MAAQI,EAAIE,EAAEG,QAAQC,MAAMF,EAAGG,KAAKP,EAAErE,MACxE,CACA,MAAO6E,GAASP,EAAI,CAAEO,MAAOA,EAAS,CACtC,QACI,IACQR,IAAMA,EAAEM,OAAST,EAAIK,EAAU,SAAIL,EAAEM,KAAKD,EAClD,CACA,QAAU,GAAID,EAAG,MAAMA,EAAEO,KAAO,CACpC,CACA,OAAOJ,CACX,EACI+I,EAAiB7L,MAAQA,KAAK6L,eAAkB,SAAUC,EAAIC,EAAMC,GACpE,GAAIA,GAA6B,IAArBC,UAAU3I,OAAc,IAAK,IAA4BR,EAAxBF,EAAI,EAAGsJ,EAAIH,EAAKzI,OAAYV,EAAIsJ,EAAGtJ,KACxEE,GAAQF,KAAKmJ,IACRjJ,IAAIA,EAAKkC,MAAM9E,UAAUsD,MAAMX,KAAKkJ,EAAM,EAAGnJ,IAClDE,EAAGF,GAAKmJ,EAAKnJ,IAGrB,OAAOkJ,EAAGK,OAAOrJ,GAAMkC,MAAM9E,UAAUsD,MAAMX,KAAKkJ,GACtD,EACA7N,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtD,IAAIwF,EAAKnF,GAAG2T,WAAYC,EAAUzO,EAAGyO,QACjCC,GADqD1O,EAAG2O,SAC7B,SAAUnM,GAErC,SAASkM,IACL,IAAI9R,EAAQ4F,EAAOwE,MAAM7K,KAAM6L,EAAc,GAAIzJ,EAAO6J,YAAY,KAAWjM,KAE/E,OADAS,EAAMgS,OAAS,CAAC,EACThS,CACX,CAmGA,OAxGAkE,EAAU4N,EAAWlM,GAMrBnI,OAAOC,eAAeoU,EAAUrS,UAAW,QAAS,CAChD6F,IAAK,WACD,OAAO/F,KAAKyS,MAChB,EACAzM,YAAY,EACZC,cAAc,IAQlBsM,EAAUrS,UAAUwS,SAAW,SAAUrF,EAAOsF,EAASC,GACrD,GAAI5S,KAAKyS,SAAWpF,EAAO,CAEvB,IAAIwF,GAAe,EACf7S,KAAKyS,SACLI,EAAe7S,KAAK8S,sBAAsBzF,IAEA,oBAA1CnP,OAAOgC,UAAU6S,SAASlQ,KAAKwK,GAC/BrN,KAAKyS,OAASpF,GAGTrN,KAAKyS,SACNzS,KAAKyS,OAASvU,OAAOqH,OAAO,OAE3BoN,EAGD3S,KAAKyS,OAASpF,EAFdrN,KAAKgT,SAAShT,KAAKyS,OAAQpF,IAK/BwF,IACA7S,KAAKiT,oBAAoBjT,KAAKyS,QAE9BzS,KAAKkT,SAELN,GAAYA,IAEpB,CACJ,EAMAL,EAAUrS,UAAU8S,SAAW,SAAUhB,EAAQmB,GAC7C,IAAK,IAAIpG,KAAOoG,EAAQ,CACpB,IAAI9U,EAAQ8U,EAAOpG,GAEnBiF,EAAOjF,GAAO1O,CAClB,CACJ,EAIAkU,EAAUrS,UAAUkT,mBAAqB,WACzC,EAIAb,EAAUrS,UAAUmT,kBAAoB,WACxC,EAIAd,EAAUrS,UAAUoT,qBAAuB,WAC3C,EAKAf,EAAUrS,UAAU4S,sBAAwB,SAAUS,GAClD,OAAO,CACX,EAMAhB,EAAUrS,UAAU+S,oBAAsB,SAAUM,GACpD,EAMAhB,EAAUrS,UAAUsT,mBAAqB,SAAUC,GACnD,EAIAlB,EAAUrS,UAAUgT,OAAS,WAC7B,EACYpB,EAAW,CACnBQ,GACDC,EAEP,CA1G8B,CA0G5B7T,GAAG6T,YACLnU,EAAA,QAAkBmU,C,eC3JlBrU,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQ4U,SAYR,SAAkBU,GAEd,OAAIA,EAASpQ,OAAS,GAEXoQ,EAASC,KAAI,SAAUC,GAAO,OAAOA,EAAIpQ,OAAS,IAIlDkQ,EAASC,KAAI,SAAUC,GAAO,OAAO5O,MAAM+G,KAAK6H,EAAM,GAErE,EArBAxV,EAAQyV,kBAyBR,SAA2BH,GACvB,OAAOA,EAASC,KAAI,SAAUC,GAAO,OAAO5O,MAAM+G,KAAK6H,EAAM,GACjE,EA1BAxV,EAAQ0V,cA8BR,SAAuBJ,GACnB,OAAOA,EAASC,KAAI,SAAUC,GAAO,OAAOA,EAAIpQ,OAAS,GAC7D,EA/BApF,EAAQ2V,aAmCR,SAAsBL,GAGlB,IAFA,IAAI3L,EAAS,GACTJ,EAAM+L,EAASpQ,OACVS,EAAM,EAAGA,EAAM4D,EAAK5D,IAAO,CAIhC,IAHA,IAAI6P,EAAMF,EAAS3P,GACfiQ,EAASJ,EAAItQ,OACb2Q,EAAS,IAAIjP,MAAMgP,GACdhQ,EAAI,EAAGA,EAAIgQ,EAAQhQ,IACxBiQ,EAAOjQ,GAAK4P,EAAI5P,GAEpB+D,EAAOhE,GAAOkQ,CAClB,CACA,OAAOlM,CACX,EA/CA3J,EAAQ8V,cAmDR,SAAuBR,EAAUS,GAG7B,IAFA,IAAIpM,EAAS,GACTJ,EAAM+L,EAASpQ,OACVc,EAAM,EAAGA,EAAMuD,EAAKvD,IAAO,CAIhC,IAHA,IAAIgQ,EAASV,EAAStP,GAClBiQ,EAAkBF,GAAaC,EAAO9Q,OACtC2Q,EAAS,IAAIjP,MAAMqP,GACdrQ,EAAI,EAAGA,EAAIqQ,EAAiBrQ,IACjCiQ,EAAOjQ,GAAKoQ,EAAOpQ,GAEvB+D,EAAO3D,GAAO6P,CAClB,CACA,OAAOlM,CACX,C,eCrEA7J,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQkW,aAAU,EAClB,IAAIA,EAAyB,WACzB,SAASA,IACT,CAeA,OAVAA,EAAQC,QAAU,SAAUC,GACxB,OAAOA,CACX,EAKAF,EAAQG,QAAU,SAAUD,GACxB,OAAOA,CACX,EACOF,CACX,CAlB4B,GAmB5BlW,EAAQkW,QAAUA,C,uBCrBlB,IACQ5P,EADJC,EAAa3E,MAAQA,KAAK2E,YACtBD,EAAgB,SAAUE,EAAGC,GAI7B,OAHAH,EAAgBxG,OAAO4G,gBAClB,CAAEC,UAAW,cAAgBC,OAAS,SAAUJ,EAAGC,GAAKD,EAAEG,UAAYF,CAAG,GAC1E,SAAUD,EAAGC,GAAK,IAAK,IAAII,KAAKJ,EAAO3G,OAAOgC,UAAUgF,eAAerC,KAAKgC,EAAGI,KAAIL,EAAEK,GAAKJ,EAAEI,GAAI,EAC7FP,EAAcE,EAAGC,EAC5B,EACO,SAAUD,EAAGC,GAChB,GAAiB,mBAANA,GAA0B,OAANA,EAC3B,MAAM,IAAIM,UAAU,uBAAyBC,OAAOP,GAAK,iCAE7D,SAASQ,IAAOrF,KAAKsF,YAAcV,CAAG,CADtCF,EAAcE,EAAGC,GAEjBD,EAAE1E,UAAkB,OAAN2E,EAAa3G,OAAOqH,OAAOV,IAAMQ,EAAGnF,UAAY2E,EAAE3E,UAAW,IAAImF,EACnF,GAEJnH,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQsW,eAAY,EACpB,IACIA,EAA2B,SAAUrO,GAErC,SAASqO,IACL,OAAkB,OAAXrO,GAAmBA,EAAOwE,MAAM7K,KAAMiM,YAAcjM,IAC/D,CA0CA,OA7CA2E,EAAU+P,EAAWrO,GASrBqO,EAAUH,QAAU,SAAUjG,GAC1B,IAAIqG,EAAYC,OAAO7I,KAAKuC,GAAKyE,SAAS,UAC1C,OAAO/S,KAAK6U,KAAKF,EACrB,EAMAD,EAAUD,QAAU,SAAUnG,GAC1B,IAAIwG,EAAa9U,KAAK6U,KAAKvG,GAC3B,OAAOsG,OAAO7I,KAAK+I,EAAY,UAAU/B,SAAS,QACtD,EACA2B,EAAUG,KAAO,SAAUvG,GAOvB,IALA,IAAIyG,EAAWzG,EAAIqE,QAAQ,OAAQ,KAC/BqC,EAAcD,EAASzR,OACvB2R,EAAYjV,KAAKkV,UAAU5R,OAE3B6R,EAAY,GACPpR,EAAM,EAAGA,EAAMiR,EAAajR,IAAO,CAExC,IAAIqR,EAAML,EAAShR,GAEf2D,EAAQ1H,KAAKkV,UAAUG,QAAQD,IACpB,IAAX1N,IAIAyN,GAFQnV,KAAKkV,UAAUD,EAAYvN,EAAQ,GAInD,CAEA,OAAOyN,CACX,EACAT,EAAUQ,UAAY,oEACfR,CACX,CA/C8B,CADd,EAAQ,MAgDZJ,SACZlW,EAAQsW,UAAYA,C,cClEpBxW,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQkX,gBAiBR,SAAyBC,QACT,IAARA,IAAkBA,EAAM,GAG5B,IAFA,IAAIC,EAAcrI,KAAKC,MACnBqI,EAAO,GACF1R,EAAM,EAAGA,GAAOwR,EAAKxR,IAAO,CACjC,IAAI2R,EAAO,IAAIvI,KACfuI,EAAKC,QAAQH,EAAc,MAAsBzR,GACjD0R,EAAKxS,KAAKyS,EAAKE,cAAgB,IAAMF,EAAKG,WAAa,IAAMH,EAAKI,UACtE,CACA,OAAOL,CACX,EA1BArX,EAAQ2X,aA+BR,WACI,IAAIC,EAAQ,IAAI7I,KAChB,MAAO,GAAGhB,OAAO6J,EAAMJ,cAAe,KAAKzJ,OAAO6J,EAAMH,WAAa,EAAG,KAAK1J,OAAO6J,EAAMF,UAC9F,EAjCA1X,EAAQ6X,YAmCR,SAAqBC,EAAOC,GACxB,IAAIC,EAAmB,IAAIjJ,KAAK+I,GAChCE,EAAiBC,SAAS,EAAG,EAAG,EAAG,GACnC,IAAIC,EAAmB,IAAInJ,KAAKgJ,GAChCG,EAAiBD,SAAS,EAAG,EAAG,EAAG,GACnC,IAAIE,EAAW9W,KAAK+W,IAAIJ,EAAiBK,UAAYH,EAAiBG,WAEtE,OADehX,KAAKiX,KAAKH,EAAW,MAExC,C,iBC9CArY,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQuY,cAOR,WAEI,IADA,IAAIC,EAAY,GACPC,EAAK,EAAGA,EAAK5K,UAAU3I,OAAQuT,IACpCD,EAAUC,GAAM5K,UAAU4K,GAE9B,OAAO,SAAU7E,EAAQ8E,EAAaC,GAClC,IAAIC,EAAiBD,EAAW1Y,MAehC,OAdA0Y,EAAW1Y,MAAQ,WAGf,IAFA,IAAIoC,EAAQT,KACRiX,EAAO,GACFJ,EAAK,EAAGA,EAAK5K,UAAU3I,OAAQuT,IACpCI,EAAKJ,GAAM5K,UAAU4K,GAQzB,OANAD,EAAUM,SAAQ,SAAUC,GACxB,IAAI5Y,EAAOkC,EAAM0W,GACb5Y,IACA,EAAI6Y,EAAgB9Y,oBAAoBC,EAEhD,IACOyY,EAAenM,MAAM7K,KAAMiX,EACtC,EACOF,CACX,CACJ,EA7BA,IAAIK,EAAkB,EAAQ,K,eCM9B,SAASC,EAAQC,GACb,OAAO,SAAUtF,GACRA,EAAO9R,UAAyB,gBACjC8R,EAAO9R,UAAyB,cAAIoX,EAC5C,CACJ,CAKA,SAASC,EAAaC,GAClB,OAAO9Y,GAAG+Y,GAAGF,aAAaC,EAC9B,CApBAtZ,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQiZ,QAAUA,EAClBjZ,EAAQmZ,aAAeA,EAmBvBG,OAAgB,QAAIL,EACpBK,OAAqB,aAAIH,C,eCnBzB,SAASnF,EAASuF,GAMd,OAAO,SAAUC,EAAS7K,EAAKgK,GAC3B,IAAIc,EAAQ,KACRC,EAAK,KAST,GARgC,mBAArBf,EAAW1Y,OAClBwZ,EAAQ,QACRC,EAAKf,EAAW1Y,OAEe,mBAAnB0Y,EAAWhR,MACvB8R,EAAQ,MACRC,EAAKf,EAAWhR,MAEf+R,IAAOD,EACR,MAAM,IAAIhO,MAAM,iBAEpBkN,EAAWc,GAASF,EAAUG,EAAI/K,EACtC,CACJ,CAzBA7O,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQgU,SAAWA,EACnBhU,EAAQ2Z,SAiCR,SAAkBC,EAAO5C,GAErB,YADc,IAAV4C,IAAoBA,EAAQ,KACzB5F,GAAS,SAAU0F,EAAI/K,GAC1B,IAAIkL,EAAW,aAAa9L,OAAOY,GAC/BmL,EAAW,CAAC,EAEhB,OADAA,EAASD,IAAa,IACf,WAEH,IADA,IAAIhB,EAAO,GACFJ,EAAK,EAAGA,EAAK5K,UAAU3I,OAAQuT,IACpCI,EAAKJ,GAAM5K,UAAU4K,GAEzB,IAAIsB,EAAUhL,KAAKC,MAEnB,GAAI+K,EADWD,EAASD,IACED,EAEtB,OADAE,EAASD,GAAYE,EACdL,EAAGjN,MAAM7K,KAAMiX,GAGH,iBAAR7B,GAAmC,iBAARA,GAClCrO,QAAQC,KAAKoO,EAGzB,CACJ,GACJ,C,qBC3DA,IAAI/K,EAAarK,MAAQA,KAAKqK,WAAc,SAAUC,EAASC,EAAYC,EAAGC,GAE1E,OAAO,IAAKD,IAAMA,EAAI5E,WAAU,SAAUgB,EAASC,GAC/C,SAAS6D,EAAUrM,GAAS,IAAMsM,EAAKF,EAAU1H,KAAK1E,GAAS,CAAE,MAAOsE,GAAKkE,EAAOlE,EAAI,CAAE,CAC1F,SAASiI,EAASvM,GAAS,IAAMsM,EAAKF,EAAiB,MAAEpM,GAAS,CAAE,MAAOsE,GAAKkE,EAAOlE,EAAI,CAAE,CAC7F,SAASgI,EAAK5C,GAJlB,IAAe1J,EAIa0J,EAAO/E,KAAO4D,EAAQmB,EAAO1J,QAJ1CA,EAIyD0J,EAAO1J,MAJhDA,aAAiBmM,EAAInM,EAAQ,IAAImM,GAAE,SAAU5D,GAAWA,EAAQvI,EAAQ,KAIjByJ,KAAK4C,EAAWE,EAAW,CAC7GD,GAAMF,EAAYA,EAAUI,MAAMP,EAASC,GAAc,KAAKxH,OAClE,GACJ,EACI+H,EAAe9K,MAAQA,KAAK8K,aAAgB,SAAUR,EAASS,GAC/D,IAAsGC,EAAGC,EAAGxD,EAAxGyD,EAAI,CAAEC,MAAO,EAAGC,KAAM,WAAa,GAAW,EAAP3D,EAAE,GAAQ,MAAMA,EAAE,GAAI,OAAOA,EAAE,EAAI,EAAG4D,KAAM,GAAIC,IAAK,IAAeC,EAAIrN,OAAOqH,QAA4B,mBAAbiG,SAA0BA,SAAWtN,QAAQgC,WACtL,OAAOqL,EAAExI,KAAO0I,EAAK,GAAIF,EAAS,MAAIE,EAAK,GAAIF,EAAU,OAAIE,EAAK,GAAsB,mBAAXjJ,SAA0B+I,EAAE/I,OAAOC,UAAY,WAAa,OAAOzC,IAAM,GAAIuL,EAC1J,SAASE,EAAKnJ,GAAK,OAAO,SAAUoJ,GAAK,OACzC,SAAcC,GACV,GAAIX,EAAG,MAAM,IAAI7F,UAAU,mCAC3B,KAAOoG,IAAMA,EAAI,EAAGI,EAAG,KAAOT,EAAI,IAAKA,OACnC,GAAIF,EAAI,EAAGC,IAAMxD,EAAY,EAARkE,EAAG,GAASV,EAAU,OAAIU,EAAG,GAAKV,EAAS,SAAOxD,EAAIwD,EAAU,SAAMxD,EAAE5E,KAAKoI,GAAI,GAAKA,EAAElI,SAAW0E,EAAIA,EAAE5E,KAAKoI,EAAGU,EAAG,KAAK3I,KAAM,OAAOyE,EAE3J,OADIwD,EAAI,EAAGxD,IAAGkE,EAAK,CAAS,EAARA,EAAG,GAAQlE,EAAEpJ,QACzBsN,EAAG,IACP,KAAK,EAAG,KAAK,EAAGlE,EAAIkE,EAAI,MACxB,KAAK,EAAc,OAAXT,EAAEC,QAAgB,CAAE9M,MAAOsN,EAAG,GAAI3I,MAAM,GAChD,KAAK,EAAGkI,EAAEC,QAASF,EAAIU,EAAG,GAAIA,EAAK,CAAC,GAAI,SACxC,KAAK,EAAGA,EAAKT,EAAEI,IAAIM,MAAOV,EAAEG,KAAKO,MAAO,SACxC,QACI,MAAkBnE,GAAZA,EAAIyD,EAAEG,MAAY/H,OAAS,GAAKmE,EAAEA,EAAEnE,OAAS,KAAkB,IAAVqI,EAAG,IAAsB,IAAVA,EAAG,IAAW,CAAET,EAAI,EAAG,QAAU,CAC3G,GAAc,IAAVS,EAAG,MAAclE,GAAMkE,EAAG,GAAKlE,EAAE,IAAMkE,EAAG,GAAKlE,EAAE,IAAM,CAAEyD,EAAEC,MAAQQ,EAAG,GAAI,KAAO,CACrF,GAAc,IAAVA,EAAG,IAAYT,EAAEC,MAAQ1D,EAAE,GAAI,CAAEyD,EAAEC,MAAQ1D,EAAE,GAAIA,EAAIkE,EAAI,KAAO,CACpE,GAAIlE,GAAKyD,EAAEC,MAAQ1D,EAAE,GAAI,CAAEyD,EAAEC,MAAQ1D,EAAE,GAAIyD,EAAEI,IAAIrI,KAAK0I,GAAK,KAAO,CAC9DlE,EAAE,IAAIyD,EAAEI,IAAIM,MAChBV,EAAEG,KAAKO,MAAO,SAEtBD,EAAKZ,EAAKlI,KAAKyH,EAASY,EAC5B,CAAE,MAAOvI,GAAKgJ,EAAK,CAAC,EAAGhJ,GAAIsI,EAAI,CAAG,CAAE,QAAUD,EAAIvD,EAAI,CAAG,CACzD,GAAY,EAARkE,EAAG,GAAQ,MAAMA,EAAG,GAAI,MAAO,CAAEtN,MAAOsN,EAAG,GAAKA,EAAG,QAAK,EAAQ3I,MAAM,EAC9E,CAtBgD2H,CAAK,CAACrI,EAAGoJ,GAAK,CAAG,CAuBrE,EACAxN,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQga,QAMR,SAAiBpG,EAAQ8E,EAAaC,GAClC,IAAIC,EAAiBD,EAAW1Y,MAwBhC,OAvBA0Y,EAAW1Y,MAAQ,WAEf,IADA,IAAI4Y,EAAO,GACFJ,EAAK,EAAGA,EAAK5K,UAAU3I,OAAQuT,IACpCI,EAAKJ,GAAM5K,UAAU4K,GAEzB,OAAOxM,EAAUrK,UAAM,OAAQ,GAAQ,WACnC,IAAI8M,EAAO/E,EAAQI,EACnB,OAAO2C,EAAY9K,MAAM,SAAU6D,GAC/B,OAAQA,EAAGsH,OACP,KAAK,EACD,OAAKiE,UACLtC,EAAQuL,YAAYjL,MACb,CAAC,EAAa4J,EAAenM,MAAM7K,KAAMiX,KAF1B,CAAC,EAAa,GAGxC,KAAK,EAID,OAHAlP,EAASlE,EAAGuH,OACZjD,EAASkQ,YAAYjL,MACrBrG,QAAQuR,IAAI,GAAGnM,OAAO2K,EAAa,qBAAqB3K,OAAOhE,EAAS2E,EAAO,kBACxE,CAAC,EAAc/E,GAC1B,KAAK,EAAG,MAAO,CAAC,EAAciP,EAAenM,MAAM7K,KAAMiX,IAEjE,GACJ,GACJ,EACOF,CACX,C,eCrEA7Y,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQma,QAOR,SAAiBvG,EAAQjF,EAAKgK,GAC1B,IAAIc,EAAQ,KACRC,EAAK,KAYT,GAXgC,mBAArBf,EAAW1Y,OAClBwZ,EAAQ,QAEU,KADlBC,EAAKf,EAAW1Y,OACTiF,QACHyD,QAAQC,KAAK,kEAGc,mBAAnB+P,EAAWhR,MACvB8R,EAAQ,MACRC,EAAKf,EAAWhR,MAEf+R,EACD,MAAM,IAAIjO,MAAM,iBAEpB,IAAI2O,EAAa,YAAYrM,OAAOY,GACpCgK,EAAWc,GAAS,WAEhB,IADA,IAAIZ,EAAO,GACFJ,EAAK,EAAGA,EAAK5K,UAAU3I,OAAQuT,IACpCI,EAAKJ,GAAM5K,UAAU4K,GAUzB,OARK7W,KAAKkF,eAAesT,IACrBta,OAAOC,eAAe6B,KAAMwY,EAAY,CACpCvS,cAAc,EACdD,YAAY,EACZyS,UAAU,EACVpa,MAAOyZ,EAAGjN,MAAM7K,KAAMiX,KAGvBjX,KAAKwY,EAChB,CACJ,C,eCzCAta,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQsa,cACR,WACI,OAAO,SAAU1G,EAAQ8E,EAAaC,GAClC,IAAIC,EAAiBD,EAAW1Y,MAmBhC,OAlBA0Y,EAAW1Y,MAAQ,WAEf,IADA,IAAI4Y,EAAO,GACFJ,EAAK,EAAGA,EAAK5K,UAAU3I,OAAQuT,IACpCI,EAAKJ,GAAM5K,UAAU4K,GAEzB,IAAI8B,EAAUja,GAAGG,KAAKG,iBACtB,GAAI2Z,EAAQzZ,OAASyZ,EAAQ1Z,OAAS,KAAO,KACrC2Z,EAAUla,GAAGma,OAAOC,UAChBC,WAAY,EACpBH,EAAQI,UAAW,OAElB,GAAIL,EAAQzZ,OAASyZ,EAAQ1Z,MAAQ,KAAO,IAAK,CAClD,IAAI2Z,KAAUla,GAAGma,OAAOC,UAChBC,WAAY,EACpBH,EAAQI,UAAW,CACvB,CACA,OAAOhC,EAAenM,MAAM7K,KAAMiX,EACtC,EACOF,CACX,CACJ,C,eCrBA,SAAS3E,EAASuF,GAMd,OAAO,SAAUC,EAAS7K,EAAKgK,GAC3B,IAAIc,EAAQ,KACRC,EAAK,KAST,GARgC,mBAArBf,EAAW1Y,OAClBwZ,EAAQ,QACRC,EAAKf,EAAW1Y,OAEe,mBAAnB0Y,EAAWhR,MACvB8R,EAAQ,MACRC,EAAKf,EAAWhR,MAEf+R,IAAOD,EACR,MAAM,IAAIhO,MAAM,iBAEpBkN,EAAWc,GAASF,EAAUG,EAAI/K,EACtC,CACJ,CA1BA7O,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQgU,SAAWA,EACnBhU,EAAQ6a,SAkCR,SAAkBjB,EAAOkB,GAGrB,YAFc,IAAVlB,IAAoBA,EAAQ,UACd,IAAdkB,IAAwBA,GAAY,GACjC9G,GAAS,SAAU0F,EAAI/K,GAC1B,IAAIoM,EAAUhM,KAAKC,MACfgM,EAAQ,KACZ,OAAO,WAEH,IADA,IAAInC,EAAO,GACFJ,EAAK,EAAGA,EAAK5K,UAAU3I,OAAQuT,IACpCI,EAAKJ,GAAM5K,UAAU4K,GAEzB,IAAIwC,EAAUrZ,KACd,GAAIkZ,EAAW,CACX,IAAII,EAAUnM,KAAKC,MACfkM,EAAUH,GAAWnB,IACrBF,EAAGjN,MAAMwO,EAASpC,GAClBkC,EAAUG,EAElB,MAEI9S,aAAa4S,GACbA,EAAQrX,YAAW,WACf+V,EAAGjN,MAAMwO,EAASpC,EACtB,GAAGe,EAEX,CACJ,GACJ,EA5DA5Z,EAAQmb,aAuER,SAAsBC,EAAMxB,EAAOkB,QACjB,IAAVlB,IAAoBA,EAAQ,UACd,IAAdkB,IAAwBA,GAAY,GACxC,IAAIC,EAAUhM,KAAKC,MACfgM,EAAQ,KACZ,OAAO,WAEH,IADA,IAAInC,EAAO,GACFJ,EAAK,EAAGA,EAAK5K,UAAU3I,OAAQuT,IACpCI,EAAKJ,GAAM5K,UAAU4K,GAEzB,IAAIwC,EAAUrZ,KACd,GAAIkZ,EAAW,CACX,IAAII,EAAUnM,KAAKC,MACfkM,EAAUH,GAAWnB,IACrBwB,EAAK3O,MAAMwO,EAASpC,GACpBkC,EAAUG,EAElB,MAEI9S,aAAa4S,GACbA,EAAQrX,YAAW,WACfyX,EAAK3O,MAAMwO,EAASpC,EACxB,GAAGe,EAEX,CACJ,C,iBCnGA9Z,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQqb,aAAerb,EAAQsb,0BAA4Btb,EAAQub,mBAAqBvb,EAAQwb,cAAgBxb,EAAQyb,UAAYzb,EAAQ0b,iCAA8B,EAC1K1b,EAAQ2b,MAuCR,SAAeC,EAAY/H,GAEvB,YADa,IAATA,IAAmBA,EAAO,IACvB,SAAUD,EAAQiI,EAAYlD,GAC5B3Y,EAAQqb,aAAaS,IAAIlI,IAC1B5T,EAAQqb,aAAanI,IAAIU,EAAQ,CAAC,GAEtC,IAAImI,EAAoB/b,EAAQqb,aAAa1T,IAAIiM,GAC5CmI,EAAkBF,KACnBE,EAAkBF,GAAc,CAAC,QAEIvZ,IAArCqW,EAA+B,qBAC/BA,EAA+B,mBAAIA,EAAW1Y,OAElD,IAAI+b,EAAmBD,EAAkBF,GACzCG,EAAiBC,aAAc,EAC/BD,EAAiBzH,SAAU,EAC3ByH,EAAiBH,WAAaA,EAC9BG,EAAiBE,OAASvD,EAA+B,mBACzD,IAAIwD,EAAYnc,EAAQyb,UAAU9T,IAAIiU,GACtC,IAAKO,EAAW,EACZA,EAAY,IAAIP,GAEE,OAAIO,EAAgB,OAEtCA,EAAUC,WAMV,IAAIC,EAAYF,EAAUG,oBAC1B,GAAID,EAAW,CAEX,IADA,IAAIE,EAAgB,GACX5W,EAAM,EAAGA,EAAM0W,EAAUnX,OAAQS,IAAO,CAC7C,IAAI6W,EAAgBH,EAAU1W,GAE9B,GADuB3F,EAAQyb,UAAUK,IAAIU,GASzCD,EAAc5W,GAAO3F,EAAQyb,UAAU9T,IAAI6U,OARxB,CACnB,IAAIC,EAAe,IAAID,EACvBC,EAAqB,OAAIA,EAAmB,OAC5CA,EAAuB,WACvBF,EAAc5W,GAAO8W,EACrBzc,EAAQyb,UAAUvI,IAAIsJ,EAAeC,EACzC,CAIJ,CACAN,EAAsB,WAAII,CAC9B,CAEAvc,EAAQyb,UAAUvI,IAAI0I,EAAYO,EACtC,CACA,IAAIO,EAAiBvD,aAAayC,GAC9Bc,IAAmB1c,EAAQ0b,4BAA4BgB,KACvD1c,EAAQ0b,4BAA4BgB,GAAkBP,GAM1D,IAAIvD,EAAiBD,EAAW1Y,MAChC+b,EAA6B,WAAIpD,EAMjCD,EAAW1Y,MAAQ,WAIf,IAHA,IACIwF,EADApD,EAAQT,KAERiX,EAAO,GACFJ,EAAK,EAAGA,EAAK5K,UAAU3I,OAAQuT,IACpCI,EAAKJ,GAAM5K,UAAU4K,GAErBG,IAAmBoD,EAA6B,aAQhDA,EAAiBzH,SAAU,EAE3ByH,EAAiBC,aAAc,EAE/BD,EAAiB1E,KAAO,CAAC,EAEzB0E,EAAiBnD,KAAO,GAExBmD,EAAiBW,iBAAcra,GAGnC0Z,EAAiBpI,OAAShS,KAGrBoa,EAAiB9C,YAClB8C,EAAiB9C,UAAYC,aAAavX,KAAKsF,cAGnD,IAAI0V,EAAiBC,EAAkBC,gBAAgBC,mBAAmBL,GACtEM,EAAQJ,aAAuD,EAASA,EAAeK,MAC3F,GAAID,EAAO,CACP,IAAIE,EAAWpd,OAAOqd,KAAKH,GAAO,GAE7BrN,OAAOuN,GAIRF,EAAM5K,QAAS,EAHf4K,EAAQA,EAAMI,GAMbjB,EAAmB,UACpBA,EAAe,IAAIS,EAAeS,GAClClB,EAAkB,OAAIa,EACtBb,EAAmB,SAAI,EACvBA,EAAoB,YAQxB,IAAIE,EAAYF,EAAsB,WACtC,GAAIE,EACA,IAAK,IAAIrW,EAAM,EAAGA,EAAMqW,EAAUnX,OAAQc,IAAO,CAC7C,IAAIsX,EAAWjB,EAAUrW,GACrBuX,EAAoBV,EAAkBC,gBAAgBC,mBAAmBO,EAASE,WAClFD,IACAD,EAAiB,OAAIC,EAAkBN,MAE/C,CAER,CAQA,IAAIQ,EAAuBC,EAAehB,GACtCiB,GAA2B,EAC/B,GAAIF,EACA,IAAK,IAAIG,EAAM,EAAGA,EAAMH,EAAqBvY,OAAQ0Y,IAAO,CACxD,IAAIC,EAA2G,QAA1FpY,EAAKoX,EAAkBC,gBAAgBC,mBAAmBU,EAAqBG,WAA0B,IAAPnY,OAAgB,EAASA,EAAGwX,MACnJ,GAAIY,GAAuD,IAAtC/d,OAAOqd,KAAKU,GAAe3Y,OAAc,CAC1DyY,GAA2B,EAC3B,KACJ,CACJ,CAKJ,IAAIxB,EAAU/J,SAAUuL,IACF3B,EAAiBC,cAG/BD,EAAiBnD,KAAOA,EACxBmD,EAAiB8B,eAAiB,WAC9BlF,EAAenM,MAAMpK,EAAOwW,EAChC,EACAjX,KAAuB,iBAAIoa,EAE3B+B,EAAsB5B,EAAW,aACjCA,EAAU6B,SAAShC,IACfhL,UAAYiN,UACZC,aAAaxB,GAAkBb,GAE/B7K,UACAmN,EAAoBrS,KAAKqQ,EAAUkB,IAGvCU,EAAsB5B,EAAW,aAGjCH,EAAiBzH,SAtBzB,CA0BA,IAAIoI,EAAc/D,EAAenM,MAAM7K,KAAMiX,GACzCuF,EAA8BpC,EAAiBW,YACnD,YAAoCra,IAAhC8b,EACOA,EAEJzB,CANP,CAOJ,CACJ,CACJ,EApOA3c,EAAQqe,IA0PR,WACI,OAAO,SAAUzK,GACb,IAAI0K,EAAgB1K,EACpB,IAAK5T,EAAQyb,UAAUK,IAAIwC,GAAgB,CACvC,IAAIC,EAAe,IAAID,EACvBC,EAAqB,OAAIA,EAAmB,OAC5CA,EAAuB,WACvBve,EAAQyb,UAAUvI,IAAIU,EAAQ2K,EAClC,CACA,IAAI7B,EAAiBvD,aAAamF,GAC9B5B,IAAmB1c,EAAQ0b,4BAA4BgB,KACvD1c,EAAQ0b,4BAA4BgB,GAAkB1c,EAAQyb,UAAU9T,IAAI2W,IAE3Ete,EAAQwb,cAAcM,IAAIwC,IAC3Bte,EAAQwb,cAActI,IAAIU,EAAQ5T,EAAQyb,UAAU9T,IAAI2W,GAEhE,CACJ,EA1QAte,EAAQwe,cAkRR,SAAuBC,GACnB,OAAO,SAAUvX,GACb,GAAKuX,EAAL,CAEA,IAAIC,EAAoBvF,aAAajS,GACjCwX,IACAhB,EAAegB,GAAqBD,EAH9B,CAKd,CACJ,EA1RA,IAAI1T,EAAW,EAAQ,MACnB4T,EAAU,EAAQ,IAClB9B,EAAoB,EAAQ,MAChC,EAAQ,MAIR7c,EAAQ0b,4BAA8B,CAAC,EAEvC1b,EAAQyb,UAAY,IAAIhI,IAExBzT,EAAQwb,cAAgB,IAAI/H,IAE5B,IAAIiK,EAAiB,CAAC,EAClBS,EAAsB,IAAIpT,EAASO,QACvCtL,EAAQub,mBAAqB4C,EAAoB5S,MACjD,IAAI6R,EAA0B,EAwN9B,SAASW,EAAsBa,EAAMxO,GACjC,IACIyO,EADkBF,EAAQG,MAAMC,sBACEpX,IAAIiX,EAAiB,YAC3D,GAAIC,EACA,IAAK,IAAIG,EAAM,EAAGA,EAAMH,EAAgB3Z,OAAQ8Z,IAAO,CACnD,IAAIC,EAAiBJ,EAAgBG,IACjCC,aAAuD,EAASA,EAAe7O,KAC/E6O,EAAe7O,GAAMwO,EAE7B,CAER,CA/NA5e,EAAQsb,0BAHwB,SAAUlL,GACtCgN,EAA0BhN,CAC9B,EAKAkJ,OAAqB,aAAI,CAAC,EAI1BtZ,EAAQqb,aAAe,IAAI5H,IA+P3B6F,OAAc,MAAI,SAAU4F,GACxB,OAAOlf,EAAQyb,UAAU9T,IAAIuX,EACjC,C,iBClSApf,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQmf,mBAAgB,EACxBnf,EAAQof,cA+CR,SAAuBC,EAAYC,EAAUC,EAAQC,EAAQhL,GAEzD,IAAI8C,EAAO,CACPmI,KAAM1Q,KAAKC,OAGX0Q,EAAa1P,EAAUQ,QAAQC,QAAQ,cACvCkP,EAAY3P,EAAUQ,QAAQC,QAAQ,YAAa,KAElDkP,aAA6C,EAASA,EAAUza,QAAU,IAC3EoS,EAAKsI,SAAWD,GAGpB,IAAIE,GAAeH,EACd/P,MAAMkQ,KACPvI,EAAKwI,cAAgB,CACjBC,KAAM1e,KAAKwE,MAAMga,EAAc,KAAKlL,WACpC,EAAGkL,IAINN,IACDA,EAAS,CAAC,GAEVA,IACAjI,EAAOxX,OAAOkgB,OAAOT,EAAQjI,IAG7B+H,IAAeF,EAAcc,SAC7B3I,EAAK4I,KAAOZ,GAEZE,EAoBR,SAA8BlI,EAAMkI,GAChC,OAAO,IAAIhY,SAAQ,SAAUC,EAAGlD,GAC5B,IAAK,IAAIoB,EAAM,EAAGA,EAAM6Z,EAAOta,OAAQS,IAAO,CAC1C,IAAIwa,EAAeX,EAAO7Z,GACtBiW,EAAauE,EAAaC,MAC1B1D,EAAiBvD,aAAayC,GAC9BD,EAAQ0E,EAAiB3E,4BAA4BgB,GACzD,IAAKf,EAAO,CAER,IAAI2E,OAAe,EACdD,EAAiB5E,UAAUK,IAAIF,MAChC0E,EAAe,IAAI1E,GACE,OAAI0E,EAAmB,OAC5CA,EAAuB,WACvBD,EAAiB5E,UAAUvI,IAAI0I,EAAY0E,IAE3C5D,IAAmB2D,EAAiB3E,4BAA4BgB,KAChE2D,EAAiB3E,4BAA4BgB,GAAkB2D,EAAiB5E,UAAU9T,IAAIiU,IAElGD,EAAQ0E,EAAiB3E,4BAA4BgB,GACrD,IAAIE,EAAiBC,EAAkBC,gBAAgBC,mBAAmBL,GACtEM,EAAQJ,aAAuD,EAASA,EAAeK,MACvFD,IACAsD,EAAkB,IAAI1D,EAAeS,GACrCiD,EAAqB,OAAItD,EACzBsD,EAAsB,SAAI,EAC1BA,EAAuB,WAE/B,CACa3E,EAAMvJ,QAEX+N,EAAaH,SACb1I,EAAOxX,OAAOkgB,OAAO1I,EAAM6I,EAAaH,OAAOrE,EAAOrE,IAGlE,CACA7P,GAAE,EACN,GACJ,CAxDQ8Y,CAAqBjJ,EAAMkI,GAAQ9V,MAAK,SAAU4D,GAC1CkH,GACAA,EAAS8C,EAEjB,IAII9C,GACAA,EAAS8C,EAGrB,EA3FA,IAGI6H,EAHAnP,EAAY,EAAQ,MACpBqQ,EAAmB,EAAQ,MAC3BxD,EAAoB,EAAQ,OAEhC,SAAWsC,GAEPA,EAAcA,EAAsB,OAAI,GAAK,SAE7CA,EAAcA,EAAsB,OAAI,GAAK,QAChD,CALD,CAKGA,IAAkBnf,EAAQmf,cAAgBA,EAAgB,CAAC,G,eCZ9Drf,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQwgB,cAGR,SAAuBC,EAASxgB,GAC5B,IAAK,IAAI0O,KAAO8R,EACZ,GAAIA,EAAQ9R,KAAS1O,EACjB,OAAO0O,CAInB,EATA3O,EAAQ0gB,cAWR,SAAuBzgB,EAAOwgB,GAC1B,OAAO3gB,OAAO6gB,OAAOF,GAASG,SAAS3gB,EAC3C,C,qBCfA,IAAI8I,EAAYnH,MAAQA,KAAKmH,UAAa,SAAS9E,GAC/C,IAAI+E,EAAsB,mBAAX5E,QAAyBA,OAAOC,SAAUF,EAAI6E,GAAK/E,EAAE+E,GAAIxE,EAAI,EAC5E,GAAIL,EAAG,OAAOA,EAAEM,KAAKR,GACrB,GAAIA,GAAyB,iBAAbA,EAAEiB,OAAqB,MAAO,CAC1CP,KAAM,WAEF,OADIV,GAAKO,GAAKP,EAAEiB,SAAQjB,OAAI,GACrB,CAAEhE,MAAOgE,GAAKA,EAAEO,KAAMI,MAAOX,EACxC,GAEJ,MAAM,IAAI8C,UAAUiC,EAAI,0BAA4B,kCACxD,EACAlJ,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQ6gB,MAOR,SAASA,EAAMC,EAAMC,EAAMC,GACvB,IAAIhX,EAAKvE,EAAI+E,EAAKP,EAGlB,QAFgB,IAAZ+W,IAAsBA,EAAU,IAEhCF,IAASC,EACT,OAAO,EAEX,GAAoB,iBAATD,GAA8B,OAATA,GACZ,iBAATC,GAA8B,OAATA,EAC5B,OAAO,EAEX,IAEI,IAAK,IAAIE,EAAYlY,EAASiY,GAAUE,EAAcD,EAAUtc,QAASuc,EAAYtc,KAAMsc,EAAcD,EAAUtc,OAAQ,CACvH,IAAIwc,EAAQD,EAAYjhB,MACxB,GAAIkhB,EAAM,KAAOL,GAAQK,EAAM,KAAOJ,EAClC,OAAO,CACf,CACJ,CACA,MAAOzW,GAASN,EAAM,CAAElF,MAAOwF,EAAS,CACxC,QACI,IACQ4W,IAAgBA,EAAYtc,OAASa,EAAKwb,EAAU1W,SAAS9E,EAAGhB,KAAKwc,EAC7E,CACA,QAAU,GAAIjX,EAAK,MAAMA,EAAIlF,KAAO,CACxC,CAGA,GAFAkc,EAAQnc,KAAK,CAACic,EAAMC,IAEhBD,EAAK5Z,cAAgB6Z,EAAK7Z,YAC1B,OAAO,EAEX,GAAI4Z,EAAK5Z,cAAgBka,QAAUN,EAAK5Z,cAAgBF,QAAU8Z,EAAK5Z,cAAgBma,QACnF,OAAOP,EAAKQ,YAAcP,EAAKO,UAGnC,GAAI1a,MAAM2a,QAAQT,IAASla,MAAM2a,QAAQR,GAAO,CAC5C,GAAID,EAAK5b,SAAW6b,EAAK7b,OACrB,OAAO,EACX,IAAK,IAAIS,EAAM,EAAGA,EAAMmb,EAAK5b,OAAQS,IACjC,IAAKkb,EAAMC,EAAKnb,GAAMob,EAAKpb,GAAMqb,GAC7B,OAAO,EAEf,OAAO,CACX,CAEA,IAAIQ,EAAQ1hB,OAAOqd,KAAK2D,GACpBW,EAAQ3hB,OAAOqd,KAAK4D,GACxB,GAAIS,EAAMtc,SAAWuc,EAAMvc,OACvB,OAAO,EACX,IACI,IAAK,IAAIwc,EAAU3Y,EAASyY,GAAQG,EAAYD,EAAQ/c,QAASgd,EAAU/c,KAAM+c,EAAYD,EAAQ/c,OAAQ,CACzG,IAAIgK,EAAMgT,EAAU1hB,MACpB,IAAK8gB,EAAKja,eAAe6H,GACrB,OAAO,EACX,IAAKkS,EAAMC,EAAKnS,GAAMoS,EAAKpS,GAAMqS,GAC7B,OAAO,CACf,CACJ,CACA,MAAOnW,GAASL,EAAM,CAAE1F,MAAO+F,EAAS,CACxC,QACI,IACQ8W,IAAcA,EAAU/c,OAASqF,EAAKyX,EAAQnX,SAASN,EAAGxF,KAAKid,EACvE,CACA,QAAU,GAAIlX,EAAK,MAAMA,EAAI1F,KAAO,CACxC,CACA,OAAO,CACX,C,eCrFAhF,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQsL,aAAU,EAClB,IAAIA,EAAyB,WACzB,SAASA,EAAQsW,GACbhgB,KAAKigB,SAAWD,CACpB,CAgDA,OA/CA9hB,OAAOC,eAAeuL,EAAQxJ,UAAW,QAAS,CAI9C6F,IAAK,WACD,IACIlC,EADApD,EAAQT,KAeZ,OAbuB,QAAtB6D,EAAK7D,KAAKkgB,cAA2B,IAAPrc,IAAsB7D,KAAKkgB,OAAS,SAAUtN,EAAUuN,GACnF,IAAItc,EAAIwE,EAAIC,EAAI8X,EAAIC,EAAIC,EACpBH,IACAvN,EAAWA,EAAS2N,KAAKJ,IAExB1f,EAAM+f,aACyF,QAA/FnY,EAA+B,QAAzBxE,EAAKpD,EAAMwf,gBAA6B,IAAPpc,OAAgB,EAASA,EAAG4c,8BAA2C,IAAPpY,GAAyBA,EAAGxF,KAAKgB,EAAIpD,GAC7IA,EAAM+f,WAAa,GAC4E,QAA9FJ,EAA+B,QAAzB9X,EAAK7H,EAAMwf,gBAA6B,IAAP3X,OAAgB,EAASA,EAAGoY,6BAA0C,IAAPN,GAAyBA,EAAGvd,KAAKyF,EAAI7H,IAEtD,QAAzF6f,EAA+B,QAAzBD,EAAK5f,EAAMwf,gBAA6B,IAAPI,OAAgB,EAASA,EAAGM,wBAAqC,IAAPL,GAAyBA,EAAGzd,KAAKwd,EAAI5f,GACvIA,EAAM+f,WAAWvd,KAAK,CAAE2P,SAAUA,EAAUuN,SAAUA,GAC1D,GACOngB,KAAKkgB,MAChB,EACAla,YAAY,EACZC,cAAc,IAOlByD,EAAQxJ,UAAUgK,KAAO,SAAUP,GAE/B,IADA,IAAI6W,EAAaxgB,KAAKwgB,WACbzc,EAAM,EAAGA,GAAOyc,aAA+C,EAASA,EAAWld,QAASS,IAAO,CACxG,IAAIF,EAAK2c,EAAWzc,GAAM6O,EAAW/O,EAAG+O,SAAUuN,EAAWtc,EAAGsc,SAC5DvN,GACAA,EAAS/H,MAAMsV,EAAU,CAACxW,GAElC,CACJ,EACAD,EAAQxJ,UAAUkK,QAAU,WACxB,IAAIvG,EAAIwE,EACHrI,KAAK4gB,YACN5gB,KAAK4gB,WAAY,EACjB5gB,KAAKwgB,gBAAa9f,EAC8E,QAA/F2H,EAA8B,QAAxBxE,EAAK7D,KAAKigB,gBAA6B,IAAPpc,OAAgB,EAASA,EAAGgd,+BAA4C,IAAPxY,GAAyBA,EAAGxF,KAAKgB,GAEjJ,EACO6F,CACX,CApD4B,GAqD5BtL,EAAQsL,QAAUA,C,qBCvDlB,IAyBI7F,EAGAid,EA5BA1e,EAAUpC,MAAQA,KAAKoC,QAAW,SAAUC,EAAGC,GAC/C,IAAIC,EAAsB,mBAAXC,QAAyBH,EAAEG,OAAOC,UACjD,IAAKF,EAAG,OAAOF,EACf,IAAmBK,EAAYC,EAA3BC,EAAIL,EAAEM,KAAKR,GAAOS,EAAK,GAC3B,IACI,WAAc,IAANR,GAAgBA,KAAM,MAAQI,EAAIE,EAAEG,QAAQC,MAAMF,EAAGG,KAAKP,EAAErE,MACxE,CACA,MAAO6E,GAASP,EAAI,CAAEO,MAAOA,EAAS,CACtC,QACI,IACQR,IAAMA,EAAEM,OAAST,EAAIK,EAAU,SAAIL,EAAEM,KAAKD,EAClD,CACA,QAAU,GAAID,EAAG,MAAMA,EAAEO,KAAO,CACpC,CACA,OAAOJ,CACX,EACI+I,EAAiB7L,MAAQA,KAAK6L,eAAkB,SAAUC,EAAIC,EAAMC,GACpE,GAAIA,GAA6B,IAArBC,UAAU3I,OAAc,IAAK,IAA4BR,EAAxBF,EAAI,EAAGsJ,EAAIH,EAAKzI,OAAYV,EAAIsJ,EAAGtJ,KACxEE,GAAQF,KAAKmJ,IACRjJ,IAAIA,EAAKkC,MAAM9E,UAAUsD,MAAMX,KAAKkJ,EAAM,EAAGnJ,IAClDE,EAAGF,GAAKmJ,EAAKnJ,IAGrB,OAAOkJ,EAAGK,OAAOrJ,GAAMkC,MAAM9E,UAAUsD,MAAMX,KAAKkJ,GACtD,EAEA7N,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQ2iB,UAAY3iB,EAAQ4iB,uBAAyB5iB,EAAQ6iB,aAAe7iB,EAAQ0iB,mBAAgB,EAEpG,SAAWA,GACPA,EAAcA,EAAuC,wBAAI,GAAK,0BAC9DA,EAAcA,EAAuC,wBAAI,GAAK,0BAC9DA,EAAcA,EAAoC,qBAAI,GAAK,uBAC3DA,EAAcA,EAAiC,kBAAI,GAAK,oBACxDA,EAAcA,EAAkC,mBAAI,GAAK,qBACzDA,EAAcA,EAAkC,mBAAI,GAAK,qBACzDA,EAAcA,EAA6B,cAAI,GAAK,gBACpDA,EAAcA,EAA8B,eAAI,GAAK,iBACrDA,EAAcA,EAA+B,gBAAI,GAAK,kBACtDA,EAAcA,EAA6B,cAAI,GAAK,gBACpDA,EAAcA,EAAgC,iBAAI,IAAM,mBACxDA,EAAcA,EAA+B,gBAAI,IAAM,kBAEvDA,EAAcA,EAAqB,MAAI,IAAM,QAC7CA,EAAcA,EAAoB,KAAI,IAAM,MAC/C,CAhBD,CAgBGA,IAAkB1iB,EAAQ0iB,cAAgBA,EAAgB,CAAC,IAC9D1iB,EAAQ6iB,eAAgBpd,EAAK,CAAC,GACvBid,EAAcI,yBAA2B,0BAC5Crd,EAAGid,EAAcK,yBAA2B,0BAC5Ctd,EAAGid,EAAcM,sBAAwB,uBACzCvd,EAAGid,EAAcO,mBAAqB,oBACtCxd,EAAGid,EAAcQ,oBAAsB,qBACvCzd,EAAGid,EAAcS,oBAAsB,qBACvC1d,EAAGid,EAAcU,eAAiB,gBAClC3d,EAAGid,EAAcW,gBAAkB,iBACnC5d,EAAGid,EAAcY,iBAAmB,kBACpC7d,EAAGid,EAAca,eAAiB,gBAClC9d,EAAGid,EAAcc,kBAAoB,mBACrC/d,EAAGid,EAAce,iBAAmB,kBAEpChe,EAAGid,EAAcgB,OAAS,QAC1Bje,EAAGid,EAAciB,MAAQ,OACzBle,GAEJzF,EAAQ4iB,uBAAyB,qBACjC,IAAIgB,EAA2B,WAC3B,SAASA,IACLhiB,KAAKiiB,eAAiB,EACtBjiB,KAAKkiB,iBAAmB,EACxBliB,KAAKmiB,mBAAqB,KAC1BniB,KAAKoiB,UAAW,EAChBpiB,KAAKqiB,UAAY,EACjBriB,KAAKsiB,sBAAwB,KAC7BtiB,KAAKuiB,GAAK,KACVviB,KAAKwiB,aAAe,GACpBxiB,KAAKwgB,WAAa,EACtB,CAyaA,OAxaAtiB,OAAOC,eAAe6jB,EAAU9hB,UAAW,cAAe,CACtD6F,IAAK,WACD,OAAO/F,KAAKwiB,YAChB,EACAxc,YAAY,EACZC,cAAc,IAElB+b,EAAU9hB,UAAUuiB,MAAQ,SAAUpV,EAAO1D,GACzC3J,KAAKwgB,WAAWtJ,SAAQ,SAAUwL,GAAM,OAAOA,EAAGrV,EAAO1D,EAAQ,GACrE,EACAqY,EAAU9hB,UAAUyiB,iBAAmB,SAAU/P,GACrB,mBAAbA,GACP5S,KAAKwgB,WAAWvd,KAAK2P,EAE7B,EAEAoP,EAAU9hB,UAAU0iB,qBAAuB,SAAUC,EAAUC,GAC3D/b,QAAQuR,IAAI,iBAAmBuK,EAAW,kBAAoBC,GAG9D,IAFA,IAAIC,EAAKF,EAASG,MAAM,KACpBC,EAAKH,EAASE,MAAM,KACfpgB,EAAI,EAAGA,EAAImgB,EAAGzf,SAAUV,EAAG,CAChC,IAAIsgB,EAAIC,SAASJ,EAAGngB,IAChBiC,EAAIse,SAASF,EAAGrgB,IAAM,KAC1B,GAAIsgB,IAAMre,EAIN,OAAOqe,EAAIre,CAEnB,CACA,OAAIoe,EAAG3f,OAASyf,EAAGzf,QACP,EAGD,CAEf,EAEA0e,EAAU9hB,UAAUkjB,gBAAkB,SAAUC,EAAUC,GAItD,OAHAD,EAASE,KAAOD,EAAgBC,KAChCF,EAASG,KAAOF,EAAgBE,KAChCH,EAASI,KAAOH,EAAgBG,KACzBJ,CACX,EAEArB,EAAU9hB,UAAUwjB,UAAY,SAAUC,GACtC,IAAI9f,EAAKzB,EAAOuhB,EAAQX,MAAM,KAAKrP,IAAI6L,QAAS,GAAIoE,EAAQ/f,EAAG,GAAIggB,EAAQhgB,EAAG,GAAIigB,EAAQjgB,EAAG,GAa7F,OAZIigB,EAAQ,EACRA,IAEKD,EAAQ,GACbA,IACAC,EAAQ,IAEHF,EAAQ,IACbA,IACAC,EAAQ,GACRC,EAAQ,IAEL,GAAG3X,OAAOyX,EAAO,KAAKzX,OAAO0X,EAAO,KAAK1X,OAAO2X,EAC3D,EAEA9B,EAAU9hB,UAAU6jB,kBAAoB,WACpC,IAAIC,EAAqBhkB,KAAKwiB,aAALxiB,oBAEzB,GADAA,KAAKsY,IAAI,8BAAuDnM,OAAO6X,IAClEC,IAAIC,UAAUC,YAAYH,GAA/B,CAIA,IAAII,EAAqBH,IAAIC,UAAUG,kBAAkBL,GACzD,GAAKI,EAAL,CAIApkB,KAAKsY,IAAI,wBAAkCnM,OAAOiY,IAClD,IAAIE,EAAsBC,KAAKC,MAAMJ,GAErCpkB,KAAKojB,gBAAgBkB,EAAqBtkB,KAAKsiB,uBAC/CtiB,KAAKsY,IAAI,gCAA6EnM,OAAOoY,KAAKE,UAAUH,KAC/FtkB,KAAK4iB,qBAAqB0B,EAAoBX,QAAS3jB,KAAKsiB,sBAAsBqB,SAClF,IACTW,EAAoBX,QAAU3jB,KAAK0jB,UAAU1jB,KAAKsiB,sBAAsBqB,UAE5E,IACIM,IAAIC,UAAUQ,kBAAkBH,KAAKE,UAAUH,GAAsBN,EACzE,CACA,MAAO9gB,GACHlD,KAAKsY,IAAI,YAA0CnM,OAAO6X,EAAoB,aAAsC7X,OAAOoY,KAAKE,UAAUH,IAC9I,CACA,GAAIlV,SAAU,CACV,IAAIuV,EAAUV,IAAIC,UAAUG,kBAAkBL,GAC9ChkB,KAAKsY,IAAI,wBAAyFnM,OAAOwY,GAC7G,CAnBA,MAFI3kB,KAAKsY,IAAI,4BAA0DnM,OAAO6X,GAH9E,MAFIhkB,KAAKsY,IAAI,0BAAmDnM,OAAO6X,GA2B3E,EACAhC,EAAU9hB,UAAU0kB,WAAa,SAAU7X,EAAK1O,GACvB,iBAAVA,IACPA,EAAQkmB,KAAKE,UAAUpmB,IAE3BK,GAAGS,IAAI0lB,aAAaC,QAAQ/X,EAAK1O,EACrC,EACA2jB,EAAU9hB,UAAU6kB,WAAa,SAAUhY,EAAKvF,QACvB,IAAjBA,IAA2BA,EAAe,MAC9C,IAAInJ,EAAQK,GAAGS,IAAI0lB,aAAahW,QAAQ9B,GACxC,IAAK1O,EACD,OAAOmJ,EAEX,IACI,OAAO+c,KAAKC,MAAMnmB,EACtB,CACA,MAAOwF,GACH,OAAOxF,CACX,CACJ,EAOA2jB,EAAU9hB,UAAU8kB,YAAc,SAAUC,EAAiB3C,GACzD,IAAK5jB,GAAGS,IAAI+lB,SAKR,OAJI9V,UACApP,KAAKsY,IAAI,uBAAqDnM,OAAOzN,GAAGS,IAAI+lB,gBAEhFllB,KAAKyiB,MAAMrkB,EAAQ6iB,aAAaH,EAAciB,MAAO,CAAEoD,UAAWrE,EAAciB,KAAMqD,IAAK,WAG/FplB,KAAKyiB,MAAMrkB,EAAQ6iB,aAAaH,EAAcgB,OAAQ,CAAEqD,UAAWrE,EAAcgB,MAAOsD,IAAK,SAC7FplB,KAAKsiB,sBAAwBA,EAC7BtiB,KAAKwiB,cAAgByB,IAAIC,UAAYD,IAAIC,UAAUmB,kBAAoB,KAAO,sBAC1EjW,WACApP,KAAKsY,IAAI,QAAUtY,KAAKwiB,cACxBxiB,KAAKsY,IAAI,iBAAkBiM,KAAKE,UAAUnC,IAC1CtiB,KAAKsY,IAAI,kBAA2CnM,OAAOoY,KAAKE,UAAUQ,MAG9EjlB,KAAKojB,gBAAgB6B,EAAiB3C,GACtCtiB,KAAKilB,gBAAkBA,EACvBjlB,KAAKmiB,mBAAqBoC,KAAKE,UAAUQ,GACrC7V,UACApP,KAAKsY,IAAI,kBAAmBtY,KAAKmiB,oBAErCniB,KAAKuiB,GAAK,IAAI0B,IAAIqB,cAAc,GAAItlB,KAAKwiB,aAAcxiB,KAAK4iB,sBAG5D5iB,KAAKuiB,GAAGgD,mBAAkB,SAAUC,EAAMrV,GAStC,OAPiBA,EAAMsV,WAELtV,EAAMuV,IAELvV,EAAMqV,KAEdrV,EAAMvR,MAGN,CAMf,IACIwQ,UACApP,KAAKsY,IAAI,6CAEbtY,KAAK2lB,aACT,EAEA3D,EAAU9hB,UAAUylB,YAAc,WAC9B,GAAI3lB,KAAKoiB,SACDhT,UACApP,KAAKsY,IAAI,kBAFjB,CAWA,GALIlJ,UACApP,KAAKsY,IAAI,yBAGbtY,KAAK+jB,oBACD/jB,KAAKuiB,GAAGqD,aAAe3B,IAAIqB,cAAcO,MAAMC,SAAU,CACrD1W,UACApP,KAAKsY,IAAI,+BAEb,IAAI+K,EAAW,IAAIY,IAAI8B,SAAS/lB,KAAKmiB,mBAAoBniB,KAAKwiB,cAC9DxiB,KAAKuiB,GAAGyD,kBAAkB3C,EAAUrjB,KAAKwiB,eACrCmB,EAAU3jB,KAAK+kB,WAAW3mB,EAAQ4iB,2BAElC2C,EAAU3jB,KAAKuiB,GAAG0D,mBAAmBC,aACrClmB,KAAK4kB,WAAWxmB,EAAQ4iB,uBAAwB2C,GAExD,CACA,GAAIvU,SAAU,CACV,IAAI+W,EAAenmB,KAAKuiB,GAAG0D,mBAAmBG,kBAC1CzC,EAAU3jB,KAAKuiB,GAAG0D,mBAAmBC,aACrCG,EAAarmB,KAAKuiB,GAAG0D,mBAAmBK,gBACxCC,EAAcvmB,KAAKuiB,GAAG0D,mBAAmBO,qBACzCC,EAAazmB,KAAKuiB,GAAG0D,mBAAmBS,oBAC5C1mB,KAAKsY,IAAI,sCACTtY,KAAKsY,IAAI,2BAA4B6N,GACrCnmB,KAAKsY,IAAI,qBAAsBqL,GAC/B3jB,KAAKsY,IAAI,wBAAyB+N,GAClCrmB,KAAKsY,IAAI,+BAAgCiO,GACzCvmB,KAAKsY,IAAI,8BAA+BmO,EAC5C,CACA,IAAKzmB,KAAKuiB,GAAG0D,qBAAuBjmB,KAAKuiB,GAAG0D,mBAAmBU,WAK3D,OAJIvX,UACApP,KAAKsY,IAAI,gCAEbtY,KAAKyiB,MAAMrkB,EAAQ6iB,aAAaH,EAAciB,MAAO,CAAEoD,UAAWrE,EAAciB,KAAMqD,IAAK,gBAG3FhW,UACApP,KAAKsY,IAAI,2BAEbtY,KAAKuiB,GAAGqE,iBAAiB5mB,KAAK6mB,QAAQtG,KAAKvgB,OAC3CA,KAAKuiB,GAAGoD,cACR3lB,KAAKoiB,UAAW,CA3ChB,CA4CJ,EAEAJ,EAAU9hB,UAAU2mB,QAAU,SAAUld,GACpC,IAAIlJ,EAAQT,KACRmlB,EAAYxb,EAAMmd,eAClBC,EAAe3oB,EAAQ6iB,aAAakE,GACpC6B,GAAW,EACX5B,EAAM,GAIV,OAHIhW,UACApP,KAAKsY,IAAI,sBAAmEnM,OAAOgZ,EAAW,KAAKhZ,OAAO4a,IAEtG5B,GACJ,KAAKlB,IAAIgD,mBAAmB/F,wBACxB8F,GAAW,EACX5B,EAAM,2BACN,MACJ,KAAKnB,IAAIgD,mBAAmB9F,wBAC5B,KAAK8C,IAAIgD,mBAAmB7F,qBACxB4F,GAAW,EACX5B,EAAM,0BACN,MACJ,KAAKnB,IAAIgD,mBAAmB3F,mBACxB0F,GAAW,EACX5B,EAAM,sBACN,MACJ,KAAKnB,IAAIgD,mBAAmB5F,kBACpBjS,UACApP,KAAKsY,IAAI,0BAA2FnM,OAAOnM,KAAKuiB,GAAG2E,gBAAiB,MAAM/a,OAAOgZ,EAAW,KAAKhZ,OAAO4a,IAE5K/mB,KAAKyiB,MAAMrkB,EAAQ6iB,aAAaH,EAAcO,mBAAoB,CAAE8D,UAAWrE,EAAcO,kBAAmB+D,IAAK,uBAAwFjZ,OAAOnM,KAAKuiB,GAAG2E,gBAAiB,SAAUC,SAAUxd,IACjQ5H,YAAW,WACPtB,EAAMsgB,WACV,GAAG,KACH,MACJ,QACI,OAEJiG,GACAhnB,KAAKyiB,MAAMrkB,EAAQ6iB,aAAaH,EAAciB,MAAO,CAAEoD,UAAWrE,EAAciB,KAAMqD,IAAK,GAAGjZ,OAAOiZ,EAAK,KAAKjZ,OAAOgZ,EAAW,KAAKhZ,OAAO4a,EAAc,KAAK5a,OAAOxC,EAAMyd,cAAeD,SAAUxd,IAE1M3J,KAAKuiB,GAAGqE,iBAAiB,MACzB5mB,KAAKoiB,UAAW,CACpB,EAEAJ,EAAU9hB,UAAU6gB,UAAY,WAC5B,IAAK/gB,KAAKuiB,IAAMviB,KAAKoiB,SAGjB,OAFApiB,KAAKsY,IAAI,0CAA2CtY,KAAKuiB,GAAIviB,KAAKoiB,eAClEpiB,KAAKyiB,MAAMrkB,EAAQ6iB,aAAaH,EAAciB,MAAO,CAAEoD,UAAWrE,EAAciB,KAAMqD,IAAK,qCAM/F,GAHIhW,UACApP,KAAKsY,IAAI,sCAETtY,KAAKuiB,GAAGqD,aAAe3B,IAAIqB,cAAcO,MAAMC,SAAU,CACrD1W,UACApP,KAAKsY,IAAI,yBAA0FnM,OAAOnM,KAAKmiB,mBAAoB,KAAKhW,OAAOnM,KAAKwiB,eAExJ,IAAIa,EAAW,IAAIY,IAAI8B,SAAS/lB,KAAKmiB,mBAAoBniB,KAAKwiB,cAC9DxiB,KAAKuiB,GAAGyD,kBAAkB3C,EAAUrjB,KAAKwiB,aAC7C,CACA,IAAKxiB,KAAKuiB,GAAG0D,qBAAuBjmB,KAAKuiB,GAAG0D,mBAAmBU,WAK3D,OAJIvX,UACApP,KAAKsY,IAAI,6BAEbtY,KAAKyiB,MAAMrkB,EAAQ6iB,aAAaH,EAAciB,MAAO,CAAEoD,UAAWrE,EAAciB,KAAMqD,IAAK,gBAG3FhW,UACApP,KAAKsY,IAAI,sCAEbtY,KAAKuiB,GAAGqE,iBAAiB5mB,KAAKqnB,SAAS9G,KAAKvgB,OAC5CA,KAAKiiB,eAAiB,EACtBjiB,KAAKkiB,iBAAmB,EACxBliB,KAAKqiB,UAAY,EACjBriB,KAAKuiB,GAAG+E,SACRtnB,KAAKoiB,UAAW,CACpB,EAEAJ,EAAU9hB,UAAUmnB,SAAW,SAAU1d,GACrC,IAAI4d,GAAmB,EACnBP,GAAW,EACX5B,EAAM,GACND,EAAYxb,EAAMmd,eAClBC,EAAe3oB,EAAQ6iB,aAAakE,GAIxC,OAHI/V,UACArI,QAAQuR,IAAI,qBAAwDnM,OAAOgZ,EAAW,KAAKhZ,OAAO4a,EAAc,MAE5G5B,GACJ,KAAKlB,IAAIgD,mBAAmB/F,wBACxB8F,GAAW,EACX5B,EAAM,+BAAyHjZ,OAAOgZ,EAAW,KAAKhZ,OAAO4a,GAC7J,MACJ,KAAK9C,IAAIgD,mBAAmBzF,cACxB4D,EAAM,oBAAkDjZ,OAAO4a,EAAc,KAAK5a,OAAOgZ,EAAW,KAAKhZ,OAAOxC,EAAM6d,aAAc,KACpI,MACJ,KAAKvD,IAAIgD,mBAAmB1F,mBACxB,IAAIkG,EAAgB9d,EAAM+d,qBACtBC,EAAahe,EAAMie,gBACnBC,EAAgBle,EAAMme,qBACtBC,EAAape,EAAMud,gBACvBlnB,KAAKiiB,eAAiBtY,EAAM+d,qBAC5B1nB,KAAKkiB,iBAAmBvY,EAAMie,gBAC1BxY,WACApP,KAAKsY,IAAI,sBAAwEnM,OAAOsb,EAAe,KAAKtb,OAAOwb,EAAY,KAAKxb,OAAO4a,EAAc,KAAK5a,OAAOgZ,IACrKnlB,KAAKsY,IAAI,oBAA4DnM,OAAO0b,EAAe,KAAK1b,OAAO4b,EAAY,KAAK5b,OAAO4a,EAAc,KAAK5a,OAAOgZ,KAE7JnlB,KAAKyiB,MAAMrkB,EAAQ6iB,aAAaH,EAAcS,oBAAqB,CAAE4D,UAAWA,EAAWC,IAAK,aAA+DjZ,OAAOsb,EAAe,KAAKtb,OAAOwb,EAAY,KAAKxb,OAAO4a,EAAc,KAAK5a,OAAOgZ,GAAYgC,SAAUxd,IACzQ,MACJ,KAAKsa,IAAIgD,mBAAmB9F,wBAC5B,KAAK8C,IAAIgD,mBAAmB7F,qBACxB4F,GAAW,EACX5B,EAAM,2BAAsGjZ,OAAOgZ,EAAW,KAAKhZ,OAAO4a,EAAc,KAAK5a,OAAOxC,EAAMyd,cAC1K,MACJ,KAAKnD,IAAIgD,mBAAmB3F,mBACxB0F,GAAW,EACX5B,EAAM,wBAAoFjZ,OAAOgZ,EAAW,KAAKhZ,OAAO4a,GACxH,MACJ,KAAK9C,IAAIgD,mBAAmBvF,gBACxB6F,GAAmB,EACnBnC,EAAM,kBAAgDjZ,OAAO4a,EAAc,KAAK5a,OAAOgZ,EAAW,KAAKhZ,OAAOxC,EAAMyd,cACpH,MACJ,KAAKnD,IAAIgD,mBAAmBtF,cACpBvS,UACArI,QAAQuR,IAAI,UAAW,mEAAoE,iBAAqCnM,OAAOxC,EAAMyd,aAAc,kBAAgDjb,OAAOnM,KAAKiiB,eAAgB,mBAAsD9V,OAAOnM,KAAKkiB,mBAE7SliB,KAAKqiB,YACDriB,KAAKqiB,WAAa,EAClBriB,KAAKuiB,GAAGyF,wBAGRhB,GAAW,EACX5B,EAAM,8BAAmHjZ,OAAOgZ,EAAW,KAAKhZ,OAAO4a,EAAc,KAAK5a,OAAOxC,EAAMyd,eAE3L,MACJ,KAAKnD,IAAIgD,mBAAmBxF,eACxB,KAES9X,EAAMyd,cAAgB,IAAIpI,SAAS,UAEpCiF,IAAIC,UAAU+D,WAAW,GAAG9b,OAAOnM,KAAKwiB,aAAc,UAAUrW,OAAOxC,EAAM6d,aAAc,QAEnG,CACA,MAAOtkB,GACH6D,QAAQ7D,MAAM,UAAW,mEAAoE,cAAgEiJ,OAAOgZ,EAAW,KAAKhZ,OAAO4a,EAAc,KAAK5a,OAAOjJ,EAAO,KAChO,CACAlD,KAAKsY,IAAI,mBAAiDnM,OAAOxC,EAAM6d,aAAc,KAAKrb,OAAOxC,EAAMyd,eACvG,MACJ,KAAKnD,IAAIgD,mBAAmBrF,iBACpBxS,UACArI,QAAQuR,IAAI,kBAA2CnM,OAAOgZ,EAAW,KAAKhZ,OAAO4a,EAAc,KAAK5a,OAAOxC,EAAMyd,eAEzH,MACJ,KAAKnD,IAAIgD,mBAAmBpF,gBACxBmF,GAAW,EACX5B,EAAM,4BAA4BjZ,OAAOgZ,EAAW,KAAKhZ,OAAO4a,EAAc,KAAK5a,OAAOxC,EAAMyd,cAQxG,GAHIhY,UACApP,KAAKsY,IAAI,UAAW,mEAAoE8M,GAExF4B,EAIA,OAHAhnB,KAAKuiB,GAAGqE,iBAAiB,MACzB5mB,KAAKoiB,UAAW,OAChBpiB,KAAKyiB,MAAMrkB,EAAQ6iB,aAAaH,EAAciB,MAAO,CAAEoD,UAAWrE,EAAciB,KAAMqD,IAAK,GAAGjZ,OAAOiZ,EAAK,KAAKjZ,OAAOxC,EAAMyd,cAAeD,SAAUxd,IAGzJ,GAAI4d,EAAkB,CAClBvnB,KAAKuiB,GAAGqE,iBAAiB,MACzB5mB,KAAKoiB,UAAW,EAKhB,IAAI8F,EAAsBloB,KAAKuiB,GAAG0D,mBAAmBkC,iBACrDD,EAAoBjlB,KAAK,YACzBvE,GAAGS,IAAI0lB,aAAaC,QAAQ,0BAA2BP,KAAKE,UAAUyD,IAClE9Y,WACApP,KAAKsY,IAAI,kBAA2CnM,OAAOzN,GAAGS,IAAI0lB,aAAahW,QAAQ,6BACvF7O,KAAKsY,IAAI,0BAA2FnM,OAAOzN,GAAGS,IAAI0lB,aAAahW,QAAQ,8BAE3I7O,KAAKyiB,MAAMrkB,EAAQ6iB,aAAaH,EAAcY,iBAAkB,CAAEyD,UAAWrE,EAAcY,gBAAiB0D,IAAK,QAA4BjZ,OAAOxC,EAAMyd,cAAeD,SAAUxd,GACvL,CACJ,EACAqY,EAAU9hB,UAAUoY,IAAM,WAEtB,IADA,IAAIrB,EAAO,GACFJ,EAAK,EAAGA,EAAK5K,UAAU3I,OAAQuT,IACpCI,EAAKJ,GAAM5K,UAAU4K,GAErBzH,UAEArI,QAAQuR,IAAIzN,MAAM9D,QAAS8E,EAAc,CAAC,UAD9B,oEACiDzJ,EAAO6U,IAAO,GAEnF,EACA+K,EAAU9hB,UAAUgD,MAAQ,WAExB,IADA,IAAI+T,EAAO,GACFJ,EAAK,EAAGA,EAAK5K,UAAU3I,OAAQuT,IACpCI,EAAKJ,GAAM5K,UAAU4K,GAErBzH,UAEArI,QAAQ7D,MAAM2H,MAAM9D,QAAS8E,EAAc,CAAC,UADhC,oEACmDzJ,EAAO6U,IAAO,GAErF,EACO+K,CACX,CArb8B,GAsb9B5jB,EAAQ2iB,UAAY,IAAIiB,C,eCrfxB,IAAIoG,EAMAC,EARJnqB,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQkqB,KAAOlqB,EAAQiqB,UAAYjqB,EAAQgqB,cAAW,EAEtD,SAAWA,GACPA,EAAc,IAAI,MAClBA,EAAe,KAAI,MACtB,CAHD,CAGGA,IAAahqB,EAAQgqB,SAAWA,EAAW,CAAC,IAG/C,SAAWC,GACPA,EAAe,IAAI,KACtB,CAFD,CAEGA,IAAcjqB,EAAQiqB,UAAYA,EAAY,CAAC,IAClD,IAAIE,EAAsB,WACtB,SAASA,IACLvoB,KAAKwoB,YAAc,EACnBxoB,KAAKyoB,UAAY,CAAC,CACtB,CA+KA,OAvKAF,EAAKroB,UAAUwoB,aAAe,SAAUpa,EAAKoH,EAAMjI,GAC/C,IAAIhN,EAAQT,KACZ,OAAO,IAAI4F,SAAQ,SAAUC,EAAGlD,GAC5BlC,EAAMkoB,QAAQra,EAAKoH,GAAM,SAAUA,GAC/B7P,EAAE6P,EACN,IAAG,SAAU1M,GACTrG,EAAEqG,EACN,GAAGyE,EACP,GACJ,EASA8a,EAAKroB,UAAUyoB,QAAU,SAAUra,EAAKoH,EAAMkT,EAASC,EAAMpb,GAiHzD,IAAI6M,EAAS,OACT7M,aAAuC,EAASA,EAAOe,QACvD8L,EAAS7M,EAAOe,MAEpB,IAAIsa,EAAMpqB,GAAGqqB,OAAOC,oBAEhBC,EAAU1E,KAAKE,UAAU/O,GAC7BoT,EAAI3iB,KAAKmU,EAAQhM,GAAK,GACtB,IAAI4a,EAAc,oBACdzb,aAAuC,EAASA,EAAOyb,eACvDA,EAAczb,EAAOyb,aAGzBJ,EAAIK,iBAAiB,eAAgBD,GACrCJ,EAAIM,mBAAqB,WACrB,GAAuB,IAAnBN,EAAIO,WAAkB,CACtB,IAAIC,EAAWR,EAAIS,aACfT,EAAIU,QAAU,KAAOV,EAAIU,OAAS,KAE9B/b,EAAOgc,SACPH,EAAW7b,EAAOgc,OAAgB,QAAEH,IAExCV,EAAQrE,KAAKC,MAAM8E,KAGnBT,EAAKS,EAEb,CACJ,EAEI7b,EAAOgc,SACPR,EAAU,UAAYxb,EAAOgc,OAAgB,QAAER,IAGnDH,EAAIY,KAAKT,EACb,EACOV,CACX,CApLyB,GAqLzBnqB,EAAQkqB,KAAO,IAAIC,C,eCjMnBrqB,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQwP,SAWR,SAAkBrP,EAAMqU,EAAU5E,GAE9B,IADA,IAAIiJ,EAAO,GACFJ,EAAK,EAAGA,EAAK5K,UAAU3I,OAAQuT,IACpCI,EAAKJ,EAAK,GAAK5K,UAAU4K,GAE7B,IAAI4E,EAAK5N,aAAY,WACZtP,GAASG,GAAGirB,QAAQprB,GAAM,GAI/BqU,SAAoDA,EAAS/H,MAAM7K,KAAM,CAACiX,IAHtEnJ,cAAc2N,EAItB,GAAGzN,EAASiJ,GACZ,OAAOwE,CACX,C,eCNA,SAASmO,EAAerrB,EAAMiC,EAAQqpB,GAClCtrB,EAAK+f,KAAO9d,EAAO8d,KACnB/f,EAAKurB,OAAStpB,EAAOspB,YACEppB,IAAnBF,EAAOupB,UACPxrB,EAAKwrB,QAAUvpB,EAAOupB,SAEtBvpB,EAAOwpB,SACPzrB,EAAKiS,QAAS,GAElBqZ,EAAMzoB,SAAS7C,GACf0rB,EAAU1rB,EACd,CA3BAL,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQ8rB,aAAe9rB,EAAQ+rB,aAAe/rB,EAAQgsB,YAAchsB,EAAQisB,wBAAqB,EACjGjsB,EAAQwrB,eAAiBA,EACzBxrB,EAAQ6rB,UAAYA,EAEpB7rB,EAAQisB,mBAAqB,CACzB,CAAE7b,KAAM,SAAU8P,KAAM,cAAewL,OAAQ,IAGnD1rB,EAAQgsB,YAAc,IAAI1rB,GAAGqC,KAsB7B,IAAIopB,EAA8B,WAC9B,SAASA,IACLnqB,KAAKsqB,SAAW,IAAIzY,IACpB7R,KAAK6pB,MAAQ,IACjB,CAwHA,OApHAM,EAAajqB,UAAUqqB,KAAO,WAC1B,IAAI9pB,EAAQT,KACZA,KAAK6pB,MAAQnrB,GAAGma,OAAOC,SAASva,KAChCyB,KAAKmK,QAEL/L,EAAQisB,mBAAmBnT,SAAQ,SAAU1W,GACrB,WAAhBA,EAAOgO,OACPob,EAAexrB,EAAQgsB,YAAa5pB,EAAQC,EAAMopB,OAClDppB,EAAM6pB,SAAShZ,IAAI9Q,EAAOgO,KAAMpQ,EAAQgsB,aAEhD,GACJ,EACAlsB,OAAOC,eAAegsB,EAAajqB,UAAW,aAAc,CACxD6F,IAAK,WACD,OAAO/F,KAAK6pB,KAChB,EACA7jB,YAAY,EACZC,cAAc,IAKlBkkB,EAAajqB,UAAUiK,MAAQ,WAC3BnK,KAAKsqB,SAASpT,SAAQ,SAAU3Y,GAC5BA,EAAKisB,mBACLjsB,EAAKksB,SACT,IACAzqB,KAAKsqB,SAASngB,OAClB,EAKAggB,EAAajqB,UAAUwqB,SAAW,SAAUlqB,GACxC,IAAKR,KAAK6pB,MACN,MAAM,IAAIhgB,MAAM,gCAGpB7J,KAAK2qB,YAAYnqB,EAAOgO,MACxB,IAAIjQ,EAAO,IAAIG,GAAGqC,KAGlB,OAFA6oB,EAAerrB,EAAMiC,EAAQR,KAAK6pB,OAClC7pB,KAAKsqB,SAAShZ,IAAI9Q,EAAOgO,KAAMjQ,GACxBA,CACX,EAMA4rB,EAAajqB,UAAU0qB,aAAe,SAAUpc,EAAMjQ,GAClD,IAAKyB,KAAK6pB,MACN,MAAM,IAAIhgB,MAAM,gCAGpB7J,KAAK2qB,YAAYnc,GAEjBxO,KAAKsqB,SAAShZ,IAAI9C,EAAMjQ,EAC5B,EAKA4rB,EAAajqB,UAAUyqB,YAAc,SAAUnc,GAC3C,IAAIqc,EAAQ7qB,KAAKsqB,SAASvkB,IAAIyI,GAC9B,QAAIqc,IACAA,EAAML,mBACNK,EAAMJ,UACNzqB,KAAKsqB,SAASQ,OAAOtc,IACd,EAGf,EAKA2b,EAAajqB,UAAU6qB,SAAW,SAAUvc,GACxC,OAAOxO,KAAKsqB,SAASvkB,IAAIyI,EAC7B,EAKA2b,EAAajqB,UAAU8qB,UAAY,SAAUxc,GACzC,IAAIqc,EAAQ7qB,KAAKsqB,SAASvkB,IAAIyI,GAC1Bqc,IACAA,EAAMra,QAAS,EAEvB,EAKA2Z,EAAajqB,UAAU+qB,UAAY,SAAUzc,GACzC,IAAIqc,EAAQ7qB,KAAKsqB,SAASvkB,IAAIyI,GAC1Bqc,IACAA,EAAMra,QAAS,EAEvB,EAMA2Z,EAAajqB,UAAUgrB,gBAAkB,SAAU1c,EAAMub,GACrD,IAAIc,EAAQ7qB,KAAKsqB,SAASvkB,IAAIyI,GAC1Bqc,IACAA,EAAMd,QAAUA,EAExB,EAIAI,EAAajqB,UAAUirB,cAAgB,WACnC,OAAOnmB,MAAM+G,KAAK/L,KAAKsqB,SAAS/O,OACpC,EACO4O,CACX,CA7HiC,GAqIjC,SAASF,EAAU1rB,GACfA,EAAKU,MAAQ,IACbV,EAAKW,OAAS,OACdX,EAAK6sB,QAAU7sB,EAAK8sB,QAAU,EAC9B,IAAI7sB,EAASD,EAAK0C,aAAavC,GAAGC,QAClCH,EAAO8sB,YAAa,EACpB9sB,EAAO+sB,aAAc,EACrB/sB,EAAOgtB,cAAe,EACtBhtB,EAAOitB,eAAgB,EACvBjtB,EAAOmB,IAAM,EACbnB,EAAOktB,OAAS,EAChBltB,EAAOmtB,KAAO,EACdntB,EAAOotB,MAAQ,CACnB,CApBAxtB,EAAQ+rB,aAAeA,EAEvB/rB,EAAQ8rB,aAAe,IAAIC,C,eC/J3B,IAAI0B,EAHJ3tB,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQ6D,UAAY7D,EAAQytB,gBAAa,EAGzC,SAAWA,GACPA,EAAkB,MAAI,QACtBA,EAAoB,QAAI,UACxBA,EAAiB,KAAI,MACxB,CAJD,CAIGA,IAAeztB,EAAQytB,WAAaA,EAAa,CAAC,IACrD,IAAI5pB,EAA2B,WAC3B,SAASA,IACT,CAkYA,OA5XAA,EAAUL,iBAAmB,SAAU+H,EAAOiJ,GACrC5S,KAAK8rB,OAAOniB,KACb3J,KAAK8rB,OAAOniB,GAAS,IAEzB3J,KAAK8rB,OAAOniB,GAAO1G,KAAK2P,EAC5B,EAMA3Q,EAAU8pB,SAAW,SAAUzd,GAC3B,MAAO,iBAAiB0d,KAAK1d,EACjC,EACArM,EAAUgqB,SAAW,SAAU3d,GAC3B,IAAIzK,EACJ,OAA2C,QAAnCA,EAAK7D,KAAKksB,eAAe5d,UAAyB,IAAPzK,OAAgB,EAASA,EAAGsM,KACnF,EAWAlO,EAAUkN,KAAO,SAAUgd,EAAO3d,EAAM0B,GACpC,GAAKlQ,KAAKksB,eAAeC,GA4BjBnsB,KAAKksB,eAAeC,GAAOhc,MACvBnQ,KAAKksB,eAAeC,GAAOhc,MAAMwZ,QAC7BzZ,GACAA,EAAWrF,MAAM7K,KAAM,CAAC,KAAMA,KAAKksB,eAAeC,GAAOhc,gBAItDnQ,KAAKksB,eAAeC,GAC3BnsB,KAAKmP,KAAKgd,EAAO3d,EAAM0B,IAIvBA,GACAlQ,KAAKksB,eAAeC,GAAOC,YAAYnpB,KAAKiN,OAzCvB,CAC7BlQ,KAAKksB,eAAeC,GAAS,CAAEC,YAAalc,EAAa,CAACA,GAAc,IACxE,IAAImc,EAASrsB,KACTssB,EAAY,SAAUtjB,EAAKmH,GACvBnH,GACAtK,GAAGwE,MAAM,YAA0D8F,GAEvEqjB,EAAOH,eAAeC,GAAOhc,MAAQA,EAErC,IADA,IAAIic,EAAcC,EAAOH,eAAeC,GAAOC,YACtCroB,EAAM,EAAGA,GAAOqoB,aAAiD,EAASA,EAAY9oB,QAASS,IAC/EqoB,EAAYroB,GAClB8G,MAAM7K,KAAM,CAACgJ,EAAKmH,IAErCkc,EAAOH,eAAeC,GAAOC,YAAY9oB,OAAS,EAElD,IADA,IAAIipB,EAAaF,EAAOP,OAAa,KAC5B1nB,EAAM,EAAGA,GAAOmoB,aAA+C,EAASA,EAAWjpB,QAASc,IACnFmoB,EAAWnoB,GACjByG,MAAMwhB,EAAQ,CAAC,YAAaF,GAE5C,EACKnsB,KAAK+rB,SAASI,GAIfztB,GAAG8tB,aAAaC,WAAWN,EAAOG,GAHlC5tB,GAAGguB,UAAUvd,KAAKgd,EAAO3d,EAAM8d,EAKvC,CAmBJ,EAOArqB,EAAU0qB,UAAY,SAAUR,EAAO3d,GACnC,IAAI/N,EAAQT,KACZ,OAAO,IAAI4F,SAAQ,SAAUC,EAAGlD,GAC5BlC,EAAM0O,KAAKgd,EAAO3d,GAAM,SAAUtL,EAAO0pB,GACjC1pB,EACAP,EAAEO,GAGN2C,EAAE+mB,EACN,GACJ,GACJ,EAQA3qB,EAAU+M,aAAe,SAAUrO,EAAYwrB,EAAO3d,EAAM0B,GACxD,IAAIzP,EAAQT,KACZA,KAAK6sB,WAAWlsB,GAAY,SAAUqI,EAAK8jB,GACvC,GAAI9jB,EAKA,OAJAjC,QAAQ7D,MAAM,UAAUiJ,OAAOxL,EAAY,YAAgDqI,QACvFkH,GACAA,EAAWrF,MAAMpK,EAAO,CAACuI,EAAK,QAItC,IAAI+D,EAAMpM,EAAa,IAAMwrB,EACxB1rB,EAAMyrB,eAAenf,GAiBlBtM,EAAMyrB,eAAenf,GAAKoD,MACtB1P,EAAMyrB,eAAenf,GAAKoD,MAAMwZ,QAC5BzZ,GACAA,EAAWrF,MAAMpK,EAAO,CAAC,KAAMA,EAAMyrB,eAAenf,GAAKoD,gBAItD1P,EAAMyrB,eAAenf,GAC5BtM,EAAMuO,aAAarO,EAAYwrB,EAAO3d,EAAM0B,IAI5CA,GACAzP,EAAMyrB,eAAenf,GAAKqf,YAAYnpB,KAAKiN,IA7BnDzP,EAAMyrB,eAAenf,GAAO,CAAEqf,YAAalc,EAAa,CAACA,GAAc,IACvE4c,EAAO3d,KAAKgd,EAAO3d,GAAM,SAAUxF,EAAK+jB,GACpCtsB,EAAMyrB,eAAenf,GAAKoD,MAAQ4c,EAElC,IADA,IAAIX,EAAc3rB,EAAMyrB,eAAenf,GAAKqf,YACnCpQ,EAAM,EAAGA,GAAOoQ,aAAiD,EAASA,EAAY9oB,QAAS0Y,IAC/EoQ,EAAYpQ,GAClBnR,MAAMpK,EAAO,CAACuI,EAAK+jB,IAGtC,IADA,IAAIR,EAAa9rB,EAAMqrB,OAAa,KAC3B1O,EAAM,EAAGA,GAAOmP,aAA+C,EAASA,EAAWjpB,QAAS8Z,IACnFmP,EAAWnP,GACjBvS,MAAMpK,EAAO,CAACE,EAAYwrB,GAE1C,IAoBR,GACJ,EAQAlqB,EAAU+qB,kBAAoB,SAAUrsB,EAAYwrB,EAAO3d,GACvD,IAAI/N,EAAQT,KACZ,OAAO,IAAI4F,SAAQ,SAAUC,EAAGlD,GAC5BlC,EAAMuO,aAAarO,EAAYwrB,EAAO3d,GAAM,SAAUxF,EAAKmH,GACnDnH,EACArG,EAAEqG,GAGNnD,EAAEsK,EACN,GACJ,GACJ,EAMAlO,EAAU4qB,WAAa,SAAUI,EAAW/c,GACxC,IAAIgd,EAAOltB,KACNA,KAAKksB,eAAee,GAajBjtB,KAAKksB,eAAee,GAAW9c,MAC3BD,GACAA,EAAWrF,MAAM7K,KAAM,CAAC,KAAMA,KAAKksB,eAAee,GAAW9c,QAI7DD,GACAlQ,KAAKksB,eAAee,GAAWb,YAAYnpB,KAAKiN,IAnBxDlQ,KAAKksB,eAAee,GAAa,CAAEb,YAAalc,EAAa,CAACA,GAAc,IAC5ExR,GAAG8tB,aAAaK,WAAWI,GAAW,SAAUjkB,EAAK8jB,GACjDI,EAAKhB,eAAee,GAAW9c,MAAQ2c,EAEvC,IADA,IAAIV,EAAcc,EAAKhB,eAAee,GAAWb,YACxCe,EAAM,EAAGA,GAAOf,aAAiD,EAASA,EAAY9oB,QAAS6pB,IAC/Ef,EAAYe,GAClBtiB,MAAM7K,KAAM,CAACgJ,EAAK8jB,IAErCI,EAAKhB,eAAee,GAAWb,YAAY9oB,OAAS,CACxD,IAcR,EAOArB,EAAUmrB,mBAAqB,SAAUN,EAAQO,EAAWnd,GACxD,IAAIgd,EAAOltB,KACP+M,EAAM,eAAiBsgB,EACtBrtB,KAAKksB,eAAenf,GAcjBmgB,EAAKhB,eAAenf,GAAmB,aACnCmD,GACAA,EAAWrF,MAAM7K,KAAM,CAAC,OAIxBkQ,GACAlQ,KAAKksB,eAAenf,GAAKqf,YAAYnpB,KAAKiN,IApBlDlQ,KAAKksB,eAAenf,GAAO,CAAEqf,YAAalc,EAAa,CAACA,GAAc,IACtE4c,EAAOQ,aAAaD,GAAW,SAAUrkB,GACrC,IAAIojB,EAAcc,EAAKhB,eAAenf,GAAKqf,YAC3Cc,EAAKhB,eAAenf,GAAmB,cAAI,EAC3C,IAAK,IAAIwgB,EAAM,EAAGA,GAAOnB,aAAiD,EAASA,EAAY9oB,QAASiqB,IAC/EnB,EAAYmB,GAClB1iB,MAAM7K,KAAM,CAACgJ,IAEhCkkB,EAAKhB,eAAenf,GAAKqf,YAAY9oB,OAAS,SACvC4pB,EAAKhB,eAAenf,GAAmB,YAClD,IAcR,EAOA9K,EAAUurB,cAAgB,SAAUV,EAAQX,EAAO3d,EAAM0B,GACrD,IAAIgd,EAAOltB,KACP+M,EAAM,UAAYof,EACjBnsB,KAAKksB,eAAenf,GAajBmgB,EAAKhB,eAAenf,GAAgB,UAChCmD,GACAA,EAAWrF,MAAM7K,KAAM,CAAC,KAAMktB,EAAKhB,eAAenf,GAAgB,YAIlEmD,GACAlQ,KAAKksB,eAAenf,GAAKqf,YAAYnpB,KAAKiN,IAnBlDlQ,KAAKksB,eAAenf,GAAO,CAAEqf,YAAalc,EAAa,CAACA,GAAc,IACtE4c,EAAOW,QAAQtB,EAAO3d,GAAM,SAAUtL,EAAOwqB,GACzC,IAAItB,EAAcc,EAAKhB,eAAenf,GAAKqf,YAC3Cc,EAAKhB,eAAenf,GAAgB,UAAI2gB,EACxC,IAAK,IAAIC,EAAM,EAAGA,GAAOvB,aAAiD,EAASA,EAAY9oB,QAASqqB,IAC/EvB,EAAYuB,GAClB9iB,MAAM7K,KAAM,CAACkD,EAAOwqB,IAEvCR,EAAKhB,eAAenf,GAAKqf,YAAY9oB,OAAS,CAClD,IAcR,EAMArB,EAAU2rB,aAAe,SAAUC,EAAQvf,GACvCtO,KAAKmP,KAAKb,EAAK5P,GAAGovB,aAAa,SAAU9kB,EAAKmH,GACtCnH,IAGJ6kB,EAAOE,YAAc5d,EACzB,GACJ,EAUAlO,EAAUE,kBAAoB,SAAUnB,EAA4BJ,EAAgBotB,EAAqB3tB,EAAc4tB,EAAUC,GAC7H,IAAIztB,EAAQT,UACM,IAAdkuB,IAAwBA,EAAY,GACxC,IAAIC,EAAY,EACZhuB,EAAO,WACW,IAAdguB,IACAntB,EAA2BY,iBAAiBV,YAAYW,YAAYC,UAAU,SAAUa,GACpF,IAAIkB,EAAIwE,EACJ+G,UACArI,QAAQuR,IAAI,+BAAgC1X,IAED,QAA1CiD,EAAK7C,EAA2BzC,YAAyB,IAAPsF,OAAgB,EAASA,EAAGoM,UACpC,QAA1C5H,EAAKrH,EAA2BzC,YAAyB,IAAP8J,GAAyBA,EAAG4H,OAAOme,YAAYptB,EAA2BzC,MAErI,GAAGkC,GACHO,EAA2BX,aAAeA,EAC1CW,EAA2BqtB,cAAcJ,EAAUC,GAE3D,EACAluB,KAAKmP,KAAKvO,EAAgBM,YAAYotB,kBAAkB,SAAUtlB,EAAKmH,GAC/DnH,IAGJhI,EAA2ButB,YAAcpe,EACzCge,IACAhuB,IACJ,IACAH,KAAKmP,KAAK6e,EAAqB9sB,YAAYstB,uBAAuB,SAAUxlB,EAAKmH,GACzEnH,IAGJhI,EAA2BytB,iBAAmBte,EAC9Cge,IACAhuB,IACJ,GACJ,EAYA8B,EAAUC,0BAA4B,SAAUvB,EAAYK,EAA4BJ,EAAgBotB,EAAqB3tB,EAAc4tB,EAAUC,EAAWQ,GAC5J,IAAIjuB,EAAQT,UACM,IAAdkuB,IAAwBA,EAAY,QACjB,IAAnBQ,IAA6BA,GAAiB,GAClD1uB,KAAK6sB,WAAWlsB,GAAY,SAAUqI,EAAK8jB,GACvC,IAAI9jB,EAAJ,CAGA,IAAImlB,EAAY,EACZhuB,EAAO,WACW,IAAdguB,IACAntB,EAA2BY,iBAAiBV,YAAYW,YAAYC,UAAU,SAAUa,GACpF,IAAIkB,EAAIwE,EACJqmB,IAA8D,QAA1C7qB,EAAK7C,EAA2BzC,YAAyB,IAAPsF,OAAgB,EAASA,EAAGoM,UAC9Fb,UACArI,QAAQuR,IAAI,uCAAwC1X,GAEb,QAA1CyH,EAAKrH,EAA2BzC,YAAyB,IAAP8J,GAAyBA,EAAG4H,OAAOme,YAAYptB,EAA2BzC,MAErI,GAAGkC,GACHO,EAA2BX,aAAeA,EAC1CW,EAA2BqtB,cAAcJ,EAAUC,GAE3D,EACApB,EAAO3d,KAAKvO,EAAgBM,YAAYotB,kBAAkB,SAAUtlB,EAAKmH,GACjEnH,IAGJhI,EAA2ButB,YAAcpe,EACzCge,IACAhuB,IACJ,IACA2sB,EAAO3d,KAAK6e,EAAqB9sB,YAAYstB,uBAAuB,SAAUxlB,EAAKmH,GAC3EnH,IAGJhI,EAA2BytB,iBAAmBte,EAC9Cge,IACAhuB,IACJ,GAhCA,CAiCJ,GACJ,EACA8B,EAAU6pB,OAAS,CAAC,EACpB7pB,EAAUiqB,eAAiB,CAAC,EACrBjqB,CACX,CArY8B,GAsY9B7D,EAAQ6D,UAAYA,C,eC/YpB/D,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQuwB,aAAevwB,EAAQwwB,oBAAiB,EAChDxwB,EAAQwwB,eAAiB,sCAKzB,IAAID,EAA8B,WAC9B,SAASA,IACT,CA8BA,OAzBAA,EAAazJ,SAAW,WACpB,IAAIrhB,EACJ,OAAyB,QAAjBA,EAAKnF,GAAGS,WAAwB,IAAP0E,OAAgB,EAASA,EAAGqhB,QACjE,EASAyJ,EAAajF,KAAO,SAAUzP,EAAYvE,EAAMmZ,EAAiBvX,GAE7D,QADkB,IAAdA,IAAwBA,EAAYlZ,EAAQwwB,gBAC3C5uB,KAAKklB,WAIV,MAAY,IAARxP,EACOuO,IAAI6K,WAAWC,iBAAiBzX,EAAW2C,EAAY4U,GAGvD5K,IAAI6K,WAAWC,iBAAiBzX,EAAW2C,EAAY4U,EAAiBnZ,EAEvF,EACOiZ,CACX,CAjCiC,GAkCjCvwB,EAAQuwB,aAAeA,C,eCzCvBzwB,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQ4wB,UAQR,SAAmBC,EAAKvvB,GACpB,OAAOD,KAAKwE,MAAMxE,KAAKyE,UAAYxE,EAAMuvB,IAAQA,CACrD,EATA7wB,EAAQ8wB,YAgBR,SAAqBD,EAAKvvB,GACtB,OAAOD,KAAKyE,UAAYxE,EAAMuvB,GAAOA,CACzC,C,eCpBA/wB,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQ+wB,yBAKR,WACSC,WACDlxB,OAAOC,eAAeO,GAAGqC,KAAKb,UAAW,SAAU,CAC/C6F,IAAK,WACD,OAAO/F,KAAKqvB,OAChB,EACA/d,IAAK,SAAUjT,GAIX,GAchB,SAAoC6uB,EAAM7uB,GAEtC,IAAIixB,EAAyBpC,EAAkB,aAAKA,EAAkB,YAAEqC,MAAK,SAAU7jB,GAAK,OAAOA,EAAE8jB,cAAgB,IAGrH,GAFAtC,EAAKnD,QAAU,IAEXuF,EACA,GAAIjxB,GAEA,GADAixB,EAAsC,cAAI,GACtCpC,EAAKjd,OAAQ,CACb,IAAIwf,EAAS,SAAUzd,GACnB,KAAIA,EAAO/B,kBAAkBvR,GAAGgxB,QAE5B1d,EAAO/B,OAAQ,CACf,GAAI+B,EAAO/B,OAAO0f,SAAU,CACxB,IAAIjoB,EAAQsK,EAAO/B,OAAO0f,SAASta,QAAQrD,GAEpBA,EAAO/B,OAAO0f,SAASnsB,MAAM,EAAGkE,GACtCwP,SAAQ,SAAUxL,GAC3BA,EAAEqe,QAAU,GAAKre,EAAE8E,QACnB8e,EAAsC,cAAErsB,KAAKyI,GAEjDA,EAAEqe,QAAU,CAChB,GACJ,CAEA0F,EAAOzd,EAAO/B,OAClB,CACJ,EACAwf,EAAOvC,EACX,MAEC,CAED,IAAI0C,EAAgBN,EAAsC,cAC1D,GAAIM,EAAe,CACf,IAAK,IAAI7rB,EAAM,EAAGA,EAAM6rB,EAActsB,OAAQS,IAC1C6rB,EAAc7rB,GAAKgmB,QAAU,IAEjCuF,EAAsC,cAAEhsB,OAAS,CACrD,CACJ,CAGR,CA1DgBusB,CAA2B7vB,KAF3B3B,IAAUA,GAGN2B,KAAKqvB,UAAYhxB,EAAO,CACxB2B,KAAKqvB,QAAUhxB,EACf,IAAI4R,EAASjQ,KAAK8vB,QACd7f,GACyBA,EAAO8f,oBAE5BrxB,GAAGwS,SAAyB,eAAE8e,aAAahwB,KAAM3B,EAG7D,CACJ,GAGZ,C,qBC7BA,IAAIgM,EAAarK,MAAQA,KAAKqK,WAAc,SAAUC,EAASC,EAAYC,EAAGC,GAE1E,OAAO,IAAKD,IAAMA,EAAI5E,WAAU,SAAUgB,EAASC,GAC/C,SAAS6D,EAAUrM,GAAS,IAAMsM,EAAKF,EAAU1H,KAAK1E,GAAS,CAAE,MAAOsE,GAAKkE,EAAOlE,EAAI,CAAE,CAC1F,SAASiI,EAASvM,GAAS,IAAMsM,EAAKF,EAAiB,MAAEpM,GAAS,CAAE,MAAOsE,GAAKkE,EAAOlE,EAAI,CAAE,CAC7F,SAASgI,EAAK5C,GAJlB,IAAe1J,EAIa0J,EAAO/E,KAAO4D,EAAQmB,EAAO1J,QAJ1CA,EAIyD0J,EAAO1J,MAJhDA,aAAiBmM,EAAInM,EAAQ,IAAImM,GAAE,SAAU5D,GAAWA,EAAQvI,EAAQ,KAIjByJ,KAAK4C,EAAWE,EAAW,CAC7GD,GAAMF,EAAYA,EAAUI,MAAMP,EAASC,GAAc,KAAKxH,OAClE,GACJ,EACI+H,EAAe9K,MAAQA,KAAK8K,aAAgB,SAAUR,EAASS,GAC/D,IAAsGC,EAAGC,EAAGxD,EAAxGyD,EAAI,CAAEC,MAAO,EAAGC,KAAM,WAAa,GAAW,EAAP3D,EAAE,GAAQ,MAAMA,EAAE,GAAI,OAAOA,EAAE,EAAI,EAAG4D,KAAM,GAAIC,IAAK,IAAeC,EAAIrN,OAAOqH,QAA4B,mBAAbiG,SAA0BA,SAAWtN,QAAQgC,WACtL,OAAOqL,EAAExI,KAAO0I,EAAK,GAAIF,EAAS,MAAIE,EAAK,GAAIF,EAAU,OAAIE,EAAK,GAAsB,mBAAXjJ,SAA0B+I,EAAE/I,OAAOC,UAAY,WAAa,OAAOzC,IAAM,GAAIuL,EAC1J,SAASE,EAAKnJ,GAAK,OAAO,SAAUoJ,GAAK,OACzC,SAAcC,GACV,GAAIX,EAAG,MAAM,IAAI7F,UAAU,mCAC3B,KAAOoG,IAAMA,EAAI,EAAGI,EAAG,KAAOT,EAAI,IAAKA,OACnC,GAAIF,EAAI,EAAGC,IAAMxD,EAAY,EAARkE,EAAG,GAASV,EAAU,OAAIU,EAAG,GAAKV,EAAS,SAAOxD,EAAIwD,EAAU,SAAMxD,EAAE5E,KAAKoI,GAAI,GAAKA,EAAElI,SAAW0E,EAAIA,EAAE5E,KAAKoI,EAAGU,EAAG,KAAK3I,KAAM,OAAOyE,EAE3J,OADIwD,EAAI,EAAGxD,IAAGkE,EAAK,CAAS,EAARA,EAAG,GAAQlE,EAAEpJ,QACzBsN,EAAG,IACP,KAAK,EAAG,KAAK,EAAGlE,EAAIkE,EAAI,MACxB,KAAK,EAAc,OAAXT,EAAEC,QAAgB,CAAE9M,MAAOsN,EAAG,GAAI3I,MAAM,GAChD,KAAK,EAAGkI,EAAEC,QAASF,EAAIU,EAAG,GAAIA,EAAK,CAAC,GAAI,SACxC,KAAK,EAAGA,EAAKT,EAAEI,IAAIM,MAAOV,EAAEG,KAAKO,MAAO,SACxC,QACI,MAAkBnE,GAAZA,EAAIyD,EAAEG,MAAY/H,OAAS,GAAKmE,EAAEA,EAAEnE,OAAS,KAAkB,IAAVqI,EAAG,IAAsB,IAAVA,EAAG,IAAW,CAAET,EAAI,EAAG,QAAU,CAC3G,GAAc,IAAVS,EAAG,MAAclE,GAAMkE,EAAG,GAAKlE,EAAE,IAAMkE,EAAG,GAAKlE,EAAE,IAAM,CAAEyD,EAAEC,MAAQQ,EAAG,GAAI,KAAO,CACrF,GAAc,IAAVA,EAAG,IAAYT,EAAEC,MAAQ1D,EAAE,GAAI,CAAEyD,EAAEC,MAAQ1D,EAAE,GAAIA,EAAIkE,EAAI,KAAO,CACpE,GAAIlE,GAAKyD,EAAEC,MAAQ1D,EAAE,GAAI,CAAEyD,EAAEC,MAAQ1D,EAAE,GAAIyD,EAAEI,IAAIrI,KAAK0I,GAAK,KAAO,CAC9DlE,EAAE,IAAIyD,EAAEI,IAAIM,MAChBV,EAAEG,KAAKO,MAAO,SAEtBD,EAAKZ,EAAKlI,KAAKyH,EAASY,EAC5B,CAAE,MAAOvI,GAAKgJ,EAAK,CAAC,EAAGhJ,GAAIsI,EAAI,CAAG,CAAE,QAAUD,EAAIvD,EAAI,CAAG,CACzD,GAAY,EAARkE,EAAG,GAAQ,MAAMA,EAAG,GAAI,MAAO,CAAEtN,MAAOsN,EAAG,GAAKA,EAAG,QAAK,EAAQ3I,MAAM,EAC9E,CAtBgD2H,CAAK,CAACrI,EAAGoJ,GAAK,CAAG,CAuBrE,EACAxN,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQ6xB,gBAAa,EAQrB,IAAIA,EAA4B,WAO5B,SAASA,EAAWC,EAASC,EAAQC,GACjCpwB,KAAKkwB,QAAUA,EACflwB,KAAKmwB,OAASA,EACdnwB,KAAKowB,QAAUA,EACfpwB,KAAKqwB,KAAO,GACZrwB,KAAKswB,OAAS,IAAIC,IAClBvwB,KAAKwwB,OACT,CA6IA,OA5IAP,EAAW/vB,UAAUswB,MAAQ,WACzB,OAAOnmB,EAAUrK,UAAM,OAAQ,GAAQ,WACnC,IAAI6D,EAAIjF,EAAMqY,EAAMwZ,EAAkBC,EAAKC,EAC3C,OAAO7lB,EAAY9K,MAAM,SAAUqI,GAC/B,OAAQA,EAAG8C,OACP,KAAK,EACD,IAAKnL,KAAKowB,QAAS,MAAO,CAAC,EAAa,GACxCvsB,EAAK7D,KAAKowB,QAASxxB,EAAOiF,EAAGjF,KAAMqY,EAAOpT,EAAGoT,KAAMwZ,EAAmB5sB,EAAG4sB,iBACrE1iB,MAAMnP,KACNA,EAAO,GACXyJ,EAAG8C,MAAQ,EACf,KAAK,EACD9C,EAAGgD,KAAKpI,KAAK,CAAC,EAAG,EAAG,CAAE,IACtBoF,EAAG8C,MAAQ,EACf,KAAK,EACD,OAAMnL,KAAKqwB,KAAK/sB,OAAS1E,EAClB,CAAC,EAAaoB,KAAKkwB,QAAQjZ,IADK,CAAC,EAAa,GAEzD,KAAK,EAID,OAHAyZ,EAAMroB,EAAG+C,OACTpL,KAAKswB,OAAOM,IAAIF,GAChB1wB,KAAKqwB,KAAKptB,KAAKytB,GACR,CAAC,EAAa,GACzB,KAAK,EAID,OAHID,GACAA,EAAiB,MAEd,CAAC,EAAa,GACzB,KAAK,EAKD,OAJAE,EAAUtoB,EAAG+C,OACTqlB,GACAA,EAAiBE,GAEd,CAAC,EAAa,GACzB,KAAK,EAAG,MAAO,CAAC,GAExB,GACJ,GACJ,EACAzyB,OAAOC,eAAe8xB,EAAW/vB,UAAW,WAAY,CAIpD6F,IAAK,WACD,OAAO/F,KAAKqwB,KAAK/sB,MACrB,EACA0C,YAAY,EACZC,cAAc,IAOlBgqB,EAAW/vB,UAAU2wB,SAAW,SAAUH,GACtC,OAAO1wB,KAAKswB,OAAOpW,IAAIwW,EAC3B,EAIAT,EAAW/vB,UAAU6F,IAAM,WAEvB,IADA,IAAIkR,EAAO,GACFJ,EAAK,EAAGA,EAAK5K,UAAU3I,OAAQuT,IACpCI,EAAKJ,GAAM5K,UAAU4K,GAEzB,OAAOxM,EAAUrK,UAAM,OAAQ,GAAQ,WACnC,IAAI0wB,EACJ,OAAO5lB,EAAY9K,MAAM,SAAU6D,GAC/B,OAAQA,EAAGsH,OACP,KAAK,EACD,OAAMnL,KAAKqwB,KAAK/sB,OAAS,GACzBotB,EAAM1wB,KAAKqwB,KAAKzkB,MAChB5L,KAAKmwB,OAAOO,GACL,CAAC,EAAa,IAHe,CAAC,EAAa,GAItD,KAAK,EAAG,MAAO,CAAC,EAAa1wB,KAAKkwB,QAAQjZ,IAC1C,KAAK,EACDyZ,EAAM7sB,EAAGuH,OACTvH,EAAGsH,MAAQ,EACf,KAAK,EAED,OADAnL,KAAKswB,OAAOM,IAAIF,GACT,CAAC,EAAcA,GAElC,GACJ,GACJ,EAMAT,EAAW/vB,UAAU4wB,SAAW,WAG5B,IAFA,IAAIrwB,EAAQT,KACRiX,EAAO,GACFJ,EAAK,EAAGA,EAAK5K,UAAU3I,OAAQuT,IACpCI,EAAKJ,GAAM5K,UAAU4K,GAEzB,OAAO,IAAIjR,SAAQ,SAAUgB,EAASC,GAAU,OAAOwD,EAAU5J,OAAO,OAAQ,GAAQ,WACpF,IAAIiwB,EAAKK,EACT,OAAOjmB,EAAY9K,MAAM,SAAU6D,GAC/B,OAAQA,EAAGsH,OACP,KAAK,EACD,OAAMnL,KAAKqwB,KAAK/sB,OAAS,GACzBotB,EAAM1wB,KAAKqwB,KAAKzkB,MAChB5L,KAAKmwB,OAAOO,GACZ1wB,KAAKswB,OAAOM,IAAIF,GAChB9pB,EAAQ8pB,GACD,CAAC,EAAa,IALe,CAAC,EAAa,GAMtD,KAAK,EAED,OADA7sB,EAAGwH,KAAKpI,KAAK,CAAC,EAAG,EAAG,CAAE,IACf,CAAC,EAAajD,KAAKkwB,QAAQjZ,IACtC,KAAK,EAID,OAHAyZ,EAAM7sB,EAAGuH,OACTpL,KAAKswB,OAAOM,IAAIF,GAChB9pB,EAAQ8pB,GACD,CAAC,EAAa,GACzB,KAAK,EAGD,OAFAK,EAAUltB,EAAGuH,OACbvE,EAAOkqB,GACA,CAAC,EAAa,GACzB,KAAK,EAAG,MAAO,CAAC,GAExB,GACJ,GAAI,GACR,EAKAd,EAAW/vB,UAAU8wB,QAAU,SAAUN,GACjC1wB,KAAKswB,OAAOpW,IAAIwW,KAChB1wB,KAAKswB,OAAOxF,OAAO4F,GACnB1wB,KAAKqwB,KAAKptB,KAAKytB,GAEvB,EAIAT,EAAW/vB,UAAUiK,MAAQ,WACzBnK,KAAKqwB,KAAK/sB,OAAS,EACnBtD,KAAKswB,OAAOnmB,OAChB,EACO8lB,CACX,CA5J+B,GA6J/B7xB,EAAQ6xB,WAAaA,C,eC1MrB/xB,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQ6yB,WACR,SAAoBC,EAAMnhB,EAAOohB,GAE7B,QADiB,IAAbA,IAAuBA,GAAW,GAClCA,EAAU,CAEV,IADA,IAAIppB,EAAS,GACJhE,EAAM,EAAGA,EAAMgM,EAAOhM,IAAO,CAClC,IAAIG,EAASzE,KAAKwE,MAAMxE,KAAKyE,UAAYgtB,EAAK5tB,OAAS,IACvDyE,EAAO9E,KAAKiuB,EAAKhtB,GACrB,CACA,OAAO6D,CACX,CAEI,OAGR,SAAiBmpB,EAAME,QACA,IAAfA,IAAyBA,GAAa,GACtCA,IACAF,EAAOA,EAAK/kB,UAEhB,IAAK,IAAI/H,EAAM8sB,EAAK5tB,OAAS,EAAGc,EAAM,EAAGA,IAAO,CAC5C,IAAIF,EAASzE,KAAKwE,MAAMxE,KAAKyE,SAAWE,GACpCitB,EAAOH,EAAK9sB,GAChB8sB,EAAK9sB,GAAO8sB,EAAKhtB,GACjBgtB,EAAKhtB,GAAUmtB,CACnB,CACA,OAAOH,CACX,CAfeI,CAAQJ,GAAM1tB,MAAM,EAAGuM,EAEtC,C,qBCfA,IAAI3N,EAAUpC,MAAQA,KAAKoC,QAAW,SAAUC,EAAGC,GAC/C,IAAIC,EAAsB,mBAAXC,QAAyBH,EAAEG,OAAOC,UACjD,IAAKF,EAAG,OAAOF,EACf,IAAmBK,EAAYC,EAA3BC,EAAIL,EAAEM,KAAKR,GAAOS,EAAK,GAC3B,IACI,WAAc,IAANR,GAAgBA,KAAM,MAAQI,EAAIE,EAAEG,QAAQC,MAAMF,EAAGG,KAAKP,EAAErE,MACxE,CACA,MAAO6E,GAASP,EAAI,CAAEO,MAAOA,EAAS,CACtC,QACI,IACQR,IAAMA,EAAEM,OAAST,EAAIK,EAAU,SAAIL,EAAEM,KAAKD,EAClD,CACA,QAAU,GAAID,EAAG,MAAMA,EAAEO,KAAO,CACpC,CACA,OAAOJ,CACX,EACA5E,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQwQ,aAAU,EAClB,IAAI2iB,EAAyB,WACzB,SAASA,IAELvxB,KAAKwxB,UAAY,CAAC,CACtB,CA2HA,OArHAD,EAAQrxB,UAAU4kB,QAAU,SAAU/X,EAAK1O,GACvC,IACI,IAAIozB,EAAcF,EAAQG,OAAS3kB,EAC/B4kB,OAAc,EACdC,SAAmBvzB,EAET,OAAVA,GAAgC,WAAduzB,GAAwC,WAAdA,GAAwC,YAAdA,GAAyC,WAAdA,GAAwC,cAAdA,EAC3HD,EAActzB,EAEK,WAAduzB,EACLD,EAAcpN,KAAKE,UAAUpmB,GAG7B0I,QAAQ7D,MAAM,IAASiJ,OAAOY,EAAK,4BAAiHZ,OAAO9N,IAO/J2B,KAAKwxB,UAAUzkB,GAAO,CAAEyB,KAAMojB,EAAWlc,KAAMic,GAC/C,IAAIE,EAAeD,EAAYL,EAAQO,eAAiBH,EACxD9M,aAAaC,QAAQ2M,EAAaI,EACtC,CACA,MAAOlvB,GACH,MAAIA,aAAaovB,eAA4B,KAAXpvB,EAAEqvB,MAA0B,OAAXrvB,EAAEqvB,MAA4B,uBAAXrvB,EAAE2b,MAA4C,+BAAX3b,EAAE2b,MACjG,IAAIzU,MAAM,wBAGVlH,CAEd,CACJ,EAOA4uB,EAAQrxB,UAAU2O,QAAU,SAAU9B,EAAKvF,GACvC,GAAItJ,OAAOgC,UAAUgF,eAAerC,KAAK7C,KAAKwxB,UAAWzkB,GAAM,CAC3D,IAAIlJ,EAAK7D,KAAKwxB,UAAUzkB,GAAMyB,EAAO3K,EAAG2K,KAAMkH,EAAO7R,EAAG6R,KACxD,OAAa,OAATA,EACOA,EAGE,WAATlH,EACO+V,KAAKC,MAAM9O,GAGfA,CACX,CAGI,IAAI+b,EAAcF,EAAQG,OAAS3kB,EAC/BklB,EAAOpN,aAAahW,QAAQ4iB,GAC5B/lB,OAAI,EACR,GAAa,OAATumB,GAA0B,KAATA,EAAa,CAC9B,IAAIlT,EAASkT,EAAKjP,MAAMuO,EAAQO,gBAChC,GAAwE,KAAnE/S,aAAuC,EAASA,EAAOzb,QAiCxD,MAAM,IAAIuG,MAAM,iCAAuGsC,OAAO4S,aAAuC,EAASA,EAAOzb,OAAQ,MAhC7L,IAAI+E,EAAKjG,EAAO2c,EAAQ,GAAI6S,EAAYvpB,EAAG,GAAIhK,EAAQgK,EAAG,GACtDspB,OAAc,EAClB,OAAQC,GACJ,IAAK,SAEDD,EADAjmB,EAAIrN,EAEJ,MACJ,IAAK,SACL,IAAK,SAEDszB,EADAjmB,GAAKrN,EAEL,MACJ,IAAK,UAEDszB,EADAjmB,EAAI6Y,KAAKC,MAAMnmB,GAEf,MACJ,IAAK,YAEDszB,EADAjmB,OAAIhL,EAEJ,MACJ,IAAK,SACDgL,EAAI6Y,KAAKC,MAAMnmB,GAEfszB,EAActzB,EACd,MACJ,QACI0I,QAAQ7D,MAAM,IAASiJ,OAAOY,EAAK,yBAG3C/M,KAAKwxB,UAAUzkB,GAAO,CAAEyB,KAAMojB,EAAWlc,KAAMic,EAKvD,MAEIjmB,EAAIlE,EAER,OAAOkE,CAEf,EAKA6lB,EAAQrxB,UAAUgyB,OAAS,SAAUnlB,GACjC,IAAI0kB,EAAcF,EAAQG,OAAS3kB,EACnC8X,aAAasN,WAAWV,EAC5B,EAIAF,EAAQrxB,UAAUiK,MAAQ,WACtB0a,aAAa1a,OACjB,EACAonB,EAAQG,OAAS,eACjBH,EAAQO,eAAiB,MAClBP,CACX,CAhI4B,GAiI5BnzB,EAAQwQ,QAAU,IAAI2iB,C,eCnJtBrzB,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQg0B,UAAO,EAKf,IAAIC,EAAsB,WACtB,SAASA,IACLryB,KAAKsyB,SAAW,EACpB,CAsDA,OAhDAD,EAAKnyB,UAAUqyB,IAAM,SAAUC,GAE3B,IADA,IAAIvb,EAAO,GACFJ,EAAK,EAAGA,EAAK5K,UAAU3I,OAAQuT,IACpCI,EAAKJ,EAAK,GAAK5K,UAAU4K,GAGxBa,OAA4B,sBAC7BA,OAAO+a,oBAAsB,SAAU7f,GACnC,IAAI1F,EAAYC,KAAKC,MACrB,OAAOrL,YAAW,WACd6Q,EAAS,CACL8f,YAAY,EACZC,cAAe,WACX,OAAOlzB,KAAKC,IAAI,EAAG,IAAMyN,KAAKC,MAAQF,GAC1C,GAER,GACJ,EACAwK,OAAOkb,mBAAqB,SAAUnX,GAClCjV,aAAaiV,EACjB,GAEJzb,KAAKsyB,SAASrvB,KAAK,CACf4vB,QAASL,EACTvb,KAAMA,IAELjX,KAAK8yB,aACN9yB,KAAK8yB,WAAapb,OAAO+a,oBAAoBzyB,KAAK+yB,oBAAoBxS,KAAKvgB,MAAO,CAAEgO,QAAS,KAErG,EACAqkB,EAAKnyB,UAAU6yB,oBAAsB,SAAUC,GAE3C,MAAQA,EAASL,gBAAkB,GAAKK,EAASN,aAAe1yB,KAAKsyB,SAAShvB,OAAS,GAAG,CACtF,IAAI2vB,EAASjzB,KAAKsyB,SAAS7tB,QAEvBwuB,EAAOJ,SACPI,EAAOJ,QAAQhoB,MAAM7K,KAAMizB,EAAOhc,KAE1C,CAEIjX,KAAKsyB,SAAShvB,OAAS,EACvBtD,KAAK8yB,WAAapb,OAAO+a,oBAAoBzyB,KAAK+yB,oBAAoBxS,KAAKvgB,MAAO,CAAEgO,QAAS,MAG7F4kB,mBAAmB5yB,KAAK8yB,YACxB9yB,KAAK8yB,WAAa,EAE1B,EACOT,CACX,CA1DyB,GA2DzBj0B,EAAQg0B,KAAO,IAAIC,C,cCjEnBn0B,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQ4P,QASR,SAAiBzP,EAAMqU,EAAU5E,GAE7B,IADA,IAAIiJ,EAAO,GACFJ,EAAK,EAAGA,EAAK5K,UAAU3I,OAAQuT,IACpCI,EAAKJ,EAAK,GAAK5K,UAAU4K,GAE7B,IAAI4E,EAAK1Z,YAAW,WAChByE,aAAaiV,GACRld,GAASG,GAAGirB,QAAQprB,GAAM,KAG/BqU,SAAoDA,EAAS/H,MAAM7K,KAAM,CAACiX,IAC9E,GAAGjJ,EAASiJ,GACZ,OAAOwE,CACX,C,eCvBAvd,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQ80B,UAMR,WACI,IAAIzyB,EAAQT,KACZ,OAAO,IAAI4F,SAAQ,SAAUgB,GACzBnG,EAAM0yB,WAAU,WACZvsB,EAAQ,KACZ,GAAG,KACP,GACJ,EAZAxI,EAAQ+0B,UAaR,SAAmB3Z,EAAM4Z,GACrB10B,GAAGwS,SAASmiB,KAAK30B,GAAG40B,SAASC,mBAAoB/Z,EAAM4Z,EAC3D,C,mECjBA,IAAI/oB,UAAarK,MAAQA,KAAKqK,WAAc,SAAUC,EAASC,EAAYC,EAAGC,GAE1E,OAAO,IAAKD,IAAMA,EAAI5E,WAAU,SAAUgB,EAASC,GAC/C,SAAS6D,EAAUrM,GAAS,IAAMsM,EAAKF,EAAU1H,KAAK1E,GAAS,CAAE,MAAOsE,GAAKkE,EAAOlE,EAAI,CAAE,CAC1F,SAASiI,EAASvM,GAAS,IAAMsM,EAAKF,EAAiB,MAAEpM,GAAS,CAAE,MAAOsE,GAAKkE,EAAOlE,EAAI,CAAE,CAC7F,SAASgI,EAAK5C,GAJlB,IAAe1J,EAIa0J,EAAO/E,KAAO4D,EAAQmB,EAAO1J,QAJ1CA,EAIyD0J,EAAO1J,MAJhDA,aAAiBmM,EAAInM,EAAQ,IAAImM,GAAE,SAAU5D,GAAWA,EAAQvI,EAAQ,KAIjByJ,KAAK4C,EAAWE,EAAW,CAC7GD,GAAMF,EAAYA,EAAUI,MAAMP,EAASC,GAAc,KAAKxH,OAClE,GACJ,EACI+H,YAAe9K,MAAQA,KAAK8K,aAAgB,SAAUR,EAASS,GAC/D,IAAsGC,EAAGC,EAAGxD,EAAxGyD,EAAI,CAAEC,MAAO,EAAGC,KAAM,WAAa,GAAW,EAAP3D,EAAE,GAAQ,MAAMA,EAAE,GAAI,OAAOA,EAAE,EAAI,EAAG4D,KAAM,GAAIC,IAAK,IAAeC,EAAIrN,OAAOqH,QAA4B,mBAAbiG,SAA0BA,SAAWtN,QAAQgC,WACtL,OAAOqL,EAAExI,KAAO0I,EAAK,GAAIF,EAAS,MAAIE,EAAK,GAAIF,EAAU,OAAIE,EAAK,GAAsB,mBAAXjJ,SAA0B+I,EAAE/I,OAAOC,UAAY,WAAa,OAAOzC,IAAM,GAAIuL,EAC1J,SAASE,EAAKnJ,GAAK,OAAO,SAAUoJ,GAAK,OACzC,SAAcC,GACV,GAAIX,EAAG,MAAM,IAAI7F,UAAU,mCAC3B,KAAOoG,IAAMA,EAAI,EAAGI,EAAG,KAAOT,EAAI,IAAKA,OACnC,GAAIF,EAAI,EAAGC,IAAMxD,EAAY,EAARkE,EAAG,GAASV,EAAU,OAAIU,EAAG,GAAKV,EAAS,SAAOxD,EAAIwD,EAAU,SAAMxD,EAAE5E,KAAKoI,GAAI,GAAKA,EAAElI,SAAW0E,EAAIA,EAAE5E,KAAKoI,EAAGU,EAAG,KAAK3I,KAAM,OAAOyE,EAE3J,OADIwD,EAAI,EAAGxD,IAAGkE,EAAK,CAAS,EAARA,EAAG,GAAQlE,EAAEpJ,QACzBsN,EAAG,IACP,KAAK,EAAG,KAAK,EAAGlE,EAAIkE,EAAI,MACxB,KAAK,EAAc,OAAXT,EAAEC,QAAgB,CAAE9M,MAAOsN,EAAG,GAAI3I,MAAM,GAChD,KAAK,EAAGkI,EAAEC,QAASF,EAAIU,EAAG,GAAIA,EAAK,CAAC,GAAI,SACxC,KAAK,EAAGA,EAAKT,EAAEI,IAAIM,MAAOV,EAAEG,KAAKO,MAAO,SACxC,QACI,MAAkBnE,GAAZA,EAAIyD,EAAEG,MAAY/H,OAAS,GAAKmE,EAAEA,EAAEnE,OAAS,KAAkB,IAAVqI,EAAG,IAAsB,IAAVA,EAAG,IAAW,CAAET,EAAI,EAAG,QAAU,CAC3G,GAAc,IAAVS,EAAG,MAAclE,GAAMkE,EAAG,GAAKlE,EAAE,IAAMkE,EAAG,GAAKlE,EAAE,IAAM,CAAEyD,EAAEC,MAAQQ,EAAG,GAAI,KAAO,CACrF,GAAc,IAAVA,EAAG,IAAYT,EAAEC,MAAQ1D,EAAE,GAAI,CAAEyD,EAAEC,MAAQ1D,EAAE,GAAIA,EAAIkE,EAAI,KAAO,CACpE,GAAIlE,GAAKyD,EAAEC,MAAQ1D,EAAE,GAAI,CAAEyD,EAAEC,MAAQ1D,EAAE,GAAIyD,EAAEI,IAAIrI,KAAK0I,GAAK,KAAO,CAC9DlE,EAAE,IAAIyD,EAAEI,IAAIM,MAChBV,EAAEG,KAAKO,MAAO,SAEtBD,EAAKZ,EAAKlI,KAAKyH,EAASY,EAC5B,CAAE,MAAOvI,GAAKgJ,EAAK,CAAC,EAAGhJ,GAAIsI,EAAI,CAAG,CAAE,QAAUD,EAAIvD,EAAI,CAAG,CACzD,GAAY,EAARkE,EAAG,GAAQ,MAAMA,EAAG,GAAI,MAAO,CAAEtN,MAAOsN,EAAG,GAAKA,EAAG,QAAK,EAAQ3I,MAAM,EAC9E,CAtBgD2H,CAAK,CAACrI,EAAGoJ,GAAK,CAAG,CAuBrE,EACAxN,OAAOC,eAAeC,QAAS,aAAc,CAAEC,OAAO,IACtDD,QAAQ8e,WAAQ,EAChB,IAAIjC,kBAAoB,oBAAQ,MAe5BiC,MAAuB,WACvB,SAASA,QACLld,KAAKyS,OAAS,CAAC,EACfzS,KAAKwzB,OAAS,CAAC,EACfxzB,KAAKqvB,SAAU,EACfrvB,KAAKyzB,WAAa,EACtB,CA8KA,OA7KAv1B,OAAOC,eAAe+e,MAAO,wBAAyB,CAIlDnX,IAAK,WACD,OAAOmX,MAAMwW,sBACjB,EACA1tB,YAAY,EACZC,cAAc,IAElB/H,OAAOC,eAAe+e,MAAMhd,UAAW,KAAM,CAIzC6F,IAAK,WACD,OAAO/F,KAAK2zB,GAChB,EACA3tB,YAAY,EACZC,cAAc,IAElB/H,OAAOC,eAAe+e,MAAMhd,UAAW,YAAa,CAKhD6F,IAAK,WACD,OAAO/F,KAAK4zB,UAChB,EACA5tB,YAAY,EACZC,cAAc,IAElB/H,OAAOC,eAAe+e,MAAMhd,UAAW,YAAa,CAIhD6F,IAAK,WACD,OAAOwR,aAAavX,KAAKsF,YAC7B,EACAU,YAAY,EACZC,cAAc,IAElB/H,OAAOC,eAAe+e,MAAMhd,UAAW,QAAS,CAI5C6F,IAAK,WACD,OAAO/F,KAAKyS,MAChB,EACAzM,YAAY,EACZC,cAAc,IAMlBiX,MAAMhd,UAAUwV,KAAO,WACnB,MAAO,CAAC,CACZ,EACAxX,OAAOC,eAAe+e,MAAMhd,UAAW,QAAS,CAI5C6F,IAAK,WACD,OAAO/F,KAAKwzB,MAChB,EACAxtB,YAAY,EACZC,cAAc,IAElB/H,OAAOC,eAAe+e,MAAMhd,UAAW,SAAU,CAI7C6F,IAAK,WACD,IAAIspB,QAAUrvB,KAAKob,OAASld,OAAOqd,KAAKvb,KAAKob,OAAO9X,OAAS,EAC7D,IAAKtD,KAAK4zB,WACN,OAAOvE,QAEX,IACI,OAAOA,SAAWwE,KAAK7zB,KAAK4zB,WAChC,CACA,MAAO1wB,GAEH,OADA6D,QAAQ7D,MAAM,gBAAkFA,EAAM4wB,OAC/FzE,OACX,CACJ,EAIA/d,IAAK,SAAUjT,GACX,IAAIwF,EACA7D,KAAKqvB,UAAYhxB,IACjB2B,KAAKqvB,QAAUhxB,EACXA,GAEA2B,KAAKwzB,OAAyF,QAA/E3vB,EAAKoX,kBAAkBC,gBAAgBC,mBAAmBnb,KAAK4b,kBAA+B,IAAP/X,OAAgB,EAASA,EAAGwX,MAClIrb,KAAK+zB,aAGL/zB,KAAKwzB,OAAS,CAAC,EACfxzB,KAAKg0B,aAGjB,EACAhuB,YAAY,EACZC,cAAc,IASlBiX,MAAMhd,UAAUwa,kBAAoB,WAChC,OAAO,IACX,EAKAwC,MAAMhd,UAAU+zB,aAAe,WAC3B,OAAOj0B,KAAKyzB,UAChB,EAIAvW,MAAMhd,UAAUsa,SAAW,WAC3B,EAIA0C,MAAMhd,UAAU6zB,SAAW,WAC3B,EAIA7W,MAAMhd,UAAU8zB,UAAY,WAC5B,EAOA9W,MAAMhd,UAAUwS,SAAW,SAAUrF,GACjC,IAAK,IAAIN,KAAOM,EAAO,CACnB,IAAIhP,EAAQgP,EAAMN,GAClB/M,KAAKyS,OAAO1F,GAAO1O,CACvB,CACJ,EAKA6e,MAAMhd,UAAUkc,SAAW,SAAUpK,GACjC,OAAO3H,UAAUrK,UAAM,OAAQ,GAAQ,WACnC,OAAO8K,YAAY9K,MAAM,SAAU6D,GAC/B,MAAO,CAAC,EACZ,GACJ,GACJ,EAMAqZ,MAAMgX,cAAgB,SAAUC,EAAkBC,EAAWC,GACpDr0B,KAAK0zB,uBAAuBxZ,IAAIia,IACjCn0B,KAAK0zB,uBAAuBpiB,IAAI6iB,EAAkB,IAEtDn0B,KAAK0zB,uBAAuB3tB,IAAIouB,GAAkBlxB,KAAK,CAAEmxB,UAAWA,EAAWC,QAASA,GAC5F,EACAnX,MAAMwW,uBAAyB,IAAI7hB,IAC5BqL,KACX,CArL0B,GAsL1B9e,QAAQ8e,MAAQA,K,iBC3OhBhf,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQ8c,qBAAkB,EAC1B,IAAI9M,EAAY,EAAQ,MA4DpBkmB,EAAiC,WACjC,SAASA,IASLt0B,KAAKu0B,QAAU,CAAC,EAIhBv0B,KAAKw0B,YAAc,CAAC,EASpBx0B,KAAKy0B,WAAa,CAAC,EAInBz0B,KAAK00B,iBAAmB,CAAC,EAIzB10B,KAAK20B,oBAAsB,CAAC,EAS5B30B,KAAK40B,qBAAuB,CAAC,EAC7B50B,KAAK60B,2BAA6B,CAAC,CACvC,CAmLA,OAlLA32B,OAAOC,eAAem2B,EAAgBp0B,UAAW,qBAAsB,CAInE6F,IAAK,WACD,OAAO/F,KAAK20B,mBAChB,EACA3uB,YAAY,EACZC,cAAc,IAElBquB,EAAgBp0B,UAAU40B,WAAa,SAAUC,GAC7C,GAAKA,EAAL,CAIA,IAAIC,EAAUD,EAAIC,QACdT,EAAUv0B,KAAKu0B,QACfC,EAAcx0B,KAAKw0B,YAMvB,IAAK,IAAI5Y,KAAaoZ,EAElB,IADA,IAAIC,EAAoBD,EAAQpZ,GACvB7X,EAAM,EAAGA,EAAMkxB,EAAkB3xB,OAAQS,IAAO,CACrD,IAAImxB,EAAgBD,EAAkBlxB,GAClC0X,EAAKyZ,EAAczZ,GAAIJ,EAAQ6Z,EAAc7Z,MAC5CkZ,EAAQ3Y,KACT2Y,EAAQ3Y,GAAa,CAAC,GAE1B2Y,EAAQ3Y,GAAWH,GAAMJ,EACzBmZ,EAAY/Y,GAAM,CAAEG,UAAWA,EAAWP,MAAOA,EACrD,CAnBJ,MAFItU,QAAQ7D,MAAM,cAuBtB,EAEAoxB,EAAgBp0B,UAAUi1B,gBAAkB,SAAU30B,GAMlDR,KAAKo1B,iBAAmB50B,EACxB,IAAIqD,EAAK7D,KAAKo1B,iBAAkBC,EAAWxxB,EAAGwxB,SAAUC,EAAOzxB,EAAGyxB,KAClElnB,EAAUQ,QAAQkW,QAAQ,aAAcwQ,GACxC,IAAK,IAAIlxB,EAAM,EAAGA,EAAMixB,EAAS/xB,OAAQc,IAAO,CAC5C,IAAIqX,EAAK4Z,EAASjxB,GAAKqX,GACvBzb,KAAKy0B,WAAWhZ,GAAM4Z,EAASjxB,EACnC,CACJ,EAEAkwB,EAAgBp0B,UAAUq1B,mBAAqB,SAAU/0B,GAOrD,IADA,IAAIg1B,EAAiBh1B,EAAO60B,SACnBrZ,EAAM,EAAGA,EAAMwZ,EAAelyB,OAAQ0Y,IAAO,CAClD,IAAIP,EAAK+Z,EAAexZ,GAAKP,GAC7Bzb,KAAK40B,qBAAqBnZ,GAAM+Z,EAAexZ,EACnD,CACJ,EAaAsY,EAAgBp0B,UAAUu1B,+BAAiC,WACvD,IAAIjB,EAAcx0B,KAAKw0B,YACnBa,EAAWr1B,KAAKo1B,iBAAiBC,SACjCjmB,WACArI,QAAQuR,IAAI,YAAa,iCAAkC+c,GAC3DjnB,EAAUQ,QAAQkW,QAAQ,iBAAkBuQ,IAEhD,IAAK,IAAIjY,EAAM,EAAGA,EAAMiY,EAAS/xB,OAAQ8Z,IAAO,CAC5C,IAAI3B,EAAK4Z,EAASjY,GAAK3B,GACnBia,EAAclB,EAAY/Y,GAC9B,GAAKia,EAAL,CAMA,IAAI9Z,EAAY8Z,EAAY9Z,UAAWP,EAAQqa,EAAYra,MACvDP,EAAiBc,EAAU+Z,OAAO,GAAGC,cAAgBha,EAAUpY,MAAM,GAAK,QAC9ExD,KAAK20B,oBAAoB7Z,GAAkB,CAAEW,GAAIA,EAAIJ,MAAOA,EAH5D,MAJQjM,UACArI,QAAQ7D,MAAM,sBAA6EiJ,OAAOsP,GAO9G,CACJ,EAEA6Y,EAAgBp0B,UAAU21B,8BAAgC,SAAUC,GAGhE,IAFA,IAAItB,EAAcx0B,KAAKw0B,YAEdrH,EAAM,EAAGA,EAAM2I,EAAUxyB,OAAQ6pB,IAAO,CAC7C,IAAI1R,EAAKqa,EAAU3I,GACfuI,EAAclB,EAAY/Y,GAC9B,GAAKia,EAAL,CAMA,IAAI9Z,EAAY8Z,EAAY9Z,UAAWP,EAAQqa,EAAYra,MACvDP,EAAiBc,EAAU+Z,OAAO,GAAGC,cAAgBha,EAAUpY,MAAM,GAAK,QAC9ExD,KAAK20B,oBAAoB7Z,GAAkB,CAAEW,GAAIA,EAAIJ,MAAOA,EAH5D,MAJQjM,UACArI,QAAQ7D,MAAM,sBAA6EiJ,OAAOsP,GAO9G,CACIrM,UACArI,QAAQuR,IAAI,cAAe,iCAAkCtY,KAAK20B,oBAE1E,EAEAL,EAAgBp0B,UAAU61B,iBAAmB,SAAUC,GAEnD,IADA,IAAIxB,EAAcx0B,KAAKw0B,YACdjH,EAAM,EAAGA,EAAMyI,EAAiB1yB,OAAQiqB,IAAO,CACpD,IAAI9R,EAAKua,EAAiBzI,GACtBmI,EAAclB,EAAY/Y,GAC9B,GAAKia,EAAL,CAMA,IAAI9Z,EAAY8Z,EAAY9Z,UAAWP,EAAQqa,EAAYra,MACvDP,EAAiBc,EAAU+Z,OAAO,GAAGC,cAAgBha,EAAUpY,MAAM,GAAK,QAC9ExD,KAAK20B,oBAAoB7Z,GAAkB,CAAEW,GAAIA,EAAIJ,MAAOA,GAC5DtU,QAAQuR,IAAI,UAA6CnM,OAAOsP,GAJhE,MAJQrM,UACArI,QAAQ7D,MAAM,6BAAkHiJ,OAAOsP,GAQnJ,CACJ,EAOA6Y,EAAgBp0B,UAAU+1B,UAAY,SAAUra,EAAWH,GACvD,GAAI1N,MAAM0N,GAAK,CACX,GAAIzb,KAAK00B,iBAAiB9Y,GACtB,OAAO5b,KAAK00B,iBAAiB9Y,GAEjC,IAAIsa,EAAal2B,KAAKu0B,QAAQ3Y,GAC9B,IAAK,IAAIua,KAAOD,EACZ,GAAIl2B,KAAKy0B,WAAW0B,GAEhB,OADAn2B,KAAK00B,iBAAiB9Y,GAAasa,EAAWC,GACvCn2B,KAAK00B,iBAAiB9Y,EAGzC,CACA,OAAO5b,KAAKu0B,QAAQ3Y,GAAWH,EACnC,EAOA6Y,EAAgBp0B,UAAUk2B,oBAAsB,SAAUrpB,EAAK0O,GAC3D,GAAI1N,MAAM0N,GAAK,CACX,GAAIzb,KAAK60B,2BAA2B9nB,GAChC,OAAO/M,KAAK60B,2BAA2B9nB,GAE3C,IAAImpB,EAAal2B,KAAKu0B,QAAQxnB,GAC9B,IAAK,IAAIopB,KAAOD,EACZ,GAAIl2B,KAAK40B,qBAAqBuB,GAE1B,OADAn2B,KAAK60B,2BAA2B9nB,GAAOmpB,EAAWC,GAC3Cn2B,KAAK60B,2BAA2B9nB,EAGnD,CACA,OAAO/M,KAAKu0B,QAAQxnB,GAAK0O,EAC7B,EACO6Y,CACX,CA9NoC,GA+NpCl2B,EAAQ8c,gBAAkB,IAAIoZ,C,cC7Q9B,SAAS+B,EAASjhB,GACd,MAAuB,iBAARA,CACnB,CAoDA,SAAS3G,EAAYiiB,GACjB,YAAuB,IAARA,CACnB,CAYA,SAAS4F,EAAkB5F,GACvB,OAAQjiB,EAAYiiB,IAAgB,OAARA,CAChC,CAtFAxyB,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQi4B,SAAWA,EACnBj4B,EAAQm4B,cAoBR,SAAuBl4B,GACnB,OAAO2G,MAAM2a,QAAQthB,IAAUA,EAAMm4B,OAAM,SAAUC,GAAQ,OAAOJ,EAASI,EAAO,GACxF,EArBAr4B,EAAQs4B,SAyBR,SAAkBhG,GAId,QAAsB,iBAARA,GACC,OAARA,GACC1rB,MAAM2a,QAAQ+Q,IACbA,aAAeiG,QACfjG,aAAevjB,KAC5B,EAjCA/O,EAAQw4B,aAqCR,SAAsBlG,GAClB,IAAImG,EAAa34B,OAAO44B,eAAeC,YACvC,MAAsB,iBAARrG,GACPA,aAAemG,CAC1B,EAxCAz4B,EAAQ44B,SA6CR,SAAkBtG,GACd,MAAuB,iBAARA,IAAqB3iB,MAAM2iB,EAC9C,EA9CAtyB,EAAQ64B,WAmDR,SAAoBvG,GAChB,QAASA,GAAuC,mBAAzBA,EAAIluB,OAAOC,SACtC,EApDArE,EAAQ84B,UAwDR,SAAmBxG,GACf,OAAgB,IAARA,IAAwB,IAARA,CAC5B,EAzDAtyB,EAAQqQ,YAAcA,EACtBrQ,EAAQ+4B,UAoER,SAAmBC,GACf,OAAQd,EAAkBc,EAC9B,EArEAh5B,EAAQk4B,kBAAoBA,EAC5Bl4B,EAAQi5B,WAgFR,SAAoB3G,GAChB,MAAuB,mBAARA,CACnB,EAjFAtyB,EAAQk5B,QAsFR,SAAiB5hB,GAEb,OADgBxX,OAAOgC,UAAU6S,SAASlQ,KAAK6S,IAE3C,IAAK,kBAUL,QACI,MAAO,SAVX,IAAK,kBAAmB,MAAO,SAC/B,IAAK,kBAAmB,MAAO,SAC/B,IAAK,mBAAoB,MAAO,UAChC,IAAK,iBAAkB,MAAO,QAC9B,IAAK,qBAAsB,MAAO,YAClC,IAAK,gBAAiB,MAAO,OAC7B,IAAK,gBAAiB,MAAO,OAC7B,IAAK,kBAAmB,MAAO,SAC/B,IAAK,oBAAqB,MAAO,WAIzC,C,uBClHA,IAAIrL,EAAarK,MAAQA,KAAKqK,WAAc,SAAUC,EAASC,EAAYC,EAAGC,GAE1E,OAAO,IAAKD,IAAMA,EAAI5E,WAAU,SAAUgB,EAASC,GAC/C,SAAS6D,EAAUrM,GAAS,IAAMsM,EAAKF,EAAU1H,KAAK1E,GAAS,CAAE,MAAOsE,GAAKkE,EAAOlE,EAAI,CAAE,CAC1F,SAASiI,EAASvM,GAAS,IAAMsM,EAAKF,EAAiB,MAAEpM,GAAS,CAAE,MAAOsE,GAAKkE,EAAOlE,EAAI,CAAE,CAC7F,SAASgI,EAAK5C,GAJlB,IAAe1J,EAIa0J,EAAO/E,KAAO4D,EAAQmB,EAAO1J,QAJ1CA,EAIyD0J,EAAO1J,MAJhDA,aAAiBmM,EAAInM,EAAQ,IAAImM,GAAE,SAAU5D,GAAWA,EAAQvI,EAAQ,KAIjByJ,KAAK4C,EAAWE,EAAW,CAC7GD,GAAMF,EAAYA,EAAUI,MAAMP,EAASC,GAAc,KAAKxH,OAClE,GACJ,EACI+H,EAAe9K,MAAQA,KAAK8K,aAAgB,SAAUR,EAASS,GAC/D,IAAsGC,EAAGC,EAAGxD,EAAxGyD,EAAI,CAAEC,MAAO,EAAGC,KAAM,WAAa,GAAW,EAAP3D,EAAE,GAAQ,MAAMA,EAAE,GAAI,OAAOA,EAAE,EAAI,EAAG4D,KAAM,GAAIC,IAAK,IAAeC,EAAIrN,OAAOqH,QAA4B,mBAAbiG,SAA0BA,SAAWtN,QAAQgC,WACtL,OAAOqL,EAAExI,KAAO0I,EAAK,GAAIF,EAAS,MAAIE,EAAK,GAAIF,EAAU,OAAIE,EAAK,GAAsB,mBAAXjJ,SAA0B+I,EAAE/I,OAAOC,UAAY,WAAa,OAAOzC,IAAM,GAAIuL,EAC1J,SAASE,EAAKnJ,GAAK,OAAO,SAAUoJ,GAAK,OACzC,SAAcC,GACV,GAAIX,EAAG,MAAM,IAAI7F,UAAU,mCAC3B,KAAOoG,IAAMA,EAAI,EAAGI,EAAG,KAAOT,EAAI,IAAKA,OACnC,GAAIF,EAAI,EAAGC,IAAMxD,EAAY,EAARkE,EAAG,GAASV,EAAU,OAAIU,EAAG,GAAKV,EAAS,SAAOxD,EAAIwD,EAAU,SAAMxD,EAAE5E,KAAKoI,GAAI,GAAKA,EAAElI,SAAW0E,EAAIA,EAAE5E,KAAKoI,EAAGU,EAAG,KAAK3I,KAAM,OAAOyE,EAE3J,OADIwD,EAAI,EAAGxD,IAAGkE,EAAK,CAAS,EAARA,EAAG,GAAQlE,EAAEpJ,QACzBsN,EAAG,IACP,KAAK,EAAG,KAAK,EAAGlE,EAAIkE,EAAI,MACxB,KAAK,EAAc,OAAXT,EAAEC,QAAgB,CAAE9M,MAAOsN,EAAG,GAAI3I,MAAM,GAChD,KAAK,EAAGkI,EAAEC,QAASF,EAAIU,EAAG,GAAIA,EAAK,CAAC,GAAI,SACxC,KAAK,EAAGA,EAAKT,EAAEI,IAAIM,MAAOV,EAAEG,KAAKO,MAAO,SACxC,QACI,MAAkBnE,GAAZA,EAAIyD,EAAEG,MAAY/H,OAAS,GAAKmE,EAAEA,EAAEnE,OAAS,KAAkB,IAAVqI,EAAG,IAAsB,IAAVA,EAAG,IAAW,CAAET,EAAI,EAAG,QAAU,CAC3G,GAAc,IAAVS,EAAG,MAAclE,GAAMkE,EAAG,GAAKlE,EAAE,IAAMkE,EAAG,GAAKlE,EAAE,IAAM,CAAEyD,EAAEC,MAAQQ,EAAG,GAAI,KAAO,CACrF,GAAc,IAAVA,EAAG,IAAYT,EAAEC,MAAQ1D,EAAE,GAAI,CAAEyD,EAAEC,MAAQ1D,EAAE,GAAIA,EAAIkE,EAAI,KAAO,CACpE,GAAIlE,GAAKyD,EAAEC,MAAQ1D,EAAE,GAAI,CAAEyD,EAAEC,MAAQ1D,EAAE,GAAIyD,EAAEI,IAAIrI,KAAK0I,GAAK,KAAO,CAC9DlE,EAAE,IAAIyD,EAAEI,IAAIM,MAChBV,EAAEG,KAAKO,MAAO,SAEtBD,EAAKZ,EAAKlI,KAAKyH,EAASY,EAC5B,CAAE,MAAOvI,GAAKgJ,EAAK,CAAC,EAAGhJ,GAAIsI,EAAI,CAAG,CAAE,QAAUD,EAAIvD,EAAI,CAAG,CACzD,GAAY,EAARkE,EAAG,GAAQ,MAAMA,EAAG,GAAI,MAAO,CAAEtN,MAAOsN,EAAG,GAAKA,EAAG,QAAK,EAAQ3I,MAAM,EAC9E,CAtBgD2H,CAAK,CAACrI,EAAGoJ,GAAK,CAAG,CAuBrE,EACAxN,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQm5B,QAAK,EACb,IAAIC,EAAc,EAAQ,MACtB13B,EAAc,EAAQ,MACtBy3B,EAAoB,WACpB,SAASA,IACT,CAuWA,OArWAA,EAAGE,eAAiB,SAAUnpB,GAC1BtO,KAAK03B,SAAWppB,CACpB,EAIAipB,EAAGI,QAAU,WACT,IAAK,IAAI5qB,KAAO/M,KAAK43B,QAAS,CAC1B,IAAIliB,EAAO6hB,EAAGK,QAAQ7qB,GAClB2I,EAAKnX,OACLmX,EAAKnX,KAAKiS,QAAS,EAE3B,CACJ,EAIA+mB,EAAGM,YAAc,SAAUvpB,GACvB,IAAK,IAAIvB,KAAO/M,KAAK43B,QACjB,GAAI7qB,GAAOuB,EAAK,CACZ,IAAIoH,EAAO6hB,EAAGK,QAAQ7qB,GACtB,GAAI2I,EAAKnX,KACL,OAAOmX,EAAKnX,KAAKiS,MAEzB,CAEJ,OAAO,CACX,EAKA+mB,EAAGO,WAAa,SAAUt3B,GACtBR,KAAK+3B,eAAev3B,EAAO8N,MAAO,CACtC,EACAipB,EAAGS,WAAa,SAAU1pB,UACfipB,EAAGK,QAAQtpB,UACXtO,KAAKi4B,YAAY3pB,UACjBtO,KAAK+3B,eAAezpB,EAC/B,EAMAipB,EAAG31B,iBAAmB,SAAU+H,EAAOiJ,GAC9B5S,KAAK8rB,OAAOniB,KACb3J,KAAK8rB,OAAOniB,GAAS,IAEzB3J,KAAK8rB,OAAOniB,GAAO1G,KAAK2P,EAC5B,EACA2kB,EAAGW,oBAAsB,SAAUvuB,EAAOiJ,GACtC,GAAI5S,KAAK8rB,OAAOniB,GAAQ,CACpB,IAAIjC,EAAQ1H,KAAK8rB,OAAOniB,GAAO0L,QAAQzC,IACzB,GAAVlL,GACA1H,KAAK8rB,OAAOniB,GAAOwuB,OAAOzwB,EAAO,EAEzC,CACJ,EAcA6vB,EAAGa,KAAO,SAAU53B,EAAQyP,GACxB,IAAIxP,EAAQT,KACZ,OAAO,IAAI4F,SAAQ,SAAUC,EAAGlD,GAAK,OAAO0H,EAAU5J,OAAO,OAAQ,GAAQ,WACzE,IAAI43B,EAAIloB,EAAOmoB,EAAYC,EAAwBC,EAAcz0B,EAAc00B,EAAWC,EAAWC,EAAiBv0B,EAAcw0B,EAAY5c,EAAc6c,EAC9J,OAAO/tB,EAAY9K,MAAM,SAAU6D,GAC/B,OAAQA,EAAGsH,OACP,KAAK,EASD,GARIiE,UACgB,gBAAhB5O,EAAO8d,MAA0BvX,QAAQuR,IAAI,mBAAoB9X,EAAO8d,MAEvEte,KAAKuR,SAAS/Q,EAAO8N,OACtBtO,KAAKuR,SAAS/Q,EAAO8N,KAAO,IAEhCtO,KAAKuR,SAAS/Q,EAAO8N,KAAKrL,KAAK4C,GAE3B7F,KAAKi4B,YAAYz3B,EAAO8N,KACxB,MAAO,CAAC,GAEZzK,EAAGsH,MAAQ,EACf,KAAK,EAQD,OAPAtH,EAAGwH,KAAKpI,KAAK,CAAC,EAAG,GAAI,CAAE,KACvBgN,EAASA,GAAkBunB,EAAYpN,YACvCiO,OAAK,EAEDd,EAAGK,QAAQp3B,EAAO8N,OAAS5P,GAAGirB,QAAQ4N,EAAGK,QAAQp3B,EAAO8N,KAAK/P,cACtDg5B,EAAGK,QAAQp3B,EAAO8N,KAEvBipB,EAAGK,QAAQp3B,EAAO8N,KAAa,CAAC,EAAa,IACnDtO,KAAKi4B,YAAYz3B,EAAO8N,MAAO,EAC/B6B,OAAQ,EACH3P,EAAOG,WACL,CAAC,EAAab,EAAYmC,UAAU+qB,kBAAkBxsB,EAAOG,WAAYH,EAAO8N,IAAK5P,GAAGiS,SADhE,CAAC,EAAa,IAEjD,KAAK,EAED,OADAR,EAAQtM,EAAGuH,OACJ,CAAC,EAAa,GACzB,KAAK,EAAG,MAAO,CAAC,EAAatL,EAAYmC,UAAU0qB,UAAUnsB,EAAO8N,IAAK5P,GAAGiS,SAC5E,KAAK,EACDR,EAAQtM,EAAGuH,OACXvH,EAAGsH,MAAQ,EACf,KAAK,EAsBD,OArBAktB,EAAK35B,GAAGgS,YAAYP,IACL,WAAI3P,EAMfA,EAAOs4B,MAAQ94B,KAAK+4B,UACpB/4B,KAAKg5B,KAAKh5B,KAAK+4B,UAEfv4B,EAAOs4B,OACP94B,KAAK+4B,SAAWV,GAGhBr4B,KAAK+3B,eAAev3B,EAAO8N,OAC3BtO,KAAKi4B,YAAYz3B,EAAO8N,MAAO,SACxBtO,KAAK+3B,eAAev3B,EAAO8N,MAEtCipB,EAAGK,QAAQp3B,EAAO8N,KAAO,CAAC,EAC1BipB,EAAGK,QAAQp3B,EAAO8N,KAAK/P,KAAO85B,EAC9Bd,EAAGK,QAAQp3B,EAAO8N,KAAK2qB,KAAOz4B,EAAOy4B,KAChCz4B,EAAO04B,MACL,CAAC,EAAap5B,EAAYmC,UAAU0qB,UAAU3sB,KAAK03B,SAAUh5B,GAAGiS,SAD7C,CAAC,EAAa,GAE5C,KAAK,EACD2nB,EAAaz0B,EAAGuH,OAChBmtB,EAAc75B,GAAGgS,YAAY4nB,GAC7BroB,EAAO7O,SAASm3B,GACLvmB,EAIRqmB,EAHCE,EAAYY,GAAGz6B,GAAGqC,KAAKq4B,UAAUC,aAAa,SAAU1vB,GACpD4tB,EAAGyB,KAAKhnB,EACZ,IAEJulB,EAAGK,QAAQp3B,EAAO8N,KAAK4qB,MAAQX,EAC/B10B,EAAGsH,MAAQ,EACf,KAAK,EAkBD,IAjBiB,MAAbktB,EAAGpoB,OACHA,EAAO7O,SAASi3B,GAGZA,EAAGpoB,QAAUA,GACbooB,EAAGiB,UAAUrpB,GAGrBjQ,KAAKi4B,YAAYz3B,EAAO8N,MAAO,EACZ,MAAf9N,EAAOy4B,MAEU,OADjBP,EAAYL,EAAG55B,aAAa+B,EAAOy4B,SAE/BP,EAAYL,EAAGp3B,aAAaT,EAAOy4B,OAI3CT,EAAex4B,KAAK8rB,OAAe,OAC9B/nB,EAAM,EAAGA,GAAOy0B,aAAmD,EAASA,EAAal1B,QAASS,IACzFy0B,EAAaz0B,GACf8G,MAAM7K,KAAM,CAACQ,IAEzB,MAAO,CAAC,EAAa,GACzB,KAAK,EACDR,KAAKi4B,YAAYz3B,EAAO8N,MAAO,EAC/B+pB,EAAKd,EAAGK,QAAQp3B,EAAO8N,KAAK/P,KAMxBiC,EAAOs4B,MAAQ94B,KAAK+4B,UAAYV,IAAOr4B,KAAK+4B,UAC5C/4B,KAAKg5B,KAAKh5B,KAAK+4B,UAEfv4B,EAAOs4B,OACP94B,KAAK+4B,SAAWV,GAEhB73B,EAAO04B,SACPT,EAAYlB,EAAGK,QAAQp3B,EAAO8N,KAAK4qB,OACpBjpB,QACXA,EAAO7O,SAASq3B,IAGxBJ,EAAG7nB,QAAS,EACZ3M,EAAGsH,MAAQ,EACf,KAAK,EAWD,GAVIktB,EAAGpoB,QAAUA,GACbooB,EAAGiB,UAAUrpB,GAEjBsnB,EAAGK,QAAQp3B,EAAO8N,KAAK2B,OAASA,EAChCooB,EAAGkB,gBAAgBtpB,EAAOupB,eACP,MAAfh5B,EAAOy4B,OAEPP,OADAA,EAAYL,EAAG55B,aAAa+B,EAAOy4B,QACmBP,EAAUN,QAEpEO,EAAkB34B,KAAKuR,SAAS/Q,EAAO8N,KAClB,CACjB,IAAKlK,EAAM,EAAGA,EAAMu0B,EAAgBr1B,OAAQc,KACxCwC,EAAU+xB,EAAgBv0B,IAClBi0B,GAGZM,EAAgBr1B,OAAS,CAC7B,CAEA,IADAs1B,EAAa54B,KAAK8rB,OAAa,KAC1B9P,EAAM,EAAGA,GAAO4c,aAA+C,EAASA,EAAWt1B,QAAS0Y,IACnF4c,EAAW5c,GACbnR,MAAM7K,KAAM,CAACQ,IAEzB,MAAO,CAAC,EAAa,IACzB,KAAK,GAID,OAHAq4B,EAAQh1B,EAAGuH,OACXpL,KAAKi4B,YAAYz3B,EAAO8N,MAAO,EAC/B3L,EAAEk2B,GACK,CAAC,EAAa,IACzB,KAAK,GAAI,MAAO,CAAC,GAlFb,IAAW7mB,CAoFvB,GACJ,GAAI,GACR,EAOAulB,EAAGkC,WAAa,SAAUj5B,EAAQk5B,EAASzpB,GACvC,IACI,IAAI0pB,EAAYj7B,GAAGgS,YAAYgpB,GAC1BnC,EAAGK,QAAQp3B,EAAO8N,OACnBipB,EAAGK,QAAQp3B,EAAO8N,KAAO,CAAC,GAE9BipB,EAAGK,QAAQp3B,EAAO8N,KAAK/P,KAAOo7B,EAC9B1pB,EAAO7O,SAASu4B,GAChBpC,EAAGK,QAAQp3B,EAAO8N,KAAK2B,OAASA,EAChC0pB,EAAUJ,gBAAgBI,EAAU1pB,OAAOupB,eAC3CG,EAAUnpB,QAAS,CACvB,CACA,MAAOxH,GACHjC,QAAQ7D,MAAM,+BAAgC8F,EAClD,CACJ,EASAuuB,EAAG94B,aAAe,SAAU+B,EAAQyP,GAChC,OAAO5F,EAAUrK,UAAM,OAAQ,GAAQ,WACnC,IAAI0V,EAAMnX,EAAM6S,EAChB,OAAOtG,EAAY9K,MAAM,SAAU6D,GAC/B,OAAKrD,IAILkV,EAAO1V,KAAK43B,QAAQp3B,EAAO8N,QAEvB/P,EAAOmX,EAAKnX,MAMZiC,EAAO4Q,KACH7S,GACA6S,EAAO7S,EAAKE,aAAa+B,EAAO4Q,MAChCpR,KAAK43B,QAAQp3B,EAAO8N,KAAK8C,KAAOA,EACzB,CAAC,EAAcA,IAGf,CAAC,OAAc1Q,IAI1BqG,QAAQ7D,MAAM,QAAiCiJ,OAAO3L,EAAQ,YACvD,CAAC,OAAcE,MAvBtBqG,QAAQ7D,MAAM,uBACP,CAAC,OAAcxC,GAyB9B,GACJ,GACJ,EAKA62B,EAAGyB,KAAO,SAAUhnB,GAChB,IAAIxR,GAOAA,OALAA,EADAwR,aAAkBtT,GAAG6T,UACZP,EAAOzT,KAAiB,WAGxByT,EAAmB,iBAEW,EAASxR,EAAO8N,MACvDtO,KAAK45B,OAAOp5B,EAEpB,EACA+2B,EAAGqC,OAAS,SAAUp5B,GAClB,IAAIC,EAAQT,KACRq4B,EAAKd,EAAGK,QAAQp3B,EAAO8N,KAC3B,GAAK+pB,EAAL,CAII,GAAmB,MAAf73B,EAAOy4B,KAAc,CACrB,IAAIP,EAAYL,EAAG95B,KAAKE,aAAa+B,EAAOy4B,MAC3B,MAAbP,GACAA,EAAUmB,oBAAsB,WAC5BxB,EAAG95B,KAAKiS,QAAS,EACjB/P,EAAMq5B,cAAct5B,EACxB,EACAk4B,EAAUM,SAGVX,EAAG95B,KAAKiS,QAAS,EACjBxQ,KAAK85B,cAAct5B,GAE3B,MAEI63B,EAAG95B,KAAKiS,QAAS,EACjBxQ,KAAK85B,cAAct5B,GAEnBA,EAAO04B,OACPb,EAAGa,MAAMI,UAAU,KAE3B,CACJ,EACA/B,EAAGuC,cAAgB,SAAUt5B,GAEzB,IADA,IAAIu5B,EAAc/5B,KAAK8rB,OAAc,MAC5B1O,EAAM,EAAGA,GAAO2c,aAAiD,EAASA,EAAYz2B,QAAS8Z,IACtF2c,EAAY3c,GAClBvS,MAAM7K,KAAM,CAACQ,GAE7B,EACA+2B,EAAGtM,UAAY,SAAUJ,GACrBA,EAAM8E,SAASzY,SAAQ,SAAU8iB,GAC7BA,EAAIxpB,QAAS,CACjB,GACJ,EAEA+mB,EAAGK,QAAU,CAAC,EAEdL,EAAGU,YAAc,CAAC,EAElBV,EAAGQ,eAAiB,CAAC,EACrBR,EAAGzL,OAAS,CAAC,EACbyL,EAAGhmB,SAAW,CAAC,EACfgmB,EAAGG,SAAW,sBACPH,CACX,CA1WuB,GA2WvBn5B,EAAQm5B,GAAKA,C,eCnZbr5B,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQ67B,qBAMR,SAA8BC,GAC1B,IAAKC,WAAWC,SACZ,MAAO,GAIX,IAFA,IACIC,EADQF,WAAWC,SAASE,OAAOC,UAAU,GAChCvX,MAAM,KACdjf,EAAM,EAAGA,EAAMs2B,EAAK/2B,OAAQS,IAAO,CACxC,IAAIy2B,EAAOH,EAAKt2B,GAAKif,MAAM,KAC3B,GAAIwX,EAAK,KAAON,EACZ,OAAOO,mBAAmBD,EAAK,GAEvC,CACA,MAAO,EACX,C,iBCpBAt8B,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQs8B,iBAAc,EACtBt8B,EAAQu8B,SA2BR,SAAkBltB,GACd,IAAIuE,EAASvE,EAAOuE,OAAQ4oB,EAAentB,EAAOmtB,aAAcC,EAAMptB,EAAOotB,IAAKjoB,EAAWnF,EAAOmF,SAChGkoB,EAAaC,EAASh1B,IAAIiM,EAAO1M,aAChCw1B,IACDA,EAAa,CAAC,EACdC,EAASzpB,IAAIU,EAAO1M,YAAaw1B,IAIrCA,EADUF,EAAeC,GACPjoB,CACtB,EApCAxU,EAAQ48B,MA8ER,WACI,OAAO,SAAUhpB,EAAQipB,GACrB,IAAIp3B,EACAq3B,EAAQC,IAAWt3B,EAAK,CAAC,GAAMo3B,GAAejpB,EAAOipB,GAAcp3B,GAAKmO,EAAQipB,GACpF/8B,OAAOC,eAAe6T,EAAQipB,EAAa,CACvCl1B,IAAK,WACD,OAAOm1B,EAAMD,EACjB,EACA3pB,IAAK,SAAU8pB,GACXF,EAAMD,GAAeG,CACzB,EACAp1B,YAAY,EACZC,cAAc,GAEtB,CACJ,EA5FA,IAKIy0B,EALAvsB,EAAU,EAAQ,KAClB4sB,EAAW,IAAIlpB,IAyCnB,SAASspB,EAAUnpB,EAAQqpB,EAAaJ,GACpC,KAAK,EAAI9sB,EAAQuoB,UAAU1kB,GACvB,OAAOA,EACX,IAAI6gB,EAAU,CACV9sB,IAAK,SAAUiM,EAAQjF,EAAKuuB,GACxB,IAAIvzB,EAASoK,QAAQpM,IAAIiM,EAAQjF,EAAKuuB,GACtC,OAAO,EAAIntB,EAAQuoB,UAAU3uB,GAAUozB,EAAUpzB,EAAQszB,EAAaJ,GAAelzB,CACzF,EACAuJ,IAAK,SAAUU,EAAQjF,EAAK1O,EAAOi9B,GAC/B,IAAIC,EAAWvpB,EAAOjF,GAClBhF,EAASoK,QAAQb,IAAIU,EAAQjF,EAAK1O,EAAOi9B,GAC7C,GAAIC,IAAal9B,EAAO,CACpB,IAAIm9B,EAAeT,EAASh1B,IAAIs1B,EAAY/1B,aACxCk2B,GACAt9B,OAAOqd,KAAKigB,GAActkB,SAAQ,SAAUnK,GACpCA,EAAIiS,SAASic,IACTO,EAAazuB,IACbyuB,EAAazuB,GAAKA,EAAKwuB,EAAUl9B,EAG7C,KAEA,EAAI8P,EAAQuoB,UAAUr4B,IACtB88B,EAAU98B,EAAOg9B,EAAaJ,EACtC,CACA,OAAOlzB,CACX,GAEJ,OAAO,IAAI0zB,MAAMzpB,EAAQ6gB,EAC7B,EAjEA,SAAW6H,GACPA,EAAsE,yDAAI,2DAC1EA,EAAiC,oBAAI,sBACrCA,EAAgF,mEAAI,oEACvF,CAJD,CAIGA,IAAgBt8B,EAAQs8B,YAAcA,EAAc,CAAC,G,iBCdxDx8B,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQs9B,kBAAe,EACvB,IAAIC,EAAkB,EAAQ,MAC1BD,EAA8B,WAC9B,SAASA,IACT,CAiIA,OAhIAx9B,OAAOC,eAAeu9B,EAAc,SAAU,CAI1C31B,IAAK,WACD,OAAO/F,KAAK47B,OAChB,EACA51B,YAAY,EACZC,cAAc,IAOlBy1B,EAAaG,cAAgB,SAAUC,GACnC,GAAgB,OAAZA,EAAJ,CAGA,IAAIC,EAAaD,EAAQC,WACzB,GAAIA,EAAY,MACyBr7B,IAAjCV,KAAK47B,QAAQ71B,IAAIg2B,IACjB/7B,KAAK47B,QAAQtqB,IAAIyqB,EAAY,IAGvBD,EAAQC,WAAlB,IACIjQ,EAAS9rB,KAAK47B,QAAQ71B,IAAI+1B,EAAQC,YAClCxM,GAAO,EACX,GAAIzD,EAAQ,CACR,IAAK,IAAI/nB,EAAM,EAAGmI,EAAI4f,aAAuC,EAASA,EAAOxoB,OAAQS,EAAMmI,EAAGnI,IAC1F,GAAI+nB,EAAO/nB,GAAKm3B,QAAUY,EAAQZ,MAAO,CACrC3L,GAAO,EACP,KACJ,CAECA,GACDzD,EAAO7oB,KAAK64B,EAEpB,CACJ,CArBA,CAsBJ,EAOAJ,EAAaM,oBAAsB,SAAUryB,GACzC,GAAKA,EAAL,CAGA,IAAIsyB,EAAmBtyB,EAAMuyB,WACzBpQ,EAAS9rB,KAAK47B,QAAQ71B,IAAIk2B,GAC9B,GAAInQ,EACA,IAAK,IAAI1nB,EAAM,EAAG8H,EAAI4f,EAAOxoB,OAAQc,EAAM8H,EAAG9H,IAAO,CACjD,IACI82B,EADUpP,EAAO1nB,GACD82B,MAChBiB,EAAkBjB,EAAMkB,WACxBD,IAAoBR,EAAgBU,WAAWC,QAAUH,IAAoBR,EAAgBY,cAAcH,YAC3GlB,SAA8CA,EAAMsB,eAAe7yB,EAE3E,CAEJ,IAAI+mB,EAAM1wB,KAAKy8B,mBAAmB12B,IAAIk2B,GACtC,GAAIvL,EAGA,GAFiBiL,EAAgBY,cAAcH,aAE5BT,EAAgBU,WAAWC,OAC1C,IAAK,IAAII,KAAMhM,EAAK,CAChB,IAAIjI,EACJ,GADIA,EAAYiI,EAAIgM,GAEhB,IAAK,IAAI1gB,EAAM,EAAGA,EAAMyM,EAAUnlB,OAAQ0Y,KAClCpJ,EAAW6V,EAAUzM,KAErBpJ,EAASjJ,EAIzB,MAIA,GADI8e,EAAYiI,EAAIiL,EAAgBY,cAAcH,YAE9C,IAAK,IAAIhf,EAAM,EAAGA,EAAMqL,EAAUnlB,OAAQ8Z,IAAO,CAC7C,IAAIxK,KAAW6V,EAAUrL,KAErBxK,EAASjJ,EAEjB,CAtCZ,CA0CJ,EAMA+xB,EAAaiB,yBAA2B,SAAUhzB,GAC9C,OAAO,IAAI/D,SAAQ,SAAUgB,GACzB+C,EAAiB,UAAI,WAAc,OAAO/C,GAAW,EACrD80B,EAAaM,oBAAoBryB,EACrC,GACJ,EAMA+xB,EAAakB,oBAAsB,SAAUR,EAAYH,EAAkBrpB,GACvE,IAAI8d,EAAM1wB,KAAKy8B,mBAAmB12B,IAAIk2B,GACjCvL,IACDA,EAAM,CAAC,EACP1wB,KAAKy8B,mBAAmBnrB,IAAI2qB,EAAkBvL,IAE7CA,EAAI0L,KACL1L,EAAI0L,GAAc,IAEtB1L,EAAI0L,GAAYn5B,KAAK2P,EACzB,EAIA8oB,EAAaE,QAAU,IAAI/pB,IAI3B6pB,EAAae,mBAAqB,IAAI5qB,IAC/B6pB,CACX,CApIiC,GAqIjCt9B,EAAQs9B,aAAeA,C,eCxIvBx9B,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQy+B,aAAU,EAClB,IAAIA,EAAyB,WACzB,SAASA,IACL78B,KAAK2zB,IAAM,EACXkJ,EAAQC,WAAa,EACrB98B,KAAK2zB,IAAMkJ,EAAQC,SACvB,CASA,OARA5+B,OAAOC,eAAe0+B,EAAQ38B,UAAW,KAAM,CAC3C6F,IAAK,WACD,OAAO/F,KAAK2zB,GAChB,EACA3tB,YAAY,EACZC,cAAc,IAElB42B,EAAQC,UAAY,EACbD,CACX,CAf4B,GAgB5Bz+B,EAAQy+B,QAAUA,C,eClBlB3+B,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQ2+B,YAAS,EACjB,IAAIA,EAAwB,WACxB,SAASA,IAEL/8B,KAAKg9B,gBAAkB,GAEvBh9B,KAAKi9B,eAAiB,IAAIprB,GAC9B,CA2CA,OA1CA3T,OAAOC,eAAe4+B,EAAO78B,UAAW,gBAAiB,CAErD6F,IAAK,WACD,OAAO/F,KAAKi9B,cAChB,EACAj3B,YAAY,EACZC,cAAc,IAElB/H,OAAOC,eAAe4+B,EAAO78B,UAAW,aAAc,CAElD6F,IAAK,WACD,OAAO/F,KAAKk9B,WAChB,EACAl3B,YAAY,EACZC,cAAc,IAGlB82B,EAAO78B,UAAUi9B,eAAiB,WAC9B,MAAO,EACX,EAOAJ,EAAO78B,UAAUk9B,SAAW,SAAUC,GAClC,OAAOr9B,KAAKi9B,eAAel3B,IAAIs3B,EACnC,EAEAN,EAAO78B,UAAUo9B,WAAa,SAAUlB,GACpCp8B,KAAKg9B,gBAAkBh9B,KAAKm9B,iBAC5B,IAAK,IAAIp5B,EAAM,EAAGmI,EAAIlM,KAAKg9B,gBAAgB15B,OAAQS,EAAMmI,EAAGnI,IAAO,CAC/D,IAAIw5B,EAAav9B,KAAKg9B,gBAAgBj5B,GACtC,IAAK/D,KAAKi9B,eAAe/iB,IAAIqjB,GAAa,CACtC,IAAIC,EAAiB,IAAID,EAAWv9B,MACpCw9B,EAA4B,YAAIpB,EAChCp8B,KAAKi9B,eAAe3rB,IAAIisB,EAAYC,GACpCA,EAAeC,aACnB,CACJ,CACJ,EACOV,CACX,CAlD2B,GAmD3B3+B,EAAQ2+B,OAASA,C,eCrDjB7+B,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQs/B,iBAAc,EACtB,IAAIA,EAA6B,WAC7B,SAASA,IACL19B,KAAK29B,UAAY,IACrB,CAgBA,OAfAz/B,OAAOC,eAAeu/B,EAAYx9B,UAAW,WAAY,CACrD6F,IAAK,WACD,OAAO/F,KAAK29B,SAChB,EACA33B,YAAY,EACZC,cAAc,IAOlBy3B,EAAYx9B,UAAUg8B,SAAW,WAC7B,OAAOl8B,KAAkB,WAC7B,EACO09B,CACX,CApBgC,GAqBhCt/B,EAAQs/B,YAAcA,C,eCftB,IAAIrB,EARJn+B,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQm+B,cAAgBn+B,EAAQi+B,gBAAa,EAQ7C,SAAWA,GACPA,EAAmB,OAAI,SACvBA,EAAkB,MAAI,QACtBA,EAAoB,QAAI,SAC3B,CAJD,CAIGA,IAAej+B,EAAQi+B,WAAaA,EAAa,CAAC,IACrD,IAAIE,EAA+B,WAC/B,SAASA,IACT,CAsDA,OArDAr+B,OAAOC,eAAeo+B,EAAe,aAAc,CAI/Cx2B,IAAK,WACD,OAAO/F,KAAKk9B,WAChB,EACAl3B,YAAY,EACZC,cAAc,IAMlBs2B,EAAcqB,qBAAuB,SAAUxB,GAC3Cp8B,KAAKk9B,YAAcd,CACvB,EACAl+B,OAAOC,eAAeo+B,EAAe,aAAc,CAK/Cx2B,IAAK,WACD,OAAO/F,KAAK69B,WAChB,EACA73B,YAAY,EACZC,cAAc,IAOlBs2B,EAAcuB,cAAgB,SAAUC,GACpC/9B,KAAKg+B,SAAWD,CACpB,EAEAxB,EAAc0B,YAAc,SAAU7B,GAClC,IAAK,IAAIr4B,EAAM,EAAGmI,EAAIlM,KAAKg+B,SAAS16B,OAAQS,EAAMmI,EAAGnI,IAAO,CACxD,IAAIm6B,EAAel+B,KAAKg+B,SAASj6B,GACjC,IAAK/D,KAAKm+B,uBAAuBjkB,IAAIgkB,GAAe,CAChDl+B,KAAKm+B,uBAAuB7sB,IAAI4sB,GAAc,GAC9C,IAAIE,EAAY,IAAIF,EACpBE,EAAuB,YAAIhC,EAC3Bp8B,KAAK69B,YAAY95B,GAAOq6B,EAExBA,EAAUd,WAAWlB,EACzB,CACJ,CACJ,EACAG,EAAcsB,YAAc,GAC5BtB,EAAc4B,uBAAyB,IAAItsB,IAC3C0qB,EAAcW,YAAcb,EAAWC,OAChCC,CACX,CAzDkC,GA0DlCn+B,EAAQm+B,cAAgBA,C,iBCxExBr+B,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQq9B,WAAQ,EAChB,IAAI4C,EAAiB,EAAQ,MACzBC,EAAY,EAAQ,MACpB3C,EAAkB,EAAQ,MAC1BF,EAAuB,WAKvB,SAASA,EAAM8C,GACXv+B,KAAKw+B,uBAAyB,IAAI3sB,IAClC7R,KAAKy+B,QAAUF,CACnB,CAyEA,OAxEArgC,OAAOC,eAAes9B,EAAMv7B,UAAW,aAAc,CAEjD6F,IAAK,WACD,OAAO/F,KAAKk9B,WAChB,EACAl3B,YAAY,EACZC,cAAc,IAKlBw1B,EAAMv7B,UAAUw+B,OAAS,WACzB,EAMAjD,EAAMv7B,UAAUy+B,eAAiB,WAC7B,OAAO,IACX,EAEAlD,EAAMv7B,UAAUg8B,SAAW,WACvB,OAAOl8B,KAAkB,WAC7B,EAEAy7B,EAAMv7B,UAAU0+B,UAAY,WACxB,OAAO5+B,KAAKy+B,OAChB,EAMAhD,EAAMv7B,UAAUs8B,eAAiB,SAAUqC,GAAU,EAErDpD,EAAMv7B,UAAUu9B,YAAc,WAC1B,IAAI7B,EAAU57B,KAAK2+B,iBACnB,GAAgB,OAAZ/C,EACA,IAAK,IAAI73B,EAAM,EAAGmI,EAAI0vB,EAAQt4B,OAAQS,EAAMmI,EAAGnI,IAAO,CAClD,IAAIk4B,EAAmBL,EAAQ73B,GAC/B,IAAK/D,KAAKw+B,uBAAuBtkB,IAAI+hB,GAAmB,CACpDj8B,KAAKw+B,uBAAuBltB,IAAI2qB,GAAkB,GAClD,IAAI6C,EAAW,IAAIR,EAAUzB,QAC7BiC,EAAS5D,MAAQl7B,KACjB8+B,EAAS/C,WAAaE,EACtBoC,EAAe3C,aAAaG,cAAciD,EAC9C,CACJ,CAEJ9+B,KAAK0+B,QACT,EAMAjD,EAAMv7B,UAAU87B,oBAAsB,SAAU6C,GACxC7+B,KAAKo8B,aAAeT,EAAgBU,WAAWC,QAAUt8B,KAAKo8B,aAAeT,EAAgBY,cAAcH,YAC3GiC,EAAe3C,aAAaM,oBAAoB6C,EAExD,EAMApD,EAAMv7B,UAAUy8B,yBAA2B,SAAUkC,GACjD,GAAI7+B,KAAKo8B,aAAeT,EAAgBU,WAAWC,QAAUt8B,KAAKo8B,aAAeT,EAAgBY,cAAcH,WAC3G,OAAOiC,EAAe3C,aAAaiB,yBAAyBkC,EAEpE,EACOpD,CACX,CAlF0B,GAmF1Br9B,EAAQq9B,MAAQA,C,GCxFZsD,yBAA2B,CAAC,EAGhC,SAASC,oBAAoBC,GAE5B,IAAIC,EAAeH,yBAAyBE,GAC5C,QAAqBv+B,IAAjBw+B,EACH,OAAOA,EAAa9gC,QAGrB,IAAI+gC,EAASJ,yBAAyBE,GAAY,CAGjD7gC,QAAS,CAAC,GAOX,OAHAghC,oBAAoBH,GAAUp8B,KAAKs8B,EAAO/gC,QAAS+gC,EAAQA,EAAO/gC,QAAS4gC,qBAGpEG,EAAO/gC,OACf,C,2BCmCIihC,OAvDAjoB,gBACAkoB,kBACAC,SACAC,UACAC,kBACAC,QACAC,UACAC,WACAC,UACAC,YACAC,cACAC,kBACAC,YACAC,OACAC,YACAC,OACAC,mBACAC,oBACAC,mBACAC,mBACAC,kBACAC,oBACAjiB,iBACAkiB,MAEAC,OACAC,QACA13B,SACA23B,YACAC,OACAC,WACAlhC,YACAmhC,UACAC,wBACAC,aACAC,SACAhzB,UACAopB,YACAvc,kBACAomB,UACAC,QACAvkB,QACAwkB,KACAC,OACAC,eACAC,MACAC,QACAtD,eACAC,UACAsD,SACAC,cACAlG,gBACAmG,QACA3zB,QArDAiJ,gBAAkB,oBAAQ,MAC1BkoB,kBAAoB,oBAAQ,MAC5BC,SAAW,oBAAQ,MACnBC,UAAY,oBAAQ,MACpBC,kBAAoB,oBAAQ,MAC5BC,QAAU,oBAAQ,MAClBC,UAAY,oBAAQ,MACpBC,WAAa,oBAAQ,MACrBC,UAAY,oBAAQ,MACpBC,YAAc,oBAAQ,MACtBC,cAAgB,oBAAQ,MACxBC,kBAAoB,oBAAQ,MAC5BC,YAAc,oBAAQ,MACtBC,OAAS,oBAAQ,MACjBC,YAAc,oBAAQ,MACtBC,OAAS,oBAAQ,KACjBC,mBAAqB,oBAAQ,MAC7BC,oBAAsB,oBAAQ,MAC9BC,mBAAqB,oBAAQ,MAC7BC,mBAAqB,oBAAQ,MAC7BC,kBAAoB,oBAAQ,MAC5BC,oBAAsB,oBAAQ,MAC9BjiB,iBAAmB,oBAAQ,MAC3BkiB,MAAQ,oBAAQ,MAEhBC,OAAS,oBAAQ,MACjBC,QAAU,oBAAQ,MAClB13B,SAAW,oBAAQ,MACnB23B,YAAc,oBAAQ,MACtBC,OAAS,oBAAQ,MACjBC,WAAa,oBAAQ,MACrBlhC,YAAc,oBAAQ,MACtBmhC,UAAY,oBAAQ,MACpBC,wBAA0B,oBAAQ,MAClCC,aAAe,oBAAQ,MACvBC,SAAW,oBAAQ,MACnBhzB,UAAY,oBAAQ,MACpBopB,YAAc,oBAAQ,MACtBvc,kBAAoB,oBAAQ,MAC5BomB,UAAY,oBAAQ,KACpBC,QAAU,oBAAQ,MAClBvkB,QAAU,oBAAQ,IAClBwkB,KAAO,oBAAQ,MACfC,OAAS,oBAAQ,MACjBC,eAAiB,oBAAQ,MACzBC,MAAQ,oBAAQ,MAChBC,QAAU,oBAAQ,MAClBtD,eAAiB,oBAAQ,MACzBC,UAAY,oBAAQ,MACpBsD,SAAW,oBAAQ,MACnBC,cAAgB,oBAAQ,MACxBlG,gBAAkB,oBAAQ,MAC1BmG,QAAU,oBAAQ,MAClB3zB,QAAU,oBAAQ,KAGtB,SAAWkxB,GAGPA,EAAO5D,MAAQqG,QAAQrG,MAEvB4D,EAAOtC,OAAS6E,SAAS7E,OACzBsC,EAAO9C,cAAgBZ,gBAAgBY,cAEvC8C,EAAO31B,QAAUP,SAASO,QAC1B21B,EAAO3D,aAAe2C,eAAe3C,aACrC2D,EAAOxC,QAAUyB,UAAUzB,QAC3BwC,EAAO3B,YAAcmE,cAAcnE,YAGnC2B,EAAO0C,QAAU,CACbzjC,mBAAoB8Y,gBAAgB9Y,oBAGxC+gC,EAAOx/B,gBAAkBy/B,kBAAkBz/B,gBAE3Cw/B,EAAO2C,OAAS,CACZ7+B,uBAAwBo8B,SAASp8B,uBACjCQ,aAAc47B,SAAS57B,aACvBQ,YAAao7B,SAASp7B,YACtBE,gBAAiBk7B,SAASl7B,iBAG9Bg7B,EAAO4C,MAAQ,CACXz8B,eAAgBg6B,UAAUh6B,eAC1BC,QAAS+5B,UAAU/5B,QACnBgB,gBAAiBg5B,kBAAkBh5B,gBACnCuB,cAAe03B,QAAQ13B,cACvBX,MAAOq4B,QAAQr4B,MACf6B,QAASy2B,UAAUz2B,QACnBkD,SAAUwzB,WAAWxzB,SACrBO,QAASkzB,UAAUlzB,SAGvB0yB,EAAOpxB,UAAY6xB,YAAY7xB,UAE/BoxB,EAAO3vB,YAAcqwB,cAAcrwB,YACnC2vB,EAAOzuB,gBAAkBovB,kBAAkBpvB,gBAE3CyuB,EAAO9sB,UAAY0tB,YAAYiC,QAE/B7C,EAAO8C,KAAO,CACVnvB,SAAUktB,OAAOltB,SACjBa,kBAAmBqsB,OAAOrsB,kBAC1BC,cAAeosB,OAAOpsB,cACtBC,aAAcmsB,OAAOnsB,aACrBG,cAAegsB,OAAOhsB,eAG1BmrB,EAAO3qB,UAAYyrB,YAAYzrB,UAE/B2qB,EAAO+C,KAAO,CACV9sB,gBAAiB8qB,OAAO9qB,gBACxBS,aAAcqqB,OAAOrqB,aACrBE,YAAamqB,OAAOnqB,aAGxBopB,EAAO1oB,cAAgB0pB,mBAAmB1pB,cAC1C0oB,EAAOpmB,SAAWynB,oBAAoBznB,SACtComB,EAAOtnB,SAAWuoB,oBAAoBvoB,SACtCsnB,EAAOjnB,QAAUmoB,mBAAmBnoB,QACpCinB,EAAO9mB,QAAUioB,mBAAmBjoB,QACpC8mB,EAAOtlB,MAAQ0E,iBAAiB1E,MAChCslB,EAAOziB,cAAgB6B,iBAAiB7B,cACxCyiB,EAAO3mB,cAAgB+nB,kBAAkB/nB,cAEzC2mB,EAAO7hB,cAAgBmjB,MAAMnjB,cAM7B6hB,EAAOgD,UAAY,CACfzjB,cAAegiB,OAAOhiB,cACtBE,cAAe8hB,OAAO9hB,eAG1BugB,EAAOiD,WAAa,CAChBrjB,MAAO4hB,QAAQ5hB,OAGnBogB,EAAOte,UAAY+f,YAAY/f,UAE/Bse,EAAO/W,KAAOyY,OAAOzY,KAErB+W,EAAOkD,cAAgB,CACnB30B,SAAUozB,WAAWpzB,UAGzByxB,EAAO1Q,aAAe8S,eAAe9S,aAErC0Q,EAAOp9B,UAAYnC,YAAYmC,UAE/Bo9B,EAAOnV,aAAesN,YAAYtN,aAClCmV,EAAOxU,MAAQ,CACXT,YAAaoN,YAAYpN,YACzBH,UAAWuN,YAAYvN,UACvBL,eAAgB4N,YAAY5N,gBAGhCyV,EAAOmD,QAAU,CACbxT,UAAWiS,UAAUjS,UACrBE,YAAa+R,UAAU/R,aAG3BmQ,EAAOhnB,YAAc,CACjB8W,yBAA0B+R,wBAAwB/R,0BAGtDkQ,EAAOpP,WAAakR,aAAalR,WAEjCoP,EAAOn7B,OAAS,CACZ+sB,WAAYmQ,SAASnQ,YAGzBoO,EAAOzwB,QAAUR,UAAUQ,QAE3BywB,EAAOoD,aAAe,CAClBz0B,QAASqzB,UAAUrzB,SAGvBqxB,EAAOnkB,gBAAkBD,kBAAkBC,gBAE3CmkB,EAAOjN,KAAOoP,OAAOpP,KAErBiN,EAAOjmB,MAAQ,CACX8Z,UAAWoO,QAAQpO,UACnBC,UAAWmO,QAAQnO,WAGvBkM,EAAOqD,UAAY,CACfrM,SAAUloB,QAAQkoB,SAClBE,cAAepoB,QAAQooB,cACvBG,SAAUvoB,QAAQuoB,SAClBE,aAAczoB,QAAQyoB,aACtBI,SAAU7oB,QAAQ6oB,SAClBC,WAAY9oB,QAAQ8oB,WACpBC,UAAW/oB,QAAQ+oB,UACnBzoB,YAAaN,QAAQM,YACrB0oB,UAAWhpB,QAAQgpB,UACnBb,kBAAmBnoB,QAAQmoB,kBAC3Be,WAAYlpB,QAAQkpB,WACpBC,QAASnpB,QAAQmpB,SAGrB+H,EAAOniB,MAAQH,QAAQG,MAEvBmiB,EAAO9H,GAAKgK,KAAKhK,GAEjB8H,EAAO/wB,IAAM,CACT2rB,qBAAsByH,MAAMzH,sBAGhCoF,EAAOsD,WAAa,CAChBhI,SAAUgH,QAAQhH,SAClBK,MAAO2G,QAAQ3G,MAEtB,CAjKD,CAiKGqE,SAAWA,OAAS,CAAC,IAExB3nB,OAAO2nB,OAASA,M", "sources": ["webpack://block-blast/./assets/scripts/base/adapter/AdapterFringe.ts", "webpack://block-blast/./assets/scripts/base/animation/DragonbonesAnim.ts", "webpack://block-blast/./assets/scripts/base/arrays/arrays.ts", "webpack://block-blast/./assets/scripts/base/async/Barrier.ts", "webpack://block-blast/./assets/scripts/base/async/DeferredPromise.ts", "webpack://block-blast/./assets/scripts/base/async/First.ts", "webpack://block-blast/./assets/scripts/base/async/Limiter.ts", "webpack://block-blast/./assets/scripts/base/async/Sequence.ts", "webpack://block-blast/./assets/scripts/base/async/WaitFor.ts", "webpack://block-blast/./assets/scripts/base/audio/AudioInfo.ts", "webpack://block-blast/./assets/scripts/base/cache/CacheRender.ts", "webpack://block-blast/./assets/scripts/base/components/CacheComponents.ts", "webpack://block-blast/./assets/scripts/base/components/Component.ts", "webpack://block-blast/./assets/scripts/base/copy/Copy.ts", "webpack://block-blast/./assets/scripts/base/crypto/ICrypto.ts", "webpack://block-blast/./assets/scripts/base/crypto/UrlCrypto.ts", "webpack://block-blast/./assets/scripts/base/date/Date.ts", "webpack://block-blast/./assets/scripts/base/decorators/DecoratorAdapter.ts", "webpack://block-blast/./assets/scripts/base/decorators/DecoratorClassId.ts", "webpack://block-blast/./assets/scripts/base/decorators/DecoratorDebounce.ts", "webpack://block-blast/./assets/scripts/base/decorators/DecoratorMeasure.ts", "webpack://block-blast/./assets/scripts/base/decorators/DecoratorMemoize.ts", "webpack://block-blast/./assets/scripts/base/decorators/DecoratorScreen.ts", "webpack://block-blast/./assets/scripts/base/decorators/DecoratorThrottle.ts", "webpack://block-blast/./assets/scripts/base/decorators/DecoratorTrait.ts", "webpack://block-blast/./assets/scripts/base/dot/Dot.ts", "webpack://block-blast/./assets/scripts/base/enum/enum.ts", "webpack://block-blast/./assets/scripts/base/equal/Equal.ts", "webpack://block-blast/./assets/scripts/base/events/Events.ts", "webpack://block-blast/./assets/scripts/base/hotUpdate/HotUpdate.ts", "webpack://block-blast/./assets/scripts/base/http/Http.ts", "webpack://block-blast/./assets/scripts/base/interval/Interval.ts", "webpack://block-blast/./assets/scripts/base/layer/GameLayer.ts", "webpack://block-blast/./assets/scripts/base/loader/ResLoader.ts", "webpack://block-blast/./assets/scripts/base/native/NativeBridge.ts", "webpack://block-blast/./assets/scripts/base/numbers/numbers.ts", "webpack://block-blast/./assets/scripts/base/performance/RenderingOptimization.ts", "webpack://block-blast/./assets/scripts/base/pool/ObjectPool.ts", "webpack://block-blast/./assets/scripts/base/random/Random.ts", "webpack://block-blast/./assets/scripts/base/storage/Storage.ts", "webpack://block-blast/./assets/scripts/base/task/Task.ts", "webpack://block-blast/./assets/scripts/base/timeout/Timeout.ts", "webpack://block-blast/./assets/scripts/base/timer/Timer.ts", "webpack://block-blast/./assets/scripts/base/trait/Trait.ts", "webpack://block-blast/./assets/scripts/base/traitConfig/TraitConfigInfo.ts", "webpack://block-blast/./assets/scripts/base/types/Types.ts", "webpack://block-blast/./assets/scripts/base/ui/UI.ts", "webpack://block-blast/./assets/scripts/base/url/Url.ts", "webpack://block-blast/./assets/scripts/base/watch/watch.ts", "webpack://block-blast/./assets/scripts/falcon/EventManager.ts", "webpack://block-blast/./assets/scripts/falcon/EventVo.ts", "webpack://block-blast/./assets/scripts/falcon/Module.ts", "webpack://block-blast/./assets/scripts/falcon/ModuleEvent.ts", "webpack://block-blast/./assets/scripts/falcon/ModuleManager.ts", "webpack://block-blast/./assets/scripts/falcon/Proxy.ts", "webpack://block-blast/webpack/bootstrap", "webpack://block-blast/./sdk/falcon.ts"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.applyAdapterFringe = applyAdapterFringe;\nvar IPHONE_X_HEIGHT_WIDTH_RATIO = 1280 / 716;\nvar IPHONE_X_TOP_ADJUSTMENT = 0.037;\n/**\n * 适配刘海\n * @param node\n */\nfunction applyAdapterFringe(node) {\n    if (!node) {\n        return;\n    }\n    var widget = node.getComponent(cc.Widget);\n    if (!widget) {\n        return;\n    }\n    var size = cc.view.getFrameSize();\n    var size2 = cc.view.getVisibleSize();\n    function isFringe(size, size2) {\n        if (size.width / size.height > (750 + 10) / 1334) {\n            return false;\n        }\n        if (cc.sys.os === cc.sys.OS_ANDROID || cc.sys.isBrowser) {\n            return (size.height / size.width > IPHONE_X_HEIGHT_WIDTH_RATIO) ||\n                (size2.height / size2.width > IPHONE_X_HEIGHT_WIDTH_RATIO);\n        }\n        return false;\n    }\n    if (isFringe(size, size2)) {\n        var height = Math.max(size.height, size2.height);\n        widget.top += IPHONE_X_TOP_ADJUSTMENT * height;\n    }\n}\n", "\"use strict\";\n/**\n * 管理龙骨动画播放\n * （1）：支持播放龙骨动画，且播放完毕后是否销毁\n * （2）：支持同时播放声音，且播放完毕后是否销毁\n * （3）：支持缓存\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.dragonbonesAnim = void 0;\nvar ResLoader_1 = require(\"../loader/ResLoader\");\nvar DragonBonesAnim = /** @class */ (function () {\n    function DragonBonesAnim() {\n        this.dragonbones = {};\n    }\n    /**\n     * 播放龙骨动画\n     * @param parentNode\n     * @param armatureName\n     * @param animationName\n     * @param playTimes\n     * @param config\n     * @returns\n     */\n    DragonBonesAnim.prototype.play = function (parentNode, armatureName, animationName, playTimes, config) {\n        var _this = this;\n        if (!config) {\n            return;\n        }\n        if (!armatureName) {\n            return;\n        }\n        if (!animationName) {\n            return;\n        }\n        if (playTimes === undefined) {\n            playTimes = 1;\n        }\n        var bundleName = config.bundleName, dragonAssetUrl = config.dragonAssetUrl, dragonAtlasAssetUrl = config.dragonAtlasAssetUrl, frameSplitting = config.frameSplitting;\n        if (dragonAssetUrl === undefined || dragonAssetUrl === '') {\n            return;\n        }\n        if (dragonAtlasAssetUrl === undefined || dragonAtlasAssetUrl === '') {\n            return;\n        }\n        if (animationName === undefined || animationName === '') {\n            return;\n        }\n        if (armatureName === undefined || armatureName === '') {\n            return;\n        }\n        // const key = `${dragonAssetUrl}_${dragonAtlasAssetUrl}`;\n        // if (this.dragonbones[key]) {\n        //     this.dragonbones[key].node.opacity = 255;\n        //     this.dragonbones[key].playAnimation(animationName, playTimes);\n        //     return this.dragonbones[key].node;\n        // }\n        var node = new cc.Node();\n        var dragonBonesArmatureDisplay = node.addComponent(dragonBones.ArmatureDisplay);\n        parentNode.addChild(node);\n        // this.dragonbones[key] = dragonBonesArmatureDisplay;\n        dragonBonesArmatureDisplay.enableBatch = true;\n        dragonBonesArmatureDisplay.updateAnimationCache(animationName);\n        dragonBonesArmatureDisplay.setAnimationCacheMode(dragonBones.ArmatureDisplay.AnimationCacheMode.SHARED_CACHE);\n        if (dragonBonesArmatureDisplay) {\n            if (config.completeBack && config.completeBackObj) {\n                dragonBonesArmatureDisplay.addEventListener(dragonBones.EventObject.COMPLETE, config.completeBack, config.completeBackObj);\n            }\n            if (config.frameSplitting) {\n                setTimeout(function () {\n                    _this._play(config, armatureName, animationName, playTimes, dragonBonesArmatureDisplay);\n                }, 50);\n            }\n            else {\n                this._play(config, armatureName, animationName, playTimes, dragonBonesArmatureDisplay);\n            }\n        }\n        return node;\n    };\n    DragonBonesAnim.prototype._play = function (config, armatureName, animationName, playTimes, dragonBonesArmatureDisplay) {\n        var bundleName = config.bundleName, dragonAssetUrl = config.dragonAssetUrl, dragonAtlasAssetUrl = config.dragonAtlasAssetUrl, frameSplitting = config.frameSplitting;\n        if (bundleName) {\n            ResLoader_1.ResLoader.renderDragonbonesByBundle(bundleName, dragonBonesArmatureDisplay, dragonAssetUrl, dragonAtlasAssetUrl, armatureName, animationName, playTimes);\n        }\n        else {\n            ResLoader_1.ResLoader.renderDragonbones(dragonBonesArmatureDisplay, dragonAssetUrl, dragonAtlasAssetUrl, armatureName, animationName, playTimes);\n        }\n    };\n    return DragonBonesAnim;\n}());\nexports.dragonbonesAnim = new DragonBonesAnim();\n", "\"use strict\";\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.arraysHaveSameElements = arraysHaveSameElements;\nexports.shuffleArray = shuffleArray;\nexports.arraysEqual = arraysEqual;\nexports.ensureMaxLength = ensureMaxLength;\n// 定义一个函数来检查两个数组是否包含相同的元素（不考虑顺序）\nfunction arraysHaveSameElements(arr1, arr2) {\n    if (arr1.length !== arr2.length)\n        return false;\n    // 复制数组并排序\n    var sortedArr1 = arr1.slice().sort();\n    var sortedArr2 = arr2.slice().sort();\n    // 比较排序后的数组\n    for (var i = 0; i < sortedArr1.length; i++) {\n        if (sortedArr1[i] !== sortedArr2[i]) {\n            return false;\n        }\n    }\n    return true;\n}\n// 随机打乱数组\nfunction shuffleArray(array) {\n    var _a;\n    // 创建数组的副本以避免修改原始数组\n    var shuffledArray = array.slice();\n    for (var i_1 = shuffledArray.length - 1; i_1 > 0; i_1--) {\n        // 生成一个随机索引\n        var j = Math.floor(Math.random() * (i_1 + 1));\n        // 交换元素\n        _a = __read([shuffledArray[j], shuffledArray[i_1]], 2), shuffledArray[i_1] = _a[0], shuffledArray[j] = _a[1];\n    }\n    return shuffledArray;\n}\n// 检查两个数组是否相等（考虑顺序）\nfunction arraysEqual(arr1, arr2) {\n    if (arr1.length !== arr2.length) {\n        return false;\n    }\n    for (var i_2 = 0; i_2 < arr1.length; i_2++) {\n        if (arr1[i_2] !== arr2[i_2]) {\n            return false;\n        }\n    }\n    return true;\n}\n// 确保数组长度不超过maxLength，如果超过则删除第一个元素，并添加新元素到数组末尾\nfunction ensureMaxLength(arr, maxLength, newItem) {\n    if (arr.length >= maxLength) {\n        arr.shift();\n    }\n    arr.push(newItem);\n    return arr;\n}\n", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.TimeoutBarrier = exports.Barrier = void 0;\n/**\n * 屏障控制器\n */\nvar Barrier = /** @class */ (function () {\n    function Barrier() {\n        var _this = this;\n        this._isOpen = false;\n        this._promise = new Promise(function (c, e) {\n            _this._completePromise = c;\n        });\n    }\n    Object.defineProperty(Barrier.prototype, \"isOpen\", {\n        /** 屏障是否打开 */\n        get: function () {\n            return this._isOpen;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /** 重置 */\n    Barrier.prototype.reset = function () {\n        this._isOpen = false;\n    };\n    /** 打开屏障 */\n    Barrier.prototype.open = function () {\n        this._isOpen = true;\n        this._completePromise(true);\n    };\n    /** 等待屏障打开 */\n    Barrier.prototype.wait = function () {\n        return this._promise;\n    };\n    return Barrier;\n}());\nexports.Barrier = Barrier;\n/**\n * 超时屏障控制器\n */\nvar TimeoutBarrier = /** @class */ (function (_super) {\n    __extends(TimeoutBarrier, _super);\n    function TimeoutBarrier(autoOpenTimeMs) {\n        var _this = _super.call(this) || this;\n        _this._timeout = setTimeout(function () { return _this.open(); }, autoOpenTimeMs);\n        return _this;\n    }\n    TimeoutBarrier.prototype.open = function () {\n        clearTimeout(this._timeout);\n        _super.prototype.open.call(this);\n    };\n    return TimeoutBarrier;\n}(Barrier));\nexports.TimeoutBarrier = TimeoutBarrier;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.DeferredPromise = void 0;\n/**\n * 延时 Promise，支持在外部手动处理整体逻辑\n */\nvar DeferredPromise = /** @class */ (function () {\n    function DeferredPromise() {\n        this._resolved = false;\n        this._promiseQueue = [];\n    }\n    /**\n     * 等待下一次 resolve。\n     * @returns 返回一个 Promise，当 resolve 被调用时，该 Promise 会被解决。\n     */\n    DeferredPromise.prototype.wait = function () {\n        var _this = this;\n        return new Promise(function (resolve, reject) {\n            if (_this._resolved) {\n                // 如果已经解决，直接返回已解决的值\n                resolve(_this._resolvedValue);\n                _this._resolved = false;\n                return;\n            }\n            _this._promiseQueue.push({ resolve: resolve, reject: reject });\n        });\n    };\n    /**\n     * 触发等待中的 Promise 解决。\n     * @param value - 解决的值。\n     */\n    DeferredPromise.prototype.resolve = function (value) {\n        // 如果队列为空并且已经解析，记录警告\n        if (this._promiseQueue.length === 0 && this._resolved) {\n            console.warn(\"DeferredPromise:  resolve 之前没有添加 wait,顺序可能有问题！\");\n        }\n        this._resolved = true;\n        this._resolvedValue = value;\n        var preConsumeLen = this._promiseQueue.length;\n        while (this._promiseQueue.length > 0) {\n            var resolve = this._promiseQueue.shift().resolve;\n            resolve(value);\n        }\n        if (preConsumeLen) {\n            this._resolved = false;\n        }\n    };\n    /**\n     * 触发等待中的 Promise 拒绝。\n     * @param reason - 拒绝的原因。\n     */\n    DeferredPromise.prototype.reject = function (reason) {\n        while (this._promiseQueue.length > 0) {\n            var reject = this._promiseQueue.shift().reject;\n            reject(reason);\n        }\n    };\n    return DeferredPromise;\n}());\nexports.DeferredPromise = DeferredPromise;\n", "\"use strict\";\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.first = first;\nexports.firstParallel = firstParallel;\n/**\n * 按顺序执行一个数组中的多个异步任务，直到找到一个满足特定条件的任务结果为止。如果所有任务都不满足条件，则返回一个默认值\n * @param promiseFactories\n * @param shouldStop\n * @param defaultValue\n * @returns\n */\nfunction first(promiseFactories, shouldStop, defaultValue) {\n    if (shouldStop === void 0) { shouldStop = function (t) { return !!t; }; }\n    if (defaultValue === void 0) { defaultValue = null; }\n    var index = 0;\n    var len = promiseFactories.length;\n    var loop = function () {\n        if (index >= len) {\n            return Promise.resolve(defaultValue);\n        }\n        var factory = promiseFactories[index++];\n        var promise = Promise.resolve(factory());\n        return promise.then(function (result) {\n            if (shouldStop(result)) {\n                return Promise.resolve(result);\n            }\n            return loop();\n        });\n    };\n    return loop();\n}\n/**\n * 用于并行处理多个异步任务的工具函数。它的目标是从一组并行运行的 Promise 中找到第一个满足特定条件的结果。如果没有任何一个 Promise 满足条件，则返回一个默认值。这个函数提供了两种重载形式，以支持不同的使用场景\n * @param promiseList\n * @param shouldStop\n * @param defaultValue\n * @returns\n */\nfunction firstParallel(promiseList, shouldStop, defaultValue) {\n    if (shouldStop === void 0) { shouldStop = function (t) { return !!t; }; }\n    if (defaultValue === void 0) { defaultValue = null; }\n    if (promiseList.length === 0) {\n        return Promise.resolve(defaultValue);\n    }\n    var todo = promiseList.length;\n    var finish = function () {\n        var e_1, _a;\n        var _b, _c;\n        todo = -1;\n        try {\n            for (var promiseList_1 = __values(promiseList), promiseList_1_1 = promiseList_1.next(); !promiseList_1_1.done; promiseList_1_1 = promiseList_1.next()) {\n                var promise = promiseList_1_1.value;\n                (_c = (_b = promise).cancel) === null || _c === void 0 ? void 0 : _c.call(_b);\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (promiseList_1_1 && !promiseList_1_1.done && (_a = promiseList_1.return)) _a.call(promiseList_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n    };\n    return new Promise(function (resolve, reject) {\n        var e_2, _a;\n        try {\n            for (var promiseList_2 = __values(promiseList), promiseList_2_1 = promiseList_2.next(); !promiseList_2_1.done; promiseList_2_1 = promiseList_2.next()) {\n                var promise = promiseList_2_1.value;\n                promise.then(function (result) {\n                    if (--todo >= 0 && shouldStop(result)) {\n                        finish();\n                        resolve(result);\n                    }\n                    else if (todo === 0) {\n                        resolve(defaultValue);\n                    }\n                })\n                    .catch(function (err) {\n                    if (--todo >= 0) {\n                        finish();\n                        reject(err);\n                    }\n                });\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (promiseList_2_1 && !promiseList_2_1.done && (_a = promiseList_2.return)) _a.call(promiseList_2);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n    });\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Limiter = void 0;\nvar Events_1 = require(\"../events/Events\");\n/**\n * 用于控制并发任务数量的工具类。\n * 它的主要功能是将多个异步任务排队，并确保在任何时刻同时运行的任务数量不超过指定的最大并发度（maxDegreeOfParallelism）\n */\nvar Limiter = /** @class */ (function () {\n    function Limiter(maxDegreeOfParalellism) {\n        this._size = 0;\n        this._isDisposed = false;\n        this.maxDegreeOfParalellism = maxDegreeOfParalellism;\n        this.outstandingPromises = [];\n        this.runningPromises = 0;\n        this._onDrained = new Events_1.Emitter();\n    }\n    Object.defineProperty(Limiter.prototype, \"onDrained\", {\n        get: function () {\n            return this._onDrained.event;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Limiter.prototype, \"size\", {\n        get: function () {\n            return this._size;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Limiter.prototype.queue = function (factory) {\n        var _this = this;\n        if (this._isDisposed) {\n            throw new Error('Object has been disposed');\n        }\n        this._size++;\n        return new Promise(function (c, e) {\n            _this.outstandingPromises.push({ factory: factory, c: c, e: e });\n            _this.consume();\n        });\n    };\n    Limiter.prototype.consume = function () {\n        var _this = this;\n        while (this.outstandingPromises.length && this.runningPromises < this.maxDegreeOfParalellism) {\n            var iLimitedTask = this.outstandingPromises.shift();\n            this.runningPromises++;\n            var promise = iLimitedTask.factory();\n            promise.then(iLimitedTask.c, iLimitedTask.e);\n            promise.then(function () { return _this.consumed(); }, function () { return _this.consumed(); });\n        }\n    };\n    Limiter.prototype.consumed = function () {\n        if (this._isDisposed) {\n            return;\n        }\n        this.runningPromises--;\n        if (--this._size === 0) {\n            this._onDrained.fire();\n        }\n        if (this.outstandingPromises.length > 0) {\n            this.consume();\n        }\n    };\n    Limiter.prototype.clear = function () {\n        if (this._isDisposed) {\n            throw new Error('Object has been disposed');\n        }\n        this.outstandingPromises.length = 0;\n        this._size = this.runningPromises;\n    };\n    Limiter.prototype.dispose = function () {\n        this._isDisposed = true;\n        this.outstandingPromises.length = 0;\n        this._size = 0;\n        this._onDrained.dispose();\n    };\n    return Limiter;\n}());\nexports.Limiter = Limiter;\n", "\"use strict\";\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __generator = (this && this.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\n    return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.sequence = sequence;\n/**\n * 按顺序执行提供的 Promise 工厂函数列表，并返回一个 Promise，该 Promise 将解析为每个 Promise 的结果数组\n * @param promiseFactories\n * @param interruptConfition 中断条件，每执行一次 promise 之前，需判断是否满足中断条件\n * @returns\n */\nfunction sequence(promiseFactories, interruptConfition) {\n    var results = [];\n    var index = 0;\n    var len = promiseFactories.length;\n    var isInterrupt = false;\n    function next() {\n        return index < len ? promiseFactories[index++]() : null;\n    }\n    function checkInterruptCondition() {\n        return __awaiter(this, void 0, void 0, function () {\n            var cond;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!interruptConfition) return [3 /*break*/, 2];\n                        return [4 /*yield*/, interruptConfition()];\n                    case 1:\n                        cond = _a.sent();\n                        return [2 /*return*/, cond];\n                    case 2: return [2 /*return*/, false];\n                }\n            });\n        });\n    }\n    function thenHandler(result) {\n        return checkInterruptCondition().then(function (isInterrupt) {\n            if (isInterrupt) {\n                if (len >= results.length) {\n                    results.push.apply(results, __spreadArray([], __read(new Array(len - results.length).fill(undefined)), false));\n                }\n                return Promise.resolve(results);\n            }\n            if (result !== undefined && result !== null) {\n                results.push(result);\n            }\n            var n = next();\n            if (n) {\n                return n.then(thenHandler).catch(function (reason) {\n                    // If interrupted\n                    if (isInterrupt) {\n                        return Promise.resolve(results);\n                    }\n                    results.push.apply(results, __spreadArray([], __read(new Array(len - results.length).fill(undefined)), false));\n                    return Promise.resolve(results);\n                });\n            }\n            return Promise.resolve(results);\n        });\n    }\n    return Promise.resolve(null).then(thenHandler);\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.waitFor = void 0;\nvar WaitFor = /** @class */ (function () {\n    function WaitFor() {\n        this._waitFors = {};\n    }\n    /**\n     * 开始\n     * @param key\n     */\n    WaitFor.prototype.start = function (key) {\n        var _resolve, _reject;\n        this._waitFors[key] = {\n            startTime: Date.now(),\n            promise: new Promise(function (resolve, reject) {\n                _resolve = resolve;\n                _reject = reject;\n            }),\n            state: false\n        };\n        this._waitFors[key].resolve = _resolve;\n        this._waitFors[key].reject = _reject;\n    };\n    /**\n     * 结束\n     * @param key\n     */\n    WaitFor.prototype.end = function (key) {\n        var waitInfo = this._waitFors[key];\n        if (waitInfo) {\n            waitInfo.endTime = Date.now();\n            waitInfo.state = true;\n        }\n        else {\n            console.error(\"waitFor \".concat(key, \" end \\u65F6\\u9700\\u5148\\u6267\\u884C start!\"));\n        }\n    };\n    /**\n     * 等待某个键值的状态完成\n     * @param key\n     */\n    WaitFor.prototype.wait = function (key, option) {\n        var waitInfo = this._waitFors[key];\n        if (waitInfo) {\n            var resolve_1 = waitInfo.resolve, reject_1 = waitInfo.reject;\n            if (waitInfo.state) {\n                if (resolve_1) {\n                    resolve_1(waitInfo.state);\n                }\n            }\n            else {\n                waitInfo.interval = setInterval(function () {\n                    if (waitInfo.state) {\n                        clearInterval(waitInfo.interval);\n                        resolve_1(key);\n                    }\n                    else {\n                        //如果超时了，则直接返回\n                        if (option && !isNaN(option.timeout)) {\n                            if (Date.now() - waitInfo.startTime > option.timeout) {\n                                clearInterval(waitInfo.interval);\n                                reject_1(\"wait for \".concat(key, \" is timeout!\"));\n                            }\n                        }\n                    }\n                }, 16);\n            }\n            return waitInfo.promise;\n        }\n    };\n    return WaitFor;\n}());\nexports.waitFor = new WaitFor();\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.audioInfo = exports.AudioType = void 0;\nvar ResLoader_1 = require(\"../loader/ResLoader\");\nvar Types_1 = require(\"../types/Types\");\nvar Storage_1 = require(\"../storage/Storage\");\n/**\n * 声音类型\n */\nvar AudioType;\n(function (AudioType) {\n    /** 声音 */\n    AudioType[AudioType[\"SOUND\"] = 0] = \"SOUND\";\n    /** 音效 */\n    AudioType[AudioType[\"EFFECT\"] = 1] = \"EFFECT\";\n})(AudioType || (exports.AudioType = AudioType = {}));\n/*\n * 普通场景，音效 管理器，（适应于 浏览器，和native ）\n */\nvar AudioInfo = /** @class */ (function () {\n    function AudioInfo() {\n    }\n    /**\n     * 播放声音或者音效\n     * @param option\n     */\n    AudioInfo.prototype.play = function (option) {\n        var _this = this;\n        if (!option) {\n            if (CC_DEBUG) {\n                console.error(\"\\u58F0\\u97F3\\u64AD\\u653E\\u65F6\\u64AD\\u653E\\u65F6\\u9009\\u9879\\u4E0D\\u80FD\\u4E3A\\u7A7A\\uFF01\");\n            }\n            return;\n        }\n        var url = option.url, volume = option.volume, type = option.type, bundleName = option.bundleName;\n        if (!url) {\n            return;\n        }\n        if ((0, Types_1.isUndefined)(type)) {\n            type = AudioType.EFFECT;\n        }\n        var audioSwitch = Storage_1.storage.getItem(\"audioSwitch\", true);\n        var bgmSwitch = Storage_1.storage.getItem(\"bgmSwitch\", false);\n        // @ts-ignore\n        if (type === AudioType.EFFECT && !audioSwitch\n            || type === AudioType.SOUND && !bgmSwitch) {\n            return;\n        }\n        if (volume === undefined) {\n            volume = 1;\n        }\n        if (!bundleName) {\n            ResLoader_1.ResLoader.load(url, cc.AudioClip, function (err, clip) {\n                _this._play(type, volume, err, clip);\n            });\n        }\n        else {\n            ResLoader_1.ResLoader.loadByBundle(bundleName, url, cc.AudioClip, function (err, clip) {\n                _this._play(type, volume, err, clip);\n            });\n        }\n    };\n    AudioInfo.prototype._play = function (type, volume, err, clip) {\n        if (err) {\n            return;\n        }\n        if (type == AudioType.EFFECT) {\n            cc.audioEngine.play(clip, false, volume);\n        }\n        else {\n            cc.audioEngine.playMusic(clip, true);\n        }\n    };\n    /**停止播放声音或者所有音效 */\n    AudioInfo.prototype.stop = function (option) {\n        var url = option.url, volume = option.volume, type = option.type;\n        if ((0, Types_1.isUndefined)(type)) {\n            type = AudioType.EFFECT;\n        }\n        var audioSwitch = Storage_1.storage.getItem(\"audioSwitch\", true);\n        var bgmSwitch = Storage_1.storage.getItem(\"bgmSwitch\", false);\n        // @ts-ignore\n        if (type === AudioType.EFFECT && !audioSwitch\n            || type === AudioType.SOUND && !bgmSwitch) {\n            return;\n        }\n        if (type == AudioType.EFFECT) {\n            cc.audioEngine.stopAllEffects();\n        }\n        else {\n            cc.audioEngine.stopMusic();\n        }\n    };\n    return AudioInfo;\n}());\nexports.audioInfo = new AudioInfo();\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.cacheRender = void 0;\nvar ResLoader_1 = require(\"../loader/ResLoader\");\nvar CacheRender = /** @class */ (function () {\n    function CacheRender() {\n        this._caches = {};\n    }\n    /**\n     * 创建或更新缓存列表组件\n     * @param prefabUrl 预预制体地址\n     * @param count 缓存池长度\n     * @param compClass 缓存的组件类\n     * @param parent 父容器\n     * @returns\n     */\n    CacheRender.prototype.createOrUpdateCacheListComponents = function (option) {\n        var _this = this;\n        if (!option) {\n            return;\n        }\n        var prefabUrl = option.prefabUrl, bundleName = option.bundleName, count = option.count, typeOrClassName = option.typeOrClassName, parent = option.parent;\n        if (count <= 0) {\n            return;\n        }\n        if (!parent) {\n            return;\n        }\n        return new Promise(function (c, e) {\n            var onComplete = function (err, asset) {\n                if (err) {\n                    e(err);\n                    return;\n                }\n                var caches = _this._caches;\n                var itemCaches = caches[prefabUrl];\n                if (!itemCaches) {\n                    caches[prefabUrl] = [];\n                    itemCaches = caches[prefabUrl];\n                }\n                var cacheLen = itemCaches.length;\n                // 禁用多余的\n                if (cacheLen > count) {\n                    for (var i_1 = count; i_1 < cacheLen; i_1++) {\n                        var cacheComp = itemCaches[i_1];\n                        cacheComp.node.active = false;\n                    }\n                }\n                // 创建或者激活当前的\n                var comps = [];\n                for (var i_2 = 0; i_2 < count; i_2++) {\n                    var cacheComp = itemCaches[i_2];\n                    if (!cacheComp) {\n                        var node = cc.instantiate(asset);\n                        itemCaches[i_2] = node.getComponent(typeOrClassName);\n                        parent.addChild(node);\n                        comps[i_2] = itemCaches[i_2];\n                    }\n                    else {\n                        cacheComp.node.active = true;\n                        if (cacheComp.node.parent !== parent) {\n                            cacheComp.node.parent = parent;\n                        }\n                        comps[i_2] = cacheComp;\n                    }\n                }\n                c(comps);\n            };\n            if (bundleName) {\n                ResLoader_1.ResLoader.loadByBundle(bundleName, prefabUrl, cc.Prefab, onComplete);\n            }\n            else {\n                ResLoader_1.ResLoader.load(prefabUrl, cc.Prefab, onComplete);\n            }\n        });\n    };\n    return CacheRender;\n}());\nexports.cacheRender = new CacheRender();\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.cacheComponents = void 0;\n/**\n * 高性能组件快速获取器\n */\nvar CacheComponents = /** @class */ (function () {\n    function CacheComponents() {\n        CacheComponents._cacheComponents = this;\n    }\n    /**\n     * 组件 Enable 劫持，组件激活时存储\n     * @param callback\n     */\n    CacheComponents.prototype.componentEnabledIntercept = function () {\n        if (!CacheComponents._componentOnEnabledIntercept) {\n            CacheComponents._componentOnEnabledIntercept = true;\n            var originalCompSchedulerOnEnabled_1 = cc.director['_compScheduler']._onEnabled;\n            cc.director['_compScheduler']._onEnabled = function (comp) {\n                CacheComponents._cacheAllComponents.set(comp.constructor, comp);\n                originalCompSchedulerOnEnabled_1.apply(this, [comp]);\n                var resolves = CacheComponents._resolves.get(comp.constructor);\n                if (resolves) {\n                    for (var i_1 = 0; i_1 < resolves.length; i_1++) {\n                        var resolve = resolves[i_1];\n                        resolve(comp);\n                    }\n                    resolves.length = 0;\n                }\n            };\n        }\n    };\n    /**\n     * 快速同步获取实例\n     * @param CompClass\n     * @returns\n     */\n    CacheComponents.prototype.Cinst = function (CompClass) {\n        var comp = CacheComponents._cacheAllComponents.get(CompClass);\n        if (comp) {\n            return comp;\n        }\n        return undefined;\n    };\n    /**\n     * 快速异步获取实例\n     * @param CompClass\n     * @returns\n     */\n    CacheComponents.prototype.CinstAsync = function (CompClass) {\n        return new Promise(function (resolve, e) {\n            var comp = CacheComponents._cacheComponents.Cinst(CompClass);\n            if (comp) {\n                resolve(comp);\n                return;\n            }\n            else {\n                var resovles = CacheComponents._resolves.get(CompClass);\n                if (!resovles) {\n                    resovles = [resolve];\n                    CacheComponents._resolves.set(CompClass, resovles);\n                }\n                else {\n                    resovles.push(resolve);\n                }\n                CacheComponents._cacheComponents.componentEnabledIntercept();\n            }\n        });\n    };\n    CacheComponents._cacheAllComponents = new Map();\n    CacheComponents._resolves = new Map();\n    return CacheComponents;\n}());\nexports.cacheComponents = new CacheComponents();\n", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;\nvar Component = /** @class */ (function (_super) {\n    __extends(Component, _super);\n    function Component() {\n        var _this = _super.apply(this, __spreadArray([], __read(arguments), false)) || this;\n        _this._state = {};\n        return _this;\n    }\n    Object.defineProperty(Component.prototype, \"state\", {\n        get: function () {\n            return this._state;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * 设置最新状态\n     * @param state\n     * @param replace\n     * @param callback\n     */\n    Component.prototype.setState = function (state, replace, callback) {\n        if (this._state !== state) {\n            //状态不能直接传过去，这样每次都需要传所有的值，否则，会出问题\n            var shouldUpdate = true;\n            if (this._state) {\n                shouldUpdate = this.shouldComponentUpdate(state);\n            }\n            if (Object.prototype.toString.call(state) !== '[object Object]') {\n                this._state = state;\n            }\n            else {\n                if (!this._state) {\n                    this._state = Object.create(null);\n                }\n                if (!replace)\n                    this.deepCopy(this._state, state);\n                else\n                    this._state = state;\n            }\n            //是否需要更新render函数\n            if (shouldUpdate) {\n                this.componentWillUpdate(this._state);\n                //开始更新\n                this.render();\n                //回调\n                callback && callback();\n            }\n        }\n    };\n    /**\n     * 深度拷贝数据\n     * @param target\n     * @param source\n     */\n    Component.prototype.deepCopy = function (target, source) {\n        for (var key in source) {\n            var value = source[key];\n            //直接替换属性\n            target[key] = value;\n        }\n    };\n    /**\n     * 组件将初始化时调用\n     */\n    Component.prototype.componentWillMount = function () {\n    };\n    /**\n     * 组件初始化时调用，只会被调用一次\n     */\n    Component.prototype.componentDidMount = function () {\n    };\n    /**\n     * 组件卸载时调用\n     */\n    Component.prototype.componentWillUnmount = function () {\n    };\n    /**\n     * 是否应该更新改组件，一般通过这个函数控制可以优化性能\n     * @param nextState\n     */\n    Component.prototype.shouldComponentUpdate = function (nextState) {\n        return true;\n    };\n    /**\n     * 生命周期shouldComponentUpdate如果为真，那么，才会被调用，如果调用，会在render函数之前调用\n     * @param nextProps\n     * @param nextState\n     */\n    Component.prototype.componentWillUpdate = function (nextState) {\n    };\n    /**\n     * 组件更新完成之后，调用render函数之后执行\n     * @param prevProps\n     * @param prevState\n     */\n    Component.prototype.componentDidUpdate = function (prevState) {\n    };\n    /**\n     * 渲染ui\n     */\n    Component.prototype.render = function () {\n    };\n    Component = __decorate([\n        ccclass\n    ], Component);\n    return Component;\n}(cc.Component));\nexports.default = Component;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.deepCopy = deepCopy;\nexports.deepCopyArrayFrom = deepCopyArrayFrom;\nexports.deepCopySlice = deepCopySlice;\nexports.deepCopyLoop = deepCopyLoop;\nexports.deepCopyFixed = deepCopyFixed;\n// 以前gametools的拷贝方法\n/**\n * 深度拷贝二维数字数组，根据数组大小自动选择最高效的方法\n * 根据性能测试，对不同大小的数组使用不同的拷贝策略：\n * - 小型和中型数组（< 80行）：使用slice方法\n * - 大型数组（>= 80行）：使用Array.from方法\n */\nfunction deepCopy(gameData) {\n    // 根据数组大小选择最佳方法\n    if (gameData.length < 80) {\n        // 小型和中型数组使用slice方法\n        return gameData.map(function (row) { return row.slice(); });\n    }\n    else {\n        // 大型数组使用原始方法\n        return gameData.map(function (row) { return Array.from(row); });\n    }\n}\n/**\n * 使用Array.from方法的深度拷贝，在大型数组上表现最佳\n */\nfunction deepCopyArrayFrom(gameData) {\n    return gameData.map(function (row) { return Array.from(row); });\n}\n/**\n * 使用slice方法的深度拷贝，在小型和中型数组上表现最佳\n */\nfunction deepCopySlice(gameData) {\n    return gameData.map(function (row) { return row.slice(); });\n}\n/**\n * 使用循环的深度拷贝，在某些特殊场景可能有用\n */\nfunction deepCopyLoop(gameData) {\n    var result = [];\n    var len = gameData.length;\n    for (var i_1 = 0; i_1 < len; i_1++) {\n        var row = gameData[i_1];\n        var rowLen = row.length;\n        var newRow = new Array(rowLen);\n        for (var j = 0; j < rowLen; j++) {\n            newRow[j] = row[j];\n        }\n        result[i_1] = newRow;\n    }\n    return result;\n}\n/**\n * 如果知道数组大小固定，可使用定长数组深度拷贝\n */\nfunction deepCopyFixed(gameData, rowLength) {\n    var result = [];\n    var len = gameData.length;\n    for (var i_2 = 0; i_2 < len; i_2++) {\n        var srcRow = gameData[i_2];\n        var actualRowLength = rowLength || srcRow.length;\n        var newRow = new Array(actualRowLength);\n        for (var j = 0; j < actualRowLength; j++) {\n            newRow[j] = srcRow[j];\n        }\n        result[i_2] = newRow;\n    }\n    return result;\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ICrypto = void 0;\nvar ICrypto = /** @class */ (function () {\n    function ICrypto() {\n    }\n    /**\n     * 加密\n     * @param content\n     */\n    ICrypto.encrypt = function (content) {\n        return content;\n    };\n    /**\n     * 解密\n     * @param content\n     */\n    ICrypto.decrypt = function (content) {\n        return content;\n    };\n    return ICrypto;\n}());\nexports.ICrypto = ICrypto;\n", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.UrlCrypto = void 0;\nvar ICrypto_1 = require(\"./ICrypto\");\nvar UrlCrypto = /** @class */ (function (_super) {\n    __extends(UrlCrypto, _super);\n    function UrlCrypto() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    /**\n     * 加密\n     * @url url\n     * @returns\n     */\n    UrlCrypto.encrypt = function (url) {\n        var base64Str = Buffer.from(url).toString('base64');\n        return this._des(base64Str);\n    };\n    /**\n     * 解密\n     * @param url\n     * @returns\n     */\n    UrlCrypto.decrypt = function (url) {\n        var decryptStr = this._des(url);\n        return Buffer.from(decryptStr, 'base64').toString('utf-8');\n    };\n    UrlCrypto._des = function (url) {\n        // 替换空格为加号\n        var paramStr = url.replace(/\\s+/g, '+');\n        var paramLength = paramStr.length;\n        var keyLength = this.encodeKey.length;\n        // 初始化一个字节数组用于存放加密后的结果\n        var encodeStr = \"\";\n        for (var i_1 = 0; i_1 < paramLength; i_1++) {\n            // 获取原始字节串中的单个字节\n            var str = paramStr[i_1];\n            // 查找字节在EncodeKey中的索引\n            var index = this.encodeKey.indexOf(str);\n            if (index !== -1) {\n                // 根据索引找到对应的加密字节\n                var t = this.encodeKey[keyLength - index - 1];\n                // 将加密字节追加到结果中\n                encodeStr += t;\n            }\n        }\n        // 返回加密后的字节序列\n        return encodeStr;\n    };\n    UrlCrypto.encodeKey = \"C1eWgtN/ZOJ=qw2TXyhxjV+0SlUL35R6ri9G4uamPfQpK78AdHbBczFnYEskMDIvo\";\n    return UrlCrypto;\n}(ICrypto_1.ICrypto));\nexports.UrlCrypto = UrlCrypto;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getLastSomeDays = getLastSomeDays;\nexports.getTodayDate = getTodayDate;\nexports.getDiffDays = getDiffDays;\n/**\n * 获取最近几天日期\n * [\n    \"2025_2_14\",\n    \"2025_2_13\",\n    \"2025_2_12\",\n    \"2025_2_11\",\n    \"2025_2_10\",\n    \"2025_2_9\",\n    \"2025_2_8\"\n   ]\n * @param day\n * @returns\n */\nfunction getLastSomeDays(day) {\n    if (day === void 0) { day = 7; }\n    var currentTime = Date.now();\n    var days = [];\n    for (var i_1 = 1; i_1 <= day; i_1++) {\n        var data = new Date();\n        data.setTime(currentTime - 24 * 60 * 60 * 1000 * i_1);\n        days.push(data.getFullYear() + \"_\" + data.getMonth() + \"_\" + data.getDate());\n    }\n    return days;\n}\n/**\n * 获取今天日期: 2025_3_24\n * @returns\n */\nfunction getTodayDate() {\n    var today = new Date();\n    return \"\".concat(today.getFullYear(), \"_\").concat(today.getMonth() + 1, \"_\").concat(today.getDate());\n}\n//获取两个日期之间相差的天数 \nfunction getDiffDays(date1, date2) {\n    var date1WithoutTime = new Date(date1);\n    date1WithoutTime.setHours(0, 0, 0, 0);\n    var date2WithoutTime = new Date(date2);\n    date2WithoutTime.setHours(0, 0, 0, 0);\n    var diffTime = Math.abs(date1WithoutTime.getTime() - date2WithoutTime.getTime());\n    var diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return diffDays;\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.adapterFringe = adapterFringe;\nvar AdapterFringe_1 = require(\"../adapter/AdapterFringe\");\n/**\n * 适配刘海\n * @param className\n * @returns\n */\nfunction adapterFringe() {\n    var nodeNames = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        nodeNames[_i] = arguments[_i];\n    }\n    return function (target, propertyKey, descriptor) {\n        var originalMethod = descriptor.value;\n        descriptor.value = function () {\n            var _this = this;\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            nodeNames.forEach(function (nodeName) {\n                var node = _this[nodeName];\n                if (node) {\n                    (0, AdapterFringe_1.applyAdapterFringe)(node);\n                }\n            });\n            return originalMethod.apply(this, args);\n        };\n        return descriptor;\n    };\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.classId = classId;\nexports.getClassName = getClassName;\n/**\n * 绑定真实类名\n * @param className\n * @returns\n */\nfunction classId(className) {\n    return function (target) {\n        if (!target.prototype['__classname__'])\n            target.prototype['__classname__'] = className;\n    };\n}\n/**\n * 获取类名\n * @param objOrCtor\n */\nfunction getClassName(objOrCtor) {\n    return cc.js.getClassName(objOrCtor);\n}\nwindow['classId'] = classId;\nwindow['getClassName'] = getClassName;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.decorate = decorate;\nexports.debounce = debounce;\nfunction decorate(decorator) {\n    /**\n     * @param {any} _target\n     * @param {string} key\n     * @param {any} descriptor\n     */\n    return function (_target, key, descriptor) {\n        var fnKey = null;\n        var fn = null;\n        if (typeof descriptor.value === 'function') {\n            fnKey = 'value';\n            fn = descriptor.value;\n        }\n        else if (typeof descriptor.get === 'function') {\n            fnKey = 'get';\n            fn = descriptor.get;\n        }\n        if (!fn || !fnKey) {\n            throw new Error('not supported');\n        }\n        descriptor[fnKey] = decorator(fn, key);\n    };\n}\n/**\n * 函数去抖,优化掉可能会执行非常频繁的操作，按delay时间间隔执行\n * |Click：—————————————————— delay\n   |        Click：—————————————————— delay\n   |                  Click：—————————————————— delay\n   |                              Click：—————————————————— delay | 执行动作\n * @param {number} delay 去抖动时间 单位：毫秒\n * @param {string=} str 去抖动时间期间 toast文案\n */\nfunction debounce(delay, str) {\n    if (delay === void 0) { delay = 1000; }\n    return decorate(function (fn, key) {\n        var timerKey = \"$debounce$\".concat(key);\n        var oldTimes = {};\n        oldTimes[timerKey] = -10000000;\n        return function () {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            var curTime = Date.now();\n            var $oldTime = oldTimes[timerKey];\n            if (curTime - $oldTime >= delay) {\n                oldTimes[timerKey] = curTime;\n                return fn.apply(this, args);\n            }\n            else {\n                if (typeof str === 'string' || typeof str === 'number') {\n                    console.warn(str);\n                }\n            }\n        };\n    });\n}\n", "\"use strict\";\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __generator = (this && this.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\n    return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.measure = measure;\n/**\n * 时间检测\n * @param className\n * @returns\n */\nfunction measure(target, propertyKey, descriptor) {\n    var originalMethod = descriptor.value;\n    descriptor.value = function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        return __awaiter(this, void 0, void 0, function () {\n            var start, result, finish;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!CC_DEBUG) return [3 /*break*/, 2];\n                        start = performance.now();\n                        return [4 /*yield*/, originalMethod.apply(this, args)];\n                    case 1:\n                        result = _a.sent();\n                        finish = performance.now();\n                        console.log(\"\".concat(propertyKey, \" execution time: \").concat(finish - start, \" milliseconds\"));\n                        return [2 /*return*/, result];\n                    case 2: return [2 /*return*/, originalMethod.apply(this, args)];\n                }\n            });\n        });\n    };\n    return descriptor;\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.memoize = memoize;\n/**\n* 能对属性进行缓存（为了解决某些属性全局只需要调用一次）\n* @param {any} target\n* @param {string} key\n* @param {any} descriptor\n*/\nfunction memoize(target, key, descriptor) {\n    var fnKey = null;\n    var fn = null;\n    if (typeof descriptor.value === 'function') {\n        fnKey = 'value';\n        fn = descriptor.value;\n        if (fn.length !== 0) {\n            console.warn('Memoize should only be used in functions with zero parameters');\n        }\n    }\n    else if (typeof descriptor.get === 'function') {\n        fnKey = 'get';\n        fn = descriptor.get;\n    }\n    if (!fn) {\n        throw new Error('not supported');\n    }\n    var memoizeKey = \"$memoize$\".concat(key);\n    descriptor[fnKey] = function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        if (!this.hasOwnProperty(memoizeKey)) {\n            Object.defineProperty(this, memoizeKey, {\n                configurable: false,\n                enumerable: false,\n                writable: false,\n                value: fn.apply(this, args)\n            });\n        }\n        return this[memoizeKey];\n    };\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ScreenAdapter = ScreenAdapter;\nfunction ScreenAdapter() {\n    return function (target, propertyKey, descriptor) {\n        var originalMethod = descriptor.value;\n        descriptor.value = function () {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            var winsize = cc.view.getVisibleSize();\n            if (winsize.height / winsize.width >= 1334 / 750) {\n                var _canvas = cc.Canvas.instance;\n                _canvas.fitHeight = false;\n                _canvas.fitWidth = true;\n            }\n            else if (winsize.height / winsize.width < 1334 / 750) {\n                var _canvas = cc.Canvas.instance;\n                _canvas.fitHeight = true;\n                _canvas.fitWidth = false;\n            }\n            return originalMethod.apply(this, args);\n        };\n        return descriptor;\n    };\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.decorate = decorate;\nexports.throttle = throttle;\nexports.throttleCall = throttleCall;\nfunction decorate(decorator) {\n    /**\n     * @param {any} _target\n     * @param {string} key\n     * @param {any} descriptor\n     */\n    return function (_target, key, descriptor) {\n        var fnKey = null;\n        var fn = null;\n        if (typeof descriptor.value === 'function') {\n            fnKey = 'value';\n            fn = descriptor.value;\n        }\n        else if (typeof descriptor.get === 'function') {\n            fnKey = 'get';\n            fn = descriptor.get;\n        }\n        if (!fn || !fnKey) {\n            throw new Error('not supported');\n        }\n        descriptor[fnKey] = decorator(fn, key);\n    };\n}\n/**\n * 节流 n 秒内只运行一次，若在 n 秒内重复触发，只有一次生效\n * |Click：————————————————————————————————————| delay\n   |        Click：————————————————————————————| delay\n   |                  Click：——————————————————| delay\n   |                              Click：——————| delay | 执行动作\n * @param delay\n * @returns\n */\nfunction throttle(delay, immediate) {\n    if (delay === void 0) { delay = 500; }\n    if (immediate === void 0) { immediate = true; }\n    return decorate(function (fn, key) {\n        var oldtime = Date.now();\n        var timer = null;\n        return function () {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            var context = this;\n            if (immediate) {\n                var newtime = Date.now();\n                if (newtime - oldtime >= delay) {\n                    fn.apply(context, args);\n                    oldtime = newtime;\n                }\n            }\n            else {\n                clearTimeout(timer);\n                timer = setTimeout(function () {\n                    fn.apply(context, args);\n                }, delay);\n            }\n        };\n    });\n}\n/**\n * 节流 n 秒内只运行一次，若在 n 秒内重复触发，只有一次生效\n * |Click：————————————————————————————————————| delay\n   |        Click：————————————————————————————| delay\n   |                  Click：——————————————————| delay\n   |                              Click：——————| delay | 执行动作\n * @param func\n * @param delay\n * @returns\n */\nfunction throttleCall(func, delay, immediate) {\n    if (delay === void 0) { delay = 500; }\n    if (immediate === void 0) { immediate = true; }\n    var oldtime = Date.now();\n    var timer = null;\n    return function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        var context = this;\n        if (immediate) {\n            var newtime = Date.now();\n            if (newtime - oldtime >= delay) {\n                func.apply(context, args);\n                oldtime = newtime;\n            }\n        }\n        else {\n            clearTimeout(timer);\n            timer = setTimeout(function () {\n                func.apply(context, args);\n            }, delay);\n        }\n    };\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.shareObjects = exports.setPerformanceTieringType = exports.onDidTraitOnActive = exports.GBMTraitsMaps = exports.traitMaps = exports.decoratorTraitsClassNameMap = void 0;\nexports.trait = trait;\nexports.GBM = GBM;\nexports.templateTrait = templateTrait;\nvar Events_1 = require(\"../events/Events\");\nvar Trait_1 = require(\"../trait/Trait\");\nvar TraitConfigInfo_1 = require(\"../traitConfig/TraitConfigInfo\");\nrequire(\"./DecoratorClassId\");\n/**\n * 装饰的所有特性\n */\nexports.decoratorTraitsClassNameMap = {};\n/** 所有特性集 */\nexports.traitMaps = new Map();\n/** GBM 特性集 */\nexports.GBMTraitsMaps = new Map();\n/** 存储模板特性列表 */\nvar templateTraits = {};\nvar _onDidTraitOnActive = new Events_1.Emitter();\nexports.onDidTraitOnActive = _onDidTraitOnActive.event;\nvar _performanceTieringType = 0;\nvar setPerformanceTieringType = function (type) {\n    _performanceTieringType = type;\n};\nexports.setPerformanceTieringType = setPerformanceTieringType;\n/**\n * 已经激活的特性\n */\nwindow['activeTraits'] = {};\n/**\n * 共享数据\n */\nexports.shareObjects = new Map();\n/**\n * 特性装饰器\n * 如果一个方法上有多个装饰器，装饰器的函数会从下往上执行，但是装饰器包装器（descriptor.value)的顺序是从上往下执行\n * @param traitClass 特性类\n * @param desc 特性描述\n * @returns\n */\nfunction trait(traitClass, desc) {\n    if (desc === void 0) { desc = \"\"; }\n    return function (target, methodName, descriptor) {\n        if (!exports.shareObjects.has(target)) {\n            exports.shareObjects.set(target, {});\n        }\n        var methodShareObject = exports.shareObjects.get(target);\n        if (!methodShareObject[methodName]) {\n            methodShareObject[methodName] = {};\n        }\n        if (descriptor['__originalMethod__'] === undefined) {\n            descriptor['__originalMethod__'] = descriptor.value;\n        }\n        var shareTraitTarget = methodShareObject[methodName];\n        shareTraitTarget.returnState = false;\n        shareTraitTarget.replace = false;\n        shareTraitTarget.methodName = methodName;\n        shareTraitTarget.method = descriptor['__originalMethod__'];\n        var traitInst = exports.traitMaps.get(traitClass);\n        if (!traitInst) {\n            traitInst = new traitClass();\n            // 特性数据初始化到状态上\n            traitInst['_state'] = traitInst['data']();\n            // 【生命周期】特性被创建\n            traitInst.onCreate();\n            /*******************************************************\n             *\n             *                      处理子特性\n             *\n             *******************************************************/\n            var subTraits = traitInst.registerSubTraits();\n            if (subTraits) {\n                var subTraitsInst = [];\n                for (var i_1 = 0; i_1 < subTraits.length; i_1++) {\n                    var subTraitClass = subTraits[i_1];\n                    var hasSubTraitClass = exports.traitMaps.has(subTraitClass);\n                    if (!hasSubTraitClass) {\n                        var subTraitInst = new subTraitClass();\n                        subTraitInst['_state'] = subTraitInst['data']();\n                        subTraitInst['onCreate']();\n                        subTraitsInst[i_1] = subTraitInst;\n                        exports.traitMaps.set(subTraitClass, subTraitInst);\n                    }\n                    else {\n                        subTraitsInst[i_1] = exports.traitMaps.get(subTraitClass);\n                    }\n                }\n                traitInst['_subTraits'] = subTraitsInst;\n            }\n            // 每一个特性只实例化一次（单例）\n            exports.traitMaps.set(traitClass, traitInst);\n        }\n        var traitClassName = getClassName(traitClass);\n        if (traitClassName && !exports.decoratorTraitsClassNameMap[traitClassName]) {\n            exports.decoratorTraitsClassNameMap[traitClassName] = traitInst;\n        }\n        /**\n         * 如果一个方法上有多个装饰器，则 descriptor.value 会更新包装，并不会始终指向最原始的 method\n         * 方法最下面的装饰器 descriptor.value 首先指向原始方法\n         */\n        var originalMethod = descriptor.value;\n        shareTraitTarget['lastMethod'] = originalMethod;\n        /**\n         * 劫持装饰器绑定的方法，首先由最底下的特性装饰器劫持 target[methodName]，再将结果传递给上一层\n         * @param args\n         * @returns\n         */\n        descriptor.value = function () {\n            var _this = this;\n            var _a;\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            if (originalMethod === shareTraitTarget['lastMethod']) {\n                /************************************************************\n                 *\n                 *  重置数据区\n                 * （1）：每次调用方法时，触发第一个装饰器前需要重置数据，以保持共享目标达到初始化状态\n                 *\n                 *************************************************************/\n                // 重置替换状态\n                shareTraitTarget.replace = false;\n                // 重置 return 状态\n                shareTraitTarget.returnState = false;\n                // 重置共享共数据，共享数据：复杂的耦合（使各个特性数据进行共享），彻底解决耦合问题\n                shareTraitTarget.data = {};\n                // 重置参数状态\n                shareTraitTarget.args = [];\n                // 重置 return value\n                shareTraitTarget.returnValue = undefined;\n            }\n            // 存储调用方法实例\n            shareTraitTarget.target = this;\n            // 为了避免因为 classId 装饰器较晚执行，放在这儿\n            // let sss = target;\n            if (!shareTraitTarget.className) {\n                shareTraitTarget.className = getClassName(this.constructor);\n            }\n            // 只能在调用的地方获取特性类信息，因为装饰的时候数据还未拿到\n            var traitClassInfo = TraitConfigInfo_1.traitConfigInfo.traitsClassNameMap[traitClassName];\n            var props = traitClassInfo === null || traitClassInfo === void 0 ? void 0 : traitClassInfo.param;\n            if (props) {\n                var keyValue = Object.keys(props)[0];\n                // 分层渲染逻辑\n                if (!isNaN(+keyValue)) {\n                    props = props[_performanceTieringType];\n                }\n                else {\n                    props.active = true;\n                }\n                // 目前只调用一次\n                if (!traitInst['_active']) {\n                    traitInst['_id'] = traitClassInfo.id;\n                    traitInst['_props'] = props;\n                    traitInst['_active'] = true;\n                    traitInst['onEnable']();\n                }\n                /*******************************************************\n                 *\n                 *                      处理子特性\n                 *\n                 *******************************************************/\n                // TODO 后面优化性能\n                var subTraits = traitInst['_subTraits'];\n                if (subTraits) {\n                    for (var i_2 = 0; i_2 < subTraits.length; i_2++) {\n                        var subTrait = subTraits[i_2];\n                        var subTraitClassInfo = TraitConfigInfo_1.traitConfigInfo.traitsClassNameMap[subTrait.traitName];\n                        if (subTraitClassInfo) {\n                            subTrait['_props'] = subTraitClassInfo.param;\n                        }\n                    }\n                }\n            }\n            /**\n             * 计算模板特性激活状态\n             * （1）：A(模板)[开]   B[关]   C[关]   激活的特性：A\n             * （2）：A(模板)[开]   B[开]   C[关]   激活的特性：BA\n             * （3）：A(模板)[关]   B[关]   C[开]   激活的特性：CA\n             * （4）：A(模板)[关]   B[开]   C[开]   激活的特性：BCA\n             */\n            var templateTraitRefList = templateTraits[traitClassName];\n            var templateTraitActiveState = false;\n            if (templateTraitRefList) {\n                for (var i_3 = 0; i_3 < templateTraitRefList.length; i_3++) {\n                    var templateProps = (_a = TraitConfigInfo_1.traitConfigInfo.traitsClassNameMap[templateTraitRefList[i_3]]) === null || _a === void 0 ? void 0 : _a.param;\n                    if (templateProps && Object.keys(templateProps).length !== 0) {\n                        templateTraitActiveState = true;\n                        break;\n                    }\n                }\n            }\n            /**\n             * 如果自身激活了或者模板特性激活了\n             */\n            if (traitInst.active || templateTraitActiveState) {\n                var returnState = shareTraitTarget.returnState;\n                if (!returnState) {\n                    //console.log('%c[特性]', \"color:#fff;background:#15019a;\", `${shareTraitTarget.className}的方法${methodName}调用了特性：${traitClassName}`);\n                    shareTraitTarget.args = args;\n                    shareTraitTarget.originalCaller = function () {\n                        originalMethod.apply(_this, args);\n                    };\n                    this['shareTraitTarget'] = shareTraitTarget;\n                    // 监听特性被激活前\n                    listenerTraitOnActive(traitInst, \"preActive\");\n                    traitInst.onActive(shareTraitTarget);\n                    if (CC_DEBUG || CC_DEV) {\n                        activeTraits[traitClassName] = methodName;\n                    }\n                    if (CC_DEBUG) {\n                        _onDidTraitOnActive.fire(traitInst.id);\n                    }\n                    // 监听特性被激活后\n                    listenerTraitOnActive(traitInst, \"actived\");\n                }\n                // 如果特性有替换状态，直接替换原始执行逻辑\n                if (shareTraitTarget.replace) {\n                    return;\n                }\n            }\n            var returnValue = originalMethod.apply(this, args);\n            var shareTraitTargetReturnValue = shareTraitTarget.returnValue;\n            if (shareTraitTargetReturnValue !== undefined) {\n                return shareTraitTargetReturnValue;\n            }\n            return returnValue;\n        };\n    };\n}\n/**\n * 监听特性被激活\n * @param inst\n * @param type\n */\nfunction listenerTraitOnActive(inst, type) {\n    var listenerActives = Trait_1.Trait.activedListenerTraits;\n    var activeListeners = listenerActives.get(inst['contructor']);\n    if (activeListeners) {\n        for (var i_4 = 0; i_4 < activeListeners.length; i_4++) {\n            var activeListener = activeListeners[i_4];\n            if (activeListener === null || activeListener === void 0 ? void 0 : activeListener[type]) {\n                activeListener[type](inst);\n            }\n        }\n    }\n}\n/**\n * GBM\n * @returns\n */\nfunction GBM() {\n    return function (target) {\n        var GBMTraitClass = target;\n        if (!exports.traitMaps.has(GBMTraitClass)) {\n            var gbmTraitInst = new GBMTraitClass();\n            gbmTraitInst['_state'] = gbmTraitInst['data']();\n            gbmTraitInst['onCreate']();\n            exports.traitMaps.set(target, gbmTraitInst);\n        }\n        var traitClassName = getClassName(GBMTraitClass);\n        if (traitClassName && !exports.decoratorTraitsClassNameMap[traitClassName]) {\n            exports.decoratorTraitsClassNameMap[traitClassName] = exports.traitMaps.get(GBMTraitClass);\n        }\n        if (!exports.GBMTraitsMaps.has(GBMTraitClass)) {\n            exports.GBMTraitsMaps.set(target, exports.traitMaps.get(GBMTraitClass));\n        }\n    };\n}\n/**\n * 模板特性\n * 特性某个时候可能会坍缩成模板特性，变成模板后：\n * （1）：如果traitClassNameList中的特性有激活状态，其模板自身的激活状态将无效，该模板始终处于激活状态\n *\n * @param traitClassNameList\n */\nfunction templateTrait(traitClassNameList) {\n    return function (constructor) {\n        if (!traitClassNameList)\n            return;\n        var templateTraitName = getClassName(constructor);\n        if (templateTraitName) {\n            templateTraits[templateTraitName] = traitClassNameList;\n        }\n    };\n}\nwindow['TRAIT'] = function (instanceClass) {\n    return exports.traitMaps.get(instanceClass);\n};\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.DotUploadType = void 0;\nexports.createDotData = createDotData;\nvar Storage_1 = require(\"../storage/Storage\");\nvar DecoratorTrait_1 = require(\"../decorators/DecoratorTrait\");\nvar TraitConfigInfo_1 = require(\"../traitConfig/TraitConfigInfo\");\nvar DotUploadType;\n(function (DotUploadType) {\n    /** 数仓 */\n    DotUploadType[DotUploadType[\"MARKET\"] = 0] = \"MARKET\";\n    /** 数籹 */\n    DotUploadType[DotUploadType[\"SHUSHU\"] = 1] = \"SHUSHU\";\n})(DotUploadType || (exports.DotUploadType = DotUploadType = {}));\n;\n/**\n         * IsRemindAndAssistPlayersTrait 特性被激活时，打点数据可能需要劫持当前数据，并附加一些数据到原打点数据上\n         * 比如：\n         * （1）：在一些打点数据上添加一些新的属性\n         * （2）：修改打点数据已存在属性的值\n         * （3）：可以做一些其它操作等，比如存储数据\n         */\n// DM('usr_data_game_end',\n//     { 'GameTime': 10, 'GameType': 100 },\n//     [\n//         {\n//             class: IsRemindAndAssistPlayersTrait,\n//             assign: (trait:Trait, data) => {\n//                 return {\n//                     abc: 11\n//                 };\n//             }\n//         },\n//         {\n//             class: IsPointMarkerTrait,\n//             assign: (trait:Trait, data) => {\n//                 return {\n//                     CornerIcon: 1\n//                 }\n//             }\n//         }\n//     ]\n// );\n/**\n * 创建打点数据\n * @param uploadType\n * @param eventKey\n * @param params\n * @param traits\n */\nfunction createDotData(uploadType, eventKey, params, traits, callback) {\n    /** 通用化配置 */\n    var data = {\n        time: Date.now()\n    };\n    //const { nowWayArr, gameWayNum } = gameWayInfo;\n    var gameWayNum = Storage_1.storage.getItem(\"gameWayNum\");\n    var nowWayArr = Storage_1.storage.getItem(\"nowWayArr\", []);\n    // ABStatus 补充\n    if ((nowWayArr === null || nowWayArr === void 0 ? void 0 : nowWayArr.length) > 0) {\n        data.ABStatus = nowWayArr;\n    }\n    // active_waynum 补充 TODO 如果遇到 A_B这种形式，则可能计算有问题，需要看主包的处理逻辑\n    var _gameWayNum = +gameWayNum;\n    if (!isNaN(_gameWayNum)) {\n        data.active_waynum = {\n            pici: Math.floor(_gameWayNum / 100).toString(),\n            1: _gameWayNum\n        };\n    }\n    // TODO new_waynum 补充\n    if (!params) {\n        params = {};\n    }\n    if (params) {\n        data = Object.assign(params, data);\n    }\n    // 数仓才会加名字，因为传给原生需要\n    if (uploadType === DotUploadType.MARKET) {\n        data.name = eventKey;\n    }\n    if (traits) {\n        //通过特性动态劫持插入数据\n        getTraitActiveStatus(data, traits).then(function (v) {\n            if (callback) {\n                callback(data);\n            }\n        });\n    }\n    else {\n        // 正常打点数据\n        if (callback) {\n            callback(data);\n        }\n    }\n}\n/**\n *\n * @param traits\n * @returns\n */\nfunction getTraitActiveStatus(data, traits) {\n    return new Promise(function (c, e) {\n        for (var i_1 = 0; i_1 < traits.length; i_1++) {\n            var traitDotInfo = traits[i_1];\n            var traitClass = traitDotInfo.class;\n            var traitClassName = getClassName(traitClass);\n            var trait = DecoratorTrait_1.decoratorTraitsClassNameMap[traitClassName];\n            if (!trait) {\n                //如果打点没有装饰，则直接创建\n                var dotTraitInst = void 0;\n                if (!DecoratorTrait_1.traitMaps.has(traitClass)) {\n                    dotTraitInst = new traitClass();\n                    dotTraitInst['_state'] = dotTraitInst['data']();\n                    dotTraitInst['onCreate']();\n                    DecoratorTrait_1.traitMaps.set(traitClass, dotTraitInst);\n                }\n                if (traitClassName && !DecoratorTrait_1.decoratorTraitsClassNameMap[traitClassName]) {\n                    DecoratorTrait_1.decoratorTraitsClassNameMap[traitClassName] = DecoratorTrait_1.traitMaps.get(traitClass);\n                }\n                trait = DecoratorTrait_1.decoratorTraitsClassNameMap[traitClassName];\n                var traitClassInfo = TraitConfigInfo_1.traitConfigInfo.traitsClassNameMap[traitClassName];\n                var props = traitClassInfo === null || traitClassInfo === void 0 ? void 0 : traitClassInfo.param;\n                if (props) {\n                    dotTraitInst['_id'] = traitClassInfo.id;\n                    dotTraitInst['_props'] = props;\n                    dotTraitInst['_active'] = true;\n                    dotTraitInst['onEnable']();\n                }\n            }\n            var active = trait.active;\n            if (active) {\n                if (traitDotInfo.assign) {\n                    data = Object.assign(data, traitDotInfo.assign(trait, data));\n                }\n            }\n        }\n        c(true);\n    });\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getKeyByValue = getKeyByValue;\nexports.isValueInEnum = isValueInEnum;\n// 查找enum的key值\nfunction getKeyByValue(enumObj, value) {\n    for (var key in enumObj) {\n        if (enumObj[key] === value) {\n            return key;\n        }\n    }\n    return undefined;\n}\n// enum 是否存在某\nfunction isValueInEnum(value, enumObj) {\n    return Object.values(enumObj).includes(value);\n}\n", "\"use strict\";\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.equal = equal;\n/**\n * 比较两个对象或者数组值是否相等\n * @param obj1\n * @param obj2\n * @returns\n */\nfunction equal(obj1, obj2, visited) {\n    var e_1, _a, e_2, _b;\n    if (visited === void 0) { visited = []; }\n    // 基本类型直接比较\n    if (obj1 === obj2)\n        return true;\n    // 检查是否为对象且不为 null\n    if (typeof obj1 !== 'object' || obj1 === null ||\n        typeof obj2 !== 'object' || obj2 === null) {\n        return false;\n    }\n    try {\n        // 处理循环引用：检查是否已比较过当前对象对\n        for (var visited_1 = __values(visited), visited_1_1 = visited_1.next(); !visited_1_1.done; visited_1_1 = visited_1.next()) {\n            var entry = visited_1_1.value;\n            if (entry[0] === obj1 && entry[1] === obj2)\n                return true;\n        }\n    }\n    catch (e_1_1) { e_1 = { error: e_1_1 }; }\n    finally {\n        try {\n            if (visited_1_1 && !visited_1_1.done && (_a = visited_1.return)) _a.call(visited_1);\n        }\n        finally { if (e_1) throw e_1.error; }\n    }\n    visited.push([obj1, obj2]);\n    // 比较构造函数\n    if (obj1.constructor !== obj2.constructor)\n        return false;\n    // 处理包装对象如 Object(123)\n    if (obj1.constructor === Number || obj1.constructor === String || obj1.constructor === Boolean) {\n        return obj1.valueOf() === obj2.valueOf();\n    }\n    // 处理数组\n    if (Array.isArray(obj1) && Array.isArray(obj2)) {\n        if (obj1.length !== obj2.length)\n            return false;\n        for (var i_1 = 0; i_1 < obj1.length; i_1++) {\n            if (!equal(obj1[i_1], obj2[i_1], visited))\n                return false;\n        }\n        return true;\n    }\n    // 处理普通对象\n    var keysA = Object.keys(obj1);\n    var keysB = Object.keys(obj2);\n    if (keysA.length !== keysB.length)\n        return false;\n    try {\n        for (var keysA_1 = __values(keysA), keysA_1_1 = keysA_1.next(); !keysA_1_1.done; keysA_1_1 = keysA_1.next()) {\n            var key = keysA_1_1.value;\n            if (!obj2.hasOwnProperty(key))\n                return false;\n            if (!equal(obj1[key], obj2[key], visited))\n                return false;\n        }\n    }\n    catch (e_2_1) { e_2 = { error: e_2_1 }; }\n    finally {\n        try {\n            if (keysA_1_1 && !keysA_1_1.done && (_b = keysA_1.return)) _b.call(keysA_1);\n        }\n        finally { if (e_2) throw e_2.error; }\n    }\n    return true;\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Emitter = void 0;\nvar Emitter = /** @class */ (function () {\n    function Emitter(options) {\n        this._options = options;\n    }\n    Object.defineProperty(Emitter.prototype, \"event\", {\n        /**\n         * 获取发射器事件\n         */\n        get: function () {\n            var _this = this;\n            var _a;\n            (_a = this._event) !== null && _a !== void 0 ? _a : (this._event = function (callback, thisArgs) {\n                var _a, _b, _c, _d, _e, _f;\n                if (thisArgs) {\n                    callback = callback.bind(thisArgs);\n                }\n                if (!_this._callbacks) {\n                    (_b = (_a = _this._options) === null || _a === void 0 ? void 0 : _a.onWillAddFirstListener) === null || _b === void 0 ? void 0 : _b.call(_a, _this);\n                    _this._callbacks = [];\n                    (_d = (_c = _this._options) === null || _c === void 0 ? void 0 : _c.onDidAddFirstListener) === null || _d === void 0 ? void 0 : _d.call(_c, _this);\n                }\n                (_f = (_e = _this._options) === null || _e === void 0 ? void 0 : _e.onDidAddListener) === null || _f === void 0 ? void 0 : _f.call(_e, _this);\n                _this._callbacks.push({ callback: callback, thisArgs: thisArgs });\n            });\n            return this._event;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * 发送事件\n     * @param event\n     * @returns\n     */\n    Emitter.prototype.fire = function (event) {\n        var _callbacks = this._callbacks;\n        for (var i_1 = 0; i_1 < (_callbacks === null || _callbacks === void 0 ? void 0 : _callbacks.length); i_1++) {\n            var _a = _callbacks[i_1], callback = _a.callback, thisArgs = _a.thisArgs;\n            if (callback) {\n                callback.apply(thisArgs, [event]);\n            }\n        }\n    };\n    Emitter.prototype.dispose = function () {\n        var _a, _b;\n        if (!this._disposed) {\n            this._disposed = true;\n            this._callbacks = undefined;\n            (_b = (_a = this._options) === null || _a === void 0 ? void 0 : _a.onDidRemoveLastListener) === null || _b === void 0 ? void 0 : _b.call(_a);\n        }\n    };\n    return Emitter;\n}());\nexports.Emitter = Emitter;\n", "\"use strict\";\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nvar _a;\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.hotUpdate = exports.LOCAL_GAME_VERSION_KEY = exports.EventCodeMap = exports.EventCodeEnum = void 0;\nvar EventCodeEnum;\n(function (EventCodeEnum) {\n    EventCodeEnum[EventCodeEnum[\"ERROR_NO_LOCAL_MANIFEST\"] = 0] = \"ERROR_NO_LOCAL_MANIFEST\";\n    EventCodeEnum[EventCodeEnum[\"ERROR_DOWNLOAD_MANIFEST\"] = 1] = \"ERROR_DOWNLOAD_MANIFEST\";\n    EventCodeEnum[EventCodeEnum[\"ERROR_PARSE_MANIFEST\"] = 2] = \"ERROR_PARSE_MANIFEST\";\n    EventCodeEnum[EventCodeEnum[\"NEW_VERSION_FOUND\"] = 3] = \"NEW_VERSION_FOUND\";\n    EventCodeEnum[EventCodeEnum[\"ALREADY_UP_TO_DATE\"] = 4] = \"ALREADY_UP_TO_DATE\";\n    EventCodeEnum[EventCodeEnum[\"UPDATE_PROGRESSION\"] = 5] = \"UPDATE_PROGRESSION\";\n    EventCodeEnum[EventCodeEnum[\"ASSET_UPDATED\"] = 6] = \"ASSET_UPDATED\";\n    EventCodeEnum[EventCodeEnum[\"ERROR_UPDATING\"] = 7] = \"ERROR_UPDATING\";\n    EventCodeEnum[EventCodeEnum[\"UPDATE_FINISHED\"] = 8] = \"UPDATE_FINISHED\";\n    EventCodeEnum[EventCodeEnum[\"UPDATE_FAILED\"] = 9] = \"UPDATE_FAILED\";\n    EventCodeEnum[EventCodeEnum[\"ERROR_DECOMPRESS\"] = 10] = \"ERROR_DECOMPRESS\";\n    EventCodeEnum[EventCodeEnum[\"STOP_WHENUPDATE\"] = 11] = \"STOP_WHENUPDATE\";\n    // 自定义状态\n    EventCodeEnum[EventCodeEnum[\"START\"] = 12] = \"START\";\n    EventCodeEnum[EventCodeEnum[\"FAIL\"] = 13] = \"FAIL\";\n})(EventCodeEnum || (exports.EventCodeEnum = EventCodeEnum = {}));\nexports.EventCodeMap = (_a = {},\n    _a[EventCodeEnum.ERROR_NO_LOCAL_MANIFEST] = 'ERROR_NO_LOCAL_MANIFEST',\n    _a[EventCodeEnum.ERROR_DOWNLOAD_MANIFEST] = 'ERROR_DOWNLOAD_MANIFEST',\n    _a[EventCodeEnum.ERROR_PARSE_MANIFEST] = 'ERROR_PARSE_MANIFEST',\n    _a[EventCodeEnum.NEW_VERSION_FOUND] = 'NEW_VERSION_FOUND',\n    _a[EventCodeEnum.ALREADY_UP_TO_DATE] = 'ALREADY_UP_TO_DATE',\n    _a[EventCodeEnum.UPDATE_PROGRESSION] = 'UPDATE_PROGRESSION',\n    _a[EventCodeEnum.ASSET_UPDATED] = 'ASSET_UPDATED',\n    _a[EventCodeEnum.ERROR_UPDATING] = 'ERROR_UPDATING',\n    _a[EventCodeEnum.UPDATE_FINISHED] = 'UPDATE_FINISHED',\n    _a[EventCodeEnum.UPDATE_FAILED] = 'UPDATE_FAILED',\n    _a[EventCodeEnum.ERROR_DECOMPRESS] = 'ERROR_DECOMPRESS',\n    _a[EventCodeEnum.STOP_WHENUPDATE] = 'STOP_WHENUPDATE',\n    // 自定义状态\n    _a[EventCodeEnum.START] = 'START',\n    _a[EventCodeEnum.FAIL] = 'FAIL',\n    _a);\n;\nexports.LOCAL_GAME_VERSION_KEY = 'local_game_version';\nvar HotUpdate = /** @class */ (function () {\n    function HotUpdate() {\n        this.currentFileNum = 0; //当前已下载的文件数\n        this.currentFileTotal = 0; //需要下载的文件总数\n        this.projectManifestStr = null;\n        this.updating = false;\n        this.failCount = 0;\n        this.remoteVersionManifest = null;\n        this.am = null;\n        this._storagePath = '';\n        this._callbacks = [];\n    }\n    Object.defineProperty(HotUpdate.prototype, \"storagePath\", {\n        get: function () {\n            return this._storagePath;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    HotUpdate.prototype._emit = function (state, event) {\n        this._callbacks.forEach(function (cb) { return cb(state, event); });\n    };\n    HotUpdate.prototype.onHotUpdateState = function (callback) {\n        if (typeof callback === 'function') {\n            this._callbacks.push(callback);\n        }\n    };\n    /** 版本比对 */\n    HotUpdate.prototype.versionCompareHandle = function (versionA, versionB) {\n        console.log(' version A is ' + versionA + ', version B is ' + versionB);\n        var vA = versionA.split('.');\n        var vB = versionB.split('.');\n        for (var i = 0; i < vA.length; ++i) {\n            var a = parseInt(vA[i]);\n            var b = parseInt(vB[i] || '0');\n            if (a === b) {\n                continue;\n            }\n            else {\n                return a - b;\n            }\n        }\n        if (vB.length > vA.length) {\n            return -1;\n        }\n        else {\n            return 0;\n        }\n    };\n    /** 同步至最新的热更地址 */\n    HotUpdate.prototype.updateRemoteUrl = function (manifest, versionManifest) {\n        manifest.url1 = versionManifest.url1;\n        manifest.url2 = versionManifest.url2;\n        manifest.url3 = versionManifest.url3;\n        return manifest;\n    };\n    /** 降版本号 */\n    HotUpdate.prototype.downgrade = function (version) {\n        var _a = __read(version.split('.').map(Number), 3), major = _a[0], minor = _a[1], patch = _a[2];\n        if (patch > 0) {\n            patch--;\n        }\n        else if (minor > 0) {\n            minor--;\n            patch = 99;\n        }\n        else if (major > 0) {\n            major--;\n            minor = 99;\n            patch = 99;\n        }\n        return \"\".concat(major, \".\").concat(minor, \".\").concat(patch);\n    };\n    /** 更新缓存目录数据 */\n    HotUpdate.prototype.updateStorageData = function () {\n        var storageManifestUrl = this._storagePath + '/' + 'project.manifest';\n        this.log(\"\\u5F53\\u524D\\u7F13\\u5B58 project.manifest\\u7684url: \".concat(storageManifestUrl));\n        if (!jsb.fileUtils.isFileExist(storageManifestUrl)) {\n            this.log(\"project.manifest \\u6587\\u4EF6\\u4E0D\\u5B58\\u5728:\".concat(storageManifestUrl));\n            return;\n        }\n        var storageManifestStr = jsb.fileUtils.getStringFromFile(storageManifestUrl);\n        if (!storageManifestStr) {\n            this.log(\"project.manifest \\u6587\\u4EF6\\u8BFB\\u53D6\\u5931\\u8D25: \".concat(storageManifestUrl));\n            return;\n        }\n        this.log(\"project.manifest \\u6570\\u636E: \".concat(storageManifestStr));\n        var storageManifestJson = JSON.parse(storageManifestStr);\n        //同步缓存目录下的远程地址\n        this.updateRemoteUrl(storageManifestJson, this.remoteVersionManifest);\n        this.log(\"\\u540C\\u6B65\\u8FDC\\u7A0B\\u5730\\u5740\\u540E project.manifest \\u6570\\u636E: \".concat(JSON.stringify(storageManifestJson)));\n        var result = this.versionCompareHandle(storageManifestJson.version, this.remoteVersionManifest.version);\n        if (result > 0) {\n            storageManifestJson.version = this.downgrade(this.remoteVersionManifest.version);\n        }\n        try {\n            jsb.fileUtils.writeStringToFile(JSON.stringify(storageManifestJson), storageManifestUrl);\n        }\n        catch (error) {\n            this.log(\"\\u5199\\u5165\\u5931\\u8D25: \\u5730\\u5740 \".concat(storageManifestUrl, \"   \\u7F13\\u5B58\\u6570\\u636E\\uFF1A \").concat(JSON.stringify(storageManifestJson)));\n        }\n        if (CC_DEBUG) {\n            var tempStr = jsb.fileUtils.getStringFromFile(storageManifestUrl);\n            this.log(\"\\u6700\\u540E\\u5F97\\u5230\\u7684\\u7F13\\u5B58\\u76EE\\u5F55\\u4E0B\\u7684project\\u6570\\u636E:\".concat(tempStr));\n        }\n    };\n    HotUpdate.prototype.setStorage = function (key, value) {\n        if (typeof value === 'object') {\n            value = JSON.stringify(value);\n        }\n        cc.sys.localStorage.setItem(key, value);\n    };\n    HotUpdate.prototype.getStorage = function (key, defaultValue) {\n        if (defaultValue === void 0) { defaultValue = null; }\n        var value = cc.sys.localStorage.getItem(key);\n        if (!value) {\n            return defaultValue;\n        }\n        try {\n            return JSON.parse(value);\n        }\n        catch (_a) {\n            return value;\n        }\n    };\n    /**\n     * 开始热更 入口\n     * @param projectManifest  app 附带的 project.manifest\n     * @param remoteVersionManifest  远程服务器 version.manifest\n     * @returns\n     */\n    HotUpdate.prototype.startUpdate = function (projectManifest, remoteVersionManifest) {\n        if (!cc.sys.isNative) {\n            if (CC_DEBUG) {\n                this.log(\"startUpdate \\u4E0D\\u662F\\u539F\\u751F\\u73AF\\u5883: \".concat(cc.sys.isNative));\n            }\n            this._emit(exports.EventCodeMap[EventCodeEnum.FAIL], { eventCode: EventCodeEnum.FAIL, msg: '不是原生环境' });\n            return;\n        }\n        this._emit(exports.EventCodeMap[EventCodeEnum.START], { eventCode: EventCodeEnum.START, msg: '开始热更' });\n        this.remoteVersionManifest = remoteVersionManifest;\n        this._storagePath = (jsb.fileUtils ? jsb.fileUtils.getWritablePath() : '/') + 'blockBlastHotUpdate';\n        if (CC_DEBUG) {\n            this.log('缓存地址:' + this._storagePath);\n            this.log('远程 version 数据:', JSON.stringify(remoteVersionManifest));\n            this.log(\"\\u4FEE\\u6539\\u524D project \\u6570\\u636E \".concat(JSON.stringify(projectManifest)));\n        }\n        //app自身的manifest同步服务器下发的远程地址\n        this.updateRemoteUrl(projectManifest, remoteVersionManifest);\n        this.projectManifest = projectManifest;\n        this.projectManifestStr = JSON.stringify(projectManifest); //manifest 字符串\n        if (CC_DEBUG) {\n            this.log('修改后 project 数据:', this.projectManifestStr);\n        }\n        this.am = new jsb.AssetsManager('', this._storagePath, this.versionCompareHandle);\n        // Setup the verification callback, but we don't have md5 check function yet, so only print some message\n        // Return true if the verification passed, otherwise return false\n        this.am.setVerifyCallback(function (path, asset) {\n            // When asset is compressed, we don't need to check its md5, because zip file have been deleted.\n            var compressed = asset.compressed;\n            // Retrieve the correct md5 value.\n            var expectedMD5 = asset.md5;\n            // asset.path is relative path and path is absolute.\n            var relativePath = asset.path;\n            // The size of asset file, but this value could be absent.\n            var size = asset.size;\n            if (compressed) {\n                //this.msgLab.string = \"Verification passed : \" + relativePath;\n                return true;\n            }\n            else {\n                //this.msgLab.string = \"Verification passed:\" + relativePath + \"(\" + expectedMD5 + \")\";\n                return true;\n            }\n        });\n        if (CC_DEBUG) {\n            this.log(\"----project\\u6570\\u636E\\u3001\\u8FDC\\u7A0Bversion\\u6570\\u636E\\u3001\\u672C\\u5730\\u7F13\\u5B58\\u76EE\\u5F55\\u5DF2\\u51C6\\u5907\\u5B8C\\u6210----\");\n        }\n        this.checkUpdate();\n    };\n    /** 检查热更新 */\n    HotUpdate.prototype.checkUpdate = function () {\n        if (this.updating) {\n            if (CC_DEBUG) {\n                this.log('正在检查或更新。。。');\n            }\n            return;\n        }\n        if (CC_DEBUG) {\n            this.log(\"----\\u5F00\\u59CB\\u68C0\\u67E5\\u662F\\u5426\\u5B58\\u5728\\u65B0\\u7684\\u7248\\u672C\\u53F7----\");\n        }\n        //app版本号与缓存版本号比对前 修改\n        this.updateStorageData();\n        if (this.am.getState() === jsb.AssetsManager.State.UNINITED) {\n            if (CC_DEBUG) {\n                this.log(\"checkUpdate \\u672C\\u5730\\u6E05\\u5355\\u5C1A\\u672A\\u521D\\u59CB\\u5316\\uFF0C\\u5F00\\u59CB\\u521D\\u59CB\\u5316\");\n            }\n            var manifest = new jsb.Manifest(this.projectManifestStr, this._storagePath);\n            this.am.loadLocalManifest(manifest, this._storagePath);\n            var version = this.getStorage(exports.LOCAL_GAME_VERSION_KEY);\n            if (!version) {\n                version = this.am.getLocalManifest().getVersion();\n                this.setStorage(exports.LOCAL_GAME_VERSION_KEY, version);\n            }\n        }\n        if (CC_DEBUG) {\n            var manifestRoot = this.am.getLocalManifest().getManifestRoot();\n            var version = this.am.getLocalManifest().getVersion();\n            var packageUrl = this.am.getLocalManifest().getPackageUrl();\n            var manifestUrl = this.am.getLocalManifest().getManifestFileUrl();\n            var versionUrl = this.am.getLocalManifest().getVersionFileUrl();\n            this.log('checkUpdate ---检查本地manifest解析内容---');\n            this.log('checkUpdate manifest根目录:', manifestRoot);\n            this.log('checkUpdate 本地版本号:', version);\n            this.log('checkUpdate 远程资源根Url:', packageUrl);\n            this.log('checkUpdate 远程资源manifestUrl:', manifestUrl);\n            this.log('checkUpdate 远程资源versionUrl:', versionUrl);\n        }\n        if (!this.am.getLocalManifest() || !this.am.getLocalManifest().isLoaded()) {\n            if (CC_DEBUG) {\n                this.log('checkUpdate 本地清单加载失败...');\n            }\n            this._emit(exports.EventCodeMap[EventCodeEnum.FAIL], { eventCode: EventCodeEnum.FAIL, msg: '本地清单加载失败...' });\n            return;\n        }\n        if (CC_DEBUG) {\n            this.log(\"checkUpdate \\u672C\\u5730\\u6E05\\u5355\\u5DF2\\u7ECF\\u521D\\u59CB\\u5316\\u6210\\u529F\");\n        }\n        this.am.setEventCallback(this.checkCb.bind(this));\n        this.am.checkUpdate();\n        this.updating = true;\n    };\n    /** 检查热更回调 */\n    HotUpdate.prototype.checkCb = function (event) {\n        var _this = this;\n        var eventCode = event.getEventCode();\n        var eventCodeStr = exports.EventCodeMap[eventCode];\n        var isFailed = false;\n        var msg = '';\n        if (CC_DEBUG) {\n            this.log(\"checkCb \\u68C0\\u67E5\\u70ED\\u66F4\\u56DE\\u8C03\\u4E8B\\u4EF6\\u540D: \".concat(eventCode, \" \").concat(eventCodeStr));\n        }\n        switch (eventCode) {\n            case jsb.EventAssetsManager.ERROR_NO_LOCAL_MANIFEST:\n                isFailed = true;\n                msg = 'checkCb 未找到本地清单文件，跳过热更新。';\n                break;\n            case jsb.EventAssetsManager.ERROR_DOWNLOAD_MANIFEST:\n            case jsb.EventAssetsManager.ERROR_PARSE_MANIFEST:\n                isFailed = true;\n                msg = 'checkCb 下载清单文件失败，跳过热更新。';\n                break;\n            case jsb.EventAssetsManager.ALREADY_UP_TO_DATE:\n                isFailed = true;\n                msg = \"checkCb \\u5DF2\\u66F4\\u65B0\\u5230\\u6700\\u65B0\\u7684\\u8FDC\\u7A0B\\u7248\\u672C\";\n                break;\n            case jsb.EventAssetsManager.NEW_VERSION_FOUND:\n                if (CC_DEBUG) {\n                    this.log(\"checkCb \\u53D1\\u73B0\\u65B0\\u7248\\u672C,\\u9700\\u8981\\u4E0B\\u8F7D\\u5B57\\u8282\\u5927\\u5C0F \".concat(this.am.getTotalBytes(), \", \").concat(eventCode, \" \").concat(eventCodeStr));\n                }\n                this._emit(exports.EventCodeMap[EventCodeEnum.NEW_VERSION_FOUND], { eventCode: EventCodeEnum.NEW_VERSION_FOUND, msg: \"\\u53D1\\u73B0\\u65B0\\u7248\\u672C,\\u9700\\u8981\\u4E0B\\u8F7D\\u5B57\\u8282\\u5927\\u5C0F (' + \".concat(this.am.getTotalBytes(), \" + ')\"), jsbEvent: event });\n                setTimeout(function () {\n                    _this.hotUpdate();\n                }, 100);\n                break;\n            default:\n                return;\n        }\n        if (isFailed) {\n            this._emit(exports.EventCodeMap[EventCodeEnum.FAIL], { eventCode: EventCodeEnum.FAIL, msg: \"\".concat(msg, \":\").concat(eventCode, \" \").concat(eventCodeStr, \" \").concat(event.getMessage()), jsbEvent: event });\n        }\n        this.am.setEventCallback(null);\n        this.updating = false;\n    };\n    /** 开始下载资源 */\n    HotUpdate.prototype.hotUpdate = function () {\n        if (!this.am || this.updating) {\n            this.log('hotUpdate jsb.AssetsManager对象创建失败 或正在热更', this.am, this.updating);\n            this._emit(exports.EventCodeMap[EventCodeEnum.FAIL], { eventCode: EventCodeEnum.FAIL, msg: 'jsb.AssetsManager对象创建失败 或正在热更...' });\n            return;\n        }\n        if (CC_DEBUG) {\n            this.log('hotUpdate 检测到新版本 开始执行热更,下载assets资源');\n        }\n        if (this.am.getState() === jsb.AssetsManager.State.UNINITED) {\n            if (CC_DEBUG) {\n                this.log(\"updateCb \\u672C\\u5730\\u6E05\\u5355\\u8FD8\\u6CA1\\u52A0\\u8F7D\\uFF0C\\u91CD\\u65B0\\u52A0\\u8F7D\".concat(this.projectManifestStr, \" \").concat(this._storagePath));\n            }\n            var manifest = new jsb.Manifest(this.projectManifestStr, this._storagePath);\n            this.am.loadLocalManifest(manifest, this._storagePath);\n        }\n        if (!this.am.getLocalManifest() || !this.am.getLocalManifest().isLoaded()) {\n            if (CC_DEBUG) {\n                this.log('updateCb 本地清单加载失败...');\n            }\n            this._emit(exports.EventCodeMap[EventCodeEnum.FAIL], { eventCode: EventCodeEnum.FAIL, msg: '本地清单加载失败...' });\n            return;\n        }\n        if (CC_DEBUG) {\n            this.log('hotUpdate 本地清单初始化成功 检测到新版本 开始执行热更，');\n        }\n        this.am.setEventCallback(this.updateCb.bind(this));\n        this.currentFileNum = 0;\n        this.currentFileTotal = 0;\n        this.failCount = 0;\n        this.am.update();\n        this.updating = true;\n    };\n    /** 热更回调 */\n    HotUpdate.prototype.updateCb = function (event) {\n        var isUpdateFinished = false;\n        var isFailed = false;\n        var msg = '';\n        var eventCode = event.getEventCode();\n        var eventCodeStr = exports.EventCodeMap[eventCode];\n        if (CC_DEBUG) {\n            console.log(\"updateCb \\u70ED\\u66F4\\u56DE\\u8C03\\u4E8B\\u4EF6\\u540D: \".concat(eventCode, \" \").concat(eventCodeStr, \" \"));\n        }\n        switch (eventCode) {\n            case jsb.EventAssetsManager.ERROR_NO_LOCAL_MANIFEST:\n                isFailed = true;\n                msg = \"updateCb  \\u6CA1\\u627E\\u5230\\u627E\\u5230\\u672C\\u5730\\u6E05\\u5355\\u6587\\u4EF6\\uFF0C\\u8DF3\\u8FC7\\u70ED\\u66F4\\u65B0\\u3002\".concat(eventCode, \" \").concat(eventCodeStr);\n                break;\n            case jsb.EventAssetsManager.ASSET_UPDATED:\n                msg = \"updateCb \\u4E0B\\u8F7D\\u6210\\u529F\\u6587\\u4EF6: \".concat(eventCodeStr, \" \").concat(eventCode, \" \").concat(event.getAssetId(), \"}\");\n                break;\n            case jsb.EventAssetsManager.UPDATE_PROGRESSION:\n                var downloadFiles = event.getDownloadedFiles();\n                var totalFiles = event.getTotalFiles();\n                var downloadBytes = event.getDownloadedBytes();\n                var totalBytes = event.getTotalBytes();\n                this.currentFileNum = event.getDownloadedFiles();\n                this.currentFileTotal = event.getTotalFiles();\n                if (CC_DEBUG) {\n                    this.log(\"updateCb \\u4E0B\\u8F7D\\u8FDB\\u5EA6\\uFF08\\u6587\\u4EF6\\u6570\\uFF09\\uFF1A\".concat(downloadFiles, \"/\").concat(totalFiles, \" \").concat(eventCodeStr, \" \").concat(eventCode));\n                    this.log(\"updateCb \\u4E0B\\u8F7D\\u8FDB\\u5EA6\\uFF08\\u5B57\\u8282\\uFF09\".concat(downloadBytes, \"/\").concat(totalBytes, \" \").concat(eventCodeStr, \" \").concat(eventCode));\n                }\n                this._emit(exports.EventCodeMap[EventCodeEnum.UPDATE_PROGRESSION], { eventCode: eventCode, msg: \"\\u4E0B\\u8F7D\\u8FDB\\u5EA6\\uFF08\\u6587\\u4EF6\\u6570\\uFF09\\uFF1A\".concat(downloadFiles, \"/\").concat(totalFiles, \" \").concat(eventCodeStr, \" \").concat(eventCode), jsbEvent: event });\n                break;\n            case jsb.EventAssetsManager.ERROR_DOWNLOAD_MANIFEST:\n            case jsb.EventAssetsManager.ERROR_PARSE_MANIFEST:\n                isFailed = true;\n                msg = \"updateCb \\u4E0B\\u8F7D\\u6E05\\u5355\\u6587\\u4EF6\\u5931\\u8D25\\uFF0C\\u8DF3\\u8FC7\\u70ED\\u66F4\\u65B0\\u3002\".concat(eventCode, \" \").concat(eventCodeStr, \" \").concat(event.getMessage());\n                break;\n            case jsb.EventAssetsManager.ALREADY_UP_TO_DATE:\n                isFailed = true;\n                msg = \"updateCb \\u5DF2\\u66F4\\u65B0\\u5230\\u6700\\u65B0\\u7684\\u8FDC\\u7A0B\\u7248\\u672C\\u3002\".concat(eventCode, \" \").concat(eventCodeStr);\n                break;\n            case jsb.EventAssetsManager.UPDATE_FINISHED:\n                isUpdateFinished = true;\n                msg = \"updateCb \\u66F4\\u65B0\\u5DF2\\u5B8C\\u6210\\u3002\".concat(eventCodeStr, \" \").concat(eventCode, \" \").concat(event.getMessage());\n                break;\n            case jsb.EventAssetsManager.UPDATE_FAILED:\n                if (CC_DEBUG) {\n                    console.log('%c[热更新]', 'color:#fff;background:#6a1b9a;padding:2px 6px;border-radius:4px;', \"updateCb \\u66F4\\u65B0\\u5931\\u8D25:\".concat(event.getMessage(), \", ' \\u5DF2\\u4E0B\\u8F7D\\u6587\\u4EF6\\u6570:', \").concat(this.currentFileNum, \", ' \\u9700\\u8981\\u4E0B\\u8F7D\\u6587\\u4EF6\\u6570:', \").concat(this.currentFileTotal));\n                }\n                this.failCount++;\n                if (this.failCount <= 5) {\n                    this.am.downloadFailedAssets();\n                }\n                else {\n                    isFailed = true;\n                    msg = \"updateCb \\u5931\\u8D25\\u6B21\\u6570\\u8FC7\\u591A\\uFF0C\\u653E\\u5F03\\u91CD\\u65B0\\u4E0B\\u8F7D\\u5931\\u8D25\\u6587\\u4EF6 \".concat(eventCode, \" \").concat(eventCodeStr, \" \").concat(event.getMessage());\n                }\n                break;\n            case jsb.EventAssetsManager.ERROR_UPDATING:\n                try {\n                    //如果下载失败了，删除文件的临时路径\n                    if ((event.getMessage() || '').includes('(416)')) {\n                        // 416 means the server doesn't support range request, we need to remove the tmp file.\n                        jsb.fileUtils.removeFile(\"\".concat(this._storagePath, \"_temp/\").concat(event.getAssetId(), \".tmp\"));\n                    }\n                }\n                catch (error) {\n                    console.error('%c[热更新]', 'color:#fff;background:#6a1b9a;padding:2px 6px;border-radius:4px;', \"\\u5220\\u9664\\u4E34\\u65F6\\u6587\\u4EF6\\u53D1\\u751F\\u9519\\u8BEF:\".concat(eventCode, \" \").concat(eventCodeStr, \" \").concat(error, \" \"));\n                }\n                this.log(\"updateCb \\u8D44\\u6E90\\u66F4\\u65B0\\u9519\\u8BEF:\".concat(event.getAssetId(), \" \").concat(event.getMessage()));\n                break;\n            case jsb.EventAssetsManager.ERROR_DECOMPRESS:\n                if (CC_DEBUG) {\n                    console.log(\"updateCb \\u89E3\\u538B\\u7F29\\u5931\\u8D25:\".concat(eventCode, \" \").concat(eventCodeStr, \" \").concat(event.getMessage()));\n                }\n                break;\n            case jsb.EventAssetsManager.STOP_WHENUPDATE:\n                isFailed = true;\n                msg = \"updateCb STOP_WHENUPDATE:\".concat(eventCode, \" \").concat(eventCodeStr, \" \").concat(event.getMessage());\n                break;\n            default:\n                break;\n        }\n        if (CC_DEBUG) {\n            this.log('%c[热更新]', 'color:#fff;background:#6a1b9a;padding:2px 6px;border-radius:4px;', msg);\n        }\n        if (isFailed) {\n            this.am.setEventCallback(null);\n            this.updating = false;\n            this._emit(exports.EventCodeMap[EventCodeEnum.FAIL], { eventCode: EventCodeEnum.FAIL, msg: \"\".concat(msg, \" \").concat(event.getMessage()), jsbEvent: event });\n            return;\n        }\n        if (isUpdateFinished) {\n            this.am.setEventCallback(null);\n            this.updating = false;\n            /**\n             * 1，storagePath\n             * 2，storagePath + manifest中的searchPaths\n             */\n            var manifestSearchPaths = this.am.getLocalManifest().getSearchPaths();\n            manifestSearchPaths.push('@assets/');\n            cc.sys.localStorage.setItem('blockBlastHotUpdateData', JSON.stringify(manifestSearchPaths));\n            if (CC_DEBUG) {\n                this.log(\"updateCb \\u641C\\u7D22\\u8DEF\\u5F84\\uFF1A \".concat(cc.sys.localStorage.getItem('blockBlastHotUpdateData')));\n                this.log(\"updateCb \\u5DF2\\u66F4\\u65B0\\u5230\\u6700\\u65B0\\u7248\\u672C\\uFF0C\\u8BF7\\u91CD\\u542F\\uFF1A \".concat(cc.sys.localStorage.getItem('blockBlastHotUpdateData')));\n            }\n            this._emit(exports.EventCodeMap[EventCodeEnum.UPDATE_FINISHED], { eventCode: EventCodeEnum.UPDATE_FINISHED, msg: \"\\u66F4\\u65B0\\u6210\\u529F:\".concat(event.getMessage()), jsbEvent: event });\n        }\n    };\n    HotUpdate.prototype.log = function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        if (CC_DEBUG) {\n            var style = 'color:#fff;background:#6a1b9a;padding:2px 6px;border-radius:4px;';\n            console.log.apply(console, __spreadArray(['%c[热更新]', style], __read(args), false));\n        }\n    };\n    HotUpdate.prototype.error = function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        if (CC_DEBUG) {\n            var style = 'color:#fff;background:#6a1b9a;padding:2px 6px;border-radius:4px;';\n            console.error.apply(console, __spreadArray(['%c[热更新]', style], __read(args), false));\n        }\n    };\n    return HotUpdate;\n}());\nexports.hotUpdate = new HotUpdate();\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.http = exports.CrytoType = exports.HttpType = void 0;\nvar HttpType;\n(function (HttpType) {\n    HttpType[\"GET\"] = \"GET\";\n    HttpType[\"POST\"] = \"POST\";\n})(HttpType || (exports.HttpType = HttpType = {}));\n/** 加密类型 */\nvar CrytoType;\n(function (CrytoType) {\n    CrytoType[\"Url\"] = \"Url\";\n})(CrytoType || (exports.CrytoType = CrytoType = {}));\nvar Http = /** @class */ (function () {\n    function Http() {\n        this.callbackId = -1;\n        this.callbacks = {};\n    }\n    /**\n     * 请求（promise)\n     * @param url 请求地址\n     * @param data 请求数据（GET 与 POST 格式都为对象）\n     * @param option 请求选项\n     * @returns\n     */\n    Http.prototype.requestAsync = function (url, data, option) {\n        var _this = this;\n        return new Promise(function (c, e) {\n            _this.request(url, data, function (data) {\n                c(data);\n            }, function (err) {\n                e(err);\n            }, option);\n        });\n    };\n    /**\n     * 请求\n     * @param url 请求地址\n     * @param data 请求数据（GET 与 POST 格式都为对象）\n     * @param success 请求成功后\n     * @param fail 请求失败\n     * @param option 请求选项\n     */\n    Http.prototype.request = function (url, data, success, fail, option) {\n        // if (!url) {\n        //     fail(\"url is empty\");\n        //     return;\n        // }\n        // const paramKeys = Object.keys(data);\n        // let fullUrl = url;\n        // if (paramKeys.length > 0) {\n        //     let paramStr = '';\n        //     if (data) {\n        //         paramStr += `?${paramKeys.reduce((p, c) => p + '&' + c + '=' + data[c], '').slice(1)}`;\n        //         fullUrl = url + paramStr;\n        //     }\n        // }\n        // this.callbackId++;\n        // this.callbacks[this.callbackId] = { url: fullUrl, startTime: Date.now() };\n        // if (CC_DEBUG) {\n        //     console.log('%c[C->S]', \"color:#fff;background:#fa5151;\", this.callbackId, fullUrl, data);\n        // }\n        // let contentType = \"application/json\";\n        // if (option?.contentType) {\n        //     contentType = option.contentType;\n        // }\n        // let method = \"GET\";\n        // if (option?.type) {\n        //     method = option.type;\n        // }\n        // const headers = {\n        //     'Content-Type': contentType,\n        //     'CallbackId': this.callbackId.toString()\n        // };\n        // // try {\n        // //     var xhr = cc.loader.getXMLHttpRequest();\n        // //     xhr.open(\"POST\", fullUrl, true);\n        // //     // xhr.setRequestHeader(\"Content-Type\", localConTentTypeStr);\n        // //     xhr.setRequestHeader('Content-Type', contentType);\n        // //     xhr.setRequestHeader('CallbackId', this.callbackId.toString());\n        // //     xhr.onreadystatechange = function (resp) {\n        // //         if (xhr.readyState === 4) {\n        // //             let responseText = xhr.responseText;\n        // //             if (xhr.status >= 200 && xhr.status < 300) {\n        // //                 const response = JSON.parse(responseText);\n        // //                 success?.(response as Resp);\n        // //             }\n        // //         }\n        // //     };\n        // //     xhr.send(JSON.stringify(data));\n        // // } catch (error) {\n        // //     fail?.(error);\n        // // }\n        // var xhr = new XMLHttpRequest(), errInfo = 'download failed: ' + url + ', status: ';\n        // xhr.open(method, url, true);\n        // // xhr.withCredentials = false;\n        // // if (options.responseType !== undefined) xhr.responseType = options.responseType;\n        // // if (options.withCredentials !== undefined) xhr.withCredentials = options.withCredentials;\n        // // if (options.mimeType !== undefined && xhr.overrideMimeType) xhr.overrideMimeType(options.mimeType);\n        // // if (options.timeout !== undefined) xhr.timeout = options.timeout;\n        // if (headers) {\n        //     for (var header in headers) {\n        //         xhr.setRequestHeader(header, headers[header]);\n        //     }\n        // }\n        // xhr.onload = function () {\n        //     if (xhr.status === 200 || xhr.status === 0) {\n        //         let responseText = xhr.responseText;\n        //         if (xhr.status >= 200 && xhr.status < 300) {\n        //             const response = JSON.parse(responseText);\n        //             success?.(response as Resp);\n        //         }\n        //     } else {\n        //         // onComplete && onComplete(new Error(errInfo + xhr.status + '(no response)'));\n        //         fail?.(errInfo + xhr.status + '(no response)');\n        //     }\n        // };\n        // // if (onProgress) {\n        // //     xhr.onprogress = function (e) {\n        // //         if (e.lengthComputable) {\n        // //             onProgress(e.loaded, e.total);\n        // //         }\n        // //     };\n        // // }\n        // xhr.onerror = function () {\n        //     // onComplete && onComplete(new Error(errInfo + xhr.status + '(error)'));\n        //     fail?.(errInfo + xhr.status + '(no error)');\n        // };\n        // xhr.ontimeout = function () {\n        //     // onComplete && onComplete(new Error(errInfo + xhr.status + '(time out)'));\n        //     fail?.(errInfo + xhr.status + '(time out)');\n        // };\n        // xhr.onabort = function () {\n        //     // onComplete && onComplete(new Error(errInfo + xhr.status + '(abort)'));\n        //     fail?.(errInfo + xhr.status + '(abort)');\n        // };\n        // xhr.send(JSON.stringify(data));\n        // // window['axios']({\n        // //     method,\n        // //     url: fullUrl,\n        // //     // baseURL: apiHost,\n        // //     data: method === 'POST' ? data : null,\n        // //     params: method === 'GET' ? data : null,\n        // //     withCredentials: false,\n        // //     headers\n        // // }).then(response => {\n        // //     const { headers: responseHeaders } = response.config;\n        // //     const responseCallbackId = responseHeaders.CallbackId;\n        // //     const callbackInfo = this.callbacks[responseCallbackId];\n        // //     if (CC_DEBUG) {\n        // //         console.log(`%c[S->C]`, \"color:#ffffff;background:#4b973d;\", responseCallbackId, `${Date.now() - callbackInfo.startTime}ms`, callbackInfo.url, response.data, JSON.stringify(response.data));\n        // //     }\n        // //     success?.(response.data);\n        // // }).catch(err => {\n        // //     fail?.(err);\n        // // });\n        var method = \"GET\";\n        if (option === null || option === void 0 ? void 0 : option.type) {\n            method = option.type;\n        }\n        var xhr = cc.loader.getXMLHttpRequest();\n        // 将参数转换为 JSON 字符串\n        var dataStr = JSON.stringify(data);\n        xhr.open(method, url, true);\n        var contentType = \"application/json\";\n        if (option === null || option === void 0 ? void 0 : option.contentType) {\n            contentType = option.contentType;\n        }\n        // 设置请求头为 application/json\n        xhr.setRequestHeader(\"Content-Type\", contentType);\n        xhr.onreadystatechange = function () {\n            if (xhr.readyState === 4) {\n                var response = xhr.responseText;\n                if (xhr.status >= 200 && xhr.status < 300) {\n                    // 响应解密\n                    if (option.crypto) {\n                        response = option.crypto['decrypt'](response);\n                    }\n                    success(JSON.parse(response));\n                }\n                else {\n                    fail(response);\n                }\n            }\n        };\n        // 参数加密\n        if (option.crypto) {\n            dataStr = \"params=\" + option.crypto['encrypt'](dataStr);\n        }\n        // 发送 JSON 数据\n        xhr.send(dataStr);\n    };\n    return Http;\n}());\nexports.http = new Http();\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.interval = interval;\n/**\n * 一些特殊情况下，如果切换了场景，interval 还会继续执行，而如果继续执行，有可能因为回调函数里有对当前节点的引用，会导致报错\n * 这里可以通过 判断该对象是否有效来兼容，取代 cc.tween.delay 这种较重的 api，提升整体性能\n *\n * 注意：如果单纯想不受场景销毁影响，不要用这个，使用原生 setInterval 即可\n * @param node\n * @param callback\n * @param timeout\n * @param args\n */\nfunction interval(node, callback, timeout) {\n    var args = [];\n    for (var _i = 3; _i < arguments.length; _i++) {\n        args[_i - 3] = arguments[_i];\n    }\n    var id = setInterval(function () {\n        if (!node || !cc.isValid(node, true)) {\n            clearInterval(id);\n            return;\n        }\n        callback === null || callback === void 0 ? void 0 : callback.apply(this, [args]);\n    }, timeout, args);\n    return id;\n}\n", "\"use strict\";\n/**\n * 游戏层级管理\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.layerManager = exports.LayerManager = exports.gameUiLayer = exports.BASE_LAYER_CONFIGS = void 0;\nexports.initNodeConfig = initNodeConfig;\nexports.addWidget = addWidget;\n/** 基础层级配置 */\nexports.BASE_LAYER_CONFIGS = [\n    { type: 'gameUI', name: 'gameUiLayer', zIndex: 2 }\n];\n/** 基础游戏UI层级 */\nexports.gameUiLayer = new cc.Node();\n/**\n * 初始化节点配置\n * @param node 目标节点\n * @param config 配置信息\n * @param scene 场景节点\n */\nfunction initNodeConfig(node, config, scene) {\n    node.name = config.name;\n    node.zIndex = config.zIndex;\n    if (config.opacity !== undefined) {\n        node.opacity = config.opacity;\n    }\n    if (config.hidden) {\n        node.active = false;\n    }\n    scene.addChild(node);\n    addWidget(node);\n}\n/**\n * 层级管理器类\n */\nvar LayerManager = /** @class */ (function () {\n    function LayerManager() {\n        this.layerMap = new Map();\n        this.scene = null;\n    }\n    /**\n     * 初始化层级管理器\n     */\n    LayerManager.prototype.init = function () {\n        var _this = this;\n        this.scene = cc.Canvas.instance.node;\n        this.clear();\n        // 初始化基础层级\n        exports.BASE_LAYER_CONFIGS.forEach(function (config) {\n            if (config.type === 'gameUI') {\n                initNodeConfig(exports.gameUiLayer, config, _this.scene);\n                _this.layerMap.set(config.type, exports.gameUiLayer);\n            }\n        });\n    };\n    Object.defineProperty(LayerManager.prototype, \"layerScene\", {\n        get: function () {\n            return this.scene;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * 清理所有层级\n     */\n    LayerManager.prototype.clear = function () {\n        this.layerMap.forEach(function (node) {\n            node.removeFromParent();\n            node.destroy();\n        });\n        this.layerMap.clear();\n    };\n    /**\n     * 添加新层级\n     * @param config 层级配置\n     */\n    LayerManager.prototype.addLayer = function (config) {\n        if (!this.scene) {\n            throw new Error('LayerManager not initialized');\n        }\n        // 如果已存在同类型层级，先移除\n        this.removeLayer(config.type);\n        var node = new cc.Node();\n        initNodeConfig(node, config, this.scene);\n        this.layerMap.set(config.type, node);\n        return node;\n    };\n    /**\n     * 添加预定义的层级节点\n     * @param type 层级类型\n     * @param node 预定义的节点\n     */\n    LayerManager.prototype.setLayerNode = function (type, node) {\n        if (!this.scene) {\n            throw new Error('LayerManager not initialized');\n        }\n        // 如果已存在同类型层级，先移除\n        this.removeLayer(type);\n        // 设置新节点\n        this.layerMap.set(type, node);\n    };\n    /**\n     * 移除层级\n     * @param type 层级类型\n     */\n    LayerManager.prototype.removeLayer = function (type) {\n        var layer = this.layerMap.get(type);\n        if (layer) {\n            layer.removeFromParent();\n            layer.destroy();\n            this.layerMap.delete(type);\n            return true;\n        }\n        return false;\n    };\n    /**\n     * 获取层级节点\n     * @param type 层级类型\n     */\n    LayerManager.prototype.getLayer = function (type) {\n        return this.layerMap.get(type);\n    };\n    /**\n     * 显示层级\n     * @param type 层级类型\n     */\n    LayerManager.prototype.showLayer = function (type) {\n        var layer = this.layerMap.get(type);\n        if (layer) {\n            layer.active = true;\n        }\n    };\n    /**\n     * 隐藏层级\n     * @param type 层级类型\n     */\n    LayerManager.prototype.hideLayer = function (type) {\n        var layer = this.layerMap.get(type);\n        if (layer) {\n            layer.active = false;\n        }\n    };\n    /**\n     * 设置层级透明度\n     * @param type 层级类型\n     * @param opacity 透明度(0-255)\n     */\n    LayerManager.prototype.setLayerOpacity = function (type, opacity) {\n        var layer = this.layerMap.get(type);\n        if (layer) {\n            layer.opacity = opacity;\n        }\n    };\n    /**\n     * 获取所有层级类型\n     */\n    LayerManager.prototype.getLayerTypes = function () {\n        return Array.from(this.layerMap.keys());\n    };\n    return LayerManager;\n}());\nexports.LayerManager = LayerManager;\n// 导出单例实例\nexports.layerManager = new LayerManager();\n/**\n * 添加Widget组件到节点\n * @param node 目标节点\n */\nfunction addWidget(node) {\n    node.width = 960;\n    node.height = 1707.5;\n    node.anchorX = node.anchorY = 0;\n    var widget = node.addComponent(cc.Widget);\n    widget.isAlignTop = true;\n    widget.isAlignLeft = true;\n    widget.isAlignRight = true;\n    widget.isAlignBottom = true;\n    widget.top = 0;\n    widget.bottom = 0;\n    widget.left = 0;\n    widget.right = 0;\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ResLoader = exports.BundleName = void 0;\n/**bundle类型 */\nvar BundleName;\n(function (BundleName) {\n    BundleName[\"class\"] = \"class\";\n    BundleName[\"chapter\"] = \"chapter\";\n    BundleName[\"tool\"] = \"tool\";\n})(BundleName || (exports.BundleName = BundleName = {}));\nvar ResLoader = /** @class */ (function () {\n    function ResLoader() {\n    }\n    /**\n     * 添加资源事件监听\n     * @param event\n     * @param callback\n     */\n    ResLoader.addEventListener = function (event, callback) {\n        if (!this.events[event]) {\n            this.events[event] = [];\n        }\n        this.events[event].push(callback);\n    };\n    /**\n     * 是否为远程地址，通过 http(s)://前缀来判断\n     * @param url\n     * @returns\n     */\n    ResLoader.isRemote = function (url) {\n        return /^(https?:\\/\\/)/.test(url);\n    };\n    ResLoader.getAsset = function (url) {\n        var _a;\n        return (_a = this.cacheResources[url]) === null || _a === void 0 ? void 0 : _a.asset;\n    };\n    /**\n     * 高效加载资源:\n     * （1）：多请求完成后分发\n     * （2）：支持远程支持加载（web 端可能产生跨域问题）\n     * （3）：远程资源不需要传 type\n     * （4）：目前远程支持暂时不支持重试次数\n     * @param paths\n     * @param type\n     * @param onComplete\n     */\n    ResLoader.load = function (paths, type, onComplete) {\n        if (!this.cacheResources[paths]) {\n            this.cacheResources[paths] = { onCompletes: onComplete ? [onComplete] : [] };\n            var self_1 = this;\n            var _complete = function (err, asset) {\n                if (err) {\n                    cc.error(\"\\u8D44\\u6E90\\u52A0\\u8F7D\\u53D1\\u751F\\u9519\\u8BEF\\uFF1A\", err);\n                }\n                self_1.cacheResources[paths].asset = asset;\n                var onCompletes = self_1.cacheResources[paths].onCompletes;\n                for (var i_1 = 0; i_1 < (onCompletes === null || onCompletes === void 0 ? void 0 : onCompletes.length); i_1++) {\n                    var onCompleteItem = onCompletes[i_1];\n                    onCompleteItem.apply(this, [err, asset]);\n                }\n                self_1.cacheResources[paths].onCompletes.length = 0;\n                var loadEvents = self_1.events[\"load\"];\n                for (var i_2 = 0; i_2 < (loadEvents === null || loadEvents === void 0 ? void 0 : loadEvents.length); i_2++) {\n                    var event_1 = loadEvents[i_2];\n                    event_1.apply(self_1, [\"resources\", paths]);\n                }\n            };\n            if (!this.isRemote(paths)) {\n                cc.resources.load(paths, type, _complete);\n            }\n            else {\n                cc.assetManager.loadRemote(paths, _complete);\n            }\n        }\n        else {\n            if (this.cacheResources[paths].asset) {\n                if (this.cacheResources[paths].asset.isValid) {\n                    if (onComplete) {\n                        onComplete.apply(this, [null, this.cacheResources[paths].asset]);\n                    }\n                }\n                else {\n                    delete this.cacheResources[paths];\n                    this.load(paths, type, onComplete);\n                }\n            }\n            else {\n                if (onComplete) {\n                    this.cacheResources[paths].onCompletes.push(onComplete);\n                }\n            }\n        }\n    };\n    /**\n     * 通过 promise 方式进行资源加载\n     * @param paths\n     * @param type\n     * @returns\n     */\n    ResLoader.asyncLoad = function (paths, type) {\n        var _this = this;\n        return new Promise(function (c, e) {\n            _this.load(paths, type, function (error, assets) {\n                if (error) {\n                    e(error);\n                    return;\n                }\n                c(assets);\n            });\n        });\n    };\n    /**\n     * 根据 bunlde 加载资源\n     * @param bundleName\n     * @param paths\n     * @param type\n     * @param onComplete\n     */\n    ResLoader.loadByBundle = function (bundleName, paths, type, onComplete) {\n        var _this = this;\n        this.loadBundle(bundleName, function (err, bundle) {\n            if (err) {\n                console.error(\"bundle:\".concat(bundleName, \" \\u8D44\\u6E90\\u52A0\\u8F7D\\u9519\\u8BEF\\uFF1A\"), err);\n                if (onComplete) {\n                    onComplete.apply(_this, [err, null]);\n                }\n                return;\n            }\n            var key = bundleName + \"_\" + paths;\n            if (!_this.cacheResources[key]) {\n                _this.cacheResources[key] = { onCompletes: onComplete ? [onComplete] : [] };\n                bundle.load(paths, type, function (err, res) {\n                    _this.cacheResources[key].asset = res;\n                    var onCompletes = _this.cacheResources[key].onCompletes;\n                    for (var i_3 = 0; i_3 < (onCompletes === null || onCompletes === void 0 ? void 0 : onCompletes.length); i_3++) {\n                        var onCompleteItem = onCompletes[i_3];\n                        onCompleteItem.apply(_this, [err, res]);\n                    }\n                    var loadEvents = _this.events[\"load\"];\n                    for (var i_4 = 0; i_4 < (loadEvents === null || loadEvents === void 0 ? void 0 : loadEvents.length); i_4++) {\n                        var event_2 = loadEvents[i_4];\n                        event_2.apply(_this, [bundleName, paths]);\n                    }\n                });\n            }\n            else {\n                if (_this.cacheResources[key].asset) {\n                    if (_this.cacheResources[key].asset.isValid) {\n                        if (onComplete) {\n                            onComplete.apply(_this, [null, _this.cacheResources[key].asset]);\n                        }\n                    }\n                    else {\n                        delete _this.cacheResources[key];\n                        _this.loadByBundle(bundleName, paths, type, onComplete);\n                    }\n                }\n                else {\n                    if (onComplete) {\n                        _this.cacheResources[key].onCompletes.push(onComplete);\n                    }\n                }\n            }\n        });\n    };\n    /**\n     * 通过 promise 方式并基于 bundle 加载资源\n     * @param bundleName\n     * @param paths\n     * @param type\n     * @returns\n     */\n    ResLoader.asyncLoadByBundle = function (bundleName, paths, type) {\n        var _this = this;\n        return new Promise(function (c, e) {\n            _this.loadByBundle(bundleName, paths, type, function (err, asset) {\n                if (err) {\n                    e(err);\n                    return;\n                }\n                c(asset);\n            });\n        });\n    };\n    /**\n     * 高性能，在未加载完成时，多次请求加载，不会触发多次加载\n     * @param nameOrUrl\n     * @param onComplete\n     */\n    ResLoader.loadBundle = function (nameOrUrl, onComplete) {\n        var self = this;\n        if (!this.cacheResources[nameOrUrl]) {\n            this.cacheResources[nameOrUrl] = { onCompletes: onComplete ? [onComplete] : [] };\n            cc.assetManager.loadBundle(nameOrUrl, function (err, bundle) {\n                self.cacheResources[nameOrUrl].asset = bundle;\n                var onCompletes = self.cacheResources[nameOrUrl].onCompletes;\n                for (var i_5 = 0; i_5 < (onCompletes === null || onCompletes === void 0 ? void 0 : onCompletes.length); i_5++) {\n                    var onCompleteItem = onCompletes[i_5];\n                    onCompleteItem.apply(this, [err, bundle]);\n                }\n                self.cacheResources[nameOrUrl].onCompletes.length = 0;\n            });\n        }\n        else {\n            if (this.cacheResources[nameOrUrl].asset) {\n                if (onComplete) {\n                    onComplete.apply(this, [null, this.cacheResources[nameOrUrl].asset]);\n                }\n            }\n            else {\n                if (onComplete) {\n                    this.cacheResources[nameOrUrl].onCompletes.push(onComplete);\n                }\n            }\n        }\n    };\n    /**\n     * 通过 bundle 预加载场景\n     * @param bundle\n     * @param sceneName\n     * @param onComplete\n     */\n    ResLoader.bundlePreloadScene = function (bundle, sceneName, onComplete) {\n        var self = this;\n        var key = \"preloadScene\" + sceneName;\n        if (!this.cacheResources[key]) {\n            this.cacheResources[key] = { onCompletes: onComplete ? [onComplete] : [] };\n            bundle.preloadScene(sceneName, function (err) {\n                var onCompletes = self.cacheResources[key].onCompletes;\n                self.cacheResources[key]['__complete__'] = true;\n                for (var i_6 = 0; i_6 < (onCompletes === null || onCompletes === void 0 ? void 0 : onCompletes.length); i_6++) {\n                    var onCompleteItem = onCompletes[i_6];\n                    onCompleteItem.apply(this, [err]);\n                }\n                self.cacheResources[key].onCompletes.length = 0;\n                delete self.cacheResources[key]['__complete__'];\n            });\n        }\n        else {\n            if (self.cacheResources[key]['__complete__']) {\n                if (onComplete) {\n                    onComplete.apply(this, [null]);\n                }\n            }\n            else {\n                if (onComplete) {\n                    this.cacheResources[key].onCompletes.push(onComplete);\n                }\n            }\n        }\n    };\n    /**\n     * 通过 bundle 预加载场景\n     * @param bundle\n     * @param sceneName\n     * @param onComplete\n     */\n    ResLoader.bundlePreload = function (bundle, paths, type, onComplete) {\n        var self = this;\n        var key = \"preload\" + paths;\n        if (!this.cacheResources[key]) {\n            this.cacheResources[key] = { onCompletes: onComplete ? [onComplete] : [] };\n            bundle.preload(paths, type, function (error, items) {\n                var onCompletes = self.cacheResources[key].onCompletes;\n                self.cacheResources[key]['__items__'] = items;\n                for (var i_7 = 0; i_7 < (onCompletes === null || onCompletes === void 0 ? void 0 : onCompletes.length); i_7++) {\n                    var onCompleteItem = onCompletes[i_7];\n                    onCompleteItem.apply(this, [error, items]);\n                }\n                self.cacheResources[key].onCompletes.length = 0;\n            });\n        }\n        else {\n            if (self.cacheResources[key]['__items__']) {\n                if (onComplete) {\n                    onComplete.apply(this, [null, self.cacheResources[key]['__items__']]);\n                }\n            }\n            else {\n                if (onComplete) {\n                    this.cacheResources[key].onCompletes.push(onComplete);\n                }\n            }\n        }\n    };\n    /**\n     * 渲染 sprite\n     * @param sprite\n     * @param url\n     */\n    ResLoader.renderSprite = function (sprite, url) {\n        this.load(url, cc.SpriteFrame, function (err, asset) {\n            if (err) {\n                return;\n            }\n            sprite.spriteFrame = asset;\n        });\n    };\n    /**\n     * 渲染龙骨动画\n     * @param dragonBonesArmatureDisplay\n     * @param dragonAssetUrl\n     * @param dragonAssetAtlasUrl\n     * @param armatureName\n     * @param animName\n     * @param playtimes\n     */\n    ResLoader.renderDragonbones = function (dragonBonesArmatureDisplay, dragonAssetUrl, dragonAssetAtlasUrl, armatureName, animName, playtimes) {\n        var _this = this;\n        if (playtimes === void 0) { playtimes = 1; }\n        var loadCount = 0;\n        var play = function () {\n            if (loadCount === 2) {\n                dragonBonesArmatureDisplay.addEventListener(dragonBones.EventObject.COMPLETE, function (e) {\n                    var _a, _b;\n                    if (CC_DEBUG) {\n                        console.log(\"renderDragonbones 动画播放结束,url\", dragonAssetUrl);\n                    }\n                    if ((_a = dragonBonesArmatureDisplay.node) === null || _a === void 0 ? void 0 : _a.parent) {\n                        (_b = dragonBonesArmatureDisplay.node) === null || _b === void 0 ? void 0 : _b.parent.removeChild(dragonBonesArmatureDisplay.node);\n                    }\n                }, _this);\n                dragonBonesArmatureDisplay.armatureName = armatureName;\n                dragonBonesArmatureDisplay.playAnimation(animName, playtimes);\n            }\n        };\n        this.load(dragonAssetUrl, dragonBones.DragonBonesAsset, function (err, asset) {\n            if (err) {\n                return;\n            }\n            dragonBonesArmatureDisplay.dragonAsset = asset;\n            loadCount++;\n            play();\n        });\n        this.load(dragonAssetAtlasUrl, dragonBones.DragonBonesAtlasAsset, function (err, asset) {\n            if (err) {\n                return;\n            }\n            dragonBonesArmatureDisplay.dragonAtlasAsset = asset;\n            loadCount++;\n            play();\n        });\n    };\n    /**\n    * 渲染bundle下龙骨动画\n    * @param bundleName\n    * @param dragonBonesArmatureDisplay\n    * @param dragonAssetUrl\n    * @param dragonAssetAtlasUrl\n    * @param armatureName\n    * @param animName\n    * @param playtimes\n    * @param completeRemove\n    */\n    ResLoader.renderDragonbonesByBundle = function (bundleName, dragonBonesArmatureDisplay, dragonAssetUrl, dragonAssetAtlasUrl, armatureName, animName, playtimes, completeRemove) {\n        var _this = this;\n        if (playtimes === void 0) { playtimes = 1; }\n        if (completeRemove === void 0) { completeRemove = true; }\n        this.loadBundle(bundleName, function (err, bundle) {\n            if (err) {\n                return;\n            }\n            var loadCount = 0;\n            var play = function () {\n                if (loadCount === 2) {\n                    dragonBonesArmatureDisplay.addEventListener(dragonBones.EventObject.COMPLETE, function (e) {\n                        var _a, _b;\n                        if (completeRemove && ((_a = dragonBonesArmatureDisplay.node) === null || _a === void 0 ? void 0 : _a.parent)) {\n                            if (CC_DEBUG) {\n                                console.log(\"renderDragonbonesByBundle 动画播放结束,url\", dragonAssetUrl);\n                            }\n                            (_b = dragonBonesArmatureDisplay.node) === null || _b === void 0 ? void 0 : _b.parent.removeChild(dragonBonesArmatureDisplay.node);\n                        }\n                    }, _this);\n                    dragonBonesArmatureDisplay.armatureName = armatureName;\n                    dragonBonesArmatureDisplay.playAnimation(animName, playtimes);\n                }\n            };\n            bundle.load(dragonAssetUrl, dragonBones.DragonBonesAsset, function (err, asset) {\n                if (err) {\n                    return;\n                }\n                dragonBonesArmatureDisplay.dragonAsset = asset;\n                loadCount++;\n                play();\n            });\n            bundle.load(dragonAssetAtlasUrl, dragonBones.DragonBonesAtlasAsset, function (err, asset) {\n                if (err) {\n                    return;\n                }\n                dragonBonesArmatureDisplay.dragonAtlasAsset = asset;\n                loadCount++;\n                play();\n            });\n        });\n    };\n    ResLoader.events = {};\n    ResLoader.cacheResources = {};\n    return ResLoader;\n}());\nexports.ResLoader = ResLoader;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.NativeBridge = exports.ANDROID_ACTIVE = void 0;\nexports.ANDROID_ACTIVE = \"org/cocos2dx/javascript/AppActivity\";\n/**\n * 与原生交互\n * 目前只处理 Android\n */\nvar NativeBridge = /** @class */ (function () {\n    function NativeBridge() {\n    }\n    /**\n     * 是否为原生平台\n     * @returns\n     */\n    NativeBridge.isNative = function () {\n        var _a;\n        return (_a = cc.sys) === null || _a === void 0 ? void 0 : _a.isNative;\n    };\n    /**\n     * 与原生交互\n     * @param methodName\n     * @param data\n     * @param methodSignature\n     * @param className TOOD: className 默认为 ANDROID_ACTIVE,等待 Native 层统一后去掉\n     * @returns\n     */\n    NativeBridge.send = function (methodName, data, methodSignature, className) {\n        if (className === void 0) { className = exports.ANDROID_ACTIVE; }\n        if (!this.isNative()) {\n            return;\n        }\n        //jsb.reflection.callStaticMethod(className, methodName, methodSignature, data == \"\" ? undefined : data);\n        if (data == \"\") {\n            return jsb.reflection.callStaticMethod(className, methodName, methodSignature);\n        }\n        else {\n            return jsb.reflection.callStaticMethod(className, methodName, methodSignature, data);\n        }\n    };\n    return NativeBridge;\n}());\nexports.NativeBridge = NativeBridge;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.randomInt = randomInt;\nexports.randomFloat = randomFloat;\n/**\n * 生成指定范围内的随机整数\n * @param min 最小值\n * @param max 最大值\n * @returns 随机整数\n */\nfunction randomInt(min, max) {\n    return Math.floor(Math.random() * (max - min)) + min;\n}\n/**\n * 生成指定范围内的随机数\n * @param min 最小值\n * @param max 最大值\n * @returns 随机数\n */\nfunction randomFloat(min, max) {\n    return Math.random() * (max - min) + min;\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.executeRenderingOptimize = executeRenderingOptimize;\n/**\n * 此类主要目的是执行渲染优化，结合全屏装饰器标记停止被遮挡结点的渲染\n * 以达到在某些特殊情况下降低drawcall,并降低耗电量等\n */\nfunction executeRenderingOptimize() {\n    if (!CC_EDITOR) {\n        Object.defineProperty(cc.Node.prototype, 'active', {\n            get: function () {\n                return this._active;\n            },\n            set: function (value) {\n                value = !!value;\n                //执行绑定FullScreen装饰器的组件的处理\n                executeFullScreenComponent(this, value);\n                if (this._active !== value) {\n                    this._active = value;\n                    var parent = this._parent;\n                    if (parent) {\n                        var couldActiveInScene = parent._activeInHierarchy;\n                        if (couldActiveInScene) {\n                            cc.director['_nodeActivator'].activateNode(this, value);\n                        }\n                    }\n                }\n            }\n        });\n    }\n}\nfunction executeFullScreenComponent(self, value) {\n    //如果当前激活了，则其它层设置为不激活\n    var hasFullScreenComponent = self['_components'] && self['_components'].find(function (v) { return v.__fullscreen__; });\n    self.opacity = 255;\n    //全屏面板下处理其它层次面板激活\n    if (hasFullScreenComponent) {\n        if (value) {\n            hasFullScreenComponent['__inactives__'] = [];\n            if (self.parent) {\n                var hide_1 = function (target) {\n                    if (target.parent instanceof cc.Scene)\n                        return;\n                    if (target.parent) {\n                        if (target.parent.children) {\n                            var index = target.parent.children.indexOf(target);\n                            //隐藏层级低的\n                            var lowLevelChildren = target.parent.children.slice(0, index);\n                            lowLevelChildren.forEach(function (v) {\n                                if (v.opacity > 0 && v.active) {\n                                    hasFullScreenComponent['__inactives__'].push(v);\n                                }\n                                v.opacity = 0;\n                            });\n                        }\n                        //再查找父容器\n                        hide_1(target.parent);\n                    }\n                };\n                hide_1(self);\n            }\n        }\n        else {\n            //还原其它面板激活\n            var __inactives__ = hasFullScreenComponent['__inactives__'];\n            if (__inactives__) {\n                for (var i_1 = 0; i_1 < __inactives__.length; i_1++) {\n                    __inactives__[i_1].opacity = 255;\n                }\n                hasFullScreenComponent['__inactives__'].length = 0;\n            }\n        }\n    }\n    return hasFullScreenComponent;\n}\n", "\"use strict\";\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __generator = (this && this.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\n    return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ObjectPool = void 0;\n/**\n * 对象池\n * 一般用于实例化对象\n * 关注：创建与恢复，恢复的目的是保证对象使用的时候为初始化状态，避免创建对象后因为修改对象属性，导致再次使用时产生副作用\n * （1）：支持初始化池大小\n * （2）：支持异步池，一些高性能场景需要通过异步池处理\n */\nvar ObjectPool = /** @class */ (function () {\n    /**\n     * 池需具备创建与恢复功能\n     * @param _create\n     * @param _reset\n     * @param _option\n     */\n    function ObjectPool(_create, _reset, _option) {\n        this._create = _create;\n        this._reset = _reset;\n        this._option = _option;\n        this.pool = [];\n        this.inUsed = new Set();\n        this._init();\n    }\n    ObjectPool.prototype._init = function () {\n        return __awaiter(this, void 0, void 0, function () {\n            var _a, size, args, onCreateComplete, obj, error_1;\n            return __generator(this, function (_b) {\n                switch (_b.label) {\n                    case 0:\n                        if (!this._option) return [3 /*break*/, 6];\n                        _a = this._option, size = _a.size, args = _a.args, onCreateComplete = _a.onCreateComplete;\n                        if (isNaN(size))\n                            size = 1;\n                        _b.label = 1;\n                    case 1:\n                        _b.trys.push([1, 5, , 6]);\n                        _b.label = 2;\n                    case 2:\n                        if (!(this.pool.length < size)) return [3 /*break*/, 4];\n                        return [4 /*yield*/, this._create(args)];\n                    case 3:\n                        obj = _b.sent();\n                        this.inUsed.add(obj);\n                        this.pool.push(obj);\n                        return [3 /*break*/, 2];\n                    case 4:\n                        if (onCreateComplete) {\n                            onCreateComplete(null);\n                        }\n                        return [3 /*break*/, 6];\n                    case 5:\n                        error_1 = _b.sent();\n                        if (onCreateComplete) {\n                            onCreateComplete(error_1);\n                        }\n                        return [3 /*break*/, 6];\n                    case 6: return [2 /*return*/];\n                }\n            });\n        });\n    };\n    Object.defineProperty(ObjectPool.prototype, \"poolSize\", {\n        /**\n         * 池大小\n         */\n        get: function () {\n            return this.pool.length;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * 当前对象是否在使用中\n     * @param obj\n     * @returns\n     */\n    ObjectPool.prototype.isInUsed = function (obj) {\n        return this.inUsed.has(obj);\n    };\n    /**\n     * 从池中获取一个对象\n     */\n    ObjectPool.prototype.get = function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        return __awaiter(this, void 0, void 0, function () {\n            var obj;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!(this.pool.length > 0)) return [3 /*break*/, 1];\n                        obj = this.pool.pop();\n                        this._reset(obj);\n                        return [3 /*break*/, 3];\n                    case 1: return [4 /*yield*/, this._create(args)];\n                    case 2:\n                        obj = _a.sent();\n                        _a.label = 3;\n                    case 3:\n                        this.inUsed.add(obj);\n                        return [2 /*return*/, obj];\n                }\n            });\n        });\n    };\n    /**\n     * 从池中异步取出一个对象\n     * @param args\n     * @returns\n     */\n    ObjectPool.prototype.asyncGet = function () {\n        var _this = this;\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        return new Promise(function (resolve, reject) { return __awaiter(_this, void 0, void 0, function () {\n            var obj, error_2;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!(this.pool.length > 0)) return [3 /*break*/, 1];\n                        obj = this.pool.pop();\n                        this._reset(obj);\n                        this.inUsed.add(obj);\n                        resolve(obj);\n                        return [3 /*break*/, 4];\n                    case 1:\n                        _a.trys.push([1, 3, , 4]);\n                        return [4 /*yield*/, this._create(args)];\n                    case 2:\n                        obj = _a.sent();\n                        this.inUsed.add(obj);\n                        resolve(obj);\n                        return [3 /*break*/, 4];\n                    case 3:\n                        error_2 = _a.sent();\n                        reject(error_2);\n                        return [3 /*break*/, 4];\n                    case 4: return [2 /*return*/];\n                }\n            });\n        }); });\n    };\n    /**\n     * 释放\n     * @param obj\n     */\n    ObjectPool.prototype.release = function (obj) {\n        if (this.inUsed.has(obj)) {\n            this.inUsed.delete(obj);\n            this.pool.push(obj);\n        }\n    };\n    /**\n     * 清空池\n     */\n    ObjectPool.prototype.clear = function () {\n        this.pool.length = 0;\n        this.inUsed.clear();\n    };\n    return ObjectPool;\n}());\nexports.ObjectPool = ObjectPool;\n/**\n * example\n * const pool = new ObjectPool<cc.Node>(\n    (prefab) => {\n        const node = cc.instantiate(prefab);\n        node.addComponent(cc.Sprite);\n        node.x = 100;\n        node.y = 100;\n        return node;\n    },\n    (node) => {\n        node.x = 0;\n        node.y = 0;\n    });\n\nlet prefab: cc.Prefab;\nconst instance = pool.get(prefab);\nsetTimeout(() => {\n    pool.release(instance);\n}, 1000);\n\nlet list=[];\nfor(let i=0;i<10;i++){\n    const inst = pool.get(prefab);\n    list[i]=inst;\n}\n\nsetTimeout(()=>{\n    for(let i=0;i<10;i++){\n        pool.release(list[i]);\n    }\n})\n */ \n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.randomList = randomList;\nfunction randomList(list, count, repeated) {\n    if (repeated === void 0) { repeated = false; }\n    if (repeated) {\n        var result = [];\n        for (var i_1 = 0; i_1 < count; i_1++) {\n            var random = Math.floor(Math.random() * (list.length - 1));\n            result.push(list[random]);\n        }\n        return result;\n    }\n    else {\n        return shuffle(list).slice(0, count);\n    }\n}\nfunction shuffle(list, useNewList) {\n    if (useNewList === void 0) { useNewList = false; }\n    if (useNewList) {\n        list = list.concat();\n    }\n    for (var i_2 = list.length - 1; i_2 > 0; i_2--) {\n        var random = Math.floor(Math.random() * i_2);\n        var temp = list[i_2];\n        list[i_2] = list[random];\n        list[random] = temp;\n    }\n    return list;\n}\n", "\"use strict\";\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.storage = void 0;\nvar Storage = /** @class */ (function () {\n    function Storage() {\n        /** 缓存数据 */\n        this.cacheData = {};\n    }\n    /**\n     * 存储到 localStorage\n     * @param key\n     * @param value\n     */\n    Storage.prototype.setItem = function (key, value) {\n        try {\n            var prefixedKey = Storage.prefix + key;\n            var memoryValue = void 0;\n            var valueType = typeof value;\n            // 特殊情况为 null，转化为简单数据类型\n            if (value === null || valueType === 'string' || valueType === 'number' || valueType === 'boolean' || valueType === 'bigint' || valueType === 'undefined') {\n                memoryValue = value;\n            }\n            else if (valueType === 'object') {\n                memoryValue = JSON.stringify(value);\n            }\n            else {\n                console.error(\"\\u3010\".concat(key, \"\\u3011\\u3010setItem\\u3011\\u672A\\u5B9E\\u73B0\\u7684\\u6570\\u636E\\u7C7B\\u578B\\u5B58\\u50A8\\uFF0C\\u503C\\u4E3A\\uFF1A\").concat(value));\n            }\n            /**\n             * 为了保持存储与取时数据的一致性，这里需要存储类型\n             * 这里有几个特殊情况需要处理，如果 value 是复杂数据类型，如果存之后，在取的时候，会直接取引用，而引用可能导致后面对引用的修改使 dirtyStatus 与 存储到磁盘中内容不一致\n             * 为了防止这种情况发生，我们缓存的时候，只缓存其字符串，后面取的时候再解析\n             */\n            this.cacheData[key] = { type: valueType, data: memoryValue };\n            var storageValue = valueType + Storage.valueTypeSplit + memoryValue;\n            localStorage.setItem(prefixedKey, storageValue);\n        }\n        catch (e) {\n            if (e instanceof DOMException && (e.code === 22 || e.code === 1014 || e.name === 'QuotaExceededError' || e.name === 'NS_ERROR_DOM_QUOTA_REACHED')) {\n                throw new Error('LocalStorage is full');\n            }\n            else {\n                throw e;\n            }\n        }\n    };\n    /**\n     * 获取存储的数据\n     * @param key\n     * @param defaultValue 默认值，如果存储中不存在，则使用默认值\n     * @returns\n     */\n    Storage.prototype.getItem = function (key, defaultValue) {\n        if (Object.prototype.hasOwnProperty.call(this.cacheData, key)) {\n            var _a = this.cacheData[key], type = _a.type, data = _a.data;\n            if (data === null) {\n                return data;\n            }\n            // 复杂数据类型，序列化\n            if (type === 'object') {\n                return JSON.parse(data);\n            }\n            // 简单数据类型，直接返回\n            return data;\n        }\n        else {\n            // 如果刷新了浏览器，再次读时，需要从磁盘中读写并解析，并还原真实的数据类型\n            var prefixedKey = Storage.prefix + key;\n            var item = localStorage.getItem(prefixedKey);\n            var v = void 0;\n            if (item !== null && item !== \"\") {\n                var values = item.split(Storage.valueTypeSplit);\n                if ((values === null || values === void 0 ? void 0 : values.length) === 2) {\n                    var _b = __read(values, 2), valueType = _b[0], value = _b[1];\n                    var memoryValue = void 0;\n                    switch (valueType) {\n                        case \"string\":\n                            v = value;\n                            memoryValue = v;\n                            break;\n                        case \"number\":\n                        case \"bigint\":\n                            v = +value;\n                            memoryValue = v;\n                            break;\n                        case \"boolean\":\n                            v = JSON.parse(value);\n                            memoryValue = v;\n                            break;\n                        case \"undefined\":\n                            v = undefined;\n                            memoryValue = v;\n                            break;\n                        case \"object\":\n                            v = JSON.parse(value);\n                            // 这里内存中的值存储字符串\n                            memoryValue = value;\n                            break;\n                        default:\n                            console.error(\"\\u3010\".concat(key, \"\\u3011\\u3010getItem\\u3011\\u672A\\u5B9E\\u73B0\\u7684\\u6570\\u636E\\u7C7B\\u578B\\u5B58\\u50A8\"));\n                            break;\n                    }\n                    this.cacheData[key] = { type: valueType, data: memoryValue };\n                }\n                else {\n                    throw new Error(\"\\u3010Storage-getItem\\u3011\\u5B58\\u50A8\\u957F\\u5EA6\\u5E94\\u8BE5\\u4E3A2\\uFF0C\\u5B9E\\u9645\\u4E3A\\uFF1A\".concat(values === null || values === void 0 ? void 0 : values.length, \"\\uFF01\"));\n                }\n            }\n            else {\n                v = defaultValue;\n            }\n            return v;\n        }\n    };\n    /**\n     * 移除存储的数据\n     * @param key\n     */\n    Storage.prototype.remove = function (key) {\n        var prefixedKey = Storage.prefix + key;\n        localStorage.removeItem(prefixedKey);\n    };\n    /**\n     * 清除所有存储的数据\n     */\n    Storage.prototype.clear = function () {\n        localStorage.clear();\n    };\n    Storage.prefix = \"block-blast-\";\n    Storage.valueTypeSplit = \"^_^\";\n    return Storage;\n}());\nexports.storage = new Storage();\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.task = void 0;\n/**\n * 高效任务系统\n * 利用系统空余时间做一些优先级不高的任务\n */\nvar Task = /** @class */ (function () {\n    function Task() {\n        this.taskList = [];\n    }\n    /**\n     * 运行任务\n     * @param taskHandler\n     * @param args\n     */\n    Task.prototype.run = function (taskHandler) {\n        var args = [];\n        for (var _i = 1; _i < arguments.length; _i++) {\n            args[_i - 1] = arguments[_i];\n        }\n        //如果不支持，直接调用\n        if (!window['requestIdleCallback']) {\n            window.requestIdleCallback = function (callback) {\n                var startTime = Date.now();\n                return setTimeout(function () {\n                    callback({\n                        didTimeout: false,\n                        timeRemaining: function () {\n                            return Math.max(0, 30 - (Date.now() - startTime));\n                        }\n                    });\n                });\n            };\n            window.cancelIdleCallback = function (id) {\n                clearTimeout(id);\n            };\n        }\n        this.taskList.push({\n            handler: taskHand<PERSON>,\n            args: args\n        });\n        if (!this.taskHandle) {\n            this.taskHandle = window.requestIdleCallback(this.idleRequestCallback.bind(this), { timeout: 20 });\n        }\n    };\n    Task.prototype.idleRequestCallback = function (deadline) {\n        /** 如果任务较轻，一次性处理了 */\n        while ((deadline.timeRemaining() > 5 || deadline.didTimeout) && this.taskList.length > 0) {\n            var task_1 = this.taskList.shift();\n            // console.log(`deadline.didTimeout:`, deadline.didTimeout);\n            if (task_1.handler) {\n                task_1.handler.apply(this, task_1.args);\n            }\n        }\n        /** 如果任务在当前帧剩余时间没有处理完，则下一个空闲时间处理 */\n        if (this.taskList.length > 0) {\n            this.taskHandle = window.requestIdleCallback(this.idleRequestCallback.bind(this), { timeout: 20 });\n        }\n        else {\n            cancelIdleCallback(this.taskHandle);\n            this.taskHandle = 0;\n        }\n    };\n    return Task;\n}());\nexports.task = new Task();\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.timeout = timeout;\n/**\n * 一些特殊情况下，如果切换了场景，setTimeout 还会继续执行，而如果继续执行，有可能因为回调函数里有对当前节点的引用，会导致报错\n * 这里可以通过 判断该对象是否有效来兼容，取代 cc.tween.delay 这种较重的 api，提升整体性能\n * @param node\n * @param callback\n * @param timeout\n * @param args\n */\nfunction timeout(node, callback, timeout) {\n    var args = [];\n    for (var _i = 3; _i < arguments.length; _i++) {\n        args[_i - 3] = arguments[_i];\n    }\n    var id = setTimeout(function () {\n        clearTimeout(id);\n        if (!node || !cc.isValid(node, true)) {\n            return;\n        }\n        callback === null || callback === void 0 ? void 0 : callback.apply(this, [args]);\n    }, timeout, args);\n    return id;\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.nextFrame = nextFrame;\nexports.callLater = callLater;\n/**\n     * 等待 一帧 配合await  使用\n     * @returns\n     */\nfunction nextFrame() {\n    var _this = this;\n    return new Promise(function (resolve) {\n        _this.callLater(function () {\n            resolve(null);\n        }, null);\n    });\n}\nfunction callLater(func, thisObj) {\n    cc.director.once(cc.Director.EVENT_AFTER_UPDATE, func, thisObj);\n}\n", "\"use strict\";\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __generator = (this && this.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\n    return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Trait = void 0;\nvar TraitConfigInfo_1 = require(\"../traitConfig/TraitConfigInfo\");\n/**\n * 特性有以下特点：\n * （1）：每个特性逻辑都独立且封装，不与界面耦合\n * （2）：每个特性有自己的属性(属性从配置里获取)\n * （3）：每个特性有自身的状态变化，状态变化外部驱动\n * （4）：traits 文件夹里存储了所有的特性\n * （5）：每个特性都有自己的生命周期\n * （6）：每个特性依赖目标组件各种数据\n * （7）：基于特性可以获取其装饰的目标组件\n * （8）：特性支持分层\n * （9）：特性不能依赖其它特性，只能依赖其它特性原始数据，如果实在需要依赖，需要采用其它方法\n *\n * TODO 需要支持动态特性，不同特性可以开启可以激活另外的特性\n */\nvar Trait = /** @class */ (function () {\n    function Trait() {\n        this._state = {};\n        this._props = {};\n        this._active = false;\n        this._subTraits = [];\n    }\n    Object.defineProperty(Trait, \"activedListenerTraits\", {\n        /**\n         * 已激活的监听特性\n         */\n        get: function () {\n            return Trait._activedListenerTraits;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Trait.prototype, \"id\", {\n        /**\n         * 特性id\n         */\n        get: function () {\n            return this._id;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Trait.prototype, \"condition\", {\n        /**\n         * 特性激活条件（目前主要是用于 M*N，不排除后期会用于业务逻辑，这里先支持)，条件特性里面所有条件变量必须暴露到条件对象上才行（TODO 后面通过语法检测其有效性）\n         * 如果有条件特性\n         */\n        get: function () {\n            return this._condition;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Trait.prototype, \"traitName\", {\n        /**\n         * 特性名称\n         */\n        get: function () {\n            return getClassName(this.constructor);\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Trait.prototype, \"state\", {\n        /**\n         * 特性所持有的数据状态（外部只能访问，不能修改，如果要修改，直接通过 setState 来做）\n         */\n        get: function () {\n            return this._state;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * state 的初始化数据\n     * @returns\n     */\n    Trait.prototype.data = function () {\n        return {};\n    };\n    Object.defineProperty(Trait.prototype, \"props\", {\n        /**\n         * 特性持有的属性（不能修改，从配置或者服务器下发获取）\n         */\n        get: function () {\n            return this._props;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Trait.prototype, \"active\", {\n        /**\n         * 特性的静态激活状态（与特性静态属性有关）\n         */\n        get: function () {\n            var _active = this.props && Object.keys(this.props).length > 0;\n            if (!this._condition) {\n                return _active;\n            }\n            try {\n                return _active && eval(this._condition);\n            }\n            catch (error) {\n                console.error(\"\\u6267\\u884C\\u6761\\u4EF6\\u7279\\u6027\\u6FC0\\u6D3B\\u53D1\\u751F\\u9519\\u8BEF\\uFF1A\", error.stack);\n                return _active;\n            }\n        },\n        /**\n         * 设置特性的激活状态（动态特性）,一般情况下只能框架层调用，上层业务不能调用\n         */\n        set: function (value) {\n            var _a;\n            if (this._active !== value) {\n                this._active = value;\n                if (value) {\n                    //如果当前特性未绑定属性，则初始化绑定一次\n                    this._props = (_a = TraitConfigInfo_1.traitConfigInfo.traitsClassNameMap[this.traitName]) === null || _a === void 0 ? void 0 : _a.param;\n                    this.onEnable();\n                }\n                else {\n                    this._props = {};\n                    this.onDisable();\n                }\n            }\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * 注册子特性，子特性的开启受父特性影响\n     * 父特性：\n     *  开启：子特性必须在配置表里有才开启，否则，关闭\n     *  关闭：子特性不管在配置表里有没有，都关闭\n     *\n     */\n    Trait.prototype.registerSubTraits = function () {\n        return null;\n    };\n    /**\n     * 获取子特性实例\n     * @returns\n     */\n    Trait.prototype.getSubTraits = function () {\n        return this._subTraits;\n    };\n    /**\n     * 特性被创建时\n     */\n    Trait.prototype.onCreate = function () {\n    };\n    /**\n     * 特性被激活时调用\n     */\n    Trait.prototype.onEnable = function () {\n    };\n    /**\n     * 特性被禁用时调用\n     */\n    Trait.prototype.onDisable = function () {\n    };\n    /**\n     * 设置最新状态\n     * @param state\n     * @param replace\n     * @param callback\n     */\n    Trait.prototype.setState = function (state) {\n        for (var key in state) {\n            var value = state[key];\n            this._state[key] = value;\n        }\n    };\n    /**\n     * 特性装饰的目标函数被调用时触发\n     * @param target\n     */\n    Trait.prototype.onActive = function (target) {\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                return [2 /*return*/];\n            });\n        });\n    };\n    /**\n     * 监听激活的的特性\n     * @param traitConstructor\n     * @param actived\n     */\n    Trait.onTraitActive = function (traitConstructor, preActive, actived) {\n        if (!this._activedListenerTraits.has(traitConstructor)) {\n            this._activedListenerTraits.set(traitConstructor, []);\n        }\n        this._activedListenerTraits.get(traitConstructor).push({ preActive: preActive, actived: actived });\n    };\n    Trait._activedListenerTraits = new Map();\n    return Trait;\n}());\nexports.Trait = Trait;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.traitConfigInfo = void 0;\nvar Storage_1 = require(\"../storage/Storage\");\n/**\n * P5\n *  新增：用户玩的天数小于等于 14 天\n *  活跃：用户玩的天数大于 14 天\n * P95\n *  新增：完全由服务器下发来定（天数不一定是14天，具体天数由策划配置）\n *  活跃：完全由服务器下发来定（天数不一定是14天，具体天数由策划配置）\n *  新增还是活跃由服务器控制（不一定是14天）\n *    活跃的前提条件必须拿到服务器下发数据，如果一直未拿到，则一直处于新增状态\n *    14天只是为了打点\n *\n * 扩量（目前是前端控制，要不要走服务器下发逻辑）\n * 安装时间：由原生给GameData.deviceInfo.install_time，cocos 这边记录的是第一次启动时间\n *\n * 流程\n * 1、进游戏后先请求服务器下发\n * 2、再请求热更\n * 3、如果热更新完毕之前服务下发消息收到了，则将服务器下发数据保存到本地，后面使用这个数据\n * 4、如果热更新完毕之后服务器下发消息没收到，则再等1秒，\n *  （1）：如果1秒内收到了服务器下发数据，则将其数据保存到本地，后面使用这个数据\n *  （2）：如果1秒内未收到服务器下发数据，则读取兜底配置 Config.ts 文件中的\n *\n * InterF：原先定义为 免插屏\n * Adf：原先定义为 免插屏与banner\n * 上面两个目前用户都不免了，只是原生标签未变，含义实际上变了\n *\n * 单向门\n *  指用户进入某个桶之后，后面就不会参与活跃了，游戏中没有单向门逻辑，只是策划定义的一个概念，游戏中没有逻辑\n *\n * 规范\n *  2.0：\n *  （1）：P5：纯净桶\n *  （2）：P95：非纯净桶\n *  3.0：\n *  （1）：interF：非纯净桶\n *  （2）：AdF：非纯净桶\n *\n * 桶概念\n *  指服务器为了不同流量区分的概念，本质上来讲，客户端不用关心什么桶，因为不有客户端逻辑，只是策划为了更清晰每个桶主要的流量方向标识符而已\n * （1）：流量桶\n * （2）：L5 元素桶\n * （3）：DUO 多邻国桶\n * （4）：TIANKONG 填空桶\n * （5）：广告桶\n *\n * 特性配置信息（Config.ts）\n * （1）：纯净桶（P5)\n *      新增：P5Config\n *      活跃：P5ActiveConfig\n * （2）：非纯净桶（P95)\n *      服务器配置：\n *          正常情况下先读服务器配置，服务器配置未读到时才走兜底配置\n *      兜底配置\n *          新增：Config\n *          活跃：ActiveConfig（暂时没用）\n *\n *\n *\n */\nvar TraitConfigInfo = /** @class */ (function () {\n    function TraitConfigInfo() {\n        /*************************************************************\n         *\n         *                          全特性\n         *\n         *************************************************************/\n        /**\n         * 基于特性名字存储特性信息\n         */\n        this._traits = {};\n        /**\n         * 基于特性 id 存储特性数据\n         */\n        this._traitsById = {};\n        /**********************************************************\n         *\n         *                    Config 配置\n         *\n         *********************************************************/\n        /**\n         * 当前用户特性\n         */\n        this._useTraits = {};\n        /**\n         * 缓存使用的特性名\n         */\n        this._cacheUsedTraits = {};\n        /**\n         * 基于特性类名存储特性数据\n         */\n        this._traitsClassNameMap = {};\n        /**********************************************************\n        *\n        *                    ActiveConfig 配置\n        *\n        *********************************************************/\n        /**\n         * 当前活跃用户特性\n         */\n        this._useActiveUserTraits = {};\n        this._cacheUsedActiveUserTraits = {};\n    }\n    Object.defineProperty(TraitConfigInfo.prototype, \"traitsClassNameMap\", {\n        /**\n         * 特性类名数据映射\n         */\n        get: function () {\n            return this._traitsClassNameMap;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    TraitConfigInfo.prototype.initialize = function (cfg) {\n        if (!cfg) {\n            console.error(\"\\u7279\\u6027\\u521D\\u59CB\\u5316\\u914D\\u7F6E\\u53D1\\u751F\\u9519\\u8BEF\");\n            return;\n        }\n        var feature = cfg.feature;\n        var _traits = this._traits;\n        var _traitsById = this._traitsById;\n        /*************************************************************\n         *\n         *                      初始化全部特性配置\n         *\n         *************************************************************/\n        for (var traitName in feature) {\n            var traitItemInfoList = feature[traitName];\n            for (var i_1 = 0; i_1 < traitItemInfoList.length; i_1++) {\n                var traitItemInfo = traitItemInfoList[i_1];\n                var id = traitItemInfo.id, param = traitItemInfo.param;\n                if (!_traits[traitName]) {\n                    _traits[traitName] = {};\n                }\n                _traits[traitName][id] = param;\n                _traitsById[id] = { traitName: traitName, param: param };\n            }\n        }\n    };\n    // 生成新增的Traits\n    TraitConfigInfo.prototype.createUseTraits = function (config) {\n        /**********************************************************\n           *\n           *                    Config 解析的出来的配置\n           *\n           *********************************************************/\n        this._curActiveConfig = config;\n        var _a = this._curActiveConfig, features = _a.features, plan = _a.plan;\n        Storage_1.storage.setItem(\"gameWayNum\", plan);\n        for (var i_2 = 0; i_2 < features.length; i_2++) {\n            var id = features[i_2].id;\n            this._useTraits[id] = features[i_2];\n        }\n    };\n    // 生成活跃的Traits\n    TraitConfigInfo.prototype.createActiveTraits = function (config) {\n        /**********************************************************\n         *\n         *                    ActiveConfig 解析的出来的配置\n         *\n         *********************************************************/\n        var ActiveFeatures = config.features;\n        for (var i_3 = 0; i_3 < ActiveFeatures.length; i_3++) {\n            var id = ActiveFeatures[i_3].id;\n            this._useActiveUserTraits[id] = ActiveFeatures[i_3];\n        }\n    };\n    /**\n     * 创建激活特性类名映射\n     *\n     * 特性名  ---  特性类名\n     *  xxx        XxxTrait\n     * 首字母大写，然后加上 Trait 结尾\n     *\n     *\n     * 这里需要等待以下条件才能执行：\n     * （1）：等待服务器下发特性，如果 2 秒内收到服务器下发特性，则使用服务器下发特性\n     * （2）：如果 2 秒内没有收到，则使用默认的特性\n     */\n    TraitConfigInfo.prototype.createActiveTraitClassNameMaps = function () {\n        var _traitsById = this._traitsById;\n        var features = this._curActiveConfig.features;\n        if (CC_DEBUG) {\n            console.log('%c[激活的特性]', \"color:#fff;background:#ff019a;\", features);\n            Storage_1.storage.setItem('activeFeatures', features);\n        }\n        for (var i_4 = 0; i_4 < features.length; i_4++) {\n            var id = features[i_4].id;\n            var traitIdInfo = _traitsById[id];\n            if (!traitIdInfo) {\n                if (CC_DEBUG) {\n                    console.error(\"\\u7279\\u6027\\u603B\\u8868 cfg.ts \\u914D\\u7F6E\\u6CA1\\u6709\\u7279\\u6027\\uFF1A\".concat(id));\n                }\n                continue;\n            }\n            var traitName = traitIdInfo.traitName, param = traitIdInfo.param;\n            var traitClassName = traitName.charAt(0).toUpperCase() + traitName.slice(1) + 'Trait';\n            this._traitsClassNameMap[traitClassName] = { id: id, param: param };\n        }\n    };\n    // 创建白名单特性剔出\n    TraitConfigInfo.prototype.createWhiteTraitClassNameMaps = function (whiteList) {\n        var _traitsById = this._traitsById;\n        // 重构包单独加的3个特性\n        for (var i_5 = 0; i_5 < whiteList.length; i_5++) {\n            var id = whiteList[i_5];\n            var traitIdInfo = _traitsById[id];\n            if (!traitIdInfo) {\n                if (CC_DEBUG) {\n                    console.error(\"\\u7279\\u6027\\u603B\\u8868 cfg.ts \\u914D\\u7F6E\\u6CA1\\u6709\\u7279\\u6027\\uFF1A\".concat(id));\n                }\n                continue;\n            }\n            var traitName = traitIdInfo.traitName, param = traitIdInfo.param;\n            var traitClassName = traitName.charAt(0).toUpperCase() + traitName.slice(1) + 'Trait';\n            this._traitsClassNameMap[traitClassName] = { id: id, param: param };\n        }\n        if (CC_DEBUG) {\n            console.log('%c[激活的特性类名]', \"color:#fff;background:#ff019a;\", this._traitsClassNameMap);\n        }\n    };\n    // 加载动态特性\n    TraitConfigInfo.prototype.loadDynamicTrait = function (dynamicTraitList) {\n        var _traitsById = this._traitsById;\n        for (var i_6 = 0; i_6 < dynamicTraitList.length; i_6++) {\n            var id = dynamicTraitList[i_6];\n            var traitIdInfo = _traitsById[id];\n            if (!traitIdInfo) {\n                if (CC_DEBUG) {\n                    console.error(\"\\u52A8\\u6001\\u65B0\\u52A0\\u7279\\u6027 \\u7279\\u6027\\u603B\\u8868 cfg.ts \\u914D\\u7F6E\\u6CA1\\u6709\\u7279\\u6027\\uFF1A\".concat(id));\n                }\n                continue;\n            }\n            var traitName = traitIdInfo.traitName, param = traitIdInfo.param;\n            var traitClassName = traitName.charAt(0).toUpperCase() + traitName.slice(1) + 'Trait';\n            this._traitsClassNameMap[traitClassName] = { id: id, param: param };\n            console.log(\"\\u52A8\\u6001\\u65B0\\u52A0\\u7279\\u6027\\uFF1A\".concat(id));\n        }\n    };\n    /**\n     * 获取特性数据\n     * @param traitName 特性键值\n     * @param id 如果值为空，则直接从 Config.ts->Config 里查找\n     * @returns\n     */\n    TraitConfigInfo.prototype.traitData = function (traitName, id) {\n        if (isNaN(id)) {\n            if (this._cacheUsedTraits[traitName]) {\n                return this._cacheUsedTraits[traitName];\n            }\n            var featureObj = this._traits[traitName];\n            for (var uid in featureObj) {\n                if (this._useTraits[uid]) {\n                    this._cacheUsedTraits[traitName] = featureObj[uid];\n                    return this._cacheUsedTraits[traitName];\n                }\n            }\n        }\n        return this._traits[traitName][id];\n    };\n    /**\n     * 获取活跃用户特性数据\n     * @param key 特性键值\n     * @param id 如果值为空，则直接从 Config.ts->ActiveConfig 里查找\n     * @returns\n     */\n    TraitConfigInfo.prototype.traitActiveUserData = function (key, id) {\n        if (isNaN(id)) {\n            if (this._cacheUsedActiveUserTraits[key]) {\n                return this._cacheUsedActiveUserTraits[key];\n            }\n            var featureObj = this._traits[key];\n            for (var uid in featureObj) {\n                if (this._useActiveUserTraits[uid]) {\n                    this._cacheUsedActiveUserTraits[key] = featureObj[uid];\n                    return this._cacheUsedActiveUserTraits[key];\n                }\n            }\n        }\n        return this._traits[key][id];\n    };\n    return TraitConfigInfo;\n}());\nexports.traitConfigInfo = new TraitConfigInfo();\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.isString = isString;\nexports.isStringArray = isStringArray;\nexports.isObject = isObject;\nexports.isTypedArray = isTypedArray;\nexports.isNumber = isNumber;\nexports.isIterable = isIterable;\nexports.isBoolean = isBoolean;\nexports.isUndefined = isUndefined;\nexports.isDefined = isDefined;\nexports.isUndefinedOrNull = isUndefinedOrNull;\nexports.isFunction = isFunction;\nexports.getType = getType;\n/**\n * @returns 是否为字符串.\n */\nfunction isString(str) {\n    return (typeof str === 'string');\n}\n/**\n * @returns 是否为字符串数组.\n */\nfunction isStringArray(value) {\n    return Array.isArray(value) && value.every(function (elem) { return isString(elem); });\n}\n/**\n * @returns 提供的参数是否为“object”类型，但**不是**“null”、“array”、“regexp”或“date”。\n */\nfunction isObject(obj) {\n    //该方法无法进行类型转换，因为存在类型（如字符串）\n    //是任何与函数不正匹配的put的子类。因此类型\n    //缩小范围会导致错误的结果。\n    return typeof obj === 'object'\n        && obj !== null\n        && !Array.isArray(obj)\n        && !(obj instanceof RegExp)\n        && !(obj instanceof Date);\n}\n/**\n * @returns 提供的参数是“Buffer”类型还是Uint8Array驱动类型\n */\nfunction isTypedArray(obj) {\n    var TypedArray = Object.getPrototypeOf(Uint8Array);\n    return typeof obj === 'object'\n        && obj instanceof TypedArray;\n}\n/**\n * 是否为 number 类型\n * @returns\n */\nfunction isNumber(obj) {\n    return (typeof obj === 'number' && !isNaN(obj));\n}\n/**\n * 是否为Iterable，是否转换为给定的泛型\n * @returns\n */\nfunction isIterable(obj) {\n    return !!obj && typeof obj[Symbol.iterator] === 'function';\n}\n/**\n * @returns 判断是否为 boolean 值\n */\nfunction isBoolean(obj) {\n    return (obj === true || obj === false);\n}\n/**\n * 是否为 undefined\n * @returns\n */\nfunction isUndefined(obj) {\n    return (typeof obj === 'undefined');\n}\n/**\n * 是否不为 undefined 与 null\n * @returns\n */\nfunction isDefined(arg) {\n    return !isUndefinedOrNull(arg);\n}\n/**\n * 是否为 undefined 或 null\n * @returns\n */\nfunction isUndefinedOrNull(obj) {\n    return (isUndefined(obj) || obj === null);\n}\n/**\n * 是否为函数\n * @returns\n */\nfunction isFunction(obj) {\n    return (typeof obj === 'function');\n}\n/**\n * 获取数据真实类型\n * @param {*} data\n */\nfunction getType(data) {\n    var prototype = Object.prototype.toString.call(data);\n    switch (prototype) {\n        case '[object Object]': return 'object';\n        case '[object Number]': return 'number';\n        case '[object String]': return 'string';\n        case '[object Boolean]': return 'boolean';\n        case '[object Array]': return 'array';\n        case '[object Undefined]': return 'undefined';\n        case '[object Null]': return 'null';\n        case '[object Date]': return 'date';\n        case '[object RegExp]': return 'regExp';\n        case '[object Function]': return 'function';\n        default:\n            return 'object';\n    }\n}\n", "\"use strict\";\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __generator = (this && this.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\n    return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.UI = void 0;\nvar GameLayer_1 = require(\"../layer/GameLayer\");\nvar ResLoader_1 = require(\"../loader/ResLoader\");\nvar UI = /** @class */ (function () {\n    function UI() {\n    }\n    // 设置通用model prefab\n    UI.setModalPrefab = function (url) {\n        this.modalUrl = url;\n    };\n    /**\n     * 隐藏所有UI\n     */\n    UI.hideAll = function () {\n        for (var key in this.uiCache) {\n            var data = UI.uiCache[key];\n            if (data.node) {\n                data.node.active = false;\n            }\n        }\n    };\n    /**\n     * UI是否处于激活状态\n     */\n    UI.activeState = function (url) {\n        for (var key in this.uiCache) {\n            if (key == url) {\n                var data = UI.uiCache[key];\n                if (data.node) {\n                    return data.node.active;\n                }\n            }\n        }\n        return false;\n    };\n    /**\n     * 取消加载后的操作\n     * @param config\n     */\n    UI.cancelLoad = function (config) {\n        this.uiIsCancelLoad[config.url] = true;\n    };\n    UI.clearCache = function (url) {\n        delete UI.uiCache[url];\n        delete this.uiIsLoading[url];\n        delete this.uiIsCancelLoad[url];\n    };\n    /**\n     * 添加面板创建、打开、关闭监听\n     * @param event\n     * @param callback\n     */\n    UI.addEventListener = function (event, callback) {\n        if (!this.events[event]) {\n            this.events[event] = [];\n        }\n        this.events[event].push(callback);\n    };\n    UI.removeEventListener = function (event, callback) {\n        if (this.events[event]) {\n            var index = this.events[event].indexOf(callback);\n            if (index != -1) {\n                this.events[event].splice(index, 1);\n            }\n        }\n    };\n    /**\n     * 打开某个预制体UI\n     * （1）：所有的预制体都放在resources文件夹下\n     * （2）：被打开的UI只会被实例化一次\n     * （3）：具备响应处理多次点击而导致的问题\n     * （4）：后打开的会放在上面\n     * （5）：增加弱网环境导致的用户多次点击而导致的多次加载预制体的BUG处理机制\n     * 打开某个UI,此处打开的是某个动态加载的预制体UI,通常情况下这些预制体都是放在resources文件夹下\n     * 被打开的UI都通过缓存管理着\n     *\n     * @param config 当前配置\n     * @param parent 父容器\n     */\n    UI.show = function (config, parent) {\n        var _this = this;\n        return new Promise(function (c, e) { return __awaiter(_this, void 0, void 0, function () {\n            var ui, asset, modalAsset, modalNode_1, panelEase, createEvents, i_1, event_1, modalNode, panelEase, currentResolves, i_2, resolve, openEvents, i_3, event_2, err_1;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (CC_DEBUG) {\n                            config.name !== 'TouchEffect' && console.log(\"ui.show() === =>\", config.name);\n                        }\n                        if (!this.resolves[config.url]) {\n                            this.resolves[config.url] = [];\n                        }\n                        this.resolves[config.url].push(c);\n                        /** 如果当前ui正在加载，直接reject */\n                        if (this.uiIsLoading[config.url]) {\n                            return [2 /*return*/];\n                        }\n                        _a.label = 1;\n                    case 1:\n                        _a.trys.push([1, 10, , 11]);\n                        parent = parent ? parent : GameLayer_1.gameUiLayer;\n                        ui = void 0;\n                        // 容错节点被销毁时没有移除缓存\n                        if (UI.uiCache[config.url] && !cc.isValid(UI.uiCache[config.url].node)) {\n                            delete UI.uiCache[config.url];\n                        }\n                        if (!!UI.uiCache[config.url]) return [3 /*break*/, 8];\n                        this.uiIsLoading[config.url] = true;\n                        asset = void 0;\n                        if (!config.bundleName) return [3 /*break*/, 3];\n                        return [4 /*yield*/, ResLoader_1.ResLoader.asyncLoadByBundle(config.bundleName, config.url, cc.Prefab)];\n                    case 2:\n                        asset = _a.sent();\n                        return [3 /*break*/, 5];\n                    case 3: return [4 /*yield*/, ResLoader_1.ResLoader.asyncLoad(config.url, cc.Prefab)];\n                    case 4:\n                        asset = _a.sent();\n                        _a.label = 5;\n                    case 5:\n                        ui = cc.instantiate(asset);\n                        ui['__config__'] = config;\n                        /******************************************************\n                         *\n                         *                      隐藏上一个 page\n                         *\n                         *******************************************************/\n                        if (config.page && this.lastPage) {\n                            this.hide(this.lastPage);\n                        }\n                        if (config.page) {\n                            this.lastPage = ui;\n                        }\n                        // 判断是否需要停止，如停止就不加载\n                        if (this.uiIsCancelLoad[config.url]) {\n                            this.uiIsLoading[config.url] = false;\n                            delete this.uiIsCancelLoad[config.url];\n                        }\n                        UI.uiCache[config.url] = {};\n                        UI.uiCache[config.url].node = ui;\n                        UI.uiCache[config.url].ease = config.ease;\n                        if (!config.modal) return [3 /*break*/, 7];\n                        return [4 /*yield*/, ResLoader_1.ResLoader.asyncLoad(this.modalUrl, cc.Prefab)];\n                    case 6:\n                        modalAsset = _a.sent();\n                        modalNode_1 = cc.instantiate(modalAsset);\n                        parent.addChild(modalNode_1);\n                        (function (target) {\n                            modalNode_1.on(cc.Node.EventType.TOUCH_START, function (event) {\n                                UI.hide(target);\n                            });\n                        })(ui);\n                        UI.uiCache[config.url].modal = modalNode_1;\n                        _a.label = 7;\n                    case 7:\n                        if (ui.parent == null) {\n                            parent.addChild(ui);\n                        }\n                        else {\n                            if (ui.parent != parent) {\n                                ui.setParent(parent);\n                            }\n                        }\n                        this.uiIsLoading[config.url] = false;\n                        if (config.ease != null) {\n                            panelEase = ui.getComponent(config.ease);\n                            if (panelEase == null) {\n                                panelEase = ui.addComponent(config.ease);\n                                // panelEase.show();\n                            }\n                        }\n                        createEvents = this.events[\"create\"];\n                        for (i_1 = 0; i_1 < (createEvents === null || createEvents === void 0 ? void 0 : createEvents.length); i_1++) {\n                            event_1 = createEvents[i_1];\n                            event_1.apply(this, [config]);\n                        }\n                        return [3 /*break*/, 9];\n                    case 8:\n                        this.uiIsLoading[config.url] = false;\n                        ui = UI.uiCache[config.url].node;\n                        /******************************************************\n                         *\n                         *                      隐藏上一个 page\n                         *\n                         *******************************************************/\n                        if (config.page && this.lastPage && ui !== this.lastPage) {\n                            this.hide(this.lastPage);\n                        }\n                        if (config.page) {\n                            this.lastPage = ui;\n                        }\n                        if (config.modal) {\n                            modalNode = UI.uiCache[config.url].modal;\n                            if (!modalNode.parent) {\n                                parent.addChild(modalNode);\n                            }\n                        }\n                        ui.active = true;\n                        _a.label = 9;\n                    case 9:\n                        if (ui.parent != parent) {\n                            ui.setParent(parent);\n                        }\n                        UI.uiCache[config.url].parent = parent;\n                        ui.setSiblingIndex(parent.childrenCount);\n                        if (config.ease != null) {\n                            panelEase = ui.getComponent(config.ease);\n                            panelEase === null || panelEase === void 0 ? void 0 : panelEase.show();\n                        }\n                        currentResolves = this.resolves[config.url];\n                        if (currentResolves) {\n                            for (i_2 = 0; i_2 < currentResolves.length; i_2++) {\n                                resolve = currentResolves[i_2];\n                                resolve(ui);\n                            }\n                            //需要清理，否则会导致 C++ 层 JS: [ERROR]:  resolveAfterPromiseResolved stacktrace: 报错，且有性能问题。\n                            currentResolves.length = 0;\n                        }\n                        openEvents = this.events[\"open\"];\n                        for (i_3 = 0; i_3 < (openEvents === null || openEvents === void 0 ? void 0 : openEvents.length); i_3++) {\n                            event_2 = openEvents[i_3];\n                            event_2.apply(this, [config]);\n                        }\n                        return [3 /*break*/, 11];\n                    case 10:\n                        err_1 = _a.sent();\n                        this.uiIsLoading[config.url] = false;\n                        e(err_1);\n                        return [3 /*break*/, 11];\n                    case 11: return [2 /*return*/];\n                }\n            });\n        }); });\n    };\n    /**\n     * 预加载时，缓存创建的节点\n     * @param config prefab 标识路径\n     * @param cacheUI 缓存prefab\n     * @param parent 添加父节点\n     */\n    UI.addUICache = function (config, cacheUI, parent) {\n        try {\n            var cacheNode = cc.instantiate(cacheUI);\n            if (!UI.uiCache[config.url]) {\n                UI.uiCache[config.url] = {};\n            }\n            UI.uiCache[config.url].node = cacheNode;\n            parent.addChild(cacheNode);\n            UI.uiCache[config.url].parent = parent;\n            cacheNode.setSiblingIndex(cacheNode.parent.childrenCount);\n            cacheNode.active = false;\n        }\n        catch (err) {\n            console.error(\"UI.addUICache throw error-> \", err);\n        }\n    };\n    /**\n     * 拿到某个预制体的【入口组件】\n     * 为了提升获取组件性能，这里拿过一遍的引用将会被缓存起来，下次不用再进行遍历拿取\n     * 如果预制体未被加载，则会直接加载，加载完毕之后，再拿组件\n     *\n     * @param config 预制体配置\n     * @param parent 父结点\n     */\n    UI.getComponent = function (config, parent) {\n        return __awaiter(this, void 0, void 0, function () {\n            var data, node, comp;\n            return __generator(this, function (_a) {\n                if (!config) {\n                    console.error(\"\\u83B7\\u53D6\\u7EC4\\u4EF6\\u65F6\\u4F20\\u5165\\u7684config\\u4E0D\\u80FD\\u4E3A\\u7A7A\\uFF01\");\n                    return [2 /*return*/, undefined];\n                }\n                data = this.uiCache[config.url];\n                if (data) {\n                    node = data.node;\n                    // node = node || await this.show(config, parent);\n                }\n                // else {\n                //     node = await this.show(config, parent);\n                // }\n                if (config.comp) {\n                    if (node) {\n                        comp = node.getComponent(config.comp);\n                        this.uiCache[config.url].comp = comp;\n                        return [2 /*return*/, comp];\n                    }\n                    else {\n                        return [2 /*return*/, undefined];\n                    }\n                }\n                else {\n                    console.error(\"\\u5FC5\\u987B\\u4E3A\\u914D\\u7F6E\".concat(config, \"\\u914D\\u7F6E\\u7EC4\\u4EF6\\u5F15\\u7528\\uFF01\"));\n                    return [2 /*return*/, undefined];\n                }\n                return [2 /*return*/];\n            });\n        });\n    };\n    /**\n     * 隐藏 ui\n     * @param target\n     */\n    UI.hide = function (target) {\n        var config;\n        if (target instanceof cc.Component) {\n            config = target.node['__config__'];\n        }\n        else {\n            config = target['__config__'];\n        }\n        if (config === null || config === void 0 ? void 0 : config.url) {\n            this.hideUI(config);\n        }\n    };\n    UI.hideUI = function (config) {\n        var _this = this;\n        var ui = UI.uiCache[config.url];\n        if (!ui) {\n            return;\n        }\n        else {\n            if (config.ease != null) {\n                var panelEase = ui.node.getComponent(config.ease);\n                if (panelEase != null) {\n                    panelEase.onCloseEaseComplete = function () {\n                        ui.node.active = false;\n                        _this.dispatchClose(config);\n                    };\n                    panelEase.hide();\n                }\n                else {\n                    ui.node.active = false;\n                    this.dispatchClose(config);\n                }\n            }\n            else {\n                ui.node.active = false;\n                this.dispatchClose(config);\n            }\n            if (config.modal) {\n                ui.modal.setParent(null);\n            }\n        }\n    };\n    UI.dispatchClose = function (config) {\n        var closeEvents = this.events[\"close\"];\n        for (var i_4 = 0; i_4 < (closeEvents === null || closeEvents === void 0 ? void 0 : closeEvents.length); i_4++) {\n            var event_3 = closeEvents[i_4];\n            event_3.apply(this, [config]);\n        }\n    };\n    UI.hideLayer = function (layer) {\n        layer.children.forEach(function (ele) {\n            ele.active = false;\n        });\n    };\n    /** 缓存当前某个预制体与其主引用 */\n    UI.uiCache = {};\n    /** 存储当前预制体是否正在加载 */\n    UI.uiIsLoading = {};\n    /** 存储当前预制体是否取消加载 */\n    UI.uiIsCancelLoad = {};\n    UI.events = {};\n    UI.resolves = {};\n    UI.modalUrl = 'prefabs/modal/Modal';\n    return UI;\n}());\nexports.UI = UI;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getUrlParameterValue = getUrlParameterValue;\n/**\n * 获取地址参数值\n * @param paramName\n * @returns\n */\nfunction getUrlParameterValue(paramName) {\n    if (!globalThis.location) {\n        return \"\";\n    }\n    var query = globalThis.location.search.substring(1);\n    var vars = query.split(\"&\");\n    for (var i_1 = 0; i_1 < vars.length; i_1++) {\n        var pair = vars[i_1].split(\"=\");\n        if (pair[0] === paramName) {\n            return decodeURIComponent(pair[1]);\n        }\n    }\n    return \"\";\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ReactivePos = void 0;\nexports.reactive = reactive;\nexports.watch = watch;\nvar Types_1 = require(\"../types/Types\");\nvar watchMap = new Map();\n/**\n * Reactive 位置\n */\nvar ReactivePos;\n(function (ReactivePos) {\n    ReactivePos[\"RandomChangeScoreLbColorTraitIsComboReachTargetTraitInit\"] = \"RandomChangeScoreLbColorTraitIsComboReachTargetTraitInit\";\n    ReactivePos[\"GuideFirstLifeTrait\"] = \"GuideFirstLifeTrait\";\n    ReactivePos[\"MoreTimeToOpreateStateTraitIsBlocksProducerTouchShowGameOverStatus\"] = \"MoreTimeToOpreateStateTraitIsBlocksProducerTouchShowGameOverStatus\";\n})(ReactivePos || (exports.ReactivePos = ReactivePos = {}));\n/**\n * 监听目标属性变化\n * @param target\n * @param propertyName\n * @param callback\n * @example\n *  reactive(gameInfo, 'obj', (key, newVal, oldVal) => {\n        console.log(`key:`, key, `newVal:`, newVal, `oldVal:`, oldVal);\n    });\n\n    setInterval(() => {\n        gameInfo.obj.a += 1;\n    }, 1000);\n */\nfunction reactive(option) {\n    var target = option.target, propertyName = option.propertyName, pos = option.pos, callback = option.callback;\n    var properties = watchMap.get(target.constructor);\n    if (!properties) {\n        properties = {};\n        watchMap.set(target.constructor, properties);\n    }\n    // 回调列表\n    var key = propertyName + pos;\n    properties[key] = callback;\n}\n/**\n * 监听目标变化\n * @param id\n * @param target\n * @returns\n */\nfunction _reactive(target, watchTarget, propertykey) {\n    if (!(0, Types_1.isObject)(target))\n        return target;\n    var handler = {\n        get: function (target, key, receiver) {\n            var result = Reflect.get(target, key, receiver);\n            return (0, Types_1.isObject)(result) ? _reactive(result, watchTarget, propertykey) : result;\n        },\n        set: function (target, key, value, receiver) {\n            var oldValue = target[key];\n            var result = Reflect.set(target, key, value, receiver);\n            if (oldValue !== value) {\n                var properties_1 = watchMap.get(watchTarget.constructor);\n                if (properties_1) {\n                    Object.keys(properties_1).forEach(function (key) {\n                        if (key.includes(propertykey)) {\n                            if (properties_1[key]) {\n                                properties_1[key](key, oldValue, value);\n                            }\n                        }\n                    });\n                }\n                if ((0, Types_1.isObject)(value))\n                    _reactive(value, watchTarget, propertykey);\n            }\n            return result;\n        }\n    };\n    return new Proxy(target, handler);\n}\n/**\n * 类属性装饰器，监测属性值变化\n * @param id 唯一 id\n * @returns\n */\nfunction watch() {\n    return function (target, propertykey) {\n        var _a;\n        var proxy = _reactive((_a = {}, _a[propertykey] = target[propertykey], _a), target, propertykey);\n        Object.defineProperty(target, propertykey, {\n            get: function () {\n                return proxy[propertykey];\n            },\n            set: function (newValue) {\n                proxy[propertykey] = newValue;\n            },\n            enumerable: true,\n            configurable: true\n        });\n    };\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.EventManager = void 0;\nvar ModuleManager_1 = require(\"./ModuleManager\");\nvar EventManager = /** @class */ (function () {\n    function EventManager() {\n    }\n    Object.defineProperty(EventManager, \"events\", {\n        /**\n         * @private\n         */\n        get: function () {\n            return this._events;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * 注册事件，初始化的时候所有的事件都被注册了\n     * @param $event 模块事件\n     *\n     */\n    EventManager.registerEvent = function (eventVo) {\n        if (eventVo === null) {\n            return;\n        }\n        var eventClass = eventVo.eventClass;\n        if (eventClass) {\n            if (this._events.get(eventClass) === undefined) {\n                this._events.set(eventClass, []);\n            }\n            //判断是否存在代理\n            var key = eventVo.eventClass;\n            var events = this._events.get(eventVo.eventClass);\n            var find = false;\n            if (events) {\n                for (var i_1 = 0, l = events === null || events === void 0 ? void 0 : events.length; i_1 < l; i_1++) {\n                    if (events[i_1].proxy === eventVo.proxy) {\n                        find = true;\n                        break;\n                    }\n                }\n                if (!find) {\n                    events.push(eventVo);\n                }\n            }\n        }\n    };\n    /**\n     * 某个代理发送事件\n     * @param $proxy 事件代理\n     * @param event 事件\n     *\n     */\n    EventManager.dispatchModuleEvent = function (event) {\n        if (!event) {\n            return;\n        }\n        var eventConstructor = event.getClass();\n        var events = this._events.get(eventConstructor);\n        if (events) {\n            for (var i_2 = 0, l = events.length; i_2 < l; i_2++) {\n                var eventVo = events[i_2];\n                var proxy = eventVo.proxy;\n                var proxyModuleType = proxy.moduleType;\n                if (proxyModuleType === ModuleManager_1.ModuleType.Common || proxyModuleType === ModuleManager_1.ModuleManager.moduleType) {\n                    proxy === null || proxy === void 0 ? void 0 : proxy.receivedEvents(event);\n                }\n            }\n        }\n        var obj = this._eventAllCompleted.get(eventConstructor);\n        if (obj) {\n            var moduleType = ModuleManager_1.ModuleManager.moduleType;\n            // 全部发送\n            if (moduleType === ModuleManager_1.ModuleType.Common) {\n                for (var mt in obj) {\n                    var callbacks = obj[mt];\n                    if (callbacks) {\n                        for (var i_3 = 0; i_3 < callbacks.length; i_3++) {\n                            var callback = callbacks[i_3];\n                            if (callback) {\n                                callback(event);\n                            }\n                        }\n                    }\n                }\n            }\n            else {\n                var callbacks = obj[ModuleManager_1.ModuleManager.moduleType];\n                if (callbacks) {\n                    for (var i_4 = 0; i_4 < callbacks.length; i_4++) {\n                        var callback = callbacks[i_4];\n                        if (callback) {\n                            callback(event);\n                        }\n                    }\n                }\n            }\n        }\n    };\n    /**\n     * 以 Async 的方式分发事件，支持处理异步事件后的回调\n     * @param event\n     * @returns\n     */\n    EventManager.dispatchModuleEventAsync = function (event) {\n        return new Promise(function (resolve) {\n            event['_callback'] = function () { return resolve(); };\n            EventManager.dispatchModuleEvent(event);\n        });\n    };\n    /**\n     * 等待当前事件全部执行完毕之后回调\n     * @param eventConstructor\n     * @param callback\n     */\n    EventManager.onEventAllCompleted = function (moduleType, eventConstructor, callback) {\n        var obj = this._eventAllCompleted.get(eventConstructor);\n        if (!obj) {\n            obj = {};\n            this._eventAllCompleted.set(eventConstructor, obj);\n        }\n        if (!obj[moduleType]) {\n            obj[moduleType] = [];\n        }\n        obj[moduleType].push(callback);\n    };\n    /**\n     * 所有的事件\n     */\n    EventManager._events = new Map();\n    /**\n     * 存储当前事件所有监听完毕\n     */\n    EventManager._eventAllCompleted = new Map();\n    return EventManager;\n}());\nexports.EventManager = EventManager;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.EventVo = void 0;\nvar EventVo = /** @class */ (function () {\n    function EventVo() {\n        this._id = 0;\n        EventVo._uniqueID += 1;\n        this._id = EventVo._uniqueID;\n    }\n    Object.defineProperty(EventVo.prototype, \"id\", {\n        get: function () {\n            return this._id;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    EventVo._uniqueID = 0;\n    return EventVo;\n}());\nexports.EventVo = EventVo;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Module = void 0;\nvar Module = /** @class */ (function () {\n    function Module() {\n        /**注册代理列表*/\n        this._registerProxys = [];\n        /**代理实例集合*/\n        this._proxyClassMap = new Map();\n    }\n    Object.defineProperty(Module.prototype, \"proxyClassMap\", {\n        /** 获取代理集合 */\n        get: function () {\n            return this._proxyClassMap;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Module.prototype, \"moduleType\", {\n        /** 模块类型 */\n        get: function () {\n            return this._moduleType;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**注册代理列表*/\n    Module.prototype.registerProxys = function () {\n        return [];\n    };\n    /**\n     * 获取代理,由于代理全局唯一，因此，只需要通过代理类名引用即可\n     * @param $proxyClass 代理名\n     * @return\n     *\n     */\n    Module.prototype.getProxy = function ($proxyClass) {\n        return this._proxyClassMap.get($proxyClass);\n    };\n    /**启动Proxy*/\n    Module.prototype.startProxy = function (moduleType) {\n        this._registerProxys = this.registerProxys();\n        for (var i_1 = 0, l = this._registerProxys.length; i_1 < l; i_1++) {\n            var proxyClass = this._registerProxys[i_1];\n            if (!this._proxyClassMap.has(proxyClass)) {\n                var _proxyInstance = new proxyClass(this);\n                _proxyInstance['_moduleType'] = moduleType;\n                this._proxyClassMap.set(proxyClass, _proxyInstance);\n                _proxyInstance.startEvents();\n            }\n        }\n    };\n    return Module;\n}());\nexports.Module = Module;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ModuleEvent = void 0;\nvar ModuleEvent = /** @class */ (function () {\n    function ModuleEvent() {\n        this._callback = null;\n    }\n    Object.defineProperty(ModuleEvent.prototype, \"callback\", {\n        get: function () {\n            return this._callback;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * 获取类\n     * @return 当前实例的类名\n     *\n     */\n    ModuleEvent.prototype.getClass = function () {\n        return this['constructor'];\n    };\n    return ModuleEvent;\n}());\nexports.ModuleEvent = ModuleEvent;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ModuleManager = exports.ModuleType = void 0;\n/**\n * 模块类型\n * Common 模块定义的事件：\n * （1）：如果切换到 Class 模块，只能 Class 模块收到\n * （2）：如果切换到 Chapter 模块，只能 Chapter 模块收到\n */\nvar ModuleType;\n(function (ModuleType) {\n    ModuleType[\"Common\"] = \"common\";\n    ModuleType[\"Class\"] = \"class\";\n    ModuleType[\"Chapter\"] = \"journey\";\n})(ModuleType || (exports.ModuleType = ModuleType = {}));\nvar ModuleManager = /** @class */ (function () {\n    function ModuleManager() {\n    }\n    Object.defineProperty(ModuleManager, \"moduleType\", {\n        /**\n         * 获取当前的模块类型\n         */\n        get: function () {\n            return this._moduleType;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * 设置当前运行时的模块类型\n     * @param moduleType\n     */\n    ModuleManager.setCurrentModuleType = function (moduleType) {\n        this._moduleType = moduleType;\n    };\n    Object.defineProperty(ModuleManager, \"moduleList\", {\n        /**\n         * 获取所在的模块\n         * @private\n         */\n        get: function () {\n            return this._moduleList;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * 注册模块\n     * @param $moduleClass\n     *\n     */\n    ModuleManager.resigerModule = function ($modules) {\n        this._modules = $modules;\n    };\n    /**启动各个模块*/\n    ModuleManager.startModule = function (moduleType) {\n        for (var i_1 = 0, l = this._modules.length; i_1 < l; i_1++) {\n            var $moduleClass = this._modules[i_1];\n            if (!this._moduleConstructorMaps.has($moduleClass)) {\n                this._moduleConstructorMaps.set($moduleClass, true);\n                var _instance = new $moduleClass();\n                _instance['_moduleType'] = moduleType;\n                this._moduleList[i_1] = _instance;\n                //启动所有代理\n                _instance.startProxy(moduleType);\n            }\n        }\n    };\n    ModuleManager._moduleList = [];\n    ModuleManager._moduleConstructorMaps = new Map();\n    ModuleManager._moduleType = ModuleType.Common;\n    return ModuleManager;\n}());\nexports.ModuleManager = ModuleManager;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Proxy = void 0;\nvar EventManager_1 = require(\"./EventManager\");\nvar EventVo_1 = require(\"./EventVo\");\nvar ModuleManager_1 = require(\"./ModuleManager\");\nvar Proxy = /** @class */ (function () {\n    /**\n     *\n     * @param $module\n     */\n    function Proxy($module) {\n        this._eventsConstructorMaps = new Map();\n        this._module = $module;\n    }\n    Object.defineProperty(Proxy.prototype, \"moduleType\", {\n        /** 模块类型 */\n        get: function () {\n            return this._moduleType;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * 初始化\n     */\n    Proxy.prototype.onInit = function () {\n    };\n    /**\n     * 获取Proxy注册的事件列表\n     * @return 返回Proxy注册的事件集\n     *\n     */\n    Proxy.prototype.registerEvents = function () {\n        return null;\n    };\n    /**获取类*/\n    Proxy.prototype.getClass = function () {\n        return this['constructor'];\n    };\n    /**获取代理所属模块*/\n    Proxy.prototype.getModule = function () {\n        return this._module;\n    };\n    /**\n     * 接收事件 子类重写此类\n     * @param $event\n     *\n     */\n    Proxy.prototype.receivedEvents = function ($event) { };\n    /**启动事件,一个代理中可能有N种事件 */\n    Proxy.prototype.startEvents = function () {\n        var _events = this.registerEvents();\n        if (_events !== null) {\n            for (var i_1 = 0, l = _events.length; i_1 < l; i_1++) {\n                var eventConstructor = _events[i_1];\n                if (!this._eventsConstructorMaps.has(eventConstructor)) {\n                    this._eventsConstructorMaps.set(eventConstructor, true);\n                    var _eventVo = new EventVo_1.EventVo();\n                    _eventVo.proxy = this;\n                    _eventVo.eventClass = eventConstructor;\n                    EventManager_1.EventManager.registerEvent(_eventVo);\n                }\n            }\n        }\n        this.onInit();\n    };\n    /**\n     * 发送事件\n     * @param $event\n     *\n     */\n    Proxy.prototype.dispatchModuleEvent = function ($event) {\n        if (this.moduleType === ModuleManager_1.ModuleType.Common || this.moduleType === ModuleManager_1.ModuleManager.moduleType) {\n            EventManager_1.EventManager.dispatchModuleEvent($event);\n        }\n    };\n    /**\n     * 以 Async 的方式分发事件，支持处理异步事件后的回调\n     * @param event\n     * @returns\n     */\n    Proxy.prototype.dispatchModuleEventAsync = function ($event) {\n        if (this.moduleType === ModuleManager_1.ModuleType.Common || this.moduleType === ModuleManager_1.ModuleManager.moduleType) {\n            return EventManager_1.EventManager.dispatchModuleEventAsync($event);\n        }\n    };\n    return Proxy;\n}());\nexports.Proxy = Proxy;\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar AdapterFringe_1 = require(\"../assets/scripts/base/adapter/AdapterFringe\");\nvar DragonbonesAnim_1 = require(\"../assets/scripts/base/animation/DragonbonesAnim\");\nvar arrays_1 = require(\"../assets/scripts/base/arrays/arrays\");\nvar Barrier_1 = require(\"../assets/scripts/base/async/Barrier\");\nvar DeferredPromise_1 = require(\"../assets/scripts/base/async/DeferredPromise\");\nvar First_1 = require(\"../assets/scripts/base/async/First\");\nvar Limiter_1 = require(\"../assets/scripts/base/async/Limiter\");\nvar Sequence_1 = require(\"../assets/scripts/base/async/Sequence\");\nvar WaitFor_1 = require(\"../assets/scripts/base/async/WaitFor\");\nvar AudioInfo_1 = require(\"../assets/scripts/base/audio/AudioInfo\");\nvar CacheRender_1 = require(\"../assets/scripts/base/cache/CacheRender\");\nvar CacheComponents_1 = require(\"../assets/scripts/base/components/CacheComponents\");\nvar Component_1 = require(\"../assets/scripts/base/components/Component\");\nvar Copy_1 = require(\"../assets/scripts/base/copy/Copy\");\nvar UrlCrypto_1 = require(\"../assets/scripts/base/crypto/UrlCrypto\");\nvar Date_1 = require(\"../assets/scripts/base/date/Date\");\nvar DecoratorAdapter_1 = require(\"../assets/scripts/base/decorators/DecoratorAdapter\");\nvar DecoratorDebounce_1 = require(\"../assets/scripts/base/decorators/DecoratorDebounce\");\nvar DecoratorMeasure_1 = require(\"../assets/scripts/base/decorators/DecoratorMeasure\");\nvar DecoratorMemoize_1 = require(\"../assets/scripts/base/decorators/DecoratorMemoize\");\nvar DecoratorScreen_1 = require(\"../assets/scripts/base/decorators/DecoratorScreen\");\nvar DecoratorThrottle_1 = require(\"../assets/scripts/base/decorators/DecoratorThrottle\");\nvar DecoratorTrait_1 = require(\"../assets/scripts/base/decorators/DecoratorTrait\");\nvar Dot_1 = require(\"../assets/scripts/base/dot/Dot\");\n//import PanelScaleEase from \"../assets/scripts/base/ease/PanelScaleEase\";\nvar enum_1 = require(\"../assets/scripts/base/enum/enum\");\nvar Equal_1 = require(\"../assets/scripts/base/equal/Equal\");\nvar Events_1 = require(\"../assets/scripts/base/events/Events\");\nvar HotUpdate_1 = require(\"../assets/scripts/base/hotUpdate/HotUpdate\");\nvar Http_1 = require(\"../assets/scripts/base/http/Http\");\nvar Interval_1 = require(\"../assets/scripts/base/interval/Interval\");\nvar ResLoader_1 = require(\"../assets/scripts/base/loader/ResLoader\");\nvar numbers_1 = require(\"../assets/scripts/base/numbers/numbers\");\nvar RenderingOptimization_1 = require(\"../assets/scripts/base/performance/RenderingOptimization\");\nvar ObjectPool_1 = require(\"../assets/scripts/base/pool/ObjectPool\");\nvar Random_1 = require(\"../assets/scripts/base/random/Random\");\nvar Storage_1 = require(\"../assets/scripts/base/storage/Storage\");\nvar GameLayer_1 = require(\"../assets/scripts/base/layer/GameLayer\");\nvar TraitConfigInfo_1 = require(\"../assets/scripts/base/traitConfig/TraitConfigInfo\");\nvar Timeout_1 = require(\"../assets/scripts/base/timeout/Timeout\");\nvar Timer_1 = require(\"../assets/scripts/base/timer/Timer\");\nvar Trait_1 = require(\"../assets/scripts/base/trait/Trait\");\nvar UI_1 = require(\"../assets/scripts/base/ui/UI\");\nvar Task_1 = require(\"../assets/scripts/base/task/Task\");\nvar NativeBridge_1 = require(\"../assets/scripts/base/native/NativeBridge\");\nvar Url_1 = require(\"../assets/scripts/base/url/Url\");\nvar watch_1 = require(\"../assets/scripts/base/watch/watch\");\nvar EventManager_1 = require(\"../assets/scripts/falcon/EventManager\");\nvar EventVo_1 = require(\"../assets/scripts/falcon/EventVo\");\nvar Module_1 = require(\"../assets/scripts/falcon/Module\");\nvar ModuleEvent_1 = require(\"../assets/scripts/falcon/ModuleEvent\");\nvar ModuleManager_1 = require(\"../assets/scripts/falcon/ModuleManager\");\nvar Proxy_1 = require(\"../assets/scripts/falcon/Proxy\");\nvar Types_1 = require(\"../assets/scripts/base/types/Types\");\n// 创建 falcon 命名空间\nvar falcon;\n(function (falcon) {\n    ///////////////////////////////   falcon   ///////////////////////////////\n    // proxy\n    falcon.Proxy = Proxy_1.Proxy;\n    // module\n    falcon.Module = Module_1.Module;\n    falcon.ModuleManager = ModuleManager_1.ModuleManager;\n    // events\n    falcon.Emitter = Events_1.Emitter;\n    falcon.EventManager = EventManager_1.EventManager;\n    falcon.EventVo = EventVo_1.EventVo;\n    falcon.ModuleEvent = ModuleEvent_1.ModuleEvent;\n    ///////////////////////////////   base   ///////////////////////////////\n    // adapter\n    falcon.adapter = {\n        applyAdapterFringe: AdapterFringe_1.applyAdapterFringe\n    };\n    // 播放龙骨动画\n    falcon.dragonbonesAnim = DragonbonesAnim_1.dragonbonesAnim;\n    // arrays\n    falcon.arrays = {\n        arraysHaveSameElements: arrays_1.arraysHaveSameElements,\n        shuffleArray: arrays_1.shuffleArray,\n        arraysEqual: arrays_1.arraysEqual,\n        ensureMaxLength: arrays_1.ensureMaxLength\n    };\n    // async\n    falcon.async = {\n        TimeoutBarrier: Barrier_1.TimeoutBarrier,\n        Barrier: Barrier_1.Barrier,\n        DeferredPromise: DeferredPromise_1.DeferredPromise,\n        firstParallel: First_1.firstParallel,\n        first: First_1.first,\n        Limiter: Limiter_1.Limiter,\n        sequence: Sequence_1.sequence,\n        waitFor: WaitFor_1.waitFor\n    };\n    // audio\n    falcon.audioInfo = AudioInfo_1.audioInfo;\n    // cache\n    falcon.cacheRender = CacheRender_1.cacheRender;\n    falcon.cacheComponents = CacheComponents_1.cacheComponents;\n    // components\n    falcon.Component = Component_1.default;\n    // copy\n    falcon.copy = {\n        deepCopy: Copy_1.deepCopy,\n        deepCopyArrayFrom: Copy_1.deepCopyArrayFrom,\n        deepCopySlice: Copy_1.deepCopySlice,\n        deepCopyLoop: Copy_1.deepCopyLoop,\n        deepCopyFixed: Copy_1.deepCopyFixed\n    };\n    // crypto\n    falcon.UrlCrypto = UrlCrypto_1.UrlCrypto;\n    // date\n    falcon.date = {\n        getLastSomeDays: Date_1.getLastSomeDays,\n        getTodayDate: Date_1.getTodayDate,\n        getDiffDays: Date_1.getDiffDays\n    };\n    // decorators\n    falcon.adapterFringe = DecoratorAdapter_1.adapterFringe;\n    falcon.throttle = DecoratorThrottle_1.throttle;\n    falcon.debounce = DecoratorDebounce_1.debounce;\n    falcon.measure = DecoratorMeasure_1.measure;\n    falcon.memoize = DecoratorMemoize_1.memoize;\n    falcon.trait = DecoratorTrait_1.trait;\n    falcon.templateTrait = DecoratorTrait_1.templateTrait;\n    falcon.ScreenAdapter = DecoratorScreen_1.ScreenAdapter;\n    // dot\n    falcon.createDotData = Dot_1.createDotData;\n    // // ease\n    // export const ease = {\n    //     PanelScaleEase\n    // };\n    // enum utils (重命名以避免与关键字冲突)\n    falcon.enumUtils = {\n        getKeyByValue: enum_1.getKeyByValue,\n        isValueInEnum: enum_1.isValueInEnum\n    };\n    // equal utils (重命名以避免与函数名冲突)\n    falcon.equalUtils = {\n        equal: Equal_1.equal\n    };\n    // hot update utils (重命名以避免与函数名冲突)\n    falcon.hotUpdate = HotUpdate_1.hotUpdate;\n    // http utils (重命名以避免与函数名冲突)\n    falcon.http = Http_1.http;\n    // interval utils (重命名以避免与函数名冲突)\n    falcon.intervalUtils = {\n        interval: Interval_1.interval\n    };\n    // NativeBridge\n    falcon.NativeBridge = NativeBridge_1.NativeBridge;\n    // loader\n    falcon.ResLoader = ResLoader_1.ResLoader;\n    // loader\n    falcon.layerManager = GameLayer_1.layerManager;\n    falcon.layer = {\n        gameUiLayer: GameLayer_1.gameUiLayer,\n        addWidget: GameLayer_1.addWidget,\n        initNodeConfig: GameLayer_1.initNodeConfig\n    };\n    // numbers\n    falcon.numbers = {\n        randomInt: numbers_1.randomInt,\n        randomFloat: numbers_1.randomFloat\n    };\n    // performance\n    falcon.performance = {\n        executeRenderingOptimize: RenderingOptimization_1.executeRenderingOptimize\n    };\n    // pool\n    falcon.ObjectPool = ObjectPool_1.ObjectPool;\n    // random\n    falcon.random = {\n        randomList: Random_1.randomList\n    };\n    // storage utils (重命名以避免与函数名冲突)\n    falcon.storage = Storage_1.storage;\n    // timeout utils (重命名以避免与函数名冲突)\n    falcon.timeoutUtils = {\n        timeout: Timeout_1.timeout\n    };\n    // traitConfig\n    falcon.traitConfigInfo = TraitConfigInfo_1.traitConfigInfo;\n    // traitConfig\n    falcon.task = Task_1.task;\n    // timer\n    falcon.timer = {\n        nextFrame: Timer_1.nextFrame,\n        callLater: Timer_1.callLater\n    };\n    // type\n    falcon.typeUtils = {\n        isString: Types_1.isString,\n        isStringArray: Types_1.isStringArray,\n        isObject: Types_1.isObject,\n        isTypedArray: Types_1.isTypedArray,\n        isNumber: Types_1.isNumber,\n        isIterable: Types_1.isIterable,\n        isBoolean: Types_1.isBoolean,\n        isUndefined: Types_1.isUndefined,\n        isDefined: Types_1.isDefined,\n        isUndefinedOrNull: Types_1.isUndefinedOrNull,\n        isFunction: Types_1.isFunction,\n        getType: Types_1.getType\n    };\n    // trait utils (重命名以避免与类名冲突)\n    falcon.Trait = Trait_1.Trait;\n    // ui\n    falcon.UI = UI_1.UI;\n    // url\n    falcon.url = {\n        getUrlParameterValue: Url_1.getUrlParameterValue\n    };\n    // watch utils (重命名以避免与函数名冲突)\n    falcon.watchUtils = {\n        reactive: watch_1.reactive,\n        watch: watch_1.watch\n    };\n})(falcon || (falcon = {}));\n// 将 falcon 命名空间挂载到全局\nwindow.falcon = falcon;\n// 导出 falcon 命名空间\nexports.default = falcon;\n"], "names": ["Object", "defineProperty", "exports", "value", "applyAdapterFringe", "node", "widget", "getComponent", "cc", "Widget", "size", "view", "getFrameSize", "size2", "getVisibleSize", "width", "height", "sys", "os", "OS_ANDROID", "<PERSON><PERSON><PERSON><PERSON>", "IPHONE_X_HEIGHT_WIDTH_RATIO", "is<PERSON>ringe", "Math", "max", "top", "IPHONE_X_TOP_ADJUSTMENT", "dragonbonesAnim", "ResLoader_1", "DragonBonesAnim", "this", "dragonbones", "prototype", "play", "parentNode", "armatureName", "animationName", "playTimes", "config", "_this", "undefined", "bundleName", "dragonAssetUrl", "dragonAtlasAssetUrl", "frameSplitting", "Node", "dragonBonesArmatureDisplay", "addComponent", "dragonBones", "ArmatureDisplay", "<PERSON><PERSON><PERSON><PERSON>", "enableBatch", "updateAnimationCache", "setAnimationCacheMode", "AnimationCacheMode", "SHARED_CACHE", "completeBack", "completeBackObj", "addEventListener", "EventObject", "COMPLETE", "setTimeout", "_play", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "renderDragonbonesByBundle", "renderDragonbones", "__read", "o", "n", "m", "Symbol", "iterator", "r", "e", "i", "call", "ar", "next", "done", "push", "error", "arraysHaveSameElements", "arr1", "arr2", "length", "sortedArr1", "slice", "sort", "sortedArr2", "shuffle<PERSON><PERSON><PERSON>", "array", "_a", "<PERSON><PERSON><PERSON><PERSON>", "i_1", "j", "floor", "random", "arraysEqual", "i_2", "ensureMaxLength", "arr", "max<PERSON><PERSON><PERSON>", "newItem", "shift", "extendStatics", "__extends", "d", "b", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "TypeError", "String", "__", "constructor", "create", "Timeout<PERSON><PERSON>rier", "Barrier", "_isOpen", "_promise", "Promise", "c", "_completePromise", "get", "enumerable", "configurable", "reset", "open", "wait", "_super", "autoOpenTimeMs", "_timeout", "clearTimeout", "DeferredPromise", "_resolved", "_promiseQueue", "resolve", "reject", "_resolvedValue", "console", "warn", "preConsumeLen", "reason", "__values", "s", "first", "promiseFactories", "shouldStop", "defaultValue", "t", "index", "len", "loop", "factory", "then", "result", "firstParallel", "promiseList", "todo", "finish", "e_1", "_b", "_c", "promiseList_1", "promiseList_1_1", "cancel", "e_1_1", "return", "e_2", "promiseList_2", "promiseList_2_1", "catch", "err", "e_2_1", "Limiter", "Events_1", "maxDegreeOfParalellism", "_size", "_isDisposed", "outstandingPromises", "runningPromises", "_onDrained", "Emitter", "event", "queue", "Error", "consume", "iLimitedTask", "promise", "consumed", "fire", "clear", "dispose", "__awaiter", "thisArg", "_arguments", "P", "generator", "fulfilled", "step", "rejected", "apply", "__generator", "body", "f", "y", "_", "label", "sent", "trys", "ops", "g", "Iterator", "verb", "v", "op", "pop", "__spread<PERSON><PERSON>y", "to", "from", "pack", "arguments", "l", "concat", "sequence", "interruptConfition", "results", "<PERSON><PERSON><PERSON><PERSON>", "checkInterruptCondition", "isInterrupt", "fill", "waitFor", "<PERSON><PERSON><PERSON>", "_waitFors", "start", "key", "_resolve", "_reject", "startTime", "Date", "now", "state", "end", "waitInfo", "endTime", "option", "resolve_1", "reject_1", "interval", "setInterval", "clearInterval", "isNaN", "timeout", "audioInfo", "AudioType", "Types_1", "Storage_1", "AudioInfo", "url", "volume", "type", "isUndefined", "EFFECT", "audioSwitch", "storage", "getItem", "bgmSwitch", "SOUND", "loadByBundle", "AudioClip", "clip", "load", "CC_DEBUG", "audioEngine", "playMusic", "stop", "stopAllEffects", "stopMusic", "cacheRender", "<PERSON><PERSON><PERSON><PERSON>", "_caches", "createOrUpdateCacheListComponents", "prefabUrl", "count", "typeOrClassName", "parent", "onComplete", "asset", "caches", "itemCaches", "cacheLen", "cacheComp", "active", "comps", "instantiate", "Prefab", "cacheComponents", "CacheComponents", "_cacheComponents", "componentEnabledIntercept", "_componentOnEnabledIntercept", "originalCompSchedulerOnEnabled_1", "director", "_onEnabled", "comp", "_cacheAllComponents", "set", "resolves", "_resolves", "Cinst", "CompClass", "CinstAsync", "resovles", "Map", "__decorate", "decorators", "target", "desc", "getOwnPropertyDescriptor", "Reflect", "decorate", "_decorator", "ccclass", "Component", "property", "_state", "setState", "replace", "callback", "shouldUpdate", "shouldComponentUpdate", "toString", "deepCopy", "componentWillUpdate", "render", "source", "componentWillMount", "componentDidMount", "componentWillUnmount", "nextState", "componentDidUpdate", "prevState", "gameData", "map", "row", "deepCopyArrayFrom", "deepCopySlice", "deepCopyLoop", "rowLen", "newRow", "deepCopyFixed", "<PERSON><PERSON><PERSON><PERSON>", "srcRow", "actualRowLength", "ICrypto", "encrypt", "content", "decrypt", "UrlCrypto", "base64Str", "<PERSON><PERSON><PERSON>", "_des", "decryptStr", "paramStr", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "encodeKey", "encodeStr", "str", "indexOf", "getLastSomeDays", "day", "currentTime", "days", "data", "setTime", "getFullYear", "getMonth", "getDate", "getTodayDate", "today", "getDiffDays", "date1", "date2", "date1WithoutTime", "setHours", "date2WithoutTime", "diffTime", "abs", "getTime", "ceil", "adapterFringe", "nodeNames", "_i", "propertyKey", "descriptor", "originalMethod", "args", "for<PERSON>ach", "nodeName", "AdapterFringe_1", "classId", "className", "getClassName", "objOrCtor", "js", "window", "decorator", "_target", "fnKey", "fn", "debounce", "delay", "timer<PERSON><PERSON>", "oldTimes", "curTime", "measure", "performance", "log", "memoize", "memoize<PERSON>ey", "writable", "ScreenAdapter", "winsize", "_canvas", "<PERSON><PERSON>", "instance", "fitHeight", "fit<PERSON><PERSON><PERSON>", "throttle", "immediate", "oldtime", "timer", "context", "newtime", "throttleCall", "func", "shareObjects", "setPerformanceTieringType", "onDidTraitOnActive", "GBMTraitsMaps", "traitMaps", "decoratorTraitsClassNameMap", "trait", "traitClass", "methodName", "has", "methodShareObject", "shareTraitTarget", "returnState", "method", "traitInst", "onCreate", "subTraits", "registerSubTraits", "subTraitsInst", "subTraitClass", "subTraitInst", "traitClassName", "returnValue", "traitClassInfo", "TraitConfigInfo_1", "traitConfigInfo", "traitsClassNameMap", "props", "param", "keyValue", "keys", "_performanceTieringType", "id", "subTrait", "subTraitClassInfo", "traitName", "templateTraitRefList", "templateTraits", "templateTraitActiveState", "i_3", "templateProps", "originalCaller", "listenerTraitOnActive", "onActive", "CC_DEV", "activeTraits", "_onDidTraitOnActive", "shareTraitTargetReturnValue", "GBM", "GBMTraitClass", "gbmTraitInst", "templateTrait", "traitClassNameList", "templateTraitName", "Trait_1", "inst", "activeListeners", "<PERSON><PERSON><PERSON>", "activedListenerTraits", "i_4", "activeListener", "instanceClass", "DotUploadType", "createDotData", "uploadType", "eventKey", "params", "traits", "time", "gameWayNum", "nowWayArr", "ABStatus", "_gameWayNum", "active_waynum", "pici", "assign", "MARKET", "name", "traitDotInfo", "class", "DecoratorTrait_1", "dotTraitInst", "getTraitActiveStatus", "getKeyByValue", "enumObj", "isValueInEnum", "values", "includes", "equal", "obj1", "obj2", "visited", "visited_1", "visited_1_1", "entry", "Number", "Boolean", "valueOf", "isArray", "keysA", "keysB", "keysA_1", "keysA_1_1", "options", "_options", "_event", "thisArgs", "_d", "_e", "_f", "bind", "_callbacks", "onWillAddFirstListener", "onDidAddFirstListener", "onDidAddListener", "_disposed", "onDidRemoveLastListener", "EventCodeEnum", "hotUpdate", "LOCAL_GAME_VERSION_KEY", "EventCodeMap", "ERROR_NO_LOCAL_MANIFEST", "ERROR_DOWNLOAD_MANIFEST", "ERROR_PARSE_MANIFEST", "NEW_VERSION_FOUND", "ALREADY_UP_TO_DATE", "UPDATE_PROGRESSION", "ASSET_UPDATED", "ERROR_UPDATING", "UPDATE_FINISHED", "UPDATE_FAILED", "ERROR_DECOMPRESS", "STOP_WHENUPDATE", "START", "FAIL", "HotUpdate", "currentFileNum", "currentFileTotal", "projectManifestStr", "updating", "failCount", "remoteVersionManifest", "am", "_storagePath", "_emit", "cb", "onHotUpdateState", "versionCompareHandle", "versionA", "versionB", "vA", "split", "vB", "a", "parseInt", "updateRemoteUrl", "manifest", "versionManifest", "url1", "url2", "url3", "downgrade", "version", "major", "minor", "patch", "updateStorageData", "storageManifestUrl", "jsb", "fileUtils", "isFileExist", "storageManifestStr", "getStringFromFile", "storageManifestJson", "JSON", "parse", "stringify", "writeStringToFile", "tempStr", "setStorage", "localStorage", "setItem", "getStorage", "startUpdate", "projectManifest", "isNative", "eventCode", "msg", "getWritablePath", "<PERSON><PERSON><PERSON>ana<PERSON>", "setVerifyCallback", "path", "compressed", "md5", "checkUpdate", "getState", "State", "UNINITED", "Manifest", "loadLocalManifest", "getLocalManifest", "getVersion", "manifestRoot", "getManifestRoot", "packageUrl", "getPackageUrl", "manifestUrl", "getManifestFileUrl", "versionUrl", "getVersionFileUrl", "isLoaded", "setEventCallback", "checkCb", "getEventCode", "eventCodeStr", "isFailed", "EventAssetsManager", "getTotalBytes", "jsbEvent", "getMessage", "updateCb", "update", "isUpdateFinished", "getAssetId", "downloadFiles", "getDownloadedFiles", "totalFiles", "getTotalFiles", "downloadBytes", "getDownloadedBytes", "totalBytes", "downloadFailedAssets", "removeFile", "manifestSearchPaths", "getSearchPaths", "HttpType", "CrytoType", "http", "Http", "callbackId", "callbacks", "requestAsync", "request", "success", "fail", "xhr", "loader", "getXMLHttpRequest", "dataStr", "contentType", "setRequestHeader", "onreadystatechange", "readyState", "response", "responseText", "status", "crypto", "send", "<PERSON><PERSON><PERSON><PERSON>", "initNodeConfig", "scene", "zIndex", "opacity", "hidden", "addWidget", "layerManager", "LayerManager", "gameUiLayer", "BASE_LAYER_CONFIGS", "layerMap", "init", "removeFromParent", "destroy", "add<PERSON><PERSON>er", "<PERSON><PERSON><PERSON>er", "setLayerNode", "layer", "delete", "<PERSON><PERSON><PERSON><PERSON>", "showLayer", "<PERSON><PERSON><PERSON><PERSON>", "setLayerOpacity", "getLayerTypes", "anchorX", "anchorY", "isAlignTop", "isAlignLeft", "isAlignRight", "isAlignBottom", "bottom", "left", "right", "BundleName", "events", "isRemote", "test", "getAsset", "cacheResources", "paths", "onCompletes", "self_1", "_complete", "loadEvents", "assetManager", "loadRemote", "resources", "asyncLoad", "assets", "loadBundle", "bundle", "res", "asyncLoadByBundle", "nameOrUrl", "self", "i_5", "bundlePreloadScene", "scene<PERSON><PERSON>", "preloadScene", "i_6", "bundlePreload", "preload", "items", "i_7", "renderSprite", "sprite", "SpriteFrame", "spriteFrame", "dragonAssetAtlasUrl", "anim<PERSON><PERSON>", "playtimes", "loadCount", "<PERSON><PERSON><PERSON><PERSON>", "playAnimation", "DragonBonesAsset", "dragonAsset", "DragonBonesAtlasAsset", "dragonAtlasAsset", "completeRemove", "NativeBridge", "ANDROID_ACTIVE", "methodSignature", "reflection", "callStaticMethod", "randomInt", "min", "randomFloat", "executeRenderingOptimize", "CC_EDITOR", "_active", "hasFullScreenComponent", "find", "__fullscreen__", "hide_1", "Scene", "children", "__inactives__", "executeFullScreenComponent", "_parent", "_activeInHierarchy", "activateNode", "ObjectPool", "_create", "_reset", "_option", "pool", "inUsed", "Set", "_init", "onCreateComplete", "obj", "error_1", "add", "isInUsed", "asyncGet", "error_2", "release", "randomList", "list", "repeated", "useNewList", "temp", "shuffle", "Storage", "cacheData", "prefixedKey", "prefix", "memoryValue", "valueType", "storageValue", "valueTypeSplit", "DOMException", "code", "item", "remove", "removeItem", "task", "Task", "taskList", "run", "<PERSON><PERSON><PERSON><PERSON>", "requestIdleCallback", "didTimeout", "timeRemaining", "cancelIdleCallback", "handler", "taskHandle", "idleRequestCallback", "deadline", "task_1", "next<PERSON><PERSON><PERSON>", "callLater", "thisObj", "once", "Director", "EVENT_AFTER_UPDATE", "_props", "_subTraits", "_activedListenerTraits", "_id", "_condition", "eval", "stack", "onEnable", "onDisable", "getSubTraits", "onTraitActive", "traitConstructor", "preActive", "actived", "TraitConfigInfo", "_traits", "_traitsById", "_useTraits", "_cacheUsedTraits", "_traitsClassNameMap", "_useActiveUserTraits", "_cacheUsedActiveUserTraits", "initialize", "cfg", "feature", "traitItemInfoList", "traitItemInfo", "createUseTraits", "_curActiveConfig", "features", "plan", "createActiveTraits", "ActiveFeatures", "createActiveTraitClassNameMaps", "traitIdInfo", "char<PERSON>t", "toUpperCase", "createWhiteTraitClassNameMaps", "whiteList", "loadDynamicTrait", "dynamicTraitList", "traitData", "featureObj", "uid", "traitActiveUserData", "isString", "isUndefinedOrNull", "isStringArray", "every", "elem", "isObject", "RegExp", "isTypedArray", "TypedArray", "getPrototypeOf", "Uint8Array", "isNumber", "isIterable", "isBoolean", "isDefined", "arg", "isFunction", "getType", "UI", "GameLayer_1", "setModalPrefab", "modalUrl", "hide<PERSON>ll", "uiCache", "activeState", "cancelLoad", "uiIsCancelLoad", "clearCache", "uiIsLoading", "removeEventListener", "splice", "show", "ui", "modalAsset", "modalNode_1", "createEvents", "modalNode", "panelEase", "currentResolves", "openEvents", "err_1", "page", "lastPage", "hide", "ease", "modal", "on", "EventType", "TOUCH_START", "setParent", "setSiblingIndex", "childrenCount", "addUICache", "cacheUI", "cacheNode", "hideUI", "onCloseEaseComplete", "dispatchClose", "closeEvents", "ele", "getUrlParameterValue", "paramName", "globalThis", "location", "vars", "search", "substring", "pair", "decodeURIComponent", "ReactivePos", "reactive", "propertyName", "pos", "properties", "watchMap", "watch", "propertykey", "proxy", "_reactive", "newValue", "watchTarget", "receiver", "oldValue", "properties_1", "Proxy", "EventManager", "ModuleManager_1", "_events", "registerEvent", "eventVo", "eventClass", "dispatchModuleEvent", "eventConstructor", "getClass", "proxyModuleType", "moduleType", "ModuleType", "Common", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "receivedEvents", "_eventAllCompleted", "mt", "dispatchModuleEventAsync", "onEventAllCompleted", "EventVo", "_uniqueID", "<PERSON><PERSON><PERSON>", "_registerProxys", "_proxyClassMap", "_moduleType", "registerProxys", "getProxy", "$proxyClass", "startProxy", "proxyClass", "_proxyInstance", "startEvents", "ModuleEvent", "_callback", "setCurrentModuleType", "_moduleList", "resigerModule", "$modules", "_modules", "startModule", "$moduleClass", "_moduleConstructorMaps", "_instance", "EventManager_1", "EventVo_1", "$module", "_eventsConstructorMaps", "_module", "onInit", "registerEvents", "getModule", "$event", "_eventVo", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "module", "__webpack_modules__", "falcon", "DragonbonesAnim_1", "arrays_1", "Barrier_1", "DeferredPromise_1", "First_1", "Limiter_1", "Sequence_1", "WaitFor_1", "AudioInfo_1", "CacheRender_1", "CacheComponents_1", "Component_1", "Copy_1", "UrlCrypto_1", "Date_1", "DecoratorAdapter_1", "DecoratorDebounce_1", "DecoratorMeasure_1", "DecoratorMemoize_1", "DecoratorScreen_1", "DecoratorThrottle_1", "Dot_1", "enum_1", "Equal_1", "HotUpdate_1", "Http_1", "Interval_1", "numbers_1", "RenderingOptimization_1", "ObjectPool_1", "Random_1", "Timeout_1", "Timer_1", "UI_1", "Task_1", "NativeBridge_1", "Url_1", "watch_1", "Module_1", "ModuleEvent_1", "Proxy_1", "adapter", "arrays", "async", "default", "copy", "date", "enumUtils", "equalUtils", "intervalUtils", "numbers", "timeoutUtils", "typeUtils", "watchUtils"], "sourceRoot": ""}