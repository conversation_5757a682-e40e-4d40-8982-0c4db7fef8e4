import { System } from '../../../../advmain/scripts/modules/level/ecs/cores/System';
import BoardComponent from '../../../../advmain/scripts/modules/level/ecs/components/board/BoardComponent';
import NodeComponent from '../../../../advmain/scripts/modules/level/ecs/components/NodeComponent';
import { BoardScene } from '../../../../advmain/scripts/modules/level/ecs/components/board/BoardScene';
import { RelationName } from '../../../../advmain/scripts/modules/level/ecs/cores/center/EntityCenter/WorldRelation';
import { CellComponent } from '../../../../advmain/scripts/modules/level/ecs/components/board/CellComponent';
import { ECSEvent } from '../../../../advmain/scripts/modules/level/ecs/GameEvent';

/**
 * 单个棋盘节点缓动任务
 */
interface TweenTask {
    entity: number;
    start: { x: number; y: number; scaleX: number; scaleY: number };
    target: { x: number; y: number; scaleX: number; scaleY: number };
    elapsed: number;
}

/**
 * BoardSwitchSystem
 * 负责在运行时切换主/镜像棋盘数据：
 *  1. 交换两块棋盘的数据（包括层堆栈）
 *  2. 交换 isCanPut 标记，使镜像盘可放置，原主盘只展示
 * 调用 switchBoards() 方法即可完成一次切换。
 */
export default class BoardSwitchSystem extends System {
    /** 缓动动画总时长（秒） */
    private static readonly DURATION = 0.3;

    /** 动画任务队列 */
    private _tweenTasks: TweenTask[] = [];

    /** 是否正在切换中 */
    private _switching = false;

    /** 辅助：向任务队列添加一个缓动 */
    private addTweenTask(entity: number, start: NodeComponent, target: NodeComponent) {
        this._tweenTasks.push({
            entity,
            start: { x: start.x, y: start.y, scaleX: start.scaleX, scaleY: start.scaleY },
            target: { x: target.x, y: target.y, scaleX: target.scaleX, scaleY: target.scaleY },
            elapsed: 0,
        });
    }

    /**
     * 处理 SWITCH_BOARD 事件：交换两个棋盘实体的 NodeComponent 信息（含平滑动画）。
     */
    private switchBoardEntity() {
        if (this._switching) return;
        this._switching = true;
        // 自动查找当前两块棋盘（约定仅存在两块棋盘）
        const boards = this.world.query([BoardComponent]);
        let mainEntity: number = null;
        let mirrorEntity: number = null;
        let boardMainComp: BoardComponent = null;
        let boardMirrorComp: BoardComponent = null;
        boards.forEach((eid) => {
            const comp = this.world.getComponent(eid, BoardComponent);
            if (comp.isCanPut) {
                mainEntity = eid;
                boardMainComp = comp;
            } else {
                boardMirrorComp = comp;
                mirrorEntity = eid;
            }
        });

        if (mainEntity == null || mirrorEntity == null) return;
        const mainNode = this.world.getComponent(mainEntity, NodeComponent);
        const mirrorNode = this.world.getComponent(mirrorEntity, NodeComponent);
        if (!mainNode || !mirrorNode) return;

        // 创建缓动任务
        this.addTweenTask(mainEntity, mainNode, mirrorNode);
        this.addTweenTask(mirrorEntity, mirrorNode, mainNode);
        let tempIsCanput = boardMainComp.isCanPut;
        // 立即交换逻辑属性（放置权限 & 名称）
        boardMainComp.isCanPut = boardMirrorComp.isCanPut;
        boardMirrorComp.isCanPut = tempIsCanput;
        const tempName = boardMainComp.boardId;
        boardMainComp.boardId = boardMirrorComp.boardId;
        boardMirrorComp.boardId = tempName;
        this.switchWaitCellColor();
    }

    switchWaitCellColor() {
        let mainBoard = this.world.query([BoardComponent]).filter((entity) => {
            return this.world.getComponent(entity, BoardComponent).isCanPut;
        });
        let boardComp = this.world.getComponent(mainBoard[0], BoardComponent);
        let boardScene = this.world.utilCenter.boardUtil.getSceneEntity();
        let boardSceneComp = this.world.getComponent(boardScene, BoardScene);
        let waitPutCells = boardSceneComp.waitPutCells;
        for (let i: number = 0; i < waitPutCells.length; i++) {
            let childs = this.world.getTargets(waitPutCells[i], RelationName.PARENT_CHILD);
            for (let j: number = 0; j < childs.length; j++) {
                let cellComp = this.world.getComponent(childs[j], CellComponent);
                //TODO 这里要查一下为什么父子关系不对
                if (cellComp == null) continue;
                cellComp.temp.color = boardComp.boardCellColor;
                cellComp.oriColor = boardComp.boardCellColor;
            }
        }
    }

    /**插值更新动画*/
    update(dt: number): void {
        if (this._tweenTasks.length === 0) return;
        this._tweenTasks = this._tweenTasks.filter((t: TweenTask) => {
            t.elapsed += dt;
            const ratio = Math.min(t.elapsed / BoardSwitchSystem.DURATION, 1);
            const node = this.world.getComponent(t.entity, NodeComponent);
            if (node) {
                node.x = t.start.x + (t.target.x - t.start.x) * ratio;
                node.y = t.start.y + (t.target.y - t.start.y) * ratio;
                node.scaleX = t.start.scaleX + (t.target.scaleX - t.start.scaleX) * ratio;
                node.scaleY = t.start.scaleY + (t.target.scaleY - t.start.scaleY) * ratio;
            }
            return ratio < 1;
        });

        // 若所有动画结束，允许再次切换
        if (this._tweenTasks.length === 0 && this._switching) {
            this._switching = false;
        }
    }

    init(): void {
        this.world.eventBus.on(ECSEvent.GameEvent.SWITCH_BOARD, this.switchBoardEntity, this);
    }
    dispose(): void {
        this.world.eventBus.off(ECSEvent.GameEvent.SWITCH_BOARD, this.switchBoardEntity, this);
    }
}
