import { BoardScene } from '../components/board/BoardScene';
import ReviveComponent from '../components/ReviveComponent';
import { ECSEvent } from '../GameEvent';
import { TempleteType } from '../registry/templete/TempleteRegistry';
import { ECSRuleBase, IECSRuleConfig } from './core/ECSRuleBase';
import { MathUtil } from '../utils/MathUtil';
import TargetComponent from '../components/special/TargetComponent';
import { BoardSceneColorsComponent } from '../components/special/BoardSceneColorsComponent';
import { CellOption, DEFAULT_CELL_RENDER_OPTION } from '../template/CellTemplete';
import { RelationName } from '../cores/center/EntityCenter/WorldRelation';
import { ShapeMargin } from '../define/EcsDefine';
import { RcShape } from '../define/EcsConfig';
import { CellType, TargetType, CELL_TEMPLATE_PARAM, BoardLayer, NormalColor7 } from '../define/BoardDefine';

/**生成块结束规则 */
export class ProduceBlockEndRule extends ECSRuleBase<IECSRuleConfig, ECSEvent.GameEvent.PRODUCE_BLOCK_END> {
    public bindEventData() {
        return ECSEvent.GameEvent.PRODUCE_BLOCK_END;
    }
    canExecute(): boolean {
        return true;
    }
    execute(): void {
        const info = this.eventData;
        const world = this.world;
        console.log('生成块结束', info);
        const sceneEntity = this.world.utilCenter.boardUtil.getSceneEntity();
        let blockIds = info.blockIds;

        // 检查是否是复活预览模式
        const reviveComp = world.getComponent(sceneEntity, ReviveComponent);
        if (reviveComp && reviveComp.isPreviewMode) {
            // 预览模式：不创建实际的可拖拽块，只缓存数据
            console.log('预览模式：缓存复活块数据', info.blockIds);
            return;
        }

        // 销毁上一次创建的实体
        const boardSceneComp = world.getComponent(sceneEntity, BoardScene)!;
        if (boardSceneComp.waitPutCells.length > 0) {
            // 销毁所有待放置的实体及其子实体
            for (const shapeEntityId of boardSceneComp.waitPutCells) {
                const childEntities = world.getTargets(shapeEntityId, RelationName.PARENT_CHILD);
                for (const childEntityId of childEntities) {
                    world.destroyEntity(childEntityId);
                }
                world.destroyEntity(shapeEntityId);
            }
            // 清空待放置列表
            boardSceneComp.waitPutCells = [];
        }

        //todo 当前生成物规则都是临时写法
        for (let i = 0; i < blockIds.length; i++) {
            const rcShape = RcShape[blockIds[i]];
            const templeteCenter = world.templeteCenter;
            const shapeEntity = templeteCenter.createTempleteEntity(TempleteType.Shape, {
                rcShape,
                x: (i - 1) * ShapeMargin,
                parentEntity: world.ECSLayerType.SceneLayerEntity,
            });
            // 随机颜色
            const color = this.getColor();
            // 获取收集目标类型
            const cellUtil = world.utilCenter.cellUtil;
            const collectTypes = world
                .getComponent(world.query([TargetComponent])[0], TargetComponent)
                .targets.filter((t) => cellUtil.isCell(t.key))
                .map((t) => t.key as CellType);
            const currentCollectibles = this.getCurrentCollectibles();
            rcShape.shape.forEach((rc) => {
                let type: CellType;
                // 40% 概率生成收集块
                const isCollect = collectTypes.length > 0 && Math.random() < 0.4;
                if (isCollect) {
                    type = collectTypes[MathUtil.randomInt(collectTypes.length - 1)];
                } else {
                    // 10%概率生成飞机块
                    if (Math.random() < 0.1) {
                        type = TargetType.Plane;
                    } else {
                        type = TargetType.Normal;
                    }
                }
                //是否需要填充元素层
                let layer = DEFAULT_CELL_RENDER_OPTION.layer;
                const cellOption: CellOption = { type, color };
                //临时写法，实际所有数据都会在生成块规则内添加
                if (type === TargetType.Glass) {
                    const cellParam = CELL_TEMPLATE_PARAM[type];
                    if (cellParam.layer) layer = cellParam.layer;
                    cellOption.hp = cellParam.components[0].defaultArg;
                }

                const slotEntity = templeteCenter.createTempleteEntity(TempleteType.Slot, {
                    rc,
                    parentSize: rcShape,
                    parentEntity: shapeEntity,
                });

                if (layer > BoardLayer.Element) {
                    templeteCenter.createTempleteEntity(TempleteType.Cell, {
                        rc,
                        parentSize: rcShape,
                        parentEntity: shapeEntity,
                        cellOption: { color, type: Math.random() < 0.1 ? TargetType.Plane : TargetType.Normal },
                        slotEntity,
                    });
                }
                templeteCenter.createTempleteEntity(TempleteType.Cell, {
                    rc,
                    parentSize: rcShape,
                    parentEntity: shapeEntity,
                    cellOption,
                    slotEntity,
                });
            });
            let boardSceneComp = world.getComponent(sceneEntity, BoardScene)!;
            boardSceneComp.waitPutCells.push(shapeEntity);
        }
    }

    // 当前可以产出的收集物
    getCurrentCollectibles(): TargetType[] {
        const world = this.world;
        const results: TargetType[] = [];
        const targets = world.getComponent(world.query([TargetComponent])[0], TargetComponent).targets;
        for (let i = 0, len = targets.length; i < len; i++) {
            const t = targets[i];
            if (t.current < t.to) {
                results.push(t.key as TargetType);
            }
        }
        return results;
    }

    // 40% 概率生成收集块
    // shouldGenerateCollectCell(collectTypes: TargetType[]): CellType {
    // const isCollect = collectTypes.length > 0 && Math.random() < 0.4;
    // let type: CellType;
    // if (isCollect) {
    //     type = collectTypes[MathUtil.randomInt(collectTypes.length - 1)];
    // } else if (Math.random() < 0.1) {
    //     type = TargetType.Plane;
    // } else {
    //     type = TargetType.Normal;
    // }
    // return type;
    // }

    getColor() {
        const world = this.world;
        const cellColorsEntities = world.query([BoardSceneColorsComponent]);
        if (!cellColorsEntities?.length) {
            console.warn('没有找到 CellColorsComponent 实体，使用默认颜色');
            return NormalColor7[MathUtil.randomInt(NormalColor7.length - 1)];
        }

        const colors = world.getComponent(cellColorsEntities[0], BoardSceneColorsComponent).colors;
        if (!colors?.length) {
            console.warn('没有可用的颜色，使用默认颜色');
            return NormalColor7[MathUtil.randomInt(NormalColor7.length - 1)];
        }

        return colors[MathUtil.randomInt(colors.length - 1)];
    }
}
