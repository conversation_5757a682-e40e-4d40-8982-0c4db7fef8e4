import { ECSRuleBase, IECSRuleConfig } from '../core/ECSRuleBase';
import { ECSEvent } from '../../GameEvent';
import { IProduceBlockEnd, IBoardData } from '../../GameEventData';
import { AlgoClassTypeConst } from '../../../../algorithm/list/algoSDK/core/puzzlecore';
import BoardComponent from '../../components/board/BoardComponent';
import { PuzzleUtil } from '../../../../algorithm/list/algoSDK/PuzzleUtil';
import { algorithmSDKArgsInfo } from '../../../../algorithm/vo/AlgorithmSDKArgsInfo';

export interface IRevivePreviewBlocksRuleConfig extends IECSRuleConfig {
    /**算法ID */
    algorithmId: number;
    /**是否需要加载完 */
    needLoad?: boolean;
    /**满足？收集进度弹出复活 */
    percent?: number;
}

/**
 * 复活预览块规则
 * 在复活弹窗显示前，先计算复活块并缓存结果
 */
export class RevivePreviewBlocksRule extends ECSRuleBase<IRevivePreviewBlocksRuleConfig> {

    canExecute(): boolean {
        // 使用工具类进行统一的复活条件检查
        if (!this.world.utilCenter.reviveUtil.canRevive(this.config)) {
            return false;
        }

        // 避免重复计算：如果已有预览数据则不执行
        return !this.world.utilCenter.reviveUtil.hasPreviewData();
    }

    async execute(): Promise<void> {
        const world = this.world;

        try {
            // 标记这是复活预览出块
            const reviveComp = world.utilCenter.reviveUtil.getReviveComponent();
            if (!reviveComp) {
                console.error('RevivePreviewBlocksRule: 找不到ReviveComponent');
                return;
            }
            reviveComp.isPreviewMode = true;

            console.log('RevivePreviewBlocksRule: 开始预览复活块计算');

            // 直接调用算法计算复活块
            const result = await this.calculateReviveBlocks();

            // 缓存结果
            reviveComp.cachedReviveBlocks = result;
            reviveComp.isPreviewMode = false;

            // 触发预览完成事件
            world.eventBus.emit(ECSEvent.GameEvent.REVIVE_PREVIEW_COMPLETE, result);

            console.log('RevivePreviewBlocksRule: 复活块预览完成', result.blockIds);

        } catch (error) {
            console.error('RevivePreviewBlocksRule: 执行失败', error);

            // 出错时重置预览模式
            const reviveComp = world.utilCenter.reviveUtil.getReviveComponent();
            if (reviveComp) {
                reviveComp.isPreviewMode = false;
            }
        }
    }

    /**
     * 直接调用算法计算复活块
     * 复用 BlockAlgorithmSystem 的逻辑
     */
    private async calculateReviveBlocks(): Promise<IProduceBlockEnd> {
        const world = this.world;

        // 获取棋盘组件
        const boardEntity = world.query([BoardComponent]).filter((entity: number) => {
            const boardComp = world.getComponent(entity, BoardComponent);
            return boardComp.isCanPut === true;
        })[0];

        if (!boardEntity) {
            throw new Error('RevivePreviewBlocksRule: 未找到可用棋盘');
        }

        const boardComp = world.getComponent(boardEntity, BoardComponent);

        // 获取合并的棋盘占用数据
        const combinedBoardOccupy = world.utilCenter.boardUtil.combineBoardOccupy(
            world.query([BoardComponent]).map((entity: number) => {
                return world.utilCenter.boardUtil.getBoardOccupy(entity);
            })
        );

        // 准备棋盘数据
        const boardData: IBoardData = {
            boardOccupy: combinedBoardOccupy,
            rows: boardComp.rCount,
            cols: boardComp.cCount,
            algorithmId: this.config.algorithmId || AlgoClassTypeConst.algo_revive_clear,
        };

        console.log('RevivePreviewBlocksRule: 调用算法SDK', boardData.algorithmId, boardData);

        // 准备算法参数
        const args = algorithmSDKArgsInfo.getArgs(boardData.boardOccupy);

        // 调用算法SDK
        const algorithmResult = await PuzzleUtil.offerTopic(args, [boardData.algorithmId]);

        // IProduceBlockEnd 就是 AlgoExecuteAdapterOutputData 的别名，直接返回
        return algorithmResult;
    }
}
