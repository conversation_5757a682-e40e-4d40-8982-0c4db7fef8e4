<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ECS架构图表查看器</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .chart-container {
            margin: 20px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: white;
            position: relative;
        }

        .chart-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }

        .chart-content {
            text-align: center;
            overflow: hidden;
            max-height: 80vh;
            padding: 20px;
            position: relative;
        }

        .controls {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 1000;
        }

        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 8px 12px;
            margin: 2px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }

        .btn:hover {
            background: #5a67d8;
        }

        .zoom-controls {
            background: rgba(255, 255, 255, 0.9);
            padding: 10px;
            border-radius: 6px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .mermaid {
            transform-origin: top left;
            transition: transform 0.3s ease;
            display: inline-block;
            min-width: 100%;
            margin: 20px;
        }

        .mermaid svg {
            max-width: none !important;
            height: auto !important;
        }

        .chart-wrapper {
            width: 100%;
            height: 70vh;
            overflow: auto;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            background: #fafafa;
            position: relative;
            /* 确保滚动条可见 */
            scrollbar-width: thin;
            scrollbar-color: #888 #f1f1f1;
        }

        .chart-wrapper::-webkit-scrollbar {
            width: 12px;
            height: 12px;
        }

        .chart-wrapper::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 6px;
        }

        .chart-wrapper::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 6px;
        }

        .chart-wrapper::-webkit-scrollbar-thumb:hover {
            background: #555;
        }

        .chart-wrapper.dragging {
            cursor: grabbing !important;
            user-select: none;
        }

        .chart-wrapper:not(.dragging) {
            cursor: grab;
        }

        .zoom-info {
            position: absolute;
            bottom: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 1000;
        }

        .navigation {
            background: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #e0e0e0;
        }

        .nav-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 8px 16px;
            margin: 0 5px;
            border-radius: 4px;
            cursor: pointer;
        }

        .nav-btn.active {
            background: #667eea;
        }

        .nav-btn:hover {
            opacity: 0.8;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>🎮 Block Blast ECS 架构图表</h1>
            <p>可缩放、可导出的交互式图表查看器</p>
        </div>

        <div class="navigation">
            <button class="nav-btn active" onclick="showChart(0)">1. 核心架构</button>
            <button class="nav-btn" onclick="showChart(1)">2. 数据流程</button>
            <button class="nav-btn" onclick="showChart(2)">3. 存储结构</button>
            <button class="nav-btn" onclick="showChart(3)">4. 查询流程</button>
            <button class="nav-btn" onclick="showChart(4)">5. 事件系统</button>
            <button class="nav-btn" onclick="showChart(5)">6. 渲染同步</button>
            <button class="nav-btn" onclick="showChart(6)">7. 消除流程</button>
        </div>

        <div id="chart-display">
            <!-- 图表将在这里显示 -->
        </div>
    </div>

    <script>
        // 初始化Mermaid
        mermaid.initialize({
            startOnLoad: false,
            theme: 'default',
            themeVariables: {
                primaryColor: '#667eea',
                primaryTextColor: '#333',
                primaryBorderColor: '#667eea',
                lineColor: '#666',
                secondaryColor: '#f8f9fa',
                tertiaryColor: '#e9ecef'
            }
        });

        // 图表数据
        const charts = [
            {
                title: "1. ECS核心架构图",
                description: "展示World、Entity、Component、System的关系以及各个子系统",
                code: `graph TB
    subgraph "ECS核心架构"
        World[World 世界管理器]
        Entity[Entity 实体<br/>只是数字ID]
        Component[Component 组件<br/>纯数据容器]
        System[System 系统<br/>逻辑处理器]
        
        World --> Entity
        World --> Component
        World --> System
        Entity -.-> Component
        System --> Entity
        System --> Component
    end
    
    subgraph "游戏组件"
        NodeComp[NodeComponent<br/>位置/旋转/缩放]
        BoardComp[Board<br/>棋盘数据]
        CellComp[Cell<br/>方块数据]
        DestroyComp[DestroyTag<br/>销毁标记]
        RenderComp[RenderInfo<br/>渲染信息]
        
        Component --> NodeComp
        Component --> BoardComp
        Component --> CellComp
        Component --> DestroyComp
        Component --> RenderComp
    end
    
    subgraph "游戏系统"
        EliminationSys[EliminationSystem<br/>消除系统]
        DestroySys[DestroySystem<br/>销毁系统]
        InteractionSys[InteractionSystem<br/>交互系统]
        CollectSys[CollectSystem<br/>收集系统]
        
        System --> EliminationSys
        System --> DestroySys
        System --> InteractionSys
        System --> CollectSys
    end
    
    subgraph "渲染世界"
        RenderWorld[RenderWorld<br/>渲染管理器]
        ECSView[ECSView<br/>渲染视图]
        RenderCmd[RenderCmd<br/>渲染命令]
        
        RenderWorld --> ECSView
        RenderWorld --> RenderCmd
        World -.-> RenderWorld
    end
    
    subgraph "事件系统"
        EventBus[EventBus<br/>事件总线]
        GameEvent[GameEvent<br/>游戏事件]
        
        World --> EventBus
        EventBus --> GameEvent
        System -.-> EventBus
    end
    
    subgraph "配置管理"
        ConfigManager[ConfigManager<br/>配置管理器]
        GameConfig[GameConfig<br/>游戏配置]
        Registry[Registry<br/>注册表]
        
        ConfigManager --> GameConfig
        ConfigManager --> Registry
        World --> ConfigManager
    end
    
    style World fill:#e1f5fe
    style RenderWorld fill:#f3e5f5
    style EventBus fill:#e8f5e8
    style ConfigManager fill:#fff3e0`
            },
            {
                title: "2. ECS数据流和执行流程",
                description: "展示游戏循环中各组件的交互时序",
                code: `sequenceDiagram
    participant Game as Game
    participant World as World
    participant System as System
    participant EventBus as EventBus
    participant RenderWorld as RenderWorld
    
    Note over Game: 游戏初始化
    Game->>World: 创建World实例
    Game->>RenderWorld: 创建RenderWorld实例
    Game->>World: 注册Systems
    Game->>World: 执行初始化Commands
    
    Note over Game: 游戏循环开始
    loop 每帧更新
        Game->>World: update(dt)
        
        Note over World: 系统更新循环
        loop 遍历所有系统
            World->>System: update(dt)
            System->>World: query(components)
            World-->>System: 返回实体列表
            System->>World: 修改组件数据
            System->>EventBus: emit(event, data)
        end
        
        Note over EventBus: 事件处理
        EventBus->>System: 触发事件处理器
        System->>World: 响应事件，修改数据
        
        Game->>RenderWorld: update(dt)
        
        Note over RenderWorld: 渲染更新
        RenderWorld->>World: 获取NodeComponent数据
        World-->>RenderWorld: 返回变换数据
        RenderWorld->>RenderWorld: 同步到渲染节点
        RenderWorld->>RenderWorld: 执行渲染命令
    end`
            },
            {
                title: "3. 组件存储结构图",
                description: "展示组件在内存中的嵌套Map存储结构",
                code: `graph LR
    subgraph "World.components"
        ComponentMap[Map&lt;string, Map&lt;number, Component&gt;&gt;]
    end
    
    subgraph "组件类型层"
        NodeCompMap["'NodeComponent'<br/>Map&lt;number, NodeComponent&gt;"]
        CellCompMap["'Cell'<br/>Map&lt;number, Cell&gt;"]
        BoardCompMap["'Board'<br/>Map&lt;number, Board&gt;"]
    end
    
    subgraph "实体层"
        Entity1["Entity 1<br/>NodeComponent实例"]
        Entity2["Entity 2<br/>NodeComponent实例"]
        Entity3["Entity 3<br/>Cell实例"]
        Entity4["Entity 4<br/>Cell实例"]
        Entity5["Entity 5<br/>Board实例"]
    end
    
    ComponentMap --> NodeCompMap
    ComponentMap --> CellCompMap
    ComponentMap --> BoardCompMap
    
    NodeCompMap --> Entity1
    NodeCompMap --> Entity2
    CellCompMap --> Entity3
    CellCompMap --> Entity4
    BoardCompMap --> Entity5`
            },
            {
                title: "4. 系统查询流程图",
                description: "展示ECS查询算法的执行步骤",
                code: `flowchart TD
    Start([系统需要查询实体]) --> Input[输入: 组件类型数组]
    Input --> Loop{遍历所有实体}
    Loop --> CheckEntity[检查实体是否拥有所有指定组件]
    CheckEntity --> HasAll{拥有所有组件?}
    HasAll -->|是| AddToResult[添加到结果列表]
    HasAll -->|否| NextEntity[检查下一个实体]
    AddToResult --> NextEntity
    NextEntity --> Loop
    Loop -->|遍历完成| Return[返回实体列表]
    Return --> End([结束])
    
    style Start fill:#e8f5e8
    style End fill:#ffebee
    style HasAll fill:#fff3e0`
            },
            {
                title: "5. 事件系统流程图",
                description: "展示事件的发送、处理和错误处理机制",
                code: `flowchart TD
    subgraph Sender ["事件发送方"]
        A[System/Component] --> B[emit事件]
    end

    subgraph Bus ["EventBus"]
        B --> C[事件总线]
        C --> D[查找事件处理器]
        D --> E[执行处理器]
    end

    subgraph Receivers ["事件接收方"]
        E --> F[处理器1]
        E --> G[处理器2]
        E --> H[处理器N...]

        F --> I[执行相应逻辑1]
        G --> J[执行相应逻辑2]
        H --> K[执行相应逻辑N]
    end

    subgraph ErrorHandling ["错误处理"]
        E --> L{处理器执行出错?}
        L -->|是| M[记录错误日志]
        L -->|否| N[继续执行下一个处理器]
        M --> N
    end

    style A fill:#e8f5e8
    style C fill:#fff3e0
    style L fill:#ffebee`
            },
            {
                title: "6. 渲染世界同步机制",
                description: "展示逻辑世界与渲染世界的数据同步过程",
                code: `sequenceDiagram
    participant Logic as 逻辑世界
    participant RenderWorld as 渲染世界
    participant Node as 渲染节点
    
    Note over Logic,Node: 每帧同步过程
    
    RenderWorld->>Logic: 获取所有ECSView对应的实体
    
    loop 遍历每个渲染实体
        RenderWorld->>Logic: getComponent(entityId, NodeComponent)
        Logic-->>RenderWorld: 返回NodeComponent数据
        
        RenderWorld->>RenderWorld: 检查数据是否变化
        
        alt 位置发生变化
            RenderWorld->>Node: setPosition(x, y)
        end
        
        alt 缩放发生变化
            RenderWorld->>Node: setScale(scaleX, scaleY)
        end
        
        alt 旋转发生变化
            RenderWorld->>Node: 设置angle
        end
        
        alt 层级发生变化
            RenderWorld->>Node: 设置zIndex
        end
    end`
            },
            {
                title: "7. 消除系统工作流程",
                description: "展示Block Blast游戏中方块消除的完整流程",
                code: `flowchart TD
    Start([玩家放置方块]) --> Event[触发PUT_BLOCK_BACK事件]
    Event --> EliminationSys[EliminationSystem响应]
    EliminationSys --> GetBoard[获取棋盘组件]
    GetBoard --> CheckLines[检查满行满列]
    CheckLines --> HasClear{有可消除的行列?}
    
    HasClear -->|否| End([结束])
    HasClear -->|是| MarkDestroy[标记要销毁的方块]
    MarkDestroy --> CreateEffect[创建消除特效]
    CreateEffect --> EmitCollect[发送COLLECT_INFO事件]
    EmitCollect --> UpdateScore[更新分数]
    UpdateScore --> NextFrame[下一帧]
    NextFrame --> DestroySystem[DestroySystem处理销毁]
    DestroySystem --> RemoveEntities[移除实体]
    RemoveEntities --> End
    
    style Start fill:#e8f5e8
    style End fill:#ffebee
    style HasClear fill:#fff3e0`
            }
        ];

        let currentChart = 0;
        let currentZoom = 1;

        function showChart(index) {
            currentChart = index;
            currentZoom = 1;

            // 更新导航按钮状态
            document.querySelectorAll('.nav-btn').forEach((btn, i) => {
                btn.classList.toggle('active', i === index);
            });

            const chart = charts[index];
            const container = document.getElementById('chart-display');

            container.innerHTML = `
                <div class="chart-container">
                    <div class="controls">
                        <div class="zoom-controls">
                            <button class="btn" onclick="zoomIn()">🔍 放大</button>
                            <button class="btn" onclick="zoomOut()">🔍 缩小</button>
                            <button class="btn" onclick="resetZoom()">↻ 重置</button>
                            <button class="btn" onclick="fitToScreen()">📐 适应屏幕</button>
                            <button class="btn" onclick="centerChart()">🎯 居中</button>
                            <button class="btn" onclick="downloadChart()">💾 下载</button>
                        </div>
                    </div>
                    <div class="chart-title">${chart.title}</div>
                    <p style="color: #666; margin-bottom: 20px;">${chart.description}</p>
                    <div class="chart-content">
                        <div class="chart-wrapper" id="chart-wrapper-${index}">
                            <div class="mermaid" id="mermaid-${index}">${chart.code}</div>
                        </div>
                        <div class="zoom-info" id="zoom-info">缩放: 100%</div>
                    </div>
                </div>
            `;

            // 渲染Mermaid图表
            mermaid.init(undefined, `#mermaid-${index}`);

            // 添加拖拽功能
            setupDragToScroll();
        }

        function zoomIn() {
            currentZoom = Math.min(currentZoom * 1.2, 5);
            applyZoom();
        }

        function zoomOut() {
            currentZoom = Math.max(currentZoom / 1.2, 0.3);
            applyZoom();
        }

        function resetZoom() {
            currentZoom = 1;
            applyZoom();
        }

        function fitToScreen() {
            const wrapper = document.querySelector('.chart-wrapper');
            const mermaidElement = document.querySelector('.mermaid');
            if (wrapper && mermaidElement) {
                // 重置缩放以获取原始尺寸
                mermaidElement.style.transform = 'scale(1)';

                // 获取容器和图表的尺寸
                const wrapperRect = wrapper.getBoundingClientRect();
                const svg = mermaidElement.querySelector('svg');
                if (svg) {
                    const svgRect = svg.getBoundingClientRect();

                    // 计算适合的缩放比例，留出边距
                    const scaleX = (wrapperRect.width - 80) / svgRect.width;
                    const scaleY = (wrapperRect.height - 80) / svgRect.height;
                    currentZoom = Math.min(scaleX, scaleY, 1); // 不超过100%

                    applyZoom();
                    centerChart();
                }
            }
        }

        function centerChart() {
            const wrapper = document.querySelector('.chart-wrapper');
            const mermaidElement = document.querySelector('.mermaid');
            if (wrapper && mermaidElement) {
                // 计算居中位置
                const wrapperRect = wrapper.getBoundingClientRect();
                const mermaidRect = mermaidElement.getBoundingClientRect();

                const centerX = Math.max(0, (mermaidRect.width * currentZoom - wrapperRect.width) / 2);
                const centerY = Math.max(0, (mermaidRect.height * currentZoom - wrapperRect.height) / 2);

                wrapper.scrollLeft = centerX;
                wrapper.scrollTop = centerY;
            }
        }

        function applyZoom() {
            const mermaidElement = document.querySelector('.mermaid');
            const zoomInfo = document.getElementById('zoom-info');

            if (mermaidElement) {
                mermaidElement.style.transform = `scale(${currentZoom})`;

                // 更新缩放信息显示
                if (zoomInfo) {
                    zoomInfo.textContent = `缩放: ${Math.round(currentZoom * 100)}%`;
                }

                // 调整容器内容大小以适应缩放后的图表
                const wrapper = document.querySelector('.chart-wrapper');
                if (wrapper) {
                    // 计算缩放后的实际尺寸
                    const svg = mermaidElement.querySelector('svg');
                    if (svg) {
                        const rect = svg.getBoundingClientRect();
                        const scaledWidth = rect.width * currentZoom;
                        const scaledHeight = rect.height * currentZoom;

                        // 设置容器的滚动区域
                        mermaidElement.style.width = scaledWidth + 'px';
                        mermaidElement.style.height = scaledHeight + 'px';
                    }
                }
            }
        }

        function downloadChart() {
            const svg = document.querySelector('.mermaid svg');
            if (svg) {
                // 克隆SVG以避免修改原始图表
                const clonedSvg = svg.cloneNode(true);

                // 设置SVG的背景色
                clonedSvg.style.backgroundColor = 'white';

                const svgData = new XMLSerializer().serializeToString(clonedSvg);
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const img = new Image();

                img.onload = function () {
                    // 设置高分辨率
                    const scale = 2;
                    canvas.width = img.width * scale;
                    canvas.height = img.height * scale;
                    ctx.scale(scale, scale);

                    // 白色背景
                    ctx.fillStyle = 'white';
                    ctx.fillRect(0, 0, img.width, img.height);
                    ctx.drawImage(img, 0, 0);

                    const link = document.createElement('a');
                    link.download = `ECS_图表_${currentChart + 1}_${charts[currentChart].title.replace(/[^\w\s]/gi, '')}.png`;
                    link.href = canvas.toDataURL('image/png', 1.0);
                    link.click();
                };

                img.src = 'data:image/svg+xml;base64,' + btoa(unescape(encodeURIComponent(svgData)));
            }
        }

        // 添加键盘快捷键支持
        document.addEventListener('keydown', function (e) {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case '=':
                    case '+':
                        e.preventDefault();
                        zoomIn();
                        break;
                    case '-':
                        e.preventDefault();
                        zoomOut();
                        break;
                    case '0':
                        e.preventDefault();
                        resetZoom();
                        break;
                    case 's':
                        e.preventDefault();
                        downloadChart();
                        break;
                }
            }

            // 数字键切换图表
            if (e.key >= '1' && e.key <= '7') {
                const index = parseInt(e.key) - 1;
                if (index < charts.length) {
                    showChart(index);
                }
            }
        });

        // 设置拖拽滚动功能
        function setupDragToScroll() {
            const wrapper = document.querySelector('.chart-wrapper');
            if (!wrapper) return;

            let isDown = false;
            let startX;
            let startY;
            let scrollLeft;
            let scrollTop;

            wrapper.addEventListener('mousedown', (e) => {
                isDown = true;
                wrapper.classList.add('dragging');
                startX = e.pageX - wrapper.offsetLeft;
                startY = e.pageY - wrapper.offsetTop;
                scrollLeft = wrapper.scrollLeft;
                scrollTop = wrapper.scrollTop;
            });

            wrapper.addEventListener('mouseleave', () => {
                isDown = false;
                wrapper.classList.remove('dragging');
            });

            wrapper.addEventListener('mouseup', () => {
                isDown = false;
                wrapper.classList.remove('dragging');
            });

            wrapper.addEventListener('mousemove', (e) => {
                if (!isDown) return;
                e.preventDefault();
                const x = e.pageX - wrapper.offsetLeft;
                const y = e.pageY - wrapper.offsetTop;
                const walkX = (x - startX) * 2;
                const walkY = (y - startY) * 2;
                wrapper.scrollLeft = scrollLeft - walkX;
                wrapper.scrollTop = scrollTop - walkY;
            });
        }

        // 初始化显示第一个图表
        showChart(0);
    </script>
</body>

</html>