const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const args = process.argv;
let serverUrl = args[2];
let platform = args[3];
let ENV = args[4];
let version = args[5];
let hotUpdateZip = args[6];
let SRC = args[7];

/**
 * 为了兼容 gradle 打包，放在 src 目录下，否则放在根目录下无法打到 apk 里
 */
const manifest = {
    url1: `http://**************:8867/hotUpdate${platform}/${ENV}/${version}`,
    url2: `http://**************:8867/hotUpdate${platform}/${ENV}/${version}/src/project.manifest`,
    url3: `http://**************:8867/hotUpdate${platform}/${ENV}/${version}/src/version.manifest`,
    version: '1.0.0',
    assets: {},
    searchPaths: [],
};

const versionConfig = {
    version: '1.0.0'
};


if (!version) {
    version = manifest.version;
}

if (!serverUrl) {
    serverUrl = manifest.url1;
}

let src = `${SRC}/`;
let dest = './remote-assets/';

console.log('命令参数:', process.argv);

let url;
// 注意：正式服，没有 ${platform}/${ENV} 地址
if (ENV === 'prod') {
    url = serverUrl + `/hotUpdate`;
} else {
    url = serverUrl + `/hotUpdate/${platform}/${ENV}`;
}

manifest.url1 = url + "/" + version;
manifest.url2 = url + "/" + version + '/src/project.manifest';
manifest.url3 = url + "/" + version + '/src/version.manifest';
manifest.version = version;
src = `${SRC}/`;
dest = `./assets/`;

//单个文件 进入 manifest 中
function readFile(filepath, obj) {
    var stat = fs.statSync(filepath);
    if (!stat.isFile()) {
        return;
    }
    let size, md5, compressed, relative;
    // Size in Bytes
    size = stat['size'];
    md5 = crypto.createHash('md5').update(fs.readFileSync(filepath)).digest('hex');
    compressed = path.extname(filepath).toLowerCase() === '.zip';

    relative = path.relative(src, filepath);
    relative = relative.replace(/\\/g, '/');
    relative = encodeURI(relative);
    obj[relative] = {
        size: size,
        md5: md5,
    };
    if (compressed) {
        obj[relative].compressed = true;
    }
}

/** 目录下所有文件 进入manifest中 */
//TODO  排除掉特性 bundle 资源
function readDir(dir, obj) {
    let stat = fs.statSync(dir);
    if (!stat.isDirectory()) {
        return;
    }
    let subpaths = fs.readdirSync(dir),
        subpath,
        size,
        md5,
        compressed,
        relative;
    for (let i = 0; i < subpaths.length; ++i) {
        if (subpaths[i][0] === '.') {
            continue;
        }
        subpath = path.join(dir, subpaths[i]);
        stat = fs.statSync(subpath);
        if (stat.isDirectory()) {
            readDir(subpath, obj);
        } else if (stat.isFile()) {
            // Size in Bytes
            size = stat['size'];
            md5 = crypto.createHash('md5').update(fs.readFileSync(subpath)).digest('hex');
            compressed = path.extname(subpath).toLowerCase() === '.zip';

            relative = path.relative(src, subpath);
            relative = relative.replace(/\\/g, '/');
            relative = encodeURI(relative);
            obj[relative] = {
                size: size,
                md5: md5,
            };
            if (compressed) {
                obj[relative].compressed = true;
            }
        }
    }
}

const mkdirSync = function (path) {
    try {
        fs.mkdirSync(path);
    } catch (e) {
        if (e.code != 'EEXIST') throw e;
    }
};

//只读取src、assets
readDir(path.join(src, 'src'), manifest.assets);
readDir(path.join(src, 'assets'), manifest.assets);
readFile(path.join(src, 'main.js'), manifest.assets);

const destManifest = path.join(src, 'src/project.manifest');
const destVersion = path.join(src, 'src/version.manifest');
const destVersionConfig = path.join(src, 'version.config');

if (!fs.existsSync(path.dirname(destManifest))) {
    fs.mkdirSync(path.dirname(destManifest));
}

mkdirSync(dest);

versionConfig.version = version;
// 采用 zip 形式热更
if (hotUpdateZip) {
    versionConfig.zip = true;
}

//写入version.config 文件
fs.writeFile(destVersionConfig, JSON.stringify(versionConfig), (err) => {
    if (err) throw err;
    console.log('versionConfig successfully generated => ', destVersionConfig);
});

//写入project.manifest 文件
fs.writeFile(destManifest, JSON.stringify(manifest), (err) => {
    if (err) throw err;
    console.log('Manifest successfully generated => ', destManifest);
});

//删除资料列表和搜索路径，再写入version.manifest文件
delete manifest.assets;
delete manifest.searchPaths;
fs.writeFile(destVersion, JSON.stringify(manifest), (err) => {
    if (err) throw err;
    console.log('Version successfully generated => ', destVersion);
});
