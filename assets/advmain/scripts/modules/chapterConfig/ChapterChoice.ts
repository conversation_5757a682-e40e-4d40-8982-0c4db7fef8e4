import { SubGameBridge } from '../../base/SubGameBridge';
import { PrefabConfig } from '../prefab/PrefabConfig';
import { ChapterUrls } from './ChapterDefineds';
import { E_ChapterConfig_Load } from './events/E_ChapterConfig_Load';
import { chapterConfigInfo } from './vo/ChapterConfigInfo';

const { ccclass } = cc._decorator;

@ccclass
export default class ChapterChoice {
    static enter() {
        this.enterMosaic();
    }

    private static async enterMosaic() {
        // 加载配置
        await falcon.EventManager.dispatchModuleEventAsync(new E_ChapterConfig_Load());

        if (chapterConfigInfo.chapterListCfg.length) {
            await this.initRes();
            falcon.UI.show(PrefabConfig.ChapterMain);
        } else {
            if (CC_DEBUG) {
                console.log('关卡配置加载失败');
            }
        }
    }

    private static async initRes() {
        const resources = [
            { bundle: 'chapter', url: PrefabConfig.ChapterContent.url },
            { bundle: 'chapter', url: PrefabConfig.ChapterItem.url },
            { bundle: 'chapter', url: ChapterUrls.curBonePath },
            { bundle: 'chapter', url: ChapterUrls.commonBgPath, type: cc.SpriteFrame },
            { bundle: 'chapter', url: ChapterUrls.itemPath, type: cc.SpriteFrame },
            { bundle: 'chapter', url: ChapterUrls.cupBonePath },
        ];

        return new Promise<void>((resolve) => {
            let index = 0;

            const loadNextBatch = () => {
                // 每帧加载2个资源
                const batchSize = 2;
                const batch = resources.slice(index, index + batchSize);
                index += batchSize;

                const promises = batch.map((res) => {
                    if (res.type) {
                        return SubGameBridge.asyncLoadByBundle(res.bundle, res.url, res.type);
                    }
                    return SubGameBridge.asyncLoadByBundle(res.bundle, res.url);
                });

                Promise.all(promises)
                    .then(async () => {
                        if (index < resources.length) {
                            // 等待下一帧继续加载
                            await falcon.timer.nextFrame();
                            loadNextBatch();
                        } else {
                            // 所有资源加载完成
                            resolve();
                        }
                    })
                    .catch((err) => {
                        if (CC_DEBUG) {
                            console.log('加载资源出错:', err);
                        }
                        resolve(); // 即使出错也继续
                    });
            };

            // 开始加载第一批
            loadNextBatch();
        });
    }
}
