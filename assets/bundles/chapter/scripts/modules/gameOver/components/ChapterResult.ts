import { Loader } from '../../../../../../advmain/scripts/base/Loader';
import { SubGameBridge } from '../../../../../../advmain/scripts/base/SubGameBridge';
import Util from '../../../../../../advmain/scripts/base/Util';
import { AudioConfig } from '../../../../../../advmain/scripts/modules/audio/config/AudioConfig';
import { ChapterWinBtnPath, TARGET_ITEM_PF_SRC } from '../../../../../../advmain/scripts/modules/chapterConfig/ChapterDefineds';
import { PrefabConfig } from '../../../../../../advmain/scripts/modules/prefab/PrefabConfig';
import { ChapterAudioConfig } from '../../audio/config/ChapterAudioConfig';
import { E_ChapterList_Show } from '../../chapterList/events/E_ChapterList_Show';

interface IChapterResult {
    win: boolean;
    targets: { key: number; to: number; current: number }[];
}
const { ccclass } = cc._decorator;
/**
 * 关卡收集胜利界面
 */
@classId('ChapterResult')
@ccclass
export default class ChapterResult extends falcon.Component<Partial<IChapterResult>> {
    private ui: { [k: string]: cc.Node };

    async render() {
        this.ui = Util.getNodes(this.node);
        this.ui.playBtn.getComponent(cc.Sprite).spriteFrame = await Loader.asyncLoadByBundle<cc.SpriteFrame>(
            'chapter',
            this.state.win ? ChapterWinBtnPath.next : ChapterWinBtnPath.retry,
            cc.SpriteFrame,
        );
        this.resetUI();
        this.showAction();
    }

    resetUI() {
        this.node.children.forEach((n) => (n.active = false));
        this.ui.targets.destroyAllChildren();
        this.ui.targets.active = true;
    }

    /**播放动作 */
    showAction(): void {
        const aniNodeName = this.state.win ? 'winAni' : 'failAni';
        this.ui[aniNodeName].active = true;
        this.ui[aniNodeName]
            .getComponent(dragonBones.ArmatureDisplay)
            .playAnimation(`newAnimation${!this.state.win && Math.random() > 0.5 ? '_1' : ''}`, 1);
        // 目标面板弹出音效
        falcon.audioInfo.play(ChapterAudioConfig.travel_win_logo);

        cc.tween(this.node)
            .delay(0.57)
            .call(() => {
                this.ui.collectImg.active = true;
                this.ui.collectImg.opacity = 0;
                cc.tween(this.ui.collectImg).to(0.33, { opacity: 255 }).start();
            })
            .delay(0.33)
            .call(() => {
                // 目标面板弹出音效
                falcon.audioInfo.play(ChapterAudioConfig.travel_overui_collect_items);
                this.addItem();
            })
            .delay(1.1)
            .call(() => {
                this.showBtnAnim();
            })
            .start();
    }

    showBtnAnim() {
        this.ui.backBtn.active = true;
        this.ui.backBtn.scale = 0.6;
        this.ui.backBtn.getComponent(cc.Button).interactable = false;
        this.ui.playBtn.active = true;
        this.ui.playBtn.scale = 0.6;
        this.ui.playBtn.getComponent(cc.Button).interactable = false;
        // 按钮出现音效
        falcon.audioInfo.play(AudioConfig.s_btnShow);
        cc.tween(this.ui.backBtn)
            .to(0.13, { scale: 1.1 })
            .to(0.07, { scale: 1 })
            .call(() => (this.ui.backBtn.getComponent(cc.Button).interactable = true))
            .start();

        cc.tween(this.ui.playBtn)
            .to(0.13, { scale: 1.1 })
            .to(0.07, { scale: 1 })
            .call(() => (this.ui.playBtn.getComponent(cc.Button).interactable = true))
            .start();
    }

    /**添加目标item */
    async addItem() {
        const targets = this.state.targets;
        let haveScore = false;
        const len = targets.length;
        for (let i = 0; i < len; i++) {
            const t = targets[i];
            const pf: cc.Prefab = await Loader.asyncLoadByBundle(
                'chapter',
                TARGET_ITEM_PF_SRC[t.key] || 'prefabs/gameOver/ChapterCollectItem',
                cc.Prefab,
            );
            const node = cc.instantiate(pf);
            node.setParent(this.ui.targets);

            const nodes = Util.getNodes(node);
            //临时写法
            if (t.key === 1) {
                haveScore = true;
                this.showScore(nodes, t);
            } else this.showCollect(nodes, t);
        }
        const lo = this.ui.targets.getComponent(cc.Layout);
        lo.spacingX = haveScore ? 10 : 200 / len;
        lo.updateLayout();
    }

    private showCollect(nodes: { [k: string]: cc.Node }, t: IChapterResult['targets'][0]) {
        nodes.finishAnim.active = false;
        const lbNode = nodes.numLab;
        lbNode.getComponent(cc.Label).string = String(t.to - t.current);
        // 播放动画效果
        if (t.current < t.to) {
            lbNode.scale = 0.25;
            cc.Tween.stopAllByTarget(lbNode);
            cc.tween(lbNode).to(0.1, { scale: 0.6 }).to(0.1, { scale: 0.5 }).start();
        } else {
            lbNode.active = false;
            nodes.finishAnim.active = true;
            nodes.finishAnim.getComponent(dragonBones.ArmatureDisplay).playAnimation('newAnimation', 1);
        }

        const gemImg = nodes.gemImg;
        Loader.loadByBundle('level', `cell/${t.key}/img/iconBig`, cc.SpriteFrame, (err, spf: cc.SpriteFrame) => {
            if (err) return;
            gemImg.getComponent(cc.Sprite).spriteFrame = spf;
        });
        gemImg.scale = 0.7;
        cc.tween(gemImg)
            .to(0.1, { scale: 1.15 * 0.7 })
            .to(0.1, { scale: 0.7 })
            .start();
        const lightAnim = nodes.lightAnim;
        lightAnim.active = true;
        lightAnim.angle += 90;
        lightAnim.opacity = 255;
        lightAnim.getComponent(dragonBones.ArmatureDisplay).playAnimation('HS_yellow', 1);
        cc.tween(lightAnim).delay(0.1).to(0.3, { opacity: 0 }).start();
    }

    private showScore(nodes: { [k: string]: cc.Node }, t: IChapterResult['targets'][0]) {
        nodes.lose.active = !(nodes.win.active = this.state.win);
        if (this.state.win) {
            nodes.score.getComponent(cc.Label).string = t.current.toString();
            nodes.win.getComponent(dragonBones.ArmatureDisplay).playAnimation(`newAnimation`, 1);
            falcon.audioInfo.play(ChapterAudioConfig.travel_overui_score_suc);
        } else {
            falcon.audioInfo.play(ChapterAudioConfig.travel_score_change);
            nodes.targetLab.getComponent(cc.Label).string = t.to.toString();
            const lb = nodes.curScoreLab.getComponent(cc.Label);
            const width = nodes.progressImg.width;
            const sp = nodes.progressImg.getComponent(cc.Sprite);
            cc.tween({ now: 0 })
                .to(
                    0.3,
                    { now: t.current },
                    {
                        progress: (s, e, c, r) => {
                            const now = s + (e - s) * r;
                            const per = Math.min(1, now / t.to);
                            sp.fillRange = per;
                            nodes.curScoreBg.x = width * per;
                            lb.string = parseInt(now) + '';
                        },
                    },
                )
                .start();
        }
    }

    /**点击开始 */
    @falcon.throttle(300)
    onClickPlay(event?: cc.Event.EventTouch, data?: any) {
        this.resetUI();
        Loader.showUI(PrefabConfig.LevelsRoot);
        //临时写法
        window['gameIns']._world.eventBus.emit('BaseEvent.GAME_RESTART');
    }

    /**点击返回 */
    @falcon.throttle(300)
    onClickBack(event?: cc.Event.EventTouch, data?: any) {
        this.resetUI();
        Loader.showUI(PrefabConfig.ChapterMain);
    }
}
