import BoardComponent from '../../../../advmain/scripts/modules/level/ecs/components/board/BoardComponent';
import { BoardScene } from '../../../../advmain/scripts/modules/level/ecs/components/board/BoardScene';
import ShapeComponent from '../../../../advmain/scripts/modules/level/ecs/components/board/ShapeComponent';
import { ECSEvent } from '../../../../advmain/scripts/modules/level/ecs/GameEvent';
import { ICheckRemainShapeRuleConfig } from '../../../../advmain/scripts/modules/level/ecs/rules/CheckRemainShapeRule';
import { ECSRuleBase } from '../../../../advmain/scripts/modules/level/ecs/rules/core/ECSRuleBase';

/** 检测待放置区是否还有可落子的形状，若无则派发 PRODUCE_BLOCK */
export class CheckDualRemainShapeRule extends ECSRuleBase<ICheckRemainShapeRuleConfig, ECSEvent.GameEvent.PUT_BLOCK_BACK> {
    bindEventData() {
        return ECSEvent.GameEvent.PUT_BLOCK_BACK;
    }
    canExecute(): boolean {
        const data = this.eventData;
        return data?.isSuccess;
    }
    execute(): void {
        const world = this.world;
        const sceneE = world.query([BoardScene])[0];
        if (!sceneE) return;
        const handler = world.utilCenter.boardUtil;
        const sceneComp = world.getComponent(sceneE, BoardScene);
        // 1. 取所有棋盘占用并集
        let boards = world.query([BoardComponent]).map((eid) => handler.getBoardOccupy(eid));
        const combinedBoardOccupy = handler.combineBoardOccupy(boards);
        if (combinedBoardOccupy.length === 0) return;
        if (sceneComp.waitPutCells.length === 0) {
            // 已经没有待放置块，系统会自动生成
            return;
        }
        const waitShapes = world.getComponent(sceneE, BoardScene).waitPutCells;
        const hasAny = waitShapes.some((shapeE) => {
            const sc = world.getComponent(shapeE, ShapeComponent);
            return sc ? handler.canPutShape(sc, combinedBoardOccupy) : false;
        });
        if (!hasAny) {
            world.eventBus.emit(ECSEvent.GameEvent.GAME_FAILED);
        }
    }
}
