import HPComponent from '../../../components/special/HPComponent';
import { ECSViewBase, IECSViewConfig } from '../../ECSViewBase';

export default class GlassECSView extends ECSViewBase<IECSViewConfig> {
    protected initView() {
        this.fresh();
    }
    protected addEvents() {
        this.logicWorld.onComponentChange(this.logicEntityId, HPComponent, this.onHpChange, this);
    }
    protected removeEvents() {
        this.logicWorld.offComponentChange(this.logicEntityId, HPComponent, this.onHpChange, this);
    }
    protected disposeView() {}
    private fresh() {
        this.renderWorld
            .loadRes('level', `cell/120/img/icon${this.logicWorld.getComponent(this.logicEntityId, HPComponent).hp}`, cc.SpriteFrame)
            .then((spf: cc.SpriteFrame) => {
                if (!cc.isValid(this.node)) return;
                this.ui.icon.getComponent(cc.Sprite).spriteFrame = spf;
            });
    }

    onHpChange(entityId: number, component: HPComponent, propertyName: string, oldValue: any, newValue: any) {
        if (newValue !== 1) return;
        const spineNode = this.ui.spine;
        spineNode.active = true;
        const spine = spineNode.getComponent(sp.Skeleton);
        spine.setAnimation(0, 'gameplay_spine_glass_1', false);
        spine.setCompleteListener(() => {
            this.ui.icon.active = true;
            spineNode.active = false;
        });
        this.fresh();
        this.ui.icon.active = false;
    }
}
