
import { NormalProjectileBehavior } from '../../behavior/projectile/NormalProjectileBehavior';
import { ProjectileBehaviorBase } from '../../behavior/projectile/ProjectileBehaviorBase';
import { MultipleProjectileBehavior } from '../../behavior/projectile/MultipleProjectileBehavior';

/**投射物行为配置表名 */
export const enum ProjectileBehaviorConst {
    /**普通投射物 */
    NormalProjectileBehavior = 'NormalProjectileBehavior',
    /**多段投射物 */
    MultipleProjectileBehavior = 'MultipleProjectileBehavior',
}

/**投射物路径行为配置注册表 */
export const ProjectileBehaviorRegistry: Record<string, clzz<ProjectileBehaviorBase>> = {
    [ProjectileBehaviorConst.NormalProjectileBehavior]: NormalProjectileBehavior,
    [ProjectileBehaviorConst.MultipleProjectileBehavior]: MultipleProjectileBehavior,
};
