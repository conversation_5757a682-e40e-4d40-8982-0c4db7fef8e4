import { SubGameBridge } from '../../../base/SubGameBridge';
import { ISetupBaseItem, SetupConfig } from '../config/LevelSetupConfig';
import { E_Levels_SetupClick } from '../events/E_Levels_SetupClick';

const { ccclass, property } = cc._decorator;
/**
 * 设置界面按钮列表部元素
 */
@ccclass
export class LevelSetupBtnItem extends falcon.Component<ISetupBaseItem> {
    /**按钮 */
    @property(cc.Button)
    btn: cc.Button = null;

    /**文本图 */
    @property(cc.Sprite)
    txt_img: cc.Sprite = null;

    /**icon图 */
    @property(cc.Sprite)
    icon_img: cc.Sprite = null;

    /**red */
    @property(cc.Node)
    redNode: cc.Node = null;

    shouldComponentUpdate(nextState: Readonly<ISetupBaseItem>): boolean {
        return this.state.value !== nextState.value;
    }

    render() {
        /**底部资源前缀*/
        const bottomBtnPre: string = this.state.bottomBtnPre || SetupConfig.bottomBtnPre;
        /**文本资源前缀*/
        const txtImgPre: string = this.state.txtImgPre || SetupConfig.txtImgPre;
        // 加载显示资源
        const data: { path: string; sp: cc.Sprite }[] = [
            { path: SetupConfig.resUrl + txtImgPre + this.state.key, sp: this.txt_img },
            { path: SetupConfig.resUrl + bottomBtnPre + this.state.key, sp: this.icon_img },
        ];

        for (let i = 0; i < data.length; i++) {
            SubGameBridge.loadByBundle('advres', data[i].path, cc.SpriteFrame, (err, sp) => {
                if (err) {
                    console.error(err);
                    return;
                }
                if (data[i].sp) {
                    data[i].sp.spriteFrame = sp as cc.SpriteFrame;
                }
                // 设置红点
                this.redNode.active = !!this.state.redPoint;
            });
        }
    }

    @falcon.throttle(300)
    onClick(event: cc.Event.EventTouch, data: any) {
        if (CC_DEBUG) {
            console.log(`点击按钮`);
        }
        falcon.EventManager.dispatchModuleEvent(new E_Levels_SetupClick(this.state.key));
    }
}
