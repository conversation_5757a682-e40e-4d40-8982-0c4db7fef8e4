import { BoardScene } from '../../components/board/BoardScene';
import BoardComponent from '../../components/board/BoardComponent';
import ReviveComponent from '../../components/ReviveComponent';
import { ECSExecuteContext, ECSRuleBase, IECSRuleConfig } from '../core/ECSRuleBase';
import { ECSEvent } from '../../GameEvent';
import { IBoardData } from '../../GameEventData';
import { AlgoExecuteAdapterOutputData } from '../../../../algorithm/list/algoSDK/puzzle/algoPuzzle';
import { PuzzleUtil } from '../../../../algorithm/list/algoSDK/PuzzleUtil';
import { algorithmSDKArgsInfo } from '../../../../algorithm/vo/AlgorithmSDKArgsInfo';
import { AlgoClassTypeConst } from '../../../../algorithm/list/algoSDK/core/puzzlecore';
import { TempleteType } from '../../registry/templete/TempleteRegistry';

export interface IRevivePreviewBlocksRuleConfig extends IECSRuleConfig {
    /**预览渲染命令配置id */
    previewRenderCmdId?: string;
}

/**
 * 复活预览块规则
 * 在复活弹窗显示前，先计算复活块并在棋盘上预览位置
 */
export class RevivePreviewBlocksRule extends ECSRuleBase<IRevivePreviewBlocksRuleConfig> {
    
    canExecute(ctx: ECSExecuteContext): boolean {
        // 检查复活条件
        const world = ctx.world;
        const sceneEntity = world.query([BoardScene])[0];
        if (!sceneEntity) return false;
        
        const reviveComp = world.getComponent(sceneEntity, ReviveComponent);
        if (!reviveComp || reviveComp.nCount <= 0) {
            return false;
        }
        
        // 检查是否已经有缓存的复活块数据，避免重复计算
        return !reviveComp.cachedReviveBlocks;
    }

    async execute(ctx: ECSExecuteContext): Promise<void> {
        const world = ctx.world;
        
        try {
            // 1. 准备算法SDK输入数据
            const boardData = this.prepareBoardData(world);
            
            // 2. 调用算法SDK计算复活块
            const algorithmResult = await this.callAlgorithmSDK(boardData);
            
            // 3. 验证算法结果
            if (!algorithmResult || !algorithmResult.blockIds || algorithmResult.blockIds.length === 0) {
                console.warn('RevivePreviewBlocksRule: 算法返回空结果');
                return;
            }
            
            // 4. 在棋盘上预览复活块位置
            this.showPreviewBlocks(world, algorithmResult);
            
            // 5. 缓存复活块数据到ReviveComponent
            this.storeReviveBlockData(world, algorithmResult);
            
            // 6. 触发预览完成事件
            world.eventBus.emit(ECSEvent.GameEvent.REVIVE_PREVIEW_COMPLETE, algorithmResult);
            
            console.log('RevivePreviewBlocksRule: 复活块预览完成', algorithmResult.blockIds);
            
        } catch (error) {
            console.error('RevivePreviewBlocksRule: 执行失败', error);
        }
    }
    
    private prepareBoardData(world: any): IBoardData {
        // 获取棋盘组件
        const boardEntity = world.query([BoardComponent]).filter((entity: number) => {
            const boardComp = world.getComponent(entity, BoardComponent);
            return boardComp.isCanPut === true;
        })[0];
        
        if (!boardEntity) {
            throw new Error('RevivePreviewBlocksRule: 未找到可用棋盘');
        }
        
        const boardComp = world.getComponent(boardEntity, BoardComponent);
        
        // 获取合并的棋盘占用数据
        const combinedBoardOccupy = world.handlerCenter.boardHandler.combineBoardOccupy(
            world.query([BoardComponent]).map((entity: number) => {
                return world.handlerCenter.boardHandler.getBoardOccupy(entity);
            })
        );
        
        return {
            boardOccupy: combinedBoardOccupy,
            rows: boardComp.rCount,
            cols: boardComp.cCount,
            algorithmId: AlgoClassTypeConst.algo_revive || AlgoClassTypeConst.algo_random_no_death, // 复活专用算法，如果没有则使用无死算法
        };
    }
    
    private async callAlgorithmSDK(boardData: IBoardData): Promise<AlgoExecuteAdapterOutputData> {
        // 复用现有的算法调用逻辑
        const args = algorithmSDKArgsInfo.getArgs(boardData.boardOccupy);
        
        // 设置算法ID
        const algorithmId = boardData.algorithmId || AlgoClassTypeConst.algo_random_no_death;
        
        console.log('RevivePreviewBlocksRule: 调用算法SDK', algorithmId, boardData);
        
        // 调用算法SDK
        const result = await PuzzleUtil.offerTopic(args, [algorithmId]);
        
        return result;
    }
    
    private showPreviewBlocks(world: any, result: AlgoExecuteAdapterOutputData): void {
        // 在棋盘上显示预览块的推荐位置
        console.log('RevivePreviewBlocksRule: 显示预览块', result.blockIds, result.blockPoses);
        
        // 这里可以创建预览实体来显示推荐位置
        // 具体的预览显示逻辑可以根据需要实现
        for (let i = 0; i < result.blockIds.length && i < result.blockPoses.length; i++) {
            const blockId = result.blockIds[i];
            const pos = result.blockPoses[i];
            
            if (blockId && pos && this.config.previewRenderCmdId) {
                try {
                    // 创建预览实体（可选，根据UI需求决定是否实现）
                    const previewEntity = world.templeteCenter.createTempleteEntity(
                        TempleteType.Render,
                        {
                            parentEntity: world.ECSLayerType.SceneLayerEntity,
                            ecsViewId: this.config.previewRenderCmdId,
                            nodeParam: {
                                blockId,
                                position: pos,
                                isRevivePreview: true
                            }
                        }
                    );
                    
                    console.log(`RevivePreviewBlocksRule: 创建预览实体 ${i}`, blockId, pos);
                } catch (error) {
                    console.warn('RevivePreviewBlocksRule: 创建预览实体失败', error);
                }
            }
        }
    }
    
    private storeReviveBlockData(world: any, result: AlgoExecuteAdapterOutputData): void {
        // 将复活块数据存储到ReviveComponent中
        const sceneEntity = world.query([BoardScene])[0];
        if (sceneEntity) {
            const reviveComp = world.getComponent(sceneEntity, ReviveComponent);
            if (reviveComp) {
                reviveComp.cachedReviveBlocks = result;
                console.log('RevivePreviewBlocksRule: 缓存复活块数据', result.blockIds);
            }
        }
    }
}
