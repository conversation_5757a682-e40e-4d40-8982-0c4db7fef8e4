import { dotInfo } from './modules/dot/vo/DotInfo';
import { gameInfo } from './modules/game/vo/GameInfo';
import { E_Launch_Start } from './modules/launch/events/E_Launch_Start';
import { ModuleList } from './modules/ModuleList';
import { NativeGame } from './modules/native/NativeGame';
import { initGameLayer } from './modules/layer/vo/LayerInfo';
import { onNativeReponse } from './modules/native/NativeReceivedNative';

const { ccclass, property } = cc._decorator;

@ccclass
export default class Main extends cc.Component {
    @falcon.ScreenAdapter()
    onLoad() {
        console.log('main onload');
        cc.debug.setDisplayStats(MACRO_SHOW_FPS);

        //屏蔽多点
        cc.macro.ENABLE_MULTI_TOUCH = false;

        requestAnimationFrame(() => {
            NativeGame.callNativeHideLoading();
        });

        if (CC_BUILD && !CC_DEBUG) {
            console.log = function () {};
            console.warn = function () {};
            console.error = function () {};
        }

        // 快速获取实例
        window['Cinst'] = falcon.cacheComponents.Cinst as any;
        window['CinstAsync'] = falcon.cacheComponents.CinstAsync as any;
        window['NativeBridge'] = falcon.NativeBridge;
        window['DM'] = function (...args) {
            dotInfo.DM.call(dotInfo, ...args);
        };
        window['DS'] = function (...args) {
            dotInfo.DS.call(dotInfo, ...args);
        };

        // 请求原生是否发送打点状态
        NativeGame.callNativeHide();

        // 初始化组件劫持
        falcon.cacheComponents.componentEnabledIntercept();

        //初始化游戏图层
        initGameLayer();

        //初始化游戏模块
        ModuleList.start();

        if (falcon.NativeBridge.isNative()) {
            onNativeReponse(`hideCallback`, this.launchStart);
        } else {
            this.launchStart();
        }
    }

    protected start(): void {
        // 进入游戏的次数
        const gameEntryCount = gameInfo.gameEntryCount;
        falcon.storage.setItem(`gameEntryCount`, gameEntryCount + 1);

        // 首次进入游戏的时间
        const gameFirstEntryTime = gameInfo.gameFirstEntryTime;
        if (gameFirstEntryTime == 0) {
            let data = new Date();
            falcon.storage.setItem(`gameFirstEntryTime`, data.getFullYear() * Math.pow(10, 4) + data.getMonth() * Math.pow(10, 2) + data.getDate());
        }
    }

    // 启动开始
    launchStart() {
        if (CC_DEBUG) {
            console.log('派发 E_Launch_Start 事件');
        }
        falcon.EventManager.dispatchModuleEvent(new E_Launch_Start());
    }
}
