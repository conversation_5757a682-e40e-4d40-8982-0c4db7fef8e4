import { Component } from '../cores/Component';
import { CellColor, TargetType } from '../define/BoardDefine';
import { IRCShape } from '../define/EcsDefine';

interface IProduceBlockShape extends IRCShape {
    types: TargetType[];
}
/**
 * 组件：生产方块列表
 *
 */
export default class ProduceBlockListComponent extends Component {
    constructor(public produceBlockList: { shape: IProduceBlockShape; color?: CellColor }[]) {
        super();
    }
}
