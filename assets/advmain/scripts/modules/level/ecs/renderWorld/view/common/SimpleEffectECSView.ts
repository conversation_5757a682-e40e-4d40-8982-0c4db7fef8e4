import { AudioId } from '../../../config/conf/EcsAudioConfig';
import { ECSViewBase } from '../../ECSViewBase';

/**简单特效视图，data必传 */
export class SimpleEffectECSView extends ECSViewBase {
    public data: {
        /**播放动画名 */
        aniName: string;
        /**动画资源路径 */
        src: string;
        /**[龙骨动画图集资源名, 龙骨动画armatureName] */
        dragonNames?: string[];
        bundleName?: string;
        timeScale?: number;
        enableBatch?: boolean;
        audioId?: AudioId;
        premultipliedAlpha?: boolean;
    };
    private _effect: dragonBones.ArmatureDisplay | sp.Skeleton;
    protected async initView() {
        const res = await this.renderWorld.loadRes(
            this.data.bundleName || '',
            this.data.src,
            this.data.dragonNames ? dragonBones.DragonBonesAsset : sp.SkeletonData,
        );
        if (!cc.isValid(this.node)) return;

        if (this.data.dragonNames) {
            //dragonBones处理
            const res2 = await this.renderWorld.loadRes(
                this.data.bundleName || '',
                this.data.src.slice(0, this.data.src.lastIndexOf('/') + 1) + this.data.dragonNames[0],
                dragonBones.DragonBonesAtlasAsset,
            );
            if (!cc.isValid(this.node)) return;

            this._effect = this.node.addComponent(dragonBones.ArmatureDisplay);
            this._effect.dragonAsset = res as dragonBones.DragonBonesAsset;
            this._effect.dragonAtlasAsset = res2 as dragonBones.DragonBonesAtlasAsset;
            this._effect.armatureName = this.data.dragonNames[1];
        } else {
            //spine处理
            this._effect = this.node.addComponent(sp.Skeleton);
            this._effect.skeletonData = res as sp.SkeletonData;
        }
        this._effect.premultipliedAlpha = this.data.premultipliedAlpha || false;
        this._effect.timeScale = this.data.timeScale || 1;
        this._effect.enableBatch = this.data.enableBatch || false;

        if (this.data.audioId) {
            this.renderWorld.playAudio(this.data.audioId);
        }
        //播放动画
        if (this._effect instanceof sp.Skeleton) {
            this._effect.setAnimation(0, this.data.aniName, false);
        } else {
            this._effect.playAnimation(this.data.aniName, 1);
        }
    }
    protected disposeView() {}
    protected addEvents() {
        if (this._effect instanceof sp.Skeleton) {
            this._effect.setCompleteListener(this.__playComplete.bind(this));
        } else {
            this._effect.addEventListener(dragonBones.EventObject.COMPLETE, this.__playComplete, this);
        }
    }
    protected removeEvents() {
        if (this._effect instanceof sp.Skeleton) {
            this._effect.setCompleteListener(null);
        } else {
            this._effect.removeEventListener(dragonBones.EventObject.COMPLETE, this.__playComplete, this);
        }
    }
    private __playComplete() {
        this.renderWorld.destroyECSView(this);
    }
}
