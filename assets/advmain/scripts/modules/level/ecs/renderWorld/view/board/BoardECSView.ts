import BoardComponent from '../../../components/board/BoardComponent';
import { RelationName } from '../../../cores/center/EntityCenter/WorldRelation';
import { CellSize, BoardLayer } from '../../../define/BoardDefine';
import { ECSEvent } from '../../../GameEvent';
import { ECSViewBase, IECSViewConfig } from '../../ECSViewBase';
const { ccclass } = cc._decorator;
@ccclass
export class BoardECSView extends ECSViewBase<IECSViewConfig> {
    private xStart: number;
    private yStart: number;
    protected async initView() {
        // 根据棋盘行列设置自身宽高，便于布局 / 点击区域等
        const board = this.logicWorld.getComponent(this.logicEntityId, BoardComponent);
        this.node.width = board.cCount * CellSize;
        this.node.height = board.rCount * CellSize;

        for (const k in BoardLayer) {
            if (!isNaN(+k)) continue;
            const node = new cc.Node(k);
            node.zIndex = +BoardLayer[k] * 10;
            node.setParent(this.node);
        }
        if (board.isShowBoardBg) {
            this.setBgOri();
        }
    }
    protected addEvents() {
        this.node.on(cc.Node.EventType.TOUCH_END, this.onSwithBoard, this);
    }
    protected removeEvents() {
        this.node.off(cc.Node.EventType.TOUCH_END, this.onSwithBoard, this);
    }
    protected disposeView() {}

    private setBgOri() {
        const board = this.logicWorld.getComponent(this.logicEntityId, BoardComponent);
        this.xStart = (-board.cCount * CellSize) / 2 + CellSize / 2;
        this.yStart = (board.rCount * CellSize) / 2 - CellSize / 2;

        // 边框配置
        const borderConfigs = {
            edges: [
                { condition: (r: number, c: number) => r === 0, params: [0, 58, 0] as [number, number, number] },
                { condition: (r: number, c: number) => r === board.rCount - 1, params: [0, -58, 180] as [number, number, number] },
                { condition: (r: number, c: number) => c === 0, params: [-58, 0, 90] as [number, number, number] },
                { condition: (r: number, c: number) => c === board.cCount - 1, params: [58, 0, -90] as [number, number, number] },
            ],

            // 外角配置
            outerCorners: [
                {
                    condition: (r: number, c: number) => !this.slotEntity(r, c - 1) && !this.slotEntity(r - 1, c),
                    params: [-58, 58, 0] as [number, number, number],
                },
                {
                    condition: (r: number, c: number) => !this.slotEntity(r, c + 1) && !this.slotEntity(r - 1, c),
                    params: [58, 58, -90] as [number, number, number],
                },
                {
                    condition: (r: number, c: number) => !this.slotEntity(r, c - 1) && !this.slotEntity(r + 1, c),
                    params: [-58, -58, 90] as [number, number, number],
                },
                {
                    condition: (r: number, c: number) => !this.slotEntity(r, c + 1) && !this.slotEntity(r + 1, c),
                    params: [58, -58, 180] as [number, number, number],
                },
            ],

            innerLines: [
                {
                    condition: (r: number, c: number) => !this.slotEntity(r - 1, c) && this.slotEntity(r + 1, c),
                    params: [0, -48, 0] as [number, number, number],
                },
                {
                    condition: (r: number, c: number) => !this.slotEntity(r + 1, c) && this.slotEntity(r - 1, c),
                    params: [0, 48, 180] as [number, number, number],
                },
                {
                    condition: (r: number, c: number) => !this.slotEntity(r, c - 1) && this.slotEntity(r, c + 1),
                    params: [48, 0, 90] as [number, number, number],
                },
                {
                    condition: (r: number, c: number) => !this.slotEntity(r, c + 1) && this.slotEntity(r, c - 1),
                    params: [-48, 0, -90] as [number, number, number],
                },
            ],

            innerCorners: [
                {
                    condition: (r: number, c: number) => this.slotEntity(r, c - 1) && this.slotEntity(r - 1, c),
                    params: [-45, 45, -90] as [number, number, number],
                },
                {
                    condition: (r: number, c: number) => this.slotEntity(r, c + 1) && this.slotEntity(r - 1, c),
                    params: [45, 45, 180] as [number, number, number],
                },
                {
                    condition: (r: number, c: number) => this.slotEntity(r, c - 1) && this.slotEntity(r + 1, c),
                    params: [-45, -45, 0] as [number, number, number],
                },
                {
                    condition: (r: number, c: number) => this.slotEntity(r, c + 1) && this.slotEntity(r + 1, c),
                    params: [45, -45, 90] as [number, number, number],
                },
            ],
        };

        for (let r = 0; r < board.rCount; r++) {
            for (let c = 0; c < board.cCount; c++) {
                if (this.slotEntity(r, c)) {
                    // 处理有槽位的格子：边缘线条和外角
                    borderConfigs.edges.forEach((config) => {
                        if (config.condition(r, c)) {
                            this.addBorder('bg_slot_line', r, c, config.params[0], config.params[1], config.params[2]);
                        }
                    });

                    borderConfigs.outerCorners.forEach((config) => {
                        if (config.condition(r, c)) {
                            this.addBorder('bg_slot_out', r, c, config.params[0], config.params[1], config.params[2]);
                        }
                    });
                } else {
                    // 处理空槽位的格子：内部线条和内角
                    borderConfigs.innerLines.forEach((config) => {
                        if (config.condition(r, c)) {
                            this.addBorder('bg_slot_line', r, c, config.params[0], config.params[1], config.params[2]);
                        }
                    });

                    borderConfigs.innerCorners.forEach((config) => {
                        if (config.condition(r, c)) {
                            this.addBorder('bg_slot_in', r, c, config.params[0], config.params[1], config.params[2]);
                        }
                    });
                }
            }
        }
    }

    private slotEntity(r: number, c: number) {
        return this.logicWorld.getTargets(this.logicEntityId, RelationName.PARENT_CHILD, `${r}_${c}`)[0];
    }

    onSwithBoard() {
        this.logicWorld.pushInputCmd({ event: ECSEvent.GameEvent.SWITCH_BOARD, data: null });
    }

    private addBorder(name: string, r: number, c: number, rOffset: number, cOffset: number, angle: number) {
        const spNode = new cc.Node();
        spNode.setPosition(this.xStart + c * CellSize + rOffset, this.yStart - r * CellSize + cOffset);
        spNode.angle = angle;
        spNode.zIndex = 10000;
        spNode.setParent(this.node.children[BoardLayer.Bg]);
        this.renderWorld
            .loadRes(this.config.prefabBundleName, `textures/level/boardBase/${name}`, cc.SpriteFrame)
            .then((spf: cc.SpriteFrame) => (spNode.addComponent(cc.Sprite).spriteFrame = spf));
    }
}
