import { AlgoClassTypeConst } from "./core/puzzlecore";

export class PuzzleCfg {
    //10001
    private static algo_random = '{"config":{"BlockLayer":[200005,200006],"AlsoSelectLayer":[400007,400007,400007]},"data":{"400007":[{"index":0},{"index":1},{"index":2}]}}';
    //10002
    private static algo_random_first = '{"config":{"BlockLayer":[200001,200003,200006],"AlsoSelectLayer":[400008,400008,400008],"BasicLayer":[600004]},"data":{"200001":{"startBlockId":2,"endBlockId":31},"200003":{"sort":1},"400008":[{"title":"随机","index":0},{"title":"随机","index":1},{"title":"随机","index":2}],"500001":{"pos":1}}}';
    //10003
    private static algo_random_fix_block = '{"config":{"BlockLayer":[200004,200006],"AlsoSelectLayer":[400007,400007,400007]},"data":{"200004":{"blocks":[1,2,3,4,5,6,9,15,27,28]},"400007":[{"title":"固定块池随机","index":0,"useRatio":0},{"title":"固定块池随机","index":1,"useRatio":0},{"title":"固定块池随机","index":2,"useRatio":0}]}}';
    //11001
    private static algo_entropy_increase1 = '{"config":{"BlockLayer":[200005,200006],"AlsoSelectLayer":[400007,400007,400007]},"data":{"400007":[{"index":0},{"index":1},{"index":2}]}}';
    //12001
    private static algo_entropy_increase3 = '{"config":{"BlockLayer":[200005,200003,200006,200005,200003,200006,200005,200003,200006,200001,200003,200006],"AlsoSelectLayer":[400005,400005,400005],"PosLayer":[500001],"BasicLayer":[600004]},"data":{"200001":{"startBlockId":2,"endBlockId":31},"200003":[{"sort":2},{"sort":2},{"sort":2},{"sort":2}],"400005":[{"index":0,"blockGroup":0},{"index":1,"blockGroup":1},{"index":2,"blockGroup":2}],"500001":{"pos":1},"600004":{"blockGroup":3}}}';
    //12002
    private static algo_entropy_increase3_zimu = '{"config":{"BlockLayer":[200004,200006,200004,200006],"AlsoSelectLayer":[400006]},"data":{"200004":[{"blocks":[22,17,7,11,35,36,13]},{"blocks":[5,4,9,3,2]}]}}';
    //14001
    private static algo_fill_in_the_blank = '{"config":{"BlockLayer":[200001,200002,200003,200006,200001,200003,200006],"AlsoSelectLayer":[400001,400001,400001],"PosLayer":[500001],"BasicLayer":[600004]},"data":{"200001":[{"startBlockId":2,"endBlockId":42},{"startBlockId":2,"endBlockId":31}],"200003":[{"sort":1},{"sort":2}],"400001":[{"index":0},{"index":1},{"index":2}],"500001":{"pos":1},"600004":{"blockGroup":1}}}';
    //14002
    private static algo_fill_in_the_blank_clear = '{"config":{"BlockLayer":[200001,200002,200003,200006,200001,200003,200006],"AlsoSelectLayer":[400001,400001,400001],"PosLayer":[500001],"BasicLayer":[600004]},"data":{"200001":[{"startBlockId":2,"endBlockId":42},{"startBlockId":2,"endBlockId":31}],"200003":[{"sort":1},{"sort":2}],"400001":[{"index":0,"statelessId":3},{"index":1,"statelessId":3},{"index":2,"statelessId":3}],"500001":{"pos":1},"600004":{"blockGroup":1}}}';
    //14003
    private static algo_fill_in_the_blank_id_9 = '{"config":{"BlockLayer":[200001,200002,200003,200006,200001,200003,200006],"AlsoSelectLayer":[400003],"PosLayer":[500001],"BasicLayer":[600004]},"data":{"200001":[{"startBlockId":2,"endBlockId":42},{"startBlockId":2,"endBlockId":31}],"200003":[{"sort":1},{"sort":2}],"400003":[{"select":2,"statelessId":1}],"500001":{"pos":1},"600004":{"blockGroup":1}}}';
    //14004
    private static algo_fill_in_the_blank_id_21 = '{"config":{"BlockLayer":[200001,200002,200003,200006,200001,200003,200006],"AlsoSelectLayer":[400003],"PosLayer":[500001],"BasicLayer":[600004]},"data":{"200001":[{"startBlockId":2,"endBlockId":42},{"startBlockId":2,"endBlockId":31}],"200003":[{"sort":1},{"sort":2}],"400003":[{"select":2,"putWay":2,"statelessId":1}],"500001":{"pos":1},"600004":{"blockGroup":1}}}';
    //14005
    private static algo_fill_in_the_blank_by_pos = '{"config":{"BlockLayer":[200001,200002,200003,200006,200001,200003,200006],"AlsoSelectLayer":[400002,400002,400002],"PosLayer":[500001],"BasicLayer":[600004]},"data":{"200001":[{"startBlockId":2,"endBlockId":42},{"startBlockId":2,"endBlockId":31}],"200003":[{"sort":1},{"sort":2}],"400002":[{"index":0,"statelessId":1},{"index":1,"statelessId":1},{"index":2,"statelessId":1}],"500001":{"pos":1},"600004":{"blockGroup":1}}}';
    //14006
    private static algo_fill_in_the_blank_clear_by_pos = '{"config":{"BlockLayer":[200001,200002,200003,200006,200001,200003,200006],"AlsoSelectLayer":[400002,400002,400002],"PosLayer":[500001],"BasicLayer":[600004]},"data":{"200001":[{"startBlockId":2,"endBlockId":42},{"startBlockId":2,"endBlockId":31}],"200003":[{"sort":1},{"sort":2}],"400002":[{"index":0,"statelessId":2},{"index":1,"statelessId":2},{"index":2,"statelessId":2}],"500001":{"pos":1},"600004":{"blockGroup":1}}}';
    //14007
    private static algo_fill_in_the_blank_journey = '{"config":{"PreLayer":[],"BlockLayer":[200001,200002,200003,200006,200001,200003,200006],"AlsoSelectLayer":[400001,400001,400001],"PosLayer":[500001],"BasicLayer":[600004]},"data":{"200001":[{"startBlockId":2,"endBlockId":31},{"startBlockId":2,"endBlockId":31}],"200002":{"highScore":0},"200003":[{"sort":1},{"sort":2}],"400001":[{"index":0,"statelessId":3,"title":"旅行填空消除"},{"index":1,"statelessId":3,"title":"旅行填空消除"},{"index":2,"statelessId":3,"title":"旅行填空消除"}],"500001":{"pos":1},"600004":{"blockGroup":1}}}';
    //14008
    private static algo_fill_in_the_blank_high_effect = '{"config":{"PreLayer":[],"BlockLayer":[200001,200002,200003,200006],"AlsoSelectLayer":[400013],"PosLayer":[500001],"BasicLayer":[]},"data":{"200001":[{"startBlockId":2,"endBlockId":31},{"startBlockId":2,"endBlockId":31}],"200003":[{"sort":1},{"sort":2}],"400013":{"title":"填空消除","statelessId":9},"500001":{"pos":1}}}';
    //14009
    private static algo_fill_in_the_blank_id_70 = '{"config":{"BlockLayer":[200001,200002,200003,200006,200001,200003,200006],"AlsoSelectLayer":[400003],"PosLayer":[500001],"BasicLayer":[600004]},"data":{"200001":[{"startBlockId":2,"endBlockId":42},{"startBlockId":2,"endBlockId":31}],"200003":[{"sort":1},{"sort":2}],"400003":[{"select":2,"putWay":3,"ratios":[4,2,1],"isClear":true,"isNear":false,"statelessId":1}],"500001":{"pos":1},"600004":{"blockGroup":1}}}';
    //14010
    private static algo_fill_in_the_blank_clear_cool = '{"config":{"BlockLayer":[200001,200002,200003,200006,200001,200003,200006],"AlsoSelectLayer":[400012],"PosLayer":[500001],"BasicLayer":[600004]},"data":{"200001":[{"startBlockId":2,"endBlockId":42},{"startBlockId":2,"endBlockId":31}],"200003":[{"sort":0},{"sort":2}],"400012":{"index":0,"title":"消除爽","statelessId":2,"blockGroup":0,"clearState":1},"500001":{"pos":1},"600004":{"blockGroup":1}}}';
    //14011
    private static algo_fill_in_the_blank_journey_by_pos = '{"config":{"PreLayer":[],"BlockLayer":[200001,200002,200003,200006,200001,200003,200006],"AlsoSelectLayer":[400002,400002,400002],"PosLayer":[500001],"BasicLayer":[600004]},"data":{"200001":[{"startBlockId":2,"endBlockId":31},{"startBlockId":2,"endBlockId":31}],"200002":{"highScore":0},"200003":[{"sort":1},{"sort":2}],"400002":[{"index":0,"statelessId":2,"title":"旅行填空消除"},{"index":1,"statelessId":2,"title":"旅行填空消除"},{"index":2,"statelessId":2,"title":"旅行填空消除"}],"500001":{"pos":1},"600004":{"blockGroup":1}}}';
    //16001
    private static algo_hard = '{"config":{"BlockLayer":[200005,200003,200006],"AlsoSelectLayer":[400004]},"data":{"200003":{"sort":2},"400004":[{"statelessId":2}]}}';
    //16002
    private static algo_simple_instinct_hard = '{"config":{"BlockLayer":[200005,200003,200006],"AlsoSelectLayer":[400004]},"data":{"200003":{"sort":2},"400004":[{"title":"简单直觉题","statelessId":6}]}}';
    //16003
    private static algo_simple_death = '{"config":{"BlockLayer":[200005,200003,200006],"AlsoSelectLayer":[400004]},"data":{"200003":{"sort":2},"400004":[{"title":"简单难题","statelessId":7}]}}';
    //17001
    private static algo_random_no_death = '{"config":{"BlockLayer":[200005,200003,200006,200001,200003,200006],"AlsoSelectLayer":[400009]},"data":{"200001":{"startBlockId":2,"endBlockId":32},"200003":[{"sort":2},{"sort":2}]}}';
    //18001
    private static algo_instinct_hard = '{"config":{"BlockLayer":[200004,200006],"AlsoSelectLayer":[400004]},"data":{"200004":{"blocks":[11,13,22,12,24,21,23,14,16,18,19]},"400004":[{"title":"直觉难题","statelessId":3}]}}';
    //20001
    private static algo_revive = '{"config":{"BlockLayer":[200004,200006],"AlsoSelectLayer":[400010]},"data":{"200004":{"blocks":[28,27,15,6,3,2,4,5,9,1]}}}';
    //21001
    private static algo_clear_board = '{"config":{"PreLayer":[100001],"BlockLayer":[200001,200003,200006],"AlsoSelectLayer":[400011],"PosLayer":[500001]},"data":{"100001":{"algoType":17001}}}';
    //22001
    private static algo_stop_refresh_record = '{"config":{"BlockLayer":[200004,200006,200001,200003,200006],"AlsoSelectLayer":[400013],"PosLayer":[500001],"BasicLayer":[600004]},"data":{"200001":{"startBlockId":2,"endBlockId":31},"200003":{"sort":2},"200004":{"blocks":[7,9,10,11,12,13,14,16,17,18,19,20,21,22,23,24,25,26,29,30,31,32,33,34,35,36,42]},"400013":{"title":"终止刷分","statelessId":8},"600004":{"blockGroup":1}}}';
    //23001
    private static algo_same_more_block = '{"config":{"BlockLayer":[200001,200003,200006],"AlsoSelectLayer":[],"PosLayer":[500001],"BasicLayer":[600004]},"data":{"200001":{"startBlockId":2,"endBlockId":31},"200003":{"sort":2},"600004":{"blockGroup":0,"filterRepeat":1,"defaultReplace":0}}}';

    private static algo_cfg: Map<AlgoClassTypeConst, string> = new Map([
        [AlgoClassTypeConst.algo_random, PuzzleCfg.algo_random],
        [AlgoClassTypeConst.algo_random_first, PuzzleCfg.algo_random_first],
        [AlgoClassTypeConst.algo_random_fix_block, PuzzleCfg.algo_random_fix_block],
        [AlgoClassTypeConst.algo_entropy_increase1, PuzzleCfg.algo_entropy_increase1],
        [AlgoClassTypeConst.algo_entropy_increase3, PuzzleCfg.algo_entropy_increase3],
        [AlgoClassTypeConst.algo_entropy_increase3_zimu, PuzzleCfg.algo_entropy_increase3_zimu],
        [AlgoClassTypeConst.algo_fill_in_the_blank, PuzzleCfg.algo_fill_in_the_blank],
        [AlgoClassTypeConst.algo_fill_in_the_blank_clear, PuzzleCfg.algo_fill_in_the_blank_clear],
        [AlgoClassTypeConst.algo_fill_in_the_blank_by_pos, PuzzleCfg.algo_fill_in_the_blank_by_pos],
        [AlgoClassTypeConst.algo_fill_in_the_blank_clear_by_pos, PuzzleCfg.algo_fill_in_the_blank_clear_by_pos],
        [AlgoClassTypeConst.algo_fill_in_the_blank_id_9, PuzzleCfg.algo_fill_in_the_blank_id_9],
        [AlgoClassTypeConst.algo_fill_in_the_blank_id_21, PuzzleCfg.algo_fill_in_the_blank_id_21],
        [AlgoClassTypeConst.algo_fill_in_the_blank_id_70, PuzzleCfg.algo_fill_in_the_blank_id_70],
        [AlgoClassTypeConst.algo_fill_in_the_blank_journey, PuzzleCfg.algo_fill_in_the_blank_journey],
        [AlgoClassTypeConst.algo_fill_in_the_blank_journey_by_pos, PuzzleCfg.algo_fill_in_the_blank_journey_by_pos],
        [AlgoClassTypeConst.algo_fill_in_the_blank_high_effect, PuzzleCfg.algo_fill_in_the_blank_high_effect],
        [AlgoClassTypeConst.algo_random_no_death, PuzzleCfg.algo_random_no_death],
        [AlgoClassTypeConst.algo_revive, PuzzleCfg.algo_revive],
        [AlgoClassTypeConst.algo_hard, PuzzleCfg.algo_hard],
        [AlgoClassTypeConst.algo_instinct_hard, PuzzleCfg.algo_instinct_hard],
        [AlgoClassTypeConst.algo_simple_death, PuzzleCfg.algo_simple_death],
        [AlgoClassTypeConst.algo_simple_instinct_hard, PuzzleCfg.algo_simple_instinct_hard],
        [AlgoClassTypeConst.algo_clear_board, PuzzleCfg.algo_clear_board],
        [AlgoClassTypeConst.algo_fill_in_the_blank_clear_cool, PuzzleCfg.algo_fill_in_the_blank_clear_cool],
        [AlgoClassTypeConst.algo_stop_refresh_record, PuzzleCfg.algo_stop_refresh_record],
        [AlgoClassTypeConst.algo_same_more_block, PuzzleCfg.algo_same_more_block]
    ]);

    public static getConfig(algoType: AlgoClassTypeConst) {
        const str = PuzzleCfg.algo_cfg.get(algoType);
        if (str && str != '') {
            return JSON.parse(str);
        }
        return null;
    }

}
