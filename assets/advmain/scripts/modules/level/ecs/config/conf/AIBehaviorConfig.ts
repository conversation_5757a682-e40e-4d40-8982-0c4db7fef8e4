import { IAIBehaviorConfig } from '../../behavior/ai/AIBehaviorBase';
import { AIBehaviorConst } from '../../registry/behavior/AIBehaviorRegistry';

/**AI行为配置 */
export const AIBehaviorConfig: Record<string, IAIBehaviorConfig> = {
    ai_cell: {
        aiBehaviorId: 'ai_cell',
        behaviorDesc: '块AI',
        behaviorType: AIBehaviorConst.CellAIBehavior,
    },
    ai_cell_plane: {
        aiBehaviorId: 'ai_cell_plane',
        behaviorDesc: '飞机块AI',
        behaviorType: AIBehaviorConst.PlaneAIBehavior,
    },
    ai_cell_glass: {
        aiBehaviorId: 'ai_cell_glass',
        behaviorDesc: '玻璃块AI',
        behaviorType: AIBehaviorConst.GlassAIBehavior,
    },
    ai_cell_diamond: {
        aiBehaviorId: 'ai_cell_diamond',
        behaviorDesc: '钻石块AI',
        behaviorType: AIBehaviorConst.DiamondAIBehavior,
    },
};
