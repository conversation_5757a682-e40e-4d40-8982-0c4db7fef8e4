

export class HHotUpdate {
    /**
     * HHotUpdate 向服务器获取版本控制
     * @param url 
     * @param data 
     * @returns 
     */
    static getRemoteVersionCfg(url: string) {
        return falcon.http.requestAsync(url, {}, { type: falcon.HttpType.GET, contentType: 'text/plain;charset=UTF-8' });
    }

    /**
    * HHotUpdate 拉取版本的versionManiFest
    * @param url 
    * @param data 
    * @returns 
    */
    static getRemoteVersionManifest(url: string) {
        return falcon.http.requestAsync(url, {}, { type: falcon.HttpType.GET, contentType: 'text/plain;charset=UTF-8' });
    }

    /**
     * 获取 manifest 数据
     * @param url 
     * @returns 
     */
    static getProjectManifest(): Promise<falcon.IProjectManifest> {
        return this.getManifest<falcon.IProjectManifest>("project.manifest");
    }

    /**
     * 获取 manifest 数据
     * @param url 
     * @returns 
     */
    static getVersionManifest(): Promise<falcon.IVersionManifest> {
        return this.getManifest<falcon.IVersionManifest>("version.manifest");
    }

    static getManifest<T>(fileName: string): Promise<T> {

        if (!falcon.NativeBridge.isNative()) {
            return falcon.http.requestAsync(fileName, {}, { type: falcon.HttpType.GET });
        }

        return new Promise((resolve, reject) => {
            if (!jsb.fileUtils.isFileExist(fileName)) {
                reject(null)
                return;
            }
            const content = jsb.fileUtils.getStringFromFile(fileName);
            if (content) {
                const data = JSON.parse(content);
                resolve(data);
            } else {
                reject(null);
            }
        });
    }

}