import BoardComponent from "../../../components/board/BoardComponent";
import { System } from "../../../cores/System";
import { ECSEvent } from "../../../GameEvent";
import { IEliminationInfo, IPutBlockBackInfo } from "../../../GameEventData";

export default class ComboSystem extends System {

    init(): void {
        this.world.eventBus.on(ECSEvent.GameEvent.PUT_BLOCK_BACK, this.checkComboContinues, this);
        this.world.eventBus.on(ECSEvent.GameEvent.ELIMINATION, this.checkComboCount, this);
    }
    dispose(): void {
        this.world.eventBus.off(ECSEvent.GameEvent.PUT_BLOCK_BACK, this.checkComboContinues, this);
        this.world.eventBus.off(ECSEvent.GameEvent.ELIMINATION, this.checkComboCount, this);
    }
    update(dt: number): void {}

    private checkComboCount(eliminationInfo: IEliminationInfo) {
        const boardEntity = eliminationInfo.boardEntity;
        const boardComponent = this.world.getComponent(boardEntity, BoardComponent);
        if (!boardComponent) return;

        if (boardComponent.emptyCount > 3) {
            boardComponent.comboCount = 1;
        }
        else {
            boardComponent.comboCount++;
            // 发送连击数变更事件
            this.world.eventBus.emit(ECSEvent.GameEvent.COMBO_CHANGED, {
                boardEntity,
                comboCount: boardComponent.comboCount,
            });
        }
        boardComponent.emptyCount = 0;
        // console.log('yjf__连击数为：', boardComponent.comboCount);
    }

    private checkComboContinues(putBlockInfo: IPutBlockBackInfo) {
        if (!putBlockInfo.isSuccess) return;
        const boardEntity = this.world
            .query([BoardComponent])
            .find((eid) => this.world.getComponent(eid, BoardComponent).boardId === putBlockInfo.boardId);
        const boardComponent = this.world.getComponent(boardEntity, BoardComponent);
        if (!boardComponent) return;
        boardComponent.emptyCount++;
        // console.log('yjf__空放数为：', boardComponent.emptyCount);
    }
}                                              