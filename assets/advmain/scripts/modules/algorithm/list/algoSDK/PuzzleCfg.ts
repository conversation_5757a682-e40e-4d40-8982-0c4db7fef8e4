import { AlgoClassTypeConst } from "./core/puzzlecore";

export class PuzzleCfg {
    private static algoArr: Map<string, any> = new Map([
        ['10001', '{"config":{"BlockLayer":[200005,200006],"AlsoSelectLayer":[400007,400007,400007]},"data":{"400007":[{"index":0},{"index":1},{"index":2}]}}'],
        ['10002', '{"config":{"BlockLayer":[200001,200003,200006],"AlsoSelectLayer":[400008,400008,400008],"BasicLayer":[600004]},"data":{"200001":{"startBlockId":2,"endBlockId":31},"200003":{"sort":1},"400008":[{"title":"随机","index":0},{"title":"随机","index":1},{"title":"随机","index":2}]}}'],
        ['10003', '{"config":{"BlockLayer":[200004,200006],"AlsoSelectLayer":[400007,400007,400007]},"data":{"200004":{"blocks":[1,2,3,4,5,6,9,15,27,28]},"400007":[{"title":"固定块池随机","index":0,"useRatio":0},{"title":"固定块池随机","index":1,"useRatio":0},{"title":"固定块池随机","index":2,"useRatio":0}]}}'],
        ['10004', '{"config":{"BlockLayer":[200005,200006],"AlsoSelectLayer":[400007,400007,400007]},"data":{"400007":[{"index":0},{"index":1},{"index":2}]}}'],
        ['12001', '{"config":{"BlockLayer":[200005,200003,200006,200005,200003,200006,200005,200003,200006],"AlsoSelectLayer":[400005,400005,400005]},"data":{"200003":[{"sort":0},{"sort":2},{"sort":2}],"400005":[{"index":0,"blockGroup":0},{"index":1,"blockGroup":1},{"index":2,"blockGroup":2}]}}'],
        ['12002', '{"config":{"BlockLayer":[200004,200006,200004,200006],"AlsoSelectLayer":[400006]},"data":{"200004":[{"blocks":[22,17,7,11,35,36,13]},{"blocks":[5,4,9,3,2]}]}}'],
        ['14001', '{"config":{"BlockLayer":[200001,200002,200003,200006],"AlsoSelectLayer":[400001,400001,400001],"PosLayer":[500001],"BasicLayer":[600004]},"data":{"200001":{"startBlockId":2,"endBlockId":42},"200003":[{"sort":1},{"sort":2}],"400001":[{"index":0},{"index":1},{"index":2}],"500001":{"pos":1},"600004":{"blockGroup":0}}}'],
        ['14002', '{"config":{"BlockLayer":[200001,200002,200003,200006],"AlsoSelectLayer":[400001,400001,400001],"PosLayer":[500001],"BasicLayer":[600004]},"data":{"200001":{"startBlockId":2,"endBlockId":42},"200003":[{"sort":1},{"sort":2}],"400001":[{"index":0,"statelessId":3},{"index":1,"statelessId":3},{"index":2,"statelessId":3}],"500001":{"pos":1},"600004":{"blockGroup":0}}}'],
        ['14003', '{"config":{"BlockLayer":[200001,200002,200003,200006],"AlsoSelectLayer":[400003],"PosLayer":[500001],"BasicLayer":[600004]},"data":{"200001":{"startBlockId":2,"endBlockId":42},"200003":{"sort":1},"400003":[{"select":2,"statelessId":1}],"500001":{"pos":1},"600004":{"blockGroup":0,"replaceCount":2}}}'],
        ['14004', '{"config":{"BlockLayer":[200001,200002,200003,200006],"AlsoSelectLayer":[400003],"PosLayer":[500001],"BasicLayer":[600004]},"data":{"200001":{"startBlockId":2,"endBlockId":42},"200003":{"sort":1},"400003":[{"select":2,"putWay":2,"statelessId":1}],"500001":{"pos":1},"600004":{"blockGroup":0,"replaceCount":2}}}'],
        ['14005', '{"config":{"BlockLayer":[200001,200002,200003,200006,200004,200006],"AlsoSelectLayer":[400002,400002,400002],"PosLayer":[500001],"BasicLayer":[600004]},"data":{"200001":{"startBlockId":2,"endBlockId":42},"200003":{"sort":1},"200004":{"blocks":[2,3,37,38,6,27,28,15,5,4,39,40,41,1]},"200006":[{"needFilter":true,"needAdd":true},{"needFilter":false,"needAdd":false}],"400002":[{"index":0,"statelessId":1},{"index":1,"statelessId":1},{"index":2,"statelessId":1}],"500001":{"pos":1},"600004":{"blockGroup":1,"algoType":2}}}'],
        ['14006', '{"config":{"BlockLayer":[200001,200002,200003,200006],"AlsoSelectLayer":[400002,400002,400002],"PosLayer":[500001],"BasicLayer":[600004]},"data":{"200001":{"startBlockId":2,"endBlockId":42},"200003":{"sort":1},"400002":[{"index":0,"statelessId":2},{"index":1,"statelessId":2},{"index":2,"statelessId":2}],"500001":{"pos":1},"600004":{"blockGroup":0,"algoType":2}}}'],
        ['14007', '{"config":{"PreLayer":[],"BlockLayer":[200001,200002,200003,200006],"AlsoSelectLayer":[400001,400001,400001],"PosLayer":[500001]},"data":{"200001":{"startBlockId":2,"endBlockId":31},"200002":{"highScore":0},"200003":{"sort":1},"400001":[{"index":0,"statelessId":3,"title":"旅行填空消除"},{"index":1,"statelessId":3,"title":"旅行填空消除"},{"index":2,"statelessId":3,"title":"旅行填空消除"}],"500001":{"pos":1}}}'],
        ['14008', '{"config":{"PreLayer":[],"BlockLayer":[200001,200002,200003,200006],"AlsoSelectLayer":[400013],"PosLayer":[500001],"BasicLayer":[]},"data":{"200001":[{"startBlockId":2,"endBlockId":31},{"startBlockId":2,"endBlockId":31}],"200003":[{"sort":1},{"sort":2}],"400013":{"title":"填空消除","statelessId":9},"500001":{"pos":1}}}'],
        ['14009', '{"config":{"BlockLayer":[200001,200002,200003,200006],"AlsoSelectLayer":[400003],"PosLayer":[500001],"BasicLayer":[600004]},"data":{"200001":{"startBlockId":2,"endBlockId":42},"200003":{"sort":1},"400003":[{"select":2,"putWay":3,"ratios":[4,2,1],"isClear":true,"isNear":false,"statelessId":1}],"500001":{"pos":1},"600004":{"blockGroup":0,"replaceCount":2}}}'],
        ['14010', '{"config":{"BlockLayer":[200001,200002,200003,200006],"AlsoSelectLayer":[400012],"PosLayer":[500001],"BasicLayer":[600004]},"data":{"200001":{"startBlockId":2,"endBlockId":42},"200003":[{"sort":0},{"sort":2}],"400012":{"index":0,"title":"消除爽","statelessId":2,"blockGroup":0,"clearState":1},"500001":{"pos":1},"600004":{"blockGroup":0}}}'],
        ['14011', '{"config":{"PreLayer":[],"BlockLayer":[200001,200002,200003,200006],"AlsoSelectLayer":[400002,400002,400002],"PosLayer":[500001]},"data":{"200001":{"startBlockId":2,"endBlockId":31},"200002":{"highScore":0},"200003":[{"sort":1},{"sort":2}],"400002":[{"index":0,"statelessId":2,"title":"旅行填空消除"},{"index":1,"statelessId":2,"title":"旅行填空消除"},{"index":2,"statelessId":2,"title":"旅行填空消除"}],"500001":{"pos":1}}}'],
        ['14012', '{"config":{"BlockLayer":[200001,200002,200003,200006],"AlsoSelectLayer":[400014,400014,400014],"PosLayer":[500001]},"data":{"200001":{"startBlockId":2,"endBlockId":42},"200003":{"sort":2},"400014":[{"index":0},{"index":1},{"index":2}],"500001":{"pos":1,"namePos":1}}}'],
        ['16001', '{"config":{"BlockLayer":[200005,200003,200006],"AlsoSelectLayer":[400004]},"data":{"200003":{"sort":2},"400004":[{"statelessId":2}]}}'],
        ['16002', '{"config":{"BlockLayer":[200005,200003,200006],"AlsoSelectLayer":[400004]},"data":{"200003":{"sort":2},"400004":[{"title":"简单直觉题","statelessId":6}]}}'],
        ['16003', '{"config":{"BlockLayer":[200005,200003,200006],"AlsoSelectLayer":[400004]},"data":{"200003":{"sort":2},"400004":[{"title":"简单难题","statelessId":7}]}}'],
        ['16004', '{"config":{"BlockLayer":[200005,200003,200006],"AlsoSelectLayer":[400004]},"data":{"200003":{"sort":2},"400004":{"title":"高回报难题","statelessId":10}}}'],
        ['16005', '{"config":{"BlockLayer":[200001,200002,200006],"AlsoSelectLayer":[400004]},"data":{"200001":{"startBlockId":2,"endBlockId":42},"400004":{"title":"困难难题","statelessId":11}}}'],
        ['16006', '{"config":{"BlockLayer":[200001,200002,200006],"AlsoSelectLayer":[400004]},"data":{"200001":{"startBlockId":2,"endBlockId":42},"400004":{"title":"困难难题","statelessId":12}}}'],
        ['16007', '{"config":{"BlockLayer":[200001,200002,200006],"AlsoSelectLayer":[400004]},"data":{"200001":{"startBlockId":2,"endBlockId":42},"400004":{"title":"困难难题","statelessId":13}}}'],
        ['16008', '{"config":{"BlockLayer":[200001,200002,200006],"AlsoSelectLayer":[400004]},"data":{"200001":{"startBlockId":2,"endBlockId":42},"400004":{"title":"困难难题","statelessId":14}}}'],
        ['16009', '{"config":{"BlockLayer":[200005,200003,200006],"AlsoSelectLayer":[400004]},"data":{"200003":{"sort":2},"400004":{"title":"困难难题","statelessId":15}}}'],
        ['17001', '{"config":{"BlockLayer":[200005,200003,200006,200001,200003,200006,200004,200006],"AlsoSelectLayer":[400009]},"data":{"200001":{"startBlockId":2,"endBlockId":32},"200003":[{"sort":2},{"sort":2}],"200004":{"blocks":[28,27,15,6,3,2,4,5,9,1]},"200005":{"checkLimitSmall":true}}}'],
        ['18001', '{"config":{"BlockLayer":[200004,200006],"AlsoSelectLayer":[400004]},"data":{"200004":{"blocks":[11,13,22,12,24,21,23,14,16,18,19]},"400004":[{"title":"直觉难题","statelessId":3}]}}'],
        ['20001', '{"config":{"BlockLayer":[200004,200006],"AlsoSelectLayer":[400010]},"data":{"200004":{"blocks":[28,27,15,6,3,2,4,5,9,1]}}}'],
        ['20002', '{"config":{"BlockLayer":[200001,200006,200004,200006],"AlsoSelectLayer":[400001,400001,400001]},"data":{"200001":{"startBlockId":2,"endBlockId":42},"200004":{"blocks":[1]},"400001":[{"title":"复活","index":1,"statelessId":4,"blockGroup":0},{"title":"复活","index":0,"statelessId":4,"blockGroup":1},{"title":"复活","index":2,"statelessId":4,"blockGroup":1}]}}'],
        ['21001', '{"config":{"PreLayer":[],"BlockLayer":[200001,200003,200006],"AlsoSelectLayer":[400011]},"data":{}}'],
        ['22001', '{"config":{"BlockLayer":[200004,200006],"AlsoSelectLayer":[400013],"BasicLayer":[600004]},"data":{"200001":{"startBlockId":2,"endBlockId":31},"200003":{"sort":2},"200004":{"blocks":[7,9,10,11,12,13,14,16,17,18,19,20,21,22,23,24,25,26,29,30,31,32,33,34,35,36,42]},"400013":{"title":"终止刷分","statelessId":8},"600004":{"blockGroup":0}}}'],
        ['23001', '{"config":{"BlockLayer":[200001,200003,200006],"AlsoSelectLayer":[],"BasicLayer":[600004]},"data":{"200001":{"startBlockId":2,"endBlockId":31},"200003":{"sort":2},"600004":{"blockGroup":0}}}']
    ]);

    public static getConfig(algoType: AlgoClassTypeConst) {
        let str = PuzzleCfg.algoArr.get('' + algoType);
        if (str && str != '') {
            return JSON.parse(str);
        }
        return null;
    }

}
