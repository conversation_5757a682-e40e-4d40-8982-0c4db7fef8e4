import { ECSRuleBase, IECSRuleConfig } from './core/ECSRuleBase';
import ReviveBuffComponent from '../components/ReviveBuffComponent';
export interface ICommonReviveBuffRuleConfig extends IECSRuleConfig {
    /**复活buff值 */
    reviveBuff: boolean;
}
// 通用复活buff规则
export class CommonReviveBuffRule extends ECSRuleBase<ICommonReviveBuffRuleConfig> {
    canExecute(): boolean {
        const entityId = this.getReviveBuffEntity();
        const reviveBuffComp = this.world.getComponent<ReviveBuffComponent>(entityId, ReviveBuffComponent);
        reviveBuffComp.value = this.config.reviveBuff;
        return !reviveBuffComp.value;
    }
    execute(): void { }
    getReviveBuffEntity(): number {
        // 查询已存在的ReviveBuffComponent实体
        console.log('getReviveBuffEntity 查询已存在的ReviveBuffComponent实体', this.world.entityCenter);
        const existingEntities = this.world.query([ReviveBuffComponent]);
        const reviveBuffComp = this.world.getComponent<ReviveBuffComponent>(existingEntities[0], ReviveBuffComponent);
        // 如果已存在，返回第一个实体ID
        if (existingEntities.length > 0) {
            if (!reviveBuffComp) {
                this.world.addComponent(existingEntities[0], ReviveBuffComponent, false);
            }
            console.log('getReviveBuffEntity 已存在');
            return existingEntities[0];
        }
        // 如果不存在，创建新实体并添加ReviveBuffComponent
        console.log('getReviveBuffEntity 不存在');
        const entityId = this.world.createEntity();
        this.world.addComponent(entityId, ReviveBuffComponent, false);
        return entityId;
    }
}