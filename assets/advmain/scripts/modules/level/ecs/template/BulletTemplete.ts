import { ProjectileComponent } from '../components/combat/ProjectileComponent';
import { RenderTemplate, RenderTemplateMustArgs, RenderTemplateOptionalArgs } from './RenderTemplate';
type ProjectileComponenMustArgs = ConstructorParameters<typeof ProjectileComponent>[0];
type ProjectileComponenOptionalArgs = ConstructorParameters<typeof ProjectileComponent>[1];
export interface BulletTempleteMustArgs extends RenderTemplateMustArgs, ProjectileComponenMustArgs {}
export interface BulletTempleteOptionalArgs extends RenderTemplateOptionalArgs, ProjectileComponenOptionalArgs {}

/** 子弹模板 */
export class BulletTemplete extends RenderTemplate<BulletTempleteMustArgs, BulletTempleteOptionalArgs> {
    getDefaultOptionalArgs(mustArgs: BulletTempleteMustArgs): BulletTempleteOptionalArgs {
        return {};
    }
    createTemplete(mustArgs: BulletTempleteMustArgs, optionArgs: BulletTempleteOptionalArgs): number {
        const entity = super.createTemplete(mustArgs, optionArgs);
        this.world.addComponent(entity, ProjectileComponent, mustArgs, optionArgs);
        return entity;
    }
}
