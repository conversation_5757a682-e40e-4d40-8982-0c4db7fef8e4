import { AIComponent } from "../components/AIComponent";
import { BehaviorConst } from "../cores/center/BehaviorCenter";
import { System } from "../cores/System";
import { ECSEvent } from "../GameEvent";
import { AIBehaviorBase } from "../behavior/ai/AIBehaviorBase";

export class AISystem extends System {
    init(): void {
        this.world.onComponentAdd(AIComponent,this.__onAIComponentAdd,this);
        this.world.onComponentRemove(AIComponent,this.__onAIComponentRemove,this);
        this.world.eventBus.on(ECSEvent.BaseEvent.SNAPSHOT_RESTORE_DATA_DONE, this.__onSnapshotDone, this);
    }
    dispose(): void {
        this.world.offComponentAdd(AIComponent,this.__onAIComponentAdd,this);
        this.world.offComponentRemove(AIComponent,this.__onAIComponentRemove,this);
        const entities = this.world.query([AIComponent]);
        for(const entityId of entities){
            this.unregisterEntityEvent(entityId);
        }
    }
    update(dt: number): void {
        for(const entityId of this.world.query([AIComponent])){//kktodo 没有实现update的ai行为可以直接就不执行。
            const aiComponent = this.world.getComponent(entityId,AIComponent);
            const aiBehavior = this.world.behaviorCenter.getBehaviorById(BehaviorConst.AI,aiComponent.aiBehaviorId);
            if(aiBehavior && aiBehavior.updateAI){
                aiBehavior.updateAI(entityId,dt);
            }
        }
    }
    /**恢复快照后重新注册事件 */
    private __onSnapshotDone(): void {
        const entities = this.world.query([AIComponent]);
        for(const entityId of entities){
            this.registerEntityEvent(entityId);
        }
    }
    /**注册事件 */
    private registerEntityEvent(entityId: number): AIBehaviorBase {
        const aiComponent = this.world.getComponent(entityId,AIComponent);
        const aiBehavior = this.world.behaviorCenter.getBehaviorById(BehaviorConst.AI,aiComponent.aiBehaviorId);
        for(const event of aiBehavior.bindEntityEvent()){
            this.world.eventBus.onEntityEvent(entityId,event,aiBehavior.receiveEntityEvent,aiBehavior);
        }
        return aiBehavior;
    }
    /**注销事件 */
    private unregisterEntityEvent(entityId: number): AIBehaviorBase {
        const aiComponent = this.world.getComponent(entityId,AIComponent);
        const aiBehavior = this.world.behaviorCenter.getBehaviorById(BehaviorConst.AI,aiComponent.aiBehaviorId);
        for(const event of aiBehavior.bindEntityEvent()){
            this.world.eventBus.offEntityEvent(entityId,event,aiBehavior.receiveEntityEvent,aiBehavior);
        }
        return aiBehavior;
    }
    /**添加AI组件 */
    private __onAIComponentAdd(entityId: number,aiComponent: AIComponent): void {
        const aiBehavior = this.registerEntityEvent(entityId);
        aiBehavior&& aiBehavior.initAI(entityId);
    }
    /**移除AI组件 */
    private __onAIComponentRemove(entityId: number,aiComponent: AIComponent): void {
        const aiBehavior = this.unregisterEntityEvent(entityId);
        aiBehavior&& aiBehavior.disposeAI(entityId);
    }
}