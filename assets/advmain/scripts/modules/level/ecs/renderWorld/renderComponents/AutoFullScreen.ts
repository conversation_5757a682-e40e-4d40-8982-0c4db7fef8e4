const { ccclass, property } = cc._decorator;

/**
 * 自动适配全屏组件
 * 将挂载的节点自动拉伸满全屏，支持横竖屏切换
 */
@ccclass
export class AutoFullScreen extends cc.Component {
    
    @property({
        tooltip: "是否在启动时自动适配全屏"
    })
    autoFitOnStart: boolean = true;
    
    @property({
        tooltip: "是否监听屏幕尺寸变化"
    })
    listenScreenResize: boolean = true;
    
    @property({
        tooltip: "是否保持宽高比"
    })
    keepAspectRatio: boolean = false;
    
    private originalSize: cc.Size = null;
    private originalPosition: cc.Vec3 = null;
    private originalAnchorPoint: cc.Vec2 = null;
    
    protected onLoad(): void {
        // 保存原始尺寸和位置
        this.saveOriginalState();
        
        if (this.autoFitOnStart) {
            this.fitToFullScreen();
        }
    }
    
    protected start(): void {
        if (this.listenScreenResize) {
            this.addScreenResizeListener();
        }
    }
    
    protected onDestroy(): void {
        this.removeScreenResizeListener();
    }
    
    /**
     * 保存节点的原始状态
     */
    private saveOriginalState(): void {
        this.originalSize = this.node.getContentSize().clone();
        this.originalPosition = this.node.position.clone();
        this.originalAnchorPoint = this.node.getAnchorPoint().clone();
    }
    
    /**
     * 适配全屏
     */
    public fitToFullScreen(): void {
        const visibleSize = cc.view.getVisibleSize();
        this.fitToFullScreenArea(visibleSize);
    }
    
    /**
     * 适配全屏区域
     */
    private fitToFullScreenArea(visibleSize: cc.Size): void {
        // 设置锚点为屏幕中心
        this.node.setAnchorPoint(cc.v2(0.5, 0.5));
        
        if (this.keepAspectRatio) {
            // 保持宽高比，以较短的边为准
            const scaleX = visibleSize.width / this.originalSize.width;
            const scaleY = visibleSize.height / this.originalSize.height;
            const scale = Math.max(scaleX, scaleY);
            
            this.node.setContentSize(this.originalSize);
            this.node.setScale(scale, scale);
        } else {
            // 直接拉伸到全屏
            this.node.setContentSize(visibleSize);
            this.node.setScale(1, 1);
        }
        
        // 设置位置为屏幕中心
        this.node.position = cc.v3(0, 0, this.node.position.z);
    }
    
    /**
     * 恢复原始状态
     */
    public restoreOriginalState(): void {
        if (this.originalSize && this.originalPosition && this.originalAnchorPoint) {
            this.node.setContentSize(this.originalSize);
            this.node.position = this.originalPosition;
            this.node.setAnchorPoint(this.originalAnchorPoint);
            this.node.setScale(1, 1);
        }
    }
    
    /**
     * 添加屏幕尺寸变化监听
     */
    private addScreenResizeListener(): void {
        cc.view.on('canvas-resize', this.onScreenResize, this);
    }
    
    /**
     * 移除屏幕尺寸变化监听
     */
    private removeScreenResizeListener(): void {
        cc.view.off('canvas-resize', this.onScreenResize, this);
    }
    
    /**
     * 屏幕尺寸变化回调
     */
    private onScreenResize(): void {
        // 延迟一帧执行，确保尺寸更新完成
        this.scheduleOnce(() => {
            this.fitToFullScreen();
        }, 0);
    }
    
    /**
     * 设置是否保持宽高比
     */
    public setKeepAspectRatio(keep: boolean): void {
        this.keepAspectRatio = keep;
        this.fitToFullScreen();
    }
    
    /**
     * 获取当前屏幕信息
     */
    public getScreenInfo(): {
        visibleSize: cc.Size;
        frameSize: cc.Size;
        designResolution: cc.Size;
    } {
        return {
            visibleSize: cc.view.getVisibleSize(),
            frameSize: cc.view.getFrameSize(),
            designResolution: cc.view.getDesignResolutionSize()
        };
    }
} 