import { ECSViewBase, IECSViewConfig } from '../../ECSViewBase';

/**特效视图-用于特效的渲染 */
export class PlaneSpineECSView extends ECSViewBase<IECSViewConfig> {
    protected initView() {
        this.node.getChildByName('gameplay_spine_plane').getComponent(sp.Skeleton).setAnimation(0, `fly_${this.data}`, false);
    }
    protected addEvents() {}
    protected removeEvents() {}
    protected disposeView() {}
}
