import { CellComponent } from '../components/board/CellComponent';
import NodeComponent from '../components/NodeComponent';
import AwardComponent from '../components/special/AwardComponent';
import TargetComponent from '../components/special/TargetComponent';
import { RelationName } from '../cores/center/EntityCenter/WorldRelation';
import { TargetType, NO_COLLECT_TWEEN } from '../define/BoardDefine';
import { AwardCondition } from '../define/EcsDefine';
import { ECSEvent } from '../GameEvent';
import { TempleteType } from '../registry/templete/TempleteRegistry';
import { ECSRuleBase, IECSRuleConfig } from './core/ECSRuleBase';

/** 块被移除规则 */
export class CellRemovedRule extends ECSRuleBase<IECSRuleConfig, ECSEvent.GameEvent.CELL_REMOVED> {
    bindEventData() {
        return ECSEvent.GameEvent.CELL_REMOVED;
    }

    canExecute(): boolean {
        // 添加您的执行条件逻辑
        return true;
    }

    execute(): void {
        const entityId = this.eventData;
        const world = this.world;
        const cell = world.getComponent(entityId, CellComponent);
        const targets = world.getComponent(world.query([TargetComponent])[0], TargetComponent).targets;
        const target = targets.find((t) => t.key === cell.type);
        if (!target) return;
        const award = world.getComponent(entityId, AwardComponent);
        if (!award) return;

        award.triggerList.forEach((trigger) => {
            if (trigger.condition !== AwardCondition.Removed) return;
            const change = trigger.change || 1;
            const current = Math.min(target.to, target.current + change);
            const targetParam = { key: cell.type, current, to: target.to };
            if (trigger.target === TargetType.Score) {
                world.eventBus.emit(ECSEvent.GameEvent.FRESH_TARGET, targetParam);
                //发送播放单次得分动画，通知单次得分规则
                const nodeComp = world.getComponent(entityId, NodeComponent);
                world.eventBus.emit(ECSEvent.GameEvent.SINGLE_SCORE_EFFECT, {
                    score: change,
                    boardEntity: world.getSources(entityId, RelationName.PARENT_CHILD)[0],
                    eliminateCount: 0,
                    position: { x: nodeComp.x, y: nodeComp.y },
                });
            } else if (trigger.target !== cell.type) return;
            else if (NO_COLLECT_TWEEN.includes(cell.type)) world.eventBus.emit(ECSEvent.GameEvent.FRESH_TARGET, targetParam);
            else {
                world.templeteCenter.createTempleteEntity(TempleteType.CollectIcon, {
                    type: cell.type,
                    startEntity: entityId,
                    parentEntity: world.ECSLayerType.EffectLayerEntity,
                    targetParam,
                });
            }
            world.eventBus.emit(ECSEvent.GameEvent.TARGET_CHANGE, { key: cell.type, change });
        });
    }
}
