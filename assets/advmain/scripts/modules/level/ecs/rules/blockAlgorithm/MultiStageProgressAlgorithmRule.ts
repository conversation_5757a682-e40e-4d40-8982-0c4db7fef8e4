import { AlgoClassTypeConst } from '../../../../algorithm/list/algoSDK/core/puzzlecore';
import ProduceBlockAlgorithmComponent from '../../components/special/ProduceBlockAlgorithmComponent';
import TargetComponent from '../../components/special/TargetComponent';
import { ECSEvent } from '../../GameEvent';
import { ECSRuleBase, IECSRuleConfig } from '../core/ECSRuleBase';

export interface IMultiStageProgressBlockAlgorithmRuleConfig extends IECSRuleConfig {
    // 对象数组 {目标进度百分比0-1之间，算法ID。0为初始阶段算法}
    algorithmIdList: { percent: number; algorithmId: AlgoClassTypeConst }[];
}
//关卡多段算法，分段比例和对应算法ID，请在RuleConfig中配置
export class MultiStageProgressBlockAlgorithmRule extends ECSRuleBase<IMultiStageProgressBlockAlgorithmRuleConfig, ECSEvent.GameEvent.PRODUCE_BLOCK> {
    canExecute(): boolean {
        return this.world.query([ProduceBlockAlgorithmComponent]).length === 0;
    }
    execute(): void {
        if (this.config.algorithmIdList.length === 0) {
            console.warn('MultiStageProgressAlgorithmRule: No algorithmIdList configured.');
            return;
        }
        // 获取当前进度百分比
        const currentProgress = this.getCurrentProgress();
        // 根据当前进度百分比选择对应的算法ID
        const algorithmId = this.config.algorithmIdList
            .filter((item) => item.percent <= currentProgress)
            .sort((a, b) => b.percent - a.percent)[0]?.algorithmId;
        const entityId = this.world.createEntity();
        this.world.addComponent(entityId, ProduceBlockAlgorithmComponent, algorithmId);
    }

    /**
     * 获得当前目标进度百分百比
     */
    private getCurrentProgress(): number {
        const targets = this.world.getComponent(this.world.query([TargetComponent])[0], TargetComponent).targets;
        const total = targets.reduce((sum, target) => sum + target.to, 0);
        const current = targets.reduce((sum, target) => sum + target.current, 0);
        return total > 0 ? current / total : 0;
    }
}
