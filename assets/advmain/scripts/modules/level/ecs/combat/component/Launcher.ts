import { Component } from "../../cores/Component";
import { IBaseActionData, IBehaviorData } from "../core/BehaviorBase";

/**
 * 发射器数据接口
 * 发射器是临时创建的，用于执行发射行为
 */
export interface LauncherData<BehaviorData extends IBehaviorData = IBehaviorData> extends IBaseActionData<BehaviorData> {
    insId: number;            // 发射器实例ID
    launcherId: string;      // 发射器配置标识
    ownerEntityId: number;   // 发射器拥有者实体ID（发射器组件所在的实体）
    casterEntityId: number;  // 创建者实体ID（触发创建发射器的实体，通常等于ownerEntityId）
    launchTime: number;      // 发射时间
}

/**
 * 发射器组件
 * 用于管理实体当前激活的发射器
 */
export class Launcher extends Component {
    static type = 'LauncherComponent';
    public launchersData: LauncherData[];
    constructor() {
        super();
        this.launchersData = [];
    }
    /**
     * 添加发射器
     * @param launcherData 发射器数据
     * @returns 是否添加成功
     */
    addLauncher(launcherData: LauncherData): boolean {
        if (!launcherData) return false;
        this.launchersData.push(launcherData);
        return true;
    }

    /**
     * 移除发射器
     * @param insId 发射器ID
     * @returns 被移除的发射器数据,如果不存在则返回undefined
     */
    removeLauncher(insId: number): LauncherData | undefined {
        const index = this.launchersData.findIndex(launcher => launcher.insId === insId);
        if (index === -1) return undefined;
        return this.launchersData.splice(index, 1)[0];
    }

    /**
     * 查找发射器
     * @param insId 发射器ID
     * @returns 发射器数据,如果不存在则返回undefined
     */
    findLauncher(insId: number): LauncherData | undefined {
        return this.launchersData.find(launcher => launcher.insId === insId);
    }
} 