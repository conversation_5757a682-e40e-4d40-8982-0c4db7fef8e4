
import { gameInfo } from '../../game/vo/GameInfo';
import { HHotUpdate } from '../../http/HHotUpdate';
import { E_Launch_Start } from '../../launch/events/E_Launch_Start';
import { getVersionConfigUrl, getVersionManifestUrl, hotUpdateStateMap } from '../config/HotUpdateConfig';
import { E_HotUpdate_State } from '../events/E_HotUpdate_State';
import { IVersionCfg } from '../interface/HotUpdateInterface';
import { HotUpdateStateType } from '../type/HotUpdateType';
import { hotUpdateInfo } from '../vo/HotUpdateInfo';
export class HotUpdate_Proxy extends falcon.Proxy {
    private hotVersion: string = '';
    private isNewUser: boolean = false;
    private isFail: boolean = false;
    protected onInit(): void {
        falcon.hotUpdate.onHotUpdateState((state: string, event: falcon.IHotUpdateEvent) => {
            this.dispatchEvent(event);
        });
    }

    registerEvents(): { new(...args: any):falcon. ModuleEvent }[] | null {
        return [E_HotUpdate_State, E_Launch_Start];
    }

    receivedEvents($event: falcon.ModuleEvent): void {
        switch ($event.getClass()) {
            case E_Launch_Start:
                this.updateUserStatus();
                this.checkUpdate();
                break;

            case E_HotUpdate_State:
                this.onHotUpdateState($event as E_HotUpdate_State);
                break;
        }
    }

    /** 热更状态监听 */
    onHotUpdateState(event: E_HotUpdate_State) {
        switch (event.state) {
            case HotUpdateStateType.START:
                this.onStart();
                break;
            case HotUpdateStateType.UPDATE_PROGRESSION:
                this.onUpdateProgression();
                break;
            case HotUpdateStateType.SUCCESS:
                this.onSuccess();
                break;
            case HotUpdateStateType.ASSET_UPDATED:
                break;
            case HotUpdateStateType.FAIL:
                this.onFail(event.event.msg);
                break;
            default:
                break;
        }
    }

    updateUserStatus() {
        this.isNewUser = gameInfo.gameEntryCount === 0;
    }

    /** 检测热更 */
    async checkUpdate() {
        if (CC_DEBUG) {
            falcon.hotUpdate.log('-------launch Start-------', gameInfo.gameEntryCount);
        }

        this.dotUpdateFlow(4);

        if (!this.isNewUser) {
            this.dotUpdateFlow(6);
        }

        if (!MACRO_HOTUPDATE_OPEN) {
            this.dispatchEvent({ eventCode: HotUpdateStateType.FAIL, msg: `热更宏定义关闭` });
            return;
        }

        if (!cc.sys.isNative) {
            this.dispatchEvent({ eventCode: HotUpdateStateType.FAIL, msg: `不是原生环境` });
            return;
        }

        //已更新后重启进入
        const hotFinishedState = hotUpdateInfo.hotFinishedState;
        if (hotFinishedState) {
            falcon.storage.setItem('hotFinishedState', false);
            this.dispatchEvent({ eventCode: HotUpdateStateType.FAIL, msg: `已经更新过` });
            return;
        }

        try {

            //获取远程版本配置
            this.promiseWithTimeout(this.startUpdate(), 6);

        } catch (err) {
            if (CC_DEBUG) {
                falcon.hotUpdate.log(`热更失败: ${err}`);
            }
            this.isFail = true;
            this.dispatchEvent({ eventCode: HotUpdateStateType.FAIL, msg: `热更失败 ${err}` });
        }
    }

    /** 开始热更 */
    async startUpdate(): Promise<void> {
        if (CC_DEBUG) {
            falcon.hotUpdate.log('startUpdate 进入热更');
        }

        //获取远程指定更新版本配置
        const remoteVersionCfgUrl = getVersionConfigUrl();
        const versionCfg = (await HHotUpdate.getRemoteVersionCfg(remoteVersionCfgUrl)) as IVersionCfg;
        this.hotVersion = versionCfg.version;

        //获取远程 versionManifest
        const versionManifestUrl = getVersionManifestUrl(versionCfg.version);
        const versionManifest = (await this.promiseWithTimeout(HHotUpdate.getRemoteVersionManifest(versionManifestUrl), 2)) as falcon.IVersionManifest;

        //读取本地projectManifest
        const projectManifest = await HHotUpdate.getProjectManifest();

        //开始热更
        falcon.hotUpdate.startUpdate(projectManifest, versionManifest);
    }

    async dispatchEvent(event: { eventCode: number; msg?: string; jsbEvent?: jsb.EventAssetsManager }) {
        if (CC_DEBUG) {
            falcon.hotUpdate.log(`派发事件 ${hotUpdateStateMap[event.eventCode]} ${event}`);
        }

        falcon.EventManager.dispatchModuleEvent(new E_HotUpdate_State(event.eventCode, event));
    }

    /**promise 超时处理 */
    promiseWithTimeout<T>(promise: Promise<T>, ms: number = 2, timeoutMessage: string = '超时'): Promise<T> {
        return new Promise((resolve, reject) => {
            let timeId = setTimeout(() => {
                timeId = null;
                reject(timeoutMessage);
            }, ms * 1000);

            const clearTime = () => {
                if (timeId) {
                    clearTimeout(timeId);
                    timeId = null;
                }
            };

            promise
                .then((v) => {
                    clearTime();
                    resolve(v);
                })
                .catch((err) => {
                    clearTime();
                    reject(err);
                });
        });
    }

    /** 开发热更 */
    onStart() { }

    /** 热更失败 */
    onFail(msg: string) {
        if (CC_DEBUG) {
            falcon.hotUpdate.log(`--onFail--', ${msg}`);
        }

        this.dotServer(0);

        if (this.isNewUser) {
            this.dotUpdateFlow(7);
        }
    }

    /** 热更成功 */
    onSuccess() {
        if (CC_DEBUG) {
            falcon.hotUpdate.log('热更成功 重新启动');
        }

        this.dotServer(1);
        this.dotUpdateFlow(17);

        if (this.isFail) {
            return;
        }

        this.dotUpdateFlow(12);
        this.dotUpdateFlow(14);

        falcon.storage.setItem('hotFinishedState', true);
        falcon.storage.setItem('hotGameVersion',this.hotVersion);

        cc.game.restart();

    }

    
    dotServer(state: -1 | 0 | 1) {
        const gameVersion = hotUpdateInfo.gameVersion;
        const newHotVersion = state === 1 ? this.hotVersion : '';
        const isNew = gameInfo.gameEntryCount === 0 ? 1 : 0;

        // DM(
        //     `usr_data_login_server_hot_result`,
        //     {
        //         state,
        //         isNew,
        //         gameVersion,
        //         newHotVersion
        //     }
        // );
    }

    dotUpdateFlow(id: 4 | 5 | 6 | 7 | 8 | 12 | 13 | 14 | 17 | 28 | 36 | 37) {
        // DM(`updateflow`, {
        //     id,
        //     planId: 4
        // })
    }

    /** 热更进度 */
    onUpdateProgression() { }
}
