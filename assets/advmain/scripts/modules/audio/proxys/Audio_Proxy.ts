
import { E_Audio_AudioSwitch } from "../events/E_Audio_AudioSwitch";
import { E_Audio_BgmSwitch } from "../events/E_Audio_BgmSwitch";
import { audioInfoData } from "../vo/AudioInfoData";

export class Audio_Proxy extends falcon.Proxy {

    registerEvents(): { new(...args: any): falcon.ModuleEvent; }[] | null {
        return [E_Audio_AudioSwitch, E_Audio_BgmSwitch];
    }

    receivedEvents($event: falcon.ModuleEvent): void {
        switch ($event.getClass()) {
            case E_Audio_AudioSwitch:
                this.setSoundSwitch();
                break;
            case E_Audio_BgmSwitch:
                this.setBgmSwitch();
                break;
        }
    }

    /**设置音效开关 */
    private setSoundSwitch() {
        const state: boolean = audioInfoData.audioSwitch;
        falcon.storage.setItem('audioSwitch', !state);
    }

    /**设置bgm开关 */
    private setBgmSwitch() {
        const state: boolean = audioInfoData.bgmSwitch;
        falcon.storage.setItem('bgmSwitch', !state);
    }
}