import { BezierPathBehavior } from '../../behavior/projectilePath/BezierPathBehavior';
import { LinearPathBehavior } from '../../behavior/projectilePath/LinearPathBehavior';
import { PausePathBehavior } from '../../behavior/projectilePath/PausePathBehavior';
import { ProjectilePathBehaviorBase } from '../../behavior/projectilePath/ProjectilePathBehaviorBase';

/**投射物路径行为配置表名 */
export const enum ProjectilePathBehaviorConst {
    /**直线路径 */
    LinearPathBehavior = 'LinearPathBehavior',
    /**暂停路径 */
    PausePathBehavior = 'PausePathBehavior',
    /**贝塞尔路径 */
    BezierPathBehavior = 'BezierPathBehavior',
}

/**投射物路径行为配置注册表 */
export const ProjectilePathBehaviorRegistry: Record<string, clzz<ProjectilePathBehaviorBase>> = {
    [ProjectilePathBehaviorConst.LinearPathBehavior]: LinearPathBehavior,
    [ProjectilePathBehaviorConst.PausePathBehavior]: PausePathBehavior,
    [ProjectilePathBehaviorConst.BezierPathBehavior]: BezierPathBehavior,
};
