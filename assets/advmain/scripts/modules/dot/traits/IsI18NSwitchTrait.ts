import { tp } from "../../traits/typePredicate/TraitTypePredicate";

export type IIsI18NSwitchTraitProps = Feature['isI18NSwitch'][number]['param'];

@classId('IsI18NSwitchTrait')
export class IsI18NSwitchTrait extends falcon.Trait<IIsI18NSwitchTraitProps> {

    async onActive<T extends ITraitTarget>(target: T) {
        if (tp.isDotInfoDS(target)) {
            //这里需要处理语言
            target.data.language = "english";
        }
    }
}