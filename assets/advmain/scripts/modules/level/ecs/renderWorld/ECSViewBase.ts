import { AudioId } from '../config/conf/EcsAudioConfig';
import { RenderWorld } from '../cores/RenderWorld';
import { ReadonlyWorld, World } from '../cores/World';
export type EcsViewId = string;
export interface IECSViewConfig {
    /**视图Id */
    readonly ecsViewId: EcsViewId;
    /**资源包名 */
    readonly prefabBundleName: string;
    /**资源路径 */
    readonly prefabUrl: string;
    /**视图名 */
    readonly ecsViewName: string;
    /**视图层级 */
    readonly layer?: number;
    /** 音效ID */
    readonly audioId?: AudioId | string;
    /**蒙版配置 */
    readonly mask?: {
        /**蒙版prefab路径 */
        prefabUrl: string;
        /**蒙版资源包名 */
        prefabBundleName?: string;
        /**透明度 0-1 */
        opacity?: number;
    };
}

/**
 * 视图基类-渲染世界中用于渲染的视图
 * @param TConfig 视图配置
 */
export abstract class ECSViewBase<TConfig extends IECSViewConfig = IECSViewConfig> extends cc.Component {
    /**配置 */
    public config: TConfig;
    /**逻辑实体Id */
    public logicEntityId: number;
    /**数据*/
    public data: any;
    /**渲染世界实例 */
    protected renderWorld: RenderWorld;
    /**逻辑世界实例 */
    protected logicWorld: ReadonlyWorld;
    /**是否已添加事件*/
    private _eventsInited: boolean;
    /** 初始化视图 - 只会调用一次*/
    protected abstract initView();
    /** 初始化事件 */
    protected abstract addEvents();
    /** 移除事件 */
    protected abstract removeEvents();
    /** 销毁视图 */
    protected abstract disposeView();
    /** 是否初始化完成 */
    protected initComplete: boolean;
    protected async start() {
        await this.initView();
        this.initComplete = true;
        if (this.enabled && !this._eventsInited) {
            this.addEvents();
            this._eventsInited = true;
        }
        this.playDefaultAudio();
    }
    private playDefaultAudio(): void {
        if (this.config.audioId) {
            this.renderWorld.playAudio(this.config.audioId);
        }
    }
    public init<T>(logicWorld: ReadonlyWorld, renderWorld: RenderWorld, logicEntityId: number, config: TConfig, data?: T) {
        this.logicWorld = logicWorld as unknown as ReadonlyWorld;
        this.renderWorld = renderWorld;
        this.logicEntityId = logicEntityId;
        this.config = config || ({} as TConfig);
        this.data = data;
    }

    public dispose(): void {
        if (this._eventsInited) {
            this.removeEvents();
            this._eventsInited = false;
        }
        this.unscheduleAllCallbacks();
        this.node.stopAllActions();
        cc.Tween.stopAllByTarget(this);
        this.disposeView();
    }

    /**
     * 开关组件及节点显示
     * @param active true=启用并显示，false=禁用并隐藏
     */
    public setActive(active: boolean): void {
        this.enabled = active; // 触发 onEnable/onDisable
        this.node.active = active; // 控制可见性
    }
    /**是否独立视图,不受逻辑世界NodeComponent影响 */
    public isIndependent(): boolean {
        return false;
    }
    /**快速获取节点 */
    get ui() {
        if (!this._ui) {
            this._ui = {};
            this.loopDo(this.node, (n) => (this._ui[n.name] = n));
        }
        return this._ui;
    }
    private _ui: { [k: string]: cc.Node };

    /**深度处理节点 */
    public loopDo(node: cc.Node, callback: (n: cc.Node) => void) {
        callback(node);
        for (const n of node.children) this.loopDo(n, callback);
    }
}
