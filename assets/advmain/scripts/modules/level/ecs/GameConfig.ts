import { BaseConfig10000 } from './config/levelConfig/BaseConfig10000';
import { BaseConfig10001 } from './config/levelConfig/BaseConfig10001';
import { AIBehaviorConfig } from './config/conf/AIBehaviorConfig';
import { EcsViewConfig } from './config/conf/EcsViewConfig';
import { ProjectileConfig } from './config/conf/ProjectileBehaviorConfig';
import { RuleConfig } from './config/conf/RuleConfig';
import { SystemConfig } from './config/conf/SystemConfig';
import { LevelConfig1 } from './config/levelConfig/LevelConfig1';
import { LevelConfig2 } from './config/levelConfig/LevelConfig2';
import { LevelConfig3 } from './config/levelConfig/LevelConfig3';
import { LevelConfig4 } from './config/levelConfig/LevelConfig4';
import { LevelConfig5 } from './config/levelConfig/LevelConfig5';
import { LevelConfig6 } from './config/levelConfig/LevelConfig6';
import { ConfigRegistry } from './registry/ConfigRegistry';
import { LevelConfig8 } from './config/levelConfig/LevelConfig8';
import { BuffBehaviorConfig } from './config/conf/BuffBehaviorConfig';
import { BoardConfig } from './config/conf/BoardConfig';
import { LowPerformanceResourceConfig } from './config/conf/LowPerformanceResourceConfig';

export const GameConfig: ConfigRegistry = {
    EcsViewConfig: EcsViewConfig,
    SystemConfig: SystemConfig,
    RuleConfig: RuleConfig,
    AIBehaviorConfig: AIBehaviorConfig,
    ProjectileBehaviorConfig: ProjectileConfig,
    BuffBehaviorConfig: BuffBehaviorConfig,
    BoardConfig: BoardConfig,
    LevelConfig: {
        BaseConfig10000: BaseConfig10000,
        BaseConfig10001: BaseConfig10001,
    },
    OverrideLevelConfig: {
        LevelConfig1: LevelConfig1,
        LevelConfig2: LevelConfig2,
        LevelConfig3: LevelConfig3,
        LevelConfig4: LevelConfig4,
        LevelConfig5: LevelConfig5,
        LevelConfig6: LevelConfig6,
        LevelConfig8: LevelConfig8,
    },
    LowPerformanceResourceConfig: LowPerformanceResourceConfig,
};
