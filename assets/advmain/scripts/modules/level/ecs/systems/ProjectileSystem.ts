import NodeComponent from '../components/NodeComponent';
import { System } from '../cores/System';
import { ProjectileComponent } from '../components/combat/ProjectileComponent';
import { BehaviorConst } from '../cores/center/BehaviorCenter';
import { IPoint } from '../define/EcsDefine';
/**
 * 投射物系统
 * 负责更新投射物状态和处理组件清理
 */
export class ProjectileSystem extends System {
    /**销毁缓存 */
    private readonly destroyCache: Array<{ entityId: number; projectile: ProjectileComponent }> = [];
    /**开始位置缓存 */
    private readonly startPosCache: IPoint = { x: 0, y: 0 };
    init(): void {
        this.world.onComponentAdd(ProjectileComponent, this.onProjectileComponentAdd, this);
        this.world.onComponentRemove(ProjectileComponent, this.onProjectileComponentRemove, this);
    }

    dispose(): void {
        this.world.offComponentAdd(ProjectileComponent, this.onProjectileComponentAdd, this);
        this.world.offComponentRemove(ProjectileComponent, this.onProjectileComponentRemove, this);
    }

    /**
     * 处理投射物组件添加
     * 确保所有投射物资源被正确初始化
     */
    private onProjectileComponentAdd(entityId: number, component: ProjectileComponent): void {
        const behavior = this.world.behaviorCenter.getBehaviorById(BehaviorConst.Projectile, component.projectileBehaviorId);
        component.createTime = this.world.worldTime;
        const nodeComp = this.world.getComponent(entityId, NodeComponent);
        component.startPosition = { x: nodeComp.x, y: nodeComp.y };
        behavior.onCreate(entityId, component);
    }

    /**
     * 处理投射物组件移除
     * 确保所有投射物资源被正确清理
     */
    private onProjectileComponentRemove(entityId: number, component: ProjectileComponent): void {
        const behavior = this.world.behaviorCenter.getBehaviorById(BehaviorConst.Projectile, component.projectileBehaviorId);
        behavior.onDestroy(entityId, component);
    }

    /**
     * 更新系统
     * @param deltaTime 时间增量
     */
    update(deltaTime: number): void {
        // 获取所有拥有投射物组件和位置组件的实体
        const entities = this.world.query([ProjectileComponent, NodeComponent]);

        if (entities.length === 0) return;

        const behaviorCenter = this.world.behaviorCenter;
        const worldTime = this.world.worldTime;
        const startPosCache = this.startPosCache;
        const destroyCache = this.destroyCache;
        // 清空销毁缓存
        destroyCache.length = 0;

        // 处理所有活跃的投射物组件
        for (let i = 0; i < entities.length; i++) {
            const entityId = entities[i];
            const projectileComponent = this.world.getComponent<ProjectileComponent>(entityId, ProjectileComponent);

            // 获取投射物行为和配置
            const behavior = behaviorCenter.getBehaviorById(BehaviorConst.Projectile, projectileComponent.projectileBehaviorId);
            if (!behavior) {
                console.error(`[ProjectileSystem] 投射物 ${projectileComponent.projectileBehaviorId} 缺少行为`);
                destroyCache.push({ entityId, projectile: projectileComponent });
                continue;
            }

            const nodeComp = this.world.getComponent(entityId, NodeComponent);
            startPosCache.x = nodeComp.x;
            startPosCache.y = nodeComp.y;
            // 执行移动逻辑，返回是否继续存活
            const shouldContinue = behavior.onMove(entityId, projectileComponent, worldTime, deltaTime);
            if (startPosCache.x !== nodeComp.x || startPosCache.y !== nodeComp.y) {
                nodeComp.angle = this.calculateAngle(startPosCache, { x: nodeComp.x, y: nodeComp.y });
            }
            if (!shouldContinue) {
                destroyCache.push({ entityId, projectile: projectileComponent });
                continue;
            }
        }

        // 统一销毁所有需要销毁的投射物
        for (let i = 0; i < this.destroyCache.length; i++) {
            const destroyInfo = this.destroyCache[i];
            const entityId = destroyInfo.entityId;
            const projectile = destroyInfo.projectile;

            console.log(`[ProjectileSystem] 投射物完成: ${projectile.projectileBehaviorId}`);

            // 获取行为来决定处理方式
            const behavior = behaviorCenter.getBehaviorById(BehaviorConst.Projectile, projectile.projectileBehaviorId);

            if (behavior.dontDestroyEntity(entityId, projectile)) {
                console.log(`[ProjectileSystem] 仅移除投射物组件，保留实体 ${entityId}`);
                // 只移除投射物组件
                const projectileComponent = this.world.getComponent<ProjectileComponent>(entityId, ProjectileComponent);
                if (projectileComponent) {
                    this.world.removeComponent(entityId, projectileComponent);
                }
            } else {
                console.log(`[ProjectileSystem] 销毁实体 ${entityId}`);
                // 销毁实体（会自动触发组件移除监听器）
                this.world.removeEntity(entityId);
            }
        }
    }

    private calculateAngle(pointStart: IPoint, pointEned: IPoint) {
        // 使用 Math.atan2 函数计算弧度值
        const radian = Math.atan2(pointEned.x - pointStart.x, pointEned.y - pointStart.y);
        // 将弧度值转换为角度值
        const degree = radian * (180 / Math.PI);
        // 转换到 [0, 360) 范围内
        return (-degree + 360) % 360;
    }
}
