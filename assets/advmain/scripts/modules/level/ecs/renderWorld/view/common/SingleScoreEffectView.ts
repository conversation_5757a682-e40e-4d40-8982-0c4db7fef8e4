import TargetComponent from '../../../components/special/TargetComponent';
import { TargetType } from '../../../define/BoardDefine';
import { ECSViewBase } from '../../ECSViewBase';

const { ccclass, property } = cc._decorator;

/**单次分数展示特效视图 */
export class SingleScoreEffectView extends ECSViewBase {
    protected async initView() {
        if (this.data && this.data.score) {
            // 设置背景
            const bg = this.node.getChildByName('bg').getComponent(cc.Sprite);
            const bgUrl = 'textures/comboScoreTip/ComboScoreTipBg1';
            if (bg) {
                this.renderWorld.loadRes(this.config.prefabBundleName, bgUrl, cc.SpriteFrame).then((spriteFrame: cc.SpriteFrame) => {
                    bg.spriteFrame = spriteFrame;
                });
            }

            // 设置分数标签
            const scoreLabel = this.node.getChildByName('score_label').getComponent(cc.Label);
            if (scoreLabel) {
                const eliminateCount = this.data.eliminateCount;
                const fontUrl = eliminateCount > 1 ? 'fonts/comboScoreTip/scoreTip1' : 'fonts/comboScoreTip/scoreTip2';
                const spacingX = eliminateCount > 1 ? -55 : -5;
                this.renderWorld.loadRes(this.config.prefabBundleName, fontUrl, cc.Font).then((font: cc.Font) => {
                    scoreLabel.spacingX = spacingX;
                    scoreLabel.fontSize = 50;
                    scoreLabel.font = font;
                    scoreLabel.string = this.data.score.toString();
                    this.node.opacity = 0;
                    this.node.scale = 0;
                    const speedRate = 1;
                    const maxScale = 1.0;
                    if (eliminateCount > 1) {
                        cc.tween(this.node)
                            .delay(0.8)
                            .call(() => {
                                this.node.opacity = 255;
                            })
                            .to(0.17 * speedRate, { scale: 1.3 * maxScale })
                            .to(0.08 * speedRate, { scale: maxScale })
                            .to(0.25 * speedRate, { scale: maxScale })
                            .call(() => {
                                this.moveToTargetPosition();
                            })
                            .start();
                    } else {
                        cc.tween(this.node)
                            .delay(0.8)
                            .to(0.12 * speedRate, { opacity: 255, scale: maxScale })
                            .delay(0.3)
                            .call(() => {
                                this.moveToTargetPosition();
                            })
                            .start();
                    }
                });
            }
        }
    }
    protected addEvents() {}
    protected removeEvents() {}
    protected disposeView() {}

    /**
     * 移动到目标位置
     */
    protected moveToTargetPosition() {
        const targetEntity = this.logicWorld.query([TargetComponent])[0];
        const targets = this.logicWorld.getComponent(targetEntity, TargetComponent).targets;
        // 检查是否有得分目标
        const target = targets.find((t) => t.key === TargetType.Score);
        if (!target) {
            this.renderWorld.destroyECSView(this);
            return;
        }
        let targetPos = this.renderWorld.getRenderEntityWorldPosition(targetEntity, `targets/target${TargetType.Score}/targetLab`);
        targetPos = this.node.parent.convertToNodeSpaceAR(cc.v3(targetPos.x, targetPos.y, 0));
        const tempY = targetPos.y - this.node.position.y;
        cc.tween(this.node)
            .bezierTo(0.27, this.node.getPosition(), cc.v2(this.node.x + tempY / 2, this.node.y - tempY), cc.v2(targetPos))
            .call(() => {
                this.renderWorld.destroyECSView(this);
            })
            .start();
    }
}
