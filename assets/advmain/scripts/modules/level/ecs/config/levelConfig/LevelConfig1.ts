import { IOverrideLevelConfig } from '../../cores/OverrideConfigManager';
import { IInitBoardRuleConfig } from '../../rules/InitBoardRule';
import { IInitTargetRuleConfig } from '../../rules/InitTargetRule';
import { IExpandHotAreaRuleConfig } from '../../rules/ExpandHotAreaRule';
import { IConfigECSRuleConfig } from '../../rules/ConfigECSRule';

export const LevelConfig1: IOverrideLevelConfig = {
    baseLevelId: 'BaseConfig10000',
    levelId: 'LevelConfig1',
    overrideTableArgs: {
        RuleConfig: {
            rule_init_target:{
                target: [[1, 500]],
            } as IInitTargetRuleConfig,
            rule_init_board: {
                boardIdList: ["classic"],
            } as IInitBoardRuleConfig,
            rule_drag_multiplier: {
                configVal: 1,
            } as IConfigECSRuleConfig,
            rule_expand_hot_area: {
                expandMultiplier: 2.0, // 热区扩大2倍
            } as IExpandHotAreaRuleConfig,
        },
    },
    overrideLevelConfig: {
        
    },
    /** 关闭系统 */
    closeSystems: [],
    /** 关闭规则 */
    closeRules: [],
    /** 插入系统 */
    insertSystems: [],
    /** 插入规则 */
    insertRules: [],
    /** 替换系统 */
    replaceSystems: [],
    /** 替换规则 */
    replaceRules: [],
};
