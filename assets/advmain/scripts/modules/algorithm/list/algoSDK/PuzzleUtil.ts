

// import { OFFER_ALGORITHM_SDK_TYPE } from "../../config/AlgorithmSDKMatchConfig";
import { AlgoClassTypeConst } from "./core/puzzlecore";
import { AlgoExecuteAdapterInputData, AlgoExecuteAdapterOutputData, algoPuzzle, PuzzleTypeConst } from "./puzzle/algoPuzzle";
import { PuzzleCfg } from "./PuzzleCfg";

export interface InitArgsDef {
    /** 块池权重表*/
    ratioJson: any;
    /** 随机种子*/
    seed: number;
}

/**拼图工具 */
export class PuzzleUtil {

    public static init(data: InitArgsDef) {
        const algoTypes: number[] = [
            10001, 10002, 10003, 11001, 12001, 12002, 14001, 14002, 14003, 14004,
            14005, 14006, 14007, 14008, 14009, 14010, 14011, 16001, 16002, 16003,
            17001, 18001, 20001, 21001, 22001, 23001
        ];
        for (const algoType of algoTypes) {
            algoPuzzle.initPuzzlePieceDynamic(PuzzleTypeConst.AlgoExecute, '' + algoType, PuzzleCfg.getConfig(algoType));
        }
        algoPuzzle.initStaticData(data);
    }

    public static dispose() {
        // 释放算法配置
    }

    private static doFeature(args, algoList) {
        if (args.extra && args.extra.feature) {
            const {feature} = args.extra;
            if (feature.randomToRandomNoDie) {
                for (const i in algoList) {
                    if (algoList[i] == AlgoClassTypeConst.algo_random) {
                        algoList[i] = AlgoClassTypeConst.algo_random_no_death;
                    }
                }
            }
            if (feature.lianGuan) {
                for (const i in algoList) {
                    if (algoList[i] == AlgoClassTypeConst.algo_fill_in_the_blank) {
                        algoList[i] = AlgoClassTypeConst.algo_fill_in_the_blank_by_pos;
                    } else if (algoList[i] == AlgoClassTypeConst.algo_fill_in_the_blank_clear) {
                        algoList[i] = AlgoClassTypeConst.algo_fill_in_the_blank_clear_by_pos;
                    } else if (algoList[i] == AlgoClassTypeConst.algo_fill_in_the_blank_journey) {
                        algoList[i] = AlgoClassTypeConst.algo_fill_in_the_blank_journey_by_pos;
                    }
                }
            }
            if (feature.limitSmall) {

            }
        }
    }


    public static async offerTopic(args: AlgoExecuteAdapterInputData, algoList) {
        // algoList = OFFER_ALGORITHM_SDK_TYPE[algoList];
        if (!Array.isArray(algoList)) {
            algoList = [algoList];
        }
        PuzzleUtil.doFeature(args, algoList);
        let result: AlgoExecuteAdapterOutputData;
        for (const algoType of algoList) {
            result = await algoPuzzle.runPuzzlePiece(PuzzleTypeConst.AlgoExecute, '' + algoType, args);
            result.blockIds = result.blockIds.filter(item => item !== 0);
            if (result.blockIds.length >= 3) {
                break;
            }
        }
        result.algoList = algoList;
        return result;
    }
}