import { Component } from '../../cores/Component';

export default class BoardComponent extends Component {
    constructor(
        /**行数 */
        public rCount: number,
        /**列数 */
        public cCount: number,
        /**棋盘名称 */
        public boardId: string,
        /**可放置 */
        public isCanPut: boolean = true,
        /**连击数 */
        public comboCount: number = 0,
        /**空放次数 */
        public emptyCount: number = 0,
        /**纯色色值 */
        public boardCellColor: number = 0,
        /**是否显示棋盘背层 */
        public isShowBoardBg: boolean = true,

        public isShowFullEffect: boolean = true,
    ) {
        super();
    }
}
