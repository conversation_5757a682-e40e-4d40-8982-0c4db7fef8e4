import { Loader } from '../../../../../../advmain/scripts/base/Loader';
import { SubGameBridge } from '../../../../../../advmain/scripts/base/SubGameBridge';
import { uiLayer } from '../../../../../../advmain/scripts/modules/layer/vo/LayerInfo';
import TargetComponent from '../../../../../../advmain/scripts/modules/level/ecs/components/special/TargetComponent';
import { ChapterPrefabConfig } from '../../prefab/ChapterPrefabConfig';
import ChapterResult from '../components/ChapterResult';
import { E_ChapterWin_Show } from '../events/E_ChapterWin_Show';

@classId('ChapterGameOver_Win_Proxy')
export class ChapterGameOver_Win_Proxy extends falcon.Proxy {
    registerEvents(): { new (...args: any): falcon.ModuleEvent }[] | null {
        return [E_ChapterWin_Show];
    }

    receivedEvents($event: falcon.ModuleEvent): void {
        switch ($event.getClass()) {
            case E_ChapterWin_Show: // 打开界面
                this.openUI($event as E_ChapterWin_Show);
                break;
        }
    }

    /**显示结算界面 */
    async openUI(event: E_ChapterWin_Show) {
        await Loader.showUI(ChapterPrefabConfig.ChapterResult, uiLayer);
        //临时写法
        const targets = window['gameIns']._world.getComponent(window['gameIns']._world.query([TargetComponent])[0], TargetComponent).targets;
        Cinst(ChapterResult).setState({ targets, win: true });
    }
}
