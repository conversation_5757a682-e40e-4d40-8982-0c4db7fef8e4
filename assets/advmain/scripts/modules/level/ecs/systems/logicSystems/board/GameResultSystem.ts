import { System } from '../../../cores/System';
import { ECSEvent } from '../../../GameEvent';
import { game } from '../../../../Game';

export class GameResultSystem extends System {
    init(): void {
        this.world.eventBus.on(ECSEvent.GameEvent.GAME_NEXT, this.onGameNext, this);
        this.world.eventBus.on(ECSEvent.GameEvent.GAME_WIN, this.onGameWin, this)
        this.world.eventBus.on(ECSEvent.GameEvent.GAME_RETRY, this.onGameRetry, this);
    }

    dispose(): void {
        this.world.eventBus.off(ECSEvent.GameEvent.GAME_NEXT, this.onGameNext, this);
        this.world.eventBus.off(ECSEvent.GameEvent.GAME_WIN, this.onGameWin, this);
        this.world.eventBus.off(ECSEvent.GameEvent.GAME_RETRY, this.onGameRetry, this);
    }
    
    /** 游戏下一关 */
    private onGameNext() {
        // console.log('yjf__游戏下一关');
        this.world.eventBus.emit(ECSEvent.BaseEvent.GAME_RESTART);
    }

    /** 游戏重试 */
    private onGameRetry() {
        // console.log('yjf__游戏重试');
        this.world.eventBus.emit(ECSEvent.BaseEvent.GAME_RESTART);
    }

    /**
     * 游戏胜利
     */
    private onGameWin() {
        const curChapter = falcon.storage.getItem('chapterNum', 0) + 1;
        falcon.storage.setItem('chapterNum', curChapter);
        game.curLevelId = curChapter + 1;
        // 通关逻辑待补充
    }

    update(): void {}
}
