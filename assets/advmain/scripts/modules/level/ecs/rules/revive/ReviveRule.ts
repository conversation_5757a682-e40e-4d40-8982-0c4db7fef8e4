import { NativePermissions } from '../../../../native/NativePermissions';
import ReviveComponent from '../../components/ReviveComponent';
import TargetComponent from '../../components/special/TargetComponent';
import { TempleteType } from '../../registry/templete/TempleteRegistry';
import { EcsViewId } from '../../renderWorld/ECSViewBase';
import { ECSExecuteContext, ECSRuleBase, IECSRuleConfig } from '../core/ECSRuleBase';

export interface IReviveRuleConfig extends IECSRuleConfig {
    /**复活视图id */
    ecsViewId: EcsViewId;
    /**满足？收集进度弹出复活 */
    percent: number;
    /**是否需要加载完 */
    needLoad: boolean;
}

/**
 * 复活规则
 */
export class ReviveRule extends ECSRuleBase<IReviveRuleConfig> {
    canExecute(): boolean {
        // 未加载完全则直接失败
        if (this.config.needLoad) {
            if (!NativePermissions.getReadyByAdType('reward')) {
                return false;
            }
        }
        // 复活次数判断
        const world = this.world;
        const sceneEntity = world.utilCenter.boardUtil.getSceneEntity();
        const reviveComp = world.getComponent(sceneEntity, ReviveComponent);
        if (reviveComp.nCount <= 0) {
            return false;
        }
        // 是否满足配置的进度要求
        const percentLimit = this.config.percent;
        const targets = this.world.getComponent(this.world.query([TargetComponent])[0], TargetComponent).targets;
        const total = targets.reduce((acc, target) => acc + target.to, 0);
        const current = targets.reduce((acc, target) => acc + target.current, 0);
        const percent = total > 0 ? current / total : 0;
        console.log('yjf__percent', percent, percentLimit);
        if (percent < percentLimit) {
            return false;
        }
        // 广告条件满足
        return true;
    }

    execute(): void {
        const world = this.world;
        const sceneEntity = world.utilCenter.boardUtil.getSceneEntity();
        const reviveComp = world.getComponent(sceneEntity, ReviveComponent);
        reviveComp.nCount--;
        const effectEntity = world.templeteCenter.createTempleteEntity(
            TempleteType.Render,
            {
                parentEntity: world.ECSLayerType.UILayerEntity,
                ecsViewId: this.config.ecsViewId,
            },
            {
                renderParam: { isBreakLink: true },
            },
        );
        world.destroyEntity(effectEntity);
    }
}
