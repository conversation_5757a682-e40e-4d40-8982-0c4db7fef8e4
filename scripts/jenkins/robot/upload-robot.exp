#!/usr/bin/expect
set timeout 10000

set current_dir [pwd]
set version [exec echo $env(MACRO_VERSION)]
set platform [exec echo $env(MACRO_PLATFORM)]
set curEnv [exec echo $env(MACRO_ENV)]

set local_dir "${current_dir}/build/web-mobile"
set local_dir_zip "${current_dir}/build/web-mobile.zip"

# 压缩 local_dir 目录到 local_dir_zip 文件
puts "开始压缩目录: $local_dir -> $local_dir_zip"
# 检查源目录是否存在
if {![file exists $local_dir]} {
puts "错误：源目录不存在: $local_dir"
exit 1
}

# 删除已存在的压缩文件
if {[file exists $local_dir_zip]} {
puts "删除已存在的压缩文件: $local_dir_zip"
file delete $local_dir_zip
}

# 使用 bash -c 确保压缩命令在正确的上下文中执行
if {[catch {exec bash -c "cd '${current_dir}/build/web-mobile' && zip -r ../web-mobile.zip ."} result]} {
puts "压缩失败: $result"
exit 1
} else {
puts "压缩成功"
}

# 验证压缩文件是否创建成功
if {[file exists $local_dir_zip]} {
set file_size [file size $local_dir_zip]
puts "压缩文件创建成功: $local_dir_zip"
puts "文件大小: $file_size bytes"
} else {
puts "错误：压缩文件未创建成功"
exit 1
}
puts "压缩完成"

# 上传到服务器：***********
# http://***********:8082/refactored_games/webgames/web/test/1.0.0/?task_id=12685&execute_sequence=1462771&query_addr=http://************:7500/query_batch&dev_bot_addr=ws://***************:11001&dev_execute_count=10&chapter=class
set remote_dir "/data/dev/baowenxue/blockblast_robot_h5/refactored_games/webgames/${platform}/${curEnv}/${version}"
set zip_file "/data/dev/baowenxue/blockblast_robot_h5/refactored_games/webgames/${platform}/${curEnv}/${version}/web-mobile.zip"
set unzip_dir "/data/dev/baowenxue/blockblast_robot_h5/refactored_games/webgames/${platform}/${curEnv}/${version}"

set server_ip "***********"
set username "zhangzhiqiang2403"
set password "LJ5S2kEY1oCUOonmx4p8IQ"

spawn ssh $username@$server_ip
expect "*assword:"
send "$password\r"
expect "$ "
send "mkdir -p $remote_dir\r"
expect "$ "
send "exit\r"
expect eof

spawn scp $local_dir_zip $username@$server_ip:$remote_dir
expect "*assword:"
send "$password\r"
expect eof

spawn ssh $username@$server_ip "unzip -o $zip_file -d $unzip_dir"
expect "*assword:"
send "$password\r"
expect eof

spawn ssh $username@$server_ip "rm -rf $zip_file"
expect "*assword:"
send "$password\r"
expect eof

# 上传到服务器：**************
# http://**************:8867/robot/web/test/1.0.0/?task_id=12685&execute_sequence=1462771&query_addr=http://************:7500/query_batch&dev_bot_addr=ws://***************:11001&dev_execute_count=10&chapter=class
set remote_dir "/Users/<USER>/blockblast-adv/robot/${platform}/${curEnv}/${version}"
set zip_file "/Users/<USER>/blockblast-adv/robot/${platform}/${curEnv}/${version}/web-mobile.zip"
set unzip_dir "/Users/<USER>/blockblast-adv/robot/${platform}/${curEnv}/${version}"

set server_ip "**************"
set username "hungrystudio"
set password "Youxi123"

spawn ssh $username@$server_ip
expect "*assword:"
send "$password\r"
expect "% "
send "mkdir -p $remote_dir\r"
expect "% "
send "exit\r"
expect eof

spawn scp $local_dir_zip $username@$server_ip:$remote_dir
expect "*assword:"
send "$password\r"
expect eof

spawn ssh $username@$server_ip "unzip -o $zip_file -d $unzip_dir"
expect "*assword:"
send "$password\r"
expect eof

spawn ssh $username@$server_ip "rm -rf $zip_file"
expect "*assword:"
send "$password\r"
expect eof
