{"ver": "1.0.27", "uuid": "eb510042-02be-4eb4-a250-de9fedd7a81f", "importer": "effect", "compiledShaders": [{"glsl1": {"vert": "\nprecision highp float;\nuniform mat4 cc_matViewProj;\nuniform mat4 cc_matWorld;\nattribute vec3 a_position;\nattribute vec4 a_color;\nvarying vec4 v_color;\n#if USE_TEXTURE\nattribute vec2 a_uv0;\nvarying vec2 v_uv0;\n#endif\nvoid main () {\n  vec4 pos = vec4(a_position, 1);\n  #if CC_USE_MODEL\n  pos = cc_matViewProj * cc_matWorld * pos;\n  #else\n  pos = cc_matViewProj * pos;\n  #endif\n  #if USE_TEXTURE\n  v_uv0 = a_uv0;\n  #endif\n  v_color = a_color;\n  gl_Position = pos;\n}", "frag": "\nprecision highp float;\n#if USE_ALPHA_TEST\n  uniform float alphaThreshold;\n#endif\nvoid ALPHA_TEST (in vec4 color) {\n  #if USE_ALPHA_TEST\n      if (color.a < alphaThreshold) discard;\n  #endif\n}\nvoid ALPHA_TEST (in float alpha) {\n  #if USE_ALPHA_TEST\n      if (alpha < alphaThreshold) discard;\n  #endif\n}\nvarying vec4 v_color;\n#if USE_TEXTURE\nvarying vec2 v_uv0;\nuniform sampler2D texture;\n#endif\nuniform float angle;\nuniform float offset;\nuniform float uvRatio;\nuniform vec4 beginColor;\nuniform vec4 middleColor;\nvoid main () {\n  vec4 o = vec4(1, 1, 1, 1);\n  #if USE_TEXTURE\n  vec4 texture_tmp = texture2D(texture, v_uv0);\n  #if CC_USE_ALPHA_ATLAS_texture\n      texture_tmp.a *= texture2D(texture, v_uv0 + vec2(0, 0.5)).r;\n  #endif\n  #if INPUT_IS_GAMMA\n    o.rgb *= (texture_tmp.rgb * texture_tmp.rgb);\n    o.a *= texture_tmp.a;\n  #else\n    o *= texture_tmp;\n  #endif\n  #endif\n  o *= v_color;\n  ALPHA_TEST(o);\n  float angleInRadians = radians(angle);\n  float ratio = clamp((v_uv0.y * cos(angleInRadians) + v_uv0.x * sin(angleInRadians) + offset) * uvRatio, 0.0, 1.0);\n  float beginRatio = 1.0 - ratio;\n  float endRatio = ratio;\n  if(beginRatio > 0.5) {\n    gl_FragColor = vec4(\n          o.r * (beginColor.r * beginRatio + middleColor.r * endRatio),\n          o.g * (beginColor.g * beginRatio + middleColor.g * endRatio),\n          o.b * (beginColor.b * beginRatio + middleColor.b * endRatio),\n          o.a * (beginColor.a * beginRatio + middleColor.a * endRatio)\n        );\n  }else {\n    gl_FragColor = vec4(\n          o.r * (middleColor.r * beginRatio + beginColor.r * endRatio),\n          o.g * (middleColor.g * beginRatio + beginColor.g * endRatio),\n          o.b * (middleColor.b * beginRatio + beginColor.b * endRatio),\n          o.a * (middleColor.a * beginRatio + beginColor.a * endRatio)\n        );\n  }\n}"}, "glsl3": {"vert": "\nprecision highp float;\nuniform CCGlobal {\n  mat4 cc_matView;\n  mat4 cc_matViewInv;\n  mat4 cc_matProj;\n  mat4 cc_matProjInv;\n  mat4 cc_matViewProj;\n  mat4 cc_matViewProjInv;\n  vec4 cc_cameraPos;\n  vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_screenScale;\n};\nuniform CCLocal {\n  mat4 cc_matWorld;\n  mat4 cc_matWorldIT;\n};\nin vec3 a_position;\nin vec4 a_color;\nout vec4 v_color;\n#if USE_TEXTURE\nin vec2 a_uv0;\nout vec2 v_uv0;\n#endif\nvoid main () {\n  vec4 pos = vec4(a_position, 1);\n  #if CC_USE_MODEL\n  pos = cc_matViewProj * cc_matWorld * pos;\n  #else\n  pos = cc_matViewProj * pos;\n  #endif\n  #if USE_TEXTURE\n  v_uv0 = a_uv0;\n  #endif\n  v_color = a_color;\n  gl_Position = pos;\n}", "frag": "\nprecision highp float;\n#if USE_ALPHA_TEST\n  uniform ALPHA_TEST {\n    float alphaThreshold;\n  };\n#endif\nvoid ALPHA_TEST (in vec4 color) {\n  #if USE_ALPHA_TEST\n      if (color.a < alphaThreshold) discard;\n  #endif\n}\nvoid ALPHA_TEST (in float alpha) {\n  #if USE_ALPHA_TEST\n      if (alpha < alphaThreshold) discard;\n  #endif\n}\nin vec4 v_color;\n#if USE_TEXTURE\nin vec2 v_uv0;\nuniform sampler2D texture;\n#endif\nuniform Factor {\n    float angle;\n    float offset;\n    float uvRatio;\n};\nuniform Constant {\n    vec4 beginColor;\n    vec4 middleColor;\n};\nvoid main () {\n  vec4 o = vec4(1, 1, 1, 1);\n  #if USE_TEXTURE\n  vec4 texture_tmp = texture(texture, v_uv0);\n  #if CC_USE_ALPHA_ATLAS_texture\n      texture_tmp.a *= texture(texture, v_uv0 + vec2(0, 0.5)).r;\n  #endif\n  #if INPUT_IS_GAMMA\n    o.rgb *= (texture_tmp.rgb * texture_tmp.rgb);\n    o.a *= texture_tmp.a;\n  #else\n    o *= texture_tmp;\n  #endif\n  #endif\n  o *= v_color;\n  ALPHA_TEST(o);\n  float angleInRadians = radians(angle);\n  float ratio = clamp((v_uv0.y * cos(angleInRadians) + v_uv0.x * sin(angleInRadians) + offset) * uvRatio, 0.0, 1.0);\n  float beginRatio = 1.0 - ratio;\n  float endRatio = ratio;\n  if(beginRatio > 0.5) {\n    gl_FragColor = vec4(\n          o.r * (beginColor.r * beginRatio + middleColor.r * endRatio),\n          o.g * (beginColor.g * beginRatio + middleColor.g * endRatio),\n          o.b * (beginColor.b * beginRatio + middleColor.b * endRatio),\n          o.a * (beginColor.a * beginRatio + middleColor.a * endRatio)\n        );\n  }else {\n    gl_FragColor = vec4(\n          o.r * (middleColor.r * beginRatio + beginColor.r * endRatio),\n          o.g * (middleColor.g * beginRatio + beginColor.g * endRatio),\n          o.b * (middleColor.b * beginRatio + beginColor.b * endRatio),\n          o.a * (middleColor.a * beginRatio + beginColor.a * endRatio)\n        );\n  }\n}"}}], "subMetas": {}}