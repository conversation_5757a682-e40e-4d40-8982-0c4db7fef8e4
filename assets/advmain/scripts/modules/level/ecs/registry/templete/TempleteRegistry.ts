import { TempleteBase } from '../../cores/center/TempleteCenter';
import { BoardTemplete, BoardTempleteMustArgs, BoardTempleteOptionalArgs } from '../../template/BoardTemplete';
import {
    CellRenderTemplate,
    CellRenderTempleteOptionalArgs,
    CellTemplete,
    CellTempleteMustArgs,
    CellTempleteOptionalArgs,
} from '../../template/CellTemplete';
import { BoardSceneTemplete, SceneTempleteMustArgs, SceneTempleteOptionalArgs } from '../../template/SceneTemplete';
import { ShapeTemplete, ShapeTempleteMustArgs, ShapeTempleteOptionalArgs } from '../../template/ShapeTemplete';
import { SlotTemplete, SlotTempleteMustArgs, SlotTempleteOptionalArgs } from '../../template/SlotTemplete';
import { PreviewCellTemplete } from '../../template/PreviewCellTemplete';
import { CollectIconTemplete, CollectIconTempleteMustArgs, CollectIconTempleteOptionalArgs } from '../../template/CollectIconTemplete';
import { TargetTemplate, TargetTempleteMustArgs, TargetTempleteOptionalArgs } from '../../template/TargetTemplate';
import { RenderTemplate, RenderTemplateMustArgs, RenderTemplateOptionalArgs } from '../../template/RenderTemplate';
import { EffectTemplete } from '../../template/EffectTemplete';
import { BulletTemplete, BulletTempleteMustArgs, BulletTempleteOptionalArgs } from '../../template/BulletTemplete';
import { UITemplete } from '../../template/UITemplete';

/**
 * 模板枚举
 */
export enum TempleteType {
    /**棋盘 */
    Board = 'Board',
    /**格子 */
    Slot = 'Slot',
    /**完整方块 */
    Cell = 'Cell',
    /**渲染方块 */
    CellRender = 'CellRender',
    /**形状 */
    Shape = 'Shape',
    /**场景 */
    BoardScene = 'BoardScene',
    /**预览方块 */
    PreviewCell = 'PreviewCell',
    /**收集图标 */
    CollectIcon = 'CollectIcon',
    /**目标 */
    Target = 'Target',
    /**通用渲染 */
    Render = 'Render',
    /**通用特效 */
    Effect = 'Effect',
    /**通用子弹 */
    Bullet = 'Bullet',
    /**通用UI */
    UI = 'UI',
}

/**
 * 模板注册表
 */
export const TempleteRegistry: Record<string, clzz<TempleteBase>> = {
    [TempleteType.Board]: BoardTemplete,
    [TempleteType.Slot]: SlotTemplete,
    [TempleteType.Cell]: CellTemplete,
    [TempleteType.CellRender]: CellRenderTemplate,
    [TempleteType.Shape]: ShapeTemplete,
    [TempleteType.BoardScene]: BoardSceneTemplete,
    [TempleteType.PreviewCell]: PreviewCellTemplete,
    [TempleteType.CollectIcon]: CollectIconTemplete,
    [TempleteType.Target]: TargetTemplate,
    [TempleteType.Render]: RenderTemplate,
    [TempleteType.Effect]: EffectTemplete,
    [TempleteType.Bullet]: BulletTemplete,
    [TempleteType.UI]: UITemplete,
};
/**
 * 模板参数类型映射
 */
export interface TempleteArgsMap {
    [TempleteType.Board]: {
        mustArgs: BoardTempleteMustArgs;
        optionalArgs: BoardTempleteOptionalArgs;
    };
    [TempleteType.Slot]: {
        mustArgs: SlotTempleteMustArgs;
        optionalArgs: SlotTempleteOptionalArgs;
    };
    [TempleteType.Cell]: {
        mustArgs: CellTempleteMustArgs;
        optionalArgs: CellTempleteOptionalArgs;
    };
    [TempleteType.CellRender]: {
        mustArgs: CellTempleteMustArgs;
        optionalArgs: CellRenderTempleteOptionalArgs;
    };
    [TempleteType.Shape]: {
        mustArgs: ShapeTempleteMustArgs;
        optionalArgs: ShapeTempleteOptionalArgs;
    };
    [TempleteType.BoardScene]: {
        mustArgs: SceneTempleteMustArgs;
        optionalArgs: SceneTempleteOptionalArgs;
    };
    [TempleteType.PreviewCell]: {
        mustArgs: CellTempleteMustArgs;
        optionalArgs: CellRenderTempleteOptionalArgs;
    };
    [TempleteType.CollectIcon]: {
        mustArgs: CollectIconTempleteMustArgs;
        optionalArgs: CollectIconTempleteOptionalArgs;
    };
    [TempleteType.Target]: {
        mustArgs: TargetTempleteMustArgs;
        optionalArgs: TargetTempleteOptionalArgs;
    };
    [TempleteType.Render]: {
        mustArgs: RenderTemplateMustArgs;
        optionalArgs: RenderTemplateOptionalArgs;
    };
    [TempleteType.Effect]: {
        mustArgs: RenderTemplateMustArgs;
        optionalArgs: RenderTemplateOptionalArgs;
    };
    [TempleteType.Bullet]: {
        mustArgs: BulletTempleteMustArgs;
        optionalArgs: BulletTempleteOptionalArgs;
    };
    [TempleteType.UI]: {
        mustArgs: RenderTemplateMustArgs;
        optionalArgs: RenderTemplateOptionalArgs;
    };
}
