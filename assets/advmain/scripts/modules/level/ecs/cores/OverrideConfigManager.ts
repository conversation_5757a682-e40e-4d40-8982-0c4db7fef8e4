import Util from "../../../../base/Util";
import { ConfigRegistry, ConfigTableConst } from "../registry/ConfigRegistry";
import { ConfigCenter, IGameLevelConfig } from "./center/ConfigCenter";
/**关闭系统 */
export interface ICloseSystem{
    /**系统id */
    systemId: string;
}
/**关闭规则 */
export interface ICloseRule{
    /**规则id */
    ruleId: string;

}
/**插入系统 */
export interface IInsertSystem{
    /**系统id */
    systemId: string;
    /**必须在哪个系统之后 */
    afterSystemId?: string;
}
/**插入规则 */
export interface IInsertRule{
    /**规则id */
    ruleId: string;
    /**必须在哪个规则之后 */
    afterRuleId?: string;
    /**必须在哪个规则之前 */
    beforeRuleId?: string;
}
/**替换系统 */
export interface IReplaceSystem{
    /**原系统id */
    originSystemId: string;
    /**替换系统id */
    replaceSystemId: string;
}
/**替换规则 */
export interface IReplaceRule{
    /**原规则id */
    originRuleId: string;
    /**替换规则id */
    replaceRuleId: string;
}
/** 重写配置 */
export interface IOverrideLevelConfig {
    /** 底板配置 */
    baseLevelId: string;
    /** 关卡ID */
    levelId: string;
    /** 重写配置表参数 */
    overrideTableArgs: DeepPartial<ConfigRegistry>;
    /** 重写关卡配置参数 */
    overrideLevelConfig: DeepPartial<IGameLevelConfig>;
    /** 关闭系统 */
    closeSystems: ICloseSystem[];
    /** 关闭规则 */
    closeRules: ICloseRule[];
    /** 插入系统 */
    insertSystems: IInsertSystem[];
    /** 插入规则 */
    insertRules: IInsertRule[];
    /** 替换系统 */
    replaceSystems: IReplaceSystem[];
    /** 替换规则 */
    replaceRules: IReplaceRule[];
}
/** 重写配置管理器 */
export class OverrideConfigManager {
    /**配置管理器 */
    private configCenter: ConfigCenter;
    constructor(configTable: ConfigCenter) {
        this.configCenter = configTable;
    }
    /**重写配置 */
    public overrideConfig(overrideConfig: IOverrideLevelConfig) {
        const overrideTable = overrideConfig.overrideTableArgs;
        if (!overrideTable) return;

        // 遍历所有重写的配置表
        for (const tableKey in overrideTable) {
            if (overrideTable.hasOwnProperty(tableKey)) {
                const tableOverrides = overrideTable[tableKey];
                
                // 遍历该配置表中的所有重写项
                for (const configId in tableOverrides) {
                    if (tableOverrides.hasOwnProperty(configId)) {
                        const configOverrides = tableOverrides[configId];
                        
                        // 直接修改ConfigManager中的配置值
                        const configCenter = this.configCenter;
                        const originalConfig = configCenter.getConfigByTableKey(tableKey as any, configId);
                        
                        if (originalConfig) {
                            // 深度合并配置
                            const mergedConfig = Util.deepMerge(originalConfig, configOverrides);
                            // 直接替换配置值
                            configCenter.setConfigByTableKey(tableKey as any, configId, mergedConfig);
                        }
                    }
                }
            }
        }
    }

    public buildConfig(config: IOverrideLevelConfig): IGameLevelConfig {
        this.overrideConfig(config);
        let baseConfig = this.configCenter.getConfigByTableKey(ConfigTableConst.LevelConfig,config.baseLevelId);
        if(!baseConfig) return null;
        baseConfig.levelId = config.levelId;
        baseConfig = Util.deepMerge(baseConfig, config.overrideLevelConfig);
        this.closeSystems(config.closeSystems);
        this.closeRules(config.closeRules);
        this.insertSystems(config.insertSystems);
        this.replaceSystems(config.replaceSystems);
        this.replaceRules(config.replaceRules);
        return baseConfig;
    }
    
    /**关闭系统 */
    private closeSystems(closeSystemIdList: ICloseSystem[]) {
        const config = this.configCenter;
        for (const systemObj of closeSystemIdList) {
            const systemConfig = config.getSystemConfig(systemObj.systemId);
            if (systemConfig) {
                (systemConfig.close as any) = true;
            }
        }
    }
    /**关闭规则 */
    private closeRules(closeList: ICloseRule[]) {
        const config = this.configCenter;
        for (const closeObj of closeList) {
            const ruleConfig = config.getRuleConfig(closeObj.ruleId);
            if (ruleConfig) {
                (ruleConfig.close as any) = true;
            }
        }
    }
    /**替换系统 */
    private replaceSystems(replaceSystemIdList: IReplaceSystem[]) {
        const config = this.configCenter;
        for (const systemObj of replaceSystemIdList) {
            const systemConfig = config.getSystemConfig(systemObj.originSystemId);
            if (systemConfig) {
                (systemConfig.replaceSystemId as any) = systemObj.replaceSystemId;
            }
        }
    }
    /**替换规则 */
    private replaceRules(replaceRuleIdList: IReplaceRule[]) {
        const config = this.configCenter;
        for (const ruleObj of replaceRuleIdList) {
            const ruleConfig = config.getRuleConfig(ruleObj.originRuleId);
            if (ruleConfig) {
                (ruleConfig.replaceRuleId as any) = ruleObj.replaceRuleId;
            }
        }
    }
    /**插入系统 */
    private insertSystems(insertSystemIdList: IInsertSystem[]) {
        const config = this.configCenter;
        
        for (const systemObj of insertSystemIdList) {
            const systemConfig = config.getSystemConfig(systemObj.systemId);
            if (!systemConfig) {
                console.error(`插入系统失败：系统不存在 systemId=${systemObj.systemId}`);
                continue;
            }
        }
    }
}