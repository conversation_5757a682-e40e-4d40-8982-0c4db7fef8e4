import { IBaseSkillConfig } from "../config/SkillConfig";
import { ISkillCooldownConfig } from "../config/SkillCooldownConfig";
import { IECSViewConfig } from "../renderWorld/ECSViewBase";
import { ISystemConfig } from "../cores/System";
import { IECSRuleConfig } from "../rules/core/ECSRuleBase";
import { IAIBehaviorConfig } from "../behavior/ai/AIBehaviorBase";
import { IBuffBehaviorConfig } from "../behavior/buff/BuffBehaviorBase";
import { IBuffStackBehaviorConfig } from "../behavior/buffstack/BuffStackBehaviorBase";
import { IProjectileBehaviorConfig } from "../behavior/projectile/ProjectileBehaviorBase";
import { IProjectilePathBehaviorConfig } from "../behavior/projectilePath/ProjectilePathBehaviorBase";
import { IOverrideLevelConfig } from "../cores/OverrideConfigManager";
import { IBoardConfig } from "../config/conf/BoardConfig";
import { IGameLevelConfig } from "../cores/center/ConfigCenter";

/**配置表名 */
export const enum ConfigTableConst {
    StackConfig = "StackConfig",
    SkillConfig = "SkillConfig",
    CooldownConfig = "CooldownConfig",
    /*----------------------------------------------------技能系统行为配置----------------------------------------------------- */
    /*********投射物路径行为配置表********* */
    ProjectilePathBehaviorConfig = "ProjectilePathBehaviorConfig",
    /*********投射物行为配置表********* */
    ProjectileBehaviorConfig = "ProjectileBehaviorConfig",
    /*********buff行为配置表********* */
    BuffBehaviorConfig = "BuffBehaviorConfig",
    /*********buff叠加行为配置表********* */
    BuffStackBehaviorConfig = "BuffStackBehaviorConfig",
    /*----------------------------------------------------配置----------------------------------------------------- */
    /*********关卡配置表********* */
    LevelConfig = "LevelConfig",
    /*********关卡重写配置表********* */
    OverrideLevelConfig = "OverrideLevelConfig",
    /*********ECS视图配置表********* */
    EcsViewConfig = "EcsViewConfig",
    /*********系统配置表********* */
    SystemConfig = "SystemConfig",
    /*********规则流配置表********* */
    RuleFlowConfig = "RuleFlowConfig",
    /*********规则配置表********* */
    RuleConfig = "RuleConfig",
    /*********AI行为配置表********* */
    AIBehaviorConfig = "AIBehaviorConfig",
    /*********棋盘配置表********* */
    BoardConfig = "BoardConfig",
}

/**配置注册表 */
export interface ConfigRegistry {
    /*********技能配置表********* */
    [ConfigTableConst.SkillConfig]?: Record<string, IBaseSkillConfig>;
    /*********技能冷却配置表********* */
    [ConfigTableConst.CooldownConfig]?: Record<string, ISkillCooldownConfig>;
    /**----------------------------------------------------行为配置表----------------------------------------------------- */
    /*********投射物行为配置表********* */
    [ConfigTableConst.ProjectileBehaviorConfig]?: Record<string, IProjectileBehaviorConfig>;
    /*********投射物路径行为配置表********* */
    [ConfigTableConst.ProjectilePathBehaviorConfig]?: Record<string, IProjectilePathBehaviorConfig>;
    /*********buff行为配置表********* */
    [ConfigTableConst.BuffBehaviorConfig]?: Record<string, IBuffBehaviorConfig>;    
    /*********buff叠加行为配置表********* */
    [ConfigTableConst.BuffStackBehaviorConfig]?: Record<string, IBuffStackBehaviorConfig>;
    /*********AI行为配置表********* */
    [ConfigTableConst.AIBehaviorConfig]?: Record<string, IAIBehaviorConfig>;
    /**----------------------------------------------------配置表----------------------------------------------------- */
    /*********关卡配置表********* */
    [ConfigTableConst.LevelConfig]?: Record<string, IGameLevelConfig>;
    /*********关卡重写配置表********* */
    [ConfigTableConst.OverrideLevelConfig]?: Record<string, IOverrideLevelConfig>;
    /*********ECS视图配置表********* */
    [ConfigTableConst.EcsViewConfig]?: Record<string, IECSViewConfig>;
    /*********系统配置表********* */
    [ConfigTableConst.SystemConfig]?: Record<string, ISystemConfig>;
    /*********规则配置表********* */
    [ConfigTableConst.RuleConfig]?: Record<string, IECSRuleConfig>;
    /*********棋盘配置表********* */
    [ConfigTableConst.BoardConfig]?: Record<string, IBoardConfig>;
    /** 当前关卡配置 */
    currentLevelConfig?:IGameLevelConfig;
}