import { BaseFlow } from "./BaseFlow";

/**
 * 顺序执行流 - 按顺序执行所有子单元
 */
export class ListFlow extends BaseFlow {
  run(): boolean {
    let isSuccess = false;
    for (const child of this.children) {
      child.setContext(this.context);
      if (this.isRule(child) && child.canRun() && child.invalidateExecuteContext()) {
        child.run();
        isSuccess = true;
      }
      // 处理执行流节点 - 可以添加流结果检查逻辑
      if (this.isFlow(child) && child.canRun() && child.invalidateExecuteContext()) {
        if(child.run()){
          isSuccess = true;
        }
      }
    }
    return isSuccess;
  }
}