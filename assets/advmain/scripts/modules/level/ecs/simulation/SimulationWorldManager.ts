interface EcsSnapshot {
    entities: { [entityId: number]: ComponentData[] };
    serializableObjects: unknown[];
}

interface ComponentData {
    type: string;
    data: unknown;
}

interface GameRef {
    levelConfig?: object;
    saveSnapshot?: () => void;
    [key: string]: unknown;
}

interface SimulatedGame extends GameRef {
    levelConfig: object | null;
    saveSnapshot: () => void;
}

interface SystemWithLifecycle {
    dispose?: () => void;
    init?: () => void;
    bubbleUnoccludedBlocksToTop?: () => void;
    constructor: { name: string };
}

interface FrameCreationState {
    isProcessing: boolean;
    entityEntries: Array<[string, ComponentData[]]>;
    currentIndex: number;
    resolveCallback: ((value: boolean) => void) | null;
    rejectCallback: ((reason?: unknown) => void) | null;
}
/**
 * 模拟世界管理器
 * 创建独立的纯逻辑模拟世界，不影响真实游戏状态
 */

export class SimulationWorldManager {}
