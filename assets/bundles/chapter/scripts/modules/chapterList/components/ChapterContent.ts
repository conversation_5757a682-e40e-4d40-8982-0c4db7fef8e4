
import { <PERSON>Loader } from '../../../../../../advmain/scripts/base/LevelLoader';
import { Loader } from '../../../../../../advmain/scripts/base/Loader';
import { SubGameBridge } from '../../../../../../advmain/scripts/base/SubGameBridge';
import { ChapterUrls } from '../../../../../../advmain/scripts/modules/chapterConfig/ChapterDefineds';
import { chapterConfigInfo } from '../../../../../../advmain/scripts/modules/chapterConfig/vo/ChapterConfigInfo';
import { PrefabConfig } from '../../../../../../advmain/scripts/modules/prefab/PrefabConfig';
import { ChapterAudioConfig } from '../../audio/config/ChapterAudioConfig';
import ChapterCurSeat from './ChapterCurSeat';
import ChapterItem from './ChapterItem';
import ChapterLevelTxt from './ChapterLevelTxt';
import ChapterThroughCupBone from './ChapterThroughCupBone';

export interface IChapterContent {
    itemFont: cc.Font;
    curChapter: number;
    /**上次移动到的关卡 */
    lastChapter: number;
    isThrough: boolean;
    redraw: boolean;
    isShowChapterBtn: boolean;
}

const ChapterListConfig = {
    atlasPath: {
        NoTheme96Config: 'textures/chapterList/periods/block/chapterListBlocks',
    },
    /**item */
    itemPath: 'prefabs/chapterList/ChapterItem',
    /**item高度 */
    itemHeight: 80,
    /**奖杯y偏移 */
    cupNodeOffsetY: 350,
    /**关卡列表 高度增量 */
    itemsContentHeightAdd: 900,
    /**默认item长度 */
    defaultHeightLength: 13,
};

const { ccclass, property } = cc._decorator;
/**
 * 关卡item列表 ->马赛克
 */
@classId('ChapterContent')
@ccclass
export default class ChapterContent extends falcon.Component<Partial<IChapterContent>> {
    /**滑动 */
    @property(cc.ScrollView)
    scroll: cc.ScrollView = null;

    /**关卡信息节点 */
    @property(cc.Node)
    itemContainer: cc.Node = null;

    /**奖杯节点 */
    @property(cc.Node)
    cupContainer: cc.Node = null;

    /**奖杯图 */
    @property(cc.Sprite)
    cup: cc.Sprite = null;

    /**奖杯动画父节点 */
    @property(cc.Node)
    cupBoneParent: cc.Node = null;

    /**当前关卡按钮 */
    @property(cc.Node)
    curChapterBtn: cc.Node = null;

    @property(cc.Node)
    content: cc.Node = null;

    /**通关动画节点 */
    private cupBoneNode: ChapterThroughCupBone = null;
    /**当前位置动画节点 */
    private curBoneNode: ChapterCurSeat = null;
    /**item列表 */
    private itemList: ChapterItem[] = [];
    /**是否初始化 */
    private isInit: boolean = false;

    private levelTextComps: ChapterLevelTxt[] = [];

    private levelTextParent: cc.Node = null;

    private levelCount = 0;

    public contentPos: cc.Vec3 = null;

    shouldComponentUpdate(nextState: Readonly<Partial<IChapterContent>>): boolean {
        return this.state.curChapter != nextState.curChapter || this.state.lastChapter != nextState.lastChapter;
    }

    private async init() {
        if (this.isInit && !this.state.redraw) {
            return;
        }
        this.node.opacity = 0;

        if (!this.contentPos) {
            this.contentPos = this.content.position;
        }

        await this.initCurBone();

        await falcon.timer.nextFrame();

        await this.loadItem();

        await this.loadLevelText();

        await falcon.timer.nextFrame();

        await this.initThroughCupBone();

        this.node.opacity = 255;
        if (!this.state.isThrough) {
            this.scroll.scrollToBottom(0);
        }

        this.isInit = true;
        this.state.redraw = false;
    }

    private async loadItem() {
        this.itemList = [];

        const list2d = chapterConfigInfo.chapterListCfg;
        /** 高长度 */
        let height = list2d.length;
        /** 宽长度 */
        let width = list2d[0].length;
        /** * item的高度跟宽度 */
        let itemHeight = ChapterListConfig.itemHeight;
        this.scroll.content.height = height * itemHeight + ChapterListConfig.itemsContentHeightAdd;
        const winsize = cc.view.getVisibleSize();
        if (winsize.height > this.scroll.content.height) {
            this.scroll.content.height = winsize.height;
        }

        this.scroll.content.position = cc.v3(0, -this.scroll.content.height / 2, 0);

        this.levelCount = 0;
        list2d.forEach((arr) => {
            arr.forEach((num) => {
                if (num !== -1) this.levelCount++;
            });
        });

        //得到所有的cell 实例组件
        const itemComps = await falcon.cacheRender.createOrUpdateCacheListComponents({
            parent: this.itemContainer,
            prefabUrl: PrefabConfig.ChapterItem.url,
            count: this.levelCount,
            typeOrClassName: ChapterItem,
            bundleName: 'chapter',
        });

        let tempCount = 0;
        // let allGroupInfo = [[-1, -1, -1, -1, 94, 95, 96, -1, -1, -1, -1], [-1, -1, 91, 92, 93, 88, 89, 90, -1, -1, -1], [-1, -1, 82, 83, 84, 85, 75, 86, 87, -1, -1], [-1, 79, 80, 81, 72, 73, 74, 69, 70, -1, -1], [-1, 76, 77, 78, 71, 59, 60, 67, 68, -1, -1], [-1, 52, 53, 54, 55, 57, 58, 66, 64, 65, -1], [-1, -1, 51, 49, 50, 45, 56, 61, 62, 63, -1], [-1, -1, 47, 48, 43, 44, 38, 39, 40, -1, -1], [-1, -1, 46, 41, 42, 34, 35, 37, -1, -1, -1], [-1, -1, -1, 30, 31, 32, 33, 36, 25, -1, -1], [-1, -1, 5, 27, 28, 29, 22, 23, 24, 20, -1], [-1, 3, 4, 26, 9, 10, 21, 14, 15, 19, -1], [1, 2, 6, 7, 8, 11, 12, 13, 16, 17, 18]];
        for (let i = height - 1; i >= 0; i--) {
            await falcon.timer.nextFrame();

            let moveAllstate = height % 2;
            for (let j = 0; j < list2d[i].length; j++) {
                let index = list2d[i][j];
                if (index != -1) {
                    const pos =
                        moveAllstate != 0
                            ? cc.v3(-Math.floor(width / 2) * itemHeight + itemHeight * j, 250 + (height - i) * itemHeight)
                            : cc.v3((-width / 2) * itemHeight + itemHeight / 2 + itemHeight * j, 250 + (height - i) * itemHeight);

                    const comp = itemComps[tempCount];
                    if (comp) {
                        tempCount++;
                        comp.node.setPosition(pos);
                        comp.setState({ levelNum: tempCount, color: index, showColor: tempCount <= this.state.lastChapter });
                        this.itemList.push(comp);
                    }
                }
            }
        }
    }
    private async loadLevelText() {
        if (this.itemList.length === 0) {
            return;
        }

        if (!this.levelTextParent) {
            this.levelTextParent = new cc.Node();
            this.levelTextParent.parent = this.itemContainer;
        }

        //得到所有的levelText 实例组件
        const comps = await falcon.cacheRender.createOrUpdateCacheListComponents({
            parent: this.levelTextParent,
            prefabUrl: PrefabConfig.ChapterLevelTxt.url,
            count: this.levelCount,
            typeOrClassName: ChapterLevelTxt,
            bundleName: 'chapter',
        });

        this.levelTextComps = comps;

        //第一期
        for (let i = 0; i < this.levelCount; i++) {
            const item = this.itemList[i];
            if (!comps[i]) {
                continue;
            }
            const level = i + 1;
            comps[i].node.x = item.node.x;
            comps[i].node.y = item.node.y;
            comps[i].level = level;
            comps[i].node.name = `关卡数字${level}`;

            comps[i].setState({
                text: level.toString(),
                opacity: level <= this.state.lastChapter ? 0 : 255,
            });
        }
    }

    /**通关奖杯动画初始化 */
    private async initThroughCupBone() {
        if (this.cupBoneNode) {
            this.setCupPos();
            return;
        }

        const p: cc.Prefab = await Loader.asyncLoadByBundle('chapter', 'prefabs/chapterList/ChapterThroughCupBone', cc.Prefab);
        if (!p) {
            return;
        }
        const node = cc.instantiate(p);
        if (node) {
            this.cupBoneParent.addChild(node);
            this.cupBoneNode = node.getComponent(ChapterThroughCupBone);
        }

        this.setCupPos();
    }
    /**当前关动画初始化 */
    private async initCurBone() {
        if (this.curBoneNode) {
            return;
        }

        const p: cc.Prefab = await Loader.asyncLoadByBundle('chapter', ChapterUrls.curBonePath, cc.Prefab);
        if (!p) {
            return;
        }
        const node = cc.instantiate(p);
        if (node) {
            this.curChapterBtn.addChild(node);
            this.curBoneNode = node.getComponent(ChapterCurSeat);
        }
    }

    /**设置奖杯节点位置 */
    private setCupPos() {
        const datas = chapterConfigInfo.chapterListCfg;
        this.cupContainer.y = datas.length * ChapterListConfig.itemHeight + ChapterListConfig.cupNodeOffsetY; // + (datas.length - ChapterListConfig.defaultHeightLength) * ChapterListConfig.itemHeight;
    }

    /**移动关卡位置 */
    private moveChapter() {
        const moveDistance = this.state.curChapter - this.state.lastChapter;

        if (moveDistance <= 0) {
            for (let i = 0; i < this.itemList.length; i++) {
                const item = this.itemList[i];
                const comp = this.levelTextComps[i];
                if (i < this.state.lastChapter) {
                    item.setState({ isRemove: true, move: false, showColor: true });
                    comp && comp.setState({ opacity: 0 });
                } else {
                    item.setState({ isRemove: true, move: false });
                }
            }

            this.refreshUIState();
            return;
        }

        const moveIndex = this.state.curChapter - 1;
        //需要移动到关卡
        for (let i = 0; i < this.itemList.length; i++) {
            const item = this.itemList[i];
            const comp = this.levelTextComps[i];
            if (i < this.state.lastChapter) {
                comp && comp.setState({ opacity: 0 });
                item.setState({ isRemove: true, move: false, showColor: true });
            } else {
                if (i <= moveIndex) {
                    //移动到当前关卡

                    cc.tween(this.node)
                        .delay(0.07 * (i - this.state.lastChapter + 1))
                        .call(() => {
                            falcon.audioInfo.play(ChapterAudioConfig.travel_unlock_lvmark);
                            const item = this.itemList[i];
                            comp && comp.setState({ opacity: 0 });
                            item.setState({ move: true, showColor: true });
                            if (i == moveIndex) {
                                this.refreshUIState();
                                return;
                            }
                        })
                        .start();
                }
            }
        }
    }

    /**刷新ui状态 */
    private refreshUIState() {
        const { isThrough, curChapter } = this.state;

        //通关
        if (isThrough) {
            //波浪动画 => 奖杯动画
            this.showWaveAnimation().then(() => {
                this.showCupAnimation();
            });
            return;
        }

        //奖杯
        if (this.curBoneNode) {
            this.curBoneNode.setState({ animName: 'stand', playTimes: 1, curNumId: curChapter + 1 });
        }

        // 当前位置动画
        this.curChapterBtn.active = true;
        this.curChapterBtn.position = this.itemList[curChapter].node.position;
    }

    /**  显示波浪动画 */
    private showWaveAnimation(): Promise<number> {
        let animationObject: { [key: string]: ChapterItem[] } = {};
        // 同一斜线的一起动 -x+(330+(n*80))=y;
        for (let i = 0; i < this.itemList.length; i++) {
            const itemLevel = this.itemList[i];
            const x = itemLevel.node.position.x;
            const y = itemLevel.node.position.y;

            const n = parseInt(`${(y - 330 + x) * 0.0125}`);
            if (animationObject[`${n}`]) {
                animationObject[`${n}`].push(itemLevel);
            } else {
                animationObject[`${n}`] = [itemLevel];
            }
        }

        const sortedKeys = Object.keys(animationObject).sort((a, b) => parseInt(a) - parseInt(b));
        let sortedAnimationList: ChapterItem[][] = [];
        sortedKeys.forEach((key) => {
            sortedAnimationList.push(animationObject[key]);
        });

        const { length } = sortedAnimationList;
        for (let i = 0; i < sortedAnimationList.length; i++) {
            const itemLevelList = sortedAnimationList[i];

            for (let j = 0; j < itemLevelList.length; j++) {
                cc.tween(this.node)
                    .delay(0.07 * (i + 1))
                    .call(() => {
                        itemLevelList[j].setState({ isThrough: true, move: false });
                    })
                    .start();
            }
        }

        return new Promise((resolve) => {
            cc.tween(this.node)
                .delay((length / 2) * 0.07)
                .call(() => {
                    resolve(1);
                })
                .start();
        });
    }

    /**
     * 显示奖杯动画
     */
    showCupAnimation() {
        this.cupBoneParent.active = this.state.isThrough;
        this.scroll.scrollToOffset(new cc.Vec2(0, 0), 0.4);
        falcon.audioInfo.play(ChapterAudioConfig.travel_cup_award);
        //奖杯动画完成回调
        this.cupBoneNode.onAnimationComplete.wait().then(() => {
            this.allAnimationComplete();
        });
        this.cupBoneNode.setState({ playTimes: 1, animName: 'newAnimation' });
    }

    /**
     * 通关动画全部完成
     */
    private allAnimationComplete() {}

    async render() {
        // 移除动画
        cc.Tween.stopAllByTarget(this.node);

        await this.init();

        // 奖杯图
        this.cup.node.active = !this.state.isThrough;

        // 当前动画
        this.curChapterBtn.active = this.state.curChapter == this.state.lastChapter && !this.state.isThrough;

        if (!this.state.isThrough) {
            this.cupBoneParent.active = false;
        }
        // 移动
        this.moveChapter();
        if (this.state.isThrough) {
            const winsize = cc.view.getVisibleSize();
            this.scroll.content.position = cc.v3(0, -winsize.height / 2, 0);
        }
    }
    /**开始关卡游戏 */
    @falcon.throttle(300)
    onClickStartGame() {
        LevelLoader.enterLevel(`${this.state.curChapter + 1}`);
    }
}
