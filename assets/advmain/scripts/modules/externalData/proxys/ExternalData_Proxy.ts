import { E_ExternalData_UpdateExternalData } from "../events/E_ExternalData_UpdateExternalData";

/**外部数据源代理 */
export class ExternalData_Proxy extends falcon.Proxy {
    registerEvents(): { new(...args: any): falcon.ModuleEvent; }[] | null {
        return [
            E_ExternalData_UpdateExternalData
        ];
    }

    receivedEvents($event: falcon.ModuleEvent): void {
        switch ($event.getClass()) {
            case E_ExternalData_UpdateExternalData:
                
                break;
        }
    }
}