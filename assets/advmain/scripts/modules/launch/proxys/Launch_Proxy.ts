import { LevelLoader } from '../../../base/LevelLoader';
import ChapterChoice from '../../chapterConfig/ChapterChoice';
import { E_Game_InitComplete } from '../../game/events/E_Game_InitComplete';
import { PrefabConfig } from '../../prefab/PrefabConfig';
import { E_Launch_Start } from '../events/E_Launch_Start';
@classId('Launch_Proxy')
export class Launch_Proxy extends falcon.Proxy {
    registerEvents(): { new (...args: any): falcon.ModuleEvent }[] | null {
        return [E_Launch_Start, E_Game_InitComplete];
    }

    receivedEvents($event: falcon.ModuleEvent): void {
        switch ($event.getClass()) {
            case E_Launch_Start:
                this.onLaunchStart();
                break;

            case E_Game_InitComplete:
                this.onTraitConfigInitComplete($event as E_Game_InitComplete);
                break;
        }
    }

    async onTraitConfigInitComplete(event: E_Game_InitComplete) {
        await falcon.UI.show(PrefabConfig.Launch);
        ChapterChoice.enter();
    }

    onLaunchStart() {
        // 初始化UI mode prefab
        falcon.UI.setModalPrefab(PrefabConfig.Modal.url);

        // TODO 初始化成功 热更 服务器特性
        this.dispatchModuleEvent(new E_Game_InitComplete());
    }
}
