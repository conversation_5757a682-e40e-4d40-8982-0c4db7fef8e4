import { Component } from "../../cores/Component";
import { IBaseActionData, IBehaviorData } from "../core/BehaviorBase";

/**
 * 技能状态枚举
 */
export enum SkillState {
    Inactive = 'inactive',   // 未激活状态（被动技能或未使用的主动技能）
    Casting = 'casting',     // 施法中状态（主动技能使用过程中）
    Cooldown = 'cooldown'    // 冷却中状态（技能使用后的冷却期）
}
/**
 * 技能数据接口
 * 只包含所有技能类型的公共属性，具体逻辑完全由行为实现
 */
export interface SkillData<T extends IBehaviorData = IBehaviorData> extends IBaseActionData<T> {
    skillId: string;         // 技能唯一标识
    ownerEntityId: number;   // 技能拥有者实体ID（技能存储在谁的技能列表中）
    casterEntityId: number;  // 施法者实体ID（当前使用技能的实体，通常等于ownerEntityId）
    state: SkillState;       // 技能状态
    lastCastTime: number;   // 上次施法时间（触发冷却计算）
    lastCompleteTime: number; // 上次完成时间（完成冷却计算）
}


/**
 * 技能组件
 * 用于存储和管理实体的技能列表
 */
export class Skill extends Component {
    static type = 'SkillComponent';
    skillList: SkillData<IBehaviorData>[] = [];

    /**
     * 添加技能
     * @param skillData 技能数据
     * @returns 是否添加成功
     */
    addSkill(skillData: SkillData<IBehaviorData>): boolean {
        if (!skillData) return false;
        this.skillList.push(skillData);
        return true;
    }

    /**
     * 移除技能
     * @param skillId 技能配置ID
     * @returns 被移除的技能数据,如果不存在则返回undefined
     */
    removeSkill(skillId: string): SkillData<IBehaviorData> | undefined {
        const index = this.skillList.findIndex(skill => skill.skillId === skillId);
        if (index === -1) return undefined;
        return this.skillList.splice(index, 1)[0];
    }

    /**
     * 查找技能
     * @param skillId 技能配置ID
     * @returns 技能数据,如果不存在则返回undefined
     */
    findSkill(skillId: string): SkillData<IBehaviorData> | undefined {
        return this.skillList.find(skill => skill.skillId === skillId);
    }

    /**
     * 检查是否存在指定技能
     * @param skillId 技能配置ID
     * @returns 是否存在
     */
    hasSkill(skillId: string): boolean {
        return this.skillList.some(skill => skill.skillId === skillId);
    }
} 