
/**
 * 带描述的调试模式错误捕获装饰器
 * @param desc 错误描述信息
 */
export function TryCatch(desc: string) {
    return function(target: any, propertyKey: string, descriptor: PropertyDescriptor) {
        const originalMethod = descriptor.value;

        descriptor.value = function (...args: any[]) {
            // 在CC_DEBUG模式下，直接执行原方法，让错误自然抛出
            if (CC_DEBUG) {
                return originalMethod.apply(this, args);
            }

            // 在非CC_DEBUG模式下，包装try-catch
            try {
                return originalMethod.apply(this, args);
            } catch (error) {
                console.error(`[CatchDebug] ${desc} - 方法 ${propertyKey} 执行出错:`, error);
                console.error(`[CatchDebug] 错误堆栈:`, error.stack);
                
                // 可以选择返回默认值或者重新抛出错误
                // return defaultValue; // 返回默认值
                throw error; // 重新抛出错误
            }
        };

        return descriptor;
    };
}
