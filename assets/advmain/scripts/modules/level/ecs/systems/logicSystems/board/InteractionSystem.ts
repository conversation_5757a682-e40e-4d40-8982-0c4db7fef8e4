import BoardComponent from '../../../components/board/BoardComponent';
import { BoardScene } from '../../../components/board/BoardScene';
import { CellComponent } from '../../../components/board/CellComponent';
import SlotComponent from '../../../components/board/SlotComponent';
import { RelationName } from '../../../cores/center/EntityCenter/WorldRelation';
import { System } from '../../../cores/System';
import { CellColor, CellType, TargetType, CELL_TEMPLATE_PARAM } from '../../../define/BoardDefine';
import { ECSEvent } from '../../../GameEvent';
import { IPutBlockInfo, IPutBlockBackInfo } from '../../../GameEventData';
import { TempleteType } from '../../../registry/templete/TempleteRegistry';
import { CellOption } from '../../../template/CellTemplete';

export class InteractionSystem extends System {
    init(): void {
        this.world.eventBus.on(ECSEvent.GameEvent.PUT_BLOCK, this.onHanderPutBlock, this);
    }
    dispose(): void {
        this.world.eventBus.off(ECSEvent.GameEvent.PUT_BLOCK, this.onHanderPutBlock, this);
    }

    onHanderPutBlock(putBlockInfo: IPutBlockInfo) {
        //先对id处理下 多层棋盘的话返回上层
        putBlockInfo.boardId = this.world.handlerCenter.boardHandler.getTopLayerBoardId(putBlockInfo.boardId);
        // 获取棋盘组件
        const boardEntity = this.world
            .query([BoardComponent])
            .find((eid) => this.world.getComponent(eid, BoardComponent).boardId === putBlockInfo.boardId);
        const boardComp: BoardComponent = this.world.getComponent(boardEntity, BoardComponent);

        const isCanPlace = this.canPlaceBlock(putBlockInfo);

        const boardScene: BoardScene = this.world.getComponent(this.world.handlerCenter.boardHandler.getSceneEntity(), BoardScene);

        // 构建回调信息对象
        const backInfo: IPutBlockBackInfo = { isSuccess: isCanPlace, ...putBlockInfo };

        if (!isCanPlace) {
            // 放置失败，直接回调
            this.world.eventBus.emit(ECSEvent.GameEvent.PUT_BLOCK_BACK, backInfo);
            return;
        }
        //放块成功，重置结算数据
        this.world.cacheCenter.caculateData = {};

        const isMirror = boardComp.isCanPut === false; // 镜像放块

        // 获取拖拽形状颜色与类型
        const cellAttrMap: Record<string, { color: CellColor; type: CellType }> = this.collectAttrMap(putBlockInfo);
        backInfo.clearColor = Object.values(cellAttrMap)[0].color;
        // 创建并放置每个小块
        this.placeCells(putBlockInfo, boardComp, boardEntity, cellAttrMap);
        this.cleanupAfterPlace(isMirror, putBlockInfo, boardScene);

        // 发送完成事件
        this.world.eventBus.emit(ECSEvent.GameEvent.PUT_BLOCK_BACK, backInfo);
        if (!isMirror && boardScene.waitPutCells.length === 0) {
            this.world.eventBus.emit(ECSEvent.GameEvent.PRODUCE_BLOCK);
        }
    }

    /**
     * 判断拖拽块是否可以放入棋盘
     * 规则：
     *  1. 目标棋盘存在；
     *  2. 拖拽块全部形状坐标落在棋盘范围内；
     *  3. 对应的 slot/grid 位置当前未被占位。
     *
     * @param info 拖拽块信息
     * @returns 可以放下返回 true，否则 false
     */
    private canPlaceBlock(info: IPutBlockInfo): boolean {
        // 获取目标棋盘实体
        const boardEntity = this.world.query([BoardComponent]).find((entityId) => {
            const b = this.world.getComponent(entityId, BoardComponent)!;
            return b.boardId === info.boardId;
        });
        if (boardEntity === undefined) {
            return false;
        }
        const boardComp: BoardComponent = this.world.getComponent(boardEntity, BoardComponent)!;

        // 确保棋盘格子数据已构建
        if (this.world.getTargets(boardEntity, RelationName.PARENT_CHILD).length === 0) {
            return false;
        }
        const rows = boardComp.rCount;
        const cols = boardComp.cCount;
        // 遍历拖拽块形状，判断每个目标格子是否合法且为空
        for (const rc of info.shape.shape) {
            const targetRow = info.y + rc.r; // 行 (Y 方向)
            const targetCol = info.x + rc.c; // 列 (X 方向)
            // 边界检查
            if (targetRow < 0 || targetRow >= rows || targetCol < 0 || targetCol >= cols) {
                return false;
            }
            // 获取目标 slot 实体并读取其 SlotComponent
            const slotEntityId = this.world.getTargets(boardEntity, RelationName.PARENT_CHILD, `${targetRow}_${targetCol}`)[0];
            const slotComp: SlotComponent = this.world.getComponent(slotEntityId, SlotComponent);
            // 若 slotComp 不存在，视为占用异常
            if (!slotComp) {
                return false;
            }
            // 判断该格子是否占用
            if (this.world.handlerCenter.boardHandler.getSlotOccupy(slotComp)) {
                return false;
            }
        }
        return true;
    }

    /** 收集拖拽形状每个 Cell 的颜色/类型 */
    private collectAttrMap(info: IPutBlockInfo): Record<string, { color: CellColor; type: CellType }> {
        const map: Record<string, { color: CellColor; type: CellType }> = {};
        const children = this.world.getTargets(info.entityId, RelationName.PARENT_CHILD);
        children.forEach((cid) => {
            const c = this.world.getComponent(cid, CellComponent);
            if (c) {
                map[`${c.r}_${c.c}`] = { color: c.oriColor, type: c.type };
            }
        });
        return map;
    }

    /** 根据 attrMap 在目标棋盘创建或搬运 Cell */
    private placeCells(
        info: IPutBlockInfo,
        boardComp: BoardComponent,
        boardEntity: number,
        attrMap: Record<string, { color: CellColor; type: CellType }>,
    ) {
        // 获取全局模板参数
        for (const rc of info.shape.shape) {
            const r = info.y + rc.r;
            const c = info.x + rc.c;
            const slotE = this.world.getTargets(boardEntity, RelationName.PARENT_CHILD, `${r}_${c}`)[0];
            this.world.getComponent(slotE, SlotComponent);
            const origin = attrMap[`${rc.r}_${rc.c}`] ?? {
                color: CellColor.Blue,
                type: TargetType.Normal,
            };

            const cellParam = CELL_TEMPLATE_PARAM[origin.type];
            //临时修复  现在还没有实现更改关系渲染跟着更改关系
            if (cellParam && cellParam['layer'] > 3) {
                const specialOption: CellOption = { color: origin.color, type: origin.type };
                if (cellParam['components']) {
                    for (const comp of cellParam['components']) {
                        const key = comp.name.replace('Component', '').toLowerCase();
                        specialOption[key] = comp.defaultArg;
                    }
                }
                this.world.templeteCenter.createTempleteEntity(TempleteType.Cell, {
                    rc: { r, c },
                    parentSize: { width: boardComp.cCount, height: boardComp.rCount },
                    parentEntity: boardEntity,
                    cellOption: specialOption,
                    slotEntity: slotE,
                });
                // 2. 再创建元素层（normal/plane等）
                this.world.templeteCenter.createTempleteEntity(TempleteType.Cell, {
                    rc: { r, c },
                    parentSize: { width: boardComp.cCount, height: boardComp.rCount },
                    parentEntity: boardEntity,
                    cellOption: { color: origin.color, type: TargetType.Normal },
                    slotEntity: slotE,
                });
            } else {
                this.world.templeteCenter.createTempleteEntity(TempleteType.Cell, {
                    rc: { r, c },
                    parentSize: { width: boardComp.cCount, height: boardComp.rCount },
                    parentEntity: boardEntity,
                    cellOption: { color: origin.color, type: origin.type },
                    slotEntity: slotE,
                });
            }
        }
    }

    /** 主盘放置后销毁拖拽 Shape */
    private cleanupAfterPlace(isMirror: boolean, info: IPutBlockInfo, boardScene: BoardScene) {
        if (!isMirror) {
            const childCells = this.world.getTargets(info.entityId, RelationName.PARENT_CHILD);
            childCells.forEach((cid) => this.world.destroyEntity(cid));
            this.world.destroyEntity(info.entityId);
            boardScene.waitPutCells.splice(boardScene.waitPutCells.indexOf(info.entityId), 1);
        }
    }

    update(dt: number): void {}
}
