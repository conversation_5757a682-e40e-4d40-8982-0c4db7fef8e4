import { IRemoteConfig, ISubGameConfig } from '../../../../typings/AdvGameBridge';
import { Loader } from './Loader';

/**
 * AdvGameBridge 在子游戏中使用桥接类
 * 提供子游戏与主游戏之间的通信接口
 */
export class SubGameBridge {
    private static bridge: any = null;
    private static isInitialized: boolean = false;
    private static remoteConfig: IRemoteConfig = null;
    /**默认bundle名称 */
    public static readonly DEFAULT_BUNDLE_NAME = 'advres';
    public static readonly NEED_REMOTE = false;
    
    /**
     * 获取桥接实例
     */
    private static getBridge(): any {
        if (!this.bridge) {
            // 从window对象获取桥接类实例
            this.bridge = (window as any).AdvGameBridge;
            
            if (!this.bridge) {
                if (this.NEED_REMOTE) {
                    console.error('[SubGameBridge] AdvGameBridge not found on window object!');
                }
                return null;
            }
        }
        
        return this.bridge;
    }
    
    /**
     * 子游戏初始化时调用
     */
    public static async onSubGameStart(): Promise<boolean> {
        const bridge = this.getBridge();
        if (!bridge) {
            return false;
        }
        
        try {
            // 获取主游戏传递的基础数据
            const baseData = bridge.getBaseData();
            console.log('[SubGameBridge] Received base data from main game:', baseData);
            
            // 获取远程配置
            this.remoteConfig = bridge.getRemoteConfig();
            console.log('[SubGameBridge] Remote config:', this.remoteConfig);
            
            // 设置主游戏消息监听
            this.setupMainGameMessageListener();
            
            // 使用主游戏数据初始化子游戏
            this.initSubGameWithMainData(baseData);
            
            this.isInitialized = true;
            return true;
            
        } catch (error) {
            console.error('[SubGameBridge] Initialization failed:', error);
            return false;
        }
    }
    
    /**
     * 设置主游戏消息监听
     */
    private static setupMainGameMessageListener(): void {
        const bridge = this.getBridge();
        if (!bridge || typeof bridge.onMessageFromMainGame !== 'function') {
            return;
        }
        
        // 监听来自主游戏的消息
        bridge.onMessageFromMainGame((type, data) => {
            if (type === 'test') {
                    // todo 有需求可以添加
            }
        });
        console.log('[SubGameBridge] Main game message listener set up successfully');
    }
    
    /**
     * 使用主游戏数据初始化子游戏
     */
    private static initSubGameWithMainData(data: any): void {
        console.log('[SubGameBridge] Initializing sub game with main data:', data);
        // todo 有需求可以添加
    }
    
    /**
     * 获取远程配置
     */
    public static getRemoteConfig(): IRemoteConfig {
        if (!this.remoteConfig) {
            const bridge = this.getBridge();
            if (bridge && typeof bridge.getRemoteConfig === 'function') {
                this.remoteConfig = bridge.getRemoteConfig();
            }
        }
        return this.remoteConfig;
    }
    
    /**
     * 获取子游戏配置
     */
    public static getSubGameConfig(bundleName: string): ISubGameConfig | null {
        const bridge = this.getBridge();
        if (bridge && typeof bridge.getSubGameConfig === 'function') {
            return bridge.getSubGameConfig(bundleName);
        }
        return null;
    }
    
    
    /**
     * 子游戏向主游戏发送消息
     */
    public static sendToMainGame(type: string, data?: any): void {
        const bridge = this.getBridge();
        
        if (bridge && typeof bridge.sendToMainGame === 'function') {
            bridge.sendToMainGame(type, data);
            console.log(`[SubGameBridge] Sent message to main game:`, { type, data });
        } else {
            console.warn('[SubGameBridge] Cannot send message to main game - bridge not available');
        }
    }
    
    /**
     * 返回主游戏
     */
    public static returnToMainGame(): void {
        const bridge = this.getBridge();
        if (bridge && typeof bridge.returnToMainGame === 'function') {
            // 发送返回通知
            this.sendToMainGame('beforeReturn', { timestamp: Date.now() });
            
            // 执行返回
            bridge.returnToMainGame();
            console.log('[SubGameBridge] Returning to main game');
        } else {
            console.warn('[SubGameBridge] Cannot return to main game - bridge not available');
        }
    }
    
    /**
     * 检查是否已初始化
     */
    public static isReady(): boolean {
        return this.isInitialized && this.getBridge() !== null;
    }
    
    /**
     * 获取桥接版本信息
     */
    public static getBridgeVersion(): string {
        const bridge = this.getBridge();
        if (bridge && typeof bridge.getBridgeVersion === 'function') {
            return bridge.getBridgeVersion();
        }
        return 'unknown';
    }
    
    /**
     * 销毁桥接连接
     */
    public static destroy(): void {
        const bridge = this.getBridge();
        if (bridge && typeof bridge.offMessageFromMainGame === 'function') {
            bridge.offMessageFromMainGame();
        }
        
        this.bridge = null;
        this.isInitialized = false;
        this.remoteConfig = null;
        console.log('[SubGameBridge] Bridge destroyed');
    }



   /**
     * 获取子游戏配置
     * @param bundleName bundle名称，如果为空则使用默认值
     * @returns ISubGameConfig 配置对象
     */
   public static getBundleConfig(bundleName?: string): ISubGameConfig {
        const finalBundleName = bundleName || SubGameBridge.DEFAULT_BUNDLE_NAME;
        
        // 如果不需要远程，返回基本配置
        if (!SubGameBridge.NEED_REMOTE) {
            return {
                bundleName: finalBundleName,
                version: undefined,
                entryScene: undefined
            };
        }
        
        // 获取远程配置
        const remoteConfig = SubGameBridge.getRemoteConfig();
        
        // 获取bundle配置
        const bundleConfig = SubGameBridge.getSubGameConfig(finalBundleName);
        
        // 如果没有远程配置，返回基本配置
        if (!remoteConfig) {
            return {
                bundleName: finalBundleName,
                version: undefined,
                entryScene: undefined
            };
        }
        
        // 组装完整的远程bundle地址
        const remoteBundleName = `${remoteConfig.baseUrl}${finalBundleName}`;
        
        // 返回完整配置
        return {
            bundleName: remoteBundleName,
            version: bundleConfig?.version || undefined,
            entryScene: bundleConfig?.entryScene
        };
    }

    /**
     * 封装的异步加载资源方法（通过bundle）
     * @param bundleName bundle名称
     * @param url 资源路径
     * @param type 资源类型（可选）
     * @param options bundle选项（包含MD5版本等）
     * @returns Promise<T>
     */
    public static asyncLoadByBundle<T extends cc.Asset = cc.Asset>(bundleName: string, url: string, type?: typeof cc.Asset, options?: Record<string, any>): Promise<T> {
        const bundleConfig = this.getBundleConfig(bundleName);
        
        // 合并版本信息到options中
        const finalOptions = { ...options };
        if (bundleConfig.version && !finalOptions.version) {
            finalOptions.version = bundleConfig.version;
        }
        
        if (type) {
            return Loader.asyncLoadByBundle<T>(bundleConfig.bundleName, url, type, finalOptions);
        }
        return Loader.asyncLoadByBundle<T>(bundleConfig.bundleName, url, undefined, finalOptions);
    }

    public static async asyncLoad<T extends cc.Asset>(paths: string, type: typeof cc.Asset): Promise<T> {
        console.error(`SubGameBridge.asyncLoad: bundleName is not specified for file '${paths}'. Please verify the file path and use asyncLoadByBundle instead.`);
        return new Promise<T>((resolve, reject) => {
            reject();
        });
    }

    /**
     * 封装的同步加载资源方法（通过bundle）
     * @param bundleName bundle名称
     * @param url 资源路径
     * @param type 资源类型
     * @param callback 回调函数
     * @param options bundle选项（包含MD5版本等）
     */
    public static loadByBundle<T extends cc.Asset = cc.Asset>(
        bundleName: string,
        url: string,
        type: typeof cc.Asset,
        callback: (err: Error, resource: T) => void,
        options?: Record<string, any>
    ): void {
        const bundleConfig = this.getBundleConfig(bundleName);
        
        // 合并版本信息到options中
        const finalOptions = { ...options };
        if (bundleConfig.version && !finalOptions.version) {
            finalOptions.version = bundleConfig.version;
        }
        
        Loader.loadByBundle(bundleConfig.bundleName, url, type, (err: Error, asset: cc.Asset) => {
            callback(err, asset as T);
        }, finalOptions);
    }

    /**
     * 封装的加载bundle方法
     * @param bundleName bundle名称
     * @param optionsOrCallback 选项或回调函数
     * @param callback 回调函数（当第二个参数是options时使用）
     */
    public static loadBundle(bundleName: string, optionsOrCallback?: Record<string, any> | ((err: Error, bundle: cc.AssetManager.Bundle) => void), callback?: (err: Error, bundle: cc.AssetManager.Bundle) => void): void {
        const bundleConfig = this.getBundleConfig(bundleName);
        
        // 处理参数
        let options: Record<string, any> = {};
        let onComplete: (err: Error, bundle: cc.AssetManager.Bundle) => void;
        
        if (typeof optionsOrCallback === 'function') {
            onComplete = optionsOrCallback as (err: Error, bundle: cc.AssetManager.Bundle) => void;
        } else {
            options = optionsOrCallback || {};
            onComplete = callback;
        }
        
        // 如果bundle配置中有版本信息，添加到options中
        if (bundleConfig.version && !options.version) {
            options.version = bundleConfig.version;
        }
        
        // 根据参数数量调用不同的重载
        if (Object.keys(options).length === 0) {
            Loader.loadBundle(bundleConfig.bundleName, onComplete);
        } else {
            Loader.loadBundle(bundleConfig.bundleName, options, onComplete);
        }
    }

    /**
     * 封装的bundle预加载方法
     * @param bundle bundle对象
     * @param url 资源路径
     * @param type 资源类型
     * @param callback 回调函数
     */
    public static bundlePreload(bundle: cc.AssetManager.Bundle, url: string, type: typeof cc.Asset, callback: (err: Error) => void): void {
        Loader.bundlePreload(bundle, url, type, callback);
    }

    /**
     * 异步加载bundle方法
     * @param bundleName bundle名称
     * @param options 选项（可选）
     * @returns Promise<cc.AssetManager.Bundle>
     */
    public static async asyncLoadBundle(bundleName: string, options?: Record<string, any>): Promise<cc.AssetManager.Bundle> {
        const bundleConfig = this.getBundleConfig(bundleName);
        
        // 如果bundle配置中有版本信息，添加到options中
        const finalOptions = { ...options };
        if (bundleConfig.version && !finalOptions.version) {
            finalOptions.version = bundleConfig.version;
        }
        
        return new Promise<cc.AssetManager.Bundle>((resolve, reject) => {
            if (Object.keys(finalOptions).length === 0) {
                Loader.loadBundle(bundleConfig.bundleName, (err, bundle) => {
                    if (err) {
                        reject(err);
                        return;
                    }
                    resolve(bundle);
                });
            } else {
                Loader.loadBundle(bundleConfig.bundleName, finalOptions, (err, bundle) => {
                    if (err) {
                        reject(err);
                        return;
                    }
                    resolve(bundle);
                });
            }
        });
    }

    /**
     * 异步bundle预加载方法
     * @param bundle bundle对象
     * @param url 资源路径
     * @param type 资源类型
     * @returns Promise<void>
     */
    public static async asyncBundlePreload(bundle: cc.AssetManager.Bundle, url: string, type: typeof cc.Asset): Promise<void> {
        return new Promise<void>((resolve, reject) => {
            Loader.bundlePreload(bundle, url, type, (err) => {
                if (err) {
                    reject(err);
                    return;
                }
                resolve();
            });
        });
    }

    /**
     * 封装的UI显示方法，自动处理bundleName的远程地址
     * @param config 预制体配置对象
     * @param parent 父节点（可选）
     * @returns Promise<cc.Node>
     */
    public static async showUI(config: any, parent?: cc.Node): Promise<cc.Node> {
        const bundleConfig = this.getBundleConfig(config.bundleName);
        // 创建新的配置对象，避免修改原始配置
        const processedConfig = {
            ...config,
            bundleName: bundleConfig.bundleName,
        };
        // 如果有版本信息，添加到配置中
        if (bundleConfig.version && !processedConfig.version) {
            processedConfig.version = bundleConfig.version;
        }
        return falcon.UI.show(processedConfig, parent);
    }

    /**
     * 封装的龙骨动画渲染方法，自动处理bundleName的远程地址和版本信息
     * @param bundleName bundle名称
     * @param dragonBonesArmatureDisplay 龙骨显示组件
     * @param dragonAssetUrl 龙骨资源URL
     * @param dragonAssetAtlasUrl 龙骨图集URL
     * @param armatureName 骨架名称
     * @param animName 动画名称
     * @param playtimes 播放次数
     * @param completeRemove 完成后是否移除
     * @param options bundle选项（包含MD5版本等）
     */
    public static renderDragonbonesByBundle(
        bundleName: string,
        dragonBonesArmatureDisplay: dragonBones.ArmatureDisplay,
        dragonAssetUrl: string,
        dragonAssetAtlasUrl: string,
        armatureName: string,
        animName: string,
        playtimes: number = 1,
        completeRemove: boolean = true,
        options?: Record<string, any>
    ): void {
        const bundleConfig = this.getBundleConfig(bundleName);
        
        // 合并版本信息到options中
        const finalOptions = { ...options };
        if (bundleConfig.version && !finalOptions.version) {
            finalOptions.version = bundleConfig.version;
        }
        
        // 处理参数重载
        if (typeof completeRemove === 'object' && completeRemove !== null) {
            // 如果completeRemove是对象，说明它实际是options
            const mergedOptions = { ...(completeRemove as Record<string, any>), ...finalOptions };
            Loader.renderDragonbonesByBundle(
                bundleConfig.bundleName,
                dragonBonesArmatureDisplay,
                dragonAssetUrl,
                dragonAssetAtlasUrl,
                armatureName,
                animName,
                playtimes,
                true, // 默认值
                mergedOptions
            );
        } else {
            Loader.renderDragonbonesByBundle(
                bundleConfig.bundleName,
                dragonBonesArmatureDisplay,
                dragonAssetUrl,
                dragonAssetAtlasUrl,
                armatureName,
                animName,
                playtimes,
                completeRemove as boolean,
                finalOptions
            );
        }
    }
}
