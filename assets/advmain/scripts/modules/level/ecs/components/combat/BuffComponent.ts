import { Component } from "../../cores/Component";
import { IBaseActionData, IBehaviorData } from "../../combat/core/BehaviorBase";
import { BuffBehaviorId } from "../../behavior/buff/BuffBehaviorBase";

/**
 * Buff数据
 */
export interface BuffData<T extends IBehaviorData = IBehaviorData> extends IBaseActionData<T> {
    /**Buff唯一标识 */
    insId: number;        
    /**拥有者实体ID */
    ownerEntityId: number; 
    /**施法者实体ID */
    casterEntityId: number; 
    /**Buff行为Id */
    buffBehaviorId: BuffBehaviorId;    
    /**叠加标识 */
    stackId: string;        
    /**剩余持续时间 */
    remainingTime: number;  
    /**叠加层数 */
    stackCount: number;    
    /**上次触发时间 */
    lastTickTime: number;  
    /**当前效果值 */
    value: number;  
    /**是否生效中 */
    isActive: boolean;     
}
/**
 * Buff组件
 * 用于存储和管理实体的buff列表
 */
export class BuffComponent extends Component {
    static type = 'BuffComponent';
    public buffList: BuffData[];
    constructor() {
        super();
        this.buffList = [];
    }
} 