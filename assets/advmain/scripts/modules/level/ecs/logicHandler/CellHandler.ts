import { HandlerBase } from '../cores/HandlerBase';
import { TargetType, CELL_TEMPLATE_PARAM, GemTypes } from '../define/BoardDefine';

export class CellHandler extends HandlerBase {
    /**是否是块 */
    isCell(targetType: TargetType) {
        return !!CELL_TEMPLATE_PARAM[targetType];
    }
    /**是否是宝石块
     * @param type 目标类型
     * @returns 是否是宝石块
     */
    isGemCell(type: TargetType): boolean {
        return GemTypes.includes(type);
    }
}
