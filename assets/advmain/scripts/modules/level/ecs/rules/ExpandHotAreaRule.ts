import { BoardScene } from '../components/board/BoardScene';
import DragComponent from '../components/DragComponent';
import NodeComponent from '../components/NodeComponent';
import { ECSEvent } from '../GameEvent';
import { ECSRuleBase, IECSRuleConfig } from './core/ECSRuleBase';

export interface IExpandHotAreaRuleConfig extends IECSRuleConfig {
    /** 热区扩大倍数 */
    expandMultiplier: number;
    /** 原始热区宽度 */
    originalWidth: number;
    /** 原始热区高度 */
    originalHeight: number;
}

/**
 * 扩大热区规则
 * 放置中间方块后，左右方块的热区变大
 */
export class ExpandHotAreaRule extends ECSRuleBase<IExpandHotAreaRuleConfig, ECSEvent.GameEvent.PUT_BLOCK_BACK> {
    bindEventData() {
        return ECSEvent.GameEvent.PUT_BLOCK_BACK;
    }
    canExecute(): boolean {
        // 只有放置成功时才执行
        console.log('ExpandHotAreaRulecanExecute,info:', this.eventData);
        return this.eventData.isSuccess;
    }

    execute(): void {
        const info = this.eventData;
        const world = this.world;
        console.log('ExpandHotAreaRule执行，info:', info);

        const sceneEntity = world.handlerCenter.boardHandler.getSceneEntity();
        const boardScene = world.getComponent(sceneEntity, BoardScene);

        if (!boardScene) {
            console.log('boardScene不存在');
            return;
        }

        console.log('放置后剩余waitPutCells长度:', boardScene.waitPutCells.length);

        // 只有当剩余2个方块时才可能是放置了中间块
        if (boardScene.waitPutCells.length !== 2) {
            console.log('剩余块数不为2，不是首次放置场景');
            return;
        }

        // 获取剩余两个方块的位置信息
        const remainingShapes = boardScene.waitPutCells
            .map((shapeId) => {
                const nodeComp = world.getComponent(shapeId, NodeComponent);
                return {
                    entityId: shapeId,
                    x: nodeComp?.x || 0,
                    dragComp: world.getComponent(shapeId, DragComponent),
                };
            })
            .filter((shape) => shape.dragComp);

        if (remainingShapes.length !== 2) {
            console.log('找不到2个有效的剩余方块');
            return;
        }

        // 按x坐标排序
        remainingShapes.sort((a, b) => a.x - b.x);
        const [leftShape, rightShape] = remainingShapes;

        console.log('剩余方块位置 - 左:', leftShape.x, '右:', rightShape.x);

        // 判断是否为左右两个方块（中间的被移除）
        // 通常生成的方块位置是: 左(-ShapeMargin), 中(0), 右(+ShapeMargin)
        // 如果剩余的是左右两个，说明中间的被放置了
        const isLeftRightRemaining = leftShape.x < 0 && rightShape.x > 0;

        if (isLeftRightRemaining) {
            console.log('判定为中间方块已放置，扩大左右方块热区');
            console.log('左侧热区扩大前:', leftShape.dragComp.hotArea);
            console.log('右侧热区扩大前:', rightShape.dragComp.hotArea);

            // 扩大左右方块的热区
            this.expandHotArea(leftShape.dragComp);
            this.expandHotArea(rightShape.dragComp);

            console.log('左侧热区扩大后:', leftShape.dragComp.hotArea);
            console.log('右侧热区扩大后:', rightShape.dragComp.hotArea);
        } else {
            console.log('判定为非中间方块放置，剩余方块位置:', leftShape.x, rightShape.x);
        }
    }

    /** 扩大热区 */
    private expandHotArea(dragComp: DragComponent): void {
        const { expandMultiplier } = this.config;

        // 基于当前热区尺寸计算新尺寸
        const currentHotArea = dragComp.hotArea;
        const newWidth = currentHotArea.width * expandMultiplier;
        const newHeight = currentHotArea.height * expandMultiplier;

        // 更新热区，保持原有的x,y偏移（通常是0,0）
        dragComp.hotArea = {
            x: currentHotArea.x,
            y: currentHotArea.y,
            width: newWidth,
            height: newHeight,
        };

        console.log('热区已扩大:', dragComp.hotArea);
    }
}
