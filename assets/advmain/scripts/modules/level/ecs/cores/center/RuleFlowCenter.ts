import { FlowModule, FlowNode, RuleNode } from "../../../../../base/rule/FlowModule";
import { ConfigCenter } from "./ConfigCenter";
import { IECSFlowConfig } from "../../systems/RuleSystem";
import { ECSRuleRegistry } from "../../registry/ECSRuleRegistry";
import { ECSExecuteContext, ECSRuleBase } from "../../rules/core/ECSRuleBase";

/**
 * 规则流中心
 */
export class RuleFlowCenter {
    /**规则流模块 */
    public flowModule: FlowModule;
    /**配置中心 */
    public configCenter: ConfigCenter;
    constructor(configCenter: ConfigCenter) {
        this.flowModule = new FlowModule();
        this.configCenter = configCenter;
        this.flowModule.registry.registerRuleMap(ECSRuleRegistry);
    }
    /**
     * 注册规则流
     * @param event 事件
     * @param ecsFlowConfig 规则流配置
     */
    public registerFlow(event: string, ecsFlowConfig: IECSFlowConfig) {
        const flowNode = this.convertToFlowNode(ecsFlowConfig);
        const flow = this.flowModule.buildFlow(flowNode);
        this.flowModule.registerFlow(event, flow);  
    }
    /**
     * 创建规则
     * @param ruleId 规则ID
     * @param config 配置
     * @returns 规则
     */
    public createRule(ruleId: string): ECSRuleBase {
        const ruleConfig = this.configCenter.getRuleConfig(ruleId);
        if (!ruleConfig) {
            console.error(`规则配置${ruleId}为空`);
            return null;
        }
        const rule = this.flowModule.registry.createRule(ruleConfig.ruleType, ruleConfig) as ECSRuleBase;
        return rule;
    }
    /**
     * 将IECSFlowConfig转换为FlowNode
     * @param config IECSFlowConfig配置
     * @returns FlowNode
     */
    private convertToFlowNode(config: IECSFlowConfig): FlowNode {
        if (typeof config === 'string') {
            // 如果是字符串，创建一个包含规则节点的序列流
            const flowNode: FlowNode = {
                flowType: 'sequence',
                flowConfig: {},
                children: [this.createRuleNode(config)]
            };
            return flowNode;
        }

        // 创建流节点
        const flowNode: FlowNode = {
            flowType: config.type,
            flowConfig: {},
            children: []
        };

        // 处理子节点
        flowNode.children = config.children.map(child => {
            if (typeof child === 'string') {
                return this.createRuleNode(child);
            }
            return this.convertToFlowNode(child);
        });

        return flowNode;
    }

    /**
     * 创建规则节点
     * @param ruleId 规则ID
     * @returns RuleNode
     */
    private createRuleNode(ruleId: string): RuleNode {
        const ruleConfig = this.configCenter.getRuleConfig(ruleId);
        const ruleNode: RuleNode = {
            ruleType: ruleConfig.ruleType,
            ruleConfig: this.configCenter.getRuleConfig(ruleId),
            replaceRuleType: null,
            replaceRuleConfig: null
        };
        if(ruleConfig.replaceRuleId){
            ruleNode.replaceRuleConfig = this.configCenter.getRuleConfig(ruleConfig.replaceRuleId);
            ruleNode.replaceRuleType = ruleNode.replaceRuleConfig.ruleType
        }
        return ruleNode;
    }

    /**
     * 执行规则流
     * @param event 事件
     * @param data 数据
     */
    public executeFlow(event: string, data: ECSExecuteContext) {
        if (!this.flowModule.hasFlow(event)) {
            return;
        }
        this.flowModule.executeFlow(event, data);
    }
}
