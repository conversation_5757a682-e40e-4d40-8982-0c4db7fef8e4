import ProduceBlockAlgorithmComponent from '../../components/special/ProduceBlockAlgorithmComponent';
import { ECSRuleBase, IECSRuleConfig } from '../core/ECSRuleBase';
import { ECSEvent } from '../../GameEvent';
import { IProduceBlockEnd } from '../../GameEventData';
import { AlgoClassTypeConst } from '../../../../algorithm/list/algoSDK/core/puzzlecore';

export interface IRevivePreviewBlocksRuleConfig extends IECSRuleConfig {
    /**算法ID */
    algorithmId: number;
    /**是否需要加载完 */
    needLoad?: boolean;
    /**满足？收集进度弹出复活 */
    percent?: number;
}

/**
 * 复活预览块规则
 * 在复活弹窗显示前，先计算复活块并缓存结果
 */
export class RevivePreviewBlocksRule extends ECSRuleBase<IRevivePreviewBlocksRuleConfig> {

    canExecute(): boolean {
        // 使用工具类进行统一的复活条件检查
        if (!this.world.utilCenter.reviveUtil.canRevive(this.config)) {
            return false;
        }

        return true;
    }

    execute(): void {
        const world = this.world;

        try {
            // 标记这是复活预览出块
            const reviveComp = world.utilCenter.reviveUtil.getReviveComponent();
            if (!reviveComp) {
                console.error('RevivePreviewBlocksRule: 找不到ReviveComponent');
                return;
            }
            reviveComp.isPreviewMode = true;

            // 监听出块完成事件，缓存结果
            world.eventBus.once(ECSEvent.BaseEvent.PRODUCE_BLOCK_END, this.onPreviewBlocksGenerated, this);

            // 创建算法组件，触发现有的出块系统
            // BlockAlgorithmSystem 会监听到组件添加，然后触发 PRODUCE_BLOCK_START 事件
            const entityId = world.createEntity();
            const algorithmId = this.config.algorithmId || AlgoClassTypeConst.algo_revive_clear;
            world.addComponent(entityId, ProduceBlockAlgorithmComponent, algorithmId);

            console.log('RevivePreviewBlocksRule: 开始预览复活块计算', algorithmId);

        } catch (error) {
            console.error('RevivePreviewBlocksRule: 执行失败', error);

            // 出错时重置预览模式
            const reviveComp = world.utilCenter.reviveUtil.getReviveComponent();
            if (reviveComp) {
                reviveComp.isPreviewMode = false;
            }
        }
    }

    private onPreviewBlocksGenerated(result: IProduceBlockEnd): void {
        const world = this.world;
        const reviveComp = world.utilCenter.reviveUtil.getReviveComponent();

        if (!reviveComp) {
            console.error('RevivePreviewBlocksRule: 找不到ReviveComponent');
            return;
        }

        // 缓存复活块数据
        reviveComp.cachedReviveBlocks = result;
        reviveComp.isPreviewMode = false;

        // 触发预览完成事件
        world.eventBus.emit(ECSEvent.GameEvent.REVIVE_PREVIEW_COMPLETE, result);

        console.log('RevivePreviewBlocksRule: 复活块预览完成', result.blockIds);
    }
}
