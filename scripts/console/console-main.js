let backBtn = document.getElementById('backBtn');
backBtn.onclick = (e) => {
    if (window.isNative) {
        document.location = "block-blast-console-scheme://close";
    } else {
        parent.postMessage('close', "*");
    }
}

var vConsole = new window.VConsole();
window.addEventListener('message', (e) => {
    const logs = e.data;
    if (logs) {
        for (let i = 0; i < logs.length; i++) {
            const { type, data } = logs[i];
            console[type](...data);
        }
    }
});

function onMessage(dataStr) {
    try {
        const logs = JSON.parse(dataStr);
        if (logs) {
            for (let i = 0; i < logs.length; i++) {
                const { type, data } = logs[i];
                console[type](...data);
            }
        }
    } catch (error) {
        console.error(`接收游戏端消息报错！`, error);
    }
}

function init(data) {
    window.isNative = data.isNative;
}