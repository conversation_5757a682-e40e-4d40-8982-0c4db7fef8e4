
/**
 * 游戏数据信息（存储全局数据信息）
 * （1）：千万不要将特性相关的数据存进来
 * （2）：无尽模式数据必须与关卡模式数据分离
 */
class GameInfo {
    /** 游戏版本 */
    get gameVersion() {
        return "9.9.9";
    }

    /** 进入游戏的次数 */
    get gameEntryCount(): number {
        return falcon.storage.getItem(`gameEntryCount`, 0);
    }

    /** 首次进入游戏的时间 */
    get gameFirstEntryTime(): number {
        return falcon.storage.getItem(`gameFirstEntryTime`, 0);
    }
}

export const gameInfo = new GameInfo();