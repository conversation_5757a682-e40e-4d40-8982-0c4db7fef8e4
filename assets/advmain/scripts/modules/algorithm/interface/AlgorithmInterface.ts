export interface IAlgorithmResult {
    //排面
    faceBlocks: number[][];
    // 出块
    blockList: number[];
    // 算法
    algorithmType: number;
    // 时间
    time?: number;
}

/** 棋盘数据接口 */
export interface IAlgorithmBoardData {
    /**盘面数据 */
    boardOccupy?: number[][];
    /**棋盘行数 */
    rows?: number;
    /**棋盘列数 */
    cols?: number;
    /**棋盘ID */
    boardId?: string;
    /**算法ID */
    algorithmId?: number;
}

export interface IAlgorithmDataInfo {
    // 历史出块信息
    blocksList: number[];
}
