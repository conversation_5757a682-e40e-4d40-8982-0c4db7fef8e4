import { World } from "../../../../cores/World";
import { SkillData, SkillState } from "../../../component/Skill";
import { IBaseSkillConfig } from "../../../../config/SkillConfig";
import { BehaviorBase } from "../../../core/BehaviorBase";
import { IActionContext } from "../../../core/ActionContext";

/**
 * 技能行为接口
 * 定义了技能的完整生命周期，支持不同类型的技能
 * 继承自BehaviorBase，与buff系统保持一致
 */
export abstract class SkillBehaviorBase<TConfig extends IBaseSkillConfig> extends BehaviorBase<TConfig> {
    /**
     * 技能添加回调 - 适用于被动技能
     * 当技能被添加到实体时调用，用于初始化行为数据和应用被动效果
     * @param skillData 技能数据
     */
    onAdd?(skillData: SkillData): void;

    /**
     * 技能移除回调 - 适用于被动技能
     * 当技能从实体移除时调用，用于清理行为数据和移除被动效果
     * @param skillData 技能数据
     */
    onRemove?(skillData: SkillData): void;

    /**
     * 技能更新回调 - 适用于持续效果
     * 每帧调用，用于更新技能状态（适用于持续效果的技能）
     * @param skillData 技能数据
     * @param deltaTime 时间增量
     * @returns 技能当前状态，用于生命周期管理
     */
    onUpdate?(skillData: SkillData, deltaTime: number): SkillState;

    /**
     * 技能施法回调 - 适用于主动技能
     * 当开始使用技能时调用，行为决定技能的执行状态
     * @param skillData 技能数据
     * @param context 动作上下文，包含目标、位置等信息（由具体行为决定如何使用）
     * @returns 技能执行后的状态（瞬发技能可返回Inactive表示立即完成）
     */
    onCast?(skillData: SkillData, context?: IActionContext): SkillState;

    /**
     * 技能完成回调 - 适用于主动技能
     * 当技能使用结束时调用，用于清理资源、停止特效、重置状态等
     * @param skillData 技能数据
     */
    onComplete?(skillData: SkillData): void;
} 