import { LauncherData } from "../../../component/Launcher";
import { BehaviorBase } from "../../../core/BehaviorBase";
import { IActionContext } from "../../../core/ActionContext";

/**
 * 发射器行为基类
 * 定义发射器的生命周期回调接口
 */
export abstract class LauncherBehaviorBase<TConfig extends IBaseLauncherConfig> extends BehaviorBase<TConfig> {

    /**
     * 发射器激活回调
     * 当发射器开始发射时调用，负责初始化和执行发射逻辑
     * @param launcherData 发射器数据
     * @param context 动作上下文（包含所有创建时的信息）
     * @returns 是否成功激活
     */
    onLaunch?(launcherData: LauncherData, context?: IActionContext): boolean;

    /**
     * 发射器更新回调
     * 每帧调用，用于更新发射器状态
     * @param launcherData 发射器数据
     * @param deltaTime 时间增量
     * @returns 是否已完成（true表示发射器应该被销毁）
     */
    onUpdate?(launcherData: LauncherData, deltaTime: number): boolean;

    /**
     * 发射器完成回调
     * 当发射器完成其任务时调用
     * @param launcherData 发射器数据
     */
    onComplete?(launcherData: LauncherData): void; 
    
   /**
    * 发射器销毁回调
    * 当发射器被销毁时调用，用于清理资源
    * @param launcherData 发射器数据
    */
    onDestroy?(launcherData: LauncherData): void;
}

export { }; 