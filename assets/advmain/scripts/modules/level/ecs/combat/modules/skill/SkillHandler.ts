import { UtilBase } from "../../../cores/UtilBase";
import { SkillData, Skill, SkillState } from "../../component/Skill";
import { CooldownType } from "../../../config/SkillCooldownConfig";
import { IBehaviorData } from "../../core/BehaviorBase";
import { SkillFactory } from "./core/SkillFactory";
import { IActionContext } from "../../core/ActionContext";

/**
 * 技能处理器
 * 负责技能的基础管理，具体的状态和逻辑完全由行为实现
 */
export class SkillHandler extends UtilBase<SkillFactory> {
    // ================================
    // 技能基础管理
    // ================================

    /**
     * 添加技能
     * @param ownerEntityId 技能拥有者实体ID
     * @param skillId 技能配置ID
     * @param casterEntityId 技能施法者实体ID（可选，默认等于ownerEntityId）
     * @returns 成功时返回技能数据，失败时返回null
     */
    addSkill(ownerEntityId: number, skillId: string, casterEntityId?: number): SkillData | null {
        // 如果没有指定施法者，默认施法者就是拥有者
        const actualCaster = casterEntityId ?? ownerEntityId;

        const component = this.world.getComponent<Skill>(ownerEntityId, Skill);
        if (!component) return null;

        if (component.hasSkill(skillId)) {
            // 如果已经有这个技能，更新施法者并返回
            const existingSkill = component.findSkill(skillId);
            if (existingSkill) {
                existingSkill.casterEntityId = actualCaster;
                return existingSkill;
            }
        }

        // 创建技能数据
        const skillConfig = this.world.config.getSkillConfig(skillId);
        if (!skillConfig) return null;

        const skillData: SkillData = {
            skillId: skillId,
            ownerEntityId: ownerEntityId,
            casterEntityId: actualCaster,
            state: SkillState.Inactive,
            lastCastTime: 0,
            lastCompleteTime: 0,
            behaviorData: {}
        };

        // 添加到组件
        const success = component.addSkill(skillData);
        if (!success) return null;

        // 激活技能行为
        this.activateSkillBehavior(skillData);

        return skillData;
    }

    /**
     * 移除技能
     * @param skillData 技能数据
     */
    removeSkill(skillData: SkillData): SkillData | undefined {
        const component = this.world.getComponent<Skill>(skillData.ownerEntityId, Skill);
        if (!component) return undefined;

        // 停用技能行为
        this.deactivateSkillBehavior(skillData);

        // 从组件中移除技能
        return component.removeSkill(skillData.skillId);
    }

    /**
     * 根据ID移除技能 - 便捷方法
     * @param ownerEntityId 技能拥有者实体ID
     * @param skillId 技能配置ID
     * @returns 被移除的技能数据,如果不存在则返回undefined
     */
    removeSkillById(ownerEntityId: number, skillId: string): SkillData<IBehaviorData> | undefined {
        const component = this.world.getComponent<Skill>(ownerEntityId, Skill);
        if (!component) return undefined;

        const skillData = component.findSkill(skillId);
        if (!skillData) return undefined;

        return this.removeSkill(skillData);
    }

    /**
     * 初始化已存在的技能
     * 用于处理技能组件添加时就已经存在的技能数据
     * @param skillData 技能数据
     */
    public initExistingSkill(skillData: SkillData): void {
        console.log(`初始化已存在的技能: ${skillData.skillId} (拥有者: ${skillData.ownerEntityId}, 施法者: ${skillData.casterEntityId})`);
        this.activateSkillBehavior(skillData);
    }

    /**
     * 激活技能行为 - 核心逻辑
     * 统一处理技能行为的初始化，避免重复代码
     * @param skillData 技能数据
     */
    private activateSkillBehavior(skillData: SkillData): void {
        const skillBehavior = this.behavior.getSkillBehaviorById(skillData.skillId);
        if (skillBehavior && skillBehavior.onAdd) {
            skillBehavior.onAdd(skillData);
        }
    }

    /**
     * 停用技能行为 - 核心逻辑
     * 统一处理技能行为的清理，与activateSkillBehavior对应
     * @param skillData 技能数据
     */
    private deactivateSkillBehavior(skillData: SkillData): void {
        const skillBehavior = this.behavior.getSkillBehaviorById(skillData.skillId);
        if (skillBehavior && skillBehavior.onRemove) {
            skillBehavior.onRemove(skillData);
        }
    }

    // ================================
    // 技能执行与生命周期管理
    // ================================

    /**
     * 增强的 castSkill - 包含冷却检查和自动状态管理
     * @param skillData 技能数据
     * @param context 施法上下文，包含目标、位置等信息
     * @returns 是否成功施放技能
     */
    castSkill(skillData: SkillData, context?: IActionContext): boolean {
        // 1. 检查冷却（只对有冷却配置的技能检查）
        if (this.isSkillOnCooldown(skillData)) {
            console.warn(`技能 ${skillData.skillId} 仍在冷却中，剩余 ${this.getCooldownRemaining(skillData)}ms`);
            return false;
        }

        // 2. 调用行为的施法逻辑
        const skillBehavior = this.behavior.getSkillBehaviorById(skillData.skillId);
        if (!skillBehavior || !skillBehavior.onCast) return false;

        const resultState = skillBehavior.onCast(skillData, context);

        // 3. 如果技能执行成功，更新状态和冷却
        if (resultState !== skillData.state) {
            this.startCooldown(skillData);
            skillData.state = resultState;

            // 如果技能直接完成（瞬发技能），调用完成回调
            if (resultState === SkillState.Inactive) {
                this.completeSkill(skillData);
            }

            return true;
        }

        return false;
    }

    /**
     * 完成技能 - 技能使用结束时调用
     * @param skillData 技能数据
     */
    completeSkill(skillData: SkillData): void {
        const skillBehavior = this.behavior.getSkillBehaviorById(skillData.skillId);
        if (skillBehavior && skillBehavior.onComplete) {
            skillBehavior.onComplete(skillData);
        }

        // 记录完成时间（用于基于完成时间的冷却计算）
        this.recordCompleteTime(skillData);

        // 确保技能状态为未激活
        skillData.state = SkillState.Inactive;
    }

    // ================================
    // 冷却系统管理
    // ================================

    /**
     * 检查技能是否具备冷却功能
     */
    private hasCooldown(skillId: string): boolean {
        const config = this.world.config.getSkillConfig(skillId);
        return !!(config && config.cooldownId);
    }

    /**
     * 检查技能是否在冷却中
     */
    isSkillOnCooldown(skillData: SkillData): boolean {
        // 没有冷却配置ID的技能不具备冷却功能
        if (!this.hasCooldown(skillData.skillId)) {
            return false;
        }

        const config = this.world.config.getSkillConfig(skillData.skillId);
        if (!config || !config.cooldownId) return false;

        const cooldownConfig = this.world.config.getCooldownConfig(config.cooldownId);
        if (!cooldownConfig) return false;

        const currentTime = Date.now();
        let lastTriggerTime = 0;

        // 根据冷却类型选择对应的时间
        if (cooldownConfig.cooldownType === CooldownType.OnCast) {
            lastTriggerTime = skillData.lastCastTime || 0;
        } else {
            lastTriggerTime = skillData.lastCompleteTime || 0;
        }

        const cooldownDuration = cooldownConfig.baseTime;
        return (currentTime - lastTriggerTime) < cooldownDuration;
    }

    /**
     * 获取技能冷却剩余时间
     */
    getCooldownRemaining(skillData: SkillData): number {
        if (!this.hasCooldown(skillData.skillId) || !this.isSkillOnCooldown(skillData)) {
            return 0;
        }

        const config = this.world.config.getSkillConfig(skillData.skillId);
        if (!config || !config.cooldownId) return 0;

        const cooldownConfig = this.world.config.getCooldownConfig(config.cooldownId);
        if (!cooldownConfig) return 0;

        const currentTime = Date.now();
        let lastTriggerTime = 0;

        // 根据冷却类型选择对应的时间
        if (cooldownConfig.cooldownType === CooldownType.OnCast) {
            lastTriggerTime = skillData.lastCastTime || 0;
        } else {
            lastTriggerTime = skillData.lastCompleteTime || 0;
        }

        const cooldownDuration = cooldownConfig.baseTime;
        return Math.max(0, cooldownDuration - (currentTime - lastTriggerTime));
    }

    /**
     * 启动技能冷却
     */
    private startCooldown(skillData: SkillData): void {
        if (this.hasCooldown(skillData.skillId)) {
            skillData.lastCastTime = this.world.worldTime;
        }
    }

    /**
     * 完成技能冷却记录
     */
    private recordCompleteTime(skillData: SkillData): void {
        if (this.hasCooldown(skillData.skillId)) {
            skillData.lastCompleteTime = this.world.worldTime;
        }
    }
}
