import { NativePermissions } from '../../../../native/NativePermissions';
import ReviveComponent from '../../components/ReviveComponent';
import TargetComponent from '../../components/special/TargetComponent';
import { ECSLayerType } from '../../define/EcsDefine';
import { TempleteType } from '../../registry/templete/TempleteRegistry';
import { EcsViewId } from '../../renderWorld/ECSViewBase';
import { ECSExecuteContext, ECSRuleBase, IECSRuleConfig } from '../core/ECSRuleBase';

export interface IReviveSignRuleConfig extends IECSRuleConfig {
    /**复活视图id */
    ecsViewId: EcsViewId;
}

/**
 * 复活规则
 */
export class ReviveSignStartRule extends ECSRuleBase<IReviveSignRuleConfig> {
    canExecute(): boolean {
        return true;
    }

    execute(): void {
        // const world = this.world;
        // const sceneEntity = world.handlerCenter.boardHandler.getSceneEntity();
        // const reviveComp = world.getComponent(sceneEntity, ReviveComponent);
        // reviveComp.nCount--;
        // const effectEntity = world.templeteCenter.createTempleteEntity(
        //     TempleteType.Render,
        //     {
        //         parentEntity: ECSLayerType.UILayerEntity,
        //         ecsViewId: this.config.ecsViewId,
        //     },
        //     {
        //         renderParam: { isBreakLink: true },
        //     },
        // );
        // world.destroyEntity(effectEntity);
    }
}
