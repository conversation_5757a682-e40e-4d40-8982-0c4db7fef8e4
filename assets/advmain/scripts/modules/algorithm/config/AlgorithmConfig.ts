import { OFFER_TYPE } from "../type/AlgorithmType";

export const OFFER_TYPE_STRINGS: { [key in OFFER_TYPE]: string } = {
  [OFFER_TYPE.NONE]: '',
  [OFFER_TYPE.SUI_JI]: '随机',
  [OFFER_TYPE.SHANG_ZENG_1]: '熵增1',
  [OFFER_TYPE.SHANG_ZENG_3]: '熵增3',
  [OFFER_TYPE.SI_WANG]: '简单难题',
  [OFFER_TYPE.TIAN_KONG_XIAO_CHU]: '填空消除',
  [OFFER_TYPE.SHANG_JIAN]: '熵减1',
  [OFFER_TYPE.KUN_NAN_TI]: '死亡难题',
  [OFFER_TYPE.SUI_JI_WU_SI]: '随机无死亡',
  [OFFER_TYPE.ZHI_JUE_NAN_TI]: '直觉难题',
  [OFFER_TYPE.ZHI_SI_TI]: '致死题',

  [OFFER_TYPE.ZIMU_SHANGZENG]: '熵增3',

  [OFFER_TYPE.TRAVEL_TIAN_KONG_XIAO_CHU]: '旅行填空消除',
  [OFFER_TYPE.SUI_JI_GUIDE]: '随机',
  [OFFER_TYPE.REVIVE]: '复活',

  [OFFER_TYPE.ALL_COMBINATION_ID9]: '填空消除',
  [OFFER_TYPE.ALL_COMBINATION_ID21]: '填空消除',
  [OFFER_TYPE.ALL_COMBINATION_ID70]: '填空消除',
  [OFFER_TYPE.EFFICIENT_ELIMINATE]: '高效消除',
  [OFFER_TYPE.EMPTYDONGFILL]: '填空消除',

  [OFFER_TYPE.ELIMINTE_PLEASURE]: '消除爽',

  [OFFER_TYPE.SIMPLE_ZHIJUE]: '简单直觉',
  [OFFER_TYPE.ALGORITHMENTROPY]: '困难难题',
  [OFFER_TYPE.ALGORITHMENTROPYMORECLEAR]: '困难难题',
  [OFFER_TYPE.ALGODIFFSPREAD1]: '困难难题',
  [OFFER_TYPE.ALGODIFFSPREAD2]: '困难难题',
  [OFFER_TYPE.DIFFICULTY_GAOHUIBAO]: '高回报难题',

  [OFFER_TYPE.CLEAR_BOARD]: '清屏plus',
  [OFFER_TYPE.IRREVERSIBLE_ALIEN]: '不可消除异形',
  [OFFER_TYPE.CLASSTERMINATE_CYCLE]: '终止循环',
  [OFFER_TYPE.CHAPTERTERMINATE_CYCLE]: '终止循环',
  [OFFER_TYPE.REPLACE_ROUNDLIMIT]: '平替局数'
};

// 初始化统计数据
export const algorithmDataInfoKeys = [
  'blocksList'
];

