
export class Loader {
    private static events: { [key: string]: ((bundleName: string, path: string) => void)[] } = {};

    private static cacheResources: {
        [key: string]: {
            onCompletes: ((error: Error, asset: cc.Asset | cc.AssetManager.Bundle) => void)[];
            asset?: cc.Asset | cc.AssetManager.Bundle
        }
    } = {};

    /**
     * 添加资源事件监听
     * @param event
     * @param callback
     */
    static addEventListener(event: `load`, callback: (bundleName: string, path: string) => void) {
        if (!this.events[event]) {
            this.events[event] = [];
        }

        this.events[event].push(callback);
    }

    /**
     * 是否为远程地址，通过 http(s)://前缀来判断
     * @param url
     * @returns
     */
    private static isRemote(url: string): boolean {
        return /^(https?:\/\/)/.test(url);
    }

    static getAsset<T extends cc.Asset>(url: string): T {
        return this.cacheResources[url]?.asset as T;
    }

    /**
     * 高效加载资源:
     * （1）：多请求完成后分发
     * （2）：支持远程支持加载（web 端可能产生跨域问题）
     * （3）：远程资源不需要传 type
     * （4）：目前远程支持暂时不支持重试次数
     * @param paths
     * @param type
     * @param onComplete
     */
    static load<T extends cc.Asset>(paths: string, type: typeof cc.Asset, onComplete?: (error: Error, assets: T) => void) {
        if (!this.cacheResources[paths]) {
            this.cacheResources[paths] = { onCompletes: onComplete ? [onComplete] : [] };
            const self = this;

            const _complete = function (err: Error, asset: T) {
                if (err) {
                    cc.error(`资源加载发生错误：`, err);
                }

                self.cacheResources[paths].asset = asset;
                const { onCompletes } = self.cacheResources[paths];
                for (let i = 0; i < onCompletes?.length; i++) {
                    const onCompleteItem = onCompletes[i];
                    onCompleteItem.apply(this, [err, asset]);
                }

                self.cacheResources[paths].onCompletes.length = 0;

                const loadEvents = self.events[`load`];
                for (let i = 0; i < loadEvents?.length; i++) {
                    const event = loadEvents[i];
                    event.apply(self, [`resources`, paths]);
                }
            };

            if (!this.isRemote(paths)) {
                cc.resources.load(paths, type, _complete);
            } else {
                cc.assetManager.loadRemote(paths, _complete);
            }
        } else {
            if (this.cacheResources[paths].asset) {
                if ((this.cacheResources[paths].asset as cc.Asset).isValid) {
                    if (onComplete) {
                        onComplete.apply(this, [null, this.cacheResources[paths].asset]);
                    }
                } else {
                    delete this.cacheResources[paths];
                    this.load(paths, type, onComplete);
                }
            } else {
                if (onComplete) {
                    this.cacheResources[paths].onCompletes.push(onComplete);
                }
            }
        }
    }

    /**
     * 通过 promise 方式进行资源加载
     * @param paths
     * @param type
     * @returns
     */
    static asyncLoad<T extends cc.Asset>(paths: string, type: typeof cc.Asset): Promise<T> {
        return new Promise((c, e) => {
            this.load(paths, type, (error, assets: T) => {
                if (error) {
                    e(error);
                    return;
                }

                c(assets);
            });
        });
    }

    /**
     * 根据 bunlde 加载资源
     * @param bundleName bundle名称
     * @param paths 资源路径
     * @param type 资源类型
     * @param onComplete 回调函数
     * @param options bundle选项（包含MD5版本等）
     */
    static loadByBundle<T extends cc.Asset>(bundleName: string, paths: string, type?: typeof cc.Asset, onComplete?: (error: Error, asset: T) => void, options?: Record<string, any>) {
        // 处理参数重载：如果第4个参数是options对象，第5个参数是回调
        let bundleOptions = options || {};
        let callback = onComplete;
        
        // 检查参数重载情况
        if (typeof onComplete === 'object' && onComplete !== null && !callback) {
            bundleOptions = onComplete as Record<string, any>;
            callback = undefined;
        }
        // console.log(`loadByBundle bundleName = ${bundleName} paths = ${paths} type = ${type} bundleOptions = ${JSON.stringify(bundleOptions)}`);
        this.loadBundle(bundleName, bundleOptions, (err: Error, bundle: cc.AssetManager.Bundle) => {
            if (err) {
                console.error(`bundle:${bundleName} 资源加载错误：`, err);
                if (callback) {
                    callback.apply(this, [err, null]);
                }
                return;
            }
            
            // 缓存key包含版本信息
            const versionSuffix = bundleOptions.md5Version || bundleOptions.version || '';
            const key = bundleName + `_` + paths + (versionSuffix ? `_v${versionSuffix}` : '');

            if (!this.cacheResources[key]) {
                this.cacheResources[key] = { onCompletes: callback ? [callback] : [] };

                bundle.load(paths, type, (err, res: T) => {
                    this.cacheResources[key].asset = res;
                    const { onCompletes } = this.cacheResources[key];
                    for (let i = 0; i < onCompletes?.length; i++) {
                        const onCompleteItem = onCompletes[i];
                        onCompleteItem.apply(this, [err, res]);
                    }

                    const loadEvents = this.events[`load`];
                    for (let i = 0; i < loadEvents?.length; i++) {
                        const event = loadEvents[i];
                        event.apply(this, [bundleName, paths]);
                    }
                });

            } else {
                if (this.cacheResources[key].asset) {
                    if ((this.cacheResources[key].asset as cc.Asset).isValid) {
                        if (callback) {
                            callback.apply(this, [null, this.cacheResources[key].asset]);
                        }
                    } else {
                        delete this.cacheResources[key];
                        this.loadByBundle(bundleName, paths, type, callback, bundleOptions);
                    }
                } else {
                    if (callback) {
                        this.cacheResources[key].onCompletes.push(callback);
                    }
                }
            }
        });
    }

    /**
     * 通过 promise 方式并基于 bundle 加载资源
     * @param bundleName bundle名称
     * @param paths 资源路径
     * @param type 资源类型
     * @param options bundle选项（包含MD5版本等）
     * @returns Promise<T>
     */
    static asyncLoadByBundle<T extends cc.Asset>(bundleName: string, paths: string, type?: typeof cc.Asset, options?: Record<string, any>): Promise<T> {
        // console.log(`asyncLoadByBundle paths = ${paths} options =  ${JSON.stringify(options)}`);
        return new Promise((c, e) => {
            this.loadByBundle(bundleName, paths, type, (err, asset) => {
                if (err) {
                    e(err);
                    return;
                }

                c(asset as T);
            }, options);
        });
    }

    /**
     * 高性能，在未加载完成时，多次请求加载，不会触发多次加载
     * @param nameOrUrl bundle名称或URL
     * @param optionsOrOnComplete 选项或回调函数
     * @param onComplete 回调函数（当第二个参数是options时使用）
     */
    static loadBundle(nameOrUrl: string, optionsOrOnComplete?: Record<string, any> | ((err: Error, bundle: cc.AssetManager.Bundle) => void), onComplete?: (err: Error, bundle: cc.AssetManager.Bundle) => void) {
        const self = this;
        // console.log(`loadBundle nameOrUrl = ${nameOrUrl} optionsOrOnComplete = ${JSON.stringify(optionsOrOnComplete)} onComplete = ${JSON.stringify(onComplete)}`);
        // 参数处理
        let options: Record<string, any> = {};
        let callback: (err: Error, bundle: cc.AssetManager.Bundle) => void;
        
        if (typeof optionsOrOnComplete === 'function') {
            // 第二个参数是回调函数
            callback = optionsOrOnComplete as (err: Error, bundle: cc.AssetManager.Bundle) => void;
        } else {
            // 第二个参数是options
            options = optionsOrOnComplete || {};
            callback = onComplete;
        }
        
        // 构建缓存key，包含版本信息
        const cacheKey = this.buildBundleCacheKey(nameOrUrl, options);
        
        
        
        if (!this.cacheResources[cacheKey]) {
            this.cacheResources[cacheKey] = { onCompletes: callback ? [callback] : [] };
            
            // 使用构建的路径加载bundle
            if(options.version) {
                cc.assetManager.loadBundle(nameOrUrl, options, function (err: Error, bundle: cc.AssetManager.Bundle) {
                    self.cacheResources[cacheKey].asset = bundle;
                    const { onCompletes } = self.cacheResources[cacheKey];
                    for (let i = 0; i < onCompletes?.length; i++) {
                        const onCompleteItem = onCompletes[i];
                        onCompleteItem.apply(this, [err, bundle]);
                    }
    
                    self.cacheResources[cacheKey].onCompletes.length = 0;
                });
            }else {
                cc.assetManager.loadBundle(nameOrUrl, function (err: Error, bundle: cc.AssetManager.Bundle) {
                    self.cacheResources[cacheKey].asset = bundle;
                    const { onCompletes } = self.cacheResources[cacheKey];
                    for (let i = 0; i < onCompletes?.length; i++) {
                        const onCompleteItem = onCompletes[i];
                        onCompleteItem.apply(this, [err, bundle]);
                    }
    
                    self.cacheResources[cacheKey].onCompletes.length = 0;
                });
            }
        } else {
            if (this.cacheResources[cacheKey].asset) {
                if (callback) {
                    callback.apply(this, [null, this.cacheResources[cacheKey].asset]);
                }
            } else {
                if (callback) {
                    this.cacheResources[cacheKey].onCompletes.push(callback);
                }
            }
        }
        }

    /**
     * 构建bundle缓存key
     * @param nameOrUrl bundle名称或URL
     * @param options 选项
     * @returns 缓存key
     */
    private static buildBundleCacheKey(nameOrUrl: string, options: Record<string, any>): string {
        const md5Version = options.md5Version || options.version;
        return md5Version ? `${nameOrUrl}_v${md5Version}` : nameOrUrl;
    }

    /**
     * 通过 bundle 预加载场景
     * @param bundle
     * @param sceneName
     * @param onComplete
     */
    static bundlePreloadScene(bundle: cc.AssetManager.Bundle, sceneName: string, onComplete?: (error: Error) => void) {
        const self = this;
        const key = `preloadScene` + sceneName;
        if (!this.cacheResources[key]) {
            this.cacheResources[key] = { onCompletes: onComplete ? [onComplete] : [] };
            bundle.preloadScene(sceneName, function (err: Error) {
                const { onCompletes } = self.cacheResources[key];
                self.cacheResources[key]['__complete__'] = true;
                for (let i = 0; i < onCompletes?.length; i++) {
                    const onCompleteItem = onCompletes[i];
                    onCompleteItem.apply(this, [err]);
                }

                self.cacheResources[key].onCompletes.length = 0;
                delete self.cacheResources[key]['__complete__'];
            });
        } else {
            if (self.cacheResources[key]['__complete__']) {
                if (onComplete) {
                    onComplete.apply(this, [null]);
                }
            } else {
                if (onComplete) {
                    this.cacheResources[key].onCompletes.push(onComplete);
                }
            }
        }
    }

    /**
     * 通过 bundle 预加载场景
     * @param bundle
     * @param sceneName
     * @param onComplete
     */
    static bundlePreload(bundle: cc.AssetManager.Bundle, paths: string | string[], type: typeof cc.Asset, onComplete?: (error: Error) => void) {
        const self = this;
        const key = `preload` + paths;
        if (!this.cacheResources[key]) {
            this.cacheResources[key] = { onCompletes: onComplete ? [onComplete] : [] };
            bundle.preload(paths, type, function (error: Error, items: any) {
                const { onCompletes } = self.cacheResources[key];
                self.cacheResources[key]['__items__'] = items;
                for (let i = 0; i < onCompletes?.length; i++) {
                    const onCompleteItem = onCompletes[i];
                    onCompleteItem.apply(this, [error, items]);
                }

                self.cacheResources[key].onCompletes.length = 0;
            });
        } else {
            if (self.cacheResources[key]['__items__']) {
                if (onComplete) {
                    onComplete.apply(this, [null, self.cacheResources[key]['__items__']]);
                }
            } else {
                if (onComplete) {
                    this.cacheResources[key].onCompletes.push(onComplete);
                }
            }
        }
    }

    /**
     * 渲染 sprite
     * @param sprite
     * @param url
     */
    static renderSprite(sprite: cc.Sprite, url: string) {
        this.load(url, cc.SpriteFrame, (err, asset: cc.SpriteFrame) => {
            if (err) {
                return;
            }
            sprite.spriteFrame = asset;
        });
    }

    /**
     * 渲染龙骨动画
     * @param dragonBonesArmatureDisplay
     * @param dragonAssetUrl
     * @param dragonAssetAtlasUrl
     * @param armatureName
     * @param animName
     * @param playtimes
     */
    static renderDragonbones(dragonBonesArmatureDisplay: dragonBones.ArmatureDisplay, dragonAssetUrl: string, dragonAssetAtlasUrl: string, armatureName: string, animName: string, playtimes: number = 1) {
        let loadCount = 0;
        const play = () => {
            if (loadCount === 2) {
                dragonBonesArmatureDisplay.addEventListener(dragonBones.EventObject.COMPLETE, (e: cc.Event) => {
                    if (CC_DEBUG) {
                        console.log("renderDragonbones 动画播放结束,url", dragonAssetUrl);
                    }
                    if (dragonBonesArmatureDisplay.node?.parent) {
                        dragonBonesArmatureDisplay.node.parent.removeChild(dragonBonesArmatureDisplay.node);
                    }
                }, this);
                dragonBonesArmatureDisplay.armatureName = armatureName;
                dragonBonesArmatureDisplay.playAnimation(animName, playtimes);
            }
        };
        this.load(dragonAssetUrl, dragonBones.DragonBonesAsset, (err, asset: dragonBones.DragonBonesAsset) => {
            if (err) {
                return;
            }
            dragonBonesArmatureDisplay.dragonAsset = asset;
            loadCount++;
            play();
        });

        this.load(dragonAssetAtlasUrl, dragonBones.DragonBonesAtlasAsset, (err, asset: dragonBones.DragonBonesAtlasAsset) => {
            if (err) {
                return;
            }
            dragonBonesArmatureDisplay.dragonAtlasAsset = asset;
            loadCount++;
            play();
        });
    }

    /**
    * 渲染bundle下龙骨动画
    * @param bundleName bundle名称
    * @param dragonBonesArmatureDisplay 龙骨显示组件
    * @param dragonAssetUrl 龙骨资源URL
    * @param dragonAssetAtlasUrl 龙骨图集URL
    * @param armatureName 骨架名称
    * @param animName 动画名称
    * @param playtimes 播放次数
    * @param completeRemove 完成后是否移除
    * @param options bundle选项（包含MD5版本等）
    */
    static renderDragonbonesByBundle(bundleName: string, dragonBonesArmatureDisplay: dragonBones.ArmatureDisplay, dragonAssetUrl: string, dragonAssetAtlasUrl: string, armatureName: string, animName: string, playtimes: number = 1, completeRemove: boolean = true, options?: Record<string, any>) {
        // 处理参数重载：如果倒数第二个参数是options对象
        let bundleOptions = options || {};
        let shouldRemove = completeRemove;
        
        // 检查参数重载情况 - 如果completeRemove是对象，说明它实际是options
        if (typeof completeRemove === 'object' && completeRemove !== null) {
            bundleOptions = completeRemove as Record<string, any>;
            shouldRemove = true; // 默认值
        }

        this.loadBundle(bundleName, bundleOptions, (err: Error, bundle: cc.AssetManager.Bundle) => {
            if (err) {
                return;
            }
            let loadCount = 0;
            const play = () => {
                if (loadCount === 2) {
                    dragonBonesArmatureDisplay.addEventListener(dragonBones.EventObject.COMPLETE, (e: cc.Event) => {
                        if (shouldRemove && dragonBonesArmatureDisplay.node?.parent) {
                            if (CC_DEBUG) {
                                console.log("renderDragonbonesByBundle 动画播放结束,url", dragonAssetUrl);
                            }
                            dragonBonesArmatureDisplay.node?.parent.removeChild(dragonBonesArmatureDisplay.node);
                        }
                    }, this);
                    dragonBonesArmatureDisplay.armatureName = armatureName;
                    dragonBonesArmatureDisplay.playAnimation(animName, playtimes);
                }
            };
            bundle.load(dragonAssetUrl, dragonBones.DragonBonesAsset, (err, asset: dragonBones.DragonBonesAsset) => {
                if (err) {
                    return;
                }
                dragonBonesArmatureDisplay.dragonAsset = asset;
                loadCount++;
                play();
            });

            bundle.load(dragonAssetAtlasUrl, dragonBones.DragonBonesAtlasAsset, (err, asset: dragonBones.DragonBonesAtlasAsset) => {
                if (err) {
                    return;
                }
                dragonBonesArmatureDisplay.dragonAtlasAsset = asset;
                loadCount++;
                play();
            });
        });
    }
}