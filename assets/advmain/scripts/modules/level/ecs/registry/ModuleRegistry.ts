// assets/advmain/scripts/modules/level/ecs/registry/ModuleRegistry.ts

import { System } from '../cores/System';
import { ECSRuleBase } from '../rules/core/ECSRuleBase';
import { ISystemConfig } from '../cores/System';
import { IECSRuleConfig } from '../rules/core/ECSRuleBase';

/**
 * 模块注册器接口
 */
export interface IModuleRegistry {
    /** 模块名称 */
    readonly moduleName: string;
    /** 模块版本 */
    readonly version: string;

    /** 注册系统 */
    registerSystems(systemRegistry: Record<string, clzz<System>>): void;
    /** 注册规则 */
    registerRules(ruleRegistry: Record<string, clzz<ECSRuleBase>>): void;
    /** 注册系统配置 */
    registerSystemConfigs(systemConfig: Record<string, ISystemConfig>): void;
    /** 注册规则配置 */
    registerRuleConfigs(ruleConfig: Record<string, IECSRuleConfig>): void;
    /** 获取系统常量 */
    getSystemConstants(): Record<string, string>;
    /** 获取规则常量 */
    getRuleConstants(): Record<string, string>;
}

/**
 * 模块注册器基类
 */
export abstract class BaseModuleRegistry implements IModuleRegistry {
    abstract readonly moduleName: string;
    abstract readonly version: string;

    registerSystems(systemRegistry: Record<string, clzz<System>>): void {
        // 子类实现
    }

    registerRules(ruleRegistry: Record<string, clzz<ECSRuleBase>>): void {
        // 子类实现
    }

    registerSystemConfigs(systemConfig: Record<string, ISystemConfig>): void {
        // 子类实现
    }

    registerRuleConfigs(ruleConfig: Record<string, IECSRuleConfig>): void {
        // 子类实现
    }

    getSystemConstants(): Record<string, string> {
        return {};
    }

    getRuleConstants(): Record<string, string> {
        return {};
    }
}

/**
 * 便捷注册函数
 */
export function registerModule(
    moduleRegistry: IModuleRegistry,
    systemRegistry: Record<string, clzz<System>>,
    ruleRegistry: Record<string, clzz<ECSRuleBase>>,
    systemConfig: Record<string, ISystemConfig>,
    ruleConfig: Record<string, IECSRuleConfig>,
): void {
    try {
        moduleRegistry.registerSystems(systemRegistry);
        moduleRegistry.registerRules(ruleRegistry);
        moduleRegistry.registerSystemConfigs(systemConfig);
        moduleRegistry.registerRuleConfigs(ruleConfig);
        console.log(`[ModuleRegistry] 模块 ${moduleRegistry.moduleName} v${moduleRegistry.version} 注册成功`);
    } catch (error) {
        console.error(`[ModuleRegistry] 模块 ${moduleRegistry.moduleName} 注册失败:`, error);
        throw error;
    }
}
