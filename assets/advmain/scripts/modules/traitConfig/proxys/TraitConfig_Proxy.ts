
import { Loader } from "../../../base/Loader";
import { SubGameBridge } from "../../../base/SubGameBridge";
import { ActiveConfig, Config } from "../config/Config";
import { E_TraitConfig_InitComplete } from "../events/E_TraitConfig_InitComplete";
import { traitConfigActiveWhiteListInfo } from "../vo/TraitConfigActiveWhiteListInfo";
import { traitConfigCfgInfo } from "../vo/TraitConfigCfgInfo";

export class TraitConfig_Proxy extends falcon.Proxy {
    registerEvents(): { new(...args: any): falcon.ModuleEvent; }[] | null {
        return [
        ];
    }

    receivedEvents($event: falcon.ModuleEvent): void {
        switch ($event.getClass()) {

        }
    }

    onServerState() {
        this.onTriatConfigInit();
    }

    /** 特性配置初始化 */
    async onTriatConfigInit() {
        if (CC_DEBUG) {
            console.log(`特性配置初始化`);
        }
        traitConfigCfgInfo['_traitConfigCfg'] = await Loader.asyncLoadByBundle<cc.JsonAsset>('advres', 'configs/cfg', cc.JsonAsset);
        const { whiteList } = traitConfigActiveWhiteListInfo;
        falcon.traitConfigInfo.initialize(traitConfigCfgInfo.traitConfigCfg.json);
        falcon.traitConfigInfo.createUseTraits(Config)
        falcon.traitConfigInfo.createActiveTraits(ActiveConfig)
        falcon.traitConfigInfo.createActiveTraitClassNameMaps();
        falcon.traitConfigInfo.createWhiteTraitClassNameMaps(whiteList)
 
        falcon.EventManager.dispatchModuleEvent(new E_TraitConfig_InitComplete());
    }
}