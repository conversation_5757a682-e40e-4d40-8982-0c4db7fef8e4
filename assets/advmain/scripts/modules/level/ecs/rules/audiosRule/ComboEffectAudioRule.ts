import BoardComponent from '../../components/board/BoardComponent';
import { AudioId } from '../../config/conf/EcsAudioConfig';
import { ECSEvent } from '../../GameEvent';
import { ECSRuleBase, IECSRuleConfig } from '../core/ECSRuleBase';
/**
 * 消除音效播放规则配置
 */
export interface IComboEffectAudioRuleConfig extends IECSRuleConfig {
    /** 不同消除数量对应的音效ID映射 */
    audioMapping: Record<number, AudioId>;
    /** 默认音效ID (当消除数量不在映射中时使用) */
    defaultAudioId: AudioId;
}
/**
 * 消除音效播放规则
 * 负责根据消除数量播放不同的音效
 */
export class ComboEffectAudioRule extends ECSRuleBase<IComboEffectAudioRuleConfig, ECSEvent.GameEvent.ELIMINATION> {
    public bindEventData(): ECSEvent.GameEvent.ELIMINATION {
        return ECSEvent.GameEvent.ELIMINATION;
    }
    /**
     * 检查是否可以执行
     */
    canExecute(): boolean {
        return true;
    }
    /**
     * 执行规则
     */
    execute(): void {
        const world = this.world;
        // 获取消除数据
        const boardEntity = this.eventData.boardEntity;
        const boardComponent = world.getComponent(boardEntity, BoardComponent);
        const ComboCount = boardComponent.comboCount || 1;
        // 根据消除数量选择音效
        const audioId = this.getAudioIdByCount(ComboCount);
        console.log(`ComboEffectAudioRule: 消除数量=${ComboCount}, 使用音效=${audioId}`);
        // 创建音效播放实体
        world.eventBus.emit(ECSEvent.BaseEvent.PLAY_AUDIO, { audioId: audioId });
    }
    /**
     * 根据消除数量获取对应的音效ID
     * @param count 消除数量
     * @returns 音效ID
     */
    private getAudioIdByCount(count: number): AudioId {
        // 优先使用映射中的音效
        if (this.config.audioMapping[count]) {
            return this.config.audioMapping[count];
        }
        // 如果超出映射范围，使用最高级别的音效
        const maxMappedCount = Math.max(...Object.keys(this.config.audioMapping).map((k) => parseInt(k)));
        if (count > maxMappedCount) {
            return this.config.audioMapping[maxMappedCount];
        }
        // 使用默认音效
        return this.config.defaultAudioId;
    }
} 