import { E_Levels_EnterLevel } from '../modules/level/events/E_Levels_EnterLevel';
import { PrefabConfig } from '../modules/prefab/PrefabConfig';
import { ConcurrentRunner } from './ConcurrentRunner';
import { SubGameBridge } from './SubGameBridge';

/**关卡资源 */
export interface ILevelResource {
    /**资源包名 */
    bundleName: string;
    /**资源路径 */
    resPath: string;
}

/**关卡配置 */
export interface IlevelConfig {
    /**关卡ID */
    levelId: string;
    /**关卡名称 */
    name: string;
    /**关卡描述 */
    description: string;
    /**关卡大类型-根据不同的关卡调用不同的关卡实现 */
    masterType: LevelMasterType;
    /**关卡玩法配置id */
    gameplayConfigId: string;
    /**关卡资源预加载列表,关卡内的资源加载都是异步设计的,如果需要预加载资源,则需要在这里配置 */
    loadResList: ILevelResource[];
}
export const enum LevelMasterType {
    /**ecs通用关卡 */
    ECSLevel = 'ECSLevel',
}
/**加载进度阶段 */
export const LOAD_STAGES = {
    /**关卡配置阶段 */
    LEVEL_CONFIG: {
        start: 0,
        end: 10,
    },
    /**玩法配置阶段 */
    GAMEPLAY_CONFIG: {
        start: 10,
        end: 20,
    },
    /**资源加载阶段 */
    RESOURCE_LOAD: {
        start: 20,
        end: 100,
    },
} as const;

/**关卡加载器 todo 半成品先凑合用 */
export class LevelLoader {
    /**关卡配置URL */
    private static levelConfigUrl: string = 'https://files.hs99.vip/level';

    /**正在加载的关卡ID集合 */
    private static loadingLevelIds: Set<string> = new Set();

    /**获取关卡配置URL */
    public static getLevelConfigUrl(levelId: string): string {
        return `${this.levelConfigUrl}/${levelId}.json`;
    }

    /**获取关卡配置URL */
    public static getGameplayConfigUrl(levelId: string, gameplayConfigId: string): string {
        return `${this.levelConfigUrl}/gameplayConfig/${gameplayConfigId}.json`;
    }

    /**获取关卡配置 */
    public static async getLevelConfig(levelId: string): Promise<DeepReadonly<IlevelConfig>> {
        const configUrl = this.getLevelConfigUrl(levelId);
        const levelConfigAsset = await falcon.ResLoader.asyncLoad<cc.JsonAsset>(configUrl, cc.JsonAsset);
        if (!levelConfigAsset) {
            console.error(`关卡配置加载失败:${levelId}`);
            return null;
        }
        return levelConfigAsset.json;
    }

    /**获取关卡玩法配置 */
    public static async getGameplayConfig(levelId: string, gameplayConfigId: string): Promise<DeepReadonly<any>> {
        const configUrl = this.getGameplayConfigUrl(levelId, gameplayConfigId);
        const configAsset = await falcon.ResLoader.asyncLoad<cc.JsonAsset>(configUrl, cc.JsonAsset);
        if (!configAsset) {
            console.error(`关卡玩法配置加载失败:${gameplayConfigId}, levelId:${levelId}`);
            return null;
        }
        return configAsset.json;
    }

    /**打断关卡资源加载 */
    public static cancelLevelLoad(levelId: string): void {
        if (this.loadingLevelIds.has(levelId)) {
            this.loadingLevelIds.delete(levelId);
            console.warn(`关卡${levelId}加载被打断`);
        }
    }
    /**打断所有关卡资源加载 */
    public static cancelAllLevelLoad(): void {
        this.loadingLevelIds.clear();
        console.warn('打断所有关卡资源加载');
    }

    /**检查关卡是否正在加载 */
    private static isLevelLoading(levelId: string): boolean {
        return this.loadingLevelIds.has(levelId);
    }

    /**预加载关卡(只下载资源) */
    public static async preloadLevel(levelId: string, onProgress?: (progress: number) => void): Promise<boolean> {
        if (this.isLevelLoading(levelId)) {
            console.warn(`关卡${levelId}正在加载中,请勿重复加载`);
            return false;
        }
        this.loadingLevelIds.add(levelId);
        try {
            const levelConfig = await this._getLevelConfigWithProgress(levelId, onProgress);
            if (!levelConfig || !this.isLevelLoading(levelId)) {
                return false;
            }
            const gameplayConfig = await this._getGameplayConfigWithProgress(levelId, levelConfig.gameplayConfigId, onProgress);
            if (!gameplayConfig || !this.isLevelLoading(levelId)) {
                return false;
            }
            await this._preloadLevelResource(levelId, levelConfig.loadResList, onProgress);
            if (this.isLevelLoading(levelId)) {
                this._setLevelProgress(levelId, 100, onProgress);
                return true;
            }
            return false;
        } finally {
            this.loadingLevelIds.delete(levelId);
        }
    }

    /**进入关卡 */
    public static async enterLevel(levelId: string, onProgress?: (progress: number) => void): Promise<boolean> {
        //临时写法
        if (CC_DEBUG) {
            falcon.EventManager.dispatchModuleEvent(new E_Levels_EnterLevel(levelId));
            return;
        }

        if (this.isLevelLoading(levelId)) {
            console.warn(`关卡${levelId}正在加载中,请勿重复加载`);
            return false;
        }
        this.loadingLevelIds.add(levelId);
        try {
            const levelConfig = await this._getLevelConfigWithProgress(levelId, onProgress);
            if (!levelConfig || !this.isLevelLoading(levelId)) {
                return false;
            }
            const gameplayConfig = await this._getGameplayConfigWithProgress(levelId, levelConfig.gameplayConfigId, onProgress);
            if (!gameplayConfig || !this.isLevelLoading(levelId)) {
                return false;
            }
            await this._loadLevelResource(levelId, levelConfig.loadResList, onProgress);
            if (this.isLevelLoading(levelId)) {
                falcon.EventManager.dispatchModuleEvent(new E_Levels_EnterLevel(levelId));
                return true;
            }
            return false;
        } finally {
            this.loadingLevelIds.delete(levelId);
        }
    }

    /**退出关卡 */
    public static async exitLevel(levelId: string): Promise<void> {
        const levelConfig = await this.getLevelConfig(levelId);
        if (levelConfig) {
            this._unloadLevelResource(levelConfig.loadResList);
        }
    }

    /**获取关卡配置(带进度) */
    private static async _getLevelConfigWithProgress(levelId: string, onProgress?: (progress: number) => void): Promise<DeepReadonly<IlevelConfig>> {
        this._setLevelProgress(levelId, LOAD_STAGES.LEVEL_CONFIG.start, onProgress);
        const levelConfig = await this.getLevelConfig(levelId);
        if (!levelConfig) {
            console.error(`关卡配置加载失败,levelId:${levelId}`);
            return null;
        }
        this._setLevelProgress(levelId, LOAD_STAGES.LEVEL_CONFIG.end, onProgress);
        return levelConfig;
    }

    /**获取关卡玩法配置(带进度) */
    private static async _getGameplayConfigWithProgress(
        levelId: string,
        gameplayConfigId: string,
        onProgress?: (progress: number) => void,
    ): Promise<DeepReadonly<any>> {
        this._setLevelProgress(levelId, LOAD_STAGES.GAMEPLAY_CONFIG.start, onProgress);
        const gameplayConfig = await this.getGameplayConfig(levelId, gameplayConfigId);
        if (gameplayConfig) {
            this._setLevelProgress(levelId, LOAD_STAGES.GAMEPLAY_CONFIG.end, onProgress);
        }
        return gameplayConfig;
    }

    /**加载关卡资源到内存 */
    private static async _loadLevelResource(
        levelId: string,
        loadResList: DeepReadonly<ILevelResource[]>,
        onProgress?: (progress: number) => void,
    ): Promise<void> {
        if (!loadResList || loadResList.length === 0 || !this.isLevelLoading(levelId)) {
            return;
        }
        const startProgress = LOAD_STAGES.RESOURCE_LOAD.start;
        const endProgress = LOAD_STAGES.RESOURCE_LOAD.end;
        let loadedCount = 0;

        // 创建所有资源的加载任务
        const loadTasks = loadResList.map((res) => async () => {
            if (!this.isLevelLoading(levelId)) {
                console.error('关卡加载被打断');
                return;
            }
            try {
                if (res.bundleName) {
                    await SubGameBridge.asyncLoadByBundle(res.bundleName, res.resPath);
                } else {
                    await SubGameBridge.asyncLoad(res.resPath, cc.Asset);
                }
                loadedCount++;
                const progress = cc.misc.lerp(startProgress, endProgress, loadedCount / loadResList.length);
                this._setLevelProgress(levelId, progress, onProgress);
            } catch (err) {
                console.error(`关卡资源加载失败:${res.resPath}, levelId:${levelId}`);
                throw err;
            }
        });

        // 并发执行所有加载任务
        await ConcurrentRunner.run(loadTasks);
    }

    /**预加载关卡资源 */
    private static async _preloadLevelResource(
        levelId: string,
        loadResList: DeepReadonly<ILevelResource[]>,
        onProgress?: (progress: number) => void,
    ): Promise<void> {
        if (!loadResList || loadResList.length === 0 || !this.isLevelLoading(levelId)) {
            return;
        }
        const startProgress = LOAD_STAGES.RESOURCE_LOAD.start;
        const endProgress = LOAD_STAGES.RESOURCE_LOAD.end;
        let loadedCount = 0;

        // 创建所有资源的预加载任务
        const loadTasks = loadResList.map((res) => async () => {
            if (!this.isLevelLoading(levelId)) {
                throw new Error('关卡加载被打断');
            }
            try {
                if (res.bundleName) {
                    const bundle = await new Promise<cc.AssetManager.Bundle>((resolve, reject) => {
                        SubGameBridge.loadBundle(res.bundleName, (err, bundle) => {
                            if (err) {
                                reject(err);
                                return;
                            }
                            resolve(bundle);
                        });
                    });
                    await new Promise<void>((resolve, reject) => {
                        SubGameBridge.bundlePreload(bundle, res.resPath, cc.Asset, (err) => {
                            if (err) {
                                reject(err);
                                return;
                            }
                            resolve();
                        });
                    });
                } else {
                    await SubGameBridge.asyncLoad(res.resPath, cc.Asset);
                }
                loadedCount++;
                const progress = cc.misc.lerp(startProgress, endProgress, loadedCount / loadResList.length);
                this._setLevelProgress(levelId, progress, onProgress);
            } catch (err) {
                console.error(`关卡资源预加载失败:${res.resPath}, levelId:${levelId}`);
                throw err;
            }
        });

        // 并发执行所有预加载任务
        await ConcurrentRunner.run(loadTasks);
    }

    /**卸载关卡资源 */
    private static async _unloadLevelResource(resList: DeepReadonly<ILevelResource[]>): Promise<void> {
        if (!resList || resList.length === 0) {
            return;
        }
        console.error(`这里还没有添加关卡资源卸载的逻辑,需要陈勇大佬支持一个卸载资源的接口`);
        for (const res of resList) {
            try {
            } catch (err) {
                console.error(`关卡资源卸载失败:${res.resPath}`);
            }
        }
    }

    /**设置关卡进度 */
    private static async _setLevelProgress(levelId: string, progress: number, onProgress?: (progress: number) => void): Promise<void> {
        if (onProgress) {
            onProgress(progress);
        }
    }
}
