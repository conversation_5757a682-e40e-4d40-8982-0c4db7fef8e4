import { System } from '../../cores/System';
import { ECSEvent } from '../../GameEvent';

export class ReviveSystem extends System {
    init(): void {
        this.world.eventBus.on(ECSEvent.GameEvent.REVIVE_FAILED, this.onReviveFailed, this);
        this.world.eventBus.on(ECSEvent.GameEvent.REVIVE_SUCCESS, this.onReviveSuccesss, this);
        this.world.eventBus.on(ECSEvent.GameEvent.REVIVE_CONFIRMED, this.onReviveConfirmed, this);
        this.world.eventBus.on(ECSEvent.GameEvent.REVIVE_CANCELLED, this.onReviveCancelled, this);
    }

    dispose(): void {
        this.world.eventBus.off(ECSEvent.GameEvent.REVIVE_FAILED, this.onReviveFailed, this);
        this.world.eventBus.off(ECSEvent.GameEvent.REVIVE_SUCCESS, this.onReviveSuccesss, this);
        this.world.eventBus.off(ECSEvent.GameEvent.REVIVE_CONFIRMED, this.onReviveConfirmed, this);
        this.world.eventBus.off(ECSEvent.GameEvent.REVIVE_CANCELLED, this.onReviveCancelled, this);
    }

    private onReviveFailed() {
        // console.log('yjf__复活失败 走失败逻辑');
        
    }

    private onReviveSuccesss() {
        // console.log('yjf__复活成功 走成功逻辑');
        this.world.eventBus.emit(ECSEvent.GameEvent.PRODUCE_BLOCK_REVIVE);
    }

    private onReviveConfirmed() {
        // console.log('yjf__复活确认 显示复活块');
        // 复活确认事件已经通过规则流处理，这里可以做其他逻辑
    }

    private onReviveCancelled() {
        // console.log('yjf__复活取消 清除预览数据');
        // 清除预览数据
        this.world.utilCenter.reviveUtil.clearPreviewData();
    }

    update(): void {}
}
