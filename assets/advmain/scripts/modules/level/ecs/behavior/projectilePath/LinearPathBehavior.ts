import { ProjectileComponent } from "../../components/combat/ProjectileComponent";
import { PathCalculationResult, PathCalculationData, ProjectilePathBehaviorBase } from "./ProjectilePathBehaviorBase";

export class LinearPathBehavior extends ProjectilePathBehaviorBase {
    calculatePath(
        params: PathCalculationData,
        projectile: ProjectileComponent
    ): PathCalculationResult {
        // 直接访问参数属性，避免解构赋值
        const startX = params.startPos.x;
        const startY = params.startPos.y;
        const targetX = params.targetPos.x;
        const targetY = params.targetPos.y;
        const elapsedTime = params.elapsedTime;
        const speed = params.speed || 200;

        // 直接计算方向分量
        const dx = targetX - startX;
        const dy = targetY - startY;

        // 计算距离平方，避免开方运算
        const distanceSquared = dx * dx + dy * dy;
        const resultCache = this.resultCache;
        // 如果距离为0，直接返回目标位置
        if (distanceSquared === 0) {
            resultCache.position.x = targetX;
            resultCache.position.y = targetY;
            resultCache.isComplete = true;
            return resultCache;
        }

        // 计算已移动的距离
        const traveledDistance = speed * elapsedTime;

        // 计算距离（只在需要时开方）
        const totalDistance = Math.sqrt(distanceSquared);

        // 检查是否已经到达目标
        if (traveledDistance >= totalDistance) {
            resultCache.position.x = targetX;
            resultCache.position.y = targetY;
            resultCache.isComplete = true;
            return resultCache;
        }

        // 计算移动比例并直接计算位置
        const ratio = traveledDistance / totalDistance;
        resultCache.position.x = startX + dx * ratio;
        resultCache.position.y = startY + dy * ratio;
        resultCache.isComplete = false;

        return resultCache;
    }
}