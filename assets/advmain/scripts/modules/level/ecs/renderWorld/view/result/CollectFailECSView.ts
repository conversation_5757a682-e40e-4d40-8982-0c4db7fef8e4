import { PrefabConfig } from '../../../../../prefab/PrefabConfig';
import TargetComponent from '../../../components/special/TargetComponent';
import { TargetType } from '../../../define/BoardDefine';
import { ECSEvent } from '../../../GameEvent';
import { MathUtil } from '../../../utils/MathUtil';
import { ECSViewBase } from '../../ECSViewBase';

/**收集物关卡失败视图 */
export class CollectFailECSView extends ECSViewBase {
    private topItems: cc.Node[] = [];
    private endItems: cc.Node[] = [];

    protected initView() {
        this.resetBtnState();
        this.showAction();
    }
    protected addEvents() {
        this.ui.backBtn.on(cc.Node.EventType.TOUCH_END, this.onClickBack, this);
        this.ui.playBtn.on(cc.Node.EventType.TOUCH_END, this.onClickPlay, this);
    }
    protected removeEvents() {
        this.ui.backBtn.off(cc.Node.EventType.TOUCH_END, this.onClickBack, this);
        this.ui.playBtn.off(cc.Node.EventType.TOUCH_END, this.onClickPlay, this);
    }
    protected disposeView() {
        this.clearView();
    }

    resetBtnState() {
        this.ui.backBtn.active = false;
        this.ui.backBtn.scale = 0;
        this.ui.playBtn.active = false;
        this.ui.boneAni_1.active = false;
    }

    /**播放动作 */
    showAction(): void {
        const targets = this.getTargetList();
        let isShowDefTip = false;
        // 任何一个收集目标不是分数都算收集物关
        for (let i = 0; i < targets.length; i++) {
            if (targets[i].key !== TargetType.Score) {
                isShowDefTip = true;
                break;
            }
        }
        cc.tween(this.node)
            .delay(0.57)
            .call(() => {
                this.ui.boneAni_1.active = true;
                this.ui.boneAni_1;
                let AniNameArr = ['KeepFighting', 'YouCanDoIt', 'notThisTime', 'TryAgain'];
                if (this.ui.boneAni_1) {
                    this.ui.boneAni_1.active = true;
                    const tagIndex = MathUtil.randomInt(0, 4);
                    let nameStr = AniNameArr[tagIndex];
                    this.ui.boneAni_1.getComponent(dragonBones.ArmatureDisplay).playAnimation(nameStr, 1);
                }
            })
            .delay(0.33)
            .call(() => {
                if (isShowDefTip) {
                    this.ui.boneAni_1.active = true;
                    this.ui.boneAni_1.getComponent(dragonBones.ArmatureDisplay).playAnimation(`in`, 1);
                } else {
                    this.ui.boneAni.active = false;
                }
                // 目标面板弹出音效
                // audioInfo.play(ChapterAudioConfig.travel_overui_collect_items);
                this.addItem();
            })
            .delay(1.1)
            .call(() => {
                this.showBtnAnim();
            })
            .start();
    }

    showBtnAnim() {
        this.ui.backBtn.active = true;
        this.ui.backBtn.scale = 0.6;
        this.ui.playBtn.active = true;
        this.ui.playBtn.scale = 0.6;

        // 按钮出现音效
        // audioInfo.play(AudioConfig.s_btnShow);
        cc.tween(this.ui.backBtn)
            .to(0.13, { scale: 1.1 })
            .to(0.07, { scale: 1 })
            .call(() => {
                this.ui.backBtn.getComponent(cc.Button).interactable = true;
            })
            .start();

        cc.tween(this.ui.playBtn)
            .to(0.13, { scale: 1.1 })
            .to(0.07, { scale: 1 })
            .call(() => {
                this.ui.playBtn.getComponent(cc.Button).interactable = true;
            })
            .start();
    }

    /**t添加收集item */
    async addItem() {
        const endTopDistanceArr: number[] = [0, 0, 0, -20, -20, -20, -20];
        const targets = this.getTargetList();
        const length = targets.length;
        const beginPos = (-(endTopDistanceArr[length - 1] + 200) * (length - 1)) / 2;
        const distance = endTopDistanceArr[length - 1] + 200;
        for (let i = 0; i < length; i++) {
            const k = targets[i].key;
            const remainNum = targets[i].current || 0;
            const targetCount = targets[i].to || 0;
            if (remainNum != 0) {
                // 未完成
                const node = cc.instantiate(this.ui.ChapterCollectTopItem);
                node.x = beginPos + distance * i;
                this.topItems.push(node);
                const lab = node.getChildByName('numLab');
                this.showTopItem(node, { color: Number(k), remainCollectCount: remainNum, targetCount: targetCount });
                this.showTopItemNumLab(lab);
                this.ui.collectItemNode.addChild(node);
            } else {
                // 收集完
                const node = cc.instantiate(this.ui.ChapterCollectEndItem);
                node.x = beginPos + distance * i;
                this.endItems.push(node);
                this.ui.collectItemNode.addChild(node);
                this.showEndItem(node, { color: Number(k) });
            }
        }
    }

    /**点击开始 */
    onClickPlay(event: cc.Event.EventTouch, data: any) {
        this.logicWorld.pushInputCmd({
            event: ECSEvent.BaseEvent.GAME_RESTART,
            data: null,
        });
        this.renderWorld.destroyECSView(this);
    }

    /**点击返回 */
    onClickBack(event: cc.Event.EventTouch, data: any) {
        this.renderWorld.destroyECSView(this);
        falcon.UI.show(PrefabConfig.ChapterMain);
        // EventManager.dispatchModuleEvent(new E_Game_BackHome());
    }

    clearView() {
        this.resetBtnState();
        this.endItems = [];
        this.topItems = [];
        if (this.ui.collectItemNode) {
            this.ui.collectItemNode.removeAllChildren();
        }
    }

    getTargetList() {
        const targets = this.logicWorld.getComponent(this.logicWorld.query([TargetComponent])[0], TargetComponent).targets;
        return targets;
    }

    showTopItemNumLab(lab: cc.Node): void {
        lab.active = true;
    }

    showTopItem(node: cc.Node, data: any): void {
        node.active = true;
        const { color, remainCollectCount } = data;
        this.renderWorld.loadRes('', `textures/blocks/Gems`, cc.SpriteAtlas).then((atlas: cc.SpriteAtlas) => {
            this.ui.gemBg.getComponent(cc.Sprite).spriteFrame = atlas.getSpriteFrame(`Gems${color}Bg`);
            this.ui.gemImg.getComponent(cc.Sprite).spriteFrame = atlas.getSpriteFrame(`Gems${color}Big`);
            this.ui.lightImg.getComponent(cc.Sprite).spriteFrame = atlas.getSpriteFrame(`Gems${color}Light`);
        });
        const remainCount = Math.max(remainCollectCount, 0);
        node.getChildByName('numLab').getComponent(cc.Label).string = `${remainCount}`;
    }

    showEndItem(node: cc.Node, data: any): void {
        node.active = true;
        const { color } = data;

        this.renderWorld.loadRes('', `textures/blocks/Gems`, cc.SpriteAtlas).then((atlas: cc.SpriteAtlas) => {
            node.getChildByName('gemImg').getComponent(cc.Sprite).spriteFrame = atlas.getSpriteFrame(`Gems${color}Big`);
            node.getChildByName('boneAni').getComponent(dragonBones.ArmatureDisplay).playAnimation(`${color}`, 1);
        });
    }

    showEndItemNumLab(lab: cc.Node): void {
        lab.active = true;
    }
}
