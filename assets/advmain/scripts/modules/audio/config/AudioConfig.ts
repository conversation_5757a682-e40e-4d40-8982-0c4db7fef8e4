import { AUDIO_TYPE } from "../../level/ecs/config/conf/EcsAudioConfig";

/**
 * 管理所有声音配置
 */
export const AudioConfig = {

    s_button: {
        url: "audios/s_button",
        bundleName: 'advres',
    },
    s_put: {
        url: "audios/s_put",
        bundleName: 'advres',
        volume: 1
    },
    s_restart: {
        url: "audios/s_restart",
        bundleName: 'advres',
    },
    s_fail: {
        url: "audios/over/s_fail",
        bundleName: 'advres',
    },
    s_win: {
        url: "audios/over/s_win",
        bundleName: 'advres',
    },
    travel_nowayput: {
        url: "audios/over/travel_nowayput",
        bundleName: 'advres',
    },
    s_touch: {
        url: "audios/s_touch",
        bundleName: 'advres',
    },
    s_btnShow: {
        url: "audios/s_btnShow",
        bundleName: 'advres',
    },
    commonBgm: {
        url: "audios/bgm/commonBgm",
        bundleName: 'advres',
        type: AUDIO_TYPE.SOUND
    },

}