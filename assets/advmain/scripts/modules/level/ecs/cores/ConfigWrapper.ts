/**
 * 配置包装器接口
 */
export interface IConfigWrapper<T extends Record<string, any>> {
    /**
     * 设置配置值重写
     */
    configOverrideSet<K extends keyof T>(key: K, value: T[K]): void;
    
    /**
     * 移除配置值重写，恢复原始值
     */
    configOverrideUnset<K extends keyof T>(key: K): void;
    
    /**
     * 检查是否有重写的值
     */
    configOverrideHas<K extends keyof T>(key: K): boolean;
    
    /**
     * 获取所有重写的键
     */
    configOverrideGetKeys(): string[];
    
    /**
     * 清除所有重写
     */
    configOverrideClear(): void;
    
    /**
     * 获取原始配置对象
     */
    configOverrideGetOriginal(): T;
    
    /**
     * 获取重写数据（用于序列化）
     */
    configOverrideGetData(): Record<string, any>;
    
    /**
     * 设置重写数据（用于反序列化）
     */
    configOverrideSetData(data: Record<string, any>): void;
}

/**
 * 配置包装器 - 可以动态重写配置值而不影响原始配置
 * 只允许通过专门的set方法修改配置，禁用直接赋值
 */
export class ConfigWrapper<T extends Record<string, any>> implements IConfigWrapper<T> {
    private originalConfig: T;
    private overrides: Record<string, any>;

    constructor(config: T,overrides: Record<string, any>) {
        this.originalConfig = config;
        this.overrides = overrides;
    }

    /**
     * 获取配置值，优先返回重写的值
     */
    configOverrideGet<K extends keyof T>(key: K): T[K] {
        if (key in this.overrides) {
            return this.overrides[key as string];
        }
        return this.originalConfig[key];
    }

    /**
     * 设置配置值重写
     */
    configOverrideSet<K extends keyof T>(key: K, value: T[K]): void {
        this.overrides[key as string] = value;
    }

    /**
     * 移除配置值重写，恢复原始值
     */
    configOverrideUnset<K extends keyof T>(key: K): void {
        delete this.overrides[key as string];
    }

    /**
     * 检查是否有重写的值
     */
    configOverrideHas<K extends keyof T>(key: K): boolean {
        return key in this.overrides;
    }

    /**
     * 获取所有重写的键
     */
    configOverrideGetKeys(): string[] {
        return Object.keys(this.overrides);
    }

    /**
     * 清除所有重写
     */
    configOverrideClear(): void {
        this.overrides = {};
    }

    /**
     * 获取原始配置对象
     */
    configOverrideGetOriginal(): T {
        return this.originalConfig;
    }

    /**
     * 获取重写数据（用于序列化）
     */
    configOverrideGetData(): Record<string, any> {
        return { ...this.overrides };
    }

    /**
     * 设置重写数据（用于反序列化）
     */
    configOverrideSetData(data: Record<string, any>): void {
        this.overrides = { ...data };
    }

    /**
     * 代理所有属性访问，禁用直接赋值
     */
    static createProxy<T extends Record<string, any>>(config: T,overrides: Record<string, any> = {}): T & IConfigWrapper<T> {
        const wrapper = new ConfigWrapper(config,overrides);
        const proxy = new Proxy(config, {
            get(target, prop) {
                if (typeof prop === 'string') {
                    // 如果有重写值，返回重写值
                    if (wrapper.configOverrideHas(prop as keyof T)) {
                        return wrapper.configOverrideGet(prop as keyof T);
                    }
                    // 否则返回原始值
                    return (target as any)[prop];
                }
                return (target as any)[prop];
            },
            set(target, prop, value) {
                if (typeof prop === 'string') {
                   wrapper.configOverrideSet(prop as keyof T, value);
                }
                return true;
            }
        });

        // 将包装器方法添加到代理对象上，使用独特的前缀
        (proxy as any).configOverrideSet = (key: keyof T, value: T[keyof T]) => wrapper.configOverrideSet(key, value);
        (proxy as any).configOverrideUnset = (key: keyof T) => wrapper.configOverrideUnset(key);
        (proxy as any).configOverrideHas = (key: keyof T) => wrapper.configOverrideHas(key);
        (proxy as any).configOverrideGetKeys = () => wrapper.configOverrideGetKeys();
        (proxy as any).configOverrideClear = () => wrapper.configOverrideClear();
        (proxy as any).configOverrideGetOriginal = () => wrapper.configOverrideGetOriginal();
        (proxy as any).configOverrideGetData = () => wrapper.configOverrideGetData();
        (proxy as any).configOverrideSetData = (data: Record<string, any>) => wrapper.configOverrideSetData(data);

        return proxy as T & IConfigWrapper<T>;
    }
} 