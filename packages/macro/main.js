'use strict';
const path = require('path');
const fs = require('fs');

let sourceTraitsSplitHelperContent;
module.exports = {
  load() {
    function getValue(data) {
      Editor.log(`gatValue！`, data);
      if (data === undefined) {
        return false;
      }

      if (data === 'false' || data === false) {
        return false;
      }

      if (data === 'true' || data === true) {
        return true;
      }

      if (typeof data === 'string') {
        return `"${data}";`
      }

      return data;
    }

    /**
     * 默认宏定义
     * 注意：如果是字符串，请在默认值上补充 ''
     */
    const defaultMacros = {
      // 自动化宏
      'MACRO_AUTO': false,
      // 特性拆分宏
      'MACRO_TRAITS_SPLIT': false,
      // 是否开启热更宏
      'MACRO_HOTUPDATE_OPEN': false,
      // 服务器热更宏服务器地址
      'MACRO_HOTUPDATE_SERVER_URL': '"http://192.168.83.169:8830/"',
      // 显示 FPS 宏
      'MACRO_SHOW_FPS': false,
    };

    Editor.Builder.on('build-start', function (options, cb) {
      Editor.log(`构建开始！`, options);
      //解析命令行参数：
      const argv = process.argv;
      Editor.log(`宏参数：`, argv);

      let macros = ``;
      let macros1 = ``;
      let macros2 = ``;

      //处理参数
      let useKeys = {};
      let useValues = {};
      for (let i = 0; i < argv.length; i++) {
        const arg = argv[i];
        const key = arg.substring(2);
        if (Object.prototype.hasOwnProperty.call(defaultMacros, key)) {
          macros += `res['${key}'] = ${getValue(argv[i + 1])}\n`;
          macros1 += `_global.${key} = ${key};\n`;
          macros2 += `defineMacro('${key}', ${getValue(argv[i + 1])}); `;
          useKeys[key] = true;
          useValues[key] = getValue(argv[i + 1]);
        }
      }

      // 有些在库文件中定义了宏，我们也依然需要替换，因为 cocos creator 在调试模式编译时不会处理宏，需要手动处理
      function escapeRegExp(string) {
        // $&表示整个被匹配的字符串，\表示转义字符，因此需要双重转义
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      }

      function createRegExpString(pattern) {
        // 转义正则表达式特殊字符
        var escapedPattern = escapeRegExp(pattern);
        // 返回new RegExp的字符串表示
        return new RegExp(escapedPattern, 'g');
      }

      const traitsSplitHelperUrl = path.join(options.project, 'assets/advmain/libs/traitsSplitHelper.js');
      sourceTraitsSplitHelperContent = fs.readFileSync(traitsSplitHelperUrl, 'utf-8');
      let newContent = sourceTraitsSplitHelperContent;
      for (let key in defaultMacros) {
        if (key in useKeys) {
          newContent = newContent.replace(createRegExpString(`_global.${key}`), useValues[key]);
        } else {
          newContent = newContent.replace(createRegExpString(`_global.${key}`), defaultMacros[key]);
        }
      }
      fs.writeFileSync(traitsSplitHelperUrl, newContent, { encoding: `utf-8` });

      //处理默认宏
      for (let key in defaultMacros) {
        if (key in useKeys) {
          macros += `res['${key}'] = ${defaultMacros[key]}\n`;
          macros1 += `_global.${key} = ${key};\n`;
          macros2 += `defineMacro('${key}', ${defaultMacros[key]}); `;
        }
      }

      if (macros !== '') {
        const sourceUtilsUrl = `/Applications/Cocos/Creator/2.4.11/CocosCreator.app/Contents/Resources/engine/gulp/util/utils.js`;
        const sourceUtilsContent = fs.readFileSync(path.join(options.project, 'packages/macro/source/utils.js'), 'utf-8');
        const sourceUtilsContentResult = sourceUtilsContent.replace('// MICRO', macros);
        fs.writeFileSync(sourceUtilsUrl, sourceUtilsContentResult, { encoding: `utf-8` });
      }

      if (macros1 !== '' && macros2 !== '') {
        const sourcePredefineUrl = `/Applications/Cocos/Creator/2.4.11/CocosCreator.app/Contents/Resources/engine/predefine.js`;
        const sourcePredefineContent = fs.readFileSync(path.join(options.project, 'packages/macro/source/predefine.js'), 'utf-8');
        let sourcePredefineContentResult = sourcePredefineContent.replace('// MICRO1', macros1);
        sourcePredefineContentResult = sourcePredefineContentResult.replace('// MICRO2', macros2);
        fs.writeFileSync(sourcePredefineUrl, sourcePredefineContentResult, { encoding: `utf-8` });
      }
      cb();
    });

    Editor.Builder.on('build-finished', function (options, cb) {
      Editor.log(`构建完成！`, options);
      // 有些在库文件中定义了宏，还原
      const traitsSplitHelperUrl = path.join(options.project, 'assets/advmain/libs/traitsSplitHelper.js');
      fs.writeFileSync(traitsSplitHelperUrl, sourceTraitsSplitHelperContent, { encoding: `utf-8` });

      const sourceUtilsUrl = `/Applications/Cocos/Creator/2.4.11/CocosCreator.app/Contents/Resources/engine/gulp/util/utils.js`;
      fs.writeFileSync(sourceUtilsUrl, fs.readFileSync(path.join(options.project, 'packages/macro/source/utils.js'), 'utf-8'), { encoding: `utf-8` });

      const sourcePredefineUrl = `/Applications/Cocos/Creator/2.4.11/CocosCreator.app/Contents/Resources/engine/predefine.js`;
      fs.writeFileSync(sourcePredefineUrl, fs.readFileSync(path.join(options.project, 'packages/macro/source/predefine.js'), 'utf-8'), { encoding: `utf-8` });

      // 因为 cocos2d-jsb.js 文件开始就编译了，所以还需要添加新的宏定义，我们这里不直接改 cocos2d-jsb.js，而是直接改库文件
      const argv = process.argv;
      let isTraitsSplit = false;
      for (let i = 0; i < argv.length; i++) {
        const arg = argv[i];
        switch (arg) {
          case '--MACRO_TRAITS_SPLIT':
            isTraitsSplit = getValue(argv[i + 1]);
            break;
        }
      }
      if (isTraitsSplit) {
        Editor.log(`构建完成！开始特性分隔`);
        // 暂时只有调试模式支持宏定义
        if (options.debug) {
          const traitsSplitHelperUrl = path.join(options.dest, 'src/assets/advmain/libs/traitsSplitHelper.js');
          const traitsSplitHelperContent = fs.readFileSync(traitsSplitHelperUrl, 'utf-8');
          const sourceUtilsContentResult = traitsSplitHelperContent.replace('// MACRO', `_global.MACRO_TRAITS_SPLIT = true;`);
          fs.writeFileSync(traitsSplitHelperUrl, sourceUtilsContentResult, { encoding: `utf-8` });
        } else {
          Editor.warn(`构建完成！非调试模式，暂时不支持：MACRO_TRAITS_SPLIT`);
        }
      }
      cb();
    });
  },

  unload() {

  },
};