import { Component } from '../../cores/Component';
import { TargetType } from '../../define/BoardDefine';
import { AwardCondition } from '../../define/EcsDefine';

/**奖励组件 */
export default class AwardComponent extends Component {
    constructor(
        /**触发 */
        public triggerList: {
            /**条件 */
            condition: AwardCondition;
            /**目标 */
            target: TargetType;
            /**固定变化（动态变化不用传） */
            change?: number;
        }[] = [],
    ) {
        super();
    }
}
