import NodeComponent from '../components/NodeComponent';
import { ECSEvent } from '../GameEvent';
import { ECSRuleBase, IECSRuleConfig } from './core/ECSRuleBase';
import { RelationName } from '../cores/center/EntityCenter/WorldRelation';
import BoardComponent from '../components/board/BoardComponent';
import { TempleteType } from '../registry/templete/TempleteRegistry';
import { EcsViewId } from '../renderWorld/ECSViewBase';
import { BoardLayer } from '../define/BoardDefine';

export interface IClearEffectECSRuleConfig extends IECSRuleConfig {
    /**消除特效视图id */
    eliminateEcsViewId: EcsViewId;
    /**动画名称 */
    aniName: Record<number, string>;
}

/**
 * 消除特效规则
 */
export class ClearEffectECSRule extends ECSRuleBase<IClearEffectECSRuleConfig, ECSEvent.GameEvent.ELIMINATION> {
    public bindEventData() {
        return ECSEvent.GameEvent.ELIMINATION;
    }
    canExecute(): boolean {
        return true;
    }
    execute(): void {
        const info = this.eventData;
        const world = this.world;
        if (!info) return;
        const boardComp = world.getComponent(info.boardEntity, BoardComponent);
        if (!boardComp) return;
        // 根据放置形状左上格子确定颜色
        let ani = 'blue';
        ani = info.putBlockBackInfo.clearColor ? this.config.aniName[info.putBlockBackInfo.clearColor] : ani;
        // 行特效
        info.clearRow.forEach((row) => this.createEffect(boardComp, info.boardEntity, row, true, ani));
        // 列特效
        info.clearCol.forEach((col) => this.createEffect(boardComp, info.boardEntity, col, false, ani));
    }
    private createEffect(board: BoardComponent, boardEntity: number, idx: number, isRow: boolean, ani: string): void {
        const world = this.world;
        const slotEntity = world.getTargets(board.entity, RelationName.PARENT_CHILD, `${isRow ? idx : 0}_${isRow ? 0 : idx}`)[0];
        const refNode = world.getComponent(slotEntity, NodeComponent);
        if (!refNode) return;
        const length = isRow ? board.cCount : board.rCount;
        let x: number, y: number, rotation: number;
        let endNode: NodeComponent;
        let now = length - 1;
        //todo 这里判断消除位置的方式有点别扭，记录一下
        if (isRow) {
            while (!(endNode = world.getComponent(world.getTargets(board.entity, RelationName.PARENT_CHILD, `${idx}_${now}`)[0], NodeComponent))) {
                now--;
            }
            x = (refNode.x + endNode.x) / 2;
            y = refNode.y;
            rotation = 0;
        } else {
            while (!(endNode = world.getComponent(world.getTargets(board.entity, RelationName.PARENT_CHILD, `${now}_${idx}`)[0], NodeComponent))) {
                now--;
            }
            x = refNode.x;
            y = (refNode.y + endNode.y) / 2;
            rotation = 90;
        }
        // 基于 8 格设计宽度计算缩放（横向使用 cols，纵向使用 rows）
        const scaleFactor = length / 8; // 8 格为基准

        world.templeteCenter.createTempleteEntity(
            TempleteType.Effect,
            { parentEntity: boardEntity, ecsViewId: this.config.eliminateEcsViewId },
            {
                nodeParam: { x, y, angle: rotation, scaleX: scaleFactor },
                renderParam: {
                    data: {
                        aniName: ani,
                        src: 'textures/level/spine/board/xc_ske',
                        dragonNames: ['xc_tex', 'Armature'],
                        timeScale: 1.3,
                    },
                    childrenPaths: [BoardLayer[BoardLayer.BottomOn]],
                },
            },
        );
    }
}
