import { System } from '../../../cores/System';
import { ECSEvent } from '../../../GameEvent';
import { IPutBlockBackInfo, IPutBlockInfo } from '../../../GameEventData';
import NodeComponent from '../../../components/NodeComponent';
import OpacityComponent from '../../../components/OpacityComponent';
import { RelationName } from '../../../cores/center/EntityCenter/WorldRelation';
import HighlightTag from '../../../components/tag/HighlightTag';
import BoardComponent from '../../../components/board/BoardComponent';
import SlotComponent from '../../../components/board/SlotComponent';
import { CellComponent } from '../../../components/board/CellComponent';
import { TempleteType } from '../../../registry/templete/TempleteRegistry';
import { CellColor, CellType, TargetType } from '../../../define/BoardDefine';

/**
 * 预览块系统
 * 在拖拽过程中显示 50% 透明的预览形状
 */
export class PreviewSystem extends System {
    /**棋盘预览状态映射 boardEntity -> state*/
    private boardState: Map<
        number,
        {
            previewCells: number[];
            highlightedCells: number[];
            visible: boolean;
        }
    > = new Map();

    /**切换预览可见性*/
    private setPreviewVisible(state: { previewCells: number[]; visible: boolean }, visible: boolean) {
        if (state.visible === visible) return;
        state.visible = visible;
        const targetOpacity = visible ? 128 : 0;
        state.previewCells.forEach((cid) => {
            let opa = this.world.getComponent(cid, OpacityComponent);
            if (opa) opa.opacity = targetOpacity;
            else if (visible) this.world.addComponent(cid, OpacityComponent, targetOpacity);
        });
    }

    /**清除行列高亮但保留 previewCells*/
    private resetHighlights(state: { highlightedCells: number[] }) {
        state.highlightedCells.forEach((cid) => {
            const tag = this.world.getComponent(cid, HighlightTag);
            if (tag) {
                const cell = this.world.getComponent(cid, CellComponent);
                cell.temp.color = tag.oriColor;
                this.world.removeComponent(cid, tag);
            }
        });
        state.highlightedCells.length = 0;
    }

    init(): void {
        this.world.eventBus.on(ECSEvent.GameEvent.PREVIEW_BLOCK, this.onPreview, this);

        this.world.eventBus.on(ECSEvent.GameEvent.CLEAR_PREVIEW_BLOCK, this.clearPreview, this);
        // 放块成功或失败后统一清理
        this.world.eventBus.on(ECSEvent.GameEvent.PUT_BLOCK_BACK, this.clearPreview, this);
    }
    dispose(): void {
        this.world.eventBus.off(ECSEvent.GameEvent.PREVIEW_BLOCK, this.onPreview, this);
        this.world.eventBus.off(ECSEvent.GameEvent.CLEAR_PREVIEW_BLOCK, this.clearPreview, this);
        this.world.eventBus.off(ECSEvent.GameEvent.PUT_BLOCK_BACK, this.clearPreview, this);
    }

    private onPreview(info: IPutBlockInfo) {
        // 1. 若当前不在任何棋盘上，则隐藏所有已存在预览并返回
        if (info.boardId == null) {
            this.boardState.forEach((state) => {
                this.setPreviewVisible(state, false);
                this.resetHighlights(state);
            });
            return;
        }

        //先对id处理下 多层棋盘的话返回上层
        info.boardId = this.world.handlerCenter.boardHandler.getTopLayerBoardId(info.boardId);

        // 2. 寻找对应棋盘
        const boardEntity = this.findBoardEntity(info.boardId);
        if (boardEntity === null) {
            // 指定棋盘不存在，同样隐藏全部预览
            this.boardState.forEach((state) => {
                this.setPreviewVisible(state, false);
                this.resetHighlights(state);
            });
            return;
        }

        const boardComp = this.world.getComponent(boardEntity, BoardComponent);

        if (!boardComp) return;

        // 获取或创建状态
        let state = this.boardState.get(boardEntity);
        if (!state) {
            state = { previewCells: [], highlightedCells: [], visible: false };
            this.boardState.set(boardEntity, state);
        }

        // 2. 校验拖拽位置是否合法并收集目标 slot
        const { valid, newSlots } = this.validatePlacement(boardComp, info, state);
        if (!valid) return;

        // 3. 获取预览颜色与块类型
        const { color: previewColor, type: blockType } = this.extractPreviewAppearance(info.entityId);

        // 4. 更新高亮 (内部已处理重置)
        this.updateHighlight(boardComp, previewColor, info, state);

        // 5. 同步可见性
        this.setPreviewVisible(state, true);

        // 6. 各棋盘状态互不影响，无需额外处理

        // 7. 创建或更新预览实体
        if (state.previewCells.length !== info.shape.shape.length) {
            this.createPreview(boardEntity, blockType, previewColor, newSlots, state);
        } else {
            this.updatePreviewPositions(newSlots, state);
        }
    }

    /** 查找指定名称棋盘对应实体 */
    private findBoardEntity(boardId: string): number | null {
        return this.world.query([BoardComponent]).find((eid) => this.world.getComponent(eid, BoardComponent).boardId === boardId);
    }

    /** 获取拖拽块的颜色与类型 */
    private extractPreviewAppearance(entityId: number): { color: CellColor; type: CellType } {
        let cellComp: CellComponent;
        this.world.getTargets(entityId, RelationName.PARENT_CHILD).some((entity) => {
            const cell = this.world.getComponent(entity, CellComponent);
            if (cell) {
                cellComp = cell;
                return true;
            }
        });
        if (cellComp) {
            const oriColor = cellComp.oriColor as CellColor;
            return { color: oriColor === CellColor.None ? CellColor.Blue : oriColor, type: cellComp.type };
        }
        return { color: CellColor.Blue, type: TargetType.Normal };
    }

    /** 校验放置位置合法性，并返回对应 slots */
    private validatePlacement(
        boardComp: BoardComponent,
        info: IPutBlockInfo,
        state: { previewCells: number[]; highlightedCells: number[]; visible: boolean },
    ): { valid: boolean; newSlots: number[] } {
        const rows = boardComp.rCount;
        const cols = boardComp.cCount;
        const resultSlots: number[] = [];
        for (const rc of info.shape.shape) {
            const r = info.y + rc.r;
            const c = info.x + rc.c;
            if (r < 0 || r >= rows || c < 0 || c >= cols) {
                this.setPreviewVisible(state, false);
                this.resetHighlights(state);
                return { valid: false, newSlots: [] };
            }
            const slotId = this.world.getTargets(boardComp.entity, RelationName.PARENT_CHILD, `${r}_${c}`)[0];
            if (slotId == null) {
                this.setPreviewVisible(state, false);
                this.resetHighlights(state);
                return { valid: false, newSlots: [] };
            }
            if (this.world.handlerCenter.boardHandler.getSlotOccupy(this.world.getComponent(slotId, SlotComponent))) {
                this.setPreviewVisible(state, false);
                this.resetHighlights(state);
                return { valid: false, newSlots: [] };
            }
            resultSlots.push(slotId);
        }
        return { valid: true, newSlots: resultSlots };
    }

    /** 创建新的预览 Cell 实体 */
    private createPreview(boardEntity: number, blockType: CellType, previewColor: CellColor, newSlots: number[], state: { previewCells: number[] }) {
        const board = this.world.getComponent(boardEntity, BoardComponent);
        newSlots.forEach((slotId) => {
            const slot = this.world.getComponent(slotId, SlotComponent);
            const cellId = this.world.templeteCenter.createTempleteEntity(TempleteType.PreviewCell, {
                parentEntity: boardEntity,
                slotEntity: slotId,
                rc: { r: slot.r, c: slot.c },
                parentSize: { width: board.cCount, height: board.rCount },
                cellOption: {
                    type: blockType,
                    color: previewColor,
                    occupy: false,
                },
            });
            state.previewCells.push(cellId);
        });
    }

    /** 更新已有预览实体的位置 */
    private updatePreviewPositions(newSlots: number[], state: { previewCells: number[] }) {
        state.previewCells.forEach((cellId, idx) => {
            const slotNode = this.world.getComponent(newSlots[idx], NodeComponent);
            const nodeComp = this.world.getComponent(cellId, NodeComponent);
            if (slotNode && nodeComp) {
                nodeComp.x = slotNode.x;
                nodeComp.y = slotNode.y;
            }
        });
    }

    private clearPreview(data?: IPutBlockBackInfo | { boardId: string }) {
        // data 可能为 IPutBlockBackInfo 或 { boardId: string }
        let boardId: string = null;
        if (data && typeof data === 'object' && 'boardId' in data) {
            boardId = data.boardId;
        } else if (data && 'boardId' in data) {
            boardId = data.boardId;
        }
        const clearAll = !boardId;
        const entries = clearAll
            ? Array.from(this.boardState.entries())
            : Array.from(this.boardState.entries()).filter(([eid]) => {
                  const comp = this.world.getComponent(eid, BoardComponent);
                  return comp && comp.boardId === boardId;
              });
        entries.forEach(([boardEntity, state]) => {
            if (state.previewCells.length === 0 && state.highlightedCells.length === 0) return;
            state.previewCells.forEach((cid) => this.world.removeEntity(cid));
            state.previewCells.length = 0;
            // 清理高亮
            state.highlightedCells.forEach((cid) => {
                const tag = this.world.getComponent(cid, HighlightTag);
                if (tag) {
                    this.world.getComponent(cid, CellComponent).temp.color = tag.oriColor;
                    this.world.removeComponent(cid, tag);
                }
            });
            state.highlightedCells.length = 0;
            state.visible = false;
        });
    }

    /**高亮将被消除的行列*/
    private updateHighlight(board: BoardComponent, color: CellColor, info: IPutBlockInfo, state: { highlightedCells: number[] }) {
        // 清理之前高亮
        state.highlightedCells.forEach((cid) => {
            const tag = this.world.getComponent(cid, HighlightTag);
            if (tag) {
                const cell = this.world.getComponent(cid, CellComponent);
                cell.temp.color = tag.oriColor;
                this.world.removeComponent(cid, tag);
            }
        });
        state.highlightedCells.length = 0;
        // 使用 elimination 判定逻辑：检查放置后哪些行列会满
        const rowCount = board.rCount;
        const colCount = board.cCount;
        const occupancy = Array.from({ length: rowCount }, () => Array(colCount).fill(false));
        // 标记已有块
        this.world.handlerCenter.boardHandler.boardSlotEach(board, (slot) => {
            if (this.world.handlerCenter.boardHandler.getSlotOccupy(slot)) occupancy[slot.r][slot.c] = true;
        });
        // 标记预览形状
        info.shape.shape.forEach((rc) => {
            const r = info.y + rc.r;
            const c = info.x + rc.c;
            if (r >= 0 && r < rowCount && c >= 0 && c < colCount) occupancy[r][c] = true;
        });
        const fullRows: number[] = [];
        const fullCols: number[] = [];
        for (let r = 0; r < rowCount; r++) if (occupancy[r].every((v) => v)) fullRows.push(r);
        for (let c = 0; c < colCount; c++) {
            let full = true;
            for (let r = 0; r < rowCount; r++)
                if (!occupancy[r][c]) {
                    full = false;
                    break;
                }
            if (full) fullCols.push(c);
        }
        if (fullRows.length === 0 && fullCols.length === 0) return;
        fullRows.forEach((r) => {
            for (let c = 0; c < colCount; c++) {
                const slotId = this.world.getTargets(board.entity, RelationName.PARENT_CHILD, `${r}_${c}`)[0];
                if (slotId != null) this.applyHighlight(slotId, color, state);
            }
        });
        fullCols.forEach((c) => {
            for (let r = 0; r < rowCount; r++) {
                const slotId = this.world.getTargets(board.entity, RelationName.PARENT_CHILD, `${r}_${c}`)[0];
                if (slotId != null) this.applyHighlight(slotId, color, state);
            }
        });
    }

    private applyHighlight(slotId: number, color: CellColor, state: { highlightedCells: number[] }) {
        const layerEntitys = this.world.getTargets(slotId, RelationName.SLOT_CELL);
        if (layerEntitys.length === 0) return;
        layerEntitys.forEach((cellId) => {
            if (this.world.hasComponent(cellId, HighlightTag)) return;
            const cell = this.world.getComponent(cellId, CellComponent);
            const ori = cell.temp.color;
            cell.temp.color = color;
            this.world.addComponent(cellId, HighlightTag, ori);
            state.highlightedCells.push(cellId);
        });
    }

    update(): void {}
}
