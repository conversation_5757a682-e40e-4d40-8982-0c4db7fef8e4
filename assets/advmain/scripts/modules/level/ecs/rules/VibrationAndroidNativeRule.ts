import { ECSRuleBase, IECSRuleConfig } from './core/ECSRuleBase';
/**
 * Android原生震动规则配置
 */
export interface IVibrationAndroidNativeRuleConfig extends IECSRuleConfig {
    /** 震动强度 0-255 */
    strength_level: number;
    /** 震动持续时间(毫秒) */
    duration_time_second: number;
}
const TP_CLIENT_CommonInfo = {
    android_method: "callNativeCommonInfo"
};
/**
 * Android原生震动规则
 */
export class VibrationAndroidNativeRule extends ECSRuleBase<IVibrationAndroidNativeRuleConfig> {
    /**
     * 检查是否可以执行
     */
    canExecute(): boolean {
        console.log('VibrationAndroidNativeRule canExecute');
        return true;
    }
    /**
     * 执行规则
     */
    execute(): void {
        this.shakeOnce(this.config.strength_level, this.config.duration_time_second);
    }
    /**
     * 单次震动 strength_level 震动强度 范围0-255 duration_time_second 继续时间 单位是毫秒
     * 马俊松工号001040 2024年11月25日记录如下：
     * "vibrateRepeat26"为《重复震动》的参数 可以用来模拟出单次震动的效果
     * 允许在震动未结束时继续调用此接口
     * SDK内部已经做了对安卓设备版本的判断（比如Android设备版本 >= 8.0） 如果后续支持8.0以下设备 客户端无需修改业务逻辑
     * 参数解析：
     *     timings：振动的时间间隔数组（以毫秒为单位）。每个值代表一个振动阶段的开始时间。
     *     amplitudes：振动强度数组。每个值对应 timings 中阶段的振动强度，范围可能是 0（无振动）到 255（最大振动强度）。
     *     比如 {"method":"vibrateRepeat26","param":"{timings:[0,500,0,0,],amplitudes:[0,255,0,0]}的意思是
     *     立刻已255强度震动并持续0.5秒的意思
     *     timings amplitudes为数组并且元素个数需为4个 
     * 
     * 文档：https://alidocs.dingtalk.com/i/nodes/ZgpG2NdyVXab52x5UXZyA9OeJMwvDqPk?cid=63761227105&utm_source=im&utm_scene=team_space&doc_type=wiki_doc&rnd=0.5886370465987049&iframeQuery=utm_medium%3Dim_card%26utm_source%3Dportal&utm_medium=portal_recent&corpId=ding56ed6f95485bd2bc35c2f4657eb6378f
     */
    shakeOnce(strength_level: number, duration_time_second: number) {
        if (cc.sys.os === cc.sys.OS_ANDROID) {
            if (strength_level === null || strength_level === undefined || duration_time_second === null || duration_time_second === undefined) {
                return;
            }
            strength_level = Math.max(strength_level, 0);
            strength_level = Math.min(strength_level, 255);
            let shakeTemp = {
                "method": "vibrateRepeat26",
                "param": `{timings:[0,${duration_time_second},0,0],amplitudes:[0,${strength_level},0,0]}`
            };
            this.callNativeCommonInfo(shakeTemp);
        }
    }
    /**
     * 发送消息
     * @param params 
     */
    callNativeCommonInfo(params: object) {
        if (cc.sys.isNative && cc.sys.os === cc.sys.OS_ANDROID) {
            console.log('发送给Native info: callNativeCommonInfo    params:', JSON.stringify(params));
            return jsb.reflection.callStaticMethod(
                falcon.ANDROID_ACTIVE,
                TP_CLIENT_CommonInfo.android_method,
                "(Ljava/lang/String;)V",
                JSON.stringify(params)
            );
        }
    }
}
