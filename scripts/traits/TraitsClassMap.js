/**
 * 该脚本用于自动生成三个 TraitsClassMap 文件：
 * 1. assets/scripts/modules/traits/map/TraitsClassMap.ts
 * 2. assets/bundles/class/scripts/modules/traits/map/ClassTraitsClassMap.ts
 * 3. assets/bundles/chapter/scripts/modules/traits/map/ChapterTraitsClassMap.ts
 * 
 * 每个文件包含对应路径下所有以 Trait.ts 结尾的文件的引用和映射
 */

const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');

// 定义三个需要处理的路径和目标文件
const paths = [
    {
        searchPath: 'assets/bundles/traits/main/scripts',
        outputFile: 'assets/bundles/traits/main/scripts/modules/traits/map/TraitsClassMap.ts'
    },
    {
        searchPath: 'assets/bundles/class/scripts',
        outputFile: 'assets/bundles/class/scripts/modules/traits/map/ClassTraitsClassMap.ts'
    },
    {
        searchPath: 'assets/bundles/chapter/scripts',
        outputFile: 'assets/bundles/chapter/scripts/modules/traits/map/ChapterTraitsClassMap.ts'
    }
];

/**
 * 查找指定路径下所有以 Trait.ts 结尾的文件
 * @param {string} searchPath - 要搜索的路径
 * @returns {Promise<Array<{path: string, className: string}>>} - 找到的文件列表
 */
function findTraitFiles(searchPath) {
    return new Promise((resolve, reject) => {
        // 使用 find 命令查找文件，并排除 node_modules、dist、Trait.ts 和 DecoratorTrait.ts
        const command = `find ${searchPath} -name "*Trait.ts" | grep -v node_modules | grep -v dist | grep -v "/Trait.ts" | grep -v "/DecoratorTrait.ts"`;

        exec(command, (error, stdout, stderr) => {
            if (error) {
                console.error(`执行命令出错: ${error.message}`);
                reject(error);
                return;
            }

            if (stderr) {
                console.error(`命令执行错误: ${stderr}`);
            }

            const files = stdout.trim().split('\n').filter(Boolean);

            // 解析每个文件，获取导出的类名
            const promises = files.map(filePath => {
                return new Promise((res, rej) => {
                    fs.readFile(filePath, 'utf8', (err, content) => {
                        if (err) {
                            console.error(`读取文件 ${filePath} 出错: ${err.message}`);
                            rej(err);
                            return;
                        }

                        // 尝试找到类名，假设格式为 "export class ClassName"
                        const classMatch = content.match(/export\s+class\s+(\w+Trait)\s+/);
                        if (classMatch && classMatch[1]) {
                            res({
                                path: filePath,
                                className: classMatch[1]
                            });
                        } else {
                            // 如果找不到，使用文件名作为类名（去掉路径和扩展名）
                            const baseName = path.basename(filePath, '.ts');
                            res({
                                path: filePath,
                                className: baseName
                            });
                        }
                    });
                });
            });

            Promise.all(promises).then(results => resolve(results)).catch(reject);
        });
    });
}

/**
 * 生成 TraitsClassMap 文件内容
 * @param {Array<{path: string, className: string}>} traitFiles - 特征文件列表
 * @param {string} basePath - 基本路径
 * @returns {string} - 生成的文件内容
 */
function generateMapFileContent(traitFiles, basePath) {
    // 导入语句
    let imports = [];
    // 映射对象
    let mapEntries = [];

    traitFiles.forEach((file, index) => {
        // 计算相对路径
        let relativePath = path.relative(path.dirname(basePath), file.path);
        // 将 Windows 路径分隔符转换为 UNIX 样式
        relativePath = relativePath.replace(/\\/g, '/');
        // 移除扩展名
        relativePath = relativePath.replace(/\.ts$/, '');

        imports.push(`import { ${file.className} } from "${relativePath}";`);

        if (index !== traitFiles.length - 1) {
            mapEntries.push(`    ${file.className}: ${file.className},`);
        } else {
            mapEntries.push(`    ${file.className}: ${file.className}`);
        }
    });

    let last = `if (!window['__traitsClassMap__']) window['__traitsClassMap__'] = {};
Object.assign(window['__traitsClassMap__'], ${path.basename(basePath, '.ts')});`;

    return `${imports.join('\n')}\n\nexport const ${path.basename(basePath, '.ts')} = {\n${mapEntries.join('\n')}\n};\n\n${last}`;
}

/**
 * 确保目标目录存在
 * @param {string} filePath - 文件路径
 */
function ensureDirectoryExists(filePath) {
    const dirname = path.dirname(filePath);
    if (fs.existsSync(dirname)) {
        return;
    }
    ensureDirectoryExists(dirname);
    fs.mkdirSync(dirname);
}

/**
 * 处理单个路径的主函数
 * @param {Object} pathConfig - 路径配置
 */
async function processPath(pathConfig) {
    try {
        console.log(`处理路径: ${pathConfig.searchPath}`);
        const traitFiles = await findTraitFiles(pathConfig.searchPath);

        if (traitFiles.length === 0) {
            console.log(`在 ${pathConfig.searchPath} 中未找到 Trait 文件`);
            return;
        }

        console.log(`在 ${pathConfig.searchPath} 中找到 ${traitFiles.length} 个 Trait 文件`);

        const content = generateMapFileContent(traitFiles, pathConfig.outputFile);

        // 确保目标目录存在
        ensureDirectoryExists(pathConfig.outputFile);

        // 写入文件
        fs.writeFileSync(pathConfig.outputFile, content, 'utf8');
        console.log(`已生成文件: ${pathConfig.outputFile}`);
    } catch (error) {
        console.error(`处理路径 ${pathConfig.searchPath} 时出错:`, error);
    }
}

/**
 * 主函数
 */
async function main() {
    console.log('开始生成 TraitsClassMap 文件...');

    for (const pathConfig of paths) {
        await processPath(pathConfig);
    }

    console.log('所有 TraitsClassMap 文件生成完成');
}

main().catch(error => {
    console.error('执行过程中出错:', error);
    process.exit(1);
}); 