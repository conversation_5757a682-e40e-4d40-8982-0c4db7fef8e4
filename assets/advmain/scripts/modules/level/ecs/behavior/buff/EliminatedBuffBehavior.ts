import { BuffData } from "../../components/combat/BuffComponent";
import { ECSEvent } from "../../GameEvent";
import { BuffBehaviorBase } from "./BuffBehaviorBase";

export class EliminatedBuffBehavior extends BuffBehaviorBase {
    onActivate(buffData: BuffData): void {
        this.world.eventBus.emit(ECSEvent.GameEvent.CELL_ELIMINATION,{entity:buffData.ownerEntityId,clearCode:buffData.value});
    }
    onDeactivate(buffData: BuffData): void {
        
    }
}   