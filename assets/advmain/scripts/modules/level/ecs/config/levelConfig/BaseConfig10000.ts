import { FlowType } from '../../../../../base/rule/flow/FlowType';
import { IGameLevelConfig } from '../../cores/center/ConfigCenter';
import { ECSEvent } from '../../GameEvent';

export const BaseConfig10000: IGameLevelConfig = {
    levelId: 'BaseConfig10000',
    sys: [
        'system_drag_block',
        'system_interaction',
        'system_combo',
        'system_eliminate',
        'system_game_result',
        'system_preview',
        'system_target',
        'system_highlight_notifier',
        'system_block_algorithm',
    ],
    ruleFlowConfigForWorldInit: {
        type: FlowType.List,
        children: [
            'rule_init_board_scene',
            'rule_init_board',
            'rule_init_target',
            'rule_show_target_info',
            'rule_produce_block_algorithm_algoRandom',
            'rule_drag_multiplier',
            'rule_init_board_scene_colors',
            'rule_init_multi_elimination_special_effect',
        ],
    },
    ruleFlowConfigForEvent: {
        [ECSEvent.GameEvent.ELIMINATION]: {
            type: FlowType.List,
            children: [
                'rule_clear_effect',
                'rule_check_remain_shape',
                'rule_show_multi_clear_word',
                'rule_elimination_score_calculator',
                'rule_combo_effect_audio_play',
            ],
        },
        [ECSEvent.GameEvent.PRODUCE_BLOCK]: {
            type: FlowType.Select,
            children: ['rule_multi_stage_progress_algorithm', 'rule_produce_block_algorithm_algoRandom'],
        },
        [ECSEvent.GameEvent.PRODUCE_BLOCK_END]: {
            type: FlowType.List,
            children: ['rule_produce_block_shape', 'rule_produce_block_color', 'rule_produce_block_collectible', 'rule_produce_block_create_entity'],
        },
        [ECSEvent.GameEvent.PUT_BLOCK_BACK]: {
            type: FlowType.List,
            children: ['rule_check_remain_shape', 'rule_put_block_score_calculator'],
        },
        [ECSEvent.GameEvent.COMBO_CHANGED]: {
            type: FlowType.List,
            children: ['rule_combo_play_sound'],
        },
        [ECSEvent.GameEvent.SINGLE_SCORE_EFFECT]: {
            type: FlowType.List,
            children: ['rule_single_score_effect'], // 单次得分特效规则
        },
        [ECSEvent.GameEvent.CELL_REMOVED]: {
            type: FlowType.List,
            children: ['rule_cell_removed'],
        },
        [ECSEvent.GameEvent.PRODUCE_BLOCK_REVIVE]: {
            type: FlowType.List,
            children: ['rule_produce_block_algorithm_algo_revive'],
        },
        [ECSEvent.GameEvent.DRAG_BLOCK_START]: {
            type: FlowType.List,
            children: ['rule_drag_effect_audio_play'],
        },
        [ECSEvent.GameEvent.GAME_FAILED]: {
            type: FlowType.Select,
            children: [
                {
                    type: FlowType.List,
                    children: [
                        'rule_revive_preview_blocks',
                        'rule_revive'
                    ],
                },
                {
                    type: FlowType.List,
                    children: ['rule_board_refurbish_effect', 'rule_game_score_fail'],
                },
            ],
        },
        [ECSEvent.GameEvent.REVIVE_CONFIRMED]: {
            type: FlowType.List,
            children: ['rule_revive_show_blocks'],
        },
        [ECSEvent.GameEvent.GAME_WIN]: {
            type: FlowType.List,
            children: ['rule_game_score_win'],
        },
    },
    snapshotEvents: [ECSEvent.BaseEvent.INIT_WORLD_COMPLETE, ECSEvent.GameEvent.PUT_BLOCK, ECSEvent.GameEvent.PRODUCE_BLOCK_END],
};
