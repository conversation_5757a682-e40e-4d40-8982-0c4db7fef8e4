import { ECSEvent } from '../GameEvent';
import { ECSExecuteContext, ECSRuleBase, IECSRuleConfig } from './core/ECSRuleBase';
import { TempleteType } from '../registry/templete/TempleteRegistry';
import BoardComponent from '../components/board/BoardComponent';
import { EcsViewId } from '../renderWorld/ECSViewBase';
import { BoardLayer } from '../define/BoardDefine';

export interface ISingleScoreEffectECSRuleConfig extends IECSRuleConfig {
    /**单次分数展示视图id */
    ecsViewId: EcsViewId;
}

/**
 * 单次分数展示特效规则
 */
export default class SingleScoreEffectECSRule extends ECSRuleBase<ISingleScoreEffectECSRuleConfig, ECSEvent.GameEvent.SINGLE_SCORE_EFFECT> {
    public bindEventData() {
        return ECSEvent.GameEvent.SINGLE_SCORE_EFFECT;
    }

    canExecute(): boolean {
        return true; // 这里可以添加具体的执行条件
    }

    execute(): void {
        const world = this.world;
        const eventData = this.eventData;
        const boardEntity = eventData.boardEntity;
        const boardComp = world.getComponent(boardEntity, BoardComponent);
        if (!boardComp) return;
        const position = eventData.position || { x: 0, y: 0 };
        const effectEntity = world.templeteCenter.createTempleteEntity(
            TempleteType.Render,
            {
                parentEntity: boardEntity,
                ecsViewId: this.config.ecsViewId,
            },
            {
                nodeParam: position,
                renderParam: {
                    data: { score: eventData.score, eliminateCount: eventData.eliminateCount },
                    childrenPaths: [BoardLayer[BoardLayer.Top]],
                    isBreakLink: true,
                },
            },
        );
        world.destroyEntity(effectEntity);
    }
}
