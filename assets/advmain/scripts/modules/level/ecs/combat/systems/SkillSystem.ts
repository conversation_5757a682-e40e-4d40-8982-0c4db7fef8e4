import { System } from "../../cores/System";
import { Skill, SkillState } from "../component/Skill";

/**
 * 技能系统
 * 负责更新技能状态和处理组件清理
 */
export class SkillSystem extends System {
    init(): void {
        // 只监听组件移除
        this.world.onComponentRemove(Skill, this.onSkillComponentRemove, this);
    }

    dispose(): void {
        // 移除监听器，防止内存泄漏
        this.world.offComponentRemove(Skill, this.onSkillComponentRemove, this);
    }

    /**
     * 处理技能组件移除
     * 确保所有技能资源被正确清理
     */
    private onSkillComponentRemove = (entityId: number, component: Skill): void => {
        if (component.skillList.length === 0) return;
        
        console.log(`[SkillSystem] 清理实体 ${entityId} 的 ${component.skillList.length} 个技能`);
        
        // 先拷贝skillList，防止在遍历过程中修改数组导致遍历失效
        const skillListCopy = [...component.skillList];
        const skillHandler = this.world.handlerCenter.skillHandler;
        
        skillListCopy.forEach(skill => {
            // 调用完整的removeSkill方法，确保完整的移除逻辑
            skillHandler.removeSkill(skill);
        });
    }

    /**
     * 系统更新
     * @param dt 时间增量（秒）
     */
    update(dt: number): void {
        // 获取所有拥有技能组件的实体
        const entities = this.world.query([Skill]);
        const world = this.world;
        const skillHandler = world.handlerCenter.skillHandler;
        
        // 遍历所有实体
        for (const entityId of entities) {
            // 获取技能组件
            const component = world.getComponent<Skill>(entityId, Skill);
            if (!component) continue;

            const skillList = component.skillList;

            // 更新所有技能
            for (const skillData of skillList) {
                // 展开的 updateSkill 实现
                const skillBehavior = skillHandler.behavior.getSkillBehaviorById(skillData.skillId);
                if (!skillBehavior || !skillBehavior.onUpdate) continue;

                // 调用行为的更新方法，获取技能状态
                const newState = skillBehavior.onUpdate(skillData, dt);

                // 如果状态发生变化，更新技能状态
                if (newState !== skillData.state) {
                    const oldState = skillData.state;
                    skillData.state = newState;

                    // 如果技能从执行状态变为非活跃状态，表示完成
                    if (oldState === SkillState.Casting && newState === SkillState.Inactive) {
                        skillHandler.completeSkill(skillData);
                    }
                }
            }
        }
    }
} 