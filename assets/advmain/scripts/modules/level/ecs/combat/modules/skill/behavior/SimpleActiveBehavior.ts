import { World } from "../../../../cores/World";
import { SkillData, SkillState } from "../../../component/Skill";
import { SkillBehaviorBase } from "../core/ISkillBehavior";
import { IBaseSkillConfig } from "../../../../config/SkillConfig";
import { IBehaviorData } from "../../../core/BehaviorBase";
import { IActionContext } from "../../../core/ActionContext";

/**
 * 简单主动技能配置
 */
export interface ISimpleActiveConfig extends IBaseSkillConfig {
    castTime: number;           // 施法时间（毫秒），0表示瞬发
    range: number;              // 施法距离
    launcherIds: string[];      // 发射器ID列表
}

/**
 * 简单主动技能行为数据
 */
interface SimpleActiveBehaviorData extends IBehaviorData {
    startTime: number;         // 施法开始时间
    castTime: number;          // 施法时间
    targetEntityId?: number;   // 目标实体ID
    targetPosition?: { x: number, y: number }; // 目标位置
}

/**
 * 简单主动技能行为
 * 最基础的主动技能实现：施法时间 -> 效果触发 -> 完成
 * 
 * 特点：
 * 1. 支持瞬发和需要施法时间的技能
 * 2. 支持目标验证和射程检查
 * 3. 与发射器系统配合，管理发射器的创建
 * 4. 冷却管理由SkillHandler统一处理
 * 5. 使用新的生命周期管理系统
 */
export class SimpleActiveBehavior extends SkillBehaviorBase<ISimpleActiveConfig> {

    onAdd(skillData: SkillData<SimpleActiveBehaviorData>): void {
        // 初始化行为数据（被动技能或技能添加时的逻辑）
        console.log(`简单主动技能已添加: ${skillData.skillId}, 射程: ${this.config ? this.config.range : 0}m`);

        // 初始化行为数据
        const behaviorData: SimpleActiveBehaviorData = {
            startTime: 0,
            castTime: this.config ? this.config.castTime : 0,
            targetEntityId: undefined,
            targetPosition: undefined
        };
        skillData.behaviorData = behaviorData;

        console.log(`[SimpleActiveBehavior] 初始化技能行为数据: ${skillData.skillId}`);
    }

    onRemove(skillData: SkillData<SimpleActiveBehaviorData>): void {
        // 技能移除时的清理逻辑
        console.log(`简单主动技能已移除: ${skillData.skillId}`);
    }

    onCast(skillData: SkillData<SimpleActiveBehaviorData>, context?: IActionContext): SkillState {
        if (!this.config) {
            console.error(`[SimpleActiveBehavior] 技能配置缺失: ${skillData.skillId}`);
            return SkillState.Inactive;
        }

        // 初始化行为数据
        const behaviorData: SimpleActiveBehaviorData = {
            startTime: this.world.worldTime,
            castTime: this.config.castTime,
            targetEntityId: context?.targetEntityId,
            targetPosition: context?.targetPosition
        };
        skillData.behaviorData = behaviorData;

        console.log(`[SimpleActiveBehavior] 开始施法: ${skillData.skillId}, 施法时间: ${this.config.castTime}ms`);
        if (behaviorData.targetEntityId) {
            console.log(`  目标实体: ${behaviorData.targetEntityId}`);
        }
        if (behaviorData.targetPosition) {
            console.log(`  目标位置: (${behaviorData.targetPosition.x}, ${behaviorData.targetPosition.y})`);
        }

        // 如果是瞬发技能，立即执行效果并完成
        if (!this.config.castTime || this.config.castTime <= 0) {
            this.executeSkillEffect(skillData);
            return SkillState.Inactive; // 瞬发技能立即完成
        }

        // 需要施法时间，返回Casting状态
        return SkillState.Casting;
    }

    onUpdate(skillData: SkillData<SimpleActiveBehaviorData>, deltaTime: number): SkillState {
        if (!this.config || !skillData.behaviorData) return SkillState.Inactive;

        const behaviorData = skillData.behaviorData;
        const elapsed = this.world.worldTime - behaviorData.startTime;

        // 检查施法是否完成
        if (elapsed >= behaviorData.castTime) {
            console.log(`[SimpleActiveBehavior] 施法完成: ${skillData.skillId}`);
            this.executeSkillEffect(skillData);
            return SkillState.Inactive; // 施法完成，技能结束
        }
        return SkillState.Casting; // 继续施法
    }

    onComplete(skillData: SkillData<SimpleActiveBehaviorData>): void {
        console.log(`[SimpleActiveBehavior] 技能完成: ${skillData.skillId}`);
        // 可以在这里添加完成后的清理逻辑
    }

    /**
     * 执行技能效果
     * 在技能施法完成时调用，负责创建发射器
     */
    private executeSkillEffect(skillData: SkillData<SimpleActiveBehaviorData>): void {
        if (!this.config || !this.config.launcherIds) return;

        console.log(`[SimpleActiveBehavior] 执行技能效果: ${skillData.skillId}`);

        const behaviorData = skillData.behaviorData;
        const launcherHandler = this.world.utilCenter.launcherHandler;

        // 创建所有配置的发射器
        for (const launcherId of this.config.launcherIds) {
            const result = launcherHandler.createLauncher(
                skillData.casterEntityId,
                launcherId,
                undefined, // ownerEntityId - 使用默认值（等于casterEntityId）
                {
                    targetEntityId: behaviorData.targetEntityId,
                    targetPosition: behaviorData.targetPosition
                }
            );

            if (result) {
                console.log(`  创建发射器成功: ${launcherId}, 实例ID: ${result}`);
            } else {
                console.warn(`  创建发射器失败: ${launcherId}`);
            }
        }
    }
}
