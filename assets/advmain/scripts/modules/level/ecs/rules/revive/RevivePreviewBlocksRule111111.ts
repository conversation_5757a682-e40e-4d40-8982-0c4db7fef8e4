import { BoardScene } from '../../components/board/BoardScene';
import { ECSRuleBase, IECSRuleConfig } from '../core/ECSRuleBase';
import { ECSEvent } from '../../GameEvent';
import { IProduceBlockEnd } from '../../GameEventData';
import { MathUtil } from '../../utils/MathUtil';

export interface IRevivePreviewBlocksRuleConfig extends IECSRuleConfig {
    // 可以添加配置参数
}

/**
 * 复活显示块规则
 * 当玩家确认复活时，在下方操作区域显示复活块
 */
export class RevivePreviewBlocksRule extends ECSRuleBase<IRevivePreviewBlocksRuleConfig> {
    
    bindEventData() {
        return ECSEvent.GameEvent.REVIVE_CONFIRMED;
    }
    
    canExecute(): boolean {
        return true;
    }
    
    execute(): void {
        const world = this.world;
        const reviveComp = world.utilCenter.reviveUtil.getReviveComponent();

        if (!reviveComp || !reviveComp.cachedReviveBlocks) {
            console.error('ReviveShowBlocksRule: 没有缓存的复活块数据');
            return;
        }

        // 1. 清除预览效果（如果有的话）
        this.clearPreviewBlocks(world);

        // 2. 使用缓存的数据创建实际的可拖拽块
        this.createReviveBlocks(world, reviveComp.cachedReviveBlocks);

        // 3. 扣除复活次数并清除缓存
        world.utilCenter.reviveUtil.consumeReviveCount();
        world.utilCenter.reviveUtil.clearPreviewData();

        console.log('ReviveShowBlocksRule: 复活块显示完成');


    }
    
    private clearPreviewBlocks(world: any): void {
        // 清除棋盘上的预览效果
        // 具体实现根据预览显示方式而定
        // 这里可以根据需要实现预览清除逻辑
        console.log('ReviveShowBlocksRule: 清除预览块');
    }
    
    private createReviveBlocks(world: any, blockData: IProduceBlockEnd): void {
        // 复用 ProduceBlockEndRule 的逻辑创建实际的方块
        const sceneEntity = world.ECSLayerType.SceneLayerEntity;
        const boardSceneComp = world.getComponent(sceneEntity, BoardScene);
        
        if (!boardSceneComp) {
            console.error('ReviveShowBlocksRule: 找不到BoardScene组件');
            return;
        }
    }
}
