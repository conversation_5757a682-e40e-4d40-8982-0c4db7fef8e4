/**
 * AdvGameBridge 类型声明文件
 * 基于 AdvGameBridge.ts v1.0.0 生成
 * 
 * 此文件包含 AdvGameBridge 的完整类型声明，适用于内部开发和维护。
 * 包含所有公共和私有接口定义，与源码保持同步。
 */

import { AdvGameBridgeProgressManager, IProgressEventData } from "./managers/AdvGameBridgeProgressManager";

// ================================
// 接口定义
// ================================

/**
 * 子游戏Bundle配置接口（统一配置）
 */
export interface ISubGameConfig {
    bundleName: string;
    version: string;
    entryScene?: string;
    preload?: boolean;              // 是否预加载此Bundle
    preloadDirs?: IPreloadDirConfig[];  // Bundle资源预加载目录配置
}

/**
 * 预下载目录配置接口
 */
export interface IPreloadDirConfig {
    path: string;
    type?: any;
    priority?: number;
}

/**
 * 远程URL配置接口
 */
export interface IRemoteUrlConfig {
    baseUrl: string;
    bundleBasePath?: string;
}

/**
 * 库文件配置接口
 */
export interface ILibraryConfig {
    libsPath: string;
    requiredLibs: string[];
}

/**
 * AdvGameBridge游戏数据接口
 * todo 有新加的字段在这扩展
 */
export interface IAdvGameBridgeGameData {
    classicScore: number; // 无尽分数
    [key: string]: any; // 允许扩展其他字段
}

/**
 * AdvGameBridge玩家数据接口
 */
export interface IAdvGameBridgePlayerData {
    playerId: string;
    playerName: string;
    [key: string]: any; // 允许扩展其他字段
}

/**
 * AdvGameBridge游戏设置接口
 */
export interface IAdvGameBridgeGameSettings {
    soundEnabled: boolean;
    musicEnabled: boolean;
    language: string;
    [key: string]: any; // 允许扩展其他字段
}

/**
 * AdvGameBridge基础数据接口
 * 包含游戏启动时需要的核心数据
 */
export interface IAdvGameBridgeBaseData {
    gameData: IAdvGameBridgeGameData;
    playerData: IAdvGameBridgePlayerData;
    settings: IAdvGameBridgeGameSettings;
    [key: string]: any; // 允许传入额外的配置数据
}

// ================================
// 主类定义
// ================================

/**
 * AdvGameBridge 主类
 * 用于连接主游戏和子游戏的桥接类
 */
export default class AdvGameBridge {
    // ================================
    // 静态属性和方法
    // ================================
    
    /** Bridge版本信息 */
    public static readonly BRIDGE_VERSION: string;
    
    /** 获取单例实例 */
    public static getInstance(): AdvGameBridge;

    // ================================
    // 初始化方法
    // ================================
    
    /**
     * 初始化桥接类
     * @param config 可选的配置参数
     * @returns 是否初始化成功
     */
    public initialize(config?: any): Promise<boolean>;

    // ================================
    // 游戏控制接口
    // ================================
    
    /**
     * 进入子游戏
     * @param bundleName Bundle名称，默认为'advmain'
     * @param sceneName 场景名称，可选
     * @param version 版本号，可选
     * @returns 是否成功进入游戏
     */
    public enterGame(bundleName?: string, sceneName?: string, version?: string): Promise<boolean>;
    
    /**
     * 返回主游戏
     */
    public returnToMainGame(): void;

    // ================================
    // 数据管理接口
    // ================================
    
    /**
     * 获取基础数据
     * @returns 完整的基础数据或null
     */
    public getBaseData(): IAdvGameBridgeBaseData | null;
    
    /**
     * 获取基础数据的特定字段
     * @param key 要获取的数据键名
     * @returns 对应的数据值
     */
    public getBaseData(key: keyof IAdvGameBridgeBaseData): any;
    
    /**
     * 获取当前游戏数据
     * @returns 游戏数据或null
     */
    public getCurrentGameData(): IAdvGameBridgeGameData | null;
    
    /**
     * 获取当前玩家数据
     * @returns 玩家数据或null
     */
    public getCurrentPlayerData(): IAdvGameBridgePlayerData | null;
    
    /**
     * 获取当前游戏设置
     * @returns 游戏设置或null
     */
    public getCurrentGameSettings(): IAdvGameBridgeGameSettings | null;
    
    /**
     * 更新游戏数据
     * @param data 要更新的游戏数据（部分更新）
     */
    public updateGameData(data: Partial<IAdvGameBridgeGameData>): void;
    
    /**
     * 更新玩家数据
     * @param data 要更新的玩家数据（部分更新）
     */
    public updatePlayerData(data: Partial<IAdvGameBridgePlayerData>): void;
    
    /**
     * 更新游戏设置
     * @param settings 要更新的游戏设置（部分更新）
     */
    public updateGameSettings(settings: Partial<IAdvGameBridgeGameSettings>): void;

    // ================================
    // 进度管理（事件驱动）
    // ================================
    
    /**
     * 监听进度更新事件
     * @param listener 进度更新监听器
     */
    public onProgressUpdate(listener: (data: IProgressEventData) => void): void;
    
    /**
     * 移除进度更新事件监听器
     * @param listener 要移除的监听器，如果不提供则移除所有监听器
     */
    public offProgressUpdate(listener?: (data: IProgressEventData) => void): void;
    
    /**
     * 监听阶段完成事件
     * @param listener 阶段完成监听器
     */
    public onPhaseComplete(listener: (data: { phase: string; phaseName: string }) => void): void;
    
    /**
     * 移除阶段完成事件监听器
     * @param listener 要移除的监听器，如果不提供则移除所有监听器
     */
    public offPhaseComplete(listener?: (data: { phase: string; phaseName: string }) => void): void;
    
    /**
     * 监听所有阶段完成事件
     * @param listener 完成监听器
     */
    public onAllComplete(listener: () => void): void;
    
    /**
     * 移除所有阶段完成事件监听器
     * @param listener 要移除的监听器，如果不提供则移除所有监听器
     */
    public offAllComplete(listener?: () => void): void;
    
    /**
     * 获取进度管理器（用于直接访问事件API）
     * @returns 进度管理器实例
     */
    public getProgressManager(): AdvGameBridgeProgressManager;

    // ================================
    // Bundle管理
    // ================================
    
    /**
     * 获取子游戏配置
     * @param bundleName Bundle名称
     * @returns 配置对象或null
     */
    public getSubGameConfig(bundleName: string): ISubGameConfig | null;
    
    /**
     * 获取所有子游戏配置
     * @returns 所有配置的记录对象
     */
    public getAllSubGameConfigs(): Record<string, ISubGameConfig>;
    
    /**
     * 检查Bundle是否已预加载
     * @param bundleName Bundle名称
     * @returns 是否已预加载
     */
    public isBundlePreloaded(bundleName: string): boolean;
    
    /**
     * 获取Bundle版本号
     * @param bundleName Bundle名称
     * @returns 版本号或null
     */
    public getBundleVersion(bundleName: string): string | null;
    
    /**
     * 获取预加载状态
     * @param bundleName 可选的Bundle名称
     * @returns 预加载状态信息
     */
    public getPreloadStatus(bundleName?: string): any;
    
    /**
     * 检查Bundle是否配置为预加载
     * @param bundleName Bundle名称
     * @returns 是否配置为预加载
     */
    public isBundleConfiguredForPreload(bundleName: string): boolean;
    
    /**
     * 获取所有配置为预加载的Bundle列表
     * @returns 预加载Bundle名称数组
     */
    public getPreloadConfiguredBundles(): string[];
    
    /**
     * 获取所有未配置预加载的Bundle列表
     * @returns 非预加载Bundle名称数组
     */
    public getNonPreloadBundles(): string[];

    // ================================
    // 预加载管理
    // ================================
    
    /**
     * 获取Bundle配置（包含预加载配置）
     * @param bundleName Bundle名称
     * @returns 配置对象或null
     */
    public getPreloadConfig(bundleName: string): ISubGameConfig | null;
    
    /**
     * 获取所有Bundle配置（包含预加载配置）
     * @returns 所有配置的记录对象
     */
    public getAllPreloadConfigs(): Record<string, ISubGameConfig>;
    
    /**
     * 检查Bundle目录是否已预加载
     * @param bundleName Bundle名称
     * @param dirPath 目录路径
     * @returns 是否已预加载
     */
    public isBundleDirPreloaded(bundleName: string, dirPath: string): boolean;
    
    /**
     * 获取已预加载的目录列表
     * @param bundleName 可选的Bundle名称
     * @returns 预加载目录信息
     */
    public getPreloadedDirs(bundleName?: string): any;

    // ================================
    // 消息系统
    // ================================
    
    /**
     * 发送消息到主游戏
     * @param type 消息类型
     * @param data 消息数据（可选）
     */
    public sendToMainGame(type: string, data?: any): void;
    
    /**
     * 发送消息到子游戏
     * @param type 消息类型
     * @param data 消息数据（可选）
     */
    public sendToSubGame(type: string, data?: any): void;
    
    /**
     * 监听来自主游戏的消息
     * @param callback 消息回调函数
     */
    public onMessageFromMainGame(callback: (type: string, data: any) => void): void;
    
    /**
     * 移除主游戏消息监听器
     */
    public offMessageFromMainGame(): void;
    
    /**
     * 监听来自子游戏的消息
     * @param callback 消息回调函数
     */
    public onMessageFromSubGame(callback: (type: string, data: any) => void): void;
    
    /**
     * 移除子游戏消息监听器
     */
    public offMessageFromSubGame(): void;
    
    /**
     * 获取消息系统状态
     * @returns 消息系统状态信息
     */
    public getMessageSystemStatus(): any;

    // ================================
    // 配置和版本信息
    // ================================
    
    /**
     * 获取远程URL配置
     * @returns 远程URL配置副本
     */
    public getRemoteUrlConfig(): IRemoteUrlConfig;
    
    /**
     * 获取库文件配置
     * @returns 库文件配置副本
     */
    public getLibraryConfig(): ILibraryConfig;
    
    /**
     * 获取Bridge版本号
     * @returns 版本号字符串
     */
    public getBridgeVersion(): string;
    
    /**
     * 检查是否已初始化
     * @returns 是否已初始化
     */
    public isInitialized(): boolean;

    // ================================
    // 销毁和清理
    // ================================
    
    /**
     * 销毁Bridge实例，清理所有资源
     */
    public destroy(): void;
}

// ================================
// 导出的实例和类型
// ================================

/**
 * AdvGameBridge的全局单例实例
 */
export declare const advGameBridge: AdvGameBridge;

/**
 * 进度事件数据接口（从ProgressManager导入）
 */
export { IProgressEventData }; 