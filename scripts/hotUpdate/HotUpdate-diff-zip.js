const path = require('path');
const fs = require('fs');
const AdmZip = require('adm-zip');

const MACRO_REMOTE_SERVER_RES_URL = process.argv[2];
const platform = process.argv[3];
const ENV = process.argv[4];

const url = `/Users/<USER>/blockblast-adv/hotUpdate/${platform}/${ENV}`;

// 匹配 x.x.x 版本号的文件夹
function getVersionDirs(dir) {
    return fs.readdirSync(dir)
        .filter(name => /^\d+\.\d+\.\d+$/.test(name))
        .sort((a, b) => {
            // 版本号排序
            const pa = a.split('.').map(Number);
            const pb = b.split('.').map(Number);
            for (let i = 0; i < 3; i++) {
                if (pa[i] !== pb[i]) return pa[i] - pb[i];
            }
            return 0;
        });
}

// 获取文件夹下所有文件的相对路径
function getAllFiles(dir, base = dir) {
    let files = [];
    fs.readdirSync(dir).forEach(file => {
        if (file === '.DS_Store') return; // 排除.DS_Store
        const fullPath = path.join(dir, file);
        if (fs.statSync(fullPath).isDirectory()) {
            files = files.concat(getAllFiles(fullPath, base));
        } else {
            files.push(path.relative(base, fullPath));
        }
    });
    return files;
}

// 生成差异包
function createDiffZip(oldDir, newDir, oldVer, newVer, outputDir) {
    const oldFiles = getAllFiles(oldDir);
    const newFiles = getAllFiles(newDir);

    // 差异文件：新版本有但旧版本没有，或内容不同
    const diffFiles = newFiles.filter(f => {
        const oldFilePath = path.join(oldDir, f);
        const newFilePath = path.join(newDir, f);
        if (!fs.existsSync(oldFilePath)) return true;
        // 简单内容比对
        const oldBuf = fs.readFileSync(oldFilePath);
        const newBuf = fs.readFileSync(newFilePath);
        return !oldBuf.equals(newBuf);
    });

    if (diffFiles.length === 0) {
        console.log(`无差异: ${oldVer} -> ${newVer}`);
        return;
    }

    const zip = new AdmZip();
    diffFiles.forEach(f => {
        const filePath = path.join(newDir, f);

        // 这两个文件过滤（压缩包里不需要，外面会生成新的）
        if (f.indexOf("project.manifest") !== -1 || f.indexOf("version.manifest") !== -1) {
            return;
        }

        zip.addLocalFile(filePath, path.dirname(f));
    });

    const zipName = `data.zip`;
    const key = `${oldVer}_${newVer}`;
    const zipUrl = `zips/${key}/${zipName}`;
    const zipPath = path.join(outputDir, zipUrl);

    // 创建 diff 文件夹
    const diffDir = path.dirname(zipPath);
    if (!fs.existsSync(diffDir)) {
        fs.mkdirSync(diffDir, { recursive: true });
    }

    // 写入 zip 文件
    zip.writeZip(zipPath);
    console.log(`生成差异包: ${zipUrl}`);

    // 获取 size 与 md5 值
    const stat = fs.statSync(zipPath);
    const size = stat.size;
    const crypto = require('crypto');
    const md5 = crypto.createHash('md5').update(fs.readFileSync(zipPath)).digest('hex');

    let zipHotUpdateBaseServerUrl;
    if (ENV === 'prod') {
        // 注意：正式环境热更服务器地址没有 platform 与 ENV 前缀了
        zipHotUpdateBaseServerUrl = MACRO_REMOTE_SERVER_RES_URL + `/hotUpdate/zips`;
    } else {
        zipHotUpdateBaseServerUrl = MACRO_REMOTE_SERVER_RES_URL + `/hotUpdate/${platform}/${ENV}/zips`;
    }

    console.log(`热更差异包 zip 服务器 base 地址: ${zipHotUpdateBaseServerUrl}`);

    const saveUrl = path.join(url, `zips/${key}`);
    // 生成 project.manifest
    fs.writeFileSync(path.join(saveUrl, 'project.manifest'), JSON.stringify({
        url1: `${zipHotUpdateBaseServerUrl}/${key}`,
        url2: `${zipHotUpdateBaseServerUrl}/${key}/project.manifest`,
        url3: `${zipHotUpdateBaseServerUrl}/${key}/version.manifest`,
        version: newVer,
        assets: {
            [zipName]: {
                size: size,
                md5: md5,
                compressed: true
            }
        },
        searchPaths: []
    }));

    console.log(`生成差异包 project.manifest:`, path.join(saveUrl, 'project.manifest'));

    // 生成 version.manifest
    fs.writeFileSync(path.join(saveUrl, 'version.manifest'), JSON.stringify({
        url1: `${zipHotUpdateBaseServerUrl}/${key}`,
        url2: `${zipHotUpdateBaseServerUrl}/${key}/project.manifest`,
        url3: `${zipHotUpdateBaseServerUrl}/${key}/version.manifest`,
        version: newVer
    }));

    console.log(`生成差异包 version.manifest:`, path.join(saveUrl, 'version.manifest'));

    return {
        size,
        version: key
    };
}

// 主流程
function main() {
    const versions = getVersionDirs(url);
    if (versions.length < 2) {
        console.log('没有足够的版本进行比对');
        return;
    }
    const newVer = versions[versions.length - 1];
    const oldVers = versions.slice(-21, -1); // 最多往前20个

    const sizeObj = [];
    oldVers.forEach(oldVer => {
        const oldDir = path.join(url, oldVer);
        const newDir = path.join(url, newVer);
        const sizeInfo = createDiffZip(oldDir, newDir, oldVer, newVer, url);
        sizeObj.push(sizeInfo);
    });

    fs.writeFileSync(path.join(url, 'hotDiff.json'), JSON.stringify(sizeObj));
}

main();