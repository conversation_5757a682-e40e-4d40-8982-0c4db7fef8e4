import TargetComponent from '../components/special/TargetComponent';
import { ECSRuleBase, IECSRuleConfig } from './core/ECSRuleBase';

export interface IDecreaseTargetRuleConfig extends IECSRuleConfig {
    // 离线时长，单位待定
    offlineDuration: number;
    // 减少目标比例 例：0.6就是目标减少60%
    decreaseRatio: number;
}

export class DecreaseTargetRule extends ECSRuleBase<IDecreaseTargetRuleConfig> {
    canExecute(): boolean {
        return true;
    }

    execute(): void {
        const targets = this.world.query([TargetComponent]);
        if (targets.length === 0) {
            console.warn('DecreaseTargetRule: No TargetComponent found');
            return;
        }
        const targetComponent = this.world.getComponent(targets[0], TargetComponent);
        if (!targetComponent || !targetComponent.targets || targetComponent.targets.length === 0) {
            console.warn('DecreaseTargetRule: No targets found in TargetComponent');
            return;
        }
        // 减少目标
        targetComponent.targets.forEach((target) => {
            if (target.to > 0) {
                target.to = Math.max(0, target.to - Math.floor(target.to * this.config.decreaseRatio));
            }
        });
    }
}
