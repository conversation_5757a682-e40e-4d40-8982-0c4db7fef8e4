import { ECSRuleBase, IECSRuleConfig } from '../core/ECSRuleBase';
import ProduceBlockAlgorithmComponent from '../../components/special/ProduceBlockAlgorithmComponent';
import { ECSEvent } from '../../GameEvent';

export interface IRevivePreviewBlocksRuleConfig extends IECSRuleConfig {
}

/**
 * 复活预览块规则
 * 在复活弹窗显示前，先触发出块计算并标记为预览模式
 */
export class RevivePreviewBlocksRule extends ECSRuleBase<IRevivePreviewBlocksRuleConfig> {

    bindEventData() {
        return ECSEvent.GameEvent.REVIVE_PREVIEW_COMPLETE;
    }

    canExecute(): boolean {
        return true;
    }

    execute(): void {
        const world = this.world;
        this.eventData;
        console.log('RevivePreviewBlocksRule: 复活预览块规则', this.eventData);
    }
}
