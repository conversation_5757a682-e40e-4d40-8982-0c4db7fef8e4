import { IBaseSkillConfig } from '../../config/SkillConfig';
import { ISkillCooldownConfig } from '../../config/SkillCooldownConfig';
import { ConfigTableConst, ConfigRegistry } from '../../registry/ConfigRegistry';
import { IECSViewConfig } from '../../renderWorld/ECSViewBase';
import { ISystemConfig } from '../System';
import { IECSFlowConfig } from '../../systems/RuleSystem';
import { IECSRuleConfig } from '../../rules/core/ECSRuleBase';
import { ISerializable } from '../ISerializable';
import { IBuffBehaviorConfig } from '../../behavior/buff/BuffBehaviorBase';
import { IBoardConfig } from '../../config/conf/BoardConfig';
import { IOverrideLevelConfig, OverrideConfigManager } from '../OverrideConfigManager';
import { ECSEvent } from '../../GameEvent';
/**
 * 游戏配置接口
 */
export interface IGameLevelConfig {
    /** 关卡ID */
    levelId: string;
    /** 基础关卡ID */
    baseLevelId: string;
    /** 逻辑系统列表 */
    sys: string[];
    /** 初始化世界规则流ID */
    initWorldRuleFlowConfig: IECSFlowConfig;
    /** 初始化所有规则流配置 */
    initAllRuleFlowConfig: Record<string, IECSFlowConfig>;
    /** 快照事件列表 */
    snapshotEvents: (ECSEvent.BaseEvent | ECSEvent.GameEvent)[];
}
/**
 * 配置中心
 * 基于配置表注册制的统一配置中心
 */
export class ConfigCenter implements ISerializable {
    /**重写配置数据管理 */
    private _overrideConfigMgr:OverrideConfigManager;
    /**配置表数据 */
    private _tableConfig: ConfigRegistry;
    /**动态重写的配置数据 */
    private _dynamicOverrideConfig:Record<string,any>;
    /**分隔符 */
    private SplitCode:string = '_$_';
    /**世界配置数据 */
    public levelConfig:IGameLevelConfig;
    constructor(overrideConfig:IOverrideLevelConfig,tableConfig:ConfigRegistry){
        this._overrideConfigMgr = new OverrideConfigManager(this);
        this._tableConfig = tableConfig;
        this._dynamicOverrideConfig = {};
        this.levelConfig = this._overrideConfigMgr.buildConfig(overrideConfig);
    }

    public serialize() {
        return {
            dynamicOverrideConfig:this._dynamicOverrideConfig,
        }
    }
    public deserialize(data: any) {
        this._dynamicOverrideConfig = data.dynamicOverrideConfig;
        
        // 将dynamicOverrideConfig中的值重写到tableConfig
        Object.keys(this._dynamicOverrideConfig).forEach(key => {
            const [tableKey, id, configKey] = key.split(this.SplitCode);
            const value = this._dynamicOverrideConfig[key];
            this._tableConfig[tableKey][id][configKey] = value;
        });
    }

    /**
     * 获取配置项
     * @param tableKey 配置表键
     * @param id 配置ID
     * @returns 配置项
     */
    public getConfig<T>(tableKey: ConfigTableConst, id: string): T | undefined {
        const table = this._tableConfig[tableKey];
        if (!table) {
            console.error(`配置表 ${tableKey} 不存在`);
            return undefined;
        }
        const config = table[id] as T;

        if (!config) {
            console.error(`配置项 ${tableKey}/${id} 不存在`);
            return undefined;
        }
        return config;
    }

    /**重写配置数据 */
    public overrideConfig(tableKey: ConfigTableConst, id: string, key: string, value: any) {
        this._tableConfig[tableKey][id][key] = value;
        this._dynamicOverrideConfig[`${tableKey}${this.SplitCode}${id}${this.SplitCode}${key}`] = value;
    }
    /**
     * 获取配置表
     * @param tableKey 配置表键
     * @param id 配置ID
     * @returns 配置表
     */
    public getConfigByTableKey<T extends keyof ConfigRegistry>(tableKey: T, id: string): ConfigRegistry[T][string]{
        return this._tableConfig[tableKey][id] as ConfigRegistry[T][string];
    }
    /**
     * 设置配置表
     * @param tableKey 配置表键
     * @param id 配置ID
     * @param config 配置
     */
    public setConfigByTableKey<T extends keyof ConfigRegistry>(tableKey: T, id: string, config: ConfigRegistry[T][string]) {
        this._tableConfig[tableKey][id] = config;
    }
    /**
     * 获取冷却配置
     */
    getCooldownConfig(cooldownId: string): ISkillCooldownConfig | undefined {
        return this.getConfig<ISkillCooldownConfig>(ConfigTableConst.CooldownConfig, cooldownId);
    }
    /**
     * 获取Buff行为配置
     * @param buffBehaviorId Buff行为ID
     * @returns Buff行为配置
     */
    getBuffBehaviorConfig(buffBehaviorId: string): IBuffBehaviorConfig | undefined {
        return this.getConfig<IBuffBehaviorConfig>(ConfigTableConst.BuffBehaviorConfig, buffBehaviorId);
    }
    /**
     * 获取技能配置
     * @param skillId 技能ID
     * @returns 技能配置
     */
    getSkillConfig(skillId: string): IBaseSkillConfig | undefined {
        return this.getConfig<IBaseSkillConfig>(ConfigTableConst.SkillConfig, skillId);
    }
    /**==================================类配置==================================== */
    /**
     * 获取ECS视图配置
     * @param ecsViewConfigId ECS视图ID
     * @returns ECS视图配置
     */
    getEcsViewConfig(ecsViewConfigId: string): IECSViewConfig | undefined {
        return this.getConfig<IECSViewConfig>(ConfigTableConst.EcsViewConfig, ecsViewConfigId);
    }

    /**
     * 获取系统配置
     * @param systemId 系统ID
     * @returns 系统配置
     */
    getSystemConfig(systemId: string): ISystemConfig | undefined {
        return this.getConfig<ISystemConfig>(ConfigTableConst.SystemConfig, systemId);
    }
    /**
     * 获取规则流配置
     * @param flowId 规则流ID
     * @returns 规则流配置
     */
    getRuleFlowConfig(flowId: string): IECSFlowConfig | undefined {
        return this.getConfig<IECSFlowConfig>(ConfigTableConst.RuleFlowConfig, flowId);
    }
    /**
     * 获取规则配置
     * @param ruleId 规则ID
     * @returns 规则配置
     */
    getRuleConfig(ruleId: string): IECSRuleConfig | undefined {
        return this.getConfig<IECSRuleConfig>(ConfigTableConst.RuleConfig, ruleId);
    }
    /**
     * 获取棋盘配置
     * @param boardId 棋盘ID
     * @returns 棋盘配置
     */
    getBoardConfig(boardId: string): IBoardConfig | undefined {
        return this.getConfig<IBoardConfig>(ConfigTableConst.BoardConfig, boardId);
    }
}
