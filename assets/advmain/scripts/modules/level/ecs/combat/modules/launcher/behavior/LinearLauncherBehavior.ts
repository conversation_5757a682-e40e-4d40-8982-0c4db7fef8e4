import { LauncherData } from "../../../component/Launcher";
import { LauncherBehaviorBase } from "../core/ILauncherBehavior";
import { IBehaviorData } from "../../../core/BehaviorBase";
import { IActionContext } from "../../../core/ActionContext";

/**
 * 投射物发射器配置
 */
export interface ILinearLauncherConfig extends IBaseLauncherConfig {
    launcherType: LauncherType.Projectile;
    projectileId: string;               // 投射物配置ID
    count: number;                      // 发射数量
    interval: number;                  // 发射间隔（秒）
}

/**
 * 投射物发射器行为数据
 */
interface LinearLauncherBehaviorData extends IBehaviorData {
    launchedCount: number;          // 已发射数量
    lastLaunchTime: number;         // 上次发射时间
    isLaunching: boolean;           // 是否正在发射中
    createdProjectiles: number[];   // 创建的投射物实体ID列表
    targetEntityId?: number;        // 目标实体ID（从上下文获取）
    targetPosition?: { x: number, y: number }; // 目标位置（从上下文获取）
}

/**
 * 投射物发射器行为
 * 发射投射物，投射物有自己的飞行轨迹和生命周期
 * 
 * 特点：
 * 1. 创建飞行中的投射物实体
 * 2. 支持多发投射物和连续发射
 * 3. 适用于火球术、箭矢、子弹等
 */
export class LinearLauncherBehavior extends LauncherBehaviorBase<ILinearLauncherConfig> {


    onLaunch(launcherData: LauncherData<LinearLauncherBehaviorData>, context?: IActionContext): boolean {
        // 初始化行为数据（原onCreate逻辑）
        const behaviorData: LinearLauncherBehaviorData = {
            launchedCount: 0,
            lastLaunchTime: 0,
            isLaunching: false,
            createdProjectiles: [],
            targetEntityId: context?.targetEntityId,
            targetPosition: context?.targetPosition
        };

        launcherData.behaviorData = behaviorData;

        // 执行发射逻辑（原onLaunch逻辑）
        behaviorData.isLaunching = true;
        behaviorData.lastLaunchTime = this.world.worldTime;

        return this.launchSingleProjectile(launcherData);
    }

    onDestroy(launcherData: LauncherData<LinearLauncherBehaviorData>): void {
        // 清理可能还存在的投射物
        const behaviorData = launcherData.behaviorData;
        for (const projectileId of behaviorData.createdProjectiles) {
            // TODO: 通过ProjectileHandler清理投射物
            console.log(`清理投射物: ${projectileId}`);
            // ProjectileHandler.getInstance().destroyProjectile(world, projectileId, launcherData.launcherId);
        }

        console.log(`投射物发射器已销毁: ${launcherData.launcherId}`);
    }

    onUpdate(launcherData: LauncherData<LinearLauncherBehaviorData>, deltaTime: number): boolean {
        if (!this.config) return true; // 没有配置就完成

        const behaviorData = launcherData.behaviorData;
        const currentTime = this.world.worldTime

        // 检查是否还在发射中
        if (!behaviorData.isLaunching) {
            return true; // 已完成发射
        }

        // 检查是否到了发射时间
        const timeSinceLastLaunch = currentTime - behaviorData.lastLaunchTime;
        if (this.config.interval && timeSinceLastLaunch >= this.config.interval) {
            this.launchSingleProjectile(launcherData);
            behaviorData.lastLaunchTime = currentTime;
            behaviorData.launchedCount++;

            // 检查是否发射完所有投射物
            if (behaviorData.launchedCount >= this.config.count) {
                behaviorData.isLaunching = false;
                return true; // 完成发射
            }
        }

        return false; // 仍在发射中
    }

    onComplete(launcherData: LauncherData<LinearLauncherBehaviorData>): void {
        console.log(`投射物发射器已完成: ${launcherData.launcherId}, 共发射 ${launcherData.behaviorData.launchedCount} 个投射物`);
    }

    /**
     * 发射单个投射物
     */
    private launchSingleProjectile(launcherData: LauncherData<LinearLauncherBehaviorData>): boolean {
        if (!this.config) return false;

        const behaviorData = launcherData.behaviorData;
        const projectileId = this.createProjectile(launcherData, behaviorData.launchedCount);

        if (projectileId) {
            behaviorData.createdProjectiles.push(projectileId);
            behaviorData.launchedCount++;
            behaviorData.lastLaunchTime = this.world.worldTime;
            return true;
        }

        return false;
    }

    /**
     * 创建投射物
     * 使用 behaviorData 中保存的目标信息创建投射物
     */
    private createProjectile(launcherData: LauncherData<LinearLauncherBehaviorData>, index: number): number | null {
        if (!this.config) return null;

        const behaviorData = launcherData.behaviorData;
        const projectileHandler = this.world.utilCenter.projectileHandler;

        // 创建投射物
        // 注意：这里需要考虑投射物实体的创建策略
        // 1. 可以在发射器实体上创建（简单但可能有问题）
        // 2. 可以创建独立的投射物实体（推荐）
        // 这里暂时使用发射器实体作为投射物载体

        const projectileInsId = projectileHandler.createProjectile(
            launcherData.casterEntityId,
            this.config.projectileId,
            launcherData.ownerEntityId, // 投射物组件放在发射器实体上
            {
                targetEntityId: behaviorData.targetEntityId,
                targetPosition: behaviorData.targetPosition
            }
        );

        return projectileInsId;
    }
} 