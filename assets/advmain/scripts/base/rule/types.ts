/**
 * 规则模块核心类型定义
 */

/**
 * 执行上下文接口
 */
export interface ExecutionContext {

}
export interface IConfig {

}
/**
 * 条件配置接口
 */
export interface IConditionConfig extends IConfig {

}
/**
 * 规则配置接口
 */
export interface IRuleConfig extends IConfig {
    /**规则ID */
    readonly ruleId: string;
    /**规则描述 */
    readonly ruleDesc: string;
    /**规则类型 */
    readonly ruleType: string;
    /**规则是否关闭 */
    readonly close?: boolean;
    /**替换规则ID */
    readonly replaceRuleId?: string;
}
/**
 * 执行流配置接口
 */
export interface IFlowConfig extends IConfig {
  /**执行流ID */
}

// 可执行单元接口 - 基础执行能力的抽象
export interface IExecutable<TConfig extends IConfig = IConfig,TContext extends ExecutionContext = ExecutionContext> {
  readonly execType: 'rule' | 'flow' ;
  /**规则配置 */
  config: TConfig;
  /**
   * 设置执行上下文
   */
  setContext(context: TContext): void;
  /**
   * 执行操作
   */
  run(): void;
  /**
   * 评估条件是否满足
   */
  canRun(): boolean;
  /**
   * 验证执行上下文是否有效
   */
  invalidateExecuteContext(): boolean;
}

// 规则接口
export interface IRule<TConfig extends IRuleConfig = IRuleConfig,TContext extends ExecutionContext = ExecutionContext> extends IExecutable<TConfig, TContext> {
  /**执行类型 */
  readonly execType: 'rule';
}

// 执行流接口
export interface IFlow<TConfig extends IFlowConfig = IFlowConfig,TContext extends ExecutionContext = ExecutionContext> extends IExecutable<TConfig, TContext> {
  readonly execType: 'flow'; // 强类型
  readonly children: IExecutable[]; // 子执行单元
  /**执行上下文 */
  context: ExecutionContext;
  /**
   * 添加子执行单元
   */
  addChild(child: IExecutable): void;
}
