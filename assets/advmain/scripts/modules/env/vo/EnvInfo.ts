
export type OS = `Android` | `iOS` | `WP` | `Others`;
export type EnvType = keyof (typeof envInfo.envs);

/**
 * 环境信息
 */
class EnvInfo {
    /** 是否为 Release 版 TODO 可能与主包逻辑不一致，需要细看*/
    get isProd() {
        return !(CC_DEBUG || CC_DEV) && falcon.NativeBridge.isNative();
    }

    /** 包名 */
    get bundleName() {
        return 'com.block.juggle';
    }

    /**
     * 环境信息
     */
    @falcon.memoize
    get envs() {
        return {
            Test: {
                // 测试服请求链接
                gameInitUrl: "http://gametester-test.afafb.com/game_init"
            },
            Prod: {
                // 正式服请求链接
                gameInitUrl: "https://gametester.afafb.com/game_init"
            }
        };
    };

    /**
     * 当前缓存信息
     */
    @falcon.memoize
    get curEnvInfo() {
        return this.envs[this.curEnv];
    }

    /**
     * 当前系统
     */
    @falcon.memoize
    get os() {
        let os: OS;
        if (CC_JSB) {
            switch (cc.sys.os) {
                case cc.sys.OS_ANDROID:
                    os = "Android";
                    break;

                case cc.sys.OS_IOS:
                    os = "iOS";
                    break;

                case cc.sys.OS_WP8:
                    os = "WP";
                    break;

                default:
                    os = "Others";
                    break;
            }
        } else {
            if (
                navigator.userAgent.indexOf("Android") > -1 ||
                navigator.userAgent.indexOf("Linux") > -1
            ) {
                os = "Android";
            } else if (navigator.userAgent.indexOf("iPhone") > -1) {
                os = "iOS";
            } else if (navigator.userAgent.indexOf("Windows Phone") > -1) {
                os = "WP";
            } else {
                os = "Others";
            }
        }
        return os;
    }

    /**
     * 系统版本
     */
    @falcon.memoize
    get osVersion() {
        let version = '1.0.0';
        if (CC_JSB) {
            version = cc.sys.osVersion;
        } else {
            const u = navigator.userAgent;
            const isAndroid = u.indexOf('Android') > -1 || u.indexOf('Linux') > -1; //Android
            const isIOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); //ios终端
            if (isAndroid) {
                version = navigator.userAgent.split(';')?.[1]?.match(/\d+(\.*\d+)*/g)?.[0] ?? `1.0.0`;
            }
            if (isIOS) {
                version = (navigator.userAgent.split(';')?.[1]?.match(/(\d+)_(\d+)_?(\d+)?/)?.[0])?.replace('_', '.') ?? `1.0.0`;
            }
        }

        return version;
    }

    /**
     * 当前环境
     */
    get curEnv(): EnvType {
        if (this.isProd) {
            return 'Prod';
        } else {
            return 'Test';
        }
    }

}

export const envInfo = new EnvInfo();