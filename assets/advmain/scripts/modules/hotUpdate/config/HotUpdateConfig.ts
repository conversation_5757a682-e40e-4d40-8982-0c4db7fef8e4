import { HotUpdateStateType } from '../type/HotUpdateType';

/** HotUpdateStateType 映射 */
export const hotUpdateStateMap = {
  ...falcon.EventCodeMap,
  [HotUpdateStateType.REQUEST_REMOTE_CONFIG_TIMEOUT]: 'REQUEST_REMOTE_CONFIG_TIMEOUT',
  [HotUpdateStateType.REMOTE_CONFIG_DATA_ERROR]: 'REMOTE_CONFIG_DATA_ERROR',
};

/** 服务器root 地址 */
export const RemoteRootUrl = MACRO_HOTUPDATE_SERVER_URL;

export function getVersionManifestUrl(version: string): string {
  return `${RemoteRootUrl}${version}/version.manifest?version=${Date.now()}`
}

export function getVersionConfigUrl(): string {
  return `${RemoteRootUrl}version.config?version=${Date.now()}`
}