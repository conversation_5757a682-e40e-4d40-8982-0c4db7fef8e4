import { Component } from '../cores/Component';

export interface IRect {
    x: number;
    y: number;
    width: number;
    height: number;
}

/**拖拽组件 */
export default class DragComponent extends Component {
    /** 热区数据 */
    hotArea: IRect;
    /** 拖动倍率 */
    dragMultiplier: number;

    constructor(hotArea: IRect) {
        super();
        this.hotArea = hotArea;
        this.dragMultiplier = 1;
    }
}
