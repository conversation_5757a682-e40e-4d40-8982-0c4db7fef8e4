import { AIBehaviorBase } from '../../behavior/ai/AIBehaviorBase';
import { CellAIBehavior } from '../../behavior/ai/CellAIBehavior';
import { DiamondAIBehavior } from '../../behavior/ai/DiamondAIBehavior';
import { GlassAIBehavior } from '../../behavior/ai/GlassAIBehavior';
import { PlaneAIBehavior } from '../../behavior/ai/PlaneAIBehavior';

/**AI行为配置表名 */
export const enum AIBehaviorConst {
    /**块AI */
    CellAIBehavior = 'CellAIBehavior',
    /**飞机块AI */
    PlaneAIBehavior = 'PlaneAIBehavior',
    /**玻璃块AI */
    GlassAIBehavior = 'GlassAIBehavior',
    /**钻石块AI */
    DiamondAIBehavior = 'DiamondAIBehavior',
}

/**AI行为配置注册表 */
export const AIBehaviorRegistry: Record<string, clzz<AIBehaviorBase>> = {
    [AIBehaviorConst.CellAIBehavior]: CellAIBehavior,
    [AIBehaviorConst.PlaneAIBehavior]: PlaneAIBehavior,
    [AIBehaviorConst.GlassAIBehavior]: GlassAIBehavior,
    [AIBehaviorConst.DiamondAIBehavior]: DiamondAIBehavior,
};
