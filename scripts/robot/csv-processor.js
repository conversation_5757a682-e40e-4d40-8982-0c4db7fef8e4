const fs = require('fs');
const path = require('path');
const csv = require('csv-parser');
const { createObjectCsvWriter } = require('csv-writer');

// CSV文件路径
const inputFilePath = '/Users/<USER>/Desktop/block_robot_data_game_end_v3_202506281452.csv';
const outputFilePath = '/Users/<USER>/Desktop/processed_block_data.csv';

// 存储数据的Map，key为round_id，value为block_state_id数组
const roundDataMap = new Map();

console.log('开始读取CSV文件...');

let totalRows = 0;
let validRows = 0;
let firstRow = true;

// 读取CSV文件
fs.createReadStream(inputFilePath)
  .pipe(csv())
  .on('data', (row) => {
    totalRows++;
    
    // 打印第一行数据来调试
    if (firstRow) {
      console.log('第一行数据：', row);
      console.log('可用的列名：', Object.keys(row));
      firstRow = false;
    }
    
    const roundId = row.round_id;
    const blockStateIdStr = row.block_state_id;
    
    // 调试信息
    if (totalRows <= 5) {
      console.log(`第${totalRows}行 - round_id: ${roundId}, block_state_id: ${blockStateIdStr}`);
    }
    
    // 跳过无效的round_id
    if (!roundId) {
      if (totalRows <= 10) {
        console.log(`跳过无效行 ${totalRows}: round_id为空`);
      }
      return;
    }
    
    // 解析block_state_id JSON数组
    let blockStateIds = [];
    try {
      if (blockStateIdStr && blockStateIdStr.trim().startsWith('[') && blockStateIdStr.trim().endsWith(']')) {
        blockStateIds = JSON.parse(blockStateIdStr);
        if (!Array.isArray(blockStateIds)) {
          throw new Error('不是数组');
        }
      } else {
        // 如果不是JSON数组格式，尝试作为单个数字处理
        const singleId = parseInt(blockStateIdStr);
        if (!isNaN(singleId)) {
          blockStateIds = [singleId];
        }
      }
    } catch (e) {
      if (totalRows <= 10) {
        console.log(`跳过无效行 ${totalRows}: 无法解析block_state_id: ${blockStateIdStr}`);
      }
      return;
    }
    
    // 跳过空数组
    if (blockStateIds.length === 0) {
      if (totalRows <= 10) {
        console.log(`跳过无效行 ${totalRows}: block_state_id数组为空`);
      }
      return;
    }
    
    validRows++;
    
    // 如果round_id不存在，创建新数组
    if (!roundDataMap.has(roundId)) {
      roundDataMap.set(roundId, []);
    }
    
    // 添加所有block_state_id到对应的round_id数组中
    blockStateIds.forEach(id => {
      const numId = parseInt(id);
      if (!isNaN(numId)) {
        roundDataMap.get(roundId).push(numId);
      }
    });
  })
  .on('end', () => {
    console.log('CSV文件读取完成，开始处理数据...');
    console.log(`总行数: ${totalRows}, 有效行数: ${validRows}`);
    
    // 处理数据：对每个round_id的block_state_id数组进行从高到低排序
    const processedData = [];
    
    roundDataMap.forEach((blockStateIds, roundId) => {
      // 统计每个数字的出现次数
      const countMap = new Map();
      blockStateIds.forEach(id => {
        countMap.set(id, (countMap.get(id) || 0) + 1);
      });
      
      // 按出现次数从高到低排序，取前5个
      const sortedByCount = Array.from(countMap.entries())
        .sort((a, b) => {
          // 先按出现次数降序，次数相同则按数字降序
          if (b[1] !== a[1]) {
            return b[1] - a[1]; // 按出现次数降序
          }
          return b[0] - a[0]; // 按数字降序
        })
        .slice(0, 5); // 只取前5个
      
      // 生成压缩格式 - 只显示数字ID
      const compressedFormat = sortedByCount.map(([value, count]) => value);
      
      processedData.push({
        round_id: roundId,
        block_state_ids: compressedFormat.join(','),
        count: blockStateIds.length,
        top5_count: sortedByCount.length
      });
    });
    
    // 按round_id排序
    processedData.sort((a, b) => a.round_id.localeCompare(b.round_id));
    
    console.log(`共处理了 ${processedData.length} 个不同的round_id`);
    
    // 创建CSV写入器
    const csvWriter = createObjectCsvWriter({
      path: outputFilePath,
      header: [
        { id: 'round_id', title: 'round_id' },
        { id: 'block_state_ids', title: 'top5_frequent_ids' },
        { id: 'count', title: 'total_count' },
        { id: 'top5_count', title: 'top5_types_count' }
      ]
    });
    
    // 写入处理后的数据到新CSV文件
    csvWriter.writeRecords(processedData)
      .then(() => {
        console.log('处理完成！新的CSV文件已生成：', outputFilePath);
        console.log('文件包含以下列：');
        console.log('- round_id: 轮次ID');
        console.log('- top5_frequent_ids: 出现次数最多的前5个数字ID(逗号分隔)');
        console.log('- total_count: 该round_id的总数据量');
        console.log('- top5_types_count: 前5个数字的种类数量(最多5种)');
        
        // 显示前几行示例数据
        console.log('\n前5行示例数据：');
        processedData.slice(0, 5).forEach((row, index) => {
          console.log(`${index + 1}. round_id: ${row.round_id}, total: ${row.count}, top5: ${row.block_state_ids}, types: ${row.top5_count}`);
        });
      })
      .catch((error) => {
        console.error('写入CSV文件时出错：', error);
      });
  })
  .on('error', (error) => {
    console.error('读取CSV文件时出错：', error);
  }); 