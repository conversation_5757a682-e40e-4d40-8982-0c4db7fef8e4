import { NativeDeviceInfo } from "../../../native/NativeDeviceInfo";
import { ConfigTableConst } from "../registry/ConfigRegistry";
import { ECSRuleBase, IECSRuleConfig } from "./core/ECSRuleBase";
export interface ILowPerformanceDeviceResAdaptRuleConfig extends IECSRuleConfig {
    /** bundle,path 逗号隔开 */
    lowPerformanceResourceConfig: {
        [key: string]: string;
    };
}
// 低性能设备资源适配规则
export class LowPerformanceDeviceResAdaptRule extends ECSRuleBase<ILowPerformanceDeviceResAdaptRuleConfig> {
    canExecute(): boolean {
        console.log(`[LowPerformanceDeviceResAdaptRule] 开始执行低性能设备资源适配`);
        const nativeInfo = NativeDeviceInfo.callNativeInfo();
        if (nativeInfo && nativeInfo.opt !== `normal`) {
            return true;
        }
        return false;
    }
    execute(): void {
        console.log(`[LowPerformanceDeviceResAdaptRule] 执行低性能设备资源适配`);
        const cfg = this.world.configCenter.getLowPerformanceResourceConfig('low_performance_resource_config');
        if (cfg) {
            Object.keys(this.config.lowPerformanceResourceConfig).forEach((key) => {
                console.log(`[LowPerformanceDeviceResAdaptRule] 替换资源: ${key}`);
                const value = this.config.lowPerformanceResourceConfig[key];
                this.world.configCenter.overrideConfig(ConfigTableConst.LowPerformanceResourceConfig
                    , 'low_performance_resource_config', key, { bundle: value.split(',')[0] || ''
                        , path: value.split(',')[1] || '' });
                console.log(this.world.configCenter.getLowPerformanceResourceConfig('low_performance_resource_config'));
            });
        }
    }
}