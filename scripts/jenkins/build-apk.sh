#!/bin/bash
# 构建新架构包APK，此脚本不能单独使用，需要配合 build-test.sh 使用

source ./scripts/jenkins/build-test.sh

# APK_FULL_NAME=""
# APK_SIZE=0
# 反编译APK
if [ "$Apk" == true ]; then
  log "反编译APK..."
  /opt/homebrew/bin/apktool d -f ${APK_BASE_URL} -o ${TEMP_DIR}
fi

# 清理并准备APK资源
log "清理并准备APK资源..."
rm -rf ${TEMP_DIR}/assets/assets
rm -rf ${TEMP_DIR}/assets/src
rm -rf ${TEMP_DIR}/assets/jsb-adapter
rm -f ${TEMP_DIR}/assets/main.js

# 删除文件前检查文件是否存在
[ -f ${TEMP_DIR}/assets/main.refactored.js ] && rm -f ${TEMP_DIR}/assets/main.refactored.js
[ -f ${TEMP_DIR}/assets/distribution.js ] && rm -f ${TEMP_DIR}/assets/distribution.js

# 复制新构建资源到APK
log "复制新构建资源到APK..."
cp -r ${BUILD_OUTPUT_DIR}/jsb-link/assets ${TEMP_DIR}/assets
cp -r ${BUILD_OUTPUT_DIR}/jsb-link/src ${TEMP_DIR}/assets
cp -r ${BUILD_OUTPUT_DIR}/jsb-link/jsb-adapter ${TEMP_DIR}/assets
cp -r ${BUILD_OUTPUT_DIR}/jsb-link/main.js ${TEMP_DIR}/assets

if [[ ${MACRO_HOTUPDATE_OPEN} == true ]]; then
  # 处理热更manifest文件
  log "复制热更manifest文件..."
  mkdir -p ${TEMP_DIR}/assets/src
  cp ${BUILD_OUTPUT_DIR}/jsb-link/src/project.manifest ${TEMP_DIR}/assets/src
  cp ${BUILD_OUTPUT_DIR}/jsb-link/src/version.manifest ${TEMP_DIR}/assets/src
fi

if [[ "$Apk" == true ]]; then
  log "复制开发工具库文件..."
  for arch in "arm64-v8a" "armeabi-v7a" "x86" "x86_64"; do
    cp ${LIB_BASE_URL}/${arch}/libcocos2djs.so ${TEMP_DIR}/lib/${arch}/libcocos2djs.so
  done

  APK_FULL_NAME="${ApkName}-${version}-$(date '+%Y%m%d%H%M%S').apk"

  # 构建APK
  log "构建未签名APK..."
  /opt/homebrew/bin/apktool b ${TEMP_DIR} -o ${UNSIGNED_APK}

  # 对齐，如果不使用这个，会报：Failure [-124: Failed parse during installPackageLI: Targeting R+ (version 30 and above) requires the resources.arsc of installed APKs to be stored uncompressed and aligned on a 4-byte boundary]
  # 从 Android 11 开始，Google 对 APK 的打包格式有了更严格的要求，尤其是 resources.arsc 文件（资源索引文件）：
  # 必须 uncompressed（未压缩）
  # 必须 4 字节对齐
  ~/Library/Android/sdk/build-tools/30.0.3/zipalign -v 4 ${UNSIGNED_APK} ${UNSIGNED_ALIGNED_APK}

  # 签名APK
  log "签名APK..."
  # /opt/homebrew/opt/openjdk/bin/jarsigner -verbose \
  #   -keystore ${KEYSTORE_FILE} \
  #   -storepass "${KEYSTORE_PASS}" \
  #   -signedjar ${APK_FULL_NAME} \
  #   ${UNSIGNED_ALIGNED_APK} \
  #   ${KEYSTORE_ALIAS}
  # 必须用 apksigner 签名，jarsigner 签名会报错：Failure [-124: Failed parse during installPackageLI: Targeting R+ (version 30 and above) requires the resources.arsc of installed APKs to be stored uncompressed and aligned on a 4-byte boundary]
  ~/Library/Android/sdk/build-tools/30.0.3/apksigner sign \
    --ks ${KEYSTORE_FILE} \
    --ks-key-alias ${KEYSTORE_ALIAS} \
    --ks-pass pass:${KEYSTORE_PASS} \
    --out ${APK_FULL_NAME} \
    ${UNSIGNED_ALIGNED_APK}

  # 移动最终APK到构建目录
  log "移动最终APK到构建目录..."
  mv ${APK_FULL_NAME} ${BUILD_OUTPUT_DIR}

  # 清理临时文件
  log "清理临时文件..."
  rm -f ${UNSIGNED_APK}
  rm -f ${UNSIGNED_ALIGNED_APK}
  rm -rf ${TEMP_DIR}

  log "构建完成！最终APK: ${BUILD_OUTPUT_DIR}/${APK_FULL_NAME}"

  # 如果是在Jenkins环境中，将构建的 apk拷贝到 ${BLOCK_BLAST_BASE_URL}/${PLATFORM}/${ENV}/${IP_ADDRESS}
  if [[ "$IS_JENKINS_ENV" = true ]]; then
    # 获取本机IP地址
    IP_ADDRESS=$(ifconfig | grep "inet " | grep -v 127.0.0.1 | awk '{print $2}' | head -n 1)
    TARGET_DIR="${BLOCK_BLAST_BASE_URL}/${PLATFORM}/${ENV}/${IP_ADDRESS}"
    
    # 创建目标目录（如果不存在）
    log "将APK拷贝到 ${TARGET_DIR}..."
    mkdir -p "${TARGET_DIR}"
    
    # 拷贝APK文件到目标目录
    cp ${BUILD_OUTPUT_DIR}/${APK_FULL_NAME} ${TARGET_DIR}/${APK_FULL_NAME}
    # 移除目标文件
    rm -f ${BUILD_OUTPUT_DIR}/${APK_FULL_NAME}
    
    # 验证拷贝成功
    if [ $? -eq 0 ]; then
      log "APK已成功拷贝到 ${TARGET_DIR}/${APK_FULL_NAME}"
    else
      log "错误: APK拷贝失败" >&2
    fi
  fi

  # 获取APK大小
  APK_SIZE=$(du -h "${TARGET_DIR}/${APK_FULL_NAME}" | cut -f1)
fi