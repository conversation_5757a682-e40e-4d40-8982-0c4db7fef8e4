import { Component } from '../../cores/Component';
import { CellColor, CellType, BoardLayer } from '../../define/BoardDefine';
import { RowCol } from '../../define/EcsDefine';

/**块组件 */
export class CellComponent extends Component {
    /**行 */
    public r: number;
    /**列 */
    public c: number;

    temp: {
        /**显示颜色 */
        color: CellColor;
    };

    constructor(
        /**类型 */
        public type: CellType,
        /**层级 */
        public layer: BoardLayer,
        /**原始颜色 */
        public oriColor: CellColor,
        /**行或行列 */
        r: number | RowCol,
        /**列 */
        c?: number,
        /**占位 */
        public occupy = true,
        /**连通 */
        public through = true,
    ) {
        super();
        this.temp = {
            color: this.oriColor,
        };
        this.type = type;
        if (typeof r === 'number') {
            this.r = r;
            this.c = c;
        } else {
            this.r = r && r.r;
            this.c = r && r.c;
        }
    }
}
