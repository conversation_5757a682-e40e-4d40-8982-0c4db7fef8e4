import { Loader } from '../../../base/Loader';
import { SubGameBridge } from '../../../base/SubGameBridge';
import { PrefabConfig } from '../../prefab/PrefabConfig';
import { enSetupKeys } from '../config/LevelSetupConfig';
import { E_Levels_SetupShow } from '../events/E_Levels_SetupShow';
import { game } from '../Game';

const { ccclass } = cc._decorator;
@ccclass
export class LevelRoot extends falcon.Component<Partial<{ levelId: number }>> {
    protected start(): void {
        game.curLevelId = this.state.levelId;
        game.init(this.node.getChildByName('root'));
        //临时写法，直接操作游戏
        window['gameIns'] = game;
    }
    protected update(dt: number): void {
        game.update(dt);
    }
    onDestroy(): void {
        // game.dispose();
    }

    clickBack() {
        Loader.showUI(PrefabConfig.ChapterMain);
    }

    clickSet() {
        falcon.EventManager.dispatchModuleEvent(
            new E_Levels_SetupShow({ keys: [enSetupKeys.sound, enSetupKeys.BGM, enSetupKeys.moreSettings, enSetupKeys.home, enSetupKeys.replay] }),
        );
    }
}
