import { CmdBase } from '../CmdBase';
import { EventCmd, IEventCmdData } from '../../cmd/EventCmd';
import { LogicWorld } from '../World';

/**
 * 输入命令中心
 * 负责管理输入命令的添加、执行等逻辑
 */
export class InputCmdCenter {
    private world: LogicWorld;
    
    /** 输入命令列表 */
    private inputCmdList: CmdBase[] = [];

    constructor(world: LogicWorld) {
        this.world = world;
    }

    /**
     * 添加输入命令
     * @param data 事件命令数据
     */
    pushInputCmd(data: IEventCmdData): void {
        const cmd = new EventCmd();
        cmd.world = this.world;
        cmd.data = data;
        this.inputCmdList.push(cmd);
    }

    /**
     * 执行输入命令
     */
    executeInputCmd(): void {
        while (this.inputCmdList.length > 0) {
            const cmd = this.inputCmdList.shift();
            this.executeCmd(cmd);
        }
    }

    /**
     * 执行命令实例
     * @param cmd 命令实例
     */
    executeCmd(cmd: CmdBase): void {
        if (cmd) {
            cmd.world = this.world;
            cmd.execute();
        }
    }

    /**
     * 获取待执行的命令数量
     */
    getPendingCmdCount(): number {
        return this.inputCmdList.length;
    }

    /**
     * 清空所有待执行的命令
     */
    clearAllCommands(): void {
        this.inputCmdList.length = 0;
    }
}

