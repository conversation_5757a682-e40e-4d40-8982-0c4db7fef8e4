import BoardComponent from '../../../components/board/BoardComponent';
import ShapeComponent from '../../../components/board/ShapeComponent';
import { Component } from '../../../cores/Component';
import { RelationName } from '../../../cores/center/EntityCenter/WorldRelation';
import { ECSViewBase, IECSViewConfig } from '../../ECSViewBase';

export default class SlotECSView extends ECSViewBase<IECSViewConfig> {
    public isIndependent(): boolean {
        return true;
    }
    protected initView() {
        const parentEntity = this.logicWorld.getSources(this.logicEntityId, RelationName.PARENT_CHILD)[0];
        let parentComponent: Component = this.logicWorld.getComponent(parentEntity, ShapeComponent);
        this.node.children[0].active = !parentComponent;
        let loadSrc: string;
        if (parentComponent) {
            loadSrc = 'textures/level/boardBase/cell_shadow';
            this.node.children[1].setPosition(18, -18);
        } else if ((parentComponent = this.logicWorld.getComponent(parentEntity, BoardComponent))) {
            loadSrc = 'textures/level/boardBase/blockBg';
            this.node.children[1].setPosition(0, 0);
        }
        this.renderWorld
            .loadRes(this.config.prefabBundleName, loadSrc, cc.SpriteFrame)
            .then((spf: cc.SpriteFrame) => (this.node.children[1].getComponent(cc.Sprite).spriteFrame = spf));
    }
    protected addEvents() {}
    protected removeEvents() {}
    protected disposeView() {}
}
