import { Component } from '../../Component';
import { DestroyTag } from '../../../components/tag/DestroyTag';
import { StyleOrange } from '../../ConsoleStyle';
import { WorldQuery } from './WorldQuery';
import { ComponentProxy, ComponentChangeListener } from './ComponentProxy';
import { WorldRelation, RelationName } from './WorldRelation';

// 组件监听器类型 - 泛型版本
export type ComponentListener<T extends Component = Component> = (entityId: number, component: T) => void;
export type ReadonlyComponentChangeListener<T extends Component = Component> = (entityId: number, component: DeepReadonly<T>, propertyName?: string, oldValue?: DeepReadonly<any>, newValue?: DeepReadonly<any>) => void;

/**
 * 实体中心
 * 负责管理实体的完整生命周期，包括实体创建、组件管理、关系管理、查询等
 */
export class EntityCenter {
    /** 下一个实体ID */
    private nextEntityId = 1;
    /** 实体存储 */
    public entities: Set<number>;
    /** 组件管理 */
    public components: Record<string, Record<number, Component>>;
    /** 实体组件缓存 */
    private entityComponentsCache: Record<number, Record<string, Component>>;
    /** 世界查询 */
    public worldQuery: WorldQuery;
    /** 关系管理器 */
    public worldRelation: WorldRelation;
    // 组件代理管理器
    public componentProxy: ComponentProxy;
    // 组件监听器
    private componentAddListeners: Map<string, { listener: ComponentListener<Component>; thisObj: any }>;
    private componentRemoveListeners: Map<string, { listener: ComponentListener<Component>; thisObj: any }>;
    
    

    constructor() {
        this.entities = new Set();
        this.components = {};
        this.entityComponentsCache = {};
        this.componentAddListeners = new Map();
        this.componentRemoveListeners = new Map();
        this.componentProxy = new ComponentProxy(this);
        this.worldQuery = new WorldQuery(this);
        this.worldRelation = new WorldRelation(this);
    }

    // 实体生命周期管理
    createEntity(): number {
        const id = this.nextEntityId++;
        this.entities.add(id);
        this.worldQuery.initEntityMask(id);
        this.entityComponentsCache[id] = {};
        return id;
    }

    removeEntity(entityId: number): void {
        this.triggerEntityDestroyComponentsEvent(entityId);
        this.removeAllChildren(entityId);
        this.removeAllComponent(entityId);
        this.entities.delete(entityId);
        this.componentProxy.clearEntity(entityId);
        this.worldQuery.removeEntityMask(entityId);
        this.worldRelation.removeEntityRelations(entityId);
        delete this.entityComponentsCache[entityId];
    }

    destroyEntity(entityId: number, delayTime: number = 0.1): void {
        this.addComponent(entityId, DestroyTag, delayTime);
    }

    willDestroyEntity(entityId: number): boolean {
        return this.hasComponent(entityId, DestroyTag);
    }

    // 组件管理
    addComponent<T extends Component, A extends any[]>(entity: number, componentClass: new (...args: A) => T, ...options: A): T {
        const component = new componentClass(...options);
        component.entity = entity;
        const type = component.constructor.name;
        if (!this.components[type]) {
            this.components[type] = {};
        }
        this.components[type][entity] = component;

        const entityComponents = this.entityComponentsCache[entity];
        if (entityComponents) {
            entityComponents[type] = component;
        }

        this._freshComponent(type, entity, true, component, this.componentAddListeners);
        return component;
    }

    removeComponent<T extends Component>(entity: number, component: T): void {
        component.entity = entity;
        const type = component.constructor.name;
        this._freshComponent(type, entity, false, component, this.componentRemoveListeners);
        delete this.components[type][entity];
        this.componentProxy.destroy(entity, type);

        const entityComponents = this.entityComponentsCache[entity];
        if (entityComponents) {
            delete entityComponents[type];
        }
    }

    removeAllComponent(entity: number): void {
        const entityComponents = this.entityComponentsCache[entity];
        if (!entityComponents) {
            return;
        }

        Object.keys(entityComponents).forEach((type) => {
            this.worldQuery.updateEntityMask(entity, type, false);
            const componentStore = this.components[type];
            if (componentStore) {
                delete componentStore[entity];
            }
        });

        delete this.entityComponentsCache[entity];
    }

    getComponent<T extends Component>(entity: number, type: clzz<T>): T {
        const component = this.components[type.name]?.[entity] as T;
        if (component && this.componentProxy.has(entity, type)) {
            return this.componentProxy.getProxy(entity, type);
        }
        return component;
    }

    hasComponent<T extends Component>(entity: number, type: clzz<T>): boolean {
        return this.components[type.name]?.[entity] !== undefined;
    }

    // 组件监听器管理
    onComponentAdd<T extends Component>(componentType: clzz<T>, listener: ComponentListener<T>, thisObj: any): void {
        if (this.componentAddListeners.has(componentType.name)) {
            console.error(`%c组件添加监听只能在唯一一处地方使用,禁止在多个地方使用%c:${componentType.name}`, StyleOrange, '');
            return;
        }
        this.componentAddListeners.set(componentType.name, { listener, thisObj });
    }

    onComponentRemove<T extends Component>(componentType: clzz<T>, listener: ComponentListener<T>, thisObj: any): void {
        if (this.componentRemoveListeners.has(componentType.name)) {
            console.error(`%c组件移除监听只能在唯一一处地方使用,禁止在多个地方使用%c:${componentType.name}`, StyleOrange, '');
            return;
        }
        this.componentRemoveListeners.set(componentType.name, { listener, thisObj });
    }

    offComponentAdd<T extends Component>(componentType: clzz<T>, listener: ComponentListener<T>, thisObj: any): void {
        this.componentAddListeners.delete(componentType.name);
    }

    offComponentRemove<T extends Component>(componentType: clzz<T>, listener: ComponentListener<T>, thisObj: any): void {
        this.componentRemoveListeners.delete(componentType.name);
    }

    onComponentChange<T extends Component>(entityId: number, componentType: clzz<T>, listener: ComponentChangeListener<T>, thisObj: any): void {
        this.componentProxy.onChange(entityId, componentType, listener, thisObj);
    }

    offComponentChange<T extends Component>(entityId: number, componentType: clzz<T>, listener: ComponentChangeListener<T>, thisObj: any): void {
        this.componentProxy.offChange(entityId, componentType, listener, thisObj);
    }

    removeAllChildren(entityId: number): void {
        const children = this.worldRelation.getTargets(entityId, RelationName.PARENT_CHILD);
        for (let i = 0; i < children.length; i++) {
            const willDestroy = this.willDestroyEntity(children[i]);
            if (!willDestroy) {
                this.removeEntity(children[i]);
            }
        }
    }

    // 私有方法
    private triggerEntityDestroyComponentsEvent(entityId: number): void {
        const entityComponents = this.entityComponentsCache[entityId];
        if (!entityComponents || Object.keys(entityComponents).length === 0) {
            return;
        }

        Object.entries(entityComponents).forEach(([type, component]) => {
            const listeners = this.componentRemoveListeners.get(type);
            if (listeners) {
                const { listener, thisObj } = listeners;
                listener.call(thisObj, entityId, component);
            }
        });
    }

    private _freshComponent(
        type: string,
        entity: number,
        bitValue: boolean,
        component: Component,
        listenerMap: Map<string, { listener: ComponentListener<any>; thisObj: any }>,
    ): void {
        this.worldQuery.updateEntityMask(entity, type, bitValue);
        const listeners = listenerMap.get(type);
        if (listeners) {
            const { listener, thisObj } = listeners;
            listener.call(thisObj, entity, component);
        }
    }

    // 序列化
    serialize(): any {
        return {
            nextEntityId: this.nextEntityId,
            entities: Array.from(this.entities),
            components: this.components,
            entityComponentsCache: this.entityComponentsCache,
            worldQuery: this.worldQuery.serialize(),
            worldRelation: this.worldRelation.serialize(),
        };
    }

    // 反序列化
    deserialize(data: any): void {
        this.nextEntityId = data.nextEntityId;
        this.entityComponentsCache = data.entityComponentsCache;
        this.components = data.components;
        this.entities = new Set(data.entities);
        this.worldQuery.deserialize(data.worldQuery);
        this.worldRelation.deserialize(data.worldRelation);
    }
}