type clzz<T> = {
    new (...p: any): T;
};
type dict<T> = {
    [id: string | number]: T;
};
/**递归只读泛型 */
type DeepReadonly<T> = {
    readonly [K in keyof T]: T[K] extends object ? DeepReadonly<T[K]> : T[K];
};
/**移除泛型 */
type Mutable<T> = {
    -readonly [K in keyof T]: T[K];
};
/**无状态函数接口 */
type StateLessFunction = (inputArgs: DeepReadonly<any>) => any;
type CompId = number;
type FeatureCompId = number;
type LogicCompId = number;
type ConditionCompId = number;
type FormulaCompId = number;
type FeatureId = number;
type FeatureIdList = FeatureId[];

type EventType = string | symbol;
type Handler<T = unknown> = (event: T) => void;
type WildcardHandler<T = Record<string, unknown>> = (type: keyof T, event: T[keyof T]) => void;
type EventHandlerList<T = unknown> = Array<Handler<T>>;
type WildCardEventHandlerList<T = Record<string, unknown>> = Array<WildcardHandler<T>>;
type EventHandlerMap<Events extends Record<EventType, unknown>> = Map<keyof Events | '*', EventHandlerList<Events[keyof Events]> | WildCardEventHandlerList<Events>>;
interface Emitter<Events extends Record<EventType, unknown>> {
    all: EventHandlerMap<Events>;
    on<Key extends keyof Events>(type: Key, handler: Handler<Events[Key]>): void;
    on(type: '*', handler: WildcardHandler<Events>): void;
    off<Key extends keyof Events>(type: Key, handler?: Handler<Events[Key]>): void;
    off(type: '*', handler: WildcardHandler<Events>): void;
    emit<Key extends keyof Events>(type: Key, event: Events[Key]): void;
    emit<Key extends keyof Events>(type: undefined extends Events[Key] ? Key : never): void;
}

/**固定随机生成器 */
declare class FixedRandomUtil {
    private seed;
    /**初始化种子 */
    initSeed(seed: number): void;
    /**获取当前种子 */
    getSeed(): number;
    originRandom(): number;
    /**
     * 随机数 带小数点
     * @param min 最小随机数
     * @param max 最大随机数
     */
    random(min: number, max: number): number;
    /**
    * 随机数 整数
    * @param min 最小随机数
    * @param max 最大随机数
    */
    randomInt(min: number, max: number): number;
    /**
    * 从列表随机出一个子列表
    * @param arr 列表
    * @param count 随机出的子列表数量
    */
    randomArr<T>(arr: Array<T>, count: number): Array<T>;
    /**随机出列表中某个子项
     * @param arr 列表
     */
    randomItem<T>(arr: Array<T>): T;
    shuffle<T>(array: Array<T>, returnNew?: boolean): Array<T>;
    /**
     * 概率 0- 1 看是否命中
     * @param val 命中率
     */
    hitRate(val: number): boolean;
}

/**原始配置结构 */
interface DataConfig {
    /**入口 */
    main: CompId;
    /**组件池 */
    compTvoMap: CompTvoMap;
}
/**组件池类 */
type CompTvoMap = Record<number, any>;
/**组件池实例 */
type CompInsMap = Record<number, any>;
/**拼图配置结构 */
interface PuzzleDataConfig {
    config: Record<string, any>;
    data: Record<number, PrefabConfigData>;
}
type PrefabConfigData = any[] | Record<string, any>;

/**获取组件Id
 * @param compType 组件类型
 * @param idAdd 自增闭包函数
 */
declare function getCompId(compType: number, idAdd: IDADD): number;
/**获取组件类型
 * @param compId 组件Id
 */
declare function getCompType(compId: number): number;
/**id自增 */
declare function IdAdd(): IDADD;
type IDADD = () => number;
/**条件组件一定生效 */
declare const AllwaysTrue = 1;

/**配置预制体基类 */
declare abstract class BaseConfigPrefab<T> {
    config: DataConfig;
    prefabConfigData: T;
    /**初始化配置预制体 */
    initConfigPrefab(idAdd: IDADD, prefabConfigData: T): void;
    private $initCompId;
    /**注册组件Id列表
     * @returns 返回组件Id对应的组件类型列表
    */
    protected abstract registeCompIdList(): number[];
    /**创建配置预制体
     * @param listCompId 组件Id列表
     */
    protected abstract createConfigPrefab(listCompId: number[], compTvoMap: CompTvoMap): any;
    /**重写配置 */
    protected abstract rewriteConfig(config: T): any;
    /**重写配置预制体数据 */
    protected abstract rewritePrefabData(listCompId: number[], compTvoMap: CompTvoMap, prefabConfigData: T): any;
}

/**配置 */
interface IGlobalFeature {
    /**改变配置预制体 */
    rewriteConfig(puzzleDataConfig: PuzzleDataConfig, globalFeatureDataArr: DeepReadonly<GlobalFeatureData[]>): any;
}
interface GlobalFeatureData {
    /**类型 */
    type: CoreGlobalFeatureType;
    /**调整层级 */
    layer: string;
    /**优先级-优先级值越小,优先级就越大 */
    priority: number;
    /**特性配置Id */
    configId: number;
    /**特性配置数据 */
    configData: any;
}
interface IFeatureConfig {
    /**特性类型 */
    type: number;
    /**特性数据 */
    data: any;
}
/**核心全局特性类型 */
declare const enum CoreGlobalFeatureType {
    /**某层头部插入特性 特性优先级 */
    InsertHead = 1,
    /**某层尾部插入特性 特性优先级 */
    InsertTail = 2
}

declare class Puzzle<T_PuzzleArgs extends {
    FuncMap: Record<number, StateLessFunction>;
    InputFuncMap: Record<keyof T_PuzzleArgs['FuncMap'], any>;
    OutputFuncMap: Record<keyof T_PuzzleArgs['FuncMap'], any>;
    IGlobalData: Record<string, any>;
    GlobalDataTypeMap: Record<keyof T_PuzzleArgs['IGlobalData'], any>;
    IStaticData: Record<string, any>;
    StaticMap: Record<keyof T_PuzzleArgs['IStaticData'], any>;
    SystemMap: Record<string, string>;
    InputSystemMap: Record<keyof T_PuzzleArgs['SystemMap'], any>;
    OutputSystemMap: Record<keyof T_PuzzleArgs['SystemMap'], any>;
}> {
    /**=======================================工具集Start=============================================== */
    /**确定随机数 */
    fixedRandom: FixedRandomUtil;
    /**事件派发器 */
    dispatcher: Emitter<Record<EventType, unknown>>;
    /**=======================================工具集End=============================================== */
    /**=======================================数据实例映射表Start=============================================== */
    /**拼图块-组件实例表-映射表 */
    private $compInsPuzzleMap;
    /**拼图块-拼图組件配置实例表-映射表 */
    private $compTvoPuzzleMap;
    /**拼图块-拼图适配组件配置实例表-映射表 */
    private $adapterMainMap;
    /**拼图块-全局特性实例表-映射表 */
    private $globalFeaturePuzzleMap;
    /**=======================================数据映射表End=============================================== */
    /**=======================================数据注册表Start=============================================== */
    /**组件表 */
    private $compClzzMap;
    /**无状态函数 */
    private $statelessFuncMap;
    /**特性表 */
    private $featureMap;
    /**全局特性表 */
    private $globalFeatureMap;
    /**=======================================数据注册表End=============================================== */
    /**===================拼图块接口间数据共享Start========================= */
    /**静态数据集 */
    private $staticDataMap;
    /**数据集 */
    private $dataMap;
    /**外部函数集 */
    private $externFuncMap;
    /**===================拼图块接口间数据共享End========================= */
    /**===================拼图块接口间数据隔离Start========================= */
    /**组件实例表 */
    private $compInsMap;
    /**拼图块入口组件Id */
    private $adapterCompId;
    /**拼图组件配置表 */
    private $compTvoMap;
    /**当前运行块接口Id */
    private $puzzleId;
    /**===================拼图块接口间数据隔离End========================= */
    /**创建一个拼图块实例 */
    static createPuzzle<T_PuzzleArgs extends {
        FuncMap: Record<string, StateLessFunction>;
        InputFuncMap: Record<keyof T_PuzzleArgs['FuncMap'], any>;
        OutputFuncMap: Record<keyof T_PuzzleArgs['FuncMap'], any>;
        IGlobalData: Record<string, any>;
        GlobalDataTypeMap: Record<keyof T_PuzzleArgs['IGlobalData'], any>;
        IStaticData: Record<string, any>;
        StaticMap: Record<keyof T_PuzzleArgs['IStaticData'], any>;
        SystemMap: Record<string, string>;
        InputSystemMap: Record<keyof T_PuzzleArgs['SystemMap'], any>;
        OutputSystemMap: Record<keyof T_PuzzleArgs['SystemMap'], any>;
    }>(): Puzzle<T_PuzzleArgs>;
    /**初始化拼图 */
    $init(): void;
    $registeCoreComponents(key: number, component: clzz<IBaseComponent>): void;
    /**注册组件 */
    registeCoreComponents(key: number, component: clzz<IBaseComponent>): void;
    /**无状态函数注册 */
    registeStateless<T extends keyof T_PuzzleArgs['FuncMap']>(statelessType: T, statelessId: number, func: T_PuzzleArgs['FuncMap'][T]): void;
    /**全局特性注册 */
    registeGlobalFeature(type: CoreGlobalFeatureType, gf: IGlobalFeature): void;
    /**配置预制体注册 */
    registeConfigPrefab(featureId: number, featureFP: clzz<BaseConfigPrefab<any>>): void;
    /**创建配置预制体 */
    createConfigPrefab<T extends BaseConfigPrefab<any>>(featureId: number): T;
    /**初始化拼图块
     * @param puzzleType 拼图块类型
     * @param puzzleId 拼图块Id
     * @param config 拼图块配置 */
    initPuzzlePiece(puzzleType: string, puzzleId: string, config: DataConfig): void;
    /**初始化拼图块全局特性
     * @param puzzleType 拼图块类型
     * @param config 全局特性配置 */
    initPuzzlePieceGlobalFeature(puzzleType: string, config: GlobalFeatureData): void;
    /**获取拼图块全局特性 */
    getPuzzlePieceGlobalFeature(type: CoreGlobalFeatureType): IGlobalFeature;
    /**获取拼图块全局特性集合 */
    getPuzzlePieceGlobalFeatureDataArr(puzzleType: string): readonly (readonly DeepReadonly<GlobalFeatureData>[])[];
    /**销毁拼图块
     * @param puzzleType 拼图块类型
     * @param puzzleId 拼图块Id
     * */
    disposePuzzlePiece(puzzleType: string, puzzleId: string): void;
    /**注册外部函数集 */
    registeExternFunc(type: string, func: Function): void;
    /**运行外部函数 */
    executeExternFunc(type: string, ...args: any): any;
    /**运行拼图块
     * @param puzzleType 拼图块类型
     * @param puzzleId 拼图块Id
     * @param inputArgs 外部全局数据传参
     * @return 返回全局数据传参(内部可能会修改外部全局数据)
     */
    runPuzzlePiece<T extends keyof T_PuzzleArgs['SystemMap']>(puzzleType: T, puzzleId: string, inputArgs: T_PuzzleArgs['InputSystemMap'][T]): T_PuzzleArgs['OutputSystemMap'][T];
    /**运行逻辑组件 */
    runLogicComp(compId: LogicCompId): void;
    /**运行特性组件 */
    runFeatureComp(compId: FeatureCompId): boolean;
    /**运行条件组件 */
    runConditionComp(compId: ConditionCompId): boolean;
    /**运行公式组件 */
    runFormulaComp(compId: FormulaCompId, args: number[]): number;
    /**运行无状态函数 */
    runStatelessFunc<T extends keyof T_PuzzleArgs['FuncMap']>(statelessType: T, statelessId: number, inputArgs: T_PuzzleArgs['InputFuncMap'][T]): T_PuzzleArgs['OutputFuncMap'][T];
    /**获取数据集 */
    getData<T extends keyof T_PuzzleArgs['IGlobalData']>(type: T): T_PuzzleArgs['GlobalDataTypeMap'][T];
    /**设置数据集 */
    setData<T extends keyof T_PuzzleArgs['IGlobalData']>(type: T, val: T_PuzzleArgs['GlobalDataTypeMap'][T]): void;
    /**获取静态数据集 */
    getStaticData<T extends keyof T_PuzzleArgs['IStaticData']>(type: T): DeepReadonly<T_PuzzleArgs['StaticMap'][T]>;
    /**设置静态数据集 */
    setStaticData<T extends keyof T_PuzzleArgs['IStaticData']>(type: T, val: T_PuzzleArgs['StaticMap'][T]): void;
    /**获取时间戳:s */
    getTime(): number;
    /**获取拼图块Id */
    getPuzzleId(): string;
    /**重置拼图块重映射 */
    resetPuzzlePieceMap(puzzleType: string, puzzleId: string): void;
    /**生成拼图块配置 */
    createPuzzlePieceConfig<T extends PuzzleDataConfig>(puzzleType: string, puzzleId: string, puzzleConfigPrefabId: number, data: T): DataConfig;
    /**获取块接口键值 */
    private $getPuzzlePieceKey;
    /**获取无状态函数键值 */
    private $getStatelessKey;
    /**获取需要移除的组件列表 */
    private $getRemoveCompArr;
    /**获取组件实例 */
    private $getCompByCompId;
    /**获取组件配置 */
    private $getCompTvo;
    /**创建组件 */
    private $createComp;
    /**销毁组件 */
    private $disposeComp;
}
type TPuzzleArgs<T extends {
    /**无状态函数 */
    FuncMap: Record<string, StateLessFunction>;
    /**无状态函数输入参数映射 */
    InputFuncMap: Record<keyof T['FuncMap'], any>;
    /**无状态函数输出参数映射 */
    OutputFuncMap: Record<keyof T['FuncMap'], any>;
    /**全局数据类型 */
    IGlobalData: Record<string, any>;
    /**全局数据类型映射 */
    GlobalDataTypeMap: Record<keyof T['IGlobalData'], any>;
    /**静态数据类型 */
    IStaticData: Record<string, any>;
    /**静态数据类型映射 */
    StaticMap: Record<keyof T['IStaticData'], any>;
    /**拼图块 */
    SystemMap: Record<string, string>;
    /**拼图块输入参数映射 */
    InputSystemMap: Record<keyof T['SystemMap'], any>;
    /**拼图块输出参数映射 */
    OutputSystemMap: Record<keyof T['SystemMap'], any>;
}> = T;
type CorePuzzle = TPuzzleArgs<{
    FuncMap: {};
    InputFuncMap: {};
    OutputFuncMap: {};
    IGlobalData: {};
    GlobalDataTypeMap: {};
    IStaticData: {};
    StaticMap: {};
    SystemMap: {};
    InputSystemMap: {};
    OutputSystemMap: {};
}>;

/**基础组件 */
interface IBaseComponent {
    /**上下文实例 */
    context?: Puzzle<CorePuzzle>;
    /**组件id */
    compId: number;
    /**组件类型 */
    compType: number;
    /**组件模板 */
    compTvo: any;
    /**组件创建时调用-禁止调用,除非你知道你在做什么 */
    $create?(): any;
    /**组件销毁时调用-禁止调用,除非你知道你在做什么 */
    $dispose?(): any;
}

/**逻辑组件 */
interface ILogicComponent extends IBaseComponent {
    /**逻辑执行 */
    execute(): void;
}

/**外部适配组件 */
interface IAdapterComponent extends ILogicComponent {
    /**适配输入数据 */
    inputData(args: any): any;
    /**适配输出数据 */
    outputData(): any;
}

/**条件组件 */
interface IConditionComponent extends IBaseComponent {
    /**是否满足条件 */
    checkCondition(): boolean;
}

/**特性组件 */
interface IFeatureComponent extends IBaseComponent {
    /**执行特性
     * @returns 特性是否满足运行条件
     */
    executeFeature(): boolean;
}

/**公式组件 */
interface IFormulaComponent extends IBaseComponent {
    /**计算公式 */
    calculate(args: number[]): number;
}

interface IPuzzle<T_PuzzleArgs extends TPuzzleArgs<CorePuzzle>> {
    /**初始化 */
    init(): any;
    /**初始化拼图系统静态数据 */
    initStaticData(data: any): any;
    /**初始化拼图块 */
    initPuzzlePiece(puzzleType: string, puzzleId: string, config: DataConfig): any;
    /**初始化拼图系统-动态配置 */
    initPuzzlePieceDynamic(puzzleType: string, puzzleId: string, data: any, puzzleConfigPrefabId?: number): any;
    /**销毁拼图系统 */
    disposePuzzlePiece(puzzleType: string, puzzleId: string): any;
    /**注册外部函数集 */
    registeExternFunc(type: string, func: Function): any;
    /**运行拼图节点 */
    runPuzzlePiece<T extends keyof T_PuzzleArgs['SystemMap']>(puzzleType: T, puzzleId: string, inputArgs: T_PuzzleArgs['InputSystemMap'][T]): T_PuzzleArgs['OutputSystemMap'][T];
}

/**注册组件 */
declare function $registeCoreComp(puzzle: Puzzle<CorePuzzle>): void;

declare class AllConditionComponent implements IConditionComponent {
    context: Puzzle<CorePuzzle>;
    compId: number;
    compType: number;
    compTvo: AllConditionComponentTvo;
    checkCondition(): boolean;
}
interface AllConditionComponentTvo {
    /**条件组件id列表 */
    readonly conditionCompIdArr: Array<ConditionCompId>;
}

declare class BasicFeatureComponent implements IFeatureComponent {
    context: Puzzle<CorePuzzle>;
    compId: number;
    compType: number;
    compTvo: BasicFeatureComponentTvo;
    /**async*/ executeFeature(): boolean;
}
interface BasicFeatureComponentTvo {
    /**特性运行条件Id */
    readonly conditionCompId: ConditionCompId;
    /**特性运行逻辑Id */
    readonly logicCompId: LogicCompId;
    /**特性能力分层条件Id */
    readonly abilityConditionCompId?: ConditionCompId;
}

declare class FeatureGroupLogicComponent implements ILogicComponent {
    context: Puzzle<CorePuzzle>;
    compId: number;
    compType: number;
    compTvo: FeatureGroupLogicComponentTvo;
    /**async*/ execute(): void;
}
interface FeatureGroupLogicComponentTvo {
    /**特性层定义 */
    readonly featureLayerDefine: FeatureLayerDefine;
    /**特性组合列表 */
    readonly featureCompIdArr: Array<FeatureCompId>;
}
/**特性层定义 */
declare enum FeatureLayerDefine {
    /**按优先级顺序执行全部 */
    ExecuteAll = 0,
    /**按优先级顺序执行一个 */
    ExecuteOne = 1
}

declare class LinearGroupLogicComponent implements ILogicComponent {
    context: Puzzle<CorePuzzle>;
    compId: number;
    compType: number;
    compTvo: LinearGroupLogicComponentTvo;
    /**async*/ execute(): void;
}
interface LinearGroupLogicComponentTvo {
    /**逻辑组件子项列表 */
    readonly itemTvoArr: Array<LinearGroupLogicComponentItemTvo>;
}
interface LinearGroupLogicComponentItemTvo {
    /**逻辑组件Id */
    readonly logicCompId: LogicCompId;
    /**条件组件Id */
    readonly conditionCompId: ConditionCompId;
}

declare class RandomLogicComponent implements ILogicComponent {
    context: Puzzle<CorePuzzle>;
    compId: number;
    compType: number;
    compTvo: RandomLogicComponentTvo;
    /**async*/ execute(): void;
    /**获取总权重 */
    $getMaxWeight(randomLogicArr: RandomLogicItem[]): number;
}
interface RandomLogicComponentTvo {
    /**随机逻辑列表 */
    readonly randomLogicArr: RandomLogicItem[];
}
interface RandomLogicItem {
    /**随机权重 */
    readonly randomWeight: number;
    /**逻辑组件id */
    readonly logicCompId: LogicCompId;
}

declare class SelectGroupLogicComponent implements ILogicComponent {
    context: Puzzle<CorePuzzle>;
    compId: number;
    compType: number;
    compTvo: SelectGroupLogicComponentTvo;
    /**async*/ execute(): void;
}
interface SelectGroupLogicComponentTvo {
    /**逻辑组件子项列表 */
    readonly itemTvoArr: Array<SelectGroupLogicItemTvo>;
}
interface SelectGroupLogicItemTvo {
    /**逻辑组件Id */
    readonly logicCompId: LogicCompId;
    /**条件组件Id */
    readonly conditionCompId: ConditionCompId;
}

/**通用组件<1000 */
declare const enum CoreComponentType {
    /**
     * 永远返回真-条件组件(特殊组件)
     */
    AlwaysTrueConditionComponent = 0,
    /**
     * 全条件-组件
     * 满足所有条件才能返回true
     */
    AllConditionComponent = 1,
    /**
     * 选择-逻辑组-组件
     * 选择逻辑组执行条件
     * 只优先级最高的逻辑组件进行逻辑执行
     */
    SelectGroupLogicComponent = 2,
    /**
     * 线性-逻辑组-组件
     * 顺序执行逻辑组里面的所有组件
     */
    LinearGroupLogicComponent = 3,
    /**随机执行一个逻辑 */
    RandomLogicComponent = 4,
    /**特性组逻辑组件 */
    FeatureGroupLogicComponent = 5,
    /**
     * 基础-特性组件
     */
    BasicFeatureComponent = 6
}

/**算法类型枚举 */
declare enum AlgoClassTypeConst {
    /**===========================类型枚举============================== */
    /** 随机 */
    AlgoRandom = 0,
    /** 熵增1 */
    AlgoEntropyIncrease1 = 1,
    /** 熵增3 */
    AlgoEntropyIncrease3 = 2,
    /** 简单难题 */
    AlgoSimpleHard = 3,
    /** 填空消除 */
    AlgoFillInTheBlank = 4,
    /** 熵减*/
    AlgoEntropyReduction = 5,
    /** 困难难题*/
    AlgoHard = 6,
    /** 随机无死题*/
    AlgoRandomNoDeath = 7,
    /** 直觉难题*/
    AlgoInstinctHard = 8,
    /** 致死题*/
    AlgoDeath = 9,
    /**===========================实例枚举============================== */
    /**底板-随机 */
    algo_random = 10001,
    /**底板-首次随机 */
    algo_random_first = 10002,
    /**底板-固定块池随机 */
    algo_random_fix_block = 10003,
    /**底板-真随机 */
    algo_real_random = 10004,
    /**底板-熵增1 */
    algo_entropy_increase1 = 11001,
    /**底板-熵增3 */
    algo_entropy_increase3 = 12001,
    /**子母熵增3 */
    algo_entropy_increase3_zimu = 12002,
    /**底板-简单难题 */
    algo_simple_hard = 13001,
    /**底板-填空消除，边数不考虑消除 */
    algo_fill_in_the_blank = 14001,
    /**填空消除，边数考虑消除 */
    algo_fill_in_the_blank_clear = 14002,
    /**全组合填空消除9 */
    algo_fill_in_the_blank_id_9 = 14003,
    /**全组合填空消除21 */
    algo_fill_in_the_blank_id_21 = 14004,
    /**填空消除位置连贯（填空消除连贯） 不考虑消除*/
    algo_fill_in_the_blank_by_pos = 14005,
    /**填空消除位置连贯（填空消除连贯） 考虑消除*/
    algo_fill_in_the_blank_clear_by_pos = 14006,
    /**旅行填空消除*/
    algo_fill_in_the_blank_journey = 14007,
    /**高效消除*/
    algo_fill_in_the_blank_high_effect = 14008,
    /**全组合填空消除70 */
    algo_fill_in_the_blank_id_70 = 14009,
    /**消除爽 */
    algo_fill_in_the_blank_clear_cool = 14010,
    /**连贯旅行填空消除 */
    algo_fill_in_the_blank_journey_by_pos = 14011,
    /**空洞消除算法*/
    algo_fill_in_the_hole = 14012,
    /**快速填空消除算法*/
    algo_quick_fill = 14013,
    /**方块之战，冒险模式用*/
    algo_fill_in_the_blank_adv = 14015,
    /**底板-熵减 */
    algo_entropy_reduction = 15001,
    /**底板-困难难题*/
    algo_hard = 16001,
    /**简单直觉难题*/
    algo_simple_instinct_hard = 16002,
    /**简单死亡*/
    algo_simple_death = 16003,
    /**底板-高回报困难难题*/
    algo_hard_high_effect = 16004,
    /**更困难难题*/
    algo_hard_hard = 16005,
    /**十字解困难难题*/
    algo_hard_cross = 16006,
    /**困难难题扩展1*/
    algo_hard_extend1 = 16007,
    /**困难难题扩展2*/
    algo_hard_extend2 = 16008,
    /**困难难题二进制*/
    algo_hard_binary = 16009,
    /**底板-随机无死亡 */
    algo_random_no_death = 17001,
    /**底板-直觉难题 */
    algo_instinct_hard = 18001,
    /**底板-致死题 */
    algo_death = 19001,
    /**复活算法 */
    algo_revive = 20001,
    /**复活算法变种，选择可消除边数小的块 */
    algo_revive_clear = 20002,
    /**清屏算法 */
    algo_clear_board = 21001,
    /**终止刷分算法 */
    algo_stop_refresh_record = 22001,
    /**连续两轮出块相似处理算法 */
    algo_same_more_block = 23001
}
/**算法类型枚举 */
declare const AlgoClassTypeConstName: {
    /**===========================类型枚举============================== */
    /** 随机 */
    0: string;
    /** 熵增1 */
    1: string;
    /** 熵增3 */
    2: string;
    /** 简单难题 */
    3: string;
    /** 填空消除 */
    4: string;
    /** 熵减*/
    5: string;
    /** 困难难题*/
    6: string;
    /** 随机无死题*/
    7: string;
    /** 直觉难题*/
    8: string;
    /** 致死题*/
    9: string;
    /**===========================实例枚举============================== */
    /**底板-随机 */
    10001: string;
    /**底板-首次随机 */
    10002: string;
    /**底板-首次随机 */
    10003: string;
    /**底板-熵增1 */
    11001: string;
    /**底板-熵增3 */
    12001: string;
    /**子母熵增3 */
    12002: string;
    /**底板-简单难题 */
    13001: string;
    /**底板-填空消除，边数不考虑消除 */
    14001: string;
    /**填空消除，边数考虑消除 */
    14002: string;
    /**全组合填空消除9 */
    14003: string;
    /**全组合填空消除21 */
    14004: string;
    /**全组合填空消除70 */
    14009: string;
    /**填空消除位置连贯（填空消除连贯） 不考虑消除*/
    14005: string;
    /**填空消除位置连贯（填空消除连贯） 考虑消除*/
    14006: string;
    /**旅行填空消除*/
    14007: string;
    14011: string;
    /**旅行填空消除*/
    14008: string;
    /**旅行填空消除*/
    14010: string;
    /**空洞填空消除*/
    14012: string;
    /**快速填空消除算法*/
    14013: string;
    /**冒险模式方块之战*/
    14015: string;
    /**底板-熵减 */
    15001: string;
    /**底板-困难难题*/
    16001: string;
    /**底板-高回报困难难题*/
    16004: string;
    /**更困难难题*/
    16005: string;
    /**十字解困难难题*/
    16006: string;
    /**困难难题扩展1*/
    16007: string;
    /**困难难题扩展2*/
    16008: string;
    /**简单直觉难题*/
    16002: string;
    /**简单难题*/
    16003: string;
    /**底板-随机无死亡 */
    17001: string;
    /**底板-直觉难题 */
    18001: string;
    /**底板-致死题 */
    19001: string;
    /**复活算法 */
    20001: string;
    /**复活算法 */
    20002: string;
    /**清屏算法 */
    21001: string;
    /**终止刷分算法 */
    22001: string;
    /**预防相似出块算法 */
    23001: string;
};
/**该枚举是否是算法实例枚举 */
declare function isAlgoInstance(algoClassTypeConst: AlgoClassTypeConst): boolean;
/**算法降级组 */
type AlgoGroup = AlgoClassTypeConst[];

interface Vec {
    x: number;
    y: number;
}

/**特性配置预制体 */
declare abstract class FeatureConfigPrefab<T> extends BaseConfigPrefab<T> {
    protected rewriteConfig(puzzleDataConfig: T): void;
}

/**拼图块配置预制体 */
declare abstract class PuzzleConfigPrefab<T extends PuzzleDataConfig> extends BaseConfigPrefab<T> {
    protected abstract registeCompIdList(): number[];
    protected abstract createConfigPrefab(listCompId: number[], compTvoMap: CompTvoMap): any;
    protected abstract rewritePrefabData(listCompId: number[], compTvoMap: CompTvoMap, configData: T): any;
    private $idAdd;
    private $puzzle;
    private puzzleType;
    init(puzzleType: string, puzzle: Puzzle<CorePuzzle>, idAdd: IDADD): void;
    protected initFeatureCompIdArr(featureIdList: FeatureIdList, featureCompIdArr: FeatureCompId[], prefabConfigData: Record<number, PrefabConfigData>): void;
    protected rewriteConfig(puzzleDataConfig: T): void;
    /**获取预制体数据 */
    private $getPrefabData;
}

/**公共工具类型 */
declare class CommonUtil {
    /**是否有值 */
    static hasValue(val: any): boolean;
}

declare class SplitFrameUtil {
    /**是否打开分帧 */
    private static $isOpenSplitFrame;
    /**是否打开分帧 */
    private static $lastFrameTime;
    /**分帧间隔时间 */
    private static frameTime;
    /**超时时间 */
    private static overTime;
    /**初始化分帧工具 */
    static openSplitFrame(): void;
    /**关闭分帧 */
    static closeSplitFrame(): void;
    /**设置分帧间隔时间 */
    static setFrameTime(frameTime: number): void;
    /**设置分帧间隔时间 */
    static setOverTime(overTime: number): void;
    /**检查是否超时 */
    static checkOverTime(): boolean;
    /**分帧计算-不要在调用很密集的地方使用该函数,这会带来额外的性能损耗*/
    static checkNextFrame(): void;
    /**强制进行分帧*/
    static forceNextFrame(): void;
}

/**
 * 打印工具
 *
 */
declare class LogUtil {
    private static readonly TAG;
    static showLog: boolean;
    static printLog(msg: string): void;
    private static print;
    static log(...args: any[]): void;
    static warn(...args: any[]): void;
}

interface BlockShape {
    /**方块ID */
    id: number;
    /**方块二进制数据 */
    shape: number[];
    /**方块宽度 */
    width: number;
    /**方块高度 */
    height: number;
    /**方块块信息 */
    info: number[][];
    /**方块格子数量 */
    count: number;
    /**方块边数 */
    border: number;
    /**方块第一行非空偏移 */
    offsetCol: number;
    /**方块熵值 */
    entroy?: number;
    /**方块第一行非空偏移 */
    blockBox?: number;
    /**方块第一行非空偏移 */
    blockBit?: number[];
}
declare class ShapeInfo {
    private static blockInfos;
    /**虚拟块ID起始 */
    private static virtualID;
    /**生成块数据 */
    static createConfig(info: number[][]): number;
    /**获取方块数据 */
    static getConfig(id: any): BlockShape | {
        id: any;
        width: number;
        height: number;
        shape: number[];
        info: number[][];
        offsetCol: number;
        count: number;
        border: number;
    };
    static getBorderNum(info: number[][]): number;
}

declare function shuffle(array: any[]): any[];
declare enum ClearType {
    NONE = 0,
    ROW = 1,
    COL = 2,
    CROSS = 3
}
declare class BinaryBoard {
    static excludeLevel: number;
    static excludeBlock: any[];
    static BLOCKS: number[];
    static excludeActive: boolean;
    rowCount: number;
    colCount: number;
    leftSide: number;
    rightSide: number;
    topSide: number;
    bottomSide: number;
    bFull: number;
    rowBinary: number[];
    _record: any[];
    getBlockBoardRC(id: number, leftTop: Vec): Vec[];
    emptyAt(x: number, y: number): boolean;
    getCanPutIds(ids: number[]): number[];
    getCanPutPoss(id: number): Vec[];
    getCanPutPossBetweenNear(id: number, minNear: number, maxNear: number): Vec[];
    /**
     * 14 w:3 h:2 shape:3、6
     * 0011
     * 0110
     * 39 w:3 h:3 shape:4、2、1
     * [0,1,0,0],
     * [0,0,1,0],
     * [0,0,0,1],
     */
    getCanPutArr(arr: number[][], pTag?: number): Vec[];
    canPutDontClear(id: number): boolean;
    canPut(id: number): boolean;
    canClear(id: number): {
        clear: boolean;
        pos: Vec;
    } | {
        clear: boolean;
        pos?: undefined;
    };
    putBlock(id: number, pos: Vec): void;
    removeBlock(id: number, pos: Vec): void;
    canPutBlock(id: number, pos: Vec): boolean;
    canPutBlockNew(id: number, pos: Vec): boolean;
    /**
     * 检查是否可以将指定形状的块放置在指定位置
     * @param id 块的ID
     * @param pos 块的位置
     * @returns 如果可以放置，返回true；否则返回false
     */
    canPutBlockWithShap(id: number, pos: Vec): boolean;
    clone(): BinaryBoard;
    convertToBinaryBoard(data: DeepReadonly<number[][]>): void;
    convertToArr(): number[][];
    /**
     * 将二进制盘面转为十进制数组
     * @returns
     */
    convertToDecimalBoard(): number[][];
    canClearBlockArr(isClear?: boolean): boolean;
    getClearType(): ClearType;
    /**
     * 获取在指定位置的消除类型
     * @param id 块id
     * @param pos 位置
     * @returns
     */
    getClearTypeInPos(id: number, pos: Vec): ClearType;
    getClearCount(): number;
    clearRow(row: number): void;
    clearCol(col: number): void;
    setBitToZero(binary: number, n: number): number;
    listToBinary(list: DeepReadonly<number[]>): number;
    binaryToList(binary: number): number[];
    splitBlocks(blockIds: DeepReadonly<number[]>): {
        all: any[];
        noPut: any[];
        put: any[];
        clear: any[];
        canPut: any[];
    };
    getEdgeGameNum(isBorderCount?: boolean): number;
    /**
     * 计算并返回指定列的二进制表示。
     * @param columnIdx 列的索引，从0开始。
     * @returns 指定列的二进制表示。
     */
    private calculateColumnBinary;
    /**
     * 获得连续边数
     * @param ori 二进制对象
     * @returns
     */
    getEdgeNumBinary(): number;
    /**
     * 获取行或列的连续边数
     * @param binaryArr 行或列的二进制数据
     * @returns
     */
    private getContEdge;
    getCanPut(filterList: number[]): any[];
    getCanPutRandom(): any;
    static getIncludeBlock(): any[];
    getCanPutEdge(): {
        block: number;
        binary: BinaryBoard;
    };
    getEdgeBinary(n: number, isBorderCount?: boolean): number;
    /**获取贴边率 */
    getNearBoarderPer(id: number, pos: Vec): number;
    logEmptyTile(): void;
    record(): any[];
    revert(): void;
    reset(record: any): void;
    set board(boardArr: number[]);
    get board(): number[];
    isBackEyeJudge2(_edge1: any, __id1: any, __canPutsInit1: any, _edge2: any, __id2: any, __canPutsInit2: any): boolean;
    separateOnes(binaryNumber: any): {
        bit: number[];
        pos: number[];
    };
    getClearRowCol(): {
        row: number[];
        col: number[];
    };
    /**
     * 是否可以将数组内的id都放入盘面 ,数组必须是3个ID。
     */
    checkPutAllBlocks(id: number[], putList?: any[]): boolean;
    canClearRowCols(isClear?: boolean): {
        row: any[];
        col: any[];
    };
    /**
     * 获得空洞总数
     */
    getEmptyHoleNum(): number;
    getEmptyNumObj(): number;
    getWeightValueObj(save_Arr?: number[][]): number;
    static getWeightValue(board: number[][]): number;
    /**
     * 是否可以将数组内的id都放入盘面,不限制数组数
     */
    checkPutIdArrInBlocks(id: number[]): boolean;
    getMinGameEdgeNum(blockList: number[]): {
        id: number;
        pos: Vec;
    };
    getMinGameEdgeNumWithClear(blocks: number[]): {
        id: number;
        pos: Vec;
    };
    /**
     * 获取当前盘面的死亡率
     * @param ids 传入计算难度系数的3个块
     * @returns
     */
    GetDeathRate(ids: number[]): number;
    /**
     * 判断某个坐标点 某个模型放进去所占用的点集合
     * @param initialX 行索引，从0开始。
     * @param initialY 列索引，从0开始。
     * @param blocks 异形块形状
     * @returns 位置是否有东西，即不是空位置。
        */
    generateCoordinates(initialX: any, initialY: any, blocks: any): any[];
    getClearPutAndNoPut(blockArr: any, clearList: any, putList: any, noPutList: any): void;
    getBlockIdByPos(pos: number[][]): BlockShape;
    /**获取贴边率 */
    getDynamicNearBoarderPer(blocks: number[][], pos: Vec): number;
}

export { $registeCoreComp, AlgoClassTypeConst, AlgoClassTypeConstName, type AlgoGroup, AllConditionComponent, type AllConditionComponentTvo, AllwaysTrue, BaseConfigPrefab, BasicFeatureComponent, type BasicFeatureComponentTvo, BinaryBoard, type BlockShape, ClearType, CommonUtil, type CompId, type CompInsMap, type CompTvoMap, type ConditionCompId, CoreComponentType, CoreGlobalFeatureType, type CorePuzzle, type DataConfig, type DeepReadonly, type FeatureCompId, FeatureConfigPrefab, FeatureGroupLogicComponent, type FeatureGroupLogicComponentTvo, type FeatureId, type FeatureIdList, FeatureLayerDefine, FixedRandomUtil, type FormulaCompId, type GlobalFeatureData, type IAdapterComponent, type IBaseComponent, type IConditionComponent, type IDADD, type IFeatureComponent, type IFeatureConfig, type IFormulaComponent, type IGlobalFeature, type ILogicComponent, type IPuzzle, IdAdd, LinearGroupLogicComponent, type LinearGroupLogicComponentTvo, LogUtil, type LogicCompId, type Mutable, type PrefabConfigData, Puzzle, PuzzleConfigPrefab, type PuzzleDataConfig, RandomLogicComponent, type RandomLogicComponentTvo, SelectGroupLogicComponent, type SelectGroupLogicComponentTvo, ShapeInfo, SplitFrameUtil, type StateLessFunction, type TPuzzleArgs, type Vec, type clzz, type dict, getCompId, getCompType, isAlgoInstance, shuffle };
