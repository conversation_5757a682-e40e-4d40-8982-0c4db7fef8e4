import TargetComponent from '../components/special/TargetComponent';
import { TempleteType } from '../registry/templete/TempleteRegistry';
import { ECSRuleBase } from './core/ECSRuleBase';

/**关卡目标信息显示 */
export class ShowTargetInfoRule extends ECSRuleBase {
    canExecute(): boolean {
        return true;
    }

    execute(): void {
        console.log('ShowTargetInfoRule execute - 显示关卡目标信息');
        const targetEntity = this.world.query([TargetComponent])[0];
        if (!targetEntity) return;
        const targetComponent = this.world.getComponent(targetEntity, TargetComponent);
        if (!targetComponent) return;
        const targetList = targetComponent.targets;
        // 创建目标信息显示实体
        this.world.templeteCenter.createTempleteEntity(
            TempleteType.Render,
            {
                parentEntity: this.world.ECSLayerType.UILayerEntity,
                ecsViewId: 'ecsview_target_info',
            },
            {
                renderParam: {
                    data: {
                        targetList: targetList,
                    },
                },
            },
        );
    }
}
