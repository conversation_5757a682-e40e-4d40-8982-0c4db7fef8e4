#!/bin/bash

# 1. 变量定义（可通过外部传入）
echo "[1/13] 初始化变量..."
GIT_URL=https://<EMAIL>/chenyong/block-blast-publish.git
GIT_PROD_URL=https://<EMAIL>/chenyong/block-blast-prod-publish.git
GIT_LOCAL_PATH=/Users/<USER>/blockblast-adv/block-blast-publish
GIT_PROD_LOCAL_PATH=/Users/<USER>/blockblast-adv/block-blast-prod-publish
PLATFORM=$1   # 例如 android/ios/web
ENV=$2        # 例如 prod/test
VERSION=$3    # 例如 1.0.0
useProdUrl=$4  # 是否使用正式服 git 上报，true/false
PROJECT_PATH=$(pwd)
HOTUPDATE_PATH=/Users/<USER>/blockblast-adv/hotUpdate/${PLATFORM}/${ENV}/${VERSION}
BUILD_TEMPLATES_PATH=${PROJECT_PATH}/build-templates/jsb-link

# 根据 useProdUrl 统一设置 GIT_URL 和 GIT_LOCAL_PATH
if [ "$useProdUrl" = "true" ]; then
  GIT_URL="$GIT_PROD_URL"
  GIT_LOCAL_PATH="$GIT_PROD_LOCAL_PATH"
  echo "使用正式服 git 上报: $GIT_URL"
else
  echo "使用测试服 git 上报: $GIT_URL"
fi

echo "GIT_URL: $GIT_URL"
echo "GIT_PROD_URL: $GIT_PROD_URL"
echo "GIT_LOCAL_PATH: $GIT_LOCAL_PATH"
echo "GIT_PROD_LOCAL_PATH: $GIT_PROD_LOCAL_PATH"
echo "PLATFORM: $PLATFORM"
echo "ENV: $ENV"
echo "VERSION: $VERSION"
echo "PROJECT_PATH: $PROJECT_PATH"
echo "HOTUPDATE_PATH: $HOTUPDATE_PATH"
echo "BUILD_TEMPLATES_PATH: $BUILD_TEMPLATES_PATH"

# 2. 拉取远程仓库
echo "[2/13] 检查并拉取远程仓库..."
if [ ! -d "$GIT_LOCAL_PATH/.git" ]; then
  git clone "$GIT_URL" "$GIT_LOCAL_PATH"
else
  cd "$GIT_LOCAL_PATH"
  git pull
fi

# 3. 删除 .git 以外的所有文件
echo "[3/13] 清理 GIT 目录..."
cd "$GIT_LOCAL_PATH"
find . -mindepth 1 -maxdepth 1 ! -name ".git" | xargs rm -rf

# 4. 拷贝热更资源
echo "[4/13] 拷贝热更资源到 GIT 目录..."
cp -rf "${HOTUPDATE_PATH}/." "$GIT_LOCAL_PATH/"

# 5. 重命名 assets 目录
# echo "[5/13] 重命名 assets 目录..."
# [ -d assets/internal ] && mv assets/internal assets/refactoredInternal
# [ -d assets/main ] && mv assets/main assets/refactoredMain
# [ -d assets/resources ] && mv assets/resources assets/refactoredResources

# 6. main.js 改名并移动
echo "[6/13] 处理 main.js..."
[ -f main.js ] && mkdir -p src && mv main.js src/main.refactored.js

# 7. 拷贝 distribution.js
echo "[7/13] 拷贝 distribution.js..."
cp "${BUILD_TEMPLATES_PATH}/distribution.js" "$GIT_LOCAL_PATH/src/distribution.js"

# 8. 拷贝 main.js
echo "[8/13] 拷贝 main.js..."
cp "${BUILD_TEMPLATES_PATH}/main.js" "$GIT_LOCAL_PATH/main.js"

# 9. 移动 settings.js
echo "[9/13] 移动 settings.js..."
[ -f src/settings.js ] && mkdir -p src/refactored && mv src/settings.js src/refactored/settings.js
[ -f src/settings.jsc ] && mkdir -p src/refactored && mv src/settings.jsc src/refactored/settings.jsc

# 10. 移动 cocos2d-jsb.js
echo "[10/13] 移动 cocos2d-jsb.js..."
[ -f src/cocos2d-jsb.js ] && mkdir -p src/refactored && mv src/cocos2d-jsb.js src/refactored/cocos2d-jsb.js
[ -f src/cocos2d-jsb.jsc ] && mkdir -p src/refactored && mv src/cocos2d-jsb.jsc src/refactored/cocos2d-jsb.jsc

# 11. 移动 jsb-engine.js
echo "[11/13] 移动 jsb-engine.js..."
[ -f ${PROJECT_PATH}/build/jsb-link/jsb-adapter/jsb-engine.js ] && mkdir -p jsb-adapter/refactored && cp ${PROJECT_PATH}/build/jsb-link/jsb-adapter/jsb-engine.js jsb-adapter/refactored/jsb-engine.js

# 12. git 提交
echo "[12/13] git add/commit/push..."
cd "$GIT_LOCAL_PATH"
git add .
DATE=$(date +%Y%m%d%H%M%S)
COMMIT_MSG="${DATE}_${VERSION}_${PLATFORM}_${ENV}"
git commit -m "$COMMIT_MSG"
git push

# 获取最新的commitId
COMMIT_ID=$(git rev-parse HEAD)

# 13. 如果环境为 prod，则打 tag 并推送
echo "[13/13] 检查是否需要打 tag..."
tag_name="tag_${COMMIT_MSG}"
if [ "$ENV" = "prod" ]; then
  git tag "$tag_name"
  git push origin "$tag_name"
  echo "已打 tag: $tag_name 并推送到远程"
fi

echo "发布完成，提交信息：$COMMIT_MSG"
# 只输出commitId，方便其他脚本捕获
echo "$COMMIT_ID"