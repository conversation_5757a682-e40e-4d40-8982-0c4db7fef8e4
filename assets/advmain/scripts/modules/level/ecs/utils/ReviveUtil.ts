import { NativePermissions } from '../../../native/NativePermissions';
import { BoardScene } from '../components/board/BoardScene';
import ReviveComponent from '../components/ReviveComponent';
import TargetComponent from '../components/special/TargetComponent';

/**
 * 复活工具类
 * 提供统一的复活条件检查和状态管理
 */
export class ReviveUtil {
    
    /**
     * 检查是否满足复活的基础条件
     * @param world 世界对象
     * @param config 配置参数
     * @returns 是否可以复活
     */
    static canRevive(world: any, config?: { needLoad?: boolean, percent?: number }): boolean {
        const sceneEntity = world.utilCenter.boardUtil.getSceneEntity();
        if (!sceneEntity) return false;
        
        const reviveComp = world.getComponent(sceneEntity, ReviveComponent);
        if (!reviveComp || reviveComp.nCount <= 0) {
            return false;
        }
        
        // 广告加载检查
        if (config?.needLoad) {
            if (!NativePermissions.getReadyByAdType('reward')) {
                return false;
            }
        }
        
        // 进度要求检查
        if (config?.percent !== undefined) {
            const targetEntities = world.query([TargetComponent]);
            if (targetEntities.length === 0) {
                return true; // 没有目标要求时默认通过
            }
            
            const targets = world.getComponent(targetEntities[0], TargetComponent).targets;
            const total = targets.reduce((acc: number, target: any) => acc + target.to, 0);
            const current = targets.reduce((acc: number, target: any) => acc + target.current, 0);
            const percent = total > 0 ? current / total : 0;
            
            console.log('ReviveUtil: 进度检查', { current, total, percent, required: config.percent });
            
            if (percent < config.percent) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 检查是否已有预览数据
     * @param world 世界对象
     * @returns 是否有预览数据
     */
    static hasPreviewData(world: any): boolean {
        const sceneEntity = world.utilCenter.boardUtil.getSceneEntity();
        if (!sceneEntity) return false;
        
        const reviveComp = world.getComponent(sceneEntity, ReviveComponent);
        return reviveComp && reviveComp.cachedReviveBlocks != null;
    }
    
    /**
     * 获取复活组件
     * @param world 世界对象
     * @returns 复活组件或null
     */
    static getReviveComponent(world: any): ReviveComponent | null {
        const sceneEntity = world.utilCenter.boardUtil.getSceneEntity();
        if (!sceneEntity) return null;
        
        return world.getComponent(sceneEntity, ReviveComponent);
    }
    
    /**
     * 清除预览数据
     * @param world 世界对象
     */
    static clearPreviewData(world: any): void {
        const reviveComp = this.getReviveComponent(world);
        if (reviveComp) {
            reviveComp.cachedReviveBlocks = null;
            reviveComp.isPreviewMode = false;
        }
    }
    
    /**
     * 获取剩余复活次数
     * @param world 世界对象
     * @returns 剩余复活次数
     */
    static getReviveCount(world: any): number {
        const reviveComp = this.getReviveComponent(world);
        return reviveComp ? reviveComp.nCount : 0;
    }
    
    /**
     * 扣除复活次数
     * @param world 世界对象
     * @returns 是否成功扣除
     */
    static consumeReviveCount(world: any): boolean {
        const reviveComp = this.getReviveComponent(world);
        if (reviveComp && reviveComp.nCount > 0) {
            reviveComp.nCount--;
            return true;
        }
        return false;
    }
}
