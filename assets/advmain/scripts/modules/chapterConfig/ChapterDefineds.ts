export const ChapterUrls = {
    /**当前位置动画 */
    curBonePath: 'prefabs/chapterList/ChapterCurSeat',
    /**通关奖杯动画 */
    cupBonePath: 'prefabs/chapterList/ChapterThroughCupBone',
    /**马赛克item背景 */
    commonBgPath: 'textures/chapterList/periods/block/common_Bg',
    /**马赛克item */
    itemPath: 'fonts/item',
    /**顶部item路径 */
    topItemPath: 'prefabs/gameOver/ChapterCollectTopItem',
};

export const ChapterWinBtnPath = {
    next: 'textures/gameOver/over/btn_NextLevel',
    continue: 'textures/gameOver/over/btn_Continue',
    retry: 'textures/gameOver/over/btn_Retry',
};

/**结算item间距 */
export const endTopDistanceArr: number[] = [0, 0, 0, -20, -20, -20, -20];

/**目标类型预设路径，临时写法 */
export const TARGET_ITEM_PF_SRC = { 1: 'prefabs/gameOver/ChapterScoreItem' };
