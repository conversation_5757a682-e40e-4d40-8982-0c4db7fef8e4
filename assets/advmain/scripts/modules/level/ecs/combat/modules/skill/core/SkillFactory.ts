import { SkillBehaviorBase } from "./ISkillBehavior";
import { SimpleActiveBehavior } from "../behavior/SimpleActiveBehavior";
import { IBaseSkillConfig, SkillType } from "../../../../config/SkillConfig";
import { AuraBehavior } from "../behavior/AuraBehavior";
import { PassiveBehavior } from "../behavior/PassiveBehavior";
import { BehaviorFactoryBase } from "../../../core/BehaviorFactoryBase";

/**
 * 技能工厂类
 * 负责创建和管理技能行为实例
 */
export class SkillFactory extends BehaviorFactoryBase {
    private behaviorMap: Map<string, SkillBehaviorBase<IBaseSkillConfig>> = new Map();

    initBehavior(): void {
        this.behaviorMap.set(SkillType.SimpleActive, new SimpleActiveBehavior());
        this.behaviorMap.set(SkillType.Passive, new PassiveBehavior());
        this.behaviorMap.set(SkillType.Aura, new AuraBehavior());
    }

    disposeBehavior(): void {
        this.behaviorMap.clear();
    }

    /**
     * 根据技能ID获取技能行为
     * @param skillId 技能ID
     * @returns 技能行为实例
     */
    getSkillBehaviorById(skillId: string): SkillBehaviorBase<IBaseSkillConfig> | undefined {
        // 通过CombatConfigManager获取配置
        const config = this.world.configCenter.getSkillConfig(skillId);
        if (!config) {
            console.warn(`未找到skillId=${skillId}的配置`);
            return undefined;
        }

        // 根据配置类型获取行为实例
        const behaviorInstance = this.behaviorMap.get(config.skillType);
        if (!behaviorInstance) {
            console.warn(`未找到类型=${config.skillType}的技能行为实例`);
            return undefined;
        }

        // 设置配置到行为实例
        behaviorInstance.config = config;
        return behaviorInstance;
    }
} 