import { ECSViewBase } from '../ECSViewBase';
import { RenderCmdBase } from '../RenderCmdBase';
/**创建ECS视图渲染命令 */
export class CreateECSViewRenderCmd extends RenderCmdBase {
    async do() {
        const view: ECSViewBase = await this.renderWorld.createECSView(this.ecsViewId, this.entityId, this.parentEntityId, this.children);
        if (!view) {
            return;
        }
        view.data = this.data;
    }
}
