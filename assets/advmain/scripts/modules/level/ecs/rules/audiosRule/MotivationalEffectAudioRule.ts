import { AudioId } from '../../config/conf/EcsAudioConfig';
import { ECSEvent } from '../../GameEvent';
import { IEliminationInfo } from '../../GameEventData';
import { ECSRuleBase, IECSRuleConfig } from '../core/ECSRuleBase';
/**
 * 激励语音播放规则配置
 */
export interface IMotivationalEffectAudioRuleConfig extends IECSRuleConfig {
    /** 不同消除数量对应的激励语音ID映射 */
    motivationalMapping: Record<number, AudioId>;
    /** 默认激励语音ID (当消除数量不在映射中时使用) */
    defaultMotivationalId: AudioId;
    /** 最小触发消除数量 */
    minTriggerCount: number;
}
/**
 * 激励语音播放规则
 * 负责根据消除数量播放相应的激励语音
 */
export class MotivationalEffectAudioRule extends ECSRuleBase<IMotivationalEffectAudioRuleConfig, ECSEvent.GameEvent.ELIMINATION> {
    /**
     * 绑定消除事件
     */
    public bindEventData(): ECSEvent.GameEvent.ELIMINATION {
        return ECSEvent.GameEvent.ELIMINATION;
    }
    /**
     * 检查是否可以执行
     */
    canExecute(): boolean {
        console.log('MotivationalEffectAudioRule canExecute');
        // 检查是否有消除事件数据
        const eliminationInfo = this.eventData as IEliminationInfo;
        if (!eliminationInfo || !eliminationInfo.clears || eliminationInfo.clears.length === 0) {
            console.warn('MotivationalEffectAudioRule: 没有消除数据');
            return false;
        }
        // 检查消除数量是否达到最小触发数量
        const eliminationCount = eliminationInfo.clears.length;
        if (eliminationCount < this.config.minTriggerCount) {
            console.log(`MotivationalEffectAudioRule: 消除数量${eliminationCount}未达到最小触发数量${this.config.minTriggerCount}`);
            return false;
        }
        return true;
    }
    /**
     * 执行规则
     */
    execute(): void {
        console.log('MotivationalEffectAudioRule execute - 播放激励语音');
        const world = this.world;
        // 获取消除数据
        const eliminationInfo = this.eventData as IEliminationInfo;
        const eliminationCount = eliminationInfo.clearCol.size + eliminationInfo.clearRow.size;
        // 根据消除数量选择激励语音
        const motivationalAudioId = this.getMotivationalAudioId(eliminationCount);
        if (!motivationalAudioId) {
            console.log(`MotivationalEffectAudioRule: 消除数量${eliminationCount}没有对应的激励语音`);
            return;
        }
        console.log(`MotivationalEffectAudioRule: 消除数量=${eliminationCount}, 使用激励语音=${motivationalAudioId}`);
        // 创建激励语音播放实体
        world.eventBus.emit(ECSEvent.BaseEvent.PLAY_AUDIO, { audioId: motivationalAudioId });
    }
    /**
     * 根据消除数量获取对应的激励语音ID
     * @param count 消除数量
     * @returns 激励语音ID，如果不需要播放则返回null
     */
    private getMotivationalAudioId(count: number): AudioId | null {
        // 检查映射中是否有对应的激励语音
        if (this.config.motivationalMapping[count]) {
            return this.config.motivationalMapping[count];
        }
        // 如果消除数量很高，使用最高级别的激励语音
        const mappedCounts = Object.keys(this.config.motivationalMapping).map(k => parseInt(k)).sort((a, b) => b - a);
        for (const mappedCount of mappedCounts) {
            if (count >= mappedCount) {
                return this.config.motivationalMapping[mappedCount];
            }
        }
        // 如果达到最小触发数量但没有特定映射，使用默认激励语音
        if (count >= this.config.minTriggerCount) {
            return this.config.defaultMotivationalId;
        }
        return null;
    }
} 