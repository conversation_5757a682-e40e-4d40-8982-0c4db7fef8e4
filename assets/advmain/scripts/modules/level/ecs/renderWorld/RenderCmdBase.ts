import {  RenderComponent } from '../components/RenderComponent';
import { RenderWorld } from '../cores/RenderWorld';
import { World } from '../cores/World';
import { RelationName } from '../cores/center/EntityCenter/WorldRelation';
import { RenderCmdConst } from '../registry/RenderCmdRegistry';
import { EcsViewId } from './ECSViewBase';

export interface IRenderCmdConfig {
    /**渲染命令id */
    renderCmdId: string;
    /**渲染命令名称 */
    renderCmdName: RenderCmdConst;
}
export type IRenderCmdData = any;
/**渲染命令基类 */
export abstract class RenderCmdBase<TConfig extends IRenderCmdConfig = IRenderCmdConfig, TData = IRenderCmdData> {
    /**子命令队列 */
    public subCmds: RenderCmdBase<any>[] = [];
    /**是否执行中 */
    public isExecuting: boolean;

    /**实体id */
    public entityId: number;
    /**父实体id */
    public parentEntityId: number;
    /**ecs视图id */
    public ecsViewId: EcsViewId;
    /**子节点路径 */
    public children: string[];
    /**配置 */
    public config: TConfig;
    /**数据 */
    public data: TData;
    /**渲染世界 */
    public renderWorld: RenderWorld;
    /**世界 */
    public world: World;

    initCmd(config: TConfig, entityId: number, renderWorld: RenderWorld, world: World) {
        this.config = config;
        this.entityId = entityId;
        this.renderWorld = renderWorld;
        this.world = world;
        const renderComponent = this.world.getComponent(entityId, RenderComponent);
        if (!renderComponent) {
            console.error(`entityId:${entityId} 不存在渲染组件`);
            return;
        }
        this.data = renderComponent.data;
        this.parentEntityId = world.getSources(entityId,RelationName.PARENT_CHILD)[0];
        this.ecsViewId = renderComponent.ecsViewId;
        this.children = renderComponent.childrenPaths || [];
    }
    abstract do();
    async execute() {
        this.isExecuting = true;
        await this.do();
        this.isExecuting = false;
        this.renderWorld.removeRenderCmd(this);
    }
}
