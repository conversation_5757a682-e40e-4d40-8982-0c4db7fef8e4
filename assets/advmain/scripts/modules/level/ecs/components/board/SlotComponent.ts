import { Component } from '../../cores/Component';
import { RowCol } from '../../define/EcsDefine';

/**格子组件 */
export default class SlotComponent extends Component {
    /**行 */
    public r: number;
    /**列 */
    public c: number;

    constructor(
        /**行或行列 */
        r: number | RowCol,
        /**列 */
        c?: number,
    ) {
        super();
        if (typeof r === 'number') {
            this.r = r;
            this.c = c!;
        } else {
            this.r = r && r.r;
            this.c = r && r.c;
        }
    }
}
