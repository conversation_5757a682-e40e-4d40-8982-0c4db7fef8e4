#!/bin/bash
debug=true
MACRO_DEVTOOLS=false
PLATFORM="web"
ENV="test"
MACRO_VERSION="1.2.3"
MACRO_AUTO=false
MACRO_ROBOT=false
MACRO_TRAITS_SPLIT=false
MACRO_HOTUPDATE_OPEN=false
MACRO_REMOTE_SERVER_RES_URL="http://192.168.83.169:8867"
MACRO_SHOW_FPS_LOG=false

# 删除 cocos creator 缓存
rm -rf /Applications/Cocos/Creator/2.4.11/CocosCreator.app/Contents/Resources/engine/bin/.cache/web-mobile

/Applications/Cocos/Creator/2.4.11/CocosCreator.app/Contents/MacOS/CocosCreator \
  --path ./ \
  --build "platform=web-mobile;debug=$debug;" \
  --MACRO_MACRO_DEVTOOLS ${MACRO_DEVTOOLS} \
  --MACRO_PLATFORM ${PLATFORM} \
  --MACRO_ENV ${ENV} \
  --MACRO_VERSION ${MACRO_VERSION} \
  --MACRO_AUTO ${MACRO_AUTO} \
  --MACRO_ROBOT ${MACRO_ROBOT} \
  --MACRO_TRAITS_SPLIT ${MACRO_TRAITS_SPLIT} \
  --MACRO_HOTUPDATE_OPEN ${MACRO_HOTUPDATE_OPEN} \
  --MACRO_REMOTE_SERVER_RES_URL ${MACRO_REMOTE_SERVER_RES_URL} \
  --MACRO_SHOW_FPS_LOG ${MACRO_SHOW_FPS_LOG} 

BUILD_OUTPUT_DIR="./build"

cp ./build-templates/jsb-link/main.refactored.js ${BUILD_OUTPUT_DIR}/web-mobile/main.js

  # 重命名 assets 目录，兼容当前模式与融合模式
JSB_LINK=${BUILD_OUTPUT_DIR}/web-mobile
[ -d ${JSB_LINK}/assets/internal ] && mv ${JSB_LINK}/assets/internal ${JSB_LINK}/assets/refactoredInternal
[ -d ${JSB_LINK}/assets/main ] && mv ${JSB_LINK}/assets/main ${JSB_LINK}/assets/refactoredMain
[ -d ${JSB_LINK}/assets/resources ] && mv ${JSB_LINK}/assets/resources ${JSB_LINK}/assets/refactoredResources

/usr/local/bin/node ./scripts/hotUpdate/version_generator.js ${MACRO_REMOTE_SERVER_RES_URL} ${PLATFORM} ${ENV} ${MACRO_VERSION} true ./build/web-mobile