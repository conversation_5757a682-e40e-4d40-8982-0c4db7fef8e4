import Util from '../../../../../base/Util';
import { Expand } from '../../define/EcsDefine';
import { TempleteType, TempleteRegistry, TempleteArgsMap } from '../../registry/templete/TempleteRegistry';
import { LogicWorld } from '../World';
/**
 * 模板基类
 */
export abstract class TempleteBase<MustArgs = {}, OptionalArgs = {}> {
    protected world: LogicWorld;

    constructor(world: LogicWorld) {
        this.world = world;
    }

    /**
     * 获取默认的可选参数
     * 子类可以重写此方法来提供默认值
     */
    abstract getDefaultOptionalArgs(mustArgs: MustArgs): OptionalArgs;

    /**创建模板
     * @param mustArgs 必填参数对象
     * @param optionalArgs 可选参数对象
     * @returns 创建的实体ID
     */
    abstract createTemplete(mustArgs: MustArgs, optionalArgs: OptionalArgs): number;
}

/**
 * 通用模板中心
 * 负责管理所有的模板注册表
 * 支持通过模板类型动态创建并缓存模板实例
 */
export class TempleteCenter {
    /**模板注册表映射 - 存储每种模板类型的注册表 */
    private templeteClzzRegistry: Record<string, clzz<TempleteBase>>;

    /**模板实例缓存映射 - 存储已创建的模板实例 */
    private templeteInstanceCacheMap: Record<TempleteType, TempleteBase>;

    /**逻辑世界 */
    private world: LogicWorld;

    /**
     * 初始化模板中心
     */
    constructor(world: LogicWorld) {
        this.world = world;

        this.templeteClzzRegistry = TempleteRegistry;
        this.templeteInstanceCacheMap = {} as Record<TempleteType, TempleteBase>;
    }
    /**
     * 根据模板类型获取或创建模板实例
     * 如果实例已存在则返回缓存的实例，否则创建新实例并缓存
     * @param templeteType 模板类型
     * @returns 模板实例
     */
    getTemplete<T extends TempleteType>(templeteType: T): TempleteBase | undefined {
        // 首先检查缓存中是否已有实例
        let templeteInstance = this.templeteInstanceCacheMap[templeteType];

        if (!templeteInstance) {
            // 缓存中没有，从注册表获取模板类并创建实例
            const templeteClzz = this.templeteClzzRegistry[templeteType];
            if (!templeteClzz) {
                console.warn(`未找到模板类型=${templeteType}的注册表`);
                return undefined;
            }

            // 创建新的模板实例
            templeteInstance = new templeteClzz(this.world);

            // 缓存新创建的实例
            this.templeteInstanceCacheMap[templeteType] = templeteInstance;
        }

        return templeteInstance;
    }

    /**
     * 创建模板实体（类型安全版本）
     * 根据模板类型自动映射参数类型
     * @param templeteType 模板类型
     * @param mustArgs 必填参数对象
     * @param optionalArgs 可选参数对象
     * @returns 创建的实体ID
     */
    createTempleteEntity<T extends keyof TempleteArgsMap>(
        templeteType: T,
        mustArgs: Expand<TempleteArgsMap[T]['mustArgs']>,
        optionalArgs: Partial<Expand<TempleteArgsMap[T]['optionalArgs']>> = {},
    ): number {
        const templete = this.getTemplete(templeteType);
        if (!templete) {
            console.error(`未找到模板类型=${templeteType}的模板实例`);
            return -1;
        }
        const defaults = templete.getDefaultOptionalArgs(mustArgs);
        const mergedOptionalArgs = Util.deepMerge(defaults, optionalArgs);
        return templete.createTemplete(mustArgs, mergedOptionalArgs);
    }
}
