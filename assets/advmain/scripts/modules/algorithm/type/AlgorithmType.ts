
/**
 * 游戏算法大的分层 检测用 2000+
 */
export enum OFFER_TYPE_BASE {
  // 空算法
  NONE = -1,
  // 随机
  SUI_JI = 0,
  // 熵减
  SHANG_JIAN = 5,
  // 随机无死亡
  SUI_JI_WU_SI = 7,

  // 清屏
  CLEAR_BOARD = 2000,
  // 新手随机
  SUI_JI_GUIDE = 2001,
  // 复活
  REVIVE = 2002,


  // 平替算法 ContinueSameMoreRoundLimitTrait
  REPLACE_ROUNDLIMIT = 2100,

  // 不可消除异形块（特性）-- 45600001
  IRREVERSIBLE_ALIEN = 2201,
  // 终止刷分循环算法（特性）-- *********
  CLASSTERMINATE_CYCLE = 2202,
  // 终止刷分循环算法（特性）-- *********
  CHAPTERTERMINATE_CYCLE = 2203,
  // 消除爽
  ELIMINTE_PLEASURE = 2400,
}

// 熵增 检测用 3000+
export enum OFFER_TYPE_SHANG {
  // 熵增1
  SHANG_ZENG_1 = 1,
  // 熵增3
  SHANG_ZENG_3 = 2,
  // 字母熵增
  ZIMU_SHANGZENG = 3000,
}

// 填空 检测用  1000+
export enum OFFER_TYPE_BLANK {
  // 填空消除
  TIAN_KONG_XIAO_CHU = 4,

  // 旅行填空消除
  TRAVEL_TIAN_KONG_XIAO_CHU = 1000,
  // 高效消除
  EFFICIENT_ELIMINATE = 1002,
  // 基于空洞数变化的填空消除方案
  EMPTYDONGFILL = 1003,

  // 全组合填空 id9
  ALL_COMBINATION_ID9 = 1309,
  // 全组合填空 id21
  ALL_COMBINATION_ID21 = 1321,
  // 全组合填空 id70
  ALL_COMBINATION_ID70 = 1370,

}

// 困难难题 检测用  4000+
export enum OFFER_TYPE_DIFFICULTY {
  // 困难难题 死亡难题
  KUN_NAN_TI = 6,
  // 直觉难题
  ZHI_JUE_NAN_TI = 8,
  // 简单直觉难题
  SIMPLE_ZHIJUE = 4000,
  // 更难的困难难题
  ALGORITHMENTROPY = 4001,
  // 十字解困难难题
  ALGORITHMENTROPYMORECLEAR = 4002,
  // 难题扩展1
  ALGODIFFSPREAD1 = 4003,
  // 难题扩展2
  ALGODIFFSPREAD2 = 4004,
  // 高回报难题
  DIFFICULTY_GAOHUIBAO = 4005,
}

//死亡难题 检测用  5000+
export enum OFFER_TYPE_DIE {
  // 死亡 简单死亡
  SI_WANG = 3,
  // 致死题
  ZHI_SI_TI = 9,
}

// 对外使用 总表
export enum OFFER_TYPE {
  NONE = OFFER_TYPE_BASE.NONE,                                // -1
  SUI_JI = OFFER_TYPE_BASE.SUI_JI,                            // 0
  SHANG_ZENG_1 = OFFER_TYPE_SHANG.SHANG_ZENG_1,               // 1
  SHANG_ZENG_3 = OFFER_TYPE_SHANG.SHANG_ZENG_3,               // 2
  SI_WANG = OFFER_TYPE_DIE.SI_WANG,                           // 3
  TIAN_KONG_XIAO_CHU = OFFER_TYPE_BLANK.TIAN_KONG_XIAO_CHU,   // 4
  SHANG_JIAN = OFFER_TYPE_BASE.SHANG_JIAN,                    // 5
  KUN_NAN_TI = OFFER_TYPE_DIFFICULTY.KUN_NAN_TI,              // 6
  SUI_JI_WU_SI = OFFER_TYPE_BASE.SUI_JI_WU_SI,                // 7
  ZHI_JUE_NAN_TI = OFFER_TYPE_DIFFICULTY.ZHI_JUE_NAN_TI,      // 8
  ZHI_SI_TI = OFFER_TYPE_DIE.ZHI_SI_TI,                       // 9

  CLEAR_BOARD = OFFER_TYPE_BASE.CLEAR_BOARD,               // 2000
  SUI_JI_GUIDE = OFFER_TYPE_BASE.SUI_JI_GUIDE,             // 2001
  REVIVE = OFFER_TYPE_BASE.REVIVE,                         // 2002

  REPLACE_ROUNDLIMIT = OFFER_TYPE_BASE.REPLACE_ROUNDLIMIT,   // 2100
  IRREVERSIBLE_ALIEN = OFFER_TYPE_BASE.IRREVERSIBLE_ALIEN,   // 2201
  CLASSTERMINATE_CYCLE = OFFER_TYPE_BASE.CLASSTERMINATE_CYCLE,  // 2202
  CHAPTERTERMINATE_CYCLE = OFFER_TYPE_BASE.CHAPTERTERMINATE_CYCLE,  // 2203
  ELIMINTE_PLEASURE = OFFER_TYPE_BASE.ELIMINTE_PLEASURE,        // 2400

  SIMPLE_ZHIJUE = OFFER_TYPE_DIFFICULTY.SIMPLE_ZHIJUE,          // 4000
  ALGORITHMENTROPY = OFFER_TYPE_DIFFICULTY.ALGORITHMENTROPY,    // 4001
  ALGORITHMENTROPYMORECLEAR = OFFER_TYPE_DIFFICULTY.ALGORITHMENTROPYMORECLEAR, // 4002
  ALGODIFFSPREAD1 = OFFER_TYPE_DIFFICULTY.ALGODIFFSPREAD1,      // 4003
  ALGODIFFSPREAD2 = OFFER_TYPE_DIFFICULTY.ALGODIFFSPREAD2,      // 4004
  DIFFICULTY_GAOHUIBAO = OFFER_TYPE_DIFFICULTY.DIFFICULTY_GAOHUIBAO,      // 4005

  ZIMU_SHANGZENG = OFFER_TYPE_SHANG.ZIMU_SHANGZENG,      // 3000

  TRAVEL_TIAN_KONG_XIAO_CHU = OFFER_TYPE_BLANK.TRAVEL_TIAN_KONG_XIAO_CHU, // 1000
  EFFICIENT_ELIMINATE = OFFER_TYPE_BLANK.EFFICIENT_ELIMINATE,  // 1002
  EMPTYDONGFILL = OFFER_TYPE_BLANK.EMPTYDONGFILL,  // 1003
  ALL_COMBINATION_ID9 = OFFER_TYPE_BLANK.ALL_COMBINATION_ID9,    // 1309
  ALL_COMBINATION_ID21 = OFFER_TYPE_BLANK.ALL_COMBINATION_ID21,  // 1321
  ALL_COMBINATION_ID70 = OFFER_TYPE_BLANK.ALL_COMBINATION_ID70,  // 1370

}

//当前出题状态
export enum algorithmSource {
  //
  NONE = 0,
  // 正常出题
  NORMAL = 1,
  // 降级出题
  FAIL = 2,
  // 成功替换出题
  SUCCESS = 3,
  // 后续出题
  FOLLOWUPONE = 4,
  FOLLOWUPTWO,
  // 兜底出题
  FALLBACK,
  // 平替出题
  REPLACE,
}

// 成功列表index
export enum algorithmSuccessIndex {
  // 替换整个困难难题系列
  DIFFICULTY = 1,
}



