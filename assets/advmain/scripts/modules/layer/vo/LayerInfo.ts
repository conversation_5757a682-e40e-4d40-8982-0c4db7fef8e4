import { LayerType } from "../type/LayerType";

/** 业务层级配置 */
const BUSINESS_LAYER_CONFIGS: falcon.ILayerConfig[] = [
    { type: LayerType.PRELOAD, name: 'preloadLayer', zIndex: 0, opacity: 0 },
    { type: LayerType.SCENE, name: 'sceneLayer', zIndex: 1 },
    { type: LayerType.GAME_EFFECT, name: 'gameEffectLayer', zIndex: 3 },
    { type: LayerType.GUIDE, name: 'guideLayer', zIndex: 4 },
    { type: LayerType.LOADING, name: 'loadingLayer', zIndex: 5 },
    { type: LayerType.UI, name: 'uiLayer', zIndex: 6 },
    { type: LayerType.GAME_ALERT, name: 'gameAlertLayer', zIndex: 7 },
    { type: LayerType.ALERT, name: 'alertLayer', zIndex: 8 },
    { type: LayerType.EFFECT, name: 'effectLayer', zIndex: 9 },
    { type: LayerType.TIP, name: 'tipLayer', zIndex: 10 }
];

/** 预加载 */
export const preloadLayer: cc.Node = new cc.Node();

/** 场景 */
export const sceneLayer: cc.Node = new cc.Node();

/** 通用游戏效果 */
export const gameEffectLayer: cc.Node = new cc.Node();

/** 新手引导 */
export const guideLayer: cc.Node = new cc.Node();

/** loading */
export const loadingLayer: cc.Node = new cc.Node();

/** 所有的通用ui图层都放在这个图层中*/
export const uiLayer: cc.Node = new cc.Node();

/** 游戏弹窗 */
export const gameAlertLayer: cc.Node = new cc.Node();

/** 游戏弹窗 */
export const alertLayer: cc.Node = new cc.Node();

/** effectLayer */
export const effectLayer: cc.Node = new cc.Node();

/** tipLayer */
export const tipLayer: cc.Node = new cc.Node();

// 预定义所有层级节点的配置
const layerNodes = {
    [LayerType.PRELOAD]: preloadLayer,
    [LayerType.SCENE]: sceneLayer,
    [LayerType.GAME_EFFECT]: gameEffectLayer,
    [LayerType.GUIDE]: guideLayer,
    [LayerType.LOADING]: loadingLayer,
    [LayerType.UI]: uiLayer,
    [LayerType.GAME_ALERT]: gameAlertLayer,
    [LayerType.ALERT]: alertLayer,
    [LayerType.EFFECT]: effectLayer,
    [LayerType.TIP]: tipLayer,
};

/**
 * 初始化游戏层级
 * @param configs 自定义层级配置
 */
export function initGameLayer(configs: falcon.ILayerConfig[] = BUSINESS_LAYER_CONFIGS) {
    falcon.layerManager.init();

    // 使用预定义的层级节点进行初始化
    configs.forEach(config => {
        const node = layerNodes[config.type] || new cc.Node();
        falcon.layer.initNodeConfig(node, config, falcon.layerManager.layerScene);
        falcon.layerManager.setLayerNode(config.type, node);
    });
}


