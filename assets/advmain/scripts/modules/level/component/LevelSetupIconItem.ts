import { SubGameBridge } from '../../../base/SubGameBridge';
import { ISetupBaseItem, SetupConfig } from '../config/LevelSetupConfig';
import { E_Levels_SetupClick } from '../events/E_Levels_SetupClick';

const { ccclass, property } = cc._decorator;
/**
 * 设置界面顶部元素
 */
@ccclass
export class LevelSetupIconItem extends falcon.Component<ISetupBaseItem> {
    /**按钮 */
    @property(cc.Button)
    btn: cc.Button = null;

    /**文本图 */
    @property(cc.Sprite)
    txt_img: cc.Sprite = null;

    /**禁用图 */
    @property(cc.Sprite)
    off_img: cc.Sprite = null;

    shouldComponentUpdate(nextState: Readonly<ISetupBaseItem>): boolean {
        return this.state.value !== nextState.value;
    }

    render() {
        /**顶部资源前缀*/
        const topBtnPre: string = this.state.topBtnPre || SetupConfig.topBtnPre;
        /**文本资源前缀*/
        const txtImgPre: string = this.state.txtImgPre || SetupConfig.txtImgPre;
        const data: { path: string; sp: cc.Sprite }[] = [
            { path: SetupConfig.resUrl + txtImgPre + this.state.key, sp: this.txt_img },
            { path: SetupConfig.resUrl + topBtnPre + this.state.key, sp: this.btn.getComponent(cc.Sprite) },
        ];
        for (let i = 0; i < data.length; i++) {
            // 加载显示资源
            SubGameBridge.loadByBundle('advres', data[i].path, cc.SpriteFrame, (err, sp) => {
                if (err) {
                    console.error(err);
                    return;
                }
                if (data[i].sp) {
                    data[i].sp.spriteFrame = sp as cc.SpriteFrame;
                }
            });
        }
        // 开关状态
        this.off_img.node.opacity = this.state.value ? 0 : 255;
    }

    @falcon.throttle(300)
    onClick(event: cc.Event.EventTouch, data: any) {
        if (CC_DEBUG) {
            console.log(`点击按钮`);
        }
        falcon.EventManager.dispatchModuleEvent(new E_Levels_SetupClick(this.state.key));
    }
}
