import { TempleteBase } from '../cores/center/TempleteCenter';
import DragComponent from '../components/DragComponent';
import { UITransformComponent } from '../components/UITransformComponent';
import ShapeComponent from '../components/board/ShapeComponent';
import NodeComponent from '../components/NodeComponent';
import { RenderComponent } from '../components/RenderComponent';
import { RelationName } from '../cores/center/EntityCenter/WorldRelation';
import { CellSize } from '../define/BoardDefine';
import { RcShape } from '../define/EcsConfig';

/**
 * 形状模板必传参数
 */
export interface ShapeTempleteMustArgs {
    /** 形状行列坐标 */
    rcShape: (typeof RcShape)[number];
    /** X坐标 */
    x: number;
    /** 父实体ID */
    parentEntity: number;
}

/**
 * 形状模板可选参数
 */
export interface ShapeTempleteOptionalArgs {
    /** 方块尺寸 */
    cellSize: number;
}

/**
 * 形状模板
 */
export class ShapeTemplete extends TempleteBase<ShapeTempleteMustArgs, ShapeTempleteOptionalArgs> {
    getDefaultOptionalArgs(mustArgs: ShapeTempleteMustArgs): ShapeTempleteOptionalArgs {
        return {
            cellSize: CellSize,
        };
    }
    createTemplete(mustArgs: ShapeTempleteMustArgs, optionalArgs: ShapeTempleteOptionalArgs): number {
        const { rcShape, x, parentEntity } = mustArgs;
        const { cellSize } = optionalArgs;
        const world = this.world;
        const shapeEntity = world.createEntity();
        world.addRelation(parentEntity, shapeEntity, RelationName.PARENT_CHILD);
        world.addComponent(shapeEntity, ShapeComponent, rcShape);
        const baseScale = 0.5 - (rcShape.width > 5 ? rcShape.width * 0.1 : 0);
        const ratio = cellSize / CellSize;
        world.addComponent(shapeEntity, NodeComponent, {
            x,
            y: -600,
            scale: baseScale * ratio,
        });
        world.addComponent(shapeEntity, DragComponent, { x: 0, y: 0, width: 600, height: 1200 });
        world.addComponent(shapeEntity, UITransformComponent, 600, 1200);
        world.addComponent(shapeEntity, RenderComponent, 'ecsview_shape');

        return shapeEntity;
    }
}
