import ProduceBlockListComponent from '../../../components/ProduceBlockListComponent';
import TargetComponent from '../../../components/special/TargetComponent';
import { CellType, TargetType } from '../../../define/BoardDefine';
import { RcShape } from '../../../define/EcsConfig';
import { ECSEvent } from '../../../GameEvent';
import { ECSRuleBase, IECSRuleConfig } from '../../core/ECSRuleBase';

/**
 * 生成块收集物规则
 */
export class ProduceBlockCollectibleRule extends ECSRuleBase<IECSRuleConfig, ECSEvent.GameEvent.PRODUCE_BLOCK_END> {
    public bindEventData() {
        return ECSEvent.GameEvent.PRODUCE_BLOCK_END;
    }
    canExecute(): boolean {
        const produceBlockLists = this.world.query([ProduceBlockListComponent]);
        if (!produceBlockLists || produceBlockLists.length === 0) {
            console.warn('没有找到 ProduceBlockListComponent 实体，无法执行目标类型规则');
            return false;
        }
        return true;
    }
    execute(): void {
        const produceBlockListEntity = this.world.query([ProduceBlockListComponent])[0];
        const produceBlocks = this.world.getComponent(produceBlockListEntity, ProduceBlockListComponent).produceBlockList;
        const collectTypes = this.getTargetTypes();
        produceBlocks.forEach((block) => {
            block.shape.shape.forEach((rc, index) => {
                // 随机选择一个类型
                const type = this.getCollectibleType(collectTypes);
                if (!block.shape.types) {
                    block.shape.types = [];
                }
                block.shape.types[index] = type;
            });
        });
    }

    private getCollectibleType(collectTypes: CellType[]): TargetType {
        //40% 概率生成收集块
        if (collectTypes.length > 0 && Math.random() < 0.4) {
            let type = collectTypes[Math.floor(Math.random() * collectTypes.length)];
            //若是钻石，90%概率换成普通块
            if (type === TargetType.Diamond && Math.random() < 0.9) type = TargetType.Normal;
            return type;
        }
        //10%概率生成飞机块
        if (Math.random() < 0.1) {
            return TargetType.Plane;
        }
        //默认返回普通块类型
        return TargetType.Normal;
    }

    private getTargetTypes(): CellType[] {
        const world = this.world;
        const cellUtil = world.utilCenter.cellUtil;
        const collectTypes = world
            .getComponent(world.query([TargetComponent])[0], TargetComponent)
            .targets.filter((t) => cellUtil.isCell(t.key))
            .map((t) => t.key as CellType);
        return collectTypes;
    }
}
