import { Audio_Module } from './audio/Audio_Module';
import { ChapterConfig_Module } from './chapterConfig/ChapterConfig_Module';
import { Dot_Module } from './dot/Dot_Module';
import { ExternalData_Module } from './externalData/ExternalData_Module';
import { Game_Module } from './game/Game_Module';
import { HotUpdate_Module } from './hotUpdate/HotUpdate_Module';
import { Launch_Module } from './launch/Launch_Module';
import { Level_Module } from './level/Level_Module';
import { Preload_Module } from './preload/Preload_Module';
import { TraitConfig_Module } from './traitConfig/TraitConfig_Module';
import { Algorithm_Module } from './algorithm/Algorithm_Module';

export class ModuleList {
    //静态方法，启动模块
    static start() {
        //注册模块
        falcon.ModuleManager.resigerModule([
            HotUpdate_Module, //热更新
            Game_Module, //游戏核心模块
            Preload_Module, //预加载
            TraitConfig_Module, //特性配置
            Dot_Module, //打点
            Launch_Module, //启动模块
            Audio_Module, //音频
            ExternalData_Module, //外部数据
            ChapterConfig_Module, //关卡配置
            Level_Module, //关卡
            Algorithm_Module, //算法
        ]);
        //启动模块
        falcon.ModuleManager.startModule('common');
    }
}
