import { AudioId } from '../../config/conf/EcsAudioConfig';
import { ECSEvent } from '../../GameEvent';
import { ECSRuleBase, IECSRuleConfig } from '../core/ECSRuleBase';
/**
 * 放置音效播放规则配置
 */
export interface IPutEffectAudioRuleConfig extends IECSRuleConfig {
    audioId: AudioId;
}
/**
 * 放置音效播放规则
 * 负责创建和管理播放音效的ECS视图
 */
export class PutEffectAudioRule extends ECSRuleBase<IPutEffectAudioRuleConfig> {
    /**
     * 检查是否可以执行
     */
    canExecute(): boolean {
        console.log('PutEffectAudioRule canExecute');
        return true;
    }
    /**
     * 执行规则
     */
    execute(): void {
        this.world.eventBus.emit(ECSEvent.BaseEvent.PLAY_AUDIO, { audioId: this.config.audioId });
    }
} 