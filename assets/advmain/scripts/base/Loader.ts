import { SubGameBridge } from "./SubGameBridge";

type BundleNameType = falcon.BundleNameType;

export class Loader {
    /**
     * 封装的异步加载资源方法（通过bundle）
     * @param bundleName bundle名称
     * @param url 资源路径
     * @param type 资源类型（可选）
     * @param options bundle选项（包含MD5版本等）
     * @returns Promise<T>
     */
    public static asyncLoadByBundle<T extends cc.Asset = cc.Asset>(bundleNameType: BundleNameType, url: string, type?: typeof cc.Asset): Promise<T> {
        // console.log(`asyncLoadByBundle bundleNameType = ${bundleNameType} url = ${url}`);
        if (bundleNameType == '') {
            console.log(`asyncLoadByBundle url = ${url} bundleNameType == '' need to check`);
        }
        const bundleConfig = SubGameBridge.getBundleConfig(bundleNameType);
        if (type) {
            return falcon.ResLoader.asyncLoadByBundle<T>(bundleConfig, url, type);
        }
        return falcon.ResLoader.asyncLoadByBundle<T>(bundleConfig, url, undefined);
    }

    public static async asyncLoad<T extends cc.Asset>(paths: string, type: typeof cc.Asset): Promise<T> {
        console.error(`SubGameBridge.asyncLoad: bundleName is not specified for file '${paths}'. Please verify the file path and use asyncLoadByBundle instead.`);
        return new Promise<T>((resolve, reject) => {
            reject();
        });
    }

    /**
     * 封装的同步加载资源方法（通过bundle）
     * @param bundleName bundle名称
     * @param url 资源路径
     * @param type 资源类型
     * @param callback 回调函数
     * @param options bundle选项（包含MD5版本等）
     */
    public static loadByBundle<T extends cc.Asset = cc.Asset>(
        bundleName: BundleNameType,
        url: string,
        type: typeof cc.Asset,
        callback: (err: Error, resource: T) => void,
    ): void {
        // console.log(`loadByBundle bundleNameType = ${bundleName} url = ${url}`);
        if (bundleName == '') {
            console.log(`loadByBundle bundleNameType == '' need to check`);
        }
        const bundleConfig = SubGameBridge.getBundleConfig(bundleName);
        falcon.ResLoader.loadByBundle(bundleConfig, url, type, (err: Error, asset: cc.Asset) => {
            callback(err, asset as T);
        });
    }

    /**
     * 封装的加载bundle方法
     * @param bundleName bundle名称
     * @param optionsOrCallback 选项或回调函数
     * @param callback 回调函数（当第二个参数是options时使用）
     */
    public static loadBundle(bundleName: BundleNameType, callback?: (err: Error, bundle: cc.AssetManager.Bundle) => void): void {
        const bundleConfig = SubGameBridge.getBundleConfig(bundleName);
        falcon.ResLoader.loadBundle(bundleConfig, callback);
    }

    /** 
     * 封装的渲染sprite方法，自动处理bundleName的远程地址和版本信息
     * @param sprite sprite组件
     * @param url 资源路径
     * @param bundleNameType bundle名称
     */
    static renderSpriteByBundle(sprite: cc.Sprite, url: string, bundleNameType: BundleNameType) {
        falcon.ResLoader.renderSpriteByBundle(sprite, url, bundleNameType);
    }

    /**
     * 封装的bundle预加载方法
     * @param bundle bundle对象
     * @param url 资源路径
     * @param type 资源类型
     * @param callback 回调函数
     */
    public static bundlePreload(bundle: cc.AssetManager.Bundle, url: string, type: typeof cc.Asset, callback: (err: Error) => void): void {
        falcon.ResLoader.bundlePreload(bundle, url, type, callback);
    }

    /**
     * 封装的UI显示方法，自动处理bundleName的远程地址
     * @param config 预制体配置对象
     * @param parent 父节点（可选）
     * @returns Promise<cc.Node>
     */
    public static async showUI(config: any, parent?: cc.Node): Promise<cc.Node> {
        const bundleConfig = SubGameBridge.getBundleConfig(config.bundleName);
        const processedConfig = {
            ...config,
        };
        if (bundleConfig.version) {
            processedConfig.version = bundleConfig.version;
        }
        if (bundleConfig.bundleName) {
            processedConfig.bundleName = bundleConfig.bundleName;
        }
        return falcon.UI.show(processedConfig, parent);
    }

    /**
     * 封装的龙骨动画渲染方法，自动处理bundleName的远程地址和版本信息
     * @param bundleName bundle名称
     * @param dragonBonesArmatureDisplay 龙骨显示组件
     * @param dragonAssetUrl 龙骨资源URL
     * @param dragonAssetAtlasUrl 龙骨图集URL
     * @param armatureName 骨架名称
     * @param animName 动画名称
     * @param playtimes 播放次数
     * @param completeRemove 完成后是否移除
     * @param options bundle选项（包含MD5版本等）
     */
    public static renderDragonbonesByBundle(
        bundleName: BundleNameType,
        dragonBonesArmatureDisplay: dragonBones.ArmatureDisplay,
        dragonAssetUrl: string,
        dragonAssetAtlasUrl: string,
        armatureName: string,
        animName: string,
        playtimes: number = 1,
        completeRemove: boolean = true,
    ): void {
        const bundleConfig = SubGameBridge.getBundleConfig(bundleName);
        falcon.ResLoader.renderDragonbonesByBundle(bundleConfig, dragonBonesArmatureDisplay, dragonAssetUrl, dragonAssetAtlasUrl, armatureName, animName, playtimes, completeRemove);
    }
}