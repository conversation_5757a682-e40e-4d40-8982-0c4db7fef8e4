import { ECSRuleBase, IECSRuleConfig } from './core/ECSRuleBase';
import { TempleteType } from '../registry/templete/TempleteRegistry';
import BoardComponent from '../components/board/BoardComponent';
export interface IBoardRefurbishEffectRuleConfig extends IECSRuleConfig {
    /** 视图id */
    ecsViewId
}
/**播放棋盘全屏特效 */
export class BoardRefurbishEffectRule extends ECSRuleBase<IBoardRefurbishEffectRuleConfig> {
    canExecute(): boolean {
        return true;
    }
    execute(): void {
        const world = this.world;
        const allBoardEntities = this.world.query([BoardComponent]);
        for (const boardEntityId of allBoardEntities) {
            const boardCom = world.getComponent(boardEntityId, BoardComponent);
            if (boardCom && boardCom.isShowFullEffect) {
                const boardOccupy = world.handlerCenter.boardHandler.getBoardOccupy(boardEntityId);
                const boardEffectEntity = world.templeteCenter.createTempleteEntity(
                    TempleteType.Render,
                    {
                        parentEntity: world.ECSLayerType.EffectLayerEntity,
                        ecsViewId: this.config.ecsViewId,
                    },
                    {
                        renderParam: { 
                            isBreakLink: true, 
                            data: {
                                boardEntityId: boardEntityId,
                                boardOccupy: boardOccupy,
                                boardConfig: [
                                    [4, 4, 4, 6, 6, 3, 3, 3],
                                    [6, 6, 4, 6, 6, 3, 5, 5],
                                    [6, 2, 2, 7, 7, 4, 4, 5],
                                    [2, 3, 3, 7, 4, 1, 1, 4],
                                    [2, 3, 3, 2, 4, 1, 1, 4],
                                    [5, 2, 2, 2, 3, 4, 4, 6],
                                    [5, 1, 1, 1, 3, 7, 7, 6],
                                    [5, 5, 5, 1, 3, 3, 7, 7]
                                ]
                            },
                        },
                    },
                );
                world.destroyEntity(boardEffectEntity);
            }
        }
    }
}
