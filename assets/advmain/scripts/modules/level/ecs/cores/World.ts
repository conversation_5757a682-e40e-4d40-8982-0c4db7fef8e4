import { ConfigCenter } from './center/ConfigCenter';
import { Component } from './Component';
import { EventBus, LogicEventBus, ReadonlyLogicEventBus } from './EventBus';
import { UtilCenter } from './center/UtilCenter';
import { RuleFlowCenter } from './center/RuleFlowCenter';
import { IEventCmdData } from '../cmd/EventCmd';
import { BehaviorCenter } from './center/BehaviorCenter';
import { TempleteCenter } from './center/TempleteCenter';
import { SystemCenter } from './center/SystemCenter';
import { ISerializable } from './ISerializable';
import { CacheCenter } from './center/CacheCenter';
import { ConfigRegistry } from '../registry/ConfigRegistry';
import { SnapshotCenter } from './center/SnapshotCenter';
import { InputCmdCenter } from './center/InputCmdCenter';
import { EntityCenter } from './center/EntityCenter/EntityCenter';
import { RelationName } from './center/EntityCenter/WorldRelation';
import { ComponentChangeListener } from './center/EntityCenter/ComponentProxy';
import { ECSEvent } from '../GameEvent';

/**
 * ECS世界类
 * 负责管理实体、组件、系统的核心逻辑
 */
export class World implements ISerializable {
    // ==================== 公共属性 ====================
    /**是否独立运行世界(单独运行,不会同步到其他世界) */
    public readonly isLocalWorld: boolean;
    /** 世界帧数 */
    public readonly frameCount = 0;
    /** 世界总时间 */
    public readonly worldTime = 0;
    /** 世界本次运行时间 */
    public readonly worldRunTime = 0;
    /** 事件总线 */
    public readonly eventBus: LogicEventBus;
    /** ECS层级类型 */
    public ECSLayerType = {
        /**场景层 */
        SceneLayerEntity: -1,
        /**特效层 */
        EffectLayerEntity: -1,
        /**UI层 */
        UILayerEntity: -1,
    };;
    /** 实体中心 */
    public entityCenter: EntityCenter;

    /** 系统中心 */
    public systemCenter: SystemCenter;

    /**缓存中心 */
    public cacheCenter: CacheCenter;

    /** 工具函数中心 */
    public utilCenter: UtilCenter;

    /**行为中心 */
    public behaviorCenter: BehaviorCenter;

    /**模板中心 */
    public templeteCenter: TempleteCenter;

    /** 配置中心 */
    public configCenter: ConfigCenter;

    /** 规则中心 */
    public ruleCenter: RuleFlowCenter;

    /** 快照中心 */
    public snapshotCenter: SnapshotCenter;

    /** 输入命令中心 */
    public inputCmdCenter: InputCmdCenter;

    // ==================== 生命周期方法 ====================

    /**
     * 初始化世界
     */
    constructor(gameConfig: ConfigRegistry,isLocalWorld: boolean = false) {
        this.isLocalWorld = isLocalWorld;
        this.eventBus = new EventBus() as LogicEventBus;
        this.configCenter = new ConfigCenter(gameConfig);
        this.utilCenter = new UtilCenter(this);
        this.behaviorCenter = new BehaviorCenter(this);
        this.templeteCenter = new TempleteCenter(this);
        this.cacheCenter = new CacheCenter();
        this.snapshotCenter = new SnapshotCenter(this, this.configCenter.levelConfig.levelId);
        this.ruleCenter = new RuleFlowCenter(this.configCenter);
        this.inputCmdCenter = new InputCmdCenter(this);
        this.entityCenter = new EntityCenter();
        this.systemCenter = new SystemCenter(this);
        this.eventBus.emit(ECSEvent.BaseEvent.INIT_RULE_FLOW, {
            ruleFlowConfig: this.configCenter.levelConfig.ruleFlowConfigForEvent,
        });
    }

    /**
     * 更新世界
     * @param dt 帧间隔时间
     */
    update(dt: number): void {
        (this['frameCount'] as number)++;
        (this['worldTime'] as number) += dt;
        (this['worldRunTime'] as number) += dt;
        this.inputCmdCenter.executeInputCmd();
        this.systemCenter.updateSystems(dt);
        if(!this.isLocalWorld){
            this.snapshotCenter.checkSnapshot();
        }
    }
    /**
     * 初始化世界
     */
    startNewWorld(){
        this.eventBus.emit(ECSEvent.BaseEvent.INIT_WORLD, {
            ruleFlowConfig: this.configCenter.levelConfig.ruleFlowConfigForWorldInit,
        });
    }
    // ==================== 实体管理委托 ====================

    /**
     * 创建实体
     * @returns 实体ID
     */
    createEntity(): number {
        return this.entityCenter.createEntity();
    }

    /**
     * 移除实体
     * @param entityId 实体ID
     */
    removeEntity(entityId: number): void {
        this.entityCenter.removeEntity(entityId);
    }

    /**
     * 销毁实体
     * @param entityId 实体ID
     */
    destroyEntity(entityId: number, delayTime: number = 0.1): void {
        this.entityCenter.destroyEntity(entityId, delayTime);
    }

    /**
     * 实体是否即将销毁
     * @param entityId 实体ID
     * @returns 是否即将销毁
     */
    willDestroyEntity(entityId: number): boolean {
        return this.entityCenter.willDestroyEntity(entityId);
    }

    // ==================== 组件管理委托 ====================

    /**
     * 添加组件
     * @param entity 实体ID
     * @param componentClass 组件类
     * @param options 组件构造参数
     * @returns 组件实例
     */
    addComponent<T extends Component, A extends any[]>(entity: number, componentClass: new (...args: A) => T, ...options: A): T {
        return this.entityCenter.addComponent(entity, componentClass, ...options);
    }

    /**
     * 移除组件
     * @param entity 实体ID
     * @param component 组件实例
     */
    removeComponent<T extends Component>(entity: number, component: T): void {
        this.entityCenter.removeComponent(entity, component);
    }

    /**
     * 移除实体的所有组件
     * @param entity 实体ID
     */
    removeAllComponent(entity: number): void {
        this.entityCenter.removeAllComponent(entity);
    }

    /**
     * 获取组件
     * @param entity 实体ID
     * @param type 组件类型
     * @returns 组件实例
     */
    getComponent<T extends Component>(entity: number, type: clzz<T>): T {
        return this.entityCenter.getComponent(entity, type);
    }

    /**
     * 检查实体是否拥有组件
     * @param entity 实体ID
     * @param type 组件类型
     * @returns 是否拥有组件
     */
    hasComponent<T extends Component>(entity: number, type: clzz<T>): boolean {
        return this.entityCenter.hasComponent(entity, type);
    }

    // ==================== 组件监听器管理委托 ====================

    /**
     * 组件添加监听
     */
    onComponentAdd<T extends Component>(componentType: clzz<T>, listener: (entityId: number, component: T) => void, thisObj: any): void {
        this.entityCenter.onComponentAdd(componentType, listener, thisObj);
    }

    /**
     * 组件移除监听
     */
    onComponentRemove<T extends Component>(componentType: clzz<T>, listener: (entityId: number, component: T) => void, thisObj: any): void {
        this.entityCenter.onComponentRemove(componentType, listener, thisObj);
    }

    /**
     * 移除组件增加监听
     */
    offComponentAdd<T extends Component>(componentType: clzz<T>, listener: (entityId: number, component: T) => void, thisObj: any): void {
        this.entityCenter.offComponentAdd(componentType, listener, thisObj);
    }

    /**
     * 移除组件移除监听
     */
    offComponentRemove<T extends Component>(componentType: clzz<T>, listener: (entityId: number, component: T) => void, thisObj: any): void {
        this.entityCenter.offComponentRemove(componentType, listener, thisObj);
    }

    /**
     * 组件变化监听
     */
    onComponentChange<T extends Component>(entityId: number, componentType: clzz<T>, listener: ComponentChangeListener, thisObj: any): void {
        this.entityCenter.componentProxy.onChange(entityId, componentType, listener, thisObj);
    }

    /**
     * 移除组件变化监听
     */
    offComponentChange<T extends Component>(entityId: number, componentType: clzz<T>, listener: ComponentChangeListener, thisObj: any): void {
        this.entityCenter.componentProxy.offChange(entityId, componentType, listener, thisObj);
    }

    // ==================== 关系管理委托 ====================

    /**
     * 添加关系
     * @param source 源实体ID
     * @param target 目标实体ID
     * @param name 关系名称
     * @returns 是否成功设置
     */
    addRelation(source: number, target: number, name: RelationName, key?: string): boolean {
        return this.entityCenter.worldRelation.addRelation(source, target, name, key);
    }

    /**
     * 添加关系
     * @param source 源实体ID
     * @param target 目标实体ID
     * @param name 关系名称
     * @param index 关系索引
     * @param key 关系key
     * @returns 是否成功设置
     */
    addRelationAt(source: number, target: number, name: RelationName, index: number, key?: string): boolean {
        return this.entityCenter.worldRelation.addRelationAt(source, target, name, index, key);
    }

    /**
     * 移除关系
     * @param source 源实体ID
     * @param target 目标实体ID
     * @param name 关系名称
     * @returns 是否成功移除
     */
    removeRelation(source: number, target: number, name: RelationName, key?: string): boolean {
        return this.entityCenter.worldRelation.removeRelation(source, target, name, key);
    }

    /**
     * 获取源实体列表（查询父亲/所有者等）
     * @param target 目标实体ID
     * @param name 关系名称
     * @returns 源实体ID数组
     */
    getSources(target: number, name: RelationName, key?: string): readonly number[] {
        return this.entityCenter.worldRelation.getSources(target, name, key);
    }

    /**
     * 获取目标实体列表（查询儿子/物品等）
     * @param source 源实体ID
     * @param name 关系名称
     * @returns 目标实体ID数组
     */
    getTargets(source: number, name: RelationName, key?: string): readonly number[] {
        return this.entityCenter.worldRelation.getTargets(source, name, key);
    }

    /**
     * 检查是否存在关系
     * @param source 源实体ID
     * @param target 目标实体ID
     * @param name 关系名称
     * @returns 是否存在关系
     */
    hasRelation(source: number, target: number, name: RelationName, key?: string): boolean {
        return this.entityCenter.worldRelation.hasRelation(source, target, name, key);
    }

    /**
     * 检查实体是否有目标（是否有儿子/物品等）
     * @param source 源实体ID
     * @param name 关系名称
     * @returns 是否有目标
     */
    hasTargets(source: number, name: RelationName, key?: string): boolean {
        return this.entityCenter.worldRelation.hasTargets(source, name, key);
    }

    /**
     * 检查实体是否有源（是否有父亲/所有者等）
     * @param target 目标实体ID
     * @param name 关系名称
     * @returns 是否有源
     */
    hasSources(target: number, name: RelationName, key?: string): boolean {
        return this.entityCenter.worldRelation.hasSources(target, name, key);
    }

    // ==================== 查询系统委托 ====================

    /**
     * 查询拥有指定组件的实体
     * @param componentTypes 组件类型数组
     * @returns 匹配的实体ID数组
     */
    query(componentTypes: clzz<Component>[]): number[] {
        return this.entityCenter.worldQuery.run(componentTypes);
    }

    // ==================== 命令系统 ====================

    /**
     * 添加输入命令
     * @param data 事件命令数据
     */
    pushInputCmd(data: IEventCmdData): void {
        this.inputCmdCenter.pushInputCmd(data);
    }

    /**
     * 序列化
     */
    serialize(): any {
        const data = {
            frameCount: this.frameCount,
            worldTime: this.worldTime,
            entityCenter: this.entityCenter.serialize(),
            configCenter: this.configCenter.serialize(),
            ECSLayerType: this.ECSLayerType,
        };
        return data;
    }

    /**
     * 反序列化
     */
    deserialize(data: any): void {
        (this['frameCount'] as number) = data.frameCount;
        (this['worldTime'] as number) = data.worldTime;
        this.entityCenter.deserialize(data.entityCenter);
        this.configCenter.deserialize(data.configCenter);
        Object.assign(this.ECSLayerType, data.ECSLayerType);
    }

    clone() {
        const newConfig = JSON.parse(JSON.stringify(this.configCenter.gameConfig));
        const newWorld = new World(newConfig,true);
        newWorld.deserialize(this.serialize());
        return newWorld;
    }
}

// ==================== 只读世界接口 ====================

/**
 * 只读逻辑世界,只能读取逻辑世界数据,禁止修改
 */
export interface ReadonlyWorld {
    getComponent<T extends Component>(entity: number, type: clzz<T>):DeepReadonly<T> | null;
    eventBus: ReadonlyLogicEventBus;
    pushInputCmd(data: IEventCmdData): void;
    query(componentTypes: clzz<Component>[]): readonly number[];
    getSources(target: number, name: RelationName, key?: string): readonly number[];
    getTargets(source: number, name: RelationName, key?: string): readonly number[];
    onComponentChange<T extends Component>(entityId: number, componentType: clzz<T>, listener: ComponentChangeListener, thisObj: any): void;
    offComponentChange<T extends Component>(entityId: number, componentType: clzz<T>, listener: ComponentChangeListener, thisObj: any): void;
}
export interface LogicWorld extends Omit<World, 'pushInputCmd'> {
    eventBus: LogicEventBus;
}
