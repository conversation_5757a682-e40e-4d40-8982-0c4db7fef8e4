import { TempleteBase } from '../cores/center/TempleteCenter';
import AwardComponent from '../components/special/AwardComponent';
import BoardComponent from '../components/board/BoardComponent';
import NodeComponent from '../components/NodeComponent';
import { RenderComponent } from '../components/RenderComponent';
import { RelationName } from '../cores/center/EntityCenter/WorldRelation';
import { TargetType } from '../define/BoardDefine';
import { AwardCondition } from '../define/EcsDefine';
/**
 * 棋盘模板必传参数
 */
export interface BoardTempleteMustArgs {
    /** 行数 */
    rCount: number;
    /** 列数 */
    cCount: number;
    /** 棋盘名称 */
    boardId: string;
    /** 父实体ID */
    parentEntity: number;
    /** 节点参数 */
    nodeParam: Record<string, any>;
}

/**
 * 棋盘模板可选参数
 */
export interface BoardTempleteOptionalArgs {
    /** 是否可放置 */
    isCanPut: boolean;
}

/**
 * 棋盘模板
 */
export class BoardTemplete extends TempleteBase<BoardTempleteMustArgs, BoardTempleteOptionalArgs> {
    getDefaultOptionalArgs(mustArgs: BoardTempleteMustArgs): BoardTempleteOptionalArgs {
        return {
            isCanPut: true,
        };
    }
    /**
     * 创建棋盘模板
     * @param mustArgs 必传参数
     * @param optionalArgs 可选参数
     * @returns 棋盘模板实体ID
     */
    createTemplete(mustArgs: BoardTempleteMustArgs, optionalArgs: BoardTempleteOptionalArgs): number {
        const { rCount, cCount, boardId: boardId, parentEntity, nodeParam } = mustArgs;
        const { isCanPut } = optionalArgs;
        const world = this.world;
        const boardEntity = world.createEntity();
        world.addRelation(parentEntity, boardEntity, RelationName.PARENT_CHILD);
        world.addComponent(boardEntity, BoardComponent, rCount, cCount, boardId, isCanPut);
        world.addComponent(boardEntity, AwardComponent, [{ condition: AwardCondition.FullLine, target: TargetType.Score }]);
        world.addComponent(boardEntity, NodeComponent, nodeParam);
        world.addComponent(boardEntity, RenderComponent, 'ecsview_board');

        return boardEntity;
    }
}
