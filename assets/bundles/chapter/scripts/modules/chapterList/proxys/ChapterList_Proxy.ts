import { Loader } from '../../../../../../advmain/scripts/base/Loader';
import { SubGameBridge } from '../../../../../../advmain/scripts/base/SubGameBridge';
import { chapterConfigInfo } from '../../../../../../advmain/scripts/modules/chapterConfig/vo/ChapterConfigInfo';
import { PrefabConfig } from '../../../../../../advmain/scripts/modules/prefab/PrefabConfig';
import ChapterList from '../components/ChapterList';
import { E_ChapterList_Show } from '../events/E_ChapterList_Show';

export class ChapterList_Proxy extends falcon.Proxy {
    protected onInit(): void {
        this.addEventListeners();
    }

    registerEvents(): { new (...args: any): falcon.ModuleEvent }[] | null {
        return [E_ChapterList_Show];
    }

    receivedEvents($event: falcon.ModuleEvent): void {
        switch ($event.getClass()) {
            case E_ChapterList_Show:
                this.openUI($event as E_ChapterList_Show);
                break;
        }
    }

    addEventListeners() {
        falcon.UI.addEventListener('open', (type) => {
            if (type === PrefabConfig.ChapterList) {
                falcon.storage.setItem(`chapterNum`, falcon.storage.getItem('chapterNum', 0));
            }
        });
    }
    private async openUI(event: E_ChapterList_Show) {
        await Loader.showUI(PrefabConfig.ChapterList);
        const com = Cinst(ChapterList);
        const curChapter = falcon.storage.getItem('chapterNum', 0);
        if (com) {
            com.setState({
                curChapter,
                lastChapter: falcon.storage.getItem('lastChapterNum', 0),
                isThrough: chapterConfigInfo.isThroughAll,
                isForceDraw: true,
            });
        }
        // 更新马赛克移动数据
        falcon.storage.setItem('lastChapterNum', curChapter);
    }
}
