import TargetComponent from '../../components/special/TargetComponent';
import { AudioId } from '../../config/conf/EcsAudioConfig';
import { ECSEvent } from '../../GameEvent';
import { ECSViewBase, IECSViewConfig } from '../ECSViewBase';

const { ccclass } = cc._decorator;
/**
 * 收集图标视图
 */
@ccclass
export class CollectIconECSView extends ECSViewBase<IECSViewConfig> {
    protected async initView() {
        const beginPoint = this.renderWorld.getRenderEntityRootPosition(this.data.startEntity);
        this.node.setPosition(beginPoint.x, beginPoint.y);

        this.renderWorld.loadRes<cc.SpriteFrame>('level', `cell/${this.data.type}/img/iconBig`, cc.SpriteFrame).then((frame) => {
            this.node.getChildByName('icon').getComponent(cc.Sprite).spriteFrame = frame;
        });
        const audioId = this.getAudioIdByType(this.data.type);
        this.renderWorld.playAudio(audioId);
        cc.tween(this.node)
            .to(0.13, { scale: 1.5 })
            .to(0.07, { scale: 1.2 })
            .call(() => {
                const targetEntity = this.logicWorld.query([TargetComponent])[0];
                const targetPos = this.renderWorld.getRenderEntityRootPosition(targetEntity, `targets/target${this.data.type}`);
                const disPos = cc.v2(targetPos.x - this.node.position.x, targetPos.y - this.node.position.y);
                //临时写法，应该拿到当前回合的收集顺序
                const listI = 0;
                cc.tween(this.node)
                    .delay(0.33 + 0.07 * listI)
                    .parallel(
                        cc.tween().to(0.27, { scale: 1, angle: 180 }),
                        cc.tween().bezierBy(0.27, cc.Vec2.ZERO, cc.v2(disPos.y / 2, -disPos.y), disPos),
                    )
                    .call(() => {
                        this.renderWorld.destroyECSView(this);
                        this.renderWorld.eventBus.emitRenderEvent(ECSEvent.RenderEvent.FRESH_TARGET_COLLECT, this.data.targetParam);
                    })
                    .start();
            })
            .start();
    }
    protected addEvents() {}
    protected removeEvents() {}
    protected disposeView() {}
    getAudioIdByType(type: number): AudioId {
        if (type % 2 === 0) {
            return AudioId.TRAVEL_GAME_COLLECT_ITEM1;
        }
        return AudioId.TRAVEL_GAME_COLLECT_ITEM2;
    }
}
