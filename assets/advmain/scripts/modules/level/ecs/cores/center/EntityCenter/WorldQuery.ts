import { Component } from '../../Component';
import { ISerializable } from '../../ISerializable';
import { EntityCenter } from './EntityCenter';

/**
 * 世界查询工具类
 * 负责在World中检索拥有指定组件集合的实体
 * 提供简单查询和优化查询两种方式，包含位掩码系统和查询缓存
 */
export class WorldQuery implements ISerializable{
    // 位掩码操作的常量
    private static readonly BITS_PER_ELEMENT = 32;
    private static readonly ELEMENT_MASK = 31; // 32 - 1
    /** 实体中心 */
    private entityCenter: EntityCenter;
    // ==================== 实例属性 ====================
    // 组件类型 -> 位索引映射
    private componentBitIndex: Record<string, number>;
    private nextBitIndex;

    // 实体 -> 组件位掩码数组映射
    private entityMasks: Record<number, Uint32Array>;

    // 查询缓存
    private queryCache: Record<string, { result: number[]; version: number }> ;
    private queryCacheMasks: Record<string, Uint32Array>;

    // 缓存版本号，用于快速失效检查
    private cacheVersion = 0;

    // 性能优化：预分配的临时数组
    private tempResultArray: number[];

    /**
     * 构造函数
     * @param world 关联的世界实例
     */
    constructor(entityCenter: EntityCenter) {
        this.entityCenter = entityCenter;
        this.componentBitIndex = {};
        this.nextBitIndex = 0;
        this.entityMasks = {};
        this.queryCache = {};
        this.queryCacheMasks = {};
        this.cacheVersion = 0;
        this.tempResultArray = [];
        

    }

    // ==================== 查询方法 ====================

    /**
     * 执行查询操作（优化版本）
     * @param componentTypes 组件类型数组
     * @returns 匹配的实体ID数组
     */
    run(componentTypes: (typeof Component)[]): number[] {
        if (!this.entityCenter || componentTypes.length === 0) {
            return [];
        }

        // 单组件查询优化
        if (componentTypes.length === 1) {
            return this.runSingleComponent(componentTypes[0]);
        }

        // 生成查询签名用于缓存
        const querySignature = this.generateQuerySignature(componentTypes);

        // 检查缓存
        const cached = this.queryCache[querySignature];
        if (cached && cached.version === this.cacheVersion) {
            return cached.result;
        }

        // 使用位掩码查询
        const result = this.runBitmaskQuery(componentTypes);

        // 缓存结果
        this.queryCache[querySignature] = { result: result.slice(), version: this.cacheVersion };

        return result;
    }

    /**
     * 单组件查询优化
     * @param componentType 组件类型
     * @returns 匹配的实体ID数组
     */
    private runSingleComponent(componentType: typeof Component): number[] {
        const componentStore = this.entityCenter.components[componentType.name];
        if (!componentStore) {
            return [];
        }

        // 直接返回组件存储中的实体ID
        return Object.keys(componentStore).map(Number);
    }

    /**
     * 使用位掩码进行查询
     * @param componentTypes 组件类型数组
     * @returns 匹配的实体ID数组
     */
    private runBitmaskQuery(componentTypes: (typeof Component)[]): number[] {
        // 构建查询掩码
        const queryMask = this.buildQueryMask(componentTypes);
        if (!queryMask) {
            return this.runSimple(componentTypes); // 降级到简单查询
        }

        // 清空临时结果数组
        this.tempResultArray.length = 0;

        // 遍历所有实体进行位掩码匹配
        this.entityCenter.entities.forEach((entityId) => {
            const entityMask = this.entityMasks[entityId];
            if (entityMask && this.maskMatches(entityMask, queryMask)) {
                this.tempResultArray.push(entityId);
            }
        });

        // 返回结果的副本
        return this.tempResultArray.slice();
    }

    /**
     * 构建查询掩码
     * @param componentTypes 组件类型数组
     * @returns 查询掩码或null
     */
    private buildQueryMask(componentTypes: (typeof Component)[]): Uint32Array | null {
        const signature = this.generateQuerySignature(componentTypes);

        // 检查缓存的查询掩码
        let queryMask = this.queryCacheMasks[signature];
        if (queryMask) {
            return queryMask;
        }

        // 构建新的查询掩码
        const maskLength = this.getMaskArrayLength();
        queryMask = new Uint32Array(maskLength);

        for (const componentType of componentTypes) {
            const bitIndex = this.componentBitIndex[componentType.name];
            if (bitIndex === undefined) {
                // 如果组件类型不存在，返回null降级到简单查询
                return null;
            }
            this.setBit(queryMask, bitIndex, true);
        }

        // 缓存查询掩码
        this.queryCacheMasks[signature] = queryMask;
        return queryMask;
    }

    /**
     * 生成查询签名
     * @param componentTypes 组件类型数组
     * @returns 查询签名字符串
     */
    private generateQuerySignature(componentTypes: (typeof Component)[]): string {
        // 排序组件名以确保相同查询的签名一致
        const sortedNames = componentTypes.map((type) => type.name).sort();
        return sortedNames.join('|');
    }

    /**
     * 执行简单查询操作（降级方案）
     * @param componentTypes 组件类型数组
     * @returns 匹配的实体ID数组
     */
    runSimple(componentTypes: (typeof Component)[]): number[] {
        if (!this.entityCenter) {
            return [];
        }

        // 清空临时结果数组
        this.tempResultArray.length = 0;

        // 找到最小的组件存储作为基准，减少遍历次数
        let minComponentStore: Record<number, Component> | null = null;
        let minSize = Infinity;

        for (const type of componentTypes) {
            const componentStore = this.entityCenter.components[type.name];
            if (!componentStore || Object.keys(componentStore).length === 0) {
                return []; // 如果任何组件不存在或为空，直接返回空数组
            }
            const storeSize = Object.keys(componentStore).length;
            if (storeSize < minSize) {
                minSize = storeSize;
                minComponentStore = componentStore;
            }
        }

        if (!minComponentStore) {
            return [];
        }

        // 遍历最小的组件存储
        Object.keys(minComponentStore).forEach((entityIdStr) => {
            const entityId = Number(entityIdStr);
            let hasAllComponents = true;
            for (const type of componentTypes) {
                if (type.name === minComponentStore![entityId]?.constructor.name) {
                    continue; // 跳过已知存在的组件
                }
                const componentStore = this.entityCenter.components[type.name];
                if (!componentStore || !componentStore[entityId]) {
                    hasAllComponents = false;
                    break;
                }
            }
            if (hasAllComponents) {
                this.tempResultArray.push(entityId);
            }
        });

        return this.tempResultArray.slice();
    }

    // ==================== 位掩码管理 ====================

    /**
     * 获取组件类型的位索引，如果不存在则分配一个新的
     */
    getComponentBitIndex(componentType: string): number {
        let bitIndex = this.componentBitIndex[componentType];
        if (bitIndex === undefined) {
            bitIndex = this.nextBitIndex++;
            this.componentBitIndex[componentType] = bitIndex;
        }
        return bitIndex;
    }

    /**
     * 获取掩码数组所需的长度
     */
    private getMaskArrayLength(): number {
        return Math.ceil(this.nextBitIndex / WorldQuery.BITS_PER_ELEMENT) || 1;
    }

    /**
     * 获取实体的组件掩码，如果需要则扩展数组
     */
    getEntityMask(entity: number): Uint32Array {
        let entityMask = this.entityMasks[entity];

        if (!entityMask) {
            // 创建新掩码并根据现有组件初始化
            entityMask = this.createEntityMask(entity);
            this.entityMasks[entity] = entityMask;
        } else {
            // 检查是否需要扩展数组
            const requiredLength = this.getMaskArrayLength();
            if (entityMask.length < requiredLength) {
                entityMask = this.expandEntityMask(entityMask, requiredLength);
                this.entityMasks[entity] = entityMask;
            }
        }

        return entityMask;
    }

    /**
     * 创建新的实体掩码
     * @param entity 实体ID
     * @returns 新的实体掩码
     */
    private createEntityMask(entity: number): Uint32Array {
        const requiredLength = this.getMaskArrayLength();
        const newMask = new Uint32Array(requiredLength);

        // 根据现有组件设置位
        Object.entries(this.entityCenter.components).forEach(([componentType, componentStore]) => {
            if (componentStore[entity]) {
                const bitIndex = this.getComponentBitIndex(componentType);
                this.setBitFast(newMask, bitIndex);
            }
        });

        return newMask;
    }

    /**
     * 扩展实体掩码数组
     * @param oldMask 旧掩码
     * @param newLength 新长度
     * @returns 扩展后的掩码
     */
    private expandEntityMask(oldMask: Uint32Array, newLength: number): Uint32Array {
        const newMask = new Uint32Array(newLength);
        newMask.set(oldMask);
        return newMask;
    }

    /**
     * 设置位掩码中的某一位（优化版本）
     */
    setBit(mask: Uint32Array, bitIndex: number, value: boolean): void {
        const arrayIndex = bitIndex >>> 5; // 等价于 Math.floor(bitIndex / 32)
        if (arrayIndex < mask.length) {
            const bitPosition = bitIndex & WorldQuery.ELEMENT_MASK; // 等价于 bitIndex % 32
            if (value) {
                mask[arrayIndex] |= 1 << bitPosition;
            } else {
                mask[arrayIndex] &= ~(1 << bitPosition);
            }
        }
    }

    /**
     * 快速设置位（只用于设置为true）
     */
    private setBitFast(mask: Uint32Array, bitIndex: number): void {
        const arrayIndex = bitIndex >>> 5;
        if (arrayIndex < mask.length) {
            const bitPosition = bitIndex & WorldQuery.ELEMENT_MASK;
            mask[arrayIndex] |= 1 << bitPosition;
        }
    }

    /**
     * 检查两个掩码是否满足查询条件（优化版本）
     */
    maskMatches(entityMask: Uint32Array, queryMask: Uint32Array): boolean {
        const minLength = Math.min(entityMask.length, queryMask.length);

        // 优化：先检查较短的部分
        for (let i = 0; i < minLength; i++) {
            if ((entityMask[i] & queryMask[i]) !== queryMask[i]) {
                return false;
            }
        }

        // 检查查询掩码是否有超出实体掩码长度的部分
        for (let i = minLength; i < queryMask.length; i++) {
            if (queryMask[i] !== 0) {
                return false;
            }
        }

        return true;
    }

    /**
     * 更新实体的组件掩码
     */
    updateEntityMask(entity: number, componentType: string, hasComponent: boolean): void {
        const bitIndex = this.getComponentBitIndex(componentType);
        const entityMask = this.getEntityMask(entity);
        this.setBit(entityMask, bitIndex, hasComponent);
        // 使查询缓存失效
        this.invalidateQueryCache();
    }

    /**
     * 删除实体掩码
     */
    removeEntityMask(entity: number): void {
        delete this.entityMasks[entity];
        this.invalidateQueryCache();
    }

    /**
     * 初始化实体掩码
     */
    initEntityMask(entity: number): void {
        const initialLength = Math.max(1, this.getMaskArrayLength());
        this.entityMasks[entity] = new Uint32Array(initialLength);
    }

    /**
     * 检查实体是否有掩码
     */
    hasEntityMask(entity: number): boolean {
        return this.entityMasks.hasOwnProperty(entity);
    }

    // ==================== 查询缓存管理 ====================

    /**
     * 使查询缓存失效
     */
    invalidateQueryCache(): void {
        this.cacheVersion++;
        // 定期清理过期缓存
        if (this.cacheVersion % 100 === 0) {
            this.cleanupCache();
        }
    }

    /**
     * 清理过期缓存
     */
    private cleanupCache(): void {
        const currentVersion = this.cacheVersion;
        Object.entries(this.queryCache).forEach(([key, value]) => {
            if (value.version < currentVersion - 10) {
                // 保留最近10个版本
                delete this.queryCache[key];
            }
        });
    }
    
    serialize() {
        // 保存核心数据：组件位索引映射和实体掩码
        const serializedData = {
            // 组件类型到位索引的映射
            componentBitIndex: this.componentBitIndex,
            // 下一个可用的位索引
            nextBitIndex: this.nextBitIndex,
            // 实体的组件位掩码（Uint32Array 需要转换为普通数组）
            entityMasks: Object.entries(this.entityMasks).map(([entityId, mask]) => [
                entityId, // entityId 本身就是 number 类型
                Array.from(mask) // 将 Uint32Array 转换为普通数组
            ])
        };
        
        return serializedData;
    }
    
    deserialize(data: any): void {
        // 恢复组件位索引映射
        this.componentBitIndex = data.componentBitIndex
        this.nextBitIndex = data.nextBitIndex;
        
        // 恢复实体掩码（将普通数组转换回 Uint32Array）
        this.entityMasks = Object.fromEntries(
            data.entityMasks.map(([entityId, maskArray]: [number, number[]]) => [
                entityId,
                new Uint32Array(maskArray)
            ])
        );
        
        // 清空缓存，让它们重新生成
        this.invalidateQueryCache();
        this.queryCache = {};
        this.queryCacheMasks = {};
        this.cacheVersion = 0;
    }
}