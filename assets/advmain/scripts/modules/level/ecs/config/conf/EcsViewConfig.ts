import { ECSViewConst } from '../../registry/ECSViewRegistry';
import { IECSViewConfig } from '../../renderWorld/ECSViewBase';
import { IShapeECSViewConfig } from '../../renderWorld/view/board/ShapeECSView';
import { IScoreWinViewConfig } from '../../renderWorld/view/result/ScoreWinECSView';
import { IWinStreak001ViewConfig } from '../../renderWorld/view/winStreak/WinStreak001ECSView';
import { AudioId } from './EcsAudioConfig';

/**ECS视图配置 */
export const EcsViewConfig: Record<string, IECSViewConfig> = {
    ecsview_init_board_scene: {
        ecsViewId: 'ecsview_init_board_scene',
        prefabBundleName: 'advres',
        prefabUrl: 'prefabs/level/board/boardScene',
        ecsViewName: ECSViewConst.SceneECSView,
    },
    ecsview_init_Icon_collect: {
        ecsViewId: 'ecsview_init_Icon_collect',
        prefabBundleName: 'advres',
        prefabUrl: 'prefabs/level/board/collectIcon',
        ecsViewName: ECSViewConst.CollectIconECSView,
    },
    ecsview_board: {
        ecsViewId: 'ecsview_board',
        prefabBundleName: 'advres',
        prefabUrl: 'prefabs/level/emptyNode',
        ecsViewName: ECSViewConst.BoardECSView,
    },
    ecsview_board_full_scene_effect: {
        ecsViewId: 'ecsview_board_full_scene_effect',
        prefabBundleName: 'advres',
        prefabUrl: 'prefabs/level/emptyNode',
        ecsViewName: ECSViewConst.BoardFullSceneEffectECSView,
    },
    ecsview_shape: {
        ecsViewId: 'ecsview_shape',
        prefabBundleName: 'advres',
        prefabUrl: 'prefabs/level/emptyNode',
        ecsViewName: ECSViewConst.ShapeECSView,
        putAudioId: AudioId.BLOCK_PUT_AUDIO,
    } as IShapeECSViewConfig,
    ecsview_simple_effect: {
        ecsViewId: 'ecsview_simple_effect',
        prefabBundleName: 'advres',
        prefabUrl: 'prefabs/level/emptyNode',
        ecsViewName: ECSViewConst.SimpleEffectECSView,
    },
    ecsview_cell: {
        ecsViewId: 'ecsview_cell',
        prefabBundleName: 'advres',
        prefabUrl: 'prefabs/level/board/cellNode',
        ecsViewName: ECSViewConst.CellECSView,
    },
    ecsview_slot: {
        ecsViewId: 'ecsview_slot',
        prefabBundleName: 'advres',
        prefabUrl: 'prefabs/level/board/slotNode',
        ecsViewName: ECSViewConst.SlotECSView,
    },
    ecsview_collect_cell: {
        ecsViewId: 'ecsview_collect_cell',
        prefabBundleName: 'advres',
        prefabUrl: 'prefabs/level/board/cellNode',
        ecsViewName: ECSViewConst.CollectCellECSView,
    },
    ecsview_target: {
        ecsViewId: 'ecsview_target',
        prefabBundleName: 'advres',
        prefabUrl: 'prefabs/level/target/targetNode',
        ecsViewName: ECSViewConst.TargetECSView,
    },
    ecsview_target_info: {
        ecsViewId: 'ecsview_target_info',
        prefabBundleName: 'advres',
        prefabUrl: 'prefabs/level/targetInfo/targetInfo',
        ecsViewName: ECSViewConst.TargetInfoECSView,
    },
    ecsview_glass_cell: {
        ecsViewId: 'ecsview_glass_cell',
        prefabBundleName: 'level',
        prefabUrl: 'cell/120/cell',
        ecsViewName: ECSViewConst.GlassECSView,
    },
    ecsview_plane_cell: {
        ecsViewId: 'ecsview_plane_cell',
        prefabBundleName: 'level',
        prefabUrl: 'cell/110/cell',
        ecsViewName: ECSViewConst.PlaneECSView,
    },
    ecsview_plane_spine: {
        ecsViewId: 'ecsview_plane_spine',
        prefabBundleName: 'level',
        prefabUrl: 'cell/110/spine',
        ecsViewName: ECSViewConst.PlaneSpineECSView,
    },
    ecsview_cell_mask: {
        ecsViewId: 'ecsview_cell_mask',
        prefabBundleName: 'advres',
        prefabUrl: 'prefabs/level/board/cellMaskNode',
        ecsViewName: ECSViewConst.StaticECSView,
    },
    ecsview_single_score_effect: {
        ecsViewId: 'ecsview_single_score_effect',
        prefabBundleName: 'advres',
        prefabUrl: 'prefabs/comboScoreTip/singleScoreEffect',
        ecsViewName: ECSViewConst.SingleScoreEffectView,
    },
    ecsview_revive: {
        ecsViewId: 'ecsview_revive',
        prefabBundleName: 'advres',
        prefabUrl: 'prefabs/level/revive/Revive',
        ecsViewName: ECSViewConst.ReviveECSView,
        mask: {
            opacity: 0.8,
        },
    } as IECSViewConfig,

    ecsview_collect_fail: {
        ecsViewId: 'ecsview_collect_fail',
        prefabBundleName: 'advres',
        prefabUrl: 'prefabs/level/fail/reduceTravelSettlement/ChapterReduceCollectFail',
        ecsViewName: ECSViewConst.CollectFailECSView,
        mask: {
            opacity: 0.8,
        },
    } as IECSViewConfig,

    ecsview_score_fail: {
        ecsViewId: 'ecsview_score_fail',
        prefabBundleName: 'advres',
        prefabUrl: 'prefabs/level/fail/reduceTravelSettlement/ChapterReduceScoreFail',
        ecsViewName: ECSViewConst.ScoreFailECSView,
        mask: {
            opacity: 0.8,
        },
    } as IECSViewConfig,

    ecsview_layer: {
        ecsViewId: 'ecsview_layer',
        prefabBundleName: 'advres',
        prefabUrl: 'prefabs/level/emptyNode',
        ecsViewName: ECSViewConst.StaticECSView,
    },

    ecsview_score_win: {
        ecsViewId: 'ecsview_score_win',
        prefabBundleName: 'advres',
        prefabUrl: 'prefabs/level/win/reduceTravelSettlement/ChapterReduceScoreWin',
        ecsViewName: ECSViewConst.ScoreWinECSView,
        mask: {
            opacity: 0.8,
        },
    } as IScoreWinViewConfig,

    ecsview_collect_win: {
        ecsViewId: 'ecsview_collect_win',
        prefabBundleName: 'advres',
        prefabUrl: 'prefabs/level/win/reduceTravelSettlement/ChapterReduceCollectWin',
        ecsViewName: ECSViewConst.CollectWinECSView,
        mask: {
            opacity: 0.8,
        },
    } as IECSViewConfig,
    ecsview_score_hard_win: {
        ecsViewId: 'ecsview_score_hard_win',
        prefabBundleName: 'advres',
        prefabUrl: 'prefabs/level/win/reduceTravelSettlement/ChapterReduceScoreWin',
        ecsViewName: ECSViewConst.ScoreWinECSView,
        nextBtnPath: 'textures/win/trait/difficultChangeColorTrait/btn_nextHardLevel',
        mask: {
            opacity: 0.8,
        },
    } as IScoreWinViewConfig,
    ecsview_collect_hard_win: {
        ecsViewId: 'ecsview_collect_hard_win',
        prefabBundleName: 'advres',
        prefabUrl: 'prefabs/level/win/reduceTravelSettlement/ChapterReduceCollectWin',
        ecsViewName: ECSViewConst.CollectWinECSView,
        nextBtnPath: 'textures/win/trait/difficultChangeColorTrait/btn_nextHardLevel',
        mask: {
            opacity: 0.8,
        },
    } as IScoreWinViewConfig,
    ecsview_win_streak001: {
        ecsViewId: 'ecsview_win_streak001',
        prefabBundleName: 'advres',
        prefabUrl: 'prefabs/winStreak/WinStreak001',
        ecsViewName: ECSViewConst.WinStreak001ECSView,
        winNum: 0,
        timestamp: 0,
    } as IWinStreak001ViewConfig,
    ecsview_multi_elimination_special_effect: {
        ecsViewId: 'ecsview_multi_elimination_special_effect',
        prefabBundleName: 'advres',
        prefabUrl: 'prefabs/level/emptyNode',
        ecsViewName: ECSViewConst.MultiEliminationEffectSpecialECSView,
    } as IECSViewConfig,
    ecsview_board_refurbish_effect: {
        ecsViewId: 'ecsview_board_refurbish_effect',
        prefabBundleName: 'advres',
        prefabUrl: 'prefabs/level/emptyNode',
        ecsViewName: ECSViewConst.BoardRefurbishEffectECSView,
    } as IECSViewConfig,
};
