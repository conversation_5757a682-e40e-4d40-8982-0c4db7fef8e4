import { CellColorsComponent } from '../components/special/CellColorsComponent';
import { CellColor } from '../define/BoardDefine';
import { ECSRuleBase, IECSRuleConfig } from './core/ECSRuleBase';

export interface IInitCellColorsRuleConfig extends IECSRuleConfig {
    /** 当前关卡可用颜色列表 */
    colors: CellColor[];
}

export class InitCellColorsRule extends ECSRuleBase<IInitCellColorsRuleConfig> {
    canExecute(): boolean {
        return true;
    }
    execute(): void {
        // Initialize cell colors using the provided colors
        const world = this.world;
        const entityId = world.createEntity();
        world.addComponent(entityId, CellColorsComponent, this.config.colors);
    }
}
