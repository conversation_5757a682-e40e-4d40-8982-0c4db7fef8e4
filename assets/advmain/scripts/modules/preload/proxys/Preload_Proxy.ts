
import { Loader } from "../../../base/Loader";
import { SubGameBridge } from "../../../base/SubGameBridge";
import { preloadConfig } from "../config/PreloadConfig";

export class Preload_Proxy extends falcon.Proxy {

    private _triggerLoaded: { [key: string]: boolean } = {};
    protected onInit(): void {
        this.preload();
    }

    /**
     * 预加载
     */
    preload() {
        falcon.UI.addEventListener('open', (prefabConfigType) => {
            const { name } = prefabConfigType;
            if (!this._triggerLoaded[name]) {
                this._triggerLoaded[name] = true;
                falcon.task.run(() => {
                    const preloadList = preloadConfig[name];
                    if (preloadList) {
                        for (let i = 0; i < preloadList.length; i++) {
                            const preloadConfig = preloadList[i];
                            if (typeof preloadConfig === 'function') {
                                preloadConfig();
                            } else {
                                const { url, type, bundleName, traitClassName } = preloadConfig;
                                if (traitClassName) {
                                    // 如果绑定了特性类名，则只能特性开启才会加载
                                    const trait = falcon.decoratorTraitsClassNameMap[traitClassName];
                                    //if (!trait || !trait.active) {
                                    if (!trait) {
                                        continue;
                                    }
                                }
                                Loader.loadByBundle(bundleName, url, type || cc.Asset, null);
                            }
                        }
                    }
                });
            }
        });
    }
}