// import { BuffData } from "../../../component/BuffComponent";
// import { IBaseStackConfig, PriorityCompareType } from "../../../../config/BuffConfig";
// import { IBehaviorData } from "../../../core/BehaviorBase";
// import { IBuffAddParams, IBuffAddResult, IBuffRemoveParams, IBuffRemoveResult, StackBehaviorBase, StackResult } from "./StackBehaviorBase";

// /**
//  * 优先级堆叠行为实现类
//  * 
//  * 核心特性：
//  * - 同一时间只有一个优先级最高的buff处于激活状态
//  * - 新buff进入时，与当前激活的buff比较优先级，高者激活
//  * - 激活buff被销毁时，自动激活剩余buff中优先级最高的
//  * 
//  * 应用场景：
//  * - 同类型增益效果，只取最强的一个生效(如多个攻击力提升buff)
//  * - 同类型减益效果，只取最强的一个生效(如多个减速效果)
//  * - 互斥型效果管理(如不同类型的姿态、形态切换)
//  */
// export class PriorityStackStrategy extends StackBehaviorBase<IPriorityStackConfig> {

//     handleBuffAdd(params: IBuffAddParams): IBuffAddResult {
//         const existingBuffs = params.existingBuffs;
//         const newBuffConfig = params.newBuffConfig;

//         // 没有现有buff，直接创建
//         if (existingBuffs.length === 0) {
//             return StackResult.CREATE_AND_ACTIVATE;
//         }

//         // 找到当前激活的buff（如果有）
//         const activeBuffs = existingBuffs.filter(buff => buff.isActive);

//         // 如果有多个激活的buff，这是一个错误状态，应该报错
//         if (activeBuffs.length > 1) {
//             console.error(
//                 `[PriorityStackStrategy] 优先级策略下同时有多个激活的buff: entityId=${activeBuffs[0].ownerEntityId}, stackId=${this.config.stackId}, activeCount=${activeBuffs.length}`
//             );
//         }

//         // 如果没有激活的buff，创建新的并激活
//         if (activeBuffs.length === 0) {
//             return StackResult.CREATE_AND_ACTIVATE;
//         }

//         // 有激活的buff，比较优先级
//         const activeBuff = activeBuffs[0]; // 应该只有一个激活的buff
//         const isNewBuffHigherPriority = this.comparePriority(
//             newBuffConfig.value,
//             activeBuff.currentValue
//         );

//         // 如果新buff优先级更高，停用当前激活的buff
//         if (isNewBuffHigherPriority) {
//             // 使用buff处理器停用当前激活的buff
//             this.world.handlerCenter.buffHandler.deactivateBuff(activeBuff);
//         }

//         return isNewBuffHigherPriority ? StackResult.CREATE_AND_ACTIVATE : StackResult.CREATE_NOT_ACTIVATE;
//     }

//     handleBuffDestroy(params: IBuffRemoveParams): IBuffRemoveResult {
//         const destroyingBuff = params.destroyingBuff;
//         const remainingBuffs = params.remainingBuffs;

//         // 如果被销毁的buff不是激活状态，没有影响
//         if (!destroyingBuff.isActive) {
//             return StackResult.NO_CREATE;
//         }

//         // 如果没有剩余的buff，不需要特殊处理
//         if (remainingBuffs.length === 0) {
//             return StackResult.NO_CREATE;
//         }

//         // 找到剩余buff中优先级最高的
//         const highestPriorityBuff = this.findHighestPriorityBuff(remainingBuffs);

//         // 激活该buff
//         if (highestPriorityBuff) {
//             // 使用buff处理器激活buff
//             this.world.handlerCenter.buffHandler.activateBuff(highestPriorityBuff);
//         }

//         return StackResult.NO_CREATE;
//     }

//     /**
//      * 比较两个值的优先级
//      * 根据配置的比较类型决定哪个值具有更高的优先级
//      * 
//      * @param newValue 新值
//      * @param existingValue 现有值
//      * @returns 如果新值优先级更高则返回true
//      */
//     private comparePriority(newValue: number, existingValue: number): boolean {
//         switch (this.config.priorityCompareType) {
//             case PriorityCompareType.VALUE:
//             default:
//                 // 默认值越大优先级越高
//                 return newValue > existingValue;
//         }
//     }

//     /**
//      * 找到优先级最高的buff
//      * 遍历所有buff，根据优先级比较规则找出最高优先级的buff
//      * 
//      * @param buffs 待比较的buff列表
//      * @returns 优先级最高的buff，如果列表为空则返回null
//      */
//     private findHighestPriorityBuff(buffs: BuffData<IBehaviorData>[]): BuffData<IBehaviorData> | null {
//         if (buffs.length === 0) return null;

//         let highestBuff = buffs[0];

//         for (let i = 1; i < buffs.length; i++) {
//             const current = buffs[i];
//             const isHigher = this.comparePriority(
//                 current.currentValue,
//                 highestBuff.currentValue
//             );

//             if (isHigher) {
//                 highestBuff = current;
//             }
//         }

//         return highestBuff;
//     }
// }
// /**
//  * 优先级堆叠配置
//  * 用于优先级堆叠策略
//  */
// export interface IPriorityStackConfig extends IBaseStackConfig {
//     priorityCompareType: PriorityCompareType; // 优先级比较方式（必需）
// }