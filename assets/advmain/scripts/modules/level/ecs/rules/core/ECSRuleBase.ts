import { ExecutionContext, IRuleConfig, IRule } from '../../../../../base/rule/types';
import { StyleGreen, StyleOrange } from '../../cores/ConsoleStyle';
import { TryCatch } from '../../cores/Descriptor';
import { LogicWorld } from '../../cores/World';
import { Expand } from '../../define/EcsDefine';
import { ECSEvent, GameEventMap } from '../../GameEvent';

/** Util函数类型 */
export type UtilFunction<T extends (...args: any[]) => any = (...args: any[]) => any> = T;

/** 工具函数替换器接口 */
export interface IUtilFunctionReplacer<T extends (...args: any[]) => any = (...args: any[]) => any> {
    /** 原始函数 */
    originalFunction: T;
    /** 替换函数 */
    replacementFunction: T;
    /** 是否已激活 */
    isActive: boolean;
    /** 激活替换 */
    activate(): void;
    /** 停用替换 */
    deactivate(): void;
}

/**执行上下文 */
export interface ECSExecuteContext<TEvent extends keyof GameEventMap = keyof GameEventMap> extends ExecutionContext {
    world: LogicWorld;
    /**事件名 */
    event: TEvent;
    /**事件数据 */
    eventData: Expand<GameEventMap[TEvent]>;
}
/**
 * ECS规则配置
 */
export interface IECSRuleConfig extends IRuleConfig {
    /**规则延时 */
    readonly delay: number;
}
/**
 * ECS规则基类
 */
export abstract class ECSRuleBase<
    TConfig extends IECSRuleConfig = IECSRuleConfig,
    TEvent extends keyof GameEventMap = keyof GameEventMap,
    TUtilFunction extends (...args: any[]) => any = (...args: any[]) => any,
> implements IRule<TConfig, ECSExecuteContext<TEvent>>
{
    readonly execType = 'rule';
    /**规则配置 */
    config: TConfig;
    /**世界 */
    world: LogicWorld;
    /**事件名 */
    event: TEvent;
    /**事件数据 */
    eventData: Expand<GameEventMap[TEvent]>;
    /** 工具函数替换器 */
    protected utilReplacer: IUtilFunctionReplacer<TUtilFunction> | null = null;
    /**
     * 设置执行上下文
     */
    setContext(context: ECSExecuteContext<TEvent>): void {
        this.world = context.world;
        this.event = context.event;
        this.eventData = context.eventData;
    }
    /**
     * 传入事件名,即可绑定事件数据。
     * @desc 绑定事件数据,绑定数据事件后,规则就可以获取到该事件传递的事件数据,
     * @desc 但是这样也将规则跟事件绑定了起来,那么规则就只能用在该事件下了。
     * @desc 如果出现绑定的事件跟规则不匹配的情况,那么就会报错。
     * @desc 所以这里建议,如无必要,不要绑定事件数据,这能大大增加规则的通用性
     */
    public bindEventData(): keyof GameEventMap {
        return null;
    }
    /**
     * 替换Handler函数
     * @desc 如果实现此方法，规则会自动替换指定的Handler函数
     * @desc 返回值: { handler: Handler对象, methodName: 方法名, replacementFunction: 替换函数 }
     */
    protected replaceUtilFunc(): {
        util: any;
        methodName: string;
        replacementFunction: (...args: any[]) => any;
    } {
        return null;
    }

    /**
     * 验证事件是否匹配
     */
    public invalidateExecuteContext(): boolean {
        const bindEvent = this.bindEventData();
        if (!bindEvent) {
            this.event = null;
            this.eventData = null;
            return true;
        }
        if (bindEvent !== this.event) {
            //事件不匹配
            console.error(`执行规则失败,规则只能绑定事件:${bindEvent},ruleId:${this.config.ruleId}`);
            return false;
        }
        return true;
    }

    canRun(): boolean {
        return this.canExecute();
    }

    @TryCatch(`规则执行出错`)
    run(): void {
        if (this.config.delay > 0) {
            console.log(`%c延迟执行规则%c:${this.config.ruleType}\n\n描述:${this.config.ruleDesc},延迟:${this.config.delay}`, StyleGreen, '');
            const world = this.world;
            const event = this.event;
            const eventData = this.eventData;
            world.eventBus.emit(ECSEvent.BaseEvent.CREATE_DELAY_RULE, { ruleId: this.config.ruleId, delay: this.config.delay, event, eventData });
            return;
        }
        console.log(`%c执行规则%c:${this.config.ruleType}\n\n描述:${this.config.ruleDesc}`, StyleGreen, '');
        this.handleUtilReplacement();
        this.execute();
    }

    /**
     * 延迟执行
     */
    @TryCatch(`延迟规则执行出错`)
    delayRun(): void {
        console.log(`%c延迟完毕,执行延迟规则%c:${this.config.ruleType}\n\n描述:${this.config.ruleDesc}`, StyleGreen, '');
        this.handleUtilReplacement();
        this.execute();
    }

    /**
     * 验证是否可以执行
     */
    abstract canExecute(): boolean;

    /**
     * 执行
     */
    abstract execute(): void;

    // ==================== Handler函数替换相关实现 ====================

    /**
     * 处理Util函数替换
     */
    private handleUtilReplacement(): void {
        if (this.utilReplacer) return;
        if (!this.replaceUtilFunc) return;
        const replaceConfig = this.replaceUtilFunc();
        if (!replaceConfig) return;
        if (!replaceConfig.util[replaceConfig.methodName] || !replaceConfig.replacementFunction) {
            console.error(`替换 Util函数失败,ruleId:${this.config.ruleId}`);
            return;
        }

        // 创建替换器
        this.utilReplacer = {
            originalFunction: replaceConfig.util[replaceConfig.methodName],
            replacementFunction: replaceConfig.replacementFunction.bind(this) as TUtilFunction,
            isActive: false,

            activate() {
                if (!this.isActive) {
                    console.log(`%c替换Util函数%c:${replaceConfig.methodName}\n\n描述:${this.config.ruleDesc}`, StyleGreen, '');
                    replaceConfig.util[replaceConfig.methodName] = this.replacementFunction;
                    this.isActive = true;
                }
            },

            deactivate() {
                if (this.isActive) {
                    // 恢复原始方法，调用者完全无感知
                    console.log(`%c恢复Util函数%c:${replaceConfig.methodName}\n\n描述:${this.config.ruleDesc}`, StyleOrange, '');
                    replaceConfig.util[replaceConfig.methodName] = this.originalFunction;
                    this.isActive = false;
                }
            },
        };

        // 自动激活替换
        this.utilReplacer.activate();
    }
}
