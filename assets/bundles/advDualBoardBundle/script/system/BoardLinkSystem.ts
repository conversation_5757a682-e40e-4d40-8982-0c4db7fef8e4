import BoardComponent from '../../../../advmain/scripts/modules/level/ecs/components/board/BoardComponent';
import { CellComponent } from '../../../../advmain/scripts/modules/level/ecs/components/board/CellComponent';
import SlotComponent from '../../../../advmain/scripts/modules/level/ecs/components/board/SlotComponent';
import OpacityComponent from '../../../../advmain/scripts/modules/level/ecs/components/OpacityComponent';
import { RelationName } from '../../../../advmain/scripts/modules/level/ecs/cores/center/EntityCenter/WorldRelation';
import { System } from '../../../../advmain/scripts/modules/level/ecs/cores/System';
import { TargetType } from '../../../../advmain/scripts/modules/level/ecs/define/BoardDefine';
import { ECSEvent } from '../../../../advmain/scripts/modules/level/ecs/GameEvent';
import { TempleteType } from '../../../../advmain/scripts/modules/level/ecs/registry/templete/TempleteRegistry';

/**
 * BoardLinkSystem
 * 在拖拽时高亮显示「镜像盘有而主盘没有」的格子，帮助玩家查看缺口。
 */
export default class BoardLinkSystem extends System {
    /**存储当前生成的差异格子实体*/
    private diffCells: number[] = [];
    private diffMap: Map<number, number> = new Map(); // cellId -> slotId

    /**主棋盘与镜像棋盘实体缓存*/
    private mainBoardEntity: number = null;
    private mirrorBoardEntity: number = null;

    init(): void {
        // 事件监听
        const bus = this.world.eventBus;
        bus.on(ECSEvent.GameEvent.DRAG_BLOCK_START, this.createDiff, this);
        bus.on(ECSEvent.GameEvent.DRAG_BLOCK_CANCEL, this.clearDiff, this);
        bus.on(ECSEvent.GameEvent.PUT_BLOCK_BACK, this.clearDiff, this);
        // 监听棋盘切换，重置缓存并清除占位块
        bus.on(ECSEvent.GameEvent.SWITCH_BOARD, this.onBoardSwitched, this);
    }

    dispose(): void {
        const bus = this.world.eventBus;
        bus.off(ECSEvent.GameEvent.DRAG_BLOCK_START, this.createDiff, this);
        bus.off(ECSEvent.GameEvent.DRAG_BLOCK_CANCEL, this.clearDiff, this);
        bus.off(ECSEvent.GameEvent.PUT_BLOCK_BACK, this.clearDiff, this);
        bus.off(ECSEvent.GameEvent.SWITCH_BOARD, this.onBoardSwitched, this);
    }

    private createDiff() {
        if (this.diffCells.length > 0) return; // 已存在则不重复创建
        if (!this.ensureBoardEntities()) return;

        this.buildDiffCells();
    }

    /**确保 main/ mirror 棋盘实体已缓存 */
    private ensureBoardEntities(): boolean {
        if (this.mainBoardEntity != null && this.mirrorBoardEntity != null) return true;
        this.world.query([BoardComponent]).forEach((eid) => {
            const comp = this.world.getComponent(eid, BoardComponent);
            if (comp.isCanPut) {
                this.mainBoardEntity = eid;
            } else {
                this.mirrorBoardEntity = eid;
            }
        });
        return this.mainBoardEntity != null && this.mirrorBoardEntity != null;
    }

    /**生成缺口占用块*/
    private buildDiffCells() {
        const mainBoard = this.world.getComponent(this.mainBoardEntity, BoardComponent);
        for (let r = 0; r < mainBoard.rCount; r++) {
            for (let c = 0; c < mainBoard.cCount; c++) {
                const key = `${r}_${c}`;
                const mainSlotId = this.world.getTargets(this.mainBoardEntity, RelationName.PARENT_CHILD, key)[0];
                const mirrorSlotId = this.world.getTargets(this.mirrorBoardEntity, RelationName.PARENT_CHILD, key)[0];
                if (mainSlotId == null || mirrorSlotId == null) continue;

                const mainOcc = this.world.utilCenter.boardUtil.getSlotOccupy(this.world.getComponent(mainSlotId, SlotComponent));
                const mirrorOcc = this.world.utilCenter.boardUtil.getSlotOccupy(this.world.getComponent(mirrorSlotId, SlotComponent));

                // 镜像有而主盘无 → 在主盘生成占位
                if (mirrorOcc && !mainOcc) {
                    this.spawnDiffCell(this.mainBoardEntity, mainSlotId, r, c, mainBoard.rCount, mainBoard.cCount);
                }
                // 主盘有而镜像无 → 在镜像盘生成占位
                if (mainOcc && !mirrorOcc) {
                    this.spawnDiffCell(this.mirrorBoardEntity, mirrorSlotId, r, c, mainBoard.rCount, mainBoard.cCount);
                }
            }
        }
    }

    /**在指定棋盘 slot 生成半透明占位块*/
    private spawnDiffCell(parentBoardE: number, slotId: number, r: number, c: number, rows: number, cols: number) {
        const board = this.world.getComponent(parentBoardE, BoardComponent);
        const cellId = this.world.templeteCenter.createTempleteEntity(TempleteType.Cell, {
            rc: { r, c },
            parentSize: { width: cols, height: rows },
            parentEntity: parentBoardE,
            cellOption: { color: board.boardCellColor, type: TargetType.Normal },
            slotEntity: slotId,
        });
        this.world.addComponent(cellId, OpacityComponent, 80);
        this.diffCells.push(cellId);
        this.diffMap.set(cellId, slotId);
        // 仅占位不参与消除
        const cellComp = this.world.getComponent(cellId, CellComponent);
        if (cellComp) {
            // 占位块仅做视觉提示：不连通、不占用
            cellComp.through = false;
        }
    }

    /** 清除所有差异格子 */
    private clearDiff() {
        if (this.diffCells.length === 0) return;
        this.diffCells.forEach((eid) => {
            this.world.removeEntity(eid);
        });
        this.diffMap.clear();
        this.diffCells.length = 0;
    }

    update(): void {}

    private onBoardSwitched() {
        // 清理并重置缓存
        this.clearDiff();
        this.mainBoardEntity = null;
        this.mirrorBoardEntity = null;
    }
}
