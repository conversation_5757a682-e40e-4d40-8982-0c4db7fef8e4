#!/usr/bin / expect
set timeout 10000

# mac-studio 打包机
set server_ip "**************"
set username "hungrystudio"
set platform [lindex $argv 0]
set ENV [lindex $argv 1]
set password [lindex $argv 2]
set version [lindex $argv 3]
set remote_dir "/Users/<USER>/blockblast-adv/hotUpdate/${platform}/${ENV}"

set current_dir [pwd]
set local_temp_versionConfig "$current_dir/build/jsb-link/version.config"
set local_temp_dir "$current_dir/build/jsb-link/$version"
set local_dist_zip [pwd]/build/jsb-link/hotUpdate-output.tar.gz

# 删除旧目录（如果存在）
file delete -force $local_temp_dir
# 创建临时目录
file mkdir $local_temp_dir

# 拷贝资源文件和目录
file copy -force "$current_dir/build/jsb-link/assets" "$local_temp_dir/assets"
file copy -force "$current_dir/build/jsb-link/src" "$local_temp_dir/src"
file copy -force "$current_dir/build/jsb-link/main.js" "$local_temp_dir/main.js"
file copy -force "$current_dir/build/jsb-link/src/project.manifest" "$local_temp_dir/src/project.manifest"
file copy -force "$current_dir/build/jsb-link/src/version.manifest" "$local_temp_dir/src/version.manifest"

send_user "热更资源拷贝至路径:$local_temp_dir\n"

# 进入 jsb-link 目录
cd "$current_dir/build/jsb-link"

# 执行 tar 命令压缩文件夹
spawn tar -czf hotUpdate-output.tar.gz $version

expect eof

if { $password eq "" } {
puts "免密登录"
spawn ssh $username@$server_ip "mkdir -p blockblast-adv/hotUpdate/${platform}/${ENV}"
expect eof

# 免密登录
spawn bash -c "scp -r $local_temp_versionConfig $username@$server_ip:$remote_dir"
expect eof

spawn bash -c "scp -r $local_dist_zip $username@$server_ip:$remote_dir"
send "exit\r"
expect eof

spawn ssh $username@$server_ip "mkdir -p blockblast-adv/hotUpdate/${platform}/${ENV} && cd blockblast-adv/hotUpdate/${platform}/${ENV} && tar -xzf hotUpdate-output.tar.gz"
expect eof
} else {
puts "需要登录密码"
spawn ssh $username@$server_ip "mkdir -p blockblast-adv/hotUpdate/${platform}/${ENV}"
expect "*assword:"
send "$password\r"
expect eof

if {[catch {exec cat $local_temp_versionConfig} result]} {
puts "versionConfig 读取失败: $result"
} else {
puts "versionConfig 文件内容如下："
puts $result
}

spawn bash -c "scp -r $local_temp_versionConfig $username@$server_ip:$remote_dir"
expect "*assword:"
send "$password\r"
expect {
"100%" {
puts "$local_temp_versionConfig 上传完成"
}
timeout {
puts "$local_temp_versionConfig 上传失败"
exit 1
}
}
expect eof

spawn bash -c "scp -r $local_dist_zip $username@$server_ip:$remote_dir"
expect "*assword:"
send "$password\r"
expect {
"100%" {
puts "File transfer completed."
}
timeout {
puts "File transfer timed out."
exit 1
}
}

spawn ssh $username@$server_ip "mkdir -p blockblast-adv/hotUpdate/${platform}/${ENV} && cd blockblast-adv/hotUpdate/${platform}/${ENV} && tar -xzf hotUpdate-output.tar.gz"
expect "*assword:"
send "$password\r"
expect eof
}
