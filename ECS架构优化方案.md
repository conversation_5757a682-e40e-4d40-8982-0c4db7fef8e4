# ECS 架构优化方案 - 解决业务开发限制

## 🎯 当前问题分析

### 1. 主要痛点
- **ECSView间无法直接通信**: 只能通过事件系统，增加了复杂度
- **数据访问限制**: 只能访问自己的logicEntityId相关组件
- **事件泛滥**: 简单的交互也需要发事件，代码冗余
- **调试困难**: 事件链路复杂，难以追踪数据流
- **开发效率低**: 写业务需要考虑太多架构限制

### 2. 根本原因
- **过度封装**: ECS纯度过高，忽略了业务便利性
- **缺少中间层**: 没有业务友好的抽象层
- **通信机制单一**: 只有事件一种通信方式

---

## 🚀 优化方案

### 方案一：ECSView 管理器 + 服务定位器

#### 1.1 ECSView管理器
```typescript
/**
 * ECSView管理器 - 提供View间通信能力
 */
export class ECSViewManager {
    private static instance: ECSViewManager;
    private viewRegistry = new Map<string, ECSViewBase>();
    private entityToView = new Map<number, ECSViewBase>();
    private viewGroups = new Map<string, Set<ECSViewBase>>();

    static getInstance(): ECSViewManager {
        if (!this.instance) {
            this.instance = new ECSViewManager();
        }
        return this.instance;
    }

    // 注册View
    registerView(view: ECSViewBase, group?: string): void {
        const key = `${view.constructor.name}_${view.logicEntityId}`;
        this.viewRegistry.set(key, view);
        this.entityToView.set(view.logicEntityId, view);
        
        if (group) {
            if (!this.viewGroups.has(group)) {
                this.viewGroups.set(group, new Set());
            }
            this.viewGroups.get(group)!.add(view);
        }
    }

    // 获取指定类型的View
    getView<T extends ECSViewBase>(viewClass: new () => T, entityId?: number): T | null {
        if (entityId) {
            const key = `${viewClass.name}_${entityId}`;
            return this.viewRegistry.get(key) as T || null;
        }
        
        // 返回第一个匹配的View
        for (const [key, view] of this.viewRegistry) {
            if (key.startsWith(viewClass.name)) {
                return view as T;
            }
        }
        return null;
    }

    // 获取View组
    getViewGroup(groupName: string): ECSViewBase[] {
        return Array.from(this.viewGroups.get(groupName) || []);
    }

    // 广播消息到组
    broadcastToGroup(groupName: string, message: string, data?: any): void {
        const views = this.getViewGroup(groupName);
        views.forEach(view => {
            if (view.node && view.node.isValid) {
                (view as any).onMessage?.(message, data);
            }
        });
    }
}
```

#### 1.2 增强的ECSViewBase
```typescript
/**
 * 增强的ECSView基类
 */
export abstract class EnhancedECSViewBase<TConfig extends IECSViewConfig = IECSViewConfig> extends ECSViewBase<TConfig> {
    protected viewManager = ECSViewManager.getInstance();
    protected serviceLocator = ServiceLocator.getInstance();
    
    // View组名，用于分组管理
    protected abstract getViewGroup(): string;
    
    // 消息处理接口
    protected onMessage(message: string, data?: any): void {
        // 子类可重写处理消息
    }

    protected start() {
        super.start();
        // 注册到管理器
        this.viewManager.registerView(this, this.getViewGroup());
    }

    // 便捷方法：获取其他View
    protected getOtherView<T extends ECSViewBase>(viewClass: new () => T, entityId?: number): T | null {
        return this.viewManager.getView(viewClass, entityId);
    }

    // 便捷方法：向其他View发送消息
    protected sendMessageToView<T extends ECSViewBase>(
        viewClass: new () => T, 
        message: string, 
        data?: any,
        entityId?: number
    ): boolean {
        const targetView = this.getOtherView(viewClass, entityId);
        if (targetView) {
            (targetView as any).onMessage?.(message, data);
            return true;
        }
        return false;
    }

    // 便捷方法：广播消息到组
    protected broadcastToGroup(message: string, data?: any): void {
        this.viewManager.broadcastToGroup(this.getViewGroup(), message, data);
    }

    // 便捷方法：获取业务服务
    protected getService<T>(serviceClass: new () => T): T {
        return this.serviceLocator.getService(serviceClass);
    }

    public dispose(): void {
        // 从管理器中移除
        this.viewManager.unregisterView(this);
        super.dispose();
    }
}
```

### 方案二：业务服务层

#### 2.1 服务定位器
```typescript
/**
 * 服务定位器 - 提供全局服务访问
 */
export class ServiceLocator {
    private static instance: ServiceLocator;
    private services = new Map<string, any>();

    static getInstance(): ServiceLocator {
        if (!this.instance) {
            this.instance = new ServiceLocator();
        }
        return this.instance;
    }

    // 注册服务
    registerService<T>(serviceClass: new () => T, instance?: T): void {
        const key = serviceClass.name;
        this.services.set(key, instance || new serviceClass());
    }

    // 获取服务
    getService<T>(serviceClass: new () => T): T {
        const key = serviceClass.name;
        if (!this.services.has(key)) {
            this.registerService(serviceClass);
        }
        return this.services.get(key);
    }
}
```

#### 2.2 业务服务示例
```typescript
/**
 * 棋盘服务 - 封装棋盘相关业务逻辑
 */
export class BoardService {
    private world: World;
    private renderWorld: RenderWorld;

    constructor() {
        // 从全局获取world实例
        this.world = GlobalContext.getWorld();
        this.renderWorld = GlobalContext.getRenderWorld();
    }

    // 获取棋盘实体
    getBoardEntity(boardId?: string): number | null {
        const boards = this.world.query([Board]);
        if (boardId) {
            return boards.find(id => {
                const board = this.world.getComponent(id, Board);
                return board?.boardId === boardId;
            }) || null;
        }
        return boards[0] || null;
    }

    // 获取指定位置的方块
    getCellAt(boardId: string, row: number, col: number): number | null {
        const boardEntity = this.getBoardEntity(boardId);
        if (!boardEntity) return null;
        
        const board = this.world.getComponent(boardEntity, Board);
        if (!board || !board.cells[row] || !board.cells[row][col]) {
            return null;
        }
        
        return board.cells[row][col];
    }

    // 获取棋盘上所有指定类型的方块
    getCellsByType(boardId: string, cellType: CellType): number[] {
        const boardEntity = this.getBoardEntity(boardId);
        if (!boardEntity) return [];
        
        const board = this.world.getComponent(boardEntity, Board);
        if (!board) return [];
        
        const result: number[] = [];
        for (let r = 0; r < board.height; r++) {
            for (let c = 0; c < board.width; c++) {
                const cellEntity = board.cells[r][c];
                if (cellEntity) {
                    const cell = this.world.getComponent(cellEntity, Cell);
                    if (cell?.type === cellType) {
                        result.push(cellEntity);
                    }
                }
            }
        }
        return result;
    }

    // 检查是否可以放置方块
    canPlaceBlock(boardId: string, shape: number[][], startRow: number, startCol: number): boolean {
        const boardEntity = this.getBoardEntity(boardId);
        if (!boardEntity) return false;
        
        const board = this.world.getComponent(boardEntity, Board);
        if (!board) return false;
        
        for (let r = 0; r < shape.length; r++) {
            for (let c = 0; c < shape[r].length; c++) {
                if (shape[r][c] === 1) {
                    const targetRow = startRow + r;
                    const targetCol = startCol + c;
                    
                    if (targetRow >= board.height || targetCol >= board.width) {
                        return false;
                    }
                    
                    if (board.cells[targetRow][targetCol] !== 0) {
                        return false;
                    }
                }
            }
        }
        return true;
    }

    // 获取棋盘View
    getBoardView(boardId?: string): BoardECSView | null {
        const boardEntity = this.getBoardEntity(boardId);
        if (!boardEntity) return null;
        
        return ECSViewManager.getInstance().getView(BoardECSView, boardEntity);
    }
}

/**
 * UI服务 - 封装UI相关操作
 */
export class UIService {
    // 显示飘字效果
    showFloatingText(text: string, position: cc.Vec2, color: cc.Color = cc.Color.WHITE): void {
        // 实现飘字逻辑
    }

    // 显示粒子效果
    showParticleEffect(effectName: string, position: cc.Vec2, duration: number = 1.0): void {
        // 实现粒子效果
    }

    // 震屏效果
    shakeScreen(intensity: number = 1.0, duration: number = 0.5): void {
        // 实现震屏效果
    }
}
```

### 方案三：智能事件系统

#### 3.1 类型安全的事件系统
```typescript
/**
 * 智能事件总线 - 提供类型安全和便捷的事件处理
 */
export class SmartEventBus {
    private eventBus: EventBus;
    private eventHistory: Array<{event: string, data: any, timestamp: number}> = [];
    private maxHistorySize = 100;

    constructor(eventBus: EventBus) {
        this.eventBus = eventBus;
    }

    // 发送事件并记录历史
    emit<T extends keyof GameEventMap>(event: T, data?: GameEventMap[T]): void {
        this.eventBus.emit(event, data);
        
        // 记录事件历史
        this.eventHistory.push({
            event: event as string,
            data,
            timestamp: Date.now()
        });
        
        if (this.eventHistory.length > this.maxHistorySize) {
            this.eventHistory.shift();
        }
    }

    // 便捷方法：请求-响应模式
    async request<TRequest, TResponse>(
        requestEvent: string, 
        requestData: TRequest, 
        responseEvent: string,
        timeout: number = 5000
    ): Promise<TResponse> {
        return new Promise((resolve, reject) => {
            const timeoutId = setTimeout(() => {
                this.eventBus.off(responseEvent, responseHandler);
                reject(new Error(`Request timeout: ${requestEvent}`));
            }, timeout);

            const responseHandler = (data: TResponse) => {
                clearTimeout(timeoutId);
                this.eventBus.off(responseEvent, responseHandler);
                resolve(data);
            };

            this.eventBus.on(responseEvent, responseHandler);
            this.emit(requestEvent as any, requestData as any);
        });
    }

    // 获取事件历史
    getEventHistory(eventType?: string): Array<{event: string, data: any, timestamp: number}> {
        if (eventType) {
            return this.eventHistory.filter(item => item.event === eventType);
        }
        return [...this.eventHistory];
    }
}
```

#### 3.2 事件装饰器
```typescript
/**
 * 事件处理装饰器
 */
export function EventHandler<T extends keyof GameEventMap>(eventType: T) {
    return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
        const originalMethod = descriptor.value;
        
        // 在类初始化时自动绑定事件
        if (!target._eventHandlers) {
            target._eventHandlers = [];
        }
        
        target._eventHandlers.push({
            eventType,
            handler: originalMethod,
            methodName: propertyKey
        });
        
        return descriptor;
    };
}

/**
 * 自动事件绑定的基类
 */
export class AutoEventBindingView extends EnhancedECSViewBase {
    private _eventHandlers: Array<{eventType: string, handler: Function, methodName: string}> = [];

    protected addEvents(): void {
        super.addEvents();
        
        // 自动绑定装饰器标记的事件处理器
        if (this._eventHandlers) {
            this._eventHandlers.forEach(({eventType, handler, methodName}) => {
                this.logicWorld.eventBus.on(eventType as any, handler.bind(this), this);
            });
        }
    }

    protected removeEvents(): void {
        super.removeEvents();
        
        // 自动移除事件绑定
        if (this._eventHandlers) {
            this._eventHandlers.forEach(({eventType, handler}) => {
                this.logicWorld.eventBus.off(eventType as any, handler, this);
            });
        }
    }
}
```

### 方案四：业务组件封装

#### 4.1 高级业务组件
```typescript
/**
 * 棋盘View - 使用新的架构
 */
export class BoardECSView extends AutoEventBindingView {
    private boardService: BoardService;
    private uiService: UIService;

    protected getViewGroup(): string {
        return 'board';
    }

    protected async initView() {
        this.boardService = this.getService(BoardService);
        this.uiService = this.getService(UIService);
        
        // 初始化棋盘UI
        await this.initBoardUI();
    }

    // 使用装饰器自动绑定事件
    @EventHandler(ECSEvent.GameEvent.PUT_BLOCK_BACK)
    private onBlockPlaced(data: IPutBlockBackInfo): void {
        // 处理方块放置
        this.playPlaceAnimation(data);
    }

    @EventHandler(ECSEvent.GameEvent.ELIMINATION)
    private onElimination(data: IEliminationInfo): void {
        // 处理消除效果
        this.playEliminationEffect(data);
    }

    // 对外提供的业务接口
    public highlightValidPositions(shape: number[][]): void {
        const validPositions = this.findValidPositions(shape);
        validPositions.forEach(pos => {
            this.highlightCell(pos.row, pos.col);
        });
    }

    public clearHighlights(): void {
        // 清除所有高亮
    }

    // 与其他View通信的示例
    public notifyScoreChange(score: number): void {
        // 直接调用ScoreView的方法
        const scoreView = this.getOtherView(ScoreECSView);
        if (scoreView) {
            (scoreView as any).updateScore(score);
        }
        
        // 或者发送消息
        this.sendMessageToView(ScoreECSView, 'updateScore', { score });
    }

    private findValidPositions(shape: number[][]): Array<{row: number, col: number}> {
        const positions: Array<{row: number, col: number}> = [];
        const board = this.logicWorld.getComponent(this.logicEntityId, Board);
        
        if (!board) return positions;
        
        for (let r = 0; r < board.height; r++) {
            for (let c = 0; c < board.width; c++) {
                if (this.boardService.canPlaceBlock(board.boardId, shape, r, c)) {
                    positions.push({ row: r, col: c });
                }
            }
        }
        
        return positions;
    }
}

/**
 * 方块View - 展示如何与棋盘View通信
 */
export class ShapeECSView extends AutoEventBindingView {
    protected getViewGroup(): string {
        return 'shape';
    }

    protected onMessage(message: string, data?: any): void {
        switch (message) {
            case 'startDrag':
                this.onDragStart();
                break;
            case 'endDrag':
                this.onDragEnd();
                break;
        }
    }

    private onDragStart(): void {
        // 通知棋盘显示可放置位置
        const boardView = this.getOtherView(BoardECSView);
        if (boardView) {
            const shape = this.getShapeData();
            (boardView as any).highlightValidPositions(shape);
        }
    }

    private onDragEnd(): void {
        // 通知棋盘清除高亮
        const boardView = this.getOtherView(BoardECSView);
        if (boardView) {
            (boardView as any).clearHighlights();
        }
    }

    private getShapeData(): number[][] {
        const shapeComp = this.logicWorld.getComponent(this.logicEntityId, ShapeComponent);
        return shapeComp?.shape.data || [];
    }
}
```

---

## 🎨 使用示例

### 示例1：View间直接通信
```typescript
// 之前：只能通过事件
this.logicWorld.eventBus.emit(ECSEvent.GameEvent.HIGHLIGHT_CELLS, { positions });

// 现在：可以直接调用
const boardView = this.getOtherView(BoardECSView);
boardView?.highlightValidPositions(shape);
```

### 示例2：业务逻辑封装
```typescript
// 之前：在View中写复杂逻辑
const boards = this.logicWorld.query([Board]);
const boardEntity = boards.find(id => {
    const board = this.logicWorld.getComponent(id, Board);
    return board?.boardId === 'main';
});

// 现在：使用服务
const boardService = this.getService(BoardService);
const boardEntity = boardService.getBoardEntity('main');
```

### 示例3：自动事件绑定
```typescript
// 之前：手动绑定事件
protected addEvents(): void {
    this.logicWorld.eventBus.on(ECSEvent.GameEvent.PUT_BLOCK_BACK, this.onBlockPlaced, this);
    this.logicWorld.eventBus.on(ECSEvent.GameEvent.ELIMINATION, this.onElimination, this);
}

// 现在：装饰器自动绑定
@EventHandler(ECSEvent.GameEvent.PUT_BLOCK_BACK)
private onBlockPlaced(data: IPutBlockBackInfo): void { }

@EventHandler(ECSEvent.GameEvent.ELIMINATION)  
private onElimination(data: IEliminationInfo): void { }
```

---

## 📈 优化效果

### 1. 开发效率提升
- **减少样板代码**: 自动事件绑定，减少手动绑定代码
- **直接通信**: View间可以直接调用，不必都走事件
- **业务封装**: 常用业务逻辑封装成服务，复用性强

### 2. 代码可维护性
- **清晰的调用链**: 直接调用比事件链更容易追踪
- **类型安全**: TypeScript类型检查，减少运行时错误
- **统一管理**: 服务定位器统一管理业务逻辑

### 3. 调试友好
- **事件历史**: 可以查看事件调用历史
- **直接断点**: 直接调用可以直接打断点调试
- **服务监控**: 可以监控服务调用情况

---

## 🔧 实施建议

### 阶段1：基础设施搭建
1. 实现 `ECSViewManager` 和 `ServiceLocator`
2. 创建 `EnhancedECSViewBase` 基类
3. 实现事件装饰器系统

### 阶段2：业务服务封装
1. 识别常用业务逻辑，封装成服务
2. 创建 `BoardService`、`UIService` 等核心服务
3. 逐步迁移现有业务逻辑到服务层

### 阶段3：View层改造
1. 新的View继承 `EnhancedECSViewBase`
2. 使用装饰器替换手动事件绑定
3. 利用View管理器实现直接通信

### 阶段4：优化和完善
1. 添加性能监控和调试工具
2. 完善类型定义和文档
3. 建立最佳实践指南

这套优化方案在保持ECS架构优势的同时，大大提升了业务开发的便利性和效率。你觉得这个方向如何？有什么特别想要优化的场景吗？