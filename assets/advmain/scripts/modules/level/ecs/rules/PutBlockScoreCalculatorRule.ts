import { ECSEvent } from '../GameEvent';
import { ECSRuleBase, IECSRuleConfig } from './core/ECSRuleBase';
import BoardComponent from '../components/board/BoardComponent';
import AwardComponent from '../components/special/AwardComponent';
import TargetComponent from '../components/special/TargetComponent';
import { RelationName } from '../cores/center/EntityCenter/WorldRelation';
import { TargetType } from '../define/BoardDefine';
import { AwardCondition } from '../define/EcsDefine';

/**
 * 放置块分数计算规则
 */
export class PutBlockScoreCalculatorRule extends ECSRuleBase<IECSRuleConfig, ECSEvent.GameEvent.PUT_BLOCK_BACK> {
    public bindEventData() {
        return ECSEvent.GameEvent.PUT_BLOCK_BACK;
    }

    canExecute(): boolean {
        return this.eventData.isSuccess;
    }

    execute(): void {
        const world = this.world;
        const putBlockInfo = this.eventData;
        if (!putBlockInfo.isSuccess) return;
        const boardEntity = world.query([BoardComponent]).find((eid) => world.getComponent(eid, BoardComponent).boardId === putBlockInfo.boardId);
        let score = 0;
        putBlockInfo.shape.shape.forEach((rc) => {
            const targetRow = putBlockInfo.y + rc.r;
            const targetCol = putBlockInfo.x + rc.c;
            const slotEntity = world.getTargets(boardEntity, RelationName.PARENT_CHILD, `${targetRow}_${targetCol}`)[0];
            world.getTargets(slotEntity, RelationName.SLOT_CELL).forEach((cellEntity) => {
                world.getComponent(cellEntity, AwardComponent)?.triggerList.forEach((trigger) => {
                    if (trigger.condition !== AwardCondition.Put || trigger.target !== TargetType.Score) return;
                    score += trigger.change;
                });
            });
        });
        const targets = world.getComponent(world.query([TargetComponent])[0], TargetComponent).targets;
        const target = targets.find((t) => t.key === TargetType.Score);
        if (!target) return; //如果没有得分目标，直接返回
        const current = Math.min(target.to, target.current + score);
        const targetParam = { key: TargetType.Score, current, to: target.to };
        world.eventBus.emit(ECSEvent.GameEvent.FRESH_TARGET, targetParam);
        world.eventBus.emit(ECSEvent.GameEvent.TARGET_CHANGE, { key: TargetType.Score, change: score });
    }
}
