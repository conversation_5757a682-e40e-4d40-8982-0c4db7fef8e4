import BoardComponent from '../../../../advmain/scripts/modules/level/ecs/components/board/BoardComponent';
import ShapeComponent from '../../../../advmain/scripts/modules/level/ecs/components/board/ShapeComponent';
import DragComponent from '../../../../advmain/scripts/modules/level/ecs/components/DragComponent';
import NodeComponent from '../../../../advmain/scripts/modules/level/ecs/components/NodeComponent';
import { RelationName } from '../../../../advmain/scripts/modules/level/ecs/cores/center/EntityCenter/WorldRelation';
import { System } from '../../../../advmain/scripts/modules/level/ecs/cores/System';
import { TargetType, CellSize } from '../../../../advmain/scripts/modules/level/ecs/define/BoardDefine';
import { RcShape } from '../../../../advmain/scripts/modules/level/ecs/define/EcsConfig';
import { IPoint } from '../../../../advmain/scripts/modules/level/ecs/define/EcsDefine';
import { ECSEvent } from '../../../../advmain/scripts/modules/level/ecs/GameEvent';
import { IDragBlockStartInfo, IPutBlockBackInfo, IPutBlockInfo } from '../../../../advmain/scripts/modules/level/ecs/GameEventData';
import { TempleteType } from '../../../../advmain/scripts/modules/level/ecs/registry/templete/TempleteRegistry';
import { MirrorShapeComponent } from '../component/MirrorShapeComponent';

/**
 * MirrorSyncSystem
 * 监听主棋盘放块成功事件，将相同形状同步到所有 isCanPut===false 的棋盘。
 * 仅复制最终放置结果
 */
export default class MirrorSyncSystem extends System {
    private mirrorDragMap: Map<string, number> = new Map(); // boardId -> mirror shape entity
    private mirrorShape: number = 0;
    /**记录拖拽开始的位置信息*/
    private _startPos: IPoint = { x: 0, y: 0 };
    private _startClickAnchorDy: number = 0; // 镜像块锚点偏移
    private _mirrorStartPositions: Map<string, IPoint> = new Map(); // boardId -> startPos

    init(): void {
        this.world.eventBus.on(ECSEvent.GameEvent.PUT_BLOCK_BACK, this.onMainPutBack, this);
        // 监听拖拽生命周期事件
        this.world.eventBus.on(ECSEvent.GameEvent.DRAG_BLOCK_START, this.onDragStart, this);
        this.world.eventBus.on(ECSEvent.GameEvent.DRAG_BLOCK_MOVE, this.onDragMove, this);
        this.world.eventBus.on(ECSEvent.GameEvent.DRAG_BLOCK_END, this.onDragEndCancel, this);
        this.world.eventBus.on(ECSEvent.GameEvent.DRAG_BLOCK_CANCEL, this.onDragEndCancel, this);
    }
    dispose(): void {
        this.world.eventBus.off(ECSEvent.GameEvent.PUT_BLOCK_BACK, this.onMainPutBack, this);

        this.world.eventBus.off(ECSEvent.GameEvent.DRAG_BLOCK_START, this.onDragStart, this);
        this.world.eventBus.off(ECSEvent.GameEvent.DRAG_BLOCK_MOVE, this.onDragMove, this);
        this.world.eventBus.off(ECSEvent.GameEvent.DRAG_BLOCK_END, this.onDragEndCancel, this);
        this.world.eventBus.off(ECSEvent.GameEvent.DRAG_BLOCK_CANCEL, this.onDragEndCancel, this);
    }

    private onMainPutBack(info: IPutBlockBackInfo) {
        // 拦截系统自身或失败事件
        if (!info.isSuccess) return;

        // 检查是否是副棋盘（镜像棋盘）产生的事件，如果是则忽略
        const boardEntity = this.world.query([BoardComponent]).find((eid) => this.world.getComponent(eid, BoardComponent).boardId === info.boardId);
        const boardComp = this.world.getComponent(boardEntity, BoardComponent);
        if (boardComp && !boardComp.isCanPut) {
            // 这是副棋盘产生的事件，忽略避免循环
            return;
        }

        // 将放块指令发送给所有 mirror 棋盘
        this.world.query([BoardComponent]).forEach((eid) => {
            const board = this.world.getComponent(eid, BoardComponent);
            if (board.isCanPut) return; // 只同步到不可放置棋盘
            const cmd: IPutBlockInfo = {
                x: info.x,
                y: info.y,
                shape: info.shape,
                entityId: this.mirrorShape,
                boardId: board.boardId,
            };
            this.world.eventBus.emit(ECSEvent.GameEvent.PUT_BLOCK, cmd);
        });
    }

    private onDragStart(info: IDragBlockStartInfo & { wPos?: IPoint }) {
        // 兼容两种数据格式
        const startPos = info.wPos || info.pos;

        // 获取主棋盘拖拽块的节点位置 - 与主棋盘保持完全一致的计算基准
        const mainNodeComponent = this.world.getComponent(info.entityId, NodeComponent);
        this._startPos = { x: startPos.x, y: mainNodeComponent.y };
        this._startClickAnchorDy = startPos.y - mainNodeComponent.y;

        let sceneEntity = this.world.utilCenter.boardUtil.getSceneEntity();
        this.world.query([BoardComponent]).forEach((eid) => {
            const mirror = this.world.getComponent(eid, BoardComponent);
            if (mirror.isCanPut) return;
            if (this.mirrorDragMap.has(mirror.boardId)) return;
            const cellSize = this.getBoardCellSize(eid);
            // 原始拖拽形状信息
            const shapeComp = this.world.getComponent(info.entityId, ShapeComponent);
            if (!shapeComp) return;
            // 创建镜像 Shape 实体
            const shapeEntity = this.world.templeteCenter.createTempleteEntity(
                TempleteType.Shape,
                {
                    rcShape: shapeComp.shape as (typeof RcShape)[number],
                    x: 0,
                    parentEntity: sceneEntity,
                },
                {
                    cellSize: cellSize,
                },
            );
            this.mirrorShape = shapeEntity;
            this.world.addComponent(shapeEntity, MirrorShapeComponent);
            // 按形状坐标创建蓝色 Cell 子实体
            shapeComp.shape.shape.forEach((rc) => {
                const slotEntity = this.world.templeteCenter.createTempleteEntity(TempleteType.Slot, {
                    rc,
                    parentEntity: shapeEntity,
                    parentSize: shapeComp.shape,
                });
                this.world.templeteCenter.createTempleteEntity(TempleteType.Cell, {
                    rc,
                    parentSize: shapeComp.shape,
                    parentEntity: shapeEntity,
                    cellOption: { color: mirror.boardCellColor, type: TargetType.Normal },
                    slotEntity,
                });
            });

            // 记录映射并初始定位
            this.mirrorDragMap.set(mirror.boardId, shapeEntity);

            // 计算镜像块的初始位置（完全跟随主块原始位置）
            const mainBlockPos = {
                x: startPos.x,
                y: this._startPos.y,
            };
            const mirrorInitialPos = this.calculateMirrorPosition(mirror, mainBlockPos);
            this._mirrorStartPositions.set(mirror.boardId, { x: mirrorInitialPos.x, y: mirrorInitialPos.y });
            this.updateMirrorPosition(shapeEntity, mirrorInitialPos);
        });
    }

    private onDragMove(aPos: IPoint) {
        if (!aPos) return;

        // 获取主拖拽块的拖动倍率
        const mainDragEntity = this.world.query([DragComponent])[0];
        const dragComp = mainDragEntity ? this.world.getComponent(mainDragEntity, DragComponent) : null;
        const dragMultiplier = dragComp?.dragMultiplier || 1.4;

        // 应用拖动倍率计算
        const dy = (aPos.y - this._startPos.y) * dragMultiplier;
        const dx = (aPos.x - this._startPos.x) * dragMultiplier;
        const newX = this._startPos.x + dx;
        const newY = this._startPos.y + dy - this._startClickAnchorDy * dragMultiplier + 200;

        const adjustedPos = {
            x: newX,
            y: newY,
        };

        this.world.query([BoardComponent]).forEach((eid) => {
            const mirror = this.world.getComponent(eid, BoardComponent);
            if (mirror.isCanPut) return;
            const shapeId = this.mirrorDragMap.get(mirror.boardId);
            if (shapeId) {
                const mirrorPos = this.calculateMirrorPosition(mirror, adjustedPos);
                this.updateMirrorPosition(shapeId, mirrorPos);
            }
        });
    }

    /**计算镜像位置（考虑棋盘比例）*/
    private calculateMirrorPosition(mirror: BoardComponent, pos: { x: number; y: number }): { x: number; y: number } {
        let boardsCfg = this.world.utilCenter.boardUtil.getBoardConfigs();
        const mainBoard = boardsCfg.find((b) => b.isCanPut);
        const mirrorBoard = boardsCfg.find((b) => !b.isCanPut);
        if (!mainBoard || !mirrorBoard) return pos;

        const ratio = mirrorBoard.cellSize / mainBoard.cellSize;
        // 将中心点转换为左下角基准坐标进行换算
        const mainBoardLeftBottomX = mainBoard.startPos.x - (mainBoard.cols * mainBoard.cellSize) / 2;
        const mainBoardLeftBottomY = mainBoard.startPos.y - (mainBoard.rows * mainBoard.cellSize) / 2;
        const mirrorBoardLeftBottomX = mirrorBoard.startPos.x - (mirrorBoard.cols * mirrorBoard.cellSize) / 2;
        const mirrorBoardLeftBottomY = mirrorBoard.startPos.y - (mirrorBoard.rows * mirrorBoard.cellSize) / 2;

        const deltaX = pos.x - mainBoardLeftBottomX;
        const deltaY = pos.y - mainBoardLeftBottomY;

        return {
            x: mirrorBoardLeftBottomX + deltaX * ratio,
            y: mirrorBoardLeftBottomY + deltaY * ratio,
        };
    }

    /**计算并更新镜像块位置*/
    private updateMirrorPosition(shapeId: number, mirrorPos: { x: number; y: number }) {
        let boardsCfg = this.world.utilCenter.boardUtil.getBoardConfigs();
        const shapeEnity = this.world.getComponent(shapeId, NodeComponent);
        const mainBoard = boardsCfg.find((b) => b.isCanPut);
        const mirrorBoard = boardsCfg.find((b) => !b.isCanPut);
        if (!mainBoard || !mirrorBoard) return;

        const ratio = mirrorBoard.cellSize / mainBoard.cellSize;

        // 直接设置计算好的镜像位置
        shapeEnity.x = mirrorPos.x;
        shapeEnity.y = mirrorPos.y;
        // 同步缩放
        shapeEnity.scaleX = ratio;
        shapeEnity.scaleY = ratio;

        const putInfo = this.world.utilCenter.boardUtil.convertToGridCoord(mirrorPos, boardsCfg, shapeId);
        const shapeComp = this.world.getComponent(shapeId, ShapeComponent)!;

        if (putInfo && putInfo.boardId === mirrorBoard.boardId) {
            const info: IPutBlockInfo = {
                x: putInfo.gridX,
                y: putInfo.gridY,
                shape: shapeComp.shape as (typeof RcShape)[number],
                entityId: shapeId,
                boardId: mirrorBoard.boardId,
            };
            this.world.eventBus.emit(ECSEvent.GameEvent.PREVIEW_BLOCK, info);
        } else {
            // 不在镜像棋盘范围内，清除其预览
            this.world.eventBus.emit(ECSEvent.GameEvent.CLEAR_PREVIEW_BLOCK, { boardId: mirrorBoard.boardId });
        }
    }

    private onDragEndCancel() {
        this.mirrorDragMap.forEach((id) => this.world.destroyEntity(id));
        this.mirrorDragMap.clear();
        this._mirrorStartPositions.clear();
    }

    private getBoardCellSize(boardId: number): number {
        const slotId = this.world.getTargets(boardId, RelationName.PARENT_CHILD, '0_0')[0];
        const slotNode = this.world.getComponent(slotId, NodeComponent);
        const nextSlotId = this.world.getTargets(boardId, RelationName.PARENT_CHILD, '0_1')[0];
        const nextNode = nextSlotId ? this.world.getComponent(nextSlotId, NodeComponent) : null;
        if (nextNode) {
            return Math.abs(nextNode.x - slotNode.x);
        }
        return CellSize;
    }

    update(): void {}
}
