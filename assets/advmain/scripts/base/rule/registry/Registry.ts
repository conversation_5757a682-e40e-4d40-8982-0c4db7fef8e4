import { IRule, IFlow, IRuleConfig, IExecutable } from '../types';
import { FlowType } from '../flow/FlowType';
import { ListFlow } from '../flow/ListFlow';
import { SelectorFlow } from '../flow/SelectorFlow';
/**
 * 统一注册器 - 管理条件、行动、规则和执行流类型
 */
export class Registry {
  // 条件类型注册表
  private conditionRegistry: Map<string, new () => IExecutable>;

  // 规则类型注册表
  private ruleRegistry: Record<string, clzz<IRule>>;

  // 执行流类型注册表
  private flowRegistry: Map<string, new () => IFlow>;
  /**
   * 构造函数
   */
  constructor() {
    // 初始化注册表
    this.conditionRegistry = new Map();
    this.ruleRegistry = {};
    this.flowRegistry = new Map();
    // 注册内置类型
    this.registerBuiltinFlowTypes();
  }
  registerRuleMap(map: Record<string, clzz<IRule>>): void {
    this.ruleRegistry = map;
  }
  /**
   * 创建规则实例
   */
  createRule(type: string, config: IRuleConfig): IRule | null {
    const ruleClzz = this.ruleRegistry[type];

    if (!ruleClzz) {
      throw new Error(`未注册的规则类型: ${type}`);
    }
    const rule = new ruleClzz();
    rule.config = config;
    return rule;
  }
  /**
   * 注册执行流类型
   */
  registerFlowType(type: string, creator: new () => IFlow): void {
    this.flowRegistry.set(type, creator);
  }

  /**
   * 创建执行流实例
   */
  createFlow(type: string, config: any): IFlow {
    const flowClzz = this.flowRegistry.get(type);

    if (!flowClzz) {
      throw new Error(`未注册的执行流类型: ${type}`);
    }
    const flow = new flowClzz();
    flow.config = config;
    return flow;
  }

  /**
   * 注册内置执行流类型
   */
  private registerBuiltinFlowTypes(): void {
    // 顺序执行流
    this.registerFlowType(FlowType.List, ListFlow);
    // 选择执行流
    this.registerFlowType(FlowType.Select, SelectorFlow);
  }

  /**
   * 清空所有注册表
   */
  clear(): void {
    this.conditionRegistry.clear();
    this.ruleRegistry = {};
    this.flowRegistry.clear();
  }
} 