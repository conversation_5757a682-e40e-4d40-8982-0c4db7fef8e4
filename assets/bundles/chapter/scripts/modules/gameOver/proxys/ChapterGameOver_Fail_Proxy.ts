import { Loader } from '../../../../../../advmain/scripts/base/Loader';
import { SubGameBridge } from '../../../../../../advmain/scripts/base/SubGameBridge';
import { uiLayer } from '../../../../../../advmain/scripts/modules/layer/vo/LayerInfo';
import TargetComponent from '../../../../../../advmain/scripts/modules/level/ecs/components/special/TargetComponent';
import { ChapterPrefabConfig } from '../../prefab/ChapterPrefabConfig';
import ChapterResult from '../components/ChapterResult';
import { E_ChapterFail_Show } from '../events/E_ChapterFail_Show';

@classId('ChapterGameOver_Fail_Proxy')
export class ChapterGameOver_Fail_Proxy extends falcon.Proxy {
    registerEvents(): { new (...args: any): falcon.ModuleEvent }[] | null {
        return [E_ChapterFail_Show];
    }

    receivedEvents($event: falcon.ModuleEvent): void {
        switch ($event.getClass()) {
            case E_ChapterFail_Show:
                this.openUI();
                break;
        }
    }

    /**显示结算界面 */
    async openUI() {
        await Loader.showUI(ChapterPrefabConfig.ChapterResult, uiLayer);
        //临时写法
        const targets = window['gameIns']._world.getComponent(window['gameIns']._world.query([TargetComponent])[0], TargetComponent).targets;
        Cinst(ChapterResult).setState({ targets, win: false });
    }
}
