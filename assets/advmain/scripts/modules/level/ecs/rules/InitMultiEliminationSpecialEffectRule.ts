import BoardComponent from '../components/board/BoardComponent';
import { BoardLayer } from '../define/BoardDefine';
import { TempleteType } from '../registry/templete/TempleteRegistry';
import { EcsViewId } from '../renderWorld/ECSViewBase';
import { ECSRuleBase, IECSRuleConfig } from './core/ECSRuleBase';

export interface IInitMultiEliminationSpecialEffectRuleConfig extends IECSRuleConfig {
    /**单次分数展示视图id */
    ecsViewId: EcsViewId;
}
export class InitMultiEliminationSpecialEffectRule extends ECSRuleBase<IInitMultiEliminationSpecialEffectRuleConfig> {
    canExecute(): boolean {
        return true;
    }
    execute(): void {
        const boardEntity = this.world.query([BoardComponent])[0];
        if (!boardEntity) return;
        const effectEntity = this.world.templeteCenter.createTempleteEntity(
            TempleteType.Render,
            {
                parentEntity: boardEntity,
                ecsViewId: this.config.ecsViewId,
            },
            {
                // nodeParam: position,
                renderParam: {
                    childrenPaths: [BoardLayer[BoardLayer.Top]],
                    isBreakLink: true,
                },
            },
        );
        // 销毁特效实体
        this.world.destroyEntity(effectEntity);
    }
}
