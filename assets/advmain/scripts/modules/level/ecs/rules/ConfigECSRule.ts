import { ConfigTableConst } from "../registry/ConfigRegistry";
import {  ECSRuleBase, IECSRuleConfig } from "./core/ECSRuleBase";

export interface IConfigECSRuleConfig extends IECSRuleConfig {
    /**
     * 配置表名
     */
    tableKey: ConfigTableConst;
    /**配置Id */
    configId: string;
    /**配置Key */
    configKey: string;
    /**配置数据 */
    configVal: any;
}
/**
 * 配置规则
 */
export class ConfigECSRule extends ECSRuleBase<IConfigECSRuleConfig> {
    canExecute(): boolean {
        return true;
    }
    execute(): void {
        this.world.configCenter.overrideConfig(this.config.tableKey, this.config.configId, this.config.configKey, this.config.configVal);
    }
}