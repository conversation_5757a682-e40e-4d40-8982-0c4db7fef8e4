import { TempleteType } from '../registry/templete/TempleteRegistry';
import { ECSRuleBase } from './core/ECSRuleBase';

/**
 * 初始化棋盘场景规则：
 * 监听 INIT_BOARD_SCENE 事件，在逻辑世界中创建场景实体并挂载渲染所需组件。
 */
export class InitBoardSceneRule extends ECSRuleBase {
    canExecute(): boolean {
        return true;
    }

    execute(): void {
        const world = this.world;
        const boardSceneEntity = world.templeteCenter.createTempleteEntity(TempleteType.BoardScene, {});
        const templeteCenter = world.templeteCenter;
        const mustArgs = {
            parentEntity: boardSceneEntity,
            ecsViewId: 'ecsview_layer',
            nodeParam: {
                x: 0,
                y: 0,
            },
        };
        world.ECSLayerType.SceneLayerEntity = templeteCenter.createTempleteEntity(TempleteType.Render, mustArgs);
        world.ECSLayerType.EffectLayerEntity = templeteCenter.createTempleteEntity(TempleteType.Render, mustArgs);
        world.ECSLayerType.UILayerEntity = templeteCenter.createTempleteEntity(TempleteType.Render, mustArgs);
    }
}
