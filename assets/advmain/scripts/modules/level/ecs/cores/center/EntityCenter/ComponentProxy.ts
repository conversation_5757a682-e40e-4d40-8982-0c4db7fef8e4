import { Component } from '../../Component';
import { EntityCenter } from './EntityCenter';

/**
 * 组件变化监听器类型
 */
export type ComponentChangeListener<T extends Component = Component> = (entityId: number, component: T, propertyName?: string, oldValue?: any, newValue?: any) => void;

/**
 * 组件代理管理器
 */
export class ComponentProxy {
    private entityCenter: EntityCenter;
    private componentProxies = new Map<string, { target: Component; listeners: Array<{ listener: ComponentChangeListener<any>; thisObj: any }> }>();

    constructor(entityCenter: EntityCenter) {
        this.entityCenter = entityCenter;
    }


    /**
     * 获取组件代理对象（如果不存在则创建）
     */
    getProxy<T extends Component>(entityId: number, componentType:clzz<T>): T {
        const key = this.getComponentProxyKey(entityId, componentType.name);
        
        if (!this.componentProxies.has(key)) {
            return;
        }
        
        const proxyData = this.componentProxies.get(key)!;
        return proxyData.target as T;
    }

    /**
     * 为组件的代理添加变化监听器
     */
    onChange<T extends Component>(entityId: number, componentType:clzz<T>, listener: ComponentChangeListener<T>, thisObj: any): void {
        const key = this.getComponentProxyKey(entityId, componentType.name);
        let proxyData = this.componentProxies.get(key)!;
        if (!proxyData) {
            proxyData = this.createComponentProxy(entityId, componentType);
        }
        proxyData.listeners.push({ listener, thisObj });
    }

    /**
     * 移除组件的代理变化监听器
     */
    offChange<T extends Component>(entityId: number, componentType:clzz<T>, listener: ComponentChangeListener<T>, thisObj: any): void {
        const key = this.getComponentProxyKey(entityId, componentType.name);
        const proxyData = this.componentProxies.get(key);
        if (proxyData) {
            const index = proxyData.listeners.findIndex(l => l.listener === listener && l.thisObj === thisObj);
            if (index !== -1) {
                proxyData.listeners.splice(index, 1);
            }
        }
    }

    /**
     * 检查组件代理是否存在
     */
    has(entityId: number, componentType:clzz<Component>): boolean {
        const key = this.getComponentProxyKey(entityId, componentType.name);
        return this.componentProxies.has(key);
    }

    /**
     * 销毁特定实体的特定组件代理
     */
    destroy(entityId: number, componentName: string): void {
        const key = this.getComponentProxyKey(entityId, componentName);
        this.componentProxies.delete(key);
    }

    /**
     * 清理特定实体的所有组件代理
     */
    clearEntity(entityId: number): void {
        const keysToRemove: string[] = [];
        this.componentProxies.forEach((proxyData, key) => {
            if (key.startsWith(`${entityId}_`)) {
                keysToRemove.push(key);
            }
        });
        keysToRemove.forEach(key => {
            this.componentProxies.delete(key);
        });
    }

    /**
     * 清理所有组件代理
     */
    clearAll(): void {
        this.componentProxies.clear();
    }
    
    /**
     * 生成组件代理的键
     */
    private getComponentProxyKey(entityId: number, componentType: string): string {
        return `${entityId}_${componentType}`;
    }

    /**
     * 创建组件代理
     */
    private createComponentProxy<T extends Component>(entityId: number, componentType:clzz<T>): { target: T; listeners: Array<{ listener: ComponentChangeListener<any>; thisObj: any }> } {
        const key = this.getComponentProxyKey(entityId, componentType.name);
        const component = this.entityCenter.getComponent(entityId, componentType);
        
        if (!component) {
            throw new Error(`实体 ${entityId} 没有组件 ${componentType.name}`);
        }
        
        // 创建代理
        const proxy = new Proxy(component, {
            set: (obj, prop, value) => {
                const proxyData = this.componentProxies.get(key);
                if (!proxyData) {
                    return true; // 代理已被销毁，直接返回
                }

                const oldValue = obj[prop as keyof T];
                obj[prop as keyof T] = value;
                
                // 如果值确实发生了变化，触发监听器
                if (oldValue !== value) {
                    this.triggerChangeListeners(key, obj, prop as string, oldValue, value);
                }
                
                return true;
            }
        });
        const proxyData = {
            target: proxy,
            listeners: []
        }
        // 存储代理数据
        this.componentProxies.set(key, proxyData);
        return proxyData;
    }

    /**
     * 触发变化监听器
     */
    private triggerChangeListeners(key: string, component: Component, propertyName: string, oldValue: any, newValue: any): void {
        const proxyData = this.componentProxies.get(key);
        if (proxyData) {
            proxyData.listeners.forEach(({ listener, thisObj }) => {
                try {
                    listener.call(thisObj, component.entity, component, propertyName, oldValue, newValue);
                } catch (error) {
                    console.error('组件变化监听器执行错误:', error);
                }
            });
        }
    }
}