#!/bin/bash
#
# 项目构建打包脚本
# 用于将Cocos Creator项目构建并打包为Android APK
# 准备：
# 1. 安装 apktool: brew install apktool
# 2. 安装 jarsigner: brew install jarsigner
# 3. 安装 openjdk: brew install openjdk
# 引擎：/Users/<USER>/Documents/projects/engine/blockpuzzle_cocos2dx/tools/cocos2d-console/plugins 里不能编译，只能用玉阳那边的
# 按 ${PLATFORM}/${ENV}/${IP_ADDRESS} 规范打包

# 记录开始时间
BUILD_START_TIME=$(date +%s)

# 严格模式，遇到错误立即退出
set -e

# 设置Java环境变量
export JAVA_HOME="/opt/homebrew/opt/openjdk/libexec/openjdk.jdk/Contents/Home"
export PATH="$JAVA_HOME/bin:$PATH"

# 平台
PLATFORM=${MACRO_PLATFORM:-android}
# 环境
ENV="${MACRO_ENV:-test}"
# 版本
version=${MACRO_VERSION}
# 融合
# combile=${combile:-false}

# 日志输出函数
log() {
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

log "开始构建过程..."

# 设置配置来源 - 根据是否在Jenkins环境中运行
IS_JENKINS_ENV=false
if [ -n "$JENKINS_HOME" ]; then
  IS_JENKINS_ENV=true
  log "检测到Jenkins环境，将使用Jenkins参数"
else
  log "本地环境运行，将使用配置文件"
fi

# 加载配置
if [ "$IS_JENKINS_ENV" = true ]; then
  # 在Jenkins环境中，使用Jenkins参数
  log "从Jenkins环境变量加载配置..."
  
  # 确保必要的参数已设置
  if [ -z "$ApkName" ]; then
    log "错误: 未设置ApkName参数" >&2
    exit 1
  fi

  if [ -z "$Apk" ]; then
    log "错误: 未设置Apk参数" >&2
    exit 1
  fi
  
  if [ -z "$version" ]; then
    log "错误: 未设置version参数" >&2
    exit 1
  fi
  
  if [ -z "$apkPublishType" ]; then
    log "错误: 未设置apkPublishType参数" >&2
    exit 1
  fi
  
  # 设置默认值（如果Jenkins中未指定）
  Apk=${Apk:-false}
  debug=${debug:-true}
  MACRO_REMOTE_SERVER_RES_URL=${MACRO_REMOTE_SERVER_RES_URL:-"http://**************:8867"}
  MACRO_TRAITS_SPLIT=${MACRO_TRAITS_SPLIT:-false}
  MACRO_AUTO=${MACRO_AUTO:-false}
  MACRO_ROBOT=false
  MACRO_SHOW_FPS_LOG=${MACRO_SHOW_FPS_LOG:-true}
  only_bundle=${only_bundle:-false}
  HOT_SERVER_URL=${MACRO_REMOTE_SERVER_RES_URL}/hotupdate/${PLATFORM}/${ENV}

else
  # 本地环境，从配置文件加载
  CONFIG_FILE="./scripts/jenkins/jenkins.config.json"
  
  # 检查配置文件是否存在
  if [ ! -f "$CONFIG_FILE" ]; then
    log "错误: 配置文件 $CONFIG_FILE 不存在" >&2
    exit 1
  fi
  
  # 使用Python解析JSON配置
  log "读取配置文件: $CONFIG_FILE"
  read_json=$(cat <<EOF
import json
import sys

try:
    with open('$CONFIG_FILE', 'r') as f:
        config = json.load(f)
    
    # 输出所有配置到标准输出，格式为 KEY=VALUE
    for key, value in config.items():
        if isinstance(value, bool):
            # 将Python的True/False转换为shell的true/false
            print(f"{key}={'true' if value else 'false'}")
        elif isinstance(value, str):
            # 字符串值需要引号
            print(f"{key}='{value}'")
        else:
            # 数字等其他值直接输出
            print(f"{key}={value}")
            
except Exception as e:
    print(f"ERROR: {str(e)}", file=sys.stderr)
    sys.exit(1)
EOF
)

  # 执行Python脚本并读取结果
  config_values=$(python3 -c "$read_json")
  if [ $? -ne 0 ]; then
    log "错误: 解析配置文件失败" >&2
    exit 1
  fi

  # 将配置加载到环境变量
  eval "$config_values"
  
  # 验证必要参数
  if [ -z "$ApkName" ]; then
    log "错误: 配置文件缺少ApkName参数" >&2
    exit 1
  fi

  if [ -z "$Apk" ]; then
    log "错误: 配置文件缺少Apk参数" >&2
    exit 1
  fi

  if [ -z "$version" ]; then
    log "错误: 配置文件缺少version参数" >&2  
    exit 1
  fi

  if [ -z "$apkPublishType" ]; then
    log "错误: 配置文件缺少apkPublishType参数" >&2
    exit 1
  fi
  
  # 设置默认值（如果配置文件中未指定）
  only_bundle=${only_bundle:-false}
fi

# 打印读取到的配置
log "使用以下配置:"
log "- ApkName: $ApkName"
log "- Apk: $Apk"
log "- 版本: $version"
# log "- 融合: $combile"
log "- 主包资源: $stableRes"
log "- APK类型: $apkPublishType"
log "- 调试模式: $debug"
log "- 开启热更: $MACRO_HOTUPDATE_OPEN"
log "- 热更新服务器URL: $HOT_SERVER_URL"
log "- 特性裁剪: $MACRO_TRAITS_SPLIT"
log "- 自动模式: $MACRO_AUTO"
log "- 显示FPS: $MACRO_SHOW_FPS_LOG"
log "- 仅构建Bundle: $only_bundle"

# 常量定义
JENKINS_FILES_PATH="./scripts/jenkins"
BUILD_OUTPUT_DIR="./build"
TEMP_DIR=${JENKINS_FILES_PATH}/temp

# 初始化跳过APK打包的标志
SKIP_APK_BUILD=false

BLOCK_BLAST_BASE_URL=/Users/<USER>/blockblast-adv

# 暂时通过代码分割时会使用 lib-combileRequire
BASE_LIB="lib-combileRequire"
if [[ "${MACRO_DEVTOOLS}" == true ]]; then
  BASE_LIB="lib-combileRequireDevtools"
fi

if [ "$IS_JENKINS_ENV" = true ]; then
  APK_BASE_URL=${BLOCK_BLAST_BASE_URL}/jenkins/apk/${apkPublishType}
  LIB_BASE_URL=${BLOCK_BLAST_BASE_URL}/jenkins/${BASE_LIB}
else
  APK_BASE_URL=${JENKINS_FILES_PATH}/apk/${apkPublishType}
  LIB_BASE_URL=${JENKINS_FILES_PATH}/${BASE_LIB}
fi

log "基本库地址: $LIB_BASE_URL"

# 创建必要的目录
mkdir -p ${BUILD_OUTPUT_DIR}
mkdir -p ${TEMP_DIR}

# 清理工作目录
log "清理工作目录..."
if [ "$IS_JENKINS_ENV" = true ]; then
  git clean -fd
fi

# 清理旧构建
log "清理旧构建..."
rm -rf ${BUILD_OUTPUT_DIR}/jsb-link

# cocos creator 会对编译的 cocos2d-js 文件产生缓存，正常情况下不需要删除，但是这里有自定义宏，因此，可能有问题，后面可以将这句去掉（原因是，现在已经不通过 predefine.js 文件修改 cocos2d-js 文件了）
rm -rf /Applications/Cocos/Creator/2.4.11/CocosCreator.app/Contents/Resources/engine/bin/.cache/${PLATFORM}

# 检查 only-bundle 参数（构建前设置）
if [[ "${only_bundle}" == true ]]; then
  log "检测到 only-bundle 参数为 true，开始处理 bundle 模式..."
  
  # 设置advmain为bundle模式
  log "设置advmain为bundle模式..."
  if [ -f "assets/advmain.meta" ]; then
    # 使用sed命令将isBundle的值从false改为true
    sed -i '' 's/"isBundle": false/"isBundle": true/' assets/advmain.meta
    if [ $? -eq 0 ]; then
      log "成功将advmain.meta中的isBundle设置为true"
    else
      log "错误: 修改advmain.meta失败" >&2
      exit 1
    fi
  else
    log "错误: assets/advmain.meta 文件不存在" >&2
    exit 1
  fi
  
  # 设置SubGameBridge中的NEED_REMOTE为true
  log "设置SubGameBridge中的NEED_REMOTE为true..."
  SUBGAMEBRIDGE_FILE="assets/advmain/scripts/base/SubGameBridge.ts"
  if [ -f "$SUBGAMEBRIDGE_FILE" ]; then
    # 检查当前NEED_REMOTE的值
    current_value=$(grep 'public static readonly NEED_REMOTE = ' "$SUBGAMEBRIDGE_FILE" | sed 's/.*= \([^;]*\);.*/\1/')
    log "当前NEED_REMOTE值: $current_value"
    
    # 使用sed命令确保NEED_REMOTE设置为true（无论原始值是什么）
    sed -i '' 's/public static readonly NEED_REMOTE = [^;]*;/public static readonly NEED_REMOTE = true;/' "$SUBGAMEBRIDGE_FILE"
    
    # 验证修改结果
    new_value=$(grep 'public static readonly NEED_REMOTE = ' "$SUBGAMEBRIDGE_FILE" | sed 's/.*= \([^;]*\);.*/\1/')
    if [ "$new_value" = "true" ]; then
      log "成功确保SubGameBridge.ts中的NEED_REMOTE设置为true"
    else
      log "错误: 修改SubGameBridge.ts失败，当前值: $new_value" >&2
      exit 1
    fi
  else
    log "错误: $SUBGAMEBRIDGE_FILE 文件不存在" >&2
    exit 1
  fi
  
  # 在构建前设置bundle为remote模式
  log "设置bundle为remote模式..."
  /usr/local/bin/node ./scripts/publishBundles/setBundleRemote.js
  
  if [ $? -ne 0 ]; then
    log "错误: 设置bundle remote模式失败" >&2
    exit 1
  fi
fi

log "执行Cocos构建..."
# 测试模块下多了 webview 模块，不排除 webview
excludedModules="['Collider','Dynamic Atlas','Geom Utils','Intersection','Label Effect','Mesh','MotionStreak','Physics','StudioComponent','TiledMap','VideoPlayer','3D','3D Primitive','SubContext','TypeScript Polyfill','3D Physics/cannon.js','3D Physics/Builtin','3D Physics Framework','3D Particle','SafeArea']"
build_options="platform=android;debug=$debug;apiLevel=23;template=link;excludedModules=$excludedModules"
/Applications/Cocos/Creator/2.4.11/CocosCreator.app/Contents/MacOS/CocosCreator \
  --path ./ \
  --build "$build_options" \
  --MACRO_MACRO_DEVTOOLS ${MACRO_DEVTOOLS} \
  --MACRO_PLATFORM ${PLATFORM} \
  --MACRO_ENV ${ENV} \
  --MACRO_VERSION ${MACRO_VERSION} \
  --MACRO_AUTO ${MACRO_AUTO} \
  --MACRO_ROBOT ${MACRO_ROBOT} \
  --MACRO_TRAITS_SPLIT ${MACRO_TRAITS_SPLIT} \
  --MACRO_HOTUPDATE_OPEN ${MACRO_HOTUPDATE_OPEN} \
  --MACRO_REMOTE_SERVER_RES_URL ${MACRO_REMOTE_SERVER_RES_URL} \
  --MACRO_SHOW_FPS_LOG ${MACRO_SHOW_FPS_LOG}

log "替换 main.js"

# if [[ "${IS_JENKINS_ENV}" == true ]]; then
#   cp ./build-templates/jsb-link/main.refactored.js ${BUILD_OUTPUT_DIR}/jsb-link/main.js
# fi

# # 重命名 assets 目录，兼容当前模式与融合模式
# JSB_LINK=${BUILD_OUTPUT_DIR}/jsb-link
# [ -d ${JSB_LINK}/assets/internal ] && mv ${JSB_LINK}/assets/internal ${JSB_LINK}/assets/refactoredInternal
# [ -d ${JSB_LINK}/assets/main ] && mv ${JSB_LINK}/assets/main ${JSB_LINK}/assets/refactoredMain
# [ -d ${JSB_LINK}/assets/resources ] && mv ${JSB_LINK}/assets/resources ${JSB_LINK}/assets/refactoredResources

# # Release模式下移除 tool bundle
# if [[ ${debug} != true ]]; then
#   log "Release模式：移除 tool bundle..."
#   rm -rf ${BUILD_OUTPUT_DIR}/jsb-link/assets/tool
# fi

PUBLISH_TYPE="Web-mobile"
if [ "$IS_JENKINS_ENV" = true ]; then
  PUBLISH_TYPE="jsb-link"
fi

log "设置当前 PUBLISH_TYPE："${PUBLISH_TYPE}

# 特性裁剪
# if [[ "${MACRO_TRAITS_SPLIT}" == true ]]; then
  
#   if [[ "${debug}" == true ]]; then
#     # 调试模式下
#     log "调试模式执行特性裁剪..."
#     /usr/local/bin/node ./scripts/jenkins/traitsSplits.js ${IS_JENKINS_ENV}
#   else
#     # 非调试模式下，需要以下步骤
#     # （1）：先解包 jsc
#     # （2）：特性分离
#     # （3）：再合包 jsc
#     log "非调试模式执行特性裁剪..."

    

#     log "解包 jsc"
#     /usr/local/bin/node ./scripts/crypto/decodeJsc.js ${BUILD_OUTPUT_DIR}/${PUBLISH_TYPE}/assets/ false

#     if [[ "${apkPublishType}" == *"release"* ]]; then
#       Decode=true
#     else
#       Decode=false
#     fi

#     log "非调试模式特性分离..."
#     /usr/local/bin/node ./scripts/jenkins/traitsSplits.js ${IS_JENKINS_ENV}

#     log "合包 js"
#     /usr/local/bin/node ./scripts/crypto/encodeJs.js ${BUILD_OUTPUT_DIR}/${PUBLISH_TYPE}/assets/ ${Decode}

#     # 正式服需要图片加密
#     if [[ ${ENV} == "prod-test" ]]; then
#       log "图片加密"
#       /usr/local/bin/node ./scripts/crypto/encodeImage.js ${BUILD_OUTPUT_DIR}/${PUBLISH_TYPE}/assets/
#     fi
#   fi
# else
  if [[ "${debug}" != true ]]; then
    # 正式服需要图片加密
    if [[ ${ENV} == "prod-test" ]]; then
      log "加密 jsc 脚本"
      /usr/local/bin/node ./scripts/crypto/encodeJsc.js ${BUILD_OUTPUT_DIR}/${PUBLISH_TYPE}/assets/

      log "图片加密"
      /usr/local/bin/node ./scripts/crypto/encodeImage.js ${BUILD_OUTPUT_DIR}/${PUBLISH_TYPE}/assets/
    fi
  fi
# fi



# 检查 only-bundle 参数（构建后处理）
if [[ "${only_bundle}" == true ]]; then
  log "开始处理 bundle 上传..."
  
  # 检查 remotebundle 目录是否存在
  REMOTE_BUNDLE_DIR="${BUILD_OUTPUT_DIR}/${PUBLISH_TYPE}/remote"
  if [ ! -d "${REMOTE_BUNDLE_DIR}" ]; then
    log "错误: remotebundle 目录不存在: ${REMOTE_BUNDLE_DIR}" >&2
    exit 1
  fi
  
  # 初始化bundle信息数组
  BUNDLE_INFO_JSON="["
  BUNDLE_COUNT=0
  
  # 遍历 remotebundle 目录下的所有 bundle 文件夹
  for bundle_dir in "${REMOTE_BUNDLE_DIR}"/*; do
    if [ -d "${bundle_dir}" ]; then
      bundle_name=$(basename "${bundle_dir}")
      log "处理 bundle: ${bundle_name}"
      
      # 从 index.*.js 文件中提取 MD5 版本号
      index_file=$(find "${bundle_dir}" -name "index.*.js" -type f | head -n 1)
      if [ -n "$index_file" ]; then
        # 从文件名中提取 MD5（如 index.8965e.js 中的 8965e）
        bundle_md5=$(basename "$index_file" | sed 's/index\.\(.*\)\.js/\1/')
        log "从文件名提取的 MD5: ${bundle_md5}"
        
        # 如果文件名中的 MD5 不完整，尝试从文件内容中提取
        if [ ${#bundle_md5} -lt 8 ]; then
          # 尝试从文件内容中搜索 MD5 模式
          content_md5=$(grep -oE '[a-f0-9]{32}' "$index_file" | head -n 1)
          if [ -n "$content_md5" ]; then
            bundle_md5=$content_md5
            log "从文件内容提取的 MD5: ${bundle_md5}"
          fi
        fi
      else
        log "警告: 未找到 index.*.js 文件，使用文件夹名作为版本号"
        bundle_md5=$bundle_name
      fi
      
      # 输出 bundle 信息到日志
      log "Bundle ${bundle_name} MD5: ${bundle_md5}"
      
      # 构建bundle信息JSON
      if [ $BUNDLE_COUNT -gt 0 ]; then
        BUNDLE_INFO_JSON="${BUNDLE_INFO_JSON},"
      fi
      BUNDLE_INFO_JSON="${BUNDLE_INFO_JSON}{\"name\":\"${bundle_name}\",\"md5\":\"${bundle_md5}\"}"
      BUNDLE_COUNT=$((BUNDLE_COUNT + 1))
      
      # 上传 bundle 到测试环境
      if [[ "${IS_JENKINS_ENV}" == true ]]; then
        # Jenkins环境中上传到S3
        S3_BUCKET="afafb-and"
        S3_TARGET_PATH="s3://${S3_BUCKET}/blockblast-adv-test/remotes/${bundle_name}/"
        
        log "上传 bundle ${bundle_name} 到 S3..."
        if /opt/homebrew/bin/aws s3 sync "${bundle_dir}/" "${S3_TARGET_PATH}"; then
          log "Bundle ${bundle_name} 上传成功到 ${S3_TARGET_PATH}"
        else
          log "错误: Bundle ${bundle_name} 上传到 S3 失败" >&2
          exit 1
        fi
      else
        # 本地环境中上传到本地目录
        LOCAL_TARGET_DIR="/Users/<USER>/Documents/hg-wp/testServer/${PLATFORM}/${ENV}/remotes"
        mkdir -p "${LOCAL_TARGET_DIR}"
        
        log "上传 bundle ${bundle_name} 到本地目录..."
        if cp -r "${bundle_dir}" "${LOCAL_TARGET_DIR}/${bundle_name}"; then
          log "Bundle ${bundle_name} 上传成功到 ${LOCAL_TARGET_DIR}/${bundle_name}"
        else
          log "错误: Bundle ${bundle_name} 上传到本地目录失败" >&2
          exit 1
        fi
      fi
      
    fi
  done
  
  # 关闭bundle信息JSON数组
  BUNDLE_INFO_JSON="${BUNDLE_INFO_JSON}]"
  
  # 上传libs文件
  log "开始上传libs文件..."
  LIBS_DIR="${BUILD_OUTPUT_DIR}/${PUBLISH_TYPE}/src/assets/advmain/libs"
  LIBS_INFO_JSON="[]"
  
  if [ -d "${LIBS_DIR}" ]; then
    log "找到libs目录: ${LIBS_DIR}"
    
    # 收集libs文件信息
    LIBS_INFO_JSON="["
    LIBS_COUNT=0
    
    for lib_file in "${LIBS_DIR}"/*; do
      if [ -f "${lib_file}" ]; then
        lib_name=$(basename "${lib_file}")
        lib_size=$(stat -f%z "${lib_file}" 2>/dev/null || echo "0")
        lib_size_kb=$((lib_size / 1024))
        
        log "发现lib文件: ${lib_name} (${lib_size_kb}KB)"
        
        # 构建libs信息JSON
        if [ $LIBS_COUNT -gt 0 ]; then
          LIBS_INFO_JSON="${LIBS_INFO_JSON},"
        fi
        LIBS_INFO_JSON="${LIBS_INFO_JSON}{\"name\":\"${lib_name}\",\"size\":\"${lib_size_kb}KB\"}"
        LIBS_COUNT=$((LIBS_COUNT + 1))
      fi
    done
    
    # 关闭libs信息JSON数组
    LIBS_INFO_JSON="${LIBS_INFO_JSON}]"
    
    if [[ "${IS_JENKINS_ENV}" == true ]]; then
      # Jenkins环境中上传到S3
      S3_BUCKET="afafb-and"
      S3_LIBS_PATH="s3://${S3_BUCKET}/blockblast-adv-test/remotes/libs/"
      
      log "上传 libs 到 S3: ${S3_LIBS_PATH}"
      if /opt/homebrew/bin/aws s3 sync "${LIBS_DIR}/" "${S3_LIBS_PATH}"; then
        log "Libs 上传成功到 ${S3_LIBS_PATH}"
      else
        log "错误: Libs 上传到 S3 失败" >&2
        exit 1
      fi
    else
      # 本地环境中上传到本地目录
      LOCAL_LIBS_DIR="/Users/<USER>/Documents/hg-wp/testServer/${PLATFORM}/${ENV}/remotes/libs"
      mkdir -p "${LOCAL_LIBS_DIR}"
      
      log "上传 libs 到本地目录: ${LOCAL_LIBS_DIR}"
      if cp -r "${LIBS_DIR}"/* "${LOCAL_LIBS_DIR}/"; then
        log "Libs 上传成功到 ${LOCAL_LIBS_DIR}"
      else
        log "错误: Libs 上传到本地目录失败" >&2
        exit 1
      fi
    fi
  else
    log "警告: libs目录不存在: ${LIBS_DIR}"
  fi
  
  log "所有 bundle 处理完成，恢复meta文件到原始状态"
  
  # 使用git命令恢复meta文件到原始状态
  log "使用git恢复meta文件和相关配置文件..."
  
  # 恢复assets目录下的meta文件（包括advmain.meta的isBundle字段）
  git checkout -- assets/advmain.meta assets/advres.meta 2>/dev/null || log "警告: assets目录meta文件恢复失败"
  
  # 恢复bundles目录下的meta文件
  if [ -d "assets/bundles" ]; then
    git checkout -- assets/bundles/*.meta 2>/dev/null || log "警告: bundles目录meta文件恢复失败"
  fi
  
  # 恢复SubGameBridge.ts文件中的NEED_REMOTE设置
  SUBGAMEBRIDGE_FILE="assets/advmain/scripts/base/SubGameBridge.ts"
  if [ -f "$SUBGAMEBRIDGE_FILE" ]; then
    git checkout -- "$SUBGAMEBRIDGE_FILE" 2>/dev/null || log "警告: SubGameBridge.ts文件恢复失败"
    log "SubGameBridge.ts文件恢复完成"
  fi
  
  log "meta文件和配置文件恢复操作完成（包括advmain.meta的isBundle字段和SubGameBridge.ts的NEED_REMOTE设置）"
  
  log "跳过APK打包，继续执行钉钉通知"
  # 设置跳过APK打包的标志
  SKIP_APK_BUILD=true
fi

if [[ ${MACRO_HOTUPDATE_OPEN} == true ]]; then
  # 生成manifest
  log "生成manifest..."
  /usr/local/bin/node ./scripts/hotUpdate/version_generator.js ${MACRO_REMOTE_SERVER_RES_URL} ${PLATFORM} ${ENV} ${version} ${HotUpdateZip} ./build/jsb-link
 
  # 上传热更资源
  log "上传热更资源..."
  /usr/bin/expect ./scripts/hotUpdate/hotUpdate-upload-file-mac-studio.exp ${PLATFORM} ${ENV} Youxi123 ${version}

  log "生成热更 zip 差异化资源..."
  /usr/local/bin/node ./scripts/hotUpdate/HotUpdate-diff-zip.js ${MACRO_REMOTE_SERVER_RES_URL} ${PLATFORM} ${ENV}
fi

# if [[ ${MACRO_HOTUPDATE_OPEN} == true ]]; then
#   log "上传当前包文件到 git 仓库..."
#   if [[ ${ENV} == "prod-test" ]]; then
#     PUBLISH_COMMIT_ID=$(sh ./scripts/jenkins/publish-to-git.sh ${PLATFORM} ${ENV} ${version} false | tail -n 1)
#   else
#     PUBLISH_COMMIT_ID=$(sh ./scripts/jenkins/publish-to-git.sh ${PLATFORM} ${ENV} ${version} | tail -n 1)
#   fi
#   log "获取到发布到 git 仓库的 commitId: ${PUBLISH_COMMIT_ID}"
# fi

HOT_UPDATE_DIR=${BLOCK_BLAST_BASE_URL}/hotUpdate/${PLATFORM}/${ENV}
if [[ ${MACRO_HOTUPDATE_OPEN} == true ]]; then
  if [[ ${ENV} == "prod-test" ]]; then
    # 上传生成的热更资源到 amazon s3 服务器
    if [[ "${IS_JENKINS_ENV}" == true ]]; then
      S3_BUCKET="afafb-and"
      # 上传热更资源到 S3
      /opt/homebrew/bin/aws s3 sync ${HOT_UPDATE_DIR}/${version}/ s3://${S3_BUCKET}/blockblast-adv-test/hotUpdate/${version}/
      # 上传增量 zip 包到 S3
      log "上传增量 zip 包到 S3..."

      # 上传 version.confg 资源到 S3
      /opt/homebrew/bin/aws s3 cp ${HOT_UPDATE_DIR}/version.config s3://${S3_BUCKET}/blockblast-adv-test/hotUpdate/version.config
      log "上传 version.config 到 S3..."
      
      # 调用增量包上传脚本
      if /usr/local/bin/node ./scripts/hotUpdate/upload-diff-zip-s3.js ${PLATFORM} ${ENV} ${S3_BUCKET} blockblast-adv-test; then
        log "增量 zip 包上传完成"
      else
        log "警告: 增量 zip 包上传失败，但不影响主流程继续执行"
      fi
    fi
  fi
fi

# 复制开发工具库文件

KEYSTORE_FILE="${JENKINS_FILES_PATH}/sign/block-blast.keystore"
KEYSTORE_PASS="Md5ctAth0h"
KEYSTORE_ALIAS="9OmruqqZww"

# ------------------------- 打包新架构包 apk start--------------------- #
APK_FULL_NAME=""
APK_SIZE=0
UNSIGNED_APK="${JENKINS_FILES_PATH}/block-blast-unsigned.apk"
UNSIGNED_ALIGNED_APK="${JENKINS_FILES_PATH}/block-blast-unsigned-aligned.apk"


if [ "$Apk" == true ] && [ "$SKIP_APK_BUILD" != true ]; then
  log "反编译APK..."
  /opt/homebrew/bin/apktool d -f ${APK_BASE_URL} -o ${TEMP_DIR}
fi

if [ "$SKIP_APK_BUILD" != true ]; then
  # 清理并准备APK资源
  log "清理并准备APK资源..."
  rm -rf ${TEMP_DIR}/assets/assets
  rm -rf ${TEMP_DIR}/assets/src
  rm -rf ${TEMP_DIR}/assets/jsb-adapter
  rm -f ${TEMP_DIR}/assets/main.js

  # # 删除文件前检查文件是否存在
  # [ -f ${TEMP_DIR}/assets/main.refactored.js ] && rm -f ${TEMP_DIR}/assets/main.refactored.js
  # [ -f ${TEMP_DIR}/assets/distribution.js ] && rm -f ${TEMP_DIR}/assets/distribution.js

  # 复制新构建资源到APK
  log "复制新构建资源到APK..."
  cp -r ${BUILD_OUTPUT_DIR}/jsb-link/assets ${TEMP_DIR}/assets
  cp -r ${BUILD_OUTPUT_DIR}/jsb-link/src ${TEMP_DIR}/assets
  cp -r ${BUILD_OUTPUT_DIR}/jsb-link/jsb-adapter ${TEMP_DIR}/assets
  cp -r ${BUILD_OUTPUT_DIR}/jsb-link/main.js ${TEMP_DIR}/assets
fi

if [[ ${MACRO_HOTUPDATE_OPEN} == true ]] && [ "$SKIP_APK_BUILD" != true ]; then
  # 处理热更manifest文件
  log "复制热更manifest文件..."
  mkdir -p ${TEMP_DIR}/assets/src
  cp ${BUILD_OUTPUT_DIR}/jsb-link/src/project.manifest ${TEMP_DIR}/assets/src
  cp ${BUILD_OUTPUT_DIR}/jsb-link/src/version.manifest ${TEMP_DIR}/assets/src
fi

if [[ "$Apk" == true ]] && [ "$SKIP_APK_BUILD" != true ]; then
  log "复制开发工具库文件..."
  for arch in "arm64-v8a" "armeabi-v7a" "x86" "x86_64"; do
    cp ${LIB_BASE_URL}/${arch}/libcocos2djs.so ${TEMP_DIR}/lib/${arch}/libcocos2djs.so
  done

  APK_FULL_NAME="${ApkName}-${version}-$(date '+%Y%m%d%H%M%S').apk"

  # 构建APK
  log "构建未签名APK..."
  /opt/homebrew/bin/apktool b ${TEMP_DIR} -o ${UNSIGNED_APK}

  # 对齐，如果不使用这个，会报：Failure [-124: Failed parse during installPackageLI: Targeting R+ (version 30 and above) requires the resources.arsc of installed APKs to be stored uncompressed and aligned on a 4-byte boundary]
  # 从 Android 11 开始，Google 对 APK 的打包格式有了更严格的要求，尤其是 resources.arsc 文件（资源索引文件）：
  # 必须 uncompressed（未压缩）
  # 必须 4 字节对齐
  ~/Library/Android/sdk/build-tools/30.0.3/zipalign -v 4 ${UNSIGNED_APK} ${UNSIGNED_ALIGNED_APK}

  # 签名APK
  log "签名APK..."
  # /opt/homebrew/opt/openjdk/bin/jarsigner -verbose \
  #   -keystore ${KEYSTORE_FILE} \
  #   -storepass "${KEYSTORE_PASS}" \
  #   -signedjar ${APK_FULL_NAME} \
  #   ${UNSIGNED_ALIGNED_APK} \
  #   ${KEYSTORE_ALIAS}
  # 必须用 apksigner 签名，jarsigner 签名会报错：Failure [-124: Failed parse during installPackageLI: Targeting R+ (version 30 and above) requires the resources.arsc of installed APKs to be stored uncompressed and aligned on a 4-byte boundary]
  ~/Library/Android/sdk/build-tools/30.0.3/apksigner sign \
    --ks ${KEYSTORE_FILE} \
    --ks-key-alias ${KEYSTORE_ALIAS} \
    --ks-pass pass:${KEYSTORE_PASS} \
    --out ${APK_FULL_NAME} \
    ${UNSIGNED_ALIGNED_APK}

  # 移动最终APK到构建目录
  log "移动最终APK到构建目录..."
  mv ${APK_FULL_NAME} ${BUILD_OUTPUT_DIR}

  # 清理临时文件
  log "清理临时文件..."
  rm -f ${UNSIGNED_APK}
  rm -f ${UNSIGNED_ALIGNED_APK}
  rm -rf ${TEMP_DIR}

  log "构建完成！最终APK: ${BUILD_OUTPUT_DIR}/${APK_FULL_NAME}"

  # 如果是在Jenkins环境中，将构建的 apk拷贝到 ${BLOCK_BLAST_BASE_URL}/${PLATFORM}/${ENV}/${IP_ADDRESS}
  if [[ "$IS_JENKINS_ENV" = true ]]; then
    # 获取本机IP地址
    IP_ADDRESS=$(ifconfig | grep "inet " | grep -v 127.0.0.1 | awk '{print $2}' | head -n 1)
    TARGET_DIR="${BLOCK_BLAST_BASE_URL}/${PLATFORM}/${ENV}/${IP_ADDRESS}"
    
    # 创建目标目录（如果不存在）
    log "将APK拷贝到 ${TARGET_DIR}..."
    mkdir -p "${TARGET_DIR}"
    
    # 拷贝APK文件到目标目录
    cp ${BUILD_OUTPUT_DIR}/${APK_FULL_NAME} ${TARGET_DIR}/${APK_FULL_NAME}
    # 移除目标文件
    rm -f ${BUILD_OUTPUT_DIR}/${APK_FULL_NAME}
    
    # 验证拷贝成功
    if [ $? -eq 0 ]; then
      log "APK已成功拷贝到 ${TARGET_DIR}/${APK_FULL_NAME}"
    else
      log "错误: APK拷贝失败" >&2
    fi
  fi

  # 获取APK大小
  APK_SIZE=$(du -h "${TARGET_DIR}/${APK_FULL_NAME}" | cut -f1)
elif [ "$SKIP_APK_BUILD" == true ]; then
  log "跳过APK构建过程..."
  # 为了避免钉钉通知时变量未定义，设置空值
  APK_FULL_NAME=""
  APK_SIZE=""
fi

# sh ./scripts/jenkins/build-apk.sh
# if [[ "$Apk" == true ]]; then
#   log "复制开发工具库文件..."
#   for arch in "arm64-v8a" "armeabi-v7a" "x86" "x86_64"; do
#     cp ${LIB_BASE_URL}/${arch}/libcocos2djs.so ${TEMP_DIR}/lib/${arch}/libcocos2djs.so
#   done

#   APK_FULL_NAME="${ApkName}-${version}-$(date '+%Y%m%d%H%M%S').apk"

#   # 构建APK
#   log "构建未签名APK..."
#   /opt/homebrew/bin/apktool b ${TEMP_DIR} -o ${UNSIGNED_APK}

#   # 对齐，如果不使用这个，会报：Failure [-124: Failed parse during installPackageLI: Targeting R+ (version 30 and above) requires the resources.arsc of installed APKs to be stored uncompressed and aligned on a 4-byte boundary]
#   # 从 Android 11 开始，Google 对 APK 的打包格式有了更严格的要求，尤其是 resources.arsc 文件（资源索引文件）：
#   # 必须 uncompressed（未压缩）
#   # 必须 4 字节对齐
#   ~/Library/Android/sdk/build-tools/30.0.3/zipalign -v 4 ${UNSIGNED_APK} ${UNSIGNED_ALIGNED_APK}

#   # 签名APK
#   log "签名APK..."
#   # /opt/homebrew/opt/openjdk/bin/jarsigner -verbose \
#   #   -keystore ${KEYSTORE_FILE} \
#   #   -storepass "${KEYSTORE_PASS}" \
#   #   -signedjar ${APK_FULL_NAME} \
#   #   ${UNSIGNED_ALIGNED_APK} \
#   #   ${KEYSTORE_ALIAS}
#   # 必须用 apksigner 签名，jarsigner 签名会报错：Failure [-124: Failed parse during installPackageLI: Targeting R+ (version 30 and above) requires the resources.arsc of installed APKs to be stored uncompressed and aligned on a 4-byte boundary]
#   ~/Library/Android/sdk/build-tools/30.0.3/apksigner sign \
#     --ks ${KEYSTORE_FILE} \
#     --ks-key-alias ${KEYSTORE_ALIAS} \
#     --ks-pass pass:${KEYSTORE_PASS} \
#     --out ${APK_FULL_NAME} \
#     ${UNSIGNED_ALIGNED_APK}

#   # 移动最终APK到构建目录
#   log "移动最终APK到构建目录..."
#   mv ${APK_FULL_NAME} ${BUILD_OUTPUT_DIR}

#   # 清理临时文件
#   log "清理临时文件..."
#   rm -f ${UNSIGNED_APK}
#   rm -f ${UNSIGNED_ALIGNED_APK}
#   rm -rf ${TEMP_DIR}

#   log "构建完成！最终APK: ${BUILD_OUTPUT_DIR}/${APK_FULL_NAME}"

#   # 如果是在Jenkins环境中，将构建的 apk拷贝到 ${BLOCK_BLAST_BASE_URL}/${PLATFORM}/${ENV}/${IP_ADDRESS}
#   if [[ "$IS_JENKINS_ENV" = true ]]; then
#     # 获取本机IP地址
#     IP_ADDRESS=$(ifconfig | grep "inet " | grep -v 127.0.0.1 | awk '{print $2}' | head -n 1)
#     TARGET_DIR="${BLOCK_BLAST_BASE_URL}/${PLATFORM}/${ENV}/${IP_ADDRESS}"
    
#     # 创建目标目录（如果不存在）
#     log "将APK拷贝到 ${TARGET_DIR}..."
#     mkdir -p "${TARGET_DIR}"
    
#     # 拷贝APK文件到目标目录
#     cp ${BUILD_OUTPUT_DIR}/${APK_FULL_NAME} ${TARGET_DIR}/${APK_FULL_NAME}
#     # 移除目标文件
#     rm -f ${BUILD_OUTPUT_DIR}/${APK_FULL_NAME}
    
#     # 验证拷贝成功
#     if [ $? -eq 0 ]; then
#       log "APK已成功拷贝到 ${TARGET_DIR}/${APK_FULL_NAME}"
#     else
#       log "错误: APK拷贝失败" >&2
#     fi
#   fi

#   # 获取APK大小
#   APK_SIZE=$(du -h "${TARGET_DIR}/${APK_FULL_NAME}" | cut -f1)
# fi

# ------------------------- 打包新架构包 apk end--------------------- #

# # 打包融合包 apk
# COMBILE_APK_FULL_NAME=""
# COMBILE_APK_SIZE=0
# COMBILE_TEMP_DIR="${JENKINS_FILES_PATH}/tempCombile"
# COMBILE_UNSIGNED_APK="${JENKINS_FILES_PATH}/block-blast-unsigned.apk"
# COMBILE_UNSIGNED_ALIGNED_APK="${JENKINS_FILES_PATH}/block-blast-unsigned-aligned.apk"
# if [[ ${combile} == true ]]; then
  
#   # sh ./scripts/jenkins/build-combile-apk.sh
  
#   # 解压主包资源
#   # 反编译APK
#   mkdir -p ${COMBILE_TEMP_DIR}
#   if [ "$Apk" == true ]; then
#     log "反编译APK..."
#     /opt/homebrew/bin/apktool d -f ${APK_BASE_URL} -o ${COMBILE_TEMP_DIR}
#   fi
  
#   # 清理并准备APK资源
#   log "清理并准备APK资源..."
#   rm -rf ${COMBILE_TEMP_DIR}/assets/assets
#   rm -rf ${COMBILE_TEMP_DIR}/assets/src
#   rm -rf ${COMBILE_TEMP_DIR}/assets/jsb-adapter
#   rm -f ${COMBILE_TEMP_DIR}/assets/main.js

#   mkdir -p ${COMBILE_TEMP_DIR}/assets
#   mkdir -p ${COMBILE_TEMP_DIR}/src
#   mkdir -p ${COMBILE_TEMP_DIR}/jsb-adapter

#   # 删除文件前检查文件是否存在
#   [ -f ${COMBILE_TEMP_DIR}/assets/main.refactored.js ] && rm -f ${COMBILE_TEMP_DIR}/assets/main.refactored.js
#   [ -f ${COMBILE_TEMP_DIR}/assets/distribution.js ] && rm -f ${COMBILE_TEMP_DIR}/assets/distribution.js

#   # 复制新构建资源到APK
#   log "复制新构建资源到APK..."
#   cp -r ${BUILD_OUTPUT_DIR}/jsb-link/assets ${COMBILE_TEMP_DIR}/assets
#   cp -r ${BUILD_OUTPUT_DIR}/jsb-link/src ${COMBILE_TEMP_DIR}/assets
#   cp -r ${BUILD_OUTPUT_DIR}/jsb-link/jsb-adapter ${COMBILE_TEMP_DIR}/assets
#   cp -r ${BUILD_OUTPUT_DIR}/jsb-link/main.js ${COMBILE_TEMP_DIR}/assets

#   if [[ ${MACRO_HOTUPDATE_OPEN} == true ]]; then
#     # 处理热更manifest文件
#     log "复制热更manifest文件..."
#     mkdir -p ${COMBILE_TEMP_DIR}/assets/src
#     cp ${BUILD_OUTPUT_DIR}/jsb-link/src/project.manifest ${COMBILE_TEMP_DIR}/assets/src
#     cp ${BUILD_OUTPUT_DIR}/jsb-link/src/version.manifest ${COMBILE_TEMP_DIR}/assets/src
#   fi

#   #########################################################
#   #
#   #             拷贝进去之后，需要改名，以避免与主包冲突
#   #
#   #########################################################
#   BUILD_TEMPLATES_PATH=./build-templates/jsb-link
#   # main.js 改名并移动
#   echo "处理 main.refactored.js"
#   [ -f "${BUILD_TEMPLATES_PATH}/main.refactored.js" ] && mkdir -p ${COMBILE_TEMP_DIR}/assets/src && mv "${BUILD_TEMPLATES_PATH}/main.refactored.js" "${COMBILE_TEMP_DIR}/assets/src/main.refactored.js"

#   # 拷贝 distribution.js
#   echo "拷贝 distribution.js..."
#   cp "${BUILD_TEMPLATES_PATH}/distribution.js" "${COMBILE_TEMP_DIR}/assets/src/distribution.js"

#   # 拷贝 main.js
#   echo "拷贝 main.js..."
#   cp "${BUILD_TEMPLATES_PATH}/main.js" "${COMBILE_TEMP_DIR}/assets/main.js"

#   # 移动 settings.js
#   echo "移动 settings.js..."
#   [ -f ${COMBILE_TEMP_DIR}/assets/src/settings.js ] && mkdir -p ${COMBILE_TEMP_DIR}/assets/src/refactored && mv ${COMBILE_TEMP_DIR}/assets/src/settings.js ${COMBILE_TEMP_DIR}/assets/src/refactored/settings.js
#   [ -f ${COMBILE_TEMP_DIR}/assets/src/settings.jsc ] && mkdir -p ${COMBILE_TEMP_DIR}/assets/src/refactored && mv ${COMBILE_TEMP_DIR}/assets/src/settings.jsc ${COMBILE_TEMP_DIR}/assets/src/refactored/settings.jsc

#   # 移动 cocos2d-jsb.js
#   echo "移动 cocos2d-jsb.js..."
#   [ -f ${COMBILE_TEMP_DIR}/assets/src/cocos2d-jsb.js ] && mkdir -p ${COMBILE_TEMP_DIR}/assets/src/refactored && mv ${COMBILE_TEMP_DIR}/assets/src/cocos2d-jsb.js ${COMBILE_TEMP_DIR}/assets/src/refactored/cocos2d-jsb.js
#   [ -f ${COMBILE_TEMP_DIR}/assets/src/cocos2d-jsb.jsc ] && mkdir -p ${COMBILE_TEMP_DIR}/assets/src/refactored && mv ${COMBILE_TEMP_DIR}/assets/src/cocos2d-jsb.jsc ${COMBILE_TEMP_DIR}/assets/src/refactored/cocos2d-jsb.jsc

#   # 移动 jsb-engine.js
#   echo "移动 jsb-engine.js..."
#   [ -f ${COMBILE_TEMP_DIR}/assets/jsb-adapter/jsb-engine.js ] && mkdir -p ${COMBILE_TEMP_DIR}/assets/jsb-adapter/refactored && cp ${COMBILE_TEMP_DIR}/assets/jsb-adapter/jsb-engine.js ${COMBILE_TEMP_DIR}/assets/jsb-adapter/refactored/jsb-engine.js

#   #########################################################
#   #
#   #              解压主包资源，并合并
#   #
#   #########################################################
#   # 解压 STABLE_RES 到临时目录
#   log "解压 STABLE_RES..."
#   STABLE_RES=${BLOCK_BLAST_BASE_URL}/jenkins/stableRes/${stableRes}
#   mkdir -p ${COMBILE_TEMP_DIR}/stableRes
#   unzip -o ${STABLE_RES} -d ${COMBILE_TEMP_DIR}/stableRes

#   # 合并 stableRes 资源到 COMBILE_TEMP_DIR
#   log "合并 stableRes 资源到 COMBILE_TEMP_DIR..."
#   for dir in assets jsb-adapter src; do
#     if [ -d ${COMBILE_TEMP_DIR}/stableRes/$dir ]; then
#       cp -rf ${COMBILE_TEMP_DIR}/stableRes/$dir/* ${COMBILE_TEMP_DIR}/assets/$dir/
#     fi
#   done

#   # 删除 stableRes 临时目录
#   rm -rf ${COMBILE_TEMP_DIR}/stableRes

#   if [[ "$Apk" == true ]]; then
#     log "复制开发工具库文件..."
#     for arch in "arm64-v8a" "armeabi-v7a" "x86" "x86_64"; do
#       cp ${LIB_BASE_URL}/${arch}/libcocos2djs.so ${COMBILE_TEMP_DIR}/lib/${arch}/libcocos2djs.so
#     done

#     COMBILE_APK_FULL_NAME="${ApkName}-${version}-combile-$(date '+%Y%m%d%H%M%S').apk"

#     # 构建APK
#     log "构建未签名APK..."
#     /opt/homebrew/bin/apktool b ${COMBILE_TEMP_DIR} -o ${COMBILE_UNSIGNED_APK}

#     # 对齐，如果不使用这个，会报：Failure [-124: Failed parse during installPackageLI: Targeting R+ (version 30 and above) requires the resources.arsc of installed APKs to be stored uncompressed and aligned on a 4-byte boundary]
#     # 从 Android 11 开始，Google 对 APK 的打包格式有了更严格的要求，尤其是 resources.arsc 文件（资源索引文件）：
#     # 必须 uncompressed（未压缩）
#     # 必须 4 字节对齐
#     ~/Library/Android/sdk/build-tools/30.0.3/zipalign -v 4 ${COMBILE_UNSIGNED_APK} ${COMBILE_UNSIGNED_ALIGNED_APK}

#     # 签名APK
#     log "签名APK..."
#     # /opt/homebrew/opt/openjdk/bin/jarsigner -verbose \
#     #   -keystore ${KEYSTORE_FILE} \
#     #   -storepass "${KEYSTORE_PASS}" \
#     #   -signedjar ${COMBILE_APK_FULL_NAME} \
#     #   ${COMBILE_UNSIGNED_ALIGNED_APK} \
#     #   ${KEYSTORE_ALIAS}
#     # 必须用 apksigner 签名，jarsigner 签名会报错：Failure [-124: Failed parse during installPackageLI: Targeting R+ (version 30 and above) requires the resources.arsc of installed APKs to be stored uncompressed and aligned on a 4-byte boundary]
#     ~/Library/Android/sdk/build-tools/30.0.3/apksigner sign \
#       --ks ${KEYSTORE_FILE} \
#       --ks-key-alias ${KEYSTORE_ALIAS} \
#       --ks-pass pass:${KEYSTORE_PASS} \
#       --out ${COMBILE_APK_FULL_NAME} \
#       ${COMBILE_UNSIGNED_ALIGNED_APK}

#     # 移动最终APK到构建目录
#     log "移动最终APK到构建目录..."
#     mv ${COMBILE_APK_FULL_NAME} ${BUILD_OUTPUT_DIR}

#     # 清理临时文件
#     log "清理临时文件..."
#     rm -f ${COMBILE_UNSIGNED_APK}
#     rm -f ${COMBILE_UNSIGNED_ALIGNED_APK}
#     rm -rf ${COMBILE_TEMP_DIR}

#     log "构建完成！最终APK: ${BUILD_OUTPUT_DIR}/${COMBILE_APK_FULL_NAME}"

#     # 如果是在Jenkins环境中，将构建的 apk拷贝到 ${BLOCK_BLAST_BASE_URL}/${PLATFORM}/${ENV}/${IP_ADDRESS}
#     if [[ "$IS_JENKINS_ENV" = true ]]; then
#       # 获取本机IP地址
#       IP_ADDRESS=$(ifconfig | grep "inet " | grep -v 127.0.0.1 | awk '{print $2}' | head -n 1)
#       TARGET_DIR="${BLOCK_BLAST_BASE_URL}/${PLATFORM}/${ENV}/${IP_ADDRESS}"
      
#       # 创建目标目录（如果不存在）
#       log "将融合包APK拷贝到 ${TARGET_DIR}..."
#       mkdir -p "${TARGET_DIR}"
      
#       # 拷贝APK文件到目标目录
#       cp ${BUILD_OUTPUT_DIR}/${COMBILE_APK_FULL_NAME} ${TARGET_DIR}/${COMBILE_APK_FULL_NAME}
#       # 移除目标文件
#       rm -f ${BUILD_OUTPUT_DIR}/${COMBILE_APK_FULL_NAME}
      
#       # 验证拷贝成功
#       if [ $? -eq 0 ]; then
#         log "combile APK已成功拷贝到 ${TARGET_DIR}/${COMBILE_APK_FULL_NAME}"
#       else
#         log "错误: combile APK拷贝失败" >&2
#       fi
#     fi

#     # 获取APK大小
#     COMBILE_APK_SIZE=$(du -h "${TARGET_DIR}/${COMBILE_APK_FULL_NAME}" | cut -f1)
#   fi
# fi

# 获取当前时间
BUILD_TIME=$(date '+%Y-%m-%d %H:%M:%S')

# 计算打包时长（秒）
BUILD_END_TIME=$(date +%s)
BUILD_DURATION=$((BUILD_END_TIME - BUILD_START_TIME))

# 将打包时长转换为易读格式 (小时:分钟:秒)
BUILD_DURATION_FORMATTED=$(printf "%02d:%02d:%02d" $((BUILD_DURATION/3600)) $((BUILD_DURATION%3600/60)) $((BUILD_DURATION%60)))

# 统计 assets 目录下所有 *.ts 文件的代码总行数（去掉空行）
log "统计 TypeScript 代码行数..."
TS_LINES_COUNT=$(find ./assets -name "*.ts" -type f -exec grep -v '^\s*$' {} \; | wc -l | tr -d ' ')
log "TypeScript 代码总行数: ${TS_LINES_COUNT}"

# 统计 assets 目录本次与上次提交的变更文件总大小（字节）
log "统计 assets 目录本次变更文件总大小..."
LAST_COMMIT=$(git rev-parse HEAD^)
CURRENT_COMMIT=$(git rev-parse HEAD)
ASSETS_DIFF_SIZE=$(git diff --name-only $LAST_COMMIT $CURRENT_COMMIT -- assets/ | xargs -I{} stat -f%z {} 2>/dev/null | awk '{sum+=$1} END {print sum}')
ASSETS_DIFF_SIZE=${ASSETS_DIFF_SIZE:-0}
GIT_COMMIT_MSG=$(git log -1 --pretty=%B)
log "assets 目录本次变更文件总大小: ${ASSETS_DIFF_SIZE} 字节"

# 创建配置信息的JSON字符串
if [[ "${only_bundle}" == true ]]; then
  # Bundle模式的配置 - 只包含必要信息和bundle列表
  CONFIG_JSON=$(cat << EOF
{
  "version": "${version}",
  "apkPublishType": "${apkPublishType}",
  "debug": ${debug},
  "robot": ${MACRO_ROBOT},
  "showFPS": ${MACRO_SHOW_FPS_LOG},
  "GIT_COMMIT": "${GIT_COMMIT}",
  "GIT_BRANCH": "${GIT_BRANCH}",
  "BUILD_NUMBER": "${BUILD_NUMBER}",
  "BUILD_USER": "${BUILD_USER}",
  "BUILD_TIME":"${BUILD_TIME}",
  "MACRO_DEVTOOLS":"${MACRO_DEVTOOLS}",
  "only_bundle": true,
  "bundles": ${BUNDLE_INFO_JSON},
  "libs": ${LIBS_INFO_JSON}
}
EOF
)
else
  # 常规模式的配置
  CONFIG_JSON=$(cat << EOF
{
  "version": "${version}",
  "apkPublishType": "${apkPublishType}",
  "debug": ${debug},
  "MACRO_HOTUPDATE_OPEN": ${MACRO_HOTUPDATE_OPEN},
  "hotUpdateServerUrl": "${HOT_SERVER_URL}",
  "traitsSplit": ${MACRO_TRAITS_SPLIT},
  "hotUpdateZip": ${HotUpdateZip},
  "autoMode": ${MACRO_AUTO},
  "robot": ${MACRO_ROBOT},
  "showFPS": ${MACRO_SHOW_FPS_LOG},
  "buildDuration": "${BUILD_DURATION_FORMATTED}",
  "tsLinesCount": ${TS_LINES_COUNT},
  "GIT_COMMIT": "${GIT_COMMIT}",
  "PUBLISH_COMMIT_ID": "${PUBLISH_COMMIT_ID}",
  "GIT_BRANCH": "${GIT_BRANCH}",
  "BUILD_NUMBER": "${BUILD_NUMBER}",
  "BUILD_USER": "${BUILD_USER}",
  "ASSETS_DIFF_SIZE":"${ASSETS_DIFF_SIZE}",
  "BUILD_TIME":"${BUILD_TIME}",
  "APK_SIZE":"${APK_SIZE}",
  "APK_FULL_NAME":"${APK_FULL_NAME}",
  "Apk":${Apk},
  "MACRO_DEVTOOLS":"${MACRO_DEVTOOLS}",
  "COMBILE_APK_FULL_NAME": "${COMBILE_APK_FULL_NAME}",
  "COMBILE_APK_SIZE": "${COMBILE_APK_SIZE}"
}
EOF
)
fi

# 将JSON字符串保存到临时文件
CONFIG_FILE=$(mktemp)
echo "${CONFIG_JSON}" > "${CONFIG_FILE}"

log "${CONFIG_FILE}"
log "${CONFIG_JSON}"

# 调用钉钉通知
log "发送钉钉通知..."
/usr/local/bin/node ./scripts/dingtalk/dingtalk.js ${PLATFORM} ${ENV} "${CONFIG_FILE}"

# 删除临时配置文件
rm -rf "${CONFIG_FILE}"