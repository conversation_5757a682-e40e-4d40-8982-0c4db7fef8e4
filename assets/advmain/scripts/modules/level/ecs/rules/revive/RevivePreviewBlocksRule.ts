import { ECSRuleBase, IECSRuleConfig } from '../core/ECSRuleBase';
import ProduceBlockAlgorithmComponent from '../../components/special/ProduceBlockAlgorithmComponent';

export interface IRevivePreviewBlocksRuleConfig extends IECSRuleConfig {
    /**算法ID */
    algorithmId: number;
    /**是否需要加载完 */
    needLoad?: boolean;
    /**满足？收集进度弹出复活 */
    percent?: number;
}

/**
 * 复活预览块规则
 * 在复活弹窗显示前，先触发出块计算并标记为预览模式
 */
export class RevivePreviewBlocksRule extends ECSRuleBase<IRevivePreviewBlocksRuleConfig> {

    canExecute(): boolean {
        // 使用工具类进行统一的复活条件检查
        if (!this.world.utilCenter.reviveUtil.canRevive(this.config)) {
            return false;
        }

        // 避免重复计算：如果已有预览数据则不执行
        return true;
    }

    execute(): void {
        const world = this.world;
        // 标记这是复活预览出块
        const reviveComp = world.utilCenter.reviveUtil.getReviveComponent();
        if (!reviveComp) {
            console.error('RevivePreviewBlocksRule: 找不到ReviveComponent');
            return;
        }
        reviveComp.isPreviewMode = true;

        // 创建算法组件，触发现有的出块系统
        // BlockAlgorithmSystem 会监听到组件添加，然后触发出块流程
        const entityId = world.createEntity();
        world.addComponent(entityId, ProduceBlockAlgorithmComponent, this.config.algorithmId);

        console.log('RevivePreviewBlocksRule: 触发复活预览出块', this.config.algorithmId);
    }
}
