import { ISpeedManagementData, ProjectileBehaviorId } from '../../behavior/projectile/ProjectileBehaviorBase';
import { IActionContext } from '../../combat/core/ActionContext';
import { Component } from '../../cores/Component';
import { IPoint } from '../../define/EcsDefine';
import { RenderComponent } from '../RenderComponent';
export interface IPathPrecomputedData {
    [key: string]: any;
    /**速度管理数据 */
    speedManagement?: ISpeedManagementData;
}
export class ProjectileComponent extends Component {
    /**组件类型 */
    public static type = 'ProjectileComponent';
    /**投射物行为配置ID，对应ProjectileBehaviorConfig.ts */
    projectileBehaviorId: ProjectileBehaviorId;
    /**开始位置 */
    startPosition: IPoint;
    /**创建时间 */
    createTime: number;
    /**动作上下文 */
    context: IActionContext;
    /**路径预计算数据（可选，某些路径类型需要） */
    pathPrecomputedData?: IPathPrecomputedData;
    /**击中特效render参数 */
    hitRenderParam?: ConstructorParameters<typeof RenderComponent>[1];
    constructor(
        mustParam: { projectileBehaviorId: ProjectileBehaviorId; context: IActionContext },
        optionParam?: { pathPrecomputedData?: IPathPrecomputedData; hitRenderParam?: ConstructorParameters<typeof RenderComponent>[1] },
    ) {
        super();
        if (!mustParam) {
            return;
        }
        this.projectileBehaviorId = mustParam.projectileBehaviorId;
        this.context = { ...mustParam.context };
        this.pathPrecomputedData = optionParam?.pathPrecomputedData || {};
        this.hitRenderParam = optionParam?.hitRenderParam;
    }
}
