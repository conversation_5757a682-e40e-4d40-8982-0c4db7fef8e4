import { endTopDistanceArr } from '../../../../../chapterConfig/ChapterDefineds';
import { PrefabConfig } from '../../../../../prefab/PrefabConfig';
import TargetComponent from '../../../components/special/TargetComponent';
import { AudioId } from '../../../config/conf/EcsAudioConfig';
import { ECSEvent } from '../../../GameEvent';
import { ECSViewBase, IECSViewConfig } from '../../ECSViewBase';

export interface ICollectWinViewConfig extends IECSViewConfig {
    readonly nextBtnPath: string;
}
/**收集物关卡失败视图 */
export class CollectWinECSView extends ECSViewBase<ICollectWinViewConfig> {
    /** 缓存的目标 */
    private _cacheCollectItems: { color: number; node: cc.Node }[] = [];
    protected initView() {
        // 胜利音效
        this.renderWorld.playAudio(AudioId.TRAVEL_WIN_LOGO);
        const sp = this.ui.playBtn.getComponent(cc.Sprite);
        if (sp && this.config.nextBtnPath) {
            this.renderWorld.loadRes('', this.config.nextBtnPath).then((texture: cc.Texture2D) => {
                sp.spriteFrame = new cc.SpriteFrame(texture);
            });
        }

        this.resetBtnState();
        this.showAction();
    }
    protected addEvents() {
        this.ui.backBtn.on(cc.Node.EventType.TOUCH_END, this.onClickBack, this);
        this.ui.playBtn.on(cc.Node.EventType.TOUCH_END, this.onClickPlay, this);
    }
    protected removeEvents() {
        this.ui.backBtn.off(cc.Node.EventType.TOUCH_END, this.onClickBack, this);
        this.ui.playBtn.off(cc.Node.EventType.TOUCH_END, this.onClickPlay, this);
    }
    protected disposeView() {}

    resetBtnState() {
        this.ui.boneAni.active = false;
        this.ui.collectItemNode.removeAllChildren();
        this.ui.collectImg.active = false;
        this.ui.backBtn.active = false;
        this.ui.playBtn.active = false;
        this.ui.scoreEffect.active = false;
        this.ui.backBtn.scale = 0;
        this.ui.playBtn.scale = 0;
    }

    /**播放动作 */
    showAction(): void {
        this.ui.boneAni.active = true;
        this.ui.boneAni.getComponent(dragonBones.ArmatureDisplay).playAnimation(`newAnimation`, 1);

        // 目标面板弹出音效
        // audioInfo.play(ChapterAudioConfig.travel_win_logo);

        cc.tween(this.node)
            .delay(0.57)
            .call(() => {
                // this.ui.collectImg.active = true;
                // this.ui.collectImg.opacity = 0;
                // cc.tween(this.ui.collectImg).to(0.33, { opacity: 255 }).start();
            })
            .delay(0.33)
            .call(() => {
                this.showWellDoneAnim();
                // 目标面板弹出音效
                // audioInfo.play(ChapterAudioConfig.travel_overui_collect_items);
                this.addItem();
            })
            .delay(2.1)
            .call(() => {
                this.showBtnAnim();
            })
            .start();
    }

    showWellDoneAnim() {
        this.ui.scoreEffect.active = true;
        this.ui.scoreEffect.getComponent(dragonBones.ArmatureDisplay).playAnimation('HS_3', 1);
    }

    showBtnAnim() {
        this.ui.backBtn.active = true;
        this.ui.backBtn.scale = 0.6;
        this.ui.playBtn.active = true;
        this.ui.playBtn.scale = 0.6;
        // 按钮出现音效
        // audioInfo.play(AudioConfig.s_btnShow);
        cc.tween(this.ui.backBtn)
            .to(0.13, { scale: 1.1 })
            .to(0.07, { scale: 1 })
            .call(() => {
                this.ui.backBtn.getComponent(cc.Button).interactable = true;
            })
            .start();

        cc.tween(this.ui.playBtn)
            .to(0.13, { scale: 1.1 })
            .to(0.07, { scale: 1 })
            .call(() => {
                this.ui.playBtn.getComponent(cc.Button).interactable = true;
            })
            .start();
    }

    /**添加收集item */
    async addItem() {
        const targets = this.getTargetList();
        const length = targets.length;
        // const { requiredCollections } = this.state;
        // const { length } = requiredCollections;
        const beginPos = (-(endTopDistanceArr[length - 1] + 200) * (length - 1)) / 2;
        const distance = endTopDistanceArr[length - 1] + 200;
        for (let i = 0; i < length; i++) {
            // const node = await chapterCollectEndPool.getNode();
            const color = targets[i].key;
            let node: cc.Node;
            if (!this._cacheCollectItems[i]) {
                const prefab = this.ui.ChapterCollectEndItem;
                node = cc.instantiate(prefab);
                this._cacheCollectItems[i] = { color: +color, node: node };
            } else {
                this._cacheCollectItems[i].color = +color;
                node = this._cacheCollectItems[i].node;
            }

            cc.tween(node)
                .delay(0.2 * i)
                .call(() => {
                    // const item = node.getComponent(ChapterCollectEndItem);
                    // if (item) {
                    //     item.setState({ color: +color });
                    // }
                    this.showEndItem(node, { color: +color });
                    node.x = beginPos + distance * i;
                    this.ui.collectItemNode.addChild(node);
                })
                .start();
        }
    }

    onClickPlay(event: cc.Event.EventTouch, data: any) {
        this.disposeView();
        this.logicWorld.pushInputCmd({
            event: ECSEvent.GameEvent.GAME_NEXT,
            data: null,
        });
        // this.resetBtnState();
        // if (this.state.throughAll) {
        //     EventManager.dispatchModuleEvent(new E_ChapterList_Show({}));
        // } else {
        //     EventManager.dispatchModuleEvent(new E_ChapterGameOver_ShowFinish({ gameOverIndex: GAMEOVERTYPE.CollectWin }));
        // }
    }

    /**点击返回 */
    onClickBack(event: cc.Event.EventTouch, data: any) {
        this.renderWorld.destroyECSView(this);
        falcon.UI.show(PrefabConfig.ChapterMain);
    }

    getTargetList() {
        const targets = this.logicWorld.getComponent(this.logicWorld.query([TargetComponent])[0], TargetComponent).targets;
        return targets;
    }

    showEndItem(node: cc.Node, data: any) {
        node.active = true;
        const color = data.color;
        this.renderWorld.loadRes('', `textures/blocks/Gems`, cc.SpriteAtlas).then((atlas: cc.SpriteAtlas) => {
            node.getChildByName('gemImg').getComponent(cc.Sprite).spriteFrame = atlas.getSpriteFrame(`Gems${color}Big`);
            node.getChildByName('boneAni').getComponent(dragonBones.ArmatureDisplay).playAnimation(`${color}`, 1);
        });
    }
}
