import { BoardScene } from '../components/board/BoardScene';
import NodeComponent from '../components/NodeComponent';
import OpacityComponent from '../components/OpacityComponent';
import { RenderComponent } from '../components/RenderComponent';
import ClearComponent from '../components/special/ClearComponent';
import { AIComponent } from '../components/AIComponent';
import AwardComponent from '../components/special/AwardComponent';
import { DestroyTag } from '../components/tag/DestroyTag';
import { PreviewTag } from '../components/tag/PreviewTag';
import BoardComponent from '../components/board/BoardComponent';
import SlotComponent from '../components/board/SlotComponent';
import ShapeComponent from '../components/board/ShapeComponent';
import { CellComponent } from '../components/board/CellComponent';
import DragComponent from '../components/DragComponent';
import { MirrorShapeComponent } from '../../../../../../bundles/advDualBoardBundle/script/component/MirrorShapeComponent';
import TargetComponent from '../components/special/TargetComponent';
import { UITransformComponent } from '../components/UITransformComponent';
import { ProjectileComponent } from '../components/combat/ProjectileComponent';
import { BuffComponent } from '../components/combat/BuffComponent';
import ReviveComponent from '../components/ReviveComponent';
import ProduceBlockAlgorithmComponent from '../components/special/ProduceBlockAlgorithmComponent';
import { BoardSceneColorsComponent } from '../components/special/BoardSceneColorsComponent';
import HPComponent from '../components/special/HPComponent';
import { DelayRuleComponent } from '../components/DelayRuleComponent';
import ReviveBuffComponent from '../components/ReviveBuffComponent';
import ProduceBlockListComponent from '../components/ProduceBlockListComponent';

// 组件注册表
export const ComponentRegistry = {
    /** 节点组件 */
    NodeComponent,
    /** 透明度组件 */
    OpacityComponent,
    /** 销毁标签组件 */
    DestroyTag,
    /** 棋盘场景组件 */
    BoardScene,
    /** 预览标签组件 */
    PreviewTag,
    /** 渲染组件 */
    RenderComponent,
    /** AI组件 */
    AIComponent,
    /** 延迟规则组件 */
    DelayRuleComponent,
    /**块组件 */
    CellComponent,
    /**形状组件 */
    ShapeComponent,
    /**格子组件 */
    SlotComponent,
    /**棋盘组件 */
    BoardComponent,
    /**清除组件 */
    ClearComponent,
    /**奖励组件 */
    AwardComponent,
    /**拖拽组件 */
    DragComponent,
    /**镜像形状组件 */
    MirrorShapeComponent,
    /**目标组件 */
    TargetComponent,
    /**投射物组件 */
    ProjectileComponent,
    /**transform */
    UITransformComponent,
    /**buff组件 */
    BuffComponent,
    /**复活组件 */
    ReviveComponent,
    /**生成块算法组件 */
    ProduceBlockAlgorithmComponent,
    /**当前关卡可用颜色组件 */
    BoardSceneColorsComponent,
    /**血量组件 */
    HPComponent,
    /**复活buff组件 */
    ReviveBuffComponent,
    /** 生产方块列表组件 */
    ProduceBlockListComponent,
};
