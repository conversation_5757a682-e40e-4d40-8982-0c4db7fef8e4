
import { IsI18NSwitchTrait } from "../traits/IsI18NSwitchTrait";
import { E_Dot_Start } from "../events/E_Dot_Start";

@classId('DotInfo')
class DotInfo implements IShareTraitTarget {

    shareTraitTarget: ITraitTarget;

    /** 30%数据上报 1：上报，其它，不上报 */
    get dotUpLoad(): number {
        return falcon.storage.getItem(`dotUpLoad`, 0) as number;
    }

    /** 数数发送 TODO，还未处理发送逻辑 */
    get dsSend() {
        return true;
    }

    /**
     * 数仓打点（DotMarket 中定义的）
     * @param eventKey 
     * @param params 
     * @param traits 
     */
    DM<T extends keyof DotMarket>(eventKey: T, params?: EventElementType<DotMarket>[T], traits?: (TraitDot<EventElementType<DotMarket>[T]>)[]) {
        if (this.shareTraitTarget) {
            if (!params) {
                params = this.shareTraitTarget.data as EventElementType<DotMarket>[T];
            } else {
                params = Object.assign(params, this.shareTraitTarget.data as EventElementType<DotMarket>[T]);
            }
        }

        falcon.createDotData(falcon.DotUploadType.MARKET, eventKey, params, traits, (data) => {
            falcon.EventManager.dispatchModuleEvent(new E_Dot_Start(falcon.DotUploadType.MARKET, eventKey, data));
        });
    }

    /**
     * 数数打点（DotShushu 中定义的）
     * @param eventKey 
     * @param params 
     * @param traits 
     */
    @falcon.trait(IsI18NSwitchTrait)
    DS<T extends keyof DotShushu>(eventKey: T, params?: EventElementType<DotShushu>[T], traits?: (TraitDot<EventElementType<DotShushu>[T]>)[]) {
        // TODO
        if (!this.dsSend) {
            return;
        }

        if (this.shareTraitTarget) {
            if (!params) {
                params = this.shareTraitTarget.data as EventElementType<DotShushu>[T];
            } else {
                params = Object.assign(params, this.shareTraitTarget.data as EventElementType<DotShushu>[T]);
            }
        }

        falcon.createDotData(falcon.DotUploadType.SHUSHU, eventKey, params, traits, (data) => {
            falcon.EventManager.dispatchModuleEvent(new E_Dot_Start(falcon.DotUploadType.SHUSHU, eventKey, data));
        });
    }

}

export const dotInfo = new DotInfo();

