import Util from "../../../../base/Util";
import { ConfigRegistry, ConfigTableConst } from "../registry/ConfigRegistry";
import { IGameLevelConfig } from "./center/ConfigCenter";
/**关闭系统 */
export interface ICloseSystem{
    /**系统id */
    systemId: string;
}
/**关闭规则 */
export interface ICloseRule{
    /**规则id */
    ruleId: string;

}
/**插入系统 */
export interface IInsertSystem{
    /**系统id */
    systemId: string;
    /**必须在哪个系统之后 */
    afterSystemId?: string;
}
/**插入规则 */
export interface IInsertRule{
    /**规则id */
    ruleId: string;
    /**必须在哪个规则之后 */
    afterRuleId?: string;
    /**必须在哪个规则之前 */
    beforeRuleId?: string;
}
/**替换系统 */
export interface IReplaceSystem{
    /**原系统id */
    originSystemId: string;
    /**替换系统id */
    replaceSystemId: string;
}
/**替换规则 */
export interface IReplaceRule{
    /**原规则id */
    originRuleId: string;
    /**替换规则id */
    replaceRuleId: string;
}
/** 重写配置 */
export interface IOverrideLevelConfig {
    /** 底板配置 */
    baseLevelId: string;
    /** 关卡ID */
    levelId: string;
    /** 重写配置表参数 */
    overrideTableArgs: DeepPartial<ConfigRegistry>;
    /** 重写关卡配置参数 */
    overrideLevelConfig: DeepPartial<IGameLevelConfig>;
    /** 关闭系统 */
    closeSystems: ICloseSystem[];
    /** 关闭规则 */
    closeRules: ICloseRule[];
    /** 插入系统 */
    insertSystems: IInsertSystem[];
    /** 插入规则 */
    insertRules: IInsertRule[];
    /** 替换系统 */
    replaceSystems: IReplaceSystem[];
    /** 替换规则 */
    replaceRules: IReplaceRule[];
}
/** 重写配置管理器 */
export class OverrideConfigManager {
    private gameConfig:ConfigRegistry;
    public buildConfig(levelId: string,gameConfig: ConfigRegistry): ConfigRegistry | null {
        this.gameConfig = JSON.parse(JSON.stringify(gameConfig));
        const overrideConfig = this.gameConfig[ConfigTableConst.OverrideLevelConfig][levelId];
        this.overrideConfig(overrideConfig);
        let baseConfig = this.gameConfig[ConfigTableConst.LevelConfig][overrideConfig.baseLevelId];
        if(!baseConfig) return null;
        baseConfig.levelId = overrideConfig.levelId;
        baseConfig = Util.deepMerge(baseConfig, overrideConfig.overrideLevelConfig);
        this.closeSystems(overrideConfig.closeSystems);
        this.closeRules(overrideConfig.closeRules);
        this.insertSystems(overrideConfig.insertSystems);
        this.replaceSystems(overrideConfig.replaceSystems);
        this.replaceRules(overrideConfig.replaceRules);
        this.gameConfig.currentLevelConfig = baseConfig;
        return this.gameConfig;
    }

    /**重写配置 */
    private overrideConfig(overrideConfig: IOverrideLevelConfig) {
        const overrideTable = overrideConfig.overrideTableArgs;
        if (!overrideTable) return;

        // 遍历所有重写的配置表
        for (const tableKey in overrideTable) {
            if (overrideTable.hasOwnProperty(tableKey)) {
                const tableOverrides = overrideTable[tableKey];
                
                // 遍历该配置表中的所有重写项
                for (const configId in tableOverrides) {
                    if (tableOverrides.hasOwnProperty(configId)) {
                        const configOverrides = tableOverrides[configId];
                        
                        // 直接修改ConfigManager中的配置值
                        const originalConfig = this.gameConfig[tableKey][configId];
                        
                        if (originalConfig) {
                            // 深度合并配置
                            const mergedConfig = Util.deepMerge(originalConfig, configOverrides);
                            // 直接替换配置值
                            this.gameConfig[tableKey][configId] = mergedConfig;
                        }
                    }
                }
            }
        }
    }
    
    /**关闭系统 */
    private closeSystems(closeSystemIdList: ICloseSystem[]) {
        for (const systemObj of closeSystemIdList) {
            const systemConfig = this.gameConfig[ConfigTableConst.SystemConfig][systemObj.systemId];
            if (systemConfig) {
                (systemConfig.close as any) = true;
            }
        }
    }
    /**关闭规则 */
    private closeRules(closeList: ICloseRule[]) {
        for (const closeObj of closeList) {
            const ruleConfig = this.gameConfig[ConfigTableConst.RuleConfig][closeObj.ruleId];
            if (ruleConfig) {
                (ruleConfig.close as any) = true;
            }
        }
    }
    /**替换系统 */
    private replaceSystems(replaceSystemIdList: IReplaceSystem[]) {
        for (const systemObj of replaceSystemIdList) {
            const systemConfig = this.gameConfig[ConfigTableConst.SystemConfig][systemObj.originSystemId];
            if (systemConfig) {
                (systemConfig.replaceSystemId as any) = systemObj.replaceSystemId;
            }
        }
    }
    /**替换规则 */
    private replaceRules(replaceRuleIdList: IReplaceRule[]) {
        for (const ruleObj of replaceRuleIdList) {
            const ruleConfig = this.gameConfig[ConfigTableConst.RuleConfig][ruleObj.originRuleId];
            if (ruleConfig) {
                (ruleConfig.replaceRuleId as any) = ruleObj.replaceRuleId;
            }
        }
    }
    /**插入系统 */
    private insertSystems(insertSystemIdList: IInsertSystem[]) {
        for (const systemObj of insertSystemIdList) {
            const systemConfig = this.gameConfig[ConfigTableConst.SystemConfig][systemObj.systemId];
            if (!systemConfig) {
                console.error(`插入系统失败：系统不存在 systemId=${systemObj.systemId}`);
                continue;
            }
        }
    }
}