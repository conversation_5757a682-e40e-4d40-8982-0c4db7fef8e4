import { TempleteType } from '../../registry/templete/TempleteRegistry';
import { EcsViewId } from '../../renderWorld/ECSViewBase';
import { ECSRuleBase, IECSRuleConfig } from '../core/ECSRuleBase';

export interface IReviveRuleConfig extends IECSRuleConfig {
    /**复活视图id */
    ecsViewId: EcsViewId;
    /**满足？收集进度弹出复活 */
    percent: number;
    /**是否需要加载完 */
    needLoad: boolean;
}

/**
 * 复活规则
 */
export class ReviveRule extends ECSRuleBase<IReviveRuleConfig> {
    canExecute(): boolean {
        // 使用工具类进行统一的复活条件检查
        if (!this.world.utilCenter.reviveUtil.canRevive(this.config)) {
            return false;
        }

        // ReviveRule 特有的检查：需要有预览数据才能显示弹窗
        return this.world.utilCenter.reviveUtil.hasPreviewData();
    }

    execute(): void {
        const world = this.world;

        // 显示复活弹窗，不再直接扣除次数
        // 弹窗中显示预览效果，等待玩家确认
        const effectEntity = world.templeteCenter.createTempleteEntity(
            TempleteType.Render,
            {
                parentEntity: world.ECSLayerType.UILayerEntity,
                ecsViewId: this.config.ecsViewId,
            },
            {
                renderParam: { isBreakLink: true },
            },
        );
        world.destroyEntity(effectEntity);
    }
}
