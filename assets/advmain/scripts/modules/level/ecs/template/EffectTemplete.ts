import { RenderTemplate, RenderTemplateMustArgs, RenderTemplateOptionalArgs } from './RenderTemplate';
/** 特效渲染模板 */
export class EffectTemplete extends RenderTemplate<RenderTemplateMustArgs, RenderTemplateOptionalArgs> {
    getDefaultOptionalArgs(mustArgs: RenderTemplateMustArgs): RenderTemplateOptionalArgs {
        return {
            renderParam: {
                isBreakLink: true,
            },
        };
    }
    createTemplete(mustArgs: RenderTemplateMustArgs, optionArgs: RenderTemplateOptionalArgs): number {
        const entity = super.createTemplete(mustArgs, optionArgs);
        this.world.destroyEntity(entity);
        return entity;
    }
}
