import { Loader } from "../../../../../../advmain/scripts/base/Loader";
import { SubGameBridge } from "../../../../../../advmain/scripts/base/SubGameBridge";
import { ChapterUrls } from "../../../../../../advmain/scripts/modules/chapterConfig/ChapterDefineds";

/**关卡收集物品item缓存 */
class ChapterCollectTopPool {

    private pool: falcon.ObjectPool<cc.Node> = null;

    /**只执行一次 */
    @falcon.memoize
    public init() {
        this.pool = new falcon.ObjectPool<cc.Node>(this.create, this.reset, { size: 10 });
    }

    private async create() {
        let node: cc.Node = null;
        await Loader.asyncLoadByBundle<cc.Prefab>('chapter', ChapterUrls.topItemPath, cc.Prefab).then(prefab => {
            node = cc.instantiate(prefab);
        });
        return node;
    }

    private reset(node: cc.Node) {
        node.parent = null;
        const item = node.getComponent("ChapterCollectItem");
        if (item) {
            item.reset();
        }
    }

    public getNode(): Promise<cc.Node> {
        if (!this.pool) {
            this.init();
        }
        return this.pool.get();
    }

    /**
     * 释放
     * @param obj
     */
    public release(obj: cc.Node) {
        this.reset(obj);
        if (!this.pool) {
            return;
        }
        this.pool.release(obj);
    }

    /**
    * 清空池
    */
    public clear() {
        if (!this.pool) {
            return;
        }
        this.pool.clear();
    }
}
export const chapterCollectTopPool = new ChapterCollectTopPool();