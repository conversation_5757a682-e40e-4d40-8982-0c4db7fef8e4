(()=>{"use strict";var __webpack_modules__={177:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getLastSomeDays=function(e){void 0===e&&(e=7);for(var t=Date.now(),r=[],n=1;n<=e;n++){var o=new Date;o.setTime(t-864e5*n),r.push(o.getFullYear()+"_"+o.getMonth()+"_"+o.getDate())}return r},t.getTodayDate=function(){var e=new Date;return"".concat(e.getFullYear(),"_").concat(e.getMonth()+1,"_").concat(e.getDate())},t.getDiffDays=function(e,t){var r=new Date(e);r.setHours(0,0,0,0);var n=new Date(t);n.setHours(0,0,0,0);var o=Math.abs(r.getTime()-n.getTime());return Math.ceil(o/864e5)}},299:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ReactivePos=void 0,t.reactive=function(e){var t=e.target,r=e.propertyName,n=e.pos,o=e.callback,i=a.get(t.constructor);i||(i={},a.set(t.constructor,i)),i[r+n]=o},t.watch=function(){return function(e,t){var r,n=i(((r={})[t]=e[t],r),e,t);Object.defineProperty(e,t,{get:function(){return n[t]},set:function(e){n[t]=e},enumerable:!0,configurable:!0})}},t.watchDebug=function e(t,r,n,a){var i;if(void 0===n&&(n=[]),void 0===a&&(a=2),CC_DEBUG)return(0,o.isObject)(r)?n.length>=a?r:new Proxy(r,{get:function(r,i,s){var c=Reflect.get(r,i,s);return(0,o.isObject)(c)&&n.length+1<a?e(t,c,n.concat(String(i)),a):c},set:function(r,s,c,l){if(r[s]!==c){Error.stackTraceLimit=20;var u=new Error("");if(u.stack){var p=u.stack.split("\n").filter((function(e){return(e.includes("Trait")||e.includes("Proxy"))&&!e.includes("cocos2d-js-for-preview.js")&&!e.includes("cocos2d-js.js")}));if(p.length>0){var f=p.join("\n");f!==i&&(i=f,CC_DEBUG&&console.log("%c[watchDebug]","color:#fff;background:#6a1b9a;padding:2px 6px;border-radius:4px;","".concat(t,"\n").concat(f)))}}}var d=(0,o.isObject)(c)&&n.length+1<a?e(t,c,n.concat(String(s)),a):c;return Reflect.set(r,s,d,l)}}):r};var n,o=r(3447),a=new Map;function i(e,t,r){if(!(0,o.isObject)(e))return e;var n={get:function(e,n,a){var s=Reflect.get(e,n,a);return(0,o.isObject)(s)?i(s,t,r):s},set:function(e,n,s,c){var l=e[n],u=Reflect.set(e,n,s,c);if(l!==s){var p=a.get(t.constructor);p&&Object.keys(p).forEach((function(e){e.includes(r)&&p[e]&&p[e](e,l,s)})),(0,o.isObject)(s)&&i(s,t,r)}return u}};return new Proxy(e,n)}!function(e){e.RandomChangeScoreLbColorTraitIsComboReachTargetTraitInit="RandomChangeScoreLbColorTraitIsComboReachTargetTraitInit",e.GuideFirstLifeTrait="GuideFirstLifeTrait",e.MoreTimeToOpreateStateTraitIsBlocksProducerTouchShowGameOverStatus="MoreTimeToOpreateStateTraitIsBlocksProducerTouchShowGameOverStatus"}(n||(t.ReactivePos=n={}))},339:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.cacheRender=void 0;var n=r(9291),o=function(){function e(){this._caches={}}return e.prototype.createOrUpdateCacheListComponents=function(e){var t=this;if(e){var r=e.prefabUrl,o=e.bundleName,a=e.count,i=e.typeOrClassName,s=e.parent;if(!(a<=0)&&s)return new Promise((function(e,c){var l=function(n,o){if(n)c(n);else{var l=t._caches,u=l[r];u||(l[r]=[],u=l[r]);var p=u.length;if(p>a)for(var f=a;f<p;f++)(_=u[f]).node.active=!1;for(var d=[],h=0;h<a;h++){var _;if(_=u[h])_.node.active=!0,_.node.parent!==s&&(_.node.parent=s),d[h]=_;else{var v=cc.instantiate(o);u[h]=v.getComponent(i),s.addChild(v),d[h]=u[h]}}e(d)}};o?n.ResLoader.loadByBundle(o,r,cc.Prefab,l):n.ResLoader.load(r,cc.Prefab,l)}))}},e.prototype.clearAllCaches=function(){for(var e in this._caches){var t=this._caches[e];t&&(t.forEach((function(e){e&&e.node&&cc.isValid(e.node)&&(e.node.removeFromParent(),e.node.destroy())})),t.length=0)}this._caches={},CC_DEBUG&&console.log("[CacheRender] 所有缓存已清理")},e.prototype.reset=function(){this.clearAllCaches(),CC_DEBUG&&console.log("[CacheRender] CacheRender 已完全重置")},e}();t.cacheRender=new o},413:function(e,t){var r=this&&this.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,a=r.call(e),i=[];try{for(;(void 0===t||t-- >0)&&!(n=a.next()).done;)i.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=a.return)&&r.call(a)}finally{if(o)throw o.error}}return i};Object.defineProperty(t,"__esModule",{value:!0}),t.arraysHaveSameElements=function(e,t){if(e.length!==t.length)return!1;for(var r=e.slice().sort(),n=t.slice().sort(),o=0;o<r.length;o++)if(r[o]!==n[o])return!1;return!0},t.shuffleArray=function(e){for(var t,n=e.slice(),o=n.length-1;o>0;o--){var a=Math.floor(Math.random()*(o+1));t=r([n[a],n[o]],2),n[o]=t[0],n[a]=t[1]}return n},t.arraysEqual=function(e,t){if(e.length!==t.length)return!1;for(var r=0;r<e.length;r++)if(e[r]!==t[r])return!1;return!0},t.ensureMaxLength=function(e,t,r){return e.length>=t&&e.shift(),e.push(r),e}},420:function(e,t,r){var n,o=this&&this.__extends||(n=function(e,t){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},n(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)});Object.defineProperty(t,"__esModule",{value:!0}),t.UrlCrypto=void 0;var a=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return o(t,e),t.encrypt=function(e){var t=Buffer.from(e).toString("base64");return this._des(t)},t.decrypt=function(e){var t=this._des(e);return Buffer.from(t,"base64").toString("utf-8")},t._des=function(e){for(var t=e.replace(/\s+/g,"+"),r=t.length,n=this.encodeKey.length,o="",a=0;a<r;a++){var i=t[a],s=this.encodeKey.indexOf(i);-1!==s&&(o+=this.encodeKey[n-s-1])}return o},t.encodeKey="C1eWgtN/ZOJ=qw2TXyhxjV+0SlUL35R6ri9G4uamPfQpK78AdHbBczFnYEskMDIvo",t}(r(2682).ICrypto);t.UrlCrypto=a},590:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.dragonbonesAnim=void 0;var n=r(9291);if(CC_DEBUG){var o=dragonBones.ArmatureDisplay.prototype.playAnimation;dragonBones.ArmatureDisplay.prototype.playAnimation=function(e,t){var r,n,a,i,s,c=this._armatureName,l=null===(r=this.dragonAsset)||void 0===r?void 0:r._dragonBonesJson;if(l){var u=null!==(a=null===(n=this.dragonAsset)||void 0===n?void 0:n._dragonBonesJsonData)&&void 0!==a?a:JSON.parse(l),p=null===(i=null==u?void 0:u.armature)||void 0===i?void 0:i.find((function(e){return e.name===c}));p?(null===(s=p.animation)||void 0===s?void 0:s.find((function(t){return t.name===e})))||console.error("龙骨动画播放错误：armatureName：".concat(this._armatureName," 中没有 animatinName:").concat(e)):console.error("龙骨动画播放错误：armatureName：".concat(this._armatureName," 不存在！，当前要播放的动画名为：").concat(e))}o.apply(this,[e,t])}}var a=function(){function e(){this.dragonbones={}}return e.prototype.play=function(e,t,r,n,o){var a=this;if(o&&t&&r){void 0===n&&(n=1),o.bundleName;var i=o.dragonAssetUrl,s=o.dragonAtlasAssetUrl;if(o.frameSplitting,void 0!==i&&""!==i&&void 0!==s&&""!==s&&void 0!==r&&""!==r&&void 0!==t&&""!==t){var c=new cc.Node,l=c.addComponent(dragonBones.ArmatureDisplay);return e.addChild(c),l.enableBatch=!0,l.updateAnimationCache(r),l.setAnimationCacheMode(dragonBones.ArmatureDisplay.AnimationCacheMode.SHARED_CACHE),l&&(o.completeBack&&o.completeBackObj&&l.addEventListener(dragonBones.EventObject.COMPLETE,o.completeBack,o.completeBackObj),o.frameSplitting?setTimeout((function(){a._play(o,t,r,n,l)}),50):this._play(o,t,r,n,l)),c}}},e.prototype._play=function(e,t,r,o,a){var i=e.bundleName,s=e.dragonAssetUrl,c=e.dragonAtlasAssetUrl;e.frameSplitting,i?n.ResLoader.renderDragonbonesByBundle(i,a,s,c,t,r,o):n.ResLoader.renderDragonbones(a,s,c,t,r,o)},e}();t.dragonbonesAnim=new a},676:function(e,t){var r=this&&this.__awaiter||function(e,t,r,n){return new(r||(r=Promise))((function(o,a){function i(e){try{c(n.next(e))}catch(e){a(e)}}function s(e){try{c(n.throw(e))}catch(e){a(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(i,s)}c((n=n.apply(e,t||[])).next())}))},n=this&&this.__generator||function(e,t){var r,n,o,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},i=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return i.next=s(0),i.throw=s(1),i.return=s(2),"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(c){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,c])}}};Object.defineProperty(t,"__esModule",{value:!0}),t.measure=function(e,t,o){var a=o.value;return o.value=function(){for(var e=[],o=0;o<arguments.length;o++)e[o]=arguments[o];return r(this,void 0,void 0,(function(){var r,o,i;return n(this,(function(n){switch(n.label){case 0:return CC_DEBUG?(r=performance.now(),[4,a.apply(this,e)]):[3,2];case 1:return o=n.sent(),i=performance.now(),CC_DEBUG&&console.log("".concat(t," execution time: ").concat(i-r," milliseconds")),[2,o];case 2:return[2,a.apply(this,e)]}}))}))},o}},783:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.randomInt=function(e,t){return Math.floor(Math.random()*(t-e))+e},t.randomFloat=function(e,t){return Math.random()*(t-e)+e}},1007:function(e,t,r){var n=this&&this.__decorate||function(e,t,r,n){var o,a=arguments.length,i=a<3?t:null===n?n=Object.getOwnPropertyDescriptor(t,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)i=Reflect.decorate(e,t,r,n);else for(var s=e.length-1;s>=0;s--)(o=e[s])&&(i=(a<3?o(i):a>3?o(t,r,i):o(t,r))||i);return a>3&&i&&Object.defineProperty(t,r,i),i};Object.defineProperty(t,"__esModule",{value:!0}),t.envInfo=void 0;var o=r(2520),a=function(){function e(){}return Object.defineProperty(e.prototype,"isProd",{get:function(){return("prod"===MACRO_ENV||"prod-test"===MACRO_ENV)&&NativeBridge.isNative()},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"bundleName",{get:function(){return"com.block.juggle"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"remoteResServerUrl",{get:function(){return MACRO_REMOTE_SERVER_RES_URL},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"hotServerUrl",{get:function(){return"prod"===MACRO_ENV||"prod-test"===MACRO_ENV?MACRO_REMOTE_SERVER_RES_URL+"/hotUpdate":MACRO_REMOTE_SERVER_RES_URL+"/hotUpdate/".concat(MACRO_PLATFORM,"/").concat(MACRO_ENV)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"envs",{get:function(){return{Test:{gameInitUrl:"http://gametester-test.afafb.com/game_init"},Prod:{gameInitUrl:"https://gametester.afafb.com/game_init"}}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"curEnvInfo",{get:function(){return this.envs[this.curEnv]},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"os",{get:function(){var e;if(CC_JSB)switch(cc.sys.os){case cc.sys.OS_ANDROID:e="Android";break;case cc.sys.OS_IOS:e="iOS";break;case cc.sys.OS_WP8:e="WP";break;default:e="Others"}else e=navigator.userAgent.indexOf("Android")>-1||navigator.userAgent.indexOf("Linux")>-1?"Android":navigator.userAgent.indexOf("iPhone")>-1?"iOS":navigator.userAgent.indexOf("Windows Phone")>-1?"WP":"Others";return e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"osVersion",{get:function(){var e,t,r,n,o,a,i,s,c,l="1.0.0";if(CC_JSB)l=cc.sys.osVersion;else{var u=navigator.userAgent,p=u.indexOf("Android")>-1||u.indexOf("Linux")>-1,f=!!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);p&&(l=null!==(n=null===(r=null===(t=null===(e=navigator.userAgent.split(";"))||void 0===e?void 0:e[1])||void 0===t?void 0:t.match(/\d+(\.*\d+)*/g))||void 0===r?void 0:r[0])&&void 0!==n?n:"1.0.0"),f&&(l=null!==(c=null===(s=null===(i=null===(a=null===(o=navigator.userAgent.split(";"))||void 0===o?void 0:o[1])||void 0===a?void 0:a.match(/(\d+)_(\d+)_?(\d+)?/))||void 0===i?void 0:i[0])||void 0===s?void 0:s.replace("_","."))&&void 0!==c?c:"1.0.0")}return l},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"curEnv",{get:function(){return this.isProd?"Prod":"Test"},enumerable:!1,configurable:!0}),n([o.memoize],e.prototype,"envs",null),n([o.memoize],e.prototype,"curEnvInfo",null),n([o.memoize],e.prototype,"os",null),n([o.memoize],e.prototype,"osVersion",null),e}();t.envInfo=new a},1195:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.deepCopy=function(e){return e.length<80?e.map((function(e){return e.slice()})):e.map((function(e){return Array.from(e)}))},t.deepCopyArrayFrom=function(e){return e.map((function(e){return Array.from(e)}))},t.deepCopySlice=function(e){return e.map((function(e){return e.slice()}))},t.deepCopyLoop=function(e){for(var t=[],r=e.length,n=0;n<r;n++){for(var o=e[n],a=o.length,i=new Array(a),s=0;s<a;s++)i[s]=o[s];t[n]=i}return t},t.deepCopyFixed=function(e,t){for(var r=[],n=e.length,o=0;o<n;o++){for(var a=e[o],i=t||a.length,s=new Array(i),c=0;c<i;c++)s[c]=a[c];r[o]=s}return r}},1296:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ModuleEvent=void 0;var r=function(){function e(){this._callback=null}return Object.defineProperty(e.prototype,"callback",{get:function(){return this._callback},enumerable:!1,configurable:!0}),e.prototype.getClass=function(){return this.constructor},e}();t.ModuleEvent=r},1593:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.layerManager=t.LayerManager=t.gameUiLayer=t.BASE_LAYER_CONFIGS=void 0,t.getGameUiLayer=n,t.resetGameUiLayer=function(){r&&cc.isValid(r)&&(r.removeFromParent(),r.destroy()),r=null,console.log("GameLayer: gameUiLayer 已重置")},t.initNodeConfig=a,t.addWidget=s,t.BASE_LAYER_CONFIGS=[{type:"gameUI",name:"gameUiLayer",zIndex:2}];var r=null;function n(){return r&&cc.isValid(r)||(console.log("GameLayer: gameUiLayer 无效，重新创建"),r=o()),r}function o(){var e=new cc.Node;if(e.name="gameUiLayer",t.layerManager.layerScene){var r=t.BASE_LAYER_CONFIGS.find((function(e){return"gameUI"===e.type}));r&&(a(e,r,t.layerManager.layerScene),t.layerManager.setLayerNode("gameUI",e))}return e}function a(e,t,r){e.name=t.name,e.zIndex=t.zIndex,void 0!==t.opacity&&(e.opacity=t.opacity),t.hidden&&(e.active=!1),r.addChild(e),s(e)}t.gameUiLayer=new Proxy({},{get:function(e,t){var r=n();return"function"==typeof r[t]?r[t].bind(r):r[t]},set:function(e,t,r){return n()[t]=r,!0}});var i=function(){function e(){this.layerMap=new Map,this.scene=null}return e.prototype.init=function(){var e=this;this.scene=cc.Canvas.instance.node,this.clear(),t.BASE_LAYER_CONFIGS.forEach((function(t){"gameUI"===t.type&&(r=o(),e.layerMap.set(t.type,r))}))},Object.defineProperty(e.prototype,"layerScene",{get:function(){return cc.isValid(this.scene)||(this.scene=cc.Canvas.instance.node),this.scene},enumerable:!1,configurable:!0}),e.prototype.clear=function(){this.layerMap.forEach((function(e){e.removeFromParent(),e.destroy()})),this.layerMap.clear(),r=null},e.prototype.addLayer=function(e){if(!this.scene)throw new Error("LayerManager not initialized");this.removeLayer(e.type);var t=new cc.Node;return a(t,e,this.scene),this.layerMap.set(e.type,t),t},e.prototype.setLayerNode=function(e,t){if(!this.scene)throw new Error("LayerManager not initialized");this.removeLayer(e),this.layerMap.set(e,t)},e.prototype.removeLayer=function(e){var t=this.layerMap.get(e);return!!t&&(t.removeFromParent(),t.destroy(),this.layerMap.delete(e),!0)},e.prototype.getLayer=function(e){return this.layerMap.get(e)},e.prototype.showLayer=function(e){var t=this.layerMap.get(e);t&&(t.active=!0)},e.prototype.hideLayer=function(e){var t=this.layerMap.get(e);t&&(t.active=!1)},e.prototype.setLayerOpacity=function(e,t){var r=this.layerMap.get(e);r&&(r.opacity=t)},e.prototype.getLayerTypes=function(){return Array.from(this.layerMap.keys())},e}();function s(e){e.width=960,e.height=1707.5,e.anchorX=e.anchorY=0;var t=e.addComponent(cc.Widget);t.isAlignTop=!0,t.isAlignLeft=!0,t.isAlignRight=!0,t.isAlignBottom=!0,t.top=0,t.bottom=0,t.left=0,t.right=0}t.LayerManager=i,t.layerManager=new i},1623:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.audioInfo=t.AudioType=void 0;var n,o=r(9291),a=r(3447),i=r(1771);!function(e){e[e.SOUND=0]="SOUND",e[e.EFFECT=1]="EFFECT"}(n||(t.AudioType=n={}));var s=function(){function e(){}return e.prototype.play=function(e){var t=this;if(e){var r=e.url,s=e.volume,c=e.type,l=e.bundleName;if(r){(0,a.isUndefined)(c)&&(c=n.EFFECT);var u=i.storage.getItem("audioSwitch",!0),p=i.storage.getItem("bgmSwitch",!1);c===n.EFFECT&&!u||c===n.SOUND&&!p||(void 0===s&&(s=1),l?o.ResLoader.loadByBundle(l,r,cc.AudioClip,(function(e,r){t._play(c,s,e,r)})):o.ResLoader.load(r,cc.AudioClip,(function(e,r){t._play(c,s,e,r)})))}}else CC_DEBUG&&console.error("声音播放时播放时选项不能为空！")},e.prototype._play=function(e,t,r,o){r||(e==n.EFFECT?cc.audioEngine.play(o,!1,t):cc.audioEngine.playMusic(o,!0))},e.prototype.stop=function(e){e.url,e.volume;var t=e.type;(0,a.isUndefined)(t)&&(t=n.EFFECT);var r=i.storage.getItem("audioSwitch",!0),o=i.storage.getItem("bgmSwitch",!1);t===n.EFFECT&&!r||t===n.SOUND&&!o||(t==n.EFFECT?cc.audioEngine.stopAllEffects():cc.audioEngine.stopMusic())},e}();t.audioInfo=new s},1644:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.shareObjects=t.setPerformanceTieringType=t.onDidTraitOnActive=t.GBMTraitsMaps=void 0,t.traitsReady=function(e){p=e},t.traitsConfigReady=function(e){f=e},t.trait=function(e,r){return void 0===r&&(r=""),function(r,n,o){t.shareObjects.has(r)||t.shareObjects.set(r,{});var i=t.shareObjects.get(r);i[n]||(i[n]={}),void 0===o.__originalMethod__&&(o.__originalMethod__=o.value);var _=i[n];_.returnState=!1,_.returnValue=l,_.replace=!1,_.methodName=n,_.method=o.__originalMethod__;var v=o.value;_.lastMethod=v,o.value=function(){for(var t,r=this,n=[],o=0;o<arguments.length;o++)n[o]=arguments[o];if(CC_DEBUG&&(p||console.warn("特性脚本:".concat(e," 未准备好，可能会导致特性无法正常工作，请检查特性配置是否正确，或者特性是否已加载。"))),!window.__traitsClassMap__)return v.apply(this,n);var i=a.traitConfigInfo.traitsClassNameMap[e],y=d(e);if(!y)return CC_DEBUG&&(f?i&&p&&console.error("特性:".concat(e," 未实例化成功，可能是当前特性所有在的 bundle 未下载或者特性按需加载出现异常，请检查！！")):console.warn("特性配置:".concat(e," 未准备好，可能会导致特性无法正常工作，请检查特性配置是否正确，或者特性是否已加载。"))),v.apply(this,n);v===_.lastMethod&&(_.replace=!1,_.returnState=!1,_.data={},_.args=[],_.returnValue=l),_.target=this,_.className||(_.className=getClassName(this.constructor));var g=null==i?void 0:i.param;if(g){var m=Object.keys(g)[0];isNaN(+m)?g.active=!0:g=g[u],y._active||(y._id=i.id,y._props=g,y._active=!0,y.onEnable());var b=y._subTraits;if(b)for(var C=0;C<b.length;C++){var E=b[C],M=a.traitConfigInfo.traitsClassNameMap[E.traitName];M&&(E._props=M.param)}}var O=s[e],w=!1;if(O)for(var T=0;T<O.length;T++){var A=null===(t=a.traitConfigInfo.traitsClassNameMap[O[T]])||void 0===t?void 0:t.param;if(A&&0!==Object.keys(A).length){w=!0;break}}if((y.active||w)&&(_.returnState||(_.args=n,_.originalCaller=function(){var e;null===(e=_.method)||void 0===e||e.apply(r,n)},this.shareTraitTarget=_,h(y,"preActive"),y.onActive(_),CC_DEBUG&&c.fire(y.id),h(y,"actived"))),v===_.method){var S=void 0;_.replace||(S=v.apply(this,n));var P=_.returnValue;return P!==l?P:S}return v.apply(this,n)}}},t.getOrCreateTraitInstance=d,t.getNewTraitName=function(e){return e?e.charAt(0).toUpperCase()+e.slice(1)+"Trait":""},t.GBM=function(){return function(e){var r=e,n=getClassName(r);t.GBMTraitsMaps.has(r)||t.GBMTraitsMaps.set(e,n)}},t.templateTrait=function(e){return function(t){if(e){var r=getClassName(t);r&&(s[r]=e)}}};var n=r(9915),o=r(7123),a=r(5651);r(4437);var i=new Map;t.GBMTraitsMaps=new Map;var s={},c=new n.Emitter,l="##undefined##";t.onDidTraitOnActive=c.event;var u=0;t.setPerformanceTieringType=function(e){u=e};var p=!1,f=!1;function d(e){var t,r=null===(t=window.__traitsClassMap__)||void 0===t?void 0:t[e];if(r){var n=i.get(r);if(!n){(n=new r)._state=n.data(),n.onCreate();var o=n.registerSubTraits();if(o){for(var a=[],s=0;s<o.length;s++){var c=o[s];if(i.has(c))a[s]=i.get(c);else{var l=new c;l._state=l.data(),l.onCreate(),a[s]=l,i.set(c,l)}}n._subTraits=a}i.set(r,n)}return n}return null}function h(e,t){var r=o.Trait.activedListenerTraits.get(e.constructor);if(r)for(var n=0;n<r.length;n++){var a=r[n];(null==a?void 0:a[t])&&a[t](e)}}t.shareObjects=new Map,window.TRAIT=function(e){return d(e)}},1771:function(e,t){var r=this&&this.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,a=r.call(e),i=[];try{for(;(void 0===t||t-- >0)&&!(n=a.next()).done;)i.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=a.return)&&r.call(a)}finally{if(o)throw o.error}}return i};Object.defineProperty(t,"__esModule",{value:!0}),t.storage=void 0;var n=function(){function e(){this.cacheData={}}return e.prototype.initPrefix=function(t){e.prefix=t},e.prototype.setItem=function(t,r){try{var n=e.prefix+t,o=void 0,a=typeof r;null===r||"string"===a||"number"===a||"boolean"===a||"bigint"===a||"undefined"===a?o=r:"object"===a?o=JSON.stringify(r):console.error("【".concat(t,"】【setItem】未实现的数据类型存储，值为：").concat(r)),this.cacheData[t]={type:a,data:o};var i=a+e.valueTypeSplit+o;localStorage.setItem(n,i)}catch(e){throw e instanceof DOMException&&(22===e.code||1014===e.code||"QuotaExceededError"===e.name||"NS_ERROR_DOM_QUOTA_REACHED"===e.name)?new Error("LocalStorage is full"):e}},e.prototype.getItem=function(t,n){if(Object.prototype.hasOwnProperty.call(this.cacheData,t)){var o=this.cacheData[t],a=o.type,i=o.data;return null===i?i:"object"===a?JSON.parse(i):i}var s=e.prefix+t,c=localStorage.getItem(s),l=void 0;if(null!==c&&""!==c){var u=c.split(e.valueTypeSplit);if(2!==(null==u?void 0:u.length))throw new Error("【Storage-getItem】存储长度应该为2，实际为：".concat(null==u?void 0:u.length,"！"));var p=r(u,2),f=p[0],d=p[1],h=void 0;switch(f){case"string":h=l=d;break;case"number":case"bigint":h=l=+d;break;case"boolean":h=l=JSON.parse(d);break;case"undefined":h=l=void 0;break;case"object":l=JSON.parse(d),h=d;break;default:console.error("【".concat(t,"】【getItem】未实现的数据类型存储"))}this.cacheData[t]={type:f,data:h}}else l=n;return l},e.prototype.remove=function(t){var r=e.prefix+t;localStorage.removeItem(r)},e.prototype.clear=function(){localStorage.clear()},e.prefix="block-blast-",e.valueTypeSplit="^_^",e}();t.storage=new n},1781:(e,t)=>{function r(e){return function(t,r,n){var o=null,a=null;if("function"==typeof n.value?(o="value",a=n.value):"function"==typeof n.get&&(o="get",a=n.get),!a||!o)throw new Error("not supported");n[o]=e(a,r)}}Object.defineProperty(t,"__esModule",{value:!0}),t.decorate=r,t.debounce=function(e,t){return void 0===e&&(e=1e3),r((function(r,n){var o="$debounce$".concat(n),a={};return a[o]=-1e7,function(){for(var n=[],i=0;i<arguments.length;i++)n[i]=arguments[i];var s=Date.now();if(s-a[o]>=e)return a[o]=s,r.apply(this,n);"string"!=typeof t&&"number"!=typeof t||console.warn(t)}}))}},2394:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ScreenAdapter=function(){return function(e,t,r){var n=r.value;return r.value=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=cc.view.getVisibleSize();if(r.height/r.width>=1334/750)(o=cc.Canvas.instance).fitHeight=!1,o.fitWidth=!0;else if(r.height/r.width<1334/750){var o;(o=cc.Canvas.instance).fitHeight=!0,o.fitWidth=!1}return n.apply(this,e)},r}}},2411:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.randomList=function(e,t,r){if(void 0===r&&(r=!1),r){for(var n=[],o=0;o<t;o++){var a=Math.floor(Math.random()*(e.length-1));n.push(e[a])}return n}return function(e,t){void 0===t&&(t=!1),t&&(e=e.concat());for(var r=e.length-1;r>0;r--){var n=Math.floor(Math.random()*r),o=e[r];e[r]=e[n],e[n]=o}return e}(e).slice(0,t)}},2515:(e,t)=>{var r;Object.defineProperty(t,"__esModule",{value:!0}),t.ModuleManager=t.ModuleType=void 0,function(e){e.Common="common",e.Class="class",e.Chapter="journey"}(r||(t.ModuleType=r={}));var n=function(){function e(){}return Object.defineProperty(e,"moduleType",{get:function(){return this._moduleType},enumerable:!1,configurable:!0}),e.setCurrentModuleType=function(e){this._moduleType=e},Object.defineProperty(e,"moduleList",{get:function(){return this._moduleList},enumerable:!1,configurable:!0}),e.resigerModule=function(e){this._modules=e},e.startModule=function(e){for(var t=0,r=this._modules.length;t<r;t++){var n=this._modules[t];if(!this._moduleConstructorMaps.has(n)){this._moduleConstructorMaps.set(n,!0);var o=new n;o._moduleType=e,this._moduleList[t]=o,o.startProxy(e)}}},e.reset=function(){this._moduleList.forEach((function(e){try{e&&e.proxyClassMap&&"function"==typeof e.proxyClassMap.clear&&e.proxyClassMap.clear()}catch(e){console.error("[ModuleManager] 清理模块时发生错误:",e)}})),this._moduleList=[],this._moduleConstructorMaps.clear(),this._moduleType=r.Common},e._moduleList=[],e._moduleConstructorMaps=new Map,e._moduleType=r.Common,e}();t.ModuleManager=n},2520:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.memoize=function(e,t,r){var n=null,o=null;if("function"==typeof r.value?(n="value",0!==(o=r.value).length&&console.warn("Memoize should only be used in functions with zero parameters")):"function"==typeof r.get&&(n="get",o=r.get),!o)throw new Error("not supported");var a="$memoize$".concat(t);r[n]=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return this.hasOwnProperty(a)||Object.defineProperty(this,a,{configurable:!1,enumerable:!1,writable:!1,value:o.apply(this,e)}),this[a]}}},2682:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ICrypto=void 0;var r=function(){function e(){}return e.encrypt=function(e){return e},e.decrypt=function(e){return e},e}();t.ICrypto=r},2834:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.traitConfigActiveBlockListInfo=void 0;t.traitConfigActiveBlockListInfo=new function(){this.blockList=[]}},3446:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NativeBridge=t.ANDROID_ACTIVE=void 0,t.ANDROID_ACTIVE="org/cocos2dx/javascript/AppActivity";var r=function(){function e(){}return e.isNative=function(){var e;return null===(e=cc.sys)||void 0===e?void 0:e.isNative},e.send=function(e,r,n,o){if(void 0===o&&(o=t.ANDROID_ACTIVE),this.isNative())return""==r?jsb.reflection.callStaticMethod(o,e,n):jsb.reflection.callStaticMethod(o,e,n,r)},e}();t.NativeBridge=r},3447:(e,t)=>{function r(e){return"string"==typeof e}function n(e){return void 0===e}function o(e){return n(e)||null===e}Object.defineProperty(t,"__esModule",{value:!0}),t.isString=r,t.isStringArray=function(e){return Array.isArray(e)&&e.every((function(e){return r(e)}))},t.isObject=function(e){return!("object"!=typeof e||null===e||Array.isArray(e)||e instanceof RegExp||e instanceof Date)},t.isTypedArray=function(e){var t=Object.getPrototypeOf(Uint8Array);return"object"==typeof e&&e instanceof t},t.isNumber=function(e){return"number"==typeof e&&!isNaN(e)},t.isIterable=function(e){return!!e&&"function"==typeof e[Symbol.iterator]},t.isBoolean=function(e){return!0===e||!1===e},t.isUndefined=n,t.isDefined=function(e){return!o(e)},t.isUndefinedOrNull=o,t.isFunction=function(e){return"function"==typeof e},t.getType=function(e){switch(Object.prototype.toString.call(e)){case"[object Object]":default:return"object";case"[object Number]":return"number";case"[object String]":return"string";case"[object Boolean]":return"boolean";case"[object Array]":return"array";case"[object Undefined]":return"undefined";case"[object Null]":return"null";case"[object Date]":return"date";case"[object RegExp]":return"regExp";case"[object Function]":return"function"}}},3683:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.cacheComponents=void 0;var n=r(2515),o=function(){function e(){e._cacheComponents=this}return e.prototype.componentEnabledIntercept=function(){e._componentOnEnabledIntercept||(e._componentOnEnabledIntercept=!0,e._originalCompSchedulerOnEnabled=cc.director._compScheduler._onEnabled,cc.director._compScheduler._onEnabled=function(t){var r=n.ModuleManager.moduleType,o=t.constructor,a=e._cacheAllComponents.get(o);a||(a={},e._cacheAllComponents.set(o,a)),a[r]=t,e._originalCompSchedulerOnEnabled.apply(this,[t]);var i=e._resolves.get(o);if(i){var s=i[r];if(s){for(var c=0;c<s.length;c++)(0,s[c])(t);s.length=0}}t.node&&!t.node.active&&(t.node.active=!0)})},e.prototype.Cinst=function(t){var r=e._cacheAllComponents.get(t),o=n.ModuleManager.moduleType;if(r)return r[o]||r[n.ModuleType.Common]},e.prototype.CinstAsync=function(t){return new Promise((function(r,o){var a=e._cacheComponents.Cinst(t);if(a)r(a);else{var i=e._resolves.get(t);i||(i={},e._resolves.set(t,i));var s=n.ModuleManager.moduleType,c=i[s];c||(c=[],i[s]=c,c.push(r)),e._cacheComponents.componentEnabledIntercept()}}))},e.prototype.clearAllCaches=function(){e._resolves.forEach((function(e,t){for(var r in e){var n=e[r];n.forEach((function(e){})),n.length=0}})),e._cacheAllComponents.clear(),e._resolves.clear(),CC_DEBUG&&console.log("[CacheComponents] 所有组件缓存已清理")},e.prototype.restoreComponentScheduler=function(){e._componentOnEnabledIntercept&&e._originalCompSchedulerOnEnabled&&(cc.director._compScheduler._onEnabled=e._originalCompSchedulerOnEnabled,e._componentOnEnabledIntercept=!1,CC_DEBUG&&console.log("[CacheComponents] 组件调度器劫持已恢复"))},e.prototype.reset=function(){this.clearAllCaches(),this.restoreComponentScheduler(),CC_DEBUG&&console.log("[CacheComponents] CacheComponents 已完全重置")},e._cacheAllComponents=new Map,e._resolves=new Map,e._originalCompSchedulerOnEnabled=null,e}();t.cacheComponents=new o},3703:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.prefabInfo=void 0;var r=function(){function e(){this._prefabs=new Map}return e.prototype.getOrCreatePrefabInstantiate=function(e){var t=this._prefabs.get(e);return t||(t=cc.instantiate(e),this._prefabs.set(e,t)),t},e.prototype.clearAllCache=function(){this._prefabs.forEach((function(e,t){cc.isValid(e)&&e.destroy()})),this._prefabs.clear()},e}();t.prefabInfo=new r},4119:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DeferredPromise=void 0;var r=function(){function e(){this._resolved=!1,this._promiseQueue=[]}return Object.defineProperty(e.prototype,"isResoved",{get:function(){return this._resolved},enumerable:!1,configurable:!0}),e.prototype.wait=function(){var e=this;return new Promise((function(t){e._promiseQueue.push({resolve:t})}))},e.prototype.reset=function(){this._resolved=!1,this._promiseQueue.length=0},e.prototype.resolve=function(e){for(;this._promiseQueue.length>0;)(0,this._promiseQueue.shift().resolve)(e);this._resolved=!0},e}();t.DeferredPromise=r},4307:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getKeyByValue=function(e,t){for(var r in e)if(e[r]===t)return r},t.isValueInEnum=function(e,t){return Object.values(t).includes(e)}},4437:(e,t)=>{function r(e){return function(t){t.prototype.__classname__||(t.prototype.__classname__=e)}}function n(e){return cc.js.getClassName(e)}Object.defineProperty(t,"__esModule",{value:!0}),t.classId=r,t.getClassName=n,window.classId=r,window.getClassName=n},4768:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.executeRenderingOptimize=function(){CC_EDITOR||Object.defineProperty(cc.Node.prototype,"active",{get:function(){return this._active},set:function(e){if(function(e,t){var r=e._components&&e._components.find((function(e){return e.__fullscreen__}));if(e.opacity=255,r)if(t){if(r.__inactives__=[],e.parent){var n=function(e){if(!(e.parent instanceof cc.Scene)&&e.parent){if(e.parent.children){var t=e.parent.children.indexOf(e);e.parent.children.slice(0,t).forEach((function(e){e.opacity>0&&e.active&&r.__inactives__.push(e),e.opacity=0}))}n(e.parent)}};n(e)}}else{var o=r.__inactives__;if(o){for(var a=0;a<o.length;a++)o[a].opacity=255;r.__inactives__.length=0}}}(this,e=!!e),this._active!==e){this._active=e;var t=this._parent;t&&t._activeInHierarchy&&cc.director._nodeActivator.activateNode(this,e)}}})}},5224:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.applyAdapterFringe=function(e){if(e){var t=e.getComponent(cc.Widget);if(t){var o=cc.view.getFrameSize(),a=cc.view.getVisibleSize();if(function(e,t){return!(e.width/e.height>760/1334||cc.sys.os!==cc.sys.OS_ANDROID&&!cc.sys.isBrowser||!(e.height/e.width>r||t.height/t.width>r))}(o,a)){var i=Math.max(o.height,a.height);t.top+=n*i}}}};var r=1280/716,n=.037},5539:function(e,t){var r=this&&this.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(t,"__esModule",{value:!0}),t.equal=function e(t,n,o){var a,i,s,c;if(void 0===o&&(o=[]),t===n)return!0;if("object"!=typeof t||null===t||"object"!=typeof n||null===n)return!1;try{for(var l=r(o),u=l.next();!u.done;u=l.next()){var p=u.value;if(p[0]===t&&p[1]===n)return!0}}catch(e){a={error:e}}finally{try{u&&!u.done&&(i=l.return)&&i.call(l)}finally{if(a)throw a.error}}if(o.push([t,n]),t.constructor!==n.constructor)return!1;if(t.constructor===Number||t.constructor===String||t.constructor===Boolean)return t.valueOf()===n.valueOf();if(Array.isArray(t)&&Array.isArray(n)){if(t.length!==n.length)return!1;for(var f=0;f<t.length;f++)if(!e(t[f],n[f],o))return!1;return!0}var d=Object.keys(t),h=Object.keys(n);if(d.length!==h.length)return!1;try{for(var _=r(d),v=_.next();!v.done;v=_.next()){var y=v.value;if(!n.hasOwnProperty(y))return!1;if(!e(t[y],n[y],o))return!1}}catch(e){s={error:e}}finally{try{v&&!v.done&&(c=_.return)&&c.call(_)}finally{if(s)throw s.error}}return!0}},5651:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.traitConfigInfo=void 0;var n=r(2834),o=r(1771),a=function(){function e(){this._traits={},this._traitsById={},this._useTraits={},this._cacheUsedTraits={},this._traitsClassNameMap={},this._useActiveUserTraits={},this._cacheUsedActiveUserTraits={},this._configReady=!1,this._ready=!1}return Object.defineProperty(e.prototype,"traitsClassNameMap",{get:function(){return this._traitsClassNameMap},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"configReady",{get:function(){return this._configReady},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"ready",{get:function(){return this._ready},enumerable:!1,configurable:!0}),e.prototype.initialize=function(e){if(e){var t=e.feature,r=this._traits,n=this._traitsById;for(var o in t)for(var a=t[o],i=0;i<a.length;i++){var s=a[i],c=s.id,l=s.param;r[o]||(r[o]={}),r[o][c]=l,n[c]={traitName:o,param:l}}}else console.error("特性初始化配置发生错误")},e.prototype.createUseTraits=function(e){this._curActiveConfig=e;var t=this._curActiveConfig,r=t.features,n=t.plan,a=localStorage.getItem("blockblast.gameWayNum"),i=a?parseInt(a):n;o.storage.setItem("gameWayNum",i);for(var s=0;s<r.length;s++){var c=r[s].id;this._useTraits[c]=r[s]}},e.prototype.createActiveTraits=function(e){for(var t=e.features,r=0;r<t.length;r++){var n=t[r].id;this._useActiveUserTraits[n]=t[r]}},e.prototype.createActiveTraitClassNameMaps=function(){var e=this._traitsById,t=this._curActiveConfig.features.filter((function(e){return!n.traitConfigActiveBlockListInfo.blockList.some((function(t){return t===e.id}))}));CC_DEBUG&&(console.log("%c[激活的特性]","color:#fff;background:#ff019a;",t),o.storage.setItem("activeFeatures",t));for(var r=0;r<t.length;r++){var a=t[r].id,i=e[a];if(i){var s=i.traitName,c=i.param,l=s.charAt(0).toUpperCase()+s.slice(1)+"Trait";this._traitsClassNameMap[l]={id:a,param:c}}else CC_DEBUG&&console.error("特性总表 cfg.ts 配置没有特性：".concat(a))}},e.prototype.createWhiteTraitClassNameMaps=function(e){for(var t=this._traitsById,r=0;r<e.length;r++){var n=e[r],o=t[n];if(o){var a=o.traitName,i=o.param,s=a.charAt(0).toUpperCase()+a.slice(1)+"Trait";this._traitsClassNameMap[s]={id:n,param:i}}else CC_DEBUG&&console.error("特性总表 cfg.ts 配置没有特性：".concat(n))}CC_DEBUG&&console.log("%c[激活的特性类名]","color:#fff;background:#ff019a;",this._traitsClassNameMap)},e.prototype.createExtraTraitClassNameMaps=function(e){this._traitsById;for(var t=0;t<e.length;t++){var r=e[t],n=r.id,o=r.name,a=r.param;this._traitsClassNameMap[o]={id:n,param:a}}},e.prototype.loadDynamicTrait=function(e){for(var t=this._traitsById,r=0;r<e.length;r++){var n=e[r],o=t[n];if(o){var a=o.traitName,i=o.param,s=a.charAt(0).toUpperCase()+a.slice(1)+"Trait";this._traitsClassNameMap[s]={id:n,param:i},console.log("动态新加特性：".concat(n))}else CC_DEBUG&&console.error("动态新加特性 特性总表 cfg.ts 配置没有特性：".concat(n))}},e.prototype.traitData=function(e,t){if(isNaN(t)){if(this._cacheUsedTraits[e])return this._cacheUsedTraits[e];var r=this._traits[e];for(var n in r)if(this._useTraits[n])return this._cacheUsedTraits[e]=r[n],this._cacheUsedTraits[e]}return this._traits[e][t]},e.prototype.traitActiveUserData=function(e,t){if(isNaN(t)){if(this._cacheUsedActiveUserTraits[e])return this._cacheUsedActiveUserTraits[e];var r=this._traits[e];for(var n in r)if(this._useActiveUserTraits[n])return this._cacheUsedActiveUserTraits[e]=r[n],this._cacheUsedActiveUserTraits[e]}return this._traits[e][t]},e}();t.traitConfigInfo=new a},5679:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.adapterFringe=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return function(t,r,o){var a=o.value;return o.value=function(){for(var t=this,r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];return e.forEach((function(e){var r=t[e];r&&(0,n.applyAdapterFringe)(r)})),a.apply(this,r)},o}};var n=r(5224)},5817:function(e,t){var r=this&&this.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(t,"__esModule",{value:!0}),t.first=function(e,t,r){void 0===t&&(t=function(e){return!!e}),void 0===r&&(r=null);var n=0,o=e.length,a=function(){if(n>=o)return Promise.resolve(r);var i=e[n++];return Promise.resolve(i()).then((function(e){return t(e)?Promise.resolve(e):a()}))};return a()},t.firstParallel=function(e,t,n){if(void 0===t&&(t=function(e){return!!e}),void 0===n&&(n=null),0===e.length)return Promise.resolve(n);var o=e.length,a=function(){var t,n,a,i;o=-1;try{for(var s=r(e),c=s.next();!c.done;c=s.next())null===(i=(a=c.value).cancel)||void 0===i||i.call(a)}catch(e){t={error:e}}finally{try{c&&!c.done&&(n=s.return)&&n.call(s)}finally{if(t)throw t.error}}};return new Promise((function(i,s){var c,l;try{for(var u=r(e),p=u.next();!p.done;p=u.next())p.value.then((function(e){--o>=0&&t(e)?(a(),i(e)):0===o&&i(n)})).catch((function(e){--o>=0&&(a(),s(e))}))}catch(e){c={error:e}}finally{try{p&&!p.done&&(l=u.return)&&l.call(u)}finally{if(c)throw c.error}}}))}},5967:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.nextFrame=function(){var e=this;return new Promise((function(t){e.callLater((function(){t(null)}),null)}))},t.callLater=function(e,t){cc.director.once(cc.Director.EVENT_AFTER_UPDATE,e,t)}},5979:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.timeout=function(e,t,r){for(var n=[],o=3;o<arguments.length;o++)n[o-3]=arguments[o];var a=setTimeout((function(){clearTimeout(a),e&&cc.isValid(e,!0)&&(null==t||t.apply(this,[n]))}),r,n);return a}},6078:(e,t)=>{function r(e){return function(t,r,n){var o=null,a=null;if("function"==typeof n.value?(o="value",a=n.value):"function"==typeof n.get&&(o="get",a=n.get),!a||!o)throw new Error("not supported");n[o]=e(a,r)}}Object.defineProperty(t,"__esModule",{value:!0}),t.decorate=r,t.throttle=function(e,t){return void 0===e&&(e=500),void 0===t&&(t=!0),r((function(r,n){var o=Date.now(),a=null;return function(){for(var n=[],i=0;i<arguments.length;i++)n[i]=arguments[i];var s=this;if(t){var c=Date.now();c-o>=e&&(r.apply(s,n),o=c)}else clearTimeout(a),a=setTimeout((function(){r.apply(s,n)}),e)}}))},t.throttleCall=function(e,t,r){void 0===t&&(t=500),void 0===r&&(r=!0);var n=Date.now(),o=null;return function(){for(var a=[],i=0;i<arguments.length;i++)a[i]=arguments[i];var s=this;if(r){var c=Date.now();c-n>=t&&(e.apply(s,a),n=c)}else clearTimeout(o),o=setTimeout((function(){e.apply(s,a)}),t)}}},6282:function(e,t){var r=this&&this.__awaiter||function(e,t,r,n){return new(r||(r=Promise))((function(o,a){function i(e){try{c(n.next(e))}catch(e){a(e)}}function s(e){try{c(n.throw(e))}catch(e){a(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(i,s)}c((n=n.apply(e,t||[])).next())}))},n=this&&this.__generator||function(e,t){var r,n,o,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},i=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return i.next=s(0),i.throw=s(1),i.return=s(2),"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(c){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,c])}}};Object.defineProperty(t,"__esModule",{value:!0}),t.ObjectPool=void 0;var o=function(){function e(e,t,r){this._create=e,this._reset=t,this._option=r,this.pool=[],this.inUsed=new Set,this._init()}return e.prototype._init=function(){return r(this,void 0,void 0,(function(){var e,t,r,o,a,i;return n(this,(function(n){switch(n.label){case 0:if(!this._option)return[3,6];e=this._option,t=e.size,r=e.args,o=e.onCreateComplete,isNaN(t)&&(t=1),n.label=1;case 1:n.trys.push([1,5,,6]),n.label=2;case 2:return this.pool.length<t?[4,this._create(r)]:[3,4];case 3:return a=n.sent(),this.inUsed.add(a),this.pool.push(a),[3,2];case 4:return o&&o(null),[3,6];case 5:return i=n.sent(),o&&o(i),[3,6];case 6:return[2]}}))}))},Object.defineProperty(e.prototype,"poolSize",{get:function(){return this.pool.length},enumerable:!1,configurable:!0}),e.prototype.isInUsed=function(e){return this.inUsed.has(e)},e.prototype.get=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return r(this,void 0,void 0,(function(){var t;return n(this,(function(r){switch(r.label){case 0:return this.pool.length>0?(t=this.pool.pop(),this._reset(t),[3,3]):[3,1];case 1:return[4,this._create(e)];case 2:t=r.sent(),r.label=3;case 3:return this.inUsed.add(t),[2,t]}}))}))},e.prototype.asyncGet=function(){for(var e=this,t=[],o=0;o<arguments.length;o++)t[o]=arguments[o];return new Promise((function(o,a){return r(e,void 0,void 0,(function(){var e,r;return n(this,(function(n){switch(n.label){case 0:return this.pool.length>0?(e=this.pool.pop(),this._reset(e),this.inUsed.add(e),o(e),[3,4]):[3,1];case 1:return n.trys.push([1,3,,4]),[4,this._create(t)];case 2:return e=n.sent(),this.inUsed.add(e),o(e),[3,4];case 3:return r=n.sent(),a(r),[3,4];case 4:return[2]}}))}))}))},e.prototype.release=function(e){this.inUsed.has(e)&&(this.inUsed.delete(e),this.pool.push(e))},e.prototype.clear=function(){this.pool.length=0,this.inUsed.clear()},e}();t.ObjectPool=o},6304:function(e,t){var r,n=this&&this.__extends||(r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},r(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(t,"__esModule",{value:!0}),t.TimeoutBarrier=t.Barrier=void 0;var o=function(){function e(){var e=this;this._isOpen=!1,this._promise=new Promise((function(t,r){e._completePromise=t}))}return Object.defineProperty(e.prototype,"isOpen",{get:function(){return this._isOpen},enumerable:!1,configurable:!0}),e.prototype.reset=function(){this._isOpen=!1},e.prototype.open=function(){this._isOpen=!0,this._completePromise(!0)},e.prototype.wait=function(){return this._promise},e}();t.Barrier=o;var a=function(e){function t(t){var r=e.call(this)||this;return r._timeout=setTimeout((function(){return r.open()}),t),r}return n(t,e),t.prototype.open=function(){clearTimeout(this._timeout),e.prototype.open.call(this)},t}(o);t.TimeoutBarrier=a},6435:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.EventVo=void 0;var r=function(){function e(){this._id=0,e._uniqueID+=1,this._id=e._uniqueID}return Object.defineProperty(e.prototype,"id",{get:function(){return this._id},enumerable:!1,configurable:!0}),e._uniqueID=0,e}();t.EventVo=r},6794:function(e,t){var r,n=this&&this.__extends||(r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},r(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),o=this&&this.__decorate||function(e,t,r,n){var o,a=arguments.length,i=a<3?t:null===n?n=Object.getOwnPropertyDescriptor(t,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)i=Reflect.decorate(e,t,r,n);else for(var s=e.length-1;s>=0;s--)(o=e[s])&&(i=(a<3?o(i):a>3?o(t,r,i):o(t,r))||i);return a>3&&i&&Object.defineProperty(t,r,i),i},a=this&&this.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,a=r.call(e),i=[];try{for(;(void 0===t||t-- >0)&&!(n=a.next()).done;)i.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=a.return)&&r.call(a)}finally{if(o)throw o.error}}return i},i=this&&this.__spreadArray||function(e,t,r){if(r||2===arguments.length)for(var n,o=0,a=t.length;o<a;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0});var s=cc._decorator,c=s.ccclass,l=(s.property,function(e){function t(){var t=e.apply(this,i([],a(arguments),!1))||this;return t._state={},t}return n(t,e),Object.defineProperty(t.prototype,"state",{get:function(){return this._state},enumerable:!1,configurable:!0}),t.prototype.setState=function(e,t,r){if(this._state!==e){var n=!0;this._state&&(n=this.shouldComponentUpdate(e)),"[object Object]"!==Object.prototype.toString.call(e)?this._state=e:(this._state||(this._state=Object.create(null)),t?this._state=e:this.deepCopy(this._state,e)),n&&(this.componentWillUpdate(this._state),this.render(),r&&r())}},t.prototype.deepCopy=function(e,t){for(var r in t){var n=t[r];e[r]=n}},t.prototype.componentWillMount=function(){},t.prototype.componentDidMount=function(){},t.prototype.componentWillUnmount=function(){},t.prototype.shouldComponentUpdate=function(e){return!0},t.prototype.componentWillUpdate=function(e){},t.prototype.componentDidUpdate=function(e){},t.prototype.render=function(){},o([c],t)}(cc.Component));t.default=l},6863:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getUrlParameterValue=function(e){if(!globalThis.location)return"";for(var t=globalThis.location.search.substring(1).split("&"),r=0;r<t.length;r++){var n=t[r].split("=");if(n[0]===e)return decodeURIComponent(n[1])}return""}},7019:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.task=void 0;var r=function(){function e(){this.taskList=[]}return e.prototype.run=function(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];window.requestIdleCallback||(window.requestIdleCallback=function(e){var t=Date.now();return setTimeout((function(){e({didTimeout:!1,timeRemaining:function(){return Math.max(0,30-(Date.now()-t))}})}))},window.cancelIdleCallback=function(e){clearTimeout(e)}),this.taskList.push({handler:e,args:t}),this.taskHandle||(this.taskHandle=window.requestIdleCallback(this.idleRequestCallback.bind(this),{timeout:20}))},e.prototype.idleRequestCallback=function(e){for(;(e.timeRemaining()>5||e.didTimeout)&&this.taskList.length>0;){var t=this.taskList.shift();t.handler&&t.handler.apply(this,t.args)}this.taskList.length>0?this.taskHandle=window.requestIdleCallback(this.idleRequestCallback.bind(this),{timeout:20}):(cancelIdleCallback(this.taskHandle),this.taskHandle=0)},e}();t.task=new r},7035:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.waitFor=void 0;var r=function(){function e(){this._waitFors={}}return e.prototype.start=function(e){var t,r;this._waitFors[e]={startTime:Date.now(),promise:new Promise((function(e,n){t=e,r=n})),state:!1},this._waitFors[e].resolve=t,this._waitFors[e].reject=r},e.prototype.end=function(e){var t=this._waitFors[e];t?(t.endTime=Date.now(),t.state=!0):console.error("waitFor ".concat(e," end 时需先执行 start!"))},e.prototype.wait=function(e,t){var r=this._waitFors[e];if(r){var n=r.resolve,o=r.reject;return r.state?n&&n(r.state):r.interval=setInterval((function(){r.state?(clearInterval(r.interval),n(e)):t&&!isNaN(t.timeout)&&Date.now()-r.startTime>t.timeout&&(clearInterval(r.interval),o("wait for ".concat(e," is timeout!")))}),16),r.promise}},e}();t.waitFor=new r},7123:function(__unused_webpack_module,exports,__webpack_require__){var __awaiter=this&&this.__awaiter||function(e,t,r,n){return new(r||(r=Promise))((function(o,a){function i(e){try{c(n.next(e))}catch(e){a(e)}}function s(e){try{c(n.throw(e))}catch(e){a(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(i,s)}c((n=n.apply(e,t||[])).next())}))},__generator=this&&this.__generator||function(e,t){var r,n,o,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},i=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return i.next=s(0),i.throw=s(1),i.return=s(2),"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(c){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,c])}}};Object.defineProperty(exports,"__esModule",{value:!0}),exports.Trait=void 0;var TraitConfigInfo_1=__webpack_require__(5651),Trait=function(){function Trait(){this._state={},this._props={},this._active=!1,this._subTraits=[]}return Object.defineProperty(Trait,"activedListenerTraits",{get:function(){return Trait._activedListenerTraits},enumerable:!1,configurable:!0}),Object.defineProperty(Trait.prototype,"id",{get:function(){return this._id},enumerable:!1,configurable:!0}),Object.defineProperty(Trait.prototype,"condition",{get:function(){return this._condition},enumerable:!1,configurable:!0}),Object.defineProperty(Trait.prototype,"traitName",{get:function(){return getClassName(this.constructor)},enumerable:!1,configurable:!0}),Object.defineProperty(Trait.prototype,"state",{get:function(){return this._state},enumerable:!1,configurable:!0}),Trait.prototype.data=function(){return{}},Object.defineProperty(Trait.prototype,"props",{get:function(){return this._props},enumerable:!1,configurable:!0}),Object.defineProperty(Trait.prototype,"active",{get:function(){var _active=this.props&&Object.keys(this.props).length>0;if(!this._condition)return _active;try{return _active&&eval(this._condition)}catch(e){return console.error("执行条件特性激活发生错误：",e.stack),_active}},set:function(e){var t;this._active!==e&&(this._active=e,e?(this._props=null===(t=TraitConfigInfo_1.traitConfigInfo.traitsClassNameMap[this.traitName])||void 0===t?void 0:t.param,this.onEnable()):(this._props={},this.onDisable()))},enumerable:!1,configurable:!0}),Trait.prototype.registerSubTraits=function(){return null},Trait.prototype.getSubTraits=function(){return this._subTraits},Trait.prototype.onCreate=function(){},Trait.prototype.onEnable=function(){},Trait.prototype.onDisable=function(){},Trait.prototype.setState=function(e){for(var t in e){var r=e[t];this._state[t]=r}},Trait.prototype.onActive=function(e){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(e){return[2]}))}))},Trait.onTraitActive=function(e,t,r){this._activedListenerTraits.has(e)||this._activedListenerTraits.set(e,[]),this._activedListenerTraits.get(e).push({preActive:t,actived:r})},Trait._activedListenerTraits=new Map,Trait}();exports.Trait=Trait},7585:function(e,t,r){var n=this&&this.__awaiter||function(e,t,r,n){return new(r||(r=Promise))((function(o,a){function i(e){try{c(n.next(e))}catch(e){a(e)}}function s(e){try{c(n.throw(e))}catch(e){a(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(i,s)}c((n=n.apply(e,t||[])).next())}))},o=this&&this.__generator||function(e,t){var r,n,o,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},i=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return i.next=s(0),i.throw=s(1),i.return=s(2),"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(c){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,c])}}};Object.defineProperty(t,"__esModule",{value:!0}),t.UI=void 0;var a=r(1593),i=r(9291),s=function(){function e(){}return e.setModalPrefab=function(e){"string"==typeof e?this.modalConfig.url=e:this.modalConfig=e},e.loadPrefabAsset=function(e){return n(this,void 0,void 0,(function(){var t,r;return o(this,(function(n){switch(n.label){case 0:if(!e||!e.url)throw new Error("[UI] 无效的预制体配置");n.label=1;case 1:return n.trys.push([1,6,,7]),e.bundleName?(t={bundleName:e.bundleName},e.version&&(t.version=e.version),[4,i.ResLoader.asyncLoadByBundle(t,e.url,cc.Prefab)]):[3,3];case 2:return[2,n.sent()];case 3:return[4,i.ResLoader.asyncLoad(e.url,cc.Prefab)];case 4:return[2,n.sent()];case 5:return[3,7];case 6:throw r=n.sent(),console.error("[UI] 加载预制体失败: ".concat(e.url),r),r;case 7:return[2]}}))}))},e.hideAll=function(){for(var t in this.uiCache){var r=e.uiCache[t];r.node&&(r.node.active=!1)}},e.clearAllCache=function(){try{this.hideAll(),this.uiCache={},this.uiIsLoading={},this.uiIsCancelLoad={},this.resolves={},this.lastPage=null,CC_DEBUG&&console.log("[UI] 所有UI缓存已清理")}catch(e){console.error("[UI] 清理UI缓存时发生错误:",e)}},e.activeState=function(t){for(var r in this.uiCache)if(r==t){var n=e.uiCache[r];if(n.node)return n.node.active}return!1},e.cancelLoad=function(e){this.uiIsCancelLoad[e.url]=!0},e.clearCache=function(t){delete e.uiCache[t],delete this.uiIsLoading[t],delete this.uiIsCancelLoad[t]},e.addEventListener=function(e,t){this.events[e]||(this.events[e]=[]),this.events[e].push(t)},e.removeEventListener=function(e,t){if(this.events[e]){var r=this.events[e].indexOf(t);-1!=r&&this.events[e].splice(r,1)}},e.show=function(t,r){var i=this;return new Promise((function(s,c){return n(i,void 0,void 0,(function(){var n,i,l,u,p,f,d,h,_,v,y,g,m;return o(this,(function(o){switch(o.label){case 0:if(CC_DEBUG&&"TouchEffect"!==t.name&&console.log("ui.show() === =>",t.name),this.resolves[t.url]||(this.resolves[t.url]=[]),this.resolves[t.url].push(s),this.uiIsLoading[t.url])return[2];o.label=1;case 1:return o.trys.push([1,7,,8]),r=r||a.gameUiLayer,n=void 0,e.uiCache[t.url]&&!cc.isValid(e.uiCache[t.url].node)&&delete e.uiCache[t.url],e.uiCache[t.url]?[3,5]:(this.uiIsLoading[t.url]=!0,[4,this.loadPrefabAsset(t)]);case 2:return i=o.sent(),(n=INSTANTIATE(i)).__config__=t,t.page&&this.lastPage&&this.hide(this.lastPage),t.page&&(this.lastPage=n),this.uiIsCancelLoad[t.url]&&(this.uiIsLoading[t.url]=!1,delete this.uiIsCancelLoad[t.url]),e.uiCache[t.url]={},e.uiCache[t.url].node=n,e.uiCache[t.url].ease=t.ease,t.modal?[4,this.loadPrefabAsset(this.modalConfig)]:[3,4];case 3:l=o.sent(),u=INSTANTIATE(l),r.addChild(u),b=n,u.on(cc.Node.EventType.TOUCH_START,(function(t){e.hide(b)})),e.uiCache[t.url].modal=u,o.label=4;case 4:for(null==n.parent?r.addChild(n):n.parent!=r&&n.setParent(r),this.uiIsLoading[t.url]=!1,null!=t.ease&&null==(h=n.getComponent(t.ease))&&(h=n.addComponent(t.ease)),p=this.events.create,f=0;f<(null==p?void 0:p.length);f++)p[f].apply(this,[t]);return[3,6];case 5:this.uiIsLoading[t.url]=!1,n=e.uiCache[t.url].node,t.page&&this.lastPage&&n!==this.lastPage&&this.hide(this.lastPage),t.page&&(this.lastPage=n),t.modal&&((d=e.uiCache[t.url].modal).parent||r.addChild(d)),n.active=!0,o.label=6;case 6:if(n.parent!=r&&n.setParent(r),e.uiCache[t.url].parent=r,n.setSiblingIndex(r.childrenCount),null!=t.ease&&(null==(h=n.getComponent(t.ease))||h.show()),m=this.resolves[t.url]){for(_=0;_<m.length;_++)(0,m[_])(n);m.length=0}for(v=this.events.open,y=0;y<(null==v?void 0:v.length);y++)v[y].apply(this,[t]);return[3,8];case 7:return g=o.sent(),console.error("[UI] 显示UI失败: ".concat(t.name," (").concat(t.url,")"),g),this.uiIsLoading[t.url]=!1,delete this.uiIsCancelLoad[t.url],(m=this.resolves[t.url])&&(m.length=0),c(g),[3,8];case 8:return[2]}var b}))}))}))},e.addUICache=function(t,r,n){try{var o=INSTANTIATE(r);e.uiCache[t.url]||(e.uiCache[t.url]={}),e.uiCache[t.url].node=o,n.addChild(o),e.uiCache[t.url].parent=n,o.setSiblingIndex(o.parent.childrenCount),o.active=!1}catch(e){console.error("[UI] addUICache 失败:",e)}},e.getComponent=function(e,t){return n(this,void 0,void 0,(function(){var t,r,n;return o(this,(function(o){return e?((t=this.uiCache[e.url])&&(r=t.node),e.comp?r?(n=r.getComponent(e.comp),this.uiCache[e.url].comp=n,[2,n]):[2,void 0]:(console.error("必须为配置".concat(e,"配置组件引用！")),[2,void 0])):(console.error("获取组件时传入的config不能为空！"),[2,void 0])}))}))},e.hide=function(e){var t;(null==(t=e instanceof cc.Component?e.node.__config__:e.__config__)?void 0:t.url)&&this.hideUI(t)},e.hideUI=function(t){var r=this,n=e.uiCache[t.url];if(n){if(null!=t.ease){var o=n.node.getComponent(t.ease);null!=o?(o.onCloseEaseComplete=function(){n.node.active=!1,r.dispatchClose(t)},o.hide()):(n.node.active=!1,this.dispatchClose(t))}else n.node.active=!1,this.dispatchClose(t);t.modal&&n.modal.setParent(null)}},e.dispatchClose=function(e){for(var t=this.events.close,r=0;r<(null==t?void 0:t.length);r++)t[r].apply(this,[e])},e.hideLayer=function(e){e.children.forEach((function(e){e.active=!1}))},e.uiCache={},e.uiIsLoading={},e.uiIsCancelLoad={},e.events={},e.resolves={},e.modalConfig={name:"Modal",url:"prefabs/modal/Modal",ease:null,modal:!0},e}();t.UI=s},7730:function(e,t){var r=this&&this.__awaiter||function(e,t,r,n){return new(r||(r=Promise))((function(o,a){function i(e){try{c(n.next(e))}catch(e){a(e)}}function s(e){try{c(n.throw(e))}catch(e){a(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(i,s)}c((n=n.apply(e,t||[])).next())}))},n=this&&this.__generator||function(e,t){var r,n,o,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},i=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return i.next=s(0),i.throw=s(1),i.return=s(2),"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(c){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,c])}}},o=this&&this.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,a=r.call(e),i=[];try{for(;(void 0===t||t-- >0)&&!(n=a.next()).done;)i.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=a.return)&&r.call(a)}finally{if(o)throw o.error}}return i},a=this&&this.__spreadArray||function(e,t,r){if(r||2===arguments.length)for(var n,o=0,a=t.length;o<a;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.sequence=function(e,t){var i=[],s=0,c=e.length;return Promise.resolve(null).then((function l(u){return function(){return r(this,void 0,void 0,(function(){return n(this,(function(e){switch(e.label){case 0:return t?[4,t()]:[3,2];case 1:return[2,e.sent()];case 2:return[2,!1]}}))}))}().then((function(t){if(t)return c>=i.length&&i.push.apply(i,a([],o(new Array(c-i.length).fill(void 0)),!1)),Promise.resolve(i);null!=u&&i.push(u);var r=s<c?e[s++]():null;return r?r.then(l).catch((function(e){return t||i.push.apply(i,a([],o(new Array(c-i.length).fill(void 0)),!1)),Promise.resolve(i)})):Promise.resolve(i)}))}))}},8107:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DotUploadType=void 0,t.createDotData=function(e,t,r,s,c){var l={time:Date.now()},u=a.storage.getItem("gameWayNum"),p=a.storage.getItem("nowWayArr",[]);(null==p?void 0:p.length)>0&&(l.ABStatus=p);var f=+u;isNaN(f)||(l.active_waynum={pici:Math.floor(f/100).toString(),1:f}),r||(r={}),r&&(l=Object.assign(r,l)),e===n.SHUSHU&&(l.name=t),s?function(e,t){return new Promise((function(r,n){for(var a=0;a<t.length;a++){var s=t[a],c=s.class,l=(0,o.getOrCreateTraitInstance)(c);if(l){var u=i.traitConfigInfo.traitsClassNameMap[c],p=null==u?void 0:u.param;p&&(l._id=u.id,l._props=p,l._active=!0,l.onEnable()),l.active&&s.assign&&(e=Object.assign(e,s.assign(l,e)))}}r(!0)}))}(l,s).then((function(e){c&&c(l)})):c&&c(l)};var n,o=r(1644),a=r(1771),i=r(5651);!function(e){e[e.SHUSHU=0]="SHUSHU",e[e.SHUCANG=1]="SHUCANG"}(n||(t.DotUploadType=n={}))},8128:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Proxy=void 0;var n=r(9909),o=r(6435),a=r(2515),i=function(){function e(e){this._eventsConstructorMaps=new Map,this._module=e}return Object.defineProperty(e.prototype,"moduleType",{get:function(){return this._moduleType},enumerable:!1,configurable:!0}),e.prototype.onInit=function(){},e.prototype.registerEvents=function(){return null},e.prototype.getClass=function(){return this.constructor},e.prototype.getModule=function(){return this._module},e.prototype.receivedEvents=function(e){},e.prototype.startEvents=function(){var e=this.registerEvents();if(null!==e)for(var t=0,r=e.length;t<r;t++){var a=e[t];if(!this._eventsConstructorMaps.has(a)){this._eventsConstructorMaps.set(a,!0);var i=new o.EventVo;i.proxy=this,i.eventClass=a,n.EventManager.registerEvent(i)}}this.onInit()},e.prototype.dispatchModuleEvent=function(e){this.moduleType!==a.ModuleType.Common&&this.moduleType!==a.ModuleManager.moduleType||n.EventManager.dispatchModuleEvent(e)},e.prototype.dispatchModuleEventAsync=function(e){if(this.moduleType===a.ModuleType.Common||this.moduleType===a.ModuleManager.moduleType)return n.EventManager.dispatchModuleEventAsync(e)},e}();t.Proxy=i},8331:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.interval=function(e,t,r){for(var n=[],o=3;o<arguments.length;o++)n[o-3]=arguments[o];var a=setInterval((function(){e&&cc.isValid(e,!0)?null==t||t.apply(this,[n]):clearInterval(a)}),r,n);return a}},8416:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.gameStateManager=void 0;var n=r(9909),o=r(2515),a=r(3703),i=r(339),s=r(3683),c=r(1593),l=r(9291),u=r(7585),p=function(){function e(){this._resettableObjects=new Map,this._initialized=!1}return Object.defineProperty(e,"instance",{get:function(){return e._instance||(e._instance=new e),e._instance},enumerable:!1,configurable:!0}),e.prototype.registerResettable=function(e,t){this._resettableObjects.set(e,t),CC_DEBUG&&console.log("[GameStateManager] 注册可重置对象: ".concat(e))},e.prototype.unregisterResettable=function(e){this._resettableObjects.delete(e),CC_DEBUG&&console.log("[GameStateManager] 注销可重置对象: ".concat(e))},e.prototype.onGameExit=function(){CC_DEBUG&&console.log("[GameStateManager] 开始游戏退出清理..."),this.clearUICache(),this.clearLayerManager(),this.resetAllObjects(),this.clearModuleSystem(),this.clearResourceLoader(),this.clearCacheRender(),this.clearCacheComponents(),this.clearPrefabCache(),this.clearEventManager(),CC_DEBUG&&console.log("[GameStateManager] 游戏退出清理完成")},e.prototype.onGameEnter=function(){CC_DEBUG&&console.log("[GameStateManager] 开始游戏重新进入初始化..."),this.reinitializeAllObjects(),this._initialized=!0,CC_DEBUG&&console.log("[GameStateManager] 游戏重新进入初始化完成")},e.prototype.clearUICache=function(){try{u.UI.clearAllCache(),CC_DEBUG&&console.log("[GameStateManager] UI缓存已清理")}catch(e){console.error("[GameStateManager] 清理UI缓存时发生错误:",e)}},e.prototype.clearLayerManager=function(){try{c.layerManager.clear(),(0,c.resetGameUiLayer)(),CC_DEBUG&&console.log("[GameStateManager] 层级管理器已清理")}catch(e){console.error("[GameStateManager] 清理层级管理器时发生错误:",e)}},e.prototype.resetAllObjects=function(){this._resettableObjects.forEach((function(e,t){try{e.reset(),CC_DEBUG&&console.log("[GameStateManager] 已重置对象: ".concat(t))}catch(e){console.error("[GameStateManager] 重置对象 ".concat(t," 时发生错误:"),e)}}))},e.prototype.reinitializeAllObjects=function(){this._resettableObjects.forEach((function(e,t){try{e.reinitialize&&(e.reinitialize(),CC_DEBUG&&console.log("[GameStateManager] 已重新初始化对象: ".concat(t)))}catch(e){console.error("[GameStateManager] 重新初始化对象 ".concat(t," 时发生错误:"),e)}}))},e.prototype.clearTraitSystem=function(){try{var e=window.__traitMaps__;e&&e.clear&&e.clear();var t=window.__shareObjects__;t&&t.clear&&t.clear(),CC_DEBUG&&console.log("[GameStateManager] 特性系统已清理")}catch(e){console.error("[GameStateManager] 清理特性系统时发生错误:",e)}},e.prototype.clearModuleSystem=function(){try{o.ModuleManager&&"function"==typeof o.ModuleManager.reset&&o.ModuleManager.reset(),CC_DEBUG&&console.log("[GameStateManager] 模块系统已清理")}catch(e){console.error("[GameStateManager] 清理模块系统时发生错误:",e)}},e.prototype.clearResourceLoader=function(){try{l.ResLoader&&"function"==typeof l.ResLoader.reset&&l.ResLoader.reset(),CC_DEBUG&&console.log("[GameStateManager] 资源加载器已清理")}catch(e){console.error("[GameStateManager] 清理资源加载器时发生错误:",e)}},e.prototype.clearCacheRender=function(){try{i.cacheRender&&"function"==typeof i.cacheRender.reset&&i.cacheRender.reset(),CC_DEBUG&&console.log("[GameStateManager] 缓存渲染器已清理")}catch(e){console.error("[GameStateManager] 清理缓存渲染器时发生错误:",e)}},e.prototype.clearCacheComponents=function(){try{s.cacheComponents&&"function"==typeof s.cacheComponents.reset&&s.cacheComponents.reset(),CC_DEBUG&&console.log("[GameStateManager] 组件缓存系统已清理")}catch(e){console.error("[GameStateManager] 清理组件缓存系统时发生错误:",e)}},e.prototype.clearPrefabCache=function(){a.prefabInfo.clearAllCache()},e.prototype.clearEventManager=function(){try{n.EventManager&&"function"==typeof n.EventManager.reset&&n.EventManager.reset()}catch(e){console.error("[ModuleManager] 重置事件管理器时发生错误:",e)}CC_DEBUG&&console.log("[ModuleManager] 模块管理器已重置")},Object.defineProperty(e.prototype,"initialized",{get:function(){return this._initialized},enumerable:!1,configurable:!0}),e.prototype.forceReset=function(){this._initialized=!1,this.onGameExit()},e}();t.gameStateManager=p.instance,CC_DEBUG&&(window.gameStateManager=t.gameStateManager)},8825:(e,t)=>{var r,n;Object.defineProperty(t,"__esModule",{value:!0}),t.http=t.CrytoType=t.HttpType=void 0,function(e){e.GET="GET",e.POST="POST"}(r||(t.HttpType=r={})),function(e){e.Url="Url"}(n||(t.CrytoType=n={}));var o=function(){function e(){this.callbackId=-1,this.callbacks={}}return e.prototype.requestAsync=function(e,t,r){var n=this;return new Promise((function(o,a){n.request(e,t,(function(e){o(e)}),(function(e){a(e)}),r)}))},e.prototype.request=function(e,t,r,n,o){var a="GET";(null==o?void 0:o.type)&&(a=o.type);var i=cc.loader.getXMLHttpRequest(),s=JSON.stringify(t);i.open(a,e,!0);var c="application/json";(null==o?void 0:o.contentType)&&(c=o.contentType),i.setRequestHeader("Content-Type",c),i.onreadystatechange=function(){if(4===i.readyState){var e=i.responseText;i.status>=200&&i.status<300?(o.crypto&&(e=o.crypto.decrypt(e)),r(JSON.parse(e))):n(e)}},o.crypto&&(s="params="+o.crypto.encrypt(s)),i.send(s)},e}();t.http=new o},9075:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Limiter=void 0;var n=r(9915),o=function(){function e(e){this._size=0,this._isDisposed=!1,this.maxDegreeOfParalellism=e,this.outstandingPromises=[],this.runningPromises=0,this._onDrained=new n.Emitter}return Object.defineProperty(e.prototype,"onDrained",{get:function(){return this._onDrained.event},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"size",{get:function(){return this._size},enumerable:!1,configurable:!0}),e.prototype.queue=function(e){var t=this;if(this._isDisposed)throw new Error("Object has been disposed");return this._size++,new Promise((function(r,n){t.outstandingPromises.push({factory:e,c:r,e:n}),t.consume()}))},e.prototype.consume=function(){for(var e=this;this.outstandingPromises.length&&this.runningPromises<this.maxDegreeOfParalellism;){var t=this.outstandingPromises.shift();this.runningPromises++;var r=t.factory();r.then(t.c,t.e),r.then((function(){return e.consumed()}),(function(){return e.consumed()}))}},e.prototype.consumed=function(){this._isDisposed||(this.runningPromises--,0==--this._size&&this._onDrained.fire(),this.outstandingPromises.length>0&&this.consume())},e.prototype.clear=function(){if(this._isDisposed)throw new Error("Object has been disposed");this.outstandingPromises.length=0,this._size=this.runningPromises},e.prototype.dispose=function(){this._isDisposed=!0,this.outstandingPromises.length=0,this._size=0,this._onDrained.dispose()},e}();t.Limiter=o},9291:(e,t)=>{var r;Object.defineProperty(t,"__esModule",{value:!0}),t.ResLoader=t.BundleName=void 0,function(e){e.class="class",e.chapter="chapter",e.mainTraits="mainTraits",e.tool="tool"}(r||(t.BundleName=r={}));var n=function(){function e(){}return e.parseBundleConfig=function(e){return"string"==typeof e?{bundleName:e}:"object"==typeof e&&null!==e&&"bundleName"in e?{bundleName:e.bundleName,version:e.version}:{bundleName:String(e)}},e.generateCacheKey=function(e,t){var r=this.parseBundleConfig(e),n=r.bundleName,o=r.version,a=t?"".concat(n,"_").concat(t):n;return o?"".concat(a,"_").concat(o):a},e.addEventListener=function(e,t){this.events[e]||(this.events[e]=[]),this.events[e].push(t)},e.clearEventListeners=function(){for(var e in this.events)this.events[e]=[];this.events={},CC_DEBUG&&console.log("[ResLoader] 所有事件监听器已清理")},e.clearResourceCache=function(){for(var e in this.cacheResources){var t=this.cacheResources[e];t.onCompletes&&(t.onCompletes.length=0),delete t.asset}this.cacheResources={},CC_DEBUG&&console.log("[ResLoader] 资源缓存已清理")},e.reset=function(){this.clearResourceCache(),this.clearEventListeners(),CC_DEBUG&&console.log("[ResLoader] ResLoader 已完全重置")},e.isRemote=function(e){return/^(https?:\/\/)/.test(e)},e.getAsset=function(e){var t;return null===(t=this.cacheResources[e])||void 0===t?void 0:t.asset},e.load=function(e,t,r){if(this.cacheResources[e])this.cacheResources[e].asset?this.cacheResources[e].asset.isValid?r&&r.apply(this,[null,this.cacheResources[e].asset]):(delete this.cacheResources[e],this.load(e,t,r)):r&&this.cacheResources[e].onCompletes.push(r);else{this.cacheResources[e]={onCompletes:r?[r]:[]};var n=this,o=function(t,r){t&&cc.error("资源加载发生错误：",t),n.cacheResources[e].asset=r;for(var o=n.cacheResources[e].onCompletes,a=0;a<(null==o?void 0:o.length);a++)o[a].apply(this,[t,r]);n.cacheResources[e].onCompletes.length=0;for(var i=n.events.load,s=0;s<(null==i?void 0:i.length);s++)i[s].apply(n,["resources",e])};this.isRemote(e)?cc.assetManager.loadRemote(e,o):cc.resources.load(e,t,o)}},e.asyncLoad=function(e,t){var r=this;return new Promise((function(n,o){r.load(e,t,(function(e,t){e?o(e):n(t)}))}))},e.loadByBundle=function(e,t,r,n){var o=this,a=this.parseBundleConfig(e),i=a.bundleName;a.version,this.loadBundle(e,(function(a,s){if(a)return console.error("bundle:".concat(i," 资源加载错误："),a),void(n&&n.apply(o,[a,null]));var c=o.generateCacheKey(e,t);o.cacheResources[c]?o.cacheResources[c].asset?o.cacheResources[c].asset.isValid?n&&n.apply(o,[null,o.cacheResources[c].asset]):(delete o.cacheResources[c],o.loadByBundle(e,t,r,n)):n&&o.cacheResources[c].onCompletes.push(n):(o.cacheResources[c]={onCompletes:n?[n]:[]},s.load(t,r,(function(e,r){if(o.cacheResources[c]){o.cacheResources[c].asset=r;var n=o.cacheResources[c].onCompletes;o.cacheResources[c].onCompletes=[];for(var a=0;a<(null==n?void 0:n.length);a++)n[a].apply(o,[e,r]);for(var s=o.events.load,l=0;l<(null==s?void 0:s.length);l++)s[l].apply(o,[i,t])}})))}))},e.asyncLoadByBundle=function(e,t,r){var n=this;return new Promise((function(o,a){n.loadByBundle(e,t,r,(function(e,t){e?a(e):o(t)}))}))},e.loadBundle=function(e,t){var r=this,n=this.parseBundleConfig(e),o=n.bundleName,a=n.version,i=this.generateCacheKey(e);this.cacheResources[i]?this.cacheResources[i].asset?t&&t.apply(this,[null,this.cacheResources[i].asset]):t&&this.cacheResources[i].onCompletes.push(t):(this.cacheResources[i]={onCompletes:t?[t]:[]},a?cc.assetManager.loadBundle(o,{version:a},(function(e,t){if(r.cacheResources[i]){r.cacheResources[i].asset=t;var n=r.cacheResources[i].onCompletes;r.cacheResources[i].onCompletes=[];for(var o=0;o<(null==n?void 0:n.length);o++)n[o].apply(this,[e,t])}})):cc.assetManager.loadBundle(o,(function(e,t){if(r.cacheResources[i]){r.cacheResources[i].asset=t;var n=r.cacheResources[i].onCompletes;r.cacheResources[i].onCompletes=[];for(var o=0;o<(null==n?void 0:n.length);o++)n[o].apply(this,[e,t])}})))},e.bundlePreloadScene=function(e,t,r){var n=this,o="preloadScene"+t;this.cacheResources[o]?n.cacheResources[o].__complete__?r&&r.apply(this,[null]):r&&this.cacheResources[o].onCompletes.push(r):(this.cacheResources[o]={onCompletes:r?[r]:[]},e.preloadScene(t,(function(e){var t=n.cacheResources[o].onCompletes;n.cacheResources[o].__complete__=!0;for(var r=0;r<(null==t?void 0:t.length);r++)t[r].apply(this,[e]);n.cacheResources[o].onCompletes.length=0,delete n.cacheResources[o].__complete__})))},e.bundlePreload=function(e,t,r,n){var o=this,a="preload"+t;this.cacheResources[a]?o.cacheResources[a].__items__?n&&n.apply(this,[null,o.cacheResources[a].__items__]):n&&this.cacheResources[a].onCompletes.push(n):(this.cacheResources[a]={onCompletes:n?[n]:[]},e.preload(t,r,(function(e,t){var r=o.cacheResources[a].onCompletes;o.cacheResources[a].__items__=t;for(var n=0;n<(null==r?void 0:r.length);n++)r[n].apply(this,[e,t]);o.cacheResources[a].onCompletes.length=0})))},e.renderSprite=function(e,t){this.load(t,cc.SpriteFrame,(function(t,r){t||(e.spriteFrame=r)}))},e.renderSpriteByBundle=function(e,t,r){this.loadByBundle(r,t,cc.SpriteFrame,(function(t,r){t||(e.spriteFrame=r)}))},e.renderDragonbones=function(e,t,r,n,o,a){var i=this;void 0===a&&(a=1);var s=0,c=function(){2===s&&(e.addEventListener(dragonBones.EventObject.COMPLETE,(function(r){var n;CC_DEBUG&&console.log("renderDragonbones 动画播放结束,url",t),(null===(n=e.node)||void 0===n?void 0:n.parent)&&e.node.parent.removeChild(e.node)}),i),e.armatureName=n,e.playAnimation(o,a))};this.load(t,dragonBones.DragonBonesAsset,(function(t,r){t||(e.dragonAsset=r,s++,c())})),this.load(r,dragonBones.DragonBonesAtlasAsset,(function(t,r){t||(e.dragonAtlasAsset=r,s++,c())}))},e.renderDragonbonesByBundle=function(e,t,r,n,o,a,i,s){var c=this;void 0===i&&(i=1),void 0===s&&(s=!0),this.loadBundle(e,(function(e,l){if(!e){var u=0,p=function(){2===u&&(t.addEventListener(dragonBones.EventObject.COMPLETE,(function(e){var n,o;s&&(null===(n=t.node)||void 0===n?void 0:n.parent)&&(CC_DEBUG&&console.log("renderDragonbonesByBundle 动画播放结束,url",r),null===(o=t.node)||void 0===o||o.parent.removeChild(t.node))}),c),t.armatureName=o,t.playAnimation(a,i))};l.load(r,dragonBones.DragonBonesAsset,(function(e,r){e||(t.dragonAsset=r,u++,p())})),l.load(n,dragonBones.DragonBonesAtlasAsset,(function(e,r){e||(t.dragonAtlasAsset=r,u++,p())}))}}))},e.events={},e.cacheResources={},e}();t.ResLoader=n},9650:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Module=void 0;var r=function(){function e(){this._registerProxys=[],this._proxyClassMap=new Map}return Object.defineProperty(e.prototype,"proxyClassMap",{get:function(){return this._proxyClassMap},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"moduleType",{get:function(){return this._moduleType},enumerable:!1,configurable:!0}),e.prototype.registerProxys=function(){return[]},e.prototype.getProxy=function(e){return this._proxyClassMap.get(e)},e.prototype.startProxy=function(e){this._registerProxys=this.registerProxys();for(var t=0,r=this._registerProxys.length;t<r;t++){var n=this._registerProxys[t];if(!this._proxyClassMap.has(n)){var o=new n(this);o._moduleType=e,this._proxyClassMap.set(n,o),o.startEvents()}}},e}();t.Module=r},9675:function(e,t,r){var n=this&&this.__awaiter||function(e,t,r,n){return new(r||(r=Promise))((function(o,a){function i(e){try{c(n.next(e))}catch(e){a(e)}}function s(e){try{c(n.throw(e))}catch(e){a(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(i,s)}c((n=n.apply(e,t||[])).next())}))},o=this&&this.__generator||function(e,t){var r,n,o,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},i=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return i.next=s(0),i.throw=s(1),i.return=s(2),"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(c){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,c])}}},a=this&&this.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,a=r.call(e),i=[];try{for(;(void 0===t||t-- >0)&&!(n=a.next()).done;)i.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=a.return)&&r.call(a)}finally{if(o)throw o.error}}return i},i=this&&this.__spreadArray||function(e,t,r){if(r||2===arguments.length)for(var n,o=0,a=t.length;o<a;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.hotUpdate=t.LOCAL_GAME_VERSION_KEY=t.HotUpdateEventCodeEnum=void 0;var s,c=r(1007);!function(e){e[e.ERROR_NO_LOCAL_MANIFEST=0]="ERROR_NO_LOCAL_MANIFEST",e[e.ERROR_DOWNLOAD_MANIFEST=1]="ERROR_DOWNLOAD_MANIFEST",e[e.ERROR_PARSE_MANIFEST=2]="ERROR_PARSE_MANIFEST",e[e.NEW_VERSION_FOUND=3]="NEW_VERSION_FOUND",e[e.ALREADY_UP_TO_DATE=4]="ALREADY_UP_TO_DATE",e[e.UPDATE_PROGRESSION=5]="UPDATE_PROGRESSION",e[e.ASSET_UPDATED=6]="ASSET_UPDATED",e[e.ERROR_UPDATING=7]="ERROR_UPDATING",e[e.UPDATE_FINISHED=8]="UPDATE_FINISHED",e[e.UPDATE_FAILED=9]="UPDATE_FAILED",e[e.ERROR_DECOMPRESS=10]="ERROR_DECOMPRESS",e[e.STOP_WHENUPDATE=11]="STOP_WHENUPDATE",e[e.START=12]="START",e[e.FAIL=13]="FAIL",e[e.VERSION_COMPARE=14]="VERSION_COMPARE",e[e.REQUEST_REMOTE_CONFIG_TIMEOUT=15]="REQUEST_REMOTE_CONFIG_TIMEOUT",e[e.REMOTE_CONFIG_DATA_ERROR=16]="REMOTE_CONFIG_DATA_ERROR"}(s||(t.HotUpdateEventCodeEnum=s={})),t.LOCAL_GAME_VERSION_KEY="localGameVersion";var l=function(){function e(){this.currentFileNum=0,this.currentFileTotal=0,this.projectManifestStr=null,this.updating=!1,this.failCount=0,this.am=null,this.firstVersionCompare=!1,this._storagePath="",this._callbacks=[]}return Object.defineProperty(e.prototype,"storagePath",{get:function(){return this._storagePath},enumerable:!1,configurable:!0}),e.prototype._emit=function(e,t){this._callbacks.forEach((function(r){return r(e,t)}))},e.prototype.onHotUpdateState=function(e){"function"==typeof e&&this._callbacks.push(e)},e.prototype.versionCompareHandle=function(e,t){CC_DEBUG&&console.log(" version A is "+e+", version B is "+t);for(var r=e.split("."),n=t.split("."),o=0;o<r.length;++o){var a=parseInt(r[o]),i=parseInt(n[o]||"0");if(a!==i)return i-a>0&&!this.firstVersionCompare&&(this.firstVersionCompare=!0,this._emit(s[s.VERSION_COMPARE],{eventCode:s.VERSION_COMPARE,msg:"版本比对成功"})),a-i}return n.length>r.length?-1:0},e.prototype.checkRemoteFileExists=function(e){var t=this;return this.log("检查热更地址：",e),new Promise((function(r){window.axios({method:"HEAD",url:e,timeout:1e4}).then((function(e){e?r(e.status>=200&&e.status<300):(t.log("检查远程文件 response 为空！"),r(!1))})).catch((function(e){t.log("检查远程文件异常",e),r(!1)}))}))},e.prototype.updateRemoteUrl=function(e,t){return n(this,void 0,void 0,(function(){var r,n,a,i,s,l,u,p;return o(this,(function(o){switch(o.label){case 0:if(r=t.version,n=t.zip,a=n,!n)return[3,4];i=c.envInfo.hotServerUrl+"/zips/".concat(e.version,"_").concat(r,"/project.manifest"),o.label=1;case 1:return o.trys.push([1,3,,4]),[4,this.checkRemoteFileExists(i)];case 2:return o.sent()?this.log("远程zip文件存在，使用zip模式: ".concat(i)):(a=!1,this.log("远程zip文件不存在，回退到非zip模式: ".concat(i))),[3,4];case 3:return s=o.sent(),this.log("checkRemoteFileExists 发生异常: ".concat(s),null==s?void 0:s.stack),[3,4];case 4:return l=c.envInfo.hotServerUrl+"/"+(a?"zips/".concat(e.version,"_").concat(r):r),u=l+(a?"/project.manifest":"/src/project.manifest"),p=l+(a?"/version.manifest":"/src/version.manifest"),e.url1=l,e.url2=u,e.url3=p,[2,e]}}))}))},e.prototype.getStorageManifest=function(){var e=this._storagePath+"/project.manifest";if(CC_DEBUG&&this.log("当前缓存 project.manifest的url: ".concat(e)),!jsb.fileUtils.isFileExist(e))return CC_DEBUG&&this.log("project.manifest 文件不存在: ".concat(e)),null;var t=jsb.fileUtils.getStringFromFile(e);return t?(CC_DEBUG&&this.log("project.manifest 数据: ".concat(t)),JSON.parse(t)):(this.log("project.manifest 文件读取失败: ".concat(e)),null)},e.prototype.updateStorageRemoteUrl=function(e){return n(this,void 0,void 0,(function(){var t,r,n,a;return o(this,(function(o){switch(o.label){case 0:if(t=this._storagePath+"/project.manifest",!(r=this.getStorageManifest()))return[2];o.label=1;case 1:return o.trys.push([1,3,,4]),[4,this.updateRemoteUrl(r,e)];case 2:return o.sent(),[3,4];case 3:return n=o.sent(),this.log("updateRemoteUrl 发生异常: ".concat(n),null==n?void 0:n.stack),[3,4];case 4:CC_DEBUG&&this.log("同步远程地址后 project.manifest 数据: ".concat(JSON.stringify(r)));try{jsb.fileUtils.writeStringToFile(JSON.stringify(r),t)}catch(e){this.log("写入失败: 地址 ".concat(t,"   缓存数据： ").concat(JSON.stringify(r)))}return CC_DEBUG&&(a=jsb.fileUtils.getStringFromFile(t),this.log("最后得到的缓存目录下的project数据: ".concat(a))),[2]}}))}))},e.prototype.setStorage=function(e,t){"object"==typeof t&&(t=JSON.stringify(t)),cc.sys.localStorage.setItem(e,t)},e.prototype.getStorage=function(e,t){void 0===t&&(t=null);var r=cc.sys.localStorage.getItem(e);if(!r)return t;try{return JSON.parse(r)}catch(e){return r}},e.prototype.startUpdate=function(e,r){return n(this,void 0,void 0,(function(){var n,a,i,l,u,p,f,d,h,_,v;return o(this,(function(o){switch(o.label){case 0:return n=r.version,cc.sys.isNative?(this._emit(s[s.START],{eventCode:s.START,msg:"开始热更"}),this._storagePath=(jsb.fileUtils?jsb.fileUtils.getWritablePath():"/")+"blockBlastHotUpdate",CC_DEBUG&&(this.log("缓存地址:"+this._storagePath),this.log("远程 version:",n),this.log("远程服务器地址：",c.envInfo.hotServerUrl),this.log("修改前 project 数据 ".concat(JSON.stringify(e)))),a=e.version,(i=this.getStorageManifest())?((l=this.versionCompareHandle(e.version,i.version))<0&&(a=i.version,this.log("使用缓存版本：".concat(i.version))),this.log("比较版本： compareNumber： ".concat(l,", 本地最新版本：").concat(a,"，app版本：").concat(e.version,", 缓存版本：").concat(i.version))):this.log("使用app版本：".concat(e.version)),a===n?(this.log("版本相同，旧版本：".concat(a,", 新版本：").concat(n,"，跳过热更逻辑执行！")),this._emit(s[s.FAIL],{eventCode:s.FAIL,msg:"版本相同，不需要热更"}),[2]):[4,this.updateRemoteUrl(e,r)]):(CC_DEBUG&&this.log("startUpdate 不是原生环境: ".concat(cc.sys.isNative)),this._emit(s[s.FAIL],{eventCode:s.FAIL,msg:"不是原生环境"}),[2]);case 1:return o.sent(),this.projectManifestStr=JSON.stringify(e),CC_DEBUG&&this.log("修改后 project 数据:",this.projectManifestStr),this.am=new jsb.AssetsManager("",this._storagePath,this.versionCompareHandle.bind(this)),this.am.setVerifyCallback((function(e,t){return t.compressed,t.md5,t.path,t.size,!0})),CC_DEBUG&&this.log("----project数据、远程version数据、本地缓存目录已准备完成----"),this.updating?(CC_DEBUG&&this.log("正在检查或更新。。。"),[2]):(this.updating=!0,CC_DEBUG&&this.log("----开始检查是否存在新的版本号----"),[4,this.updateStorageRemoteUrl(r)]);case 2:return o.sent(),this.localManifest=null,this.am.getState()===jsb.AssetsManager.State.UNINITED&&(CC_DEBUG&&this.log("checkUpdate 本地清单尚未初始化，开始初始化"),u=new jsb.Manifest(this.projectManifestStr,this._storagePath),this.am.loadLocalManifest(u,this._storagePath),this.localManifest=this.am.getLocalManifest(),this.setStorage(t.LOCAL_GAME_VERSION_KEY,this.localManifest.getVersion())),CC_DEBUG&&(p=this.localManifest,f=p.getManifestRoot(),d=p.getVersion(),h=p.getPackageUrl(),_=p.getManifestFileUrl(),v=p.getVersionFileUrl(),this.log("checkUpdate ---检查本地manifest解析内容---"),this.log("checkUpdate manifest根目录:",f),this.log("checkUpdate 本地版本号:",d),this.log("checkUpdate 远程资源根Url:",h),this.log("checkUpdate 远程资源manifestUrl:",_),this.log("checkUpdate 远程资源versionUrl:",v)),this.localManifest&&this.localManifest.isLoaded()?(CC_DEBUG&&this.log("checkUpdate 本地清单已经初始化成功"),this.am.setEventCallback(this.versionCheck.bind(this)),this.am.checkUpdate(),[2]):(CC_DEBUG&&this.log("checkUpdate 本地清单加载失败..."),this._emit(s[s.FAIL],{eventCode:s.FAIL,msg:"本地清单加载失败..."}),[2])}}))}))},e.prototype.versionCheck=function(e){var t=this,r=e.getEventCode(),n=s[r],o=!1,a="";switch(CC_DEBUG&&this.log("checkCb 检查热更回调事件名: ".concat(r," ").concat(n)),r){case jsb.EventAssetsManager.ERROR_NO_LOCAL_MANIFEST:o=!0,a="checkCb 未找到本地清单文件，跳过热更新。";break;case jsb.EventAssetsManager.ERROR_DOWNLOAD_MANIFEST:case jsb.EventAssetsManager.ERROR_PARSE_MANIFEST:o=!0,a="checkCb 下载清单文件失败，跳过热更新。";break;case jsb.EventAssetsManager.ALREADY_UP_TO_DATE:o=!0,a="checkCb 已更新到最新的远程版本";break;case jsb.EventAssetsManager.NEW_VERSION_FOUND:CC_DEBUG&&this.log("checkCb 发现新版本, 需要下载字节大小 ".concat(this.am.getTotalBytes(),", ").concat(r," ").concat(n)),this._emit(s[s.NEW_VERSION_FOUND],{eventCode:s.NEW_VERSION_FOUND,msg:"发现新版本, 需要下载字节大小(' + ".concat(this.am.getTotalBytes()," + ')"),jsbEvent:e}),setTimeout((function(){t.hotUpdate()}),100);break;default:return}o&&this._emit(s[s.FAIL],{eventCode:s.FAIL,msg:"".concat(a,": ").concat(r," ").concat(n," ").concat(e.getMessage()),jsbEvent:e}),this.am.setEventCallback(null)},e.prototype.hotUpdate=function(){if(CC_DEBUG&&this.log("hotUpdate 检测到新版本 开始执行热更,下载assets资源"),!this.localManifest||!this.localManifest.isLoaded())return CC_DEBUG&&this.log("updateCb 本地清单加载失败..."),void this._emit(s[s.FAIL],{eventCode:s.FAIL,msg:"本地清单加载失败...",jsbEvent:null});CC_DEBUG&&this.log("hotUpdate 本地清单初始化成功 检测到新版本 开始执行热更，"),this.am.setEventCallback(this.updateCb.bind(this)),this.am.update(),this.currentFileNum=0,this.currentFileTotal=0,this.failCount=0},e.prototype.updateCb=function(e){var t=!1,r=!1,n="",o=e.getEventCode(),a=s[o];switch(CC_DEBUG&&console.log("updateCb 热更回调事件名: ".concat(o," ").concat(a," ")),o){case jsb.EventAssetsManager.UPDATE_FINISHED:t=!0,n="updateCb 更新已完成。".concat(a," ").concat(o," ").concat(e.getMessage());break;case jsb.EventAssetsManager.ERROR_NO_LOCAL_MANIFEST:r=!0,n="updateCb  没找到找到本地清单文件，跳过热更新。".concat(o," ").concat(a);break;case jsb.EventAssetsManager.ASSET_UPDATED:n="updateCb 下载成功文件: ".concat(a," ").concat(o," ").concat(e.getAssetId(),"}");break;case jsb.EventAssetsManager.UPDATE_PROGRESSION:var i=e.getDownloadedFiles(),c=e.getTotalFiles(),l=e.getDownloadedBytes(),u=e.getTotalBytes();this.currentFileNum=e.getDownloadedFiles(),this.currentFileTotal=e.getTotalFiles(),CC_DEBUG&&this.log("updateCb 下载进度 => 已下载文件数：".concat(i,"/").concat(c,"  已下载字节：").concat(l,"/").concat(u,"   ").concat(a,"  ").concat(o,"  }")),this._emit(s[s.UPDATE_PROGRESSION],{eventCode:o,msg:"下载进度（文件数）：".concat(i," / ").concat(c," ").concat(a," ").concat(o),jsbEvent:e});break;case jsb.EventAssetsManager.ERROR_DOWNLOAD_MANIFEST:case jsb.EventAssetsManager.ERROR_PARSE_MANIFEST:r=!0,n="updateCb 下载清单文件失败，跳过热更新。".concat(o," ").concat(a," ").concat(e.getMessage());break;case jsb.EventAssetsManager.ALREADY_UP_TO_DATE:r=!0,n="updateCb 已更新到最新的远程版本。".concat(o," ").concat(a);break;case jsb.EventAssetsManager.UPDATE_FAILED:CC_DEBUG&&console.log("%c[热更新]","color:#fff;background:#6a1b9a;padding:2px 6px;border-radius:4px;","updateCb 更新失败: ".concat(e.getMessage(),", ' 已下载文件数:', ").concat(this.currentFileNum,", ' 需要下载文件数:', ").concat(this.currentFileTotal)),this.failCount++,this.failCount<=5?this.am.downloadFailedAssets():(r=!0,n="updateCb 失败次数过多，放弃重新下载失败文件 ".concat(o," ").concat(a," ").concat(e.getMessage()));break;case jsb.EventAssetsManager.ERROR_UPDATING:try{(e.getMessage()||"").includes("(416)")&&jsb.fileUtils.removeFile("".concat(this._storagePath,"_temp/").concat(e.getAssetId(),".tmp"))}catch(e){console.error("%c[热更新]","color:#fff;background:#6a1b9a;padding:2px 6px;border-radius:4px;","删除临时文件发生错误: ".concat(o," ").concat(a," ").concat(e," "))}this.log("updateCb 资源更新错误: ".concat(e.getAssetId()," ").concat(e.getMessage()));break;case jsb.EventAssetsManager.ERROR_DECOMPRESS:CC_DEBUG&&console.log("updateCb 解压缩失败: ".concat(o," ").concat(a," ").concat(e.getMessage()));break;case jsb.EventAssetsManager.STOP_WHENUPDATE:r=!0,n="updateCb STOP_WHENUPDATE: ".concat(o," ").concat(a," ").concat(e.getMessage())}if(CC_DEBUG&&this.log("%c[热更新]","color:#fff;background:#6a1b9a;padding:2px 6px;border-radius:4px;",n),r)return this.am.setEventCallback(null),this._emit(s[s.FAIL],{eventCode:s.FAIL,msg:"".concat(n," ").concat(e.getMessage()),jsbEvent:e}),this.updating=!1,void this.am.getLocalManifest().getVersion();if(t){this.am.setEventCallback(null);var p=this.am.getLocalManifest().getSearchPaths();cc.sys.localStorage.setItem("blockBlastHotUpdateData",JSON.stringify(p)),CC_DEBUG&&(this.log("updateCb 搜索路径： ".concat(cc.sys.localStorage.getItem("blockBlastHotUpdateData"))),this.log("updateCb 已更新到最新版本，请重启： ".concat(cc.sys.localStorage.getItem("blockBlastHotUpdateData")))),this._emit(s[s.UPDATE_FINISHED],{eventCode:s.UPDATE_FINISHED,msg:"更新成功: ".concat(e.getMessage()),jsbEvent:e}),this.updating=!1}},e.prototype.log=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];CC_DEBUG&&console.log.apply(console,i(["%c[热更新]","color:#fff;background:#6a1b9a;padding:2px 6px;border-radius:4px;"],a(e),!1))},e.prototype.error=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];CC_DEBUG&&console.error.apply(console,i(["%c[热更新]","color:#fff;background:#6a1b9a;padding:2px 6px;border-radius:4px;"],a(e),!1))},e}();t.hotUpdate=new l},9909:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.EventManager=void 0;var n=r(2515),o=function(){function e(){}return Object.defineProperty(e,"events",{get:function(){return this._events},enumerable:!1,configurable:!0}),e.registerEvent=function(e){if(null!==e){var t=e.eventClass;if(t){void 0===this._events.get(t)&&this._events.set(t,[]),e.eventClass;var r=this._events.get(e.eventClass),n=!1;if(r){for(var o=0,a=null==r?void 0:r.length;o<a;o++)if(r[o].proxy===e.proxy){n=!0;break}n||r.push(e)}}}},e.dispatchModuleEvent=function(e){if(e){var t=e.getClass(),r=this._events.get(t);if(r)for(var o=0,a=r.length;o<a;o++){var i=r[o].proxy,s=i.moduleType;s!==n.ModuleType.Common&&s!==n.ModuleManager.moduleType||null==i||i.receivedEvents(e)}var c=this._eventAllCompleted.get(t);if(c)if(n.ModuleManager.moduleType===n.ModuleType.Common)for(var l in c)for(var u=c[l],p=0;p<(null==u?void 0:u.length);p++)(d=u[p])&&d(e);else{u=c[n.ModuleManager.moduleType];for(var f=0;f<(null==u?void 0:u.length);f++){var d;(d=u[f])&&d(e)}}}},e.dispatchModuleEventAsync=function(t){return new Promise((function(r){t._callback=function(){return r()},e.dispatchModuleEvent(t)}))},e.onEventAllCompleted=function(e,t,r){var n=this._eventAllCompleted.get(t);n||(n={},this._eventAllCompleted.set(t,n)),n[e]||(n[e]=[]),n[e].push(r)},e.reset=function(){this._events.clear(),this._eventAllCompleted.clear(),CC_DEBUG&&console.log("[EventManager] 事件管理器已重置")},e._events=new Map,e._eventAllCompleted=new Map,e}();t.EventManager=o},9915:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Emitter=void 0;var r=function(){function e(e){this._options=e}return Object.defineProperty(e.prototype,"event",{get:function(){var e,t=this;return null!==(e=this._event)&&void 0!==e||(this._event=function(e,r){var n,o,a,i,s,c;r&&(e=e.bind(r)),t._callbacks||(null===(o=null===(n=t._options)||void 0===n?void 0:n.onWillAddFirstListener)||void 0===o||o.call(n,t),t._callbacks=[],null===(i=null===(a=t._options)||void 0===a?void 0:a.onDidAddFirstListener)||void 0===i||i.call(a,t)),null===(c=null===(s=t._options)||void 0===s?void 0:s.onDidAddListener)||void 0===c||c.call(s,t),t._callbacks.push({callback:e,thisArgs:r})}),this._event},enumerable:!1,configurable:!0}),e.prototype.fire=function(e){for(var t=this._callbacks,r=0;r<(null==t?void 0:t.length);r++){var n=t[r],o=n.callback,a=n.thisArgs;o&&o.apply(a,[e])}},e.prototype.dispose=function(){var e,t;this._disposed||(this._disposed=!0,this._callbacks=void 0,null===(t=null===(e=this._options)||void 0===e?void 0:e.onDidRemoveLastListener)||void 0===t||t.call(e))},e}();t.Emitter=r}},__webpack_module_cache__={};function __webpack_require__(e){var t=__webpack_module_cache__[e];if(void 0!==t)return t.exports;var r=__webpack_module_cache__[e]={exports:{}};return __webpack_modules__[e].call(r.exports,r,r.exports,__webpack_require__),r.exports}var __webpack_exports__={},falcon,AdapterFringe_1,DragonbonesAnim_1,arrays_1,Barrier_1,DeferredPromise_1,First_1,Limiter_1,Sequence_1,WaitFor_1,AudioInfo_1,CacheRender_1,CacheComponents_1,Component_1,Copy_1,UrlCrypto_1,Date_1,DecoratorAdapter_1,DecoratorDebounce_1,DecoratorMeasure_1,DecoratorMemoize_1,DecoratorScreen_1,DecoratorThrottle_1,DecoratorTrait_1,Dot_1,enum_1,Equal_1,Events_1,HotUpdate_1,Http_1,Interval_1,ResLoader_1,numbers_1,RenderingOptimization_1,ObjectPool_1,Random_1,Storage_1,GameLayer_1,TraitConfigInfo_1,Timeout_1,Timer_1,Trait_1,UI_1,Task_1,NativeBridge_1,Url_1,watch_1,EventManager_1,EventVo_1,Module_1,ModuleEvent_1,ModuleManager_1,Proxy_1,Types_1,PrefabInfo_1,GameStateManager_1;AdapterFringe_1=__webpack_require__(5224),DragonbonesAnim_1=__webpack_require__(590),arrays_1=__webpack_require__(413),Barrier_1=__webpack_require__(6304),DeferredPromise_1=__webpack_require__(4119),First_1=__webpack_require__(5817),Limiter_1=__webpack_require__(9075),Sequence_1=__webpack_require__(7730),WaitFor_1=__webpack_require__(7035),AudioInfo_1=__webpack_require__(1623),CacheRender_1=__webpack_require__(339),CacheComponents_1=__webpack_require__(3683),Component_1=__webpack_require__(6794),Copy_1=__webpack_require__(1195),UrlCrypto_1=__webpack_require__(420),Date_1=__webpack_require__(177),DecoratorAdapter_1=__webpack_require__(5679),DecoratorDebounce_1=__webpack_require__(1781),DecoratorMeasure_1=__webpack_require__(676),DecoratorMemoize_1=__webpack_require__(2520),DecoratorScreen_1=__webpack_require__(2394),DecoratorThrottle_1=__webpack_require__(6078),DecoratorTrait_1=__webpack_require__(1644),Dot_1=__webpack_require__(8107),enum_1=__webpack_require__(4307),Equal_1=__webpack_require__(5539),Events_1=__webpack_require__(9915),HotUpdate_1=__webpack_require__(9675),Http_1=__webpack_require__(8825),Interval_1=__webpack_require__(8331),ResLoader_1=__webpack_require__(9291),numbers_1=__webpack_require__(783),RenderingOptimization_1=__webpack_require__(4768),ObjectPool_1=__webpack_require__(6282),Random_1=__webpack_require__(2411),Storage_1=__webpack_require__(1771),GameLayer_1=__webpack_require__(1593),TraitConfigInfo_1=__webpack_require__(5651),Timeout_1=__webpack_require__(5979),Timer_1=__webpack_require__(5967),Trait_1=__webpack_require__(7123),UI_1=__webpack_require__(7585),Task_1=__webpack_require__(7019),NativeBridge_1=__webpack_require__(3446),Url_1=__webpack_require__(6863),watch_1=__webpack_require__(299),EventManager_1=__webpack_require__(9909),EventVo_1=__webpack_require__(6435),Module_1=__webpack_require__(9650),ModuleEvent_1=__webpack_require__(1296),ModuleManager_1=__webpack_require__(2515),Proxy_1=__webpack_require__(8128),Types_1=__webpack_require__(3447),PrefabInfo_1=__webpack_require__(3703),GameStateManager_1=__webpack_require__(8416),function(e){e.Proxy=Proxy_1.Proxy,e.Module=Module_1.Module,e.ModuleManager=ModuleManager_1.ModuleManager,e.Emitter=Events_1.Emitter,e.EventManager=EventManager_1.EventManager,e.EventVo=EventVo_1.EventVo,e.ModuleEvent=ModuleEvent_1.ModuleEvent,e.adapter={applyAdapterFringe:AdapterFringe_1.applyAdapterFringe},e.dragonbonesAnim=DragonbonesAnim_1.dragonbonesAnim,e.arrays={arraysHaveSameElements:arrays_1.arraysHaveSameElements,shuffleArray:arrays_1.shuffleArray,arraysEqual:arrays_1.arraysEqual,ensureMaxLength:arrays_1.ensureMaxLength},e.async={TimeoutBarrier:Barrier_1.TimeoutBarrier,Barrier:Barrier_1.Barrier,DeferredPromise:DeferredPromise_1.DeferredPromise,firstParallel:First_1.firstParallel,first:First_1.first,Limiter:Limiter_1.Limiter,sequence:Sequence_1.sequence,waitFor:WaitFor_1.waitFor},e.audioInfo=AudioInfo_1.audioInfo,e.cacheRender=CacheRender_1.cacheRender,e.cacheComponents=CacheComponents_1.cacheComponents,e.Component=Component_1.default,e.copy={deepCopy:Copy_1.deepCopy,deepCopyArrayFrom:Copy_1.deepCopyArrayFrom,deepCopySlice:Copy_1.deepCopySlice,deepCopyLoop:Copy_1.deepCopyLoop,deepCopyFixed:Copy_1.deepCopyFixed},e.UrlCrypto=UrlCrypto_1.UrlCrypto,e.date={getLastSomeDays:Date_1.getLastSomeDays,getTodayDate:Date_1.getTodayDate,getDiffDays:Date_1.getDiffDays},e.adapterFringe=DecoratorAdapter_1.adapterFringe,e.throttle=DecoratorThrottle_1.throttle,e.debounce=DecoratorDebounce_1.debounce,e.measure=DecoratorMeasure_1.measure,e.memoize=DecoratorMemoize_1.memoize,e.trait=DecoratorTrait_1.trait,e.templateTrait=DecoratorTrait_1.templateTrait,e.ScreenAdapter=DecoratorScreen_1.ScreenAdapter,e.createDotData=Dot_1.createDotData,e.enumUtils={getKeyByValue:enum_1.getKeyByValue,isValueInEnum:enum_1.isValueInEnum},e.equalUtils={equal:Equal_1.equal},e.hotUpdate=HotUpdate_1.hotUpdate,e.http=Http_1.http,e.intervalUtils={interval:Interval_1.interval},e.NativeBridge=NativeBridge_1.NativeBridge,e.ResLoader=ResLoader_1.ResLoader,e.layerManager=GameLayer_1.layerManager,e.layer={gameUiLayer:GameLayer_1.gameUiLayer,getGameUiLayer:GameLayer_1.getGameUiLayer,resetGameUiLayer:GameLayer_1.resetGameUiLayer,addWidget:GameLayer_1.addWidget,initNodeConfig:GameLayer_1.initNodeConfig},e.numbers={randomInt:numbers_1.randomInt,randomFloat:numbers_1.randomFloat},e.performance={executeRenderingOptimize:RenderingOptimization_1.executeRenderingOptimize},e.ObjectPool=ObjectPool_1.ObjectPool,e.random={randomList:Random_1.randomList},e.storage=Storage_1.storage,e.timeoutUtils={timeout:Timeout_1.timeout},e.traitConfigInfo=TraitConfigInfo_1.traitConfigInfo,e.task=Task_1.task,e.timer={nextFrame:Timer_1.nextFrame,callLater:Timer_1.callLater},e.typeUtils={isString:Types_1.isString,isStringArray:Types_1.isStringArray,isObject:Types_1.isObject,isTypedArray:Types_1.isTypedArray,isNumber:Types_1.isNumber,isIterable:Types_1.isIterable,isBoolean:Types_1.isBoolean,isUndefined:Types_1.isUndefined,isDefined:Types_1.isDefined,isUndefinedOrNull:Types_1.isUndefinedOrNull,isFunction:Types_1.isFunction,getType:Types_1.getType},e.Trait=Trait_1.Trait,e.UI=UI_1.UI,e.url={getUrlParameterValue:Url_1.getUrlParameterValue},e.watchUtils={reactive:watch_1.reactive,watch:watch_1.watch},e.prefabInfo=PrefabInfo_1.prefabInfo,e.INSTANTIATE=function(e){return PrefabInfo_1.prefabInfo.getOrCreatePrefabInstantiate.call(PrefabInfo_1.prefabInfo,e)},e.gameStateManager=GameStateManager_1.gameStateManager}(falcon||(falcon={})),window.falcon=falcon,window.INSTANTIATE=falcon.INSTANTIATE})();
//# sourceMappingURL=falcon.js.map