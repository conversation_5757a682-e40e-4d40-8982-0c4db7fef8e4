
import { NativeDot } from "../../native/NativeDot";
import { onNativeReponse } from "../../native/NativeReceivedNative";
import { E_Dot_Start } from "../events/E_Dot_Start";
import { dotInfo } from "../vo/DotInfo";

export class Dot_Proxy extends falcon.Proxy {
    private sendState: "0" | "1";
    protected onInit(): void {
        let dotPercent = dotInfo.dotUpLoad
        if (dotPercent == 0) {
            dotPercent = Math.random() < 0.3 ? 1 : 2;
            falcon.storage.setItem(`dotUpLoad`, dotPercent);
        }

        onNativeReponse(`hideCallback`, data => {
            this.sendState = data.sendDataState;
        });
    }
    registerEvents(): { new(...args: any): falcon.ModuleEvent; }[] | null {
        return [E_Dot_Start];
    }

    receivedEvents($event: falcon.ModuleEvent): void {
        switch ($event.getClass()) {
            case E_Dot_Start:
                this.onDotStart($event as E_Dot_Start);
                break;
        }
    }

    onDotStart(event: E_Dot_Start) {
        if (!this.shouldUploadDot()) {
            return;
        }

        const { uploadType, key, params } = event;

        let uploadStr;
        switch (uploadType) {
            case falcon.DotUploadType.MARKET:
                uploadStr = `数仓`;
                NativeDot.callNativeDataUpload([params]);
                break;

            case falcon.DotUploadType.SHUSHU:
                uploadStr = `数数`;
                NativeDot.hsEventTrack(key as keyof DotShushu, params);
                break;
        }

        if (CC_DEBUG) {
            if (falcon.NativeBridge.isNative()) {
                if (params) {
                    console.log(`【打点:${uploadStr}】`, key, JSON.stringify([params]));
                } else {
                    console.log(`【打点:${uploadStr}】`, key, JSON.stringify(params));
                }
            } else {
                if (params) {
                    console.log(`%c【打点:${uploadStr}】`, `color:#e97200;background:#dddddd;`, key, params);
                } else {
                    console.log(`%c【打点:${uploadStr}】`, `color:#e97200;background:#dddddd;`, key);
                }
            }
        }
    }

    // debug || 30%概率上传
    private shouldUploadDot(): boolean {
        if (this.sendState === '0') {
            return false;
        }

        return CC_DEBUG || dotInfo.dotUpLoad === 1;
    }

}