import { BehaviorBase, IBehaviorConfig, IBehaviorData } from "../../combat/core/BehaviorBase";
import { BuffData } from "../../components/combat/BuffComponent";
import { IBuffBehaviorConfig } from "../buff/BuffBehaviorBase";

/**
 * Buff叠加方式枚举
 */
export enum BuffStackType {
    /**更新Buff(同时只存在一个) */
    UPDATE = 'update',    
    /**优先级叠加(同时存在多个) */
    PRIORITY = 'priority' 
}
/**
 * 基础堆叠配置接口
 * 包含所有堆叠行为的通用配置
 */
export interface IBuffStackBehaviorConfig extends IBehaviorConfig {
    /**叠加ID，唯一标识 */
    stackId: string;
    /**叠加方式类型 */
    stackType: BuffStackType;
    /**最大叠加层数 */
    maxStack: number;
}
/**
 * 堆叠行为基类
 * 参考技能和Buff模块的实现，自动包含配置信息
 * 支持特定类型的堆叠配置
 */
export abstract class BuffStackBehaviorBase<T extends IBuffStackBehaviorConfig = IBuffStackBehaviorConfig> extends BehaviorBase<T> {
    /**
     * 处理buff创建/叠加
     * 决定是创建新buff还是更新现有buff，以及激活状态的处理
     */
    handleBuffAdd?(params: IBuffAddParams): IBuffAddResult;

    /**
     * 处理buff销毁
     * 当buff被销毁时，决定如何处理同组中的其他buff
     * 例如优先级行为中，会激活剩余buff中优先级最高的
     */
    handleBuffDestroy?(params: IBuffRemoveParams): IBuffRemoveResult;
}

/**
 * Buff堆叠处理参数接口
 * 包含处理buff堆叠所需的所有信息：
 * - 实体和世界信息
 * - 现有的同组buff
 * - 新buff的配置信息
 * - 堆叠规则配置
 */
export interface IBuffAddParams {
    existingBuffs: BuffData<IBehaviorData>[]; // 同组所有buff
    newBuffConfig: IBuffBehaviorConfig;  // 新buff的配置
}

/**
 * Buff堆叠处理结果接口
 * 定义堆叠策略处理后的结果：
 * - 是否需要创建新的buff实例
 * - 新创建的buff是否应该被激活
 */
export interface IBuffAddResult {
    shouldCreateNew: boolean;  // 是否需要创建新buff
    shouldActivate: boolean;   // 新buff是否应该激活
}

/**
 * Buff销毁处理参数接口
 * 包含处理buff销毁所需的所有信息：
 * - 实体和世界信息
 * - 被销毁的buff数据
 * - 同组剩余的buff列表
 * - 堆叠规则配置
 */
export interface IBuffRemoveParams {
    destroyingBuff: BuffData<IBehaviorData>; // 被销毁的buff
    remainingBuffs: BuffData<IBehaviorData>[]; // 同组剩余的所有buff
}

/**
 * Buff销毁处理结果接口
 * 目前为空，因为销毁后的激活逻辑由策略内部直接处理
 * 未来可能扩展以支持更复杂的销毁后处理
 */
export interface IBuffRemoveResult {
    // 销毁处理完成后的结果，不需要在处理器层面处理激活逻辑
    // 具体策略内部会自行处理激活逻辑
}

export const StackResult = {
    // 默认创建和激活
    CREATE_AND_ACTIVATE: {
        shouldCreateNew: true,
        shouldActivate: true
    } as IBuffAddResult,

    // 不创建新buff
    NO_CREATE: {
        shouldCreateNew: false,
        shouldActivate: false
    } as IBuffAddResult,

    // 创建但不激活
    CREATE_NOT_ACTIVATE: {
        shouldCreateNew: true,
        shouldActivate: false
    } as IBuffAddResult,
} as const;