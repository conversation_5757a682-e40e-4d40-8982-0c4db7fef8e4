CCEffect %{
  techniques:
  - passes:
    - vert: vs
      frag: fs
      blendState:
        targets:
        - blend: true
      rasterizerState:
        cullMode: none
      properties:
        texture: { value: white }
        alphaThreshold: { value: 0.5 }
        angle: { 
          value: 0.0,
          editor: { tooltip: "角度", range: [0.0, 360.0] }
        }
        offset: { 
          value: 0.0,
          editor: { tooltip: "偏移" }
        }
        uvRatio: { 
          value: 1.0,
          editor: { tooltip: "比例" }
        }
        beginColor: { 
          value: [1, 1, 1, 1],
          editor: { type: color, tooltip: "初始颜色" }
        }
        middleColor: { 
          value: [1, 1, 1, 1],
          editor: { type: color, tooltip: "中间颜色" }
        }
}%
 
 
CCProgram vs %{
  precision highp float;
 
  #include <cc-global>
  #include <cc-local>
 
  in vec3 a_position;
  in vec4 a_color;
  out vec4 v_color;
 
  #if USE_TEXTURE
  in vec2 a_uv0;
  out vec2 v_uv0;
  #endif
 
  void main () {
    vec4 pos = vec4(a_position, 1);
 
    #if CC_USE_MODEL
    pos = cc_matViewProj * cc_matWorld * pos;
    #else
    pos = cc_matViewProj * pos;
    #endif
 
    #if USE_TEXTURE
    v_uv0 = a_uv0;
    #endif
 
    v_color = a_color;
 
    gl_Position = pos;
  }
}%
 
 
CCProgram fs %{
  precision highp float;
  
  #include <alpha-test>
  #include <texture>
 
  in vec4 v_color;
  
  #if USE_TEXTURE
  in vec2 v_uv0;
  uniform sampler2D texture;
  #endif
 
  uniform Factor {
      float angle;
      float offset;
      float uvRatio;
  };
 
  uniform Constant {
      vec4 beginColor;
      vec4 middleColor;
  };
 
  void main () {
    vec4 o = vec4(1, 1, 1, 1);
 
    #if USE_TEXTURE
      CCTexture(texture, v_uv0, o);
    #endif
 
    o *= v_color;
    ALPHA_TEST(o);
 
    float angleInRadians = radians(angle); 
  	float ratio = clamp((v_uv0.y * cos(angleInRadians) + v_uv0.x * sin(angleInRadians) + offset) * uvRatio, 0.0, 1.0);
	  float beginRatio = 1.0 - ratio;
  	float endRatio = ratio;
  
    if(beginRatio > 0.5) {
      gl_FragColor = vec4(
            o.r * (beginColor.r * beginRatio + middleColor.r * endRatio),
            o.g * (beginColor.g * beginRatio + middleColor.g * endRatio),
            o.b * (beginColor.b * beginRatio + middleColor.b * endRatio),
            o.a * (beginColor.a * beginRatio + middleColor.a * endRatio)
          );
    }else {
      gl_FragColor = vec4(
            o.r * (middleColor.r * beginRatio + beginColor.r * endRatio),
            o.g * (middleColor.g * beginRatio + beginColor.g * endRatio),
            o.b * (middleColor.b * beginRatio + beginColor.b * endRatio),
            o.a * (middleColor.a * beginRatio + beginColor.a * endRatio)
          );
    }
  	
    
  }
}%
