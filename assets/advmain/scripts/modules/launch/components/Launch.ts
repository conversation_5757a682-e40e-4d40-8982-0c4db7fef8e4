
import { SubGameBridge } from "../../../base/SubGameBridge";
import { PrefabConfig } from "../../prefab/PrefabConfig";

const { ccclass, property } = cc._decorator;
/**
 * 启动
 */
@ccclass
export default class Launch extends falcon.Component {
    onLoad() {
        if (CC_DEBUG) {
            // 加载gm模块
            SubGameBridge.showUI(PrefabConfig.ToolMain);
        }
    }

    async start() {
        console.log("Launch start")
    }
}
