import OpacityComponent from '../components/OpacityComponent';
import { PreviewTag } from '../components/tag/PreviewTag';
import { CellRenderTemplate, CellTempleteMustArgs, CellTempleteOptionalArgs } from './CellTemplete';

/**
 * 预览方块模板
 */
export class PreviewCellTemplete extends CellRenderTemplate<CellTempleteOptionalArgs> {
    createTemplete(mustArgs: CellTempleteMustArgs, optionalArgs: CellTempleteOptionalArgs): number {
        const entity = super.createTemplete(mustArgs, optionalArgs);
        this.world.addComponent(entity, PreviewTag, true, '');
        this.world.addComponent(entity, OpacityComponent, 128);
        return entity;
    }
}