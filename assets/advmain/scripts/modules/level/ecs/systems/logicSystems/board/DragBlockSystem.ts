import { ISystemConfig, System } from '../../../cores/System';
import { ECSEvent } from '../../../GameEvent';
import { IDragBlockStartInfo, IPutBlockBackInfo, IPutBlockInfo } from '../../../GameEventData';
import ShapeComponent from '../../../components/board/ShapeComponent';
import NodeComponent from '../../../components/NodeComponent';
import DragComponent from '../../../components/DragComponent';
import { UITransformComponent } from '../../../components/UITransformComponent';
import { RcShape } from '../../../define/EcsConfig';
import { IPoint } from '../../../define/EcsDefine';
import { IBoardConfig } from '../../../config/conf/BoardConfig';
export interface IDragSystemConfig extends ISystemConfig {
    /** 拖拽倍率 */
    dragMultiplier: number;
    /** 拖拽Y轴偏移 */
    dragYOffset: number;
    /** 基准缩放 */
    baseScale: number;
}

export class DragBlockSystem extends System<IDragSystemConfig> {
    private boardsCfg: IBoardConfig[] = [];
    /**当前所处棋盘的 cellSize, null 表示未在任何棋盘上*/
    private _curCellSize: number = null;
    /**最大棋盘格子尺寸*/
    private maxBoardCellSize: number;
    /**拖动Y轴偏移*/
    private _curDragEntity: number = null;
    private _curNodeComponent: NodeComponent = null;
    private _curDragComponent: DragComponent = null;
    private _curTransformComponent: UITransformComponent = null;
    private _startOldPos: IPoint = null;
    private _startPos: IPoint = { x: 0, y: 0 };
    private _startClickAnchorDy: number = 0;
    private _originalZIndex: number = 0; // 保存原始zIndex

    init(): void {
        // 监听拖拽生命周期事件
        this.world.eventBus.on(ECSEvent.GameEvent.DRAG_BLOCK_START, this.onDragStart, this);
        this.world.eventBus.on(ECSEvent.GameEvent.DRAG_BLOCK_MOVE, this.onDragMove, this);
        this.world.eventBus.on(ECSEvent.GameEvent.DRAG_BLOCK_END, this.onDragEndCancel, this);
        this.world.eventBus.on(ECSEvent.GameEvent.DRAG_BLOCK_CANCEL, this.onDragEndCancel, this);
        this.world.eventBus.on(ECSEvent.GameEvent.PUT_BLOCK_BACK, this.onPutBlockBack, this);

        this.world.onComponentAdd(DragComponent, this.onDragComponentAdd, this);
    }
    dispose(): void {
        this.world.eventBus.off(ECSEvent.GameEvent.DRAG_BLOCK_START, this.onDragStart, this);
        this.world.eventBus.off(ECSEvent.GameEvent.DRAG_BLOCK_MOVE, this.onDragMove, this);
        this.world.eventBus.off(ECSEvent.GameEvent.DRAG_BLOCK_END, this.onDragEndCancel, this);
        this.world.eventBus.off(ECSEvent.GameEvent.DRAG_BLOCK_CANCEL, this.onDragEndCancel, this);
        this.world.eventBus.off(ECSEvent.GameEvent.PUT_BLOCK_BACK, this.onPutBlockBack, this);

        this.world.offComponentAdd(DragComponent, this.onDragComponentAdd, this);
    }

    private onDragComponentAdd(entityId: number, dragComponent: DragComponent) {
        dragComponent.dragMultiplier = this.config.dragMultiplier;
    }

    private onDragStart(info: IDragBlockStartInfo & { wPos?: IPoint }) {
        // 兼容两种数据格式
        const startPos = info.wPos || info.pos;
        this._curDragEntity = info.entityId;
        this.initShape(info.entityId);

        // 记录拖拽开始的相关信息
        const nPos = { x: this._curNodeComponent.x, y: this._curNodeComponent.y };
        this._startOldPos = { x: nPos.x, y: nPos.y };
        this._startPos = { x: startPos.x, y: nPos.y };
        this._startClickAnchorDy = startPos.y - nPos.y;

        // 保存原始zIndex并设置拖拽时的高层级
        this._originalZIndex = this._curNodeComponent.zIndex;
        this._curNodeComponent.zIndex = 200; // 使用更高的zIndex确保在其他待放置块上方
        console.log(`拖拽开始: 设置zIndex从 ${this._originalZIndex} 到 ${this._curNodeComponent.zIndex}`);

        // 设置拖拽时的尺寸和位置
        this._curTransformComponent.width = this._curDragComponent.hotArea.width;
        this._curTransformComponent.height = this._curDragComponent.hotArea.height;
        this._curNodeComponent.x = startPos.x;
        this._curNodeComponent.y = this._startPos.y + this.config.dragYOffset;

        // 按最大棋盘尺寸放大，进入拖拽模式
        this._curNodeComponent.scaleX = this._curNodeComponent.scaleY = this.maxBoardCellSize / 106;
        this._curCellSize = this.maxBoardCellSize; // 记录当前缩放基准
    }

    private onDragMove(aPos: IPoint) {
        if (!aPos || !this._curNodeComponent) return;

        // 应用拖动倍率
        const dragMultiplier = this._curDragComponent?.dragMultiplier || 1.4;
        const dy = (aPos.y - this._startPos.y) * dragMultiplier;
        const dx = (aPos.x - this._startPos.x) * dragMultiplier;
        const newX = this._startPos.x + dx;
        const newY = this._startPos.y + dy - this._startClickAnchorDy * dragMultiplier + this.config.dragYOffset;

        this._curNodeComponent.x = newX;
        this._curNodeComponent.y = newY;

        // 拖动过程中根据所在棋盘动态调整缩放
        this.adaptScaleByPosition({ x: newX, y: newY });
        this.sendInfo(ECSEvent.GameEvent.PREVIEW_BLOCK);
    }

    private onDragEndCancel() {
        this.sendInfo(ECSEvent.GameEvent.PUT_BLOCK);
    }

    initShape(entityId: number) {
        this.boardsCfg = this.world.handlerCenter.boardHandler.getBoardConfigs();
        this._curNodeComponent = this.world.getComponent(entityId, NodeComponent);
        this._curDragComponent = this.world.getComponent(entityId, DragComponent);
        this._curTransformComponent = this.world.getComponent(entityId, UITransformComponent);
        this._curTransformComponent.width = this._curDragComponent.hotArea.width;
        this._curTransformComponent.height = this._curDragComponent.hotArea.height;
        // 计算最大棋盘格尺寸，但等待区保持 baseScale
        this.maxBoardCellSize = this.boardsCfg.reduce((m, b) => Math.max(m, b.cellSize), 0);
        this._curNodeComponent.scaleX = this._curNodeComponent.scaleY = this.config.baseScale;
    }

    onPutBlockBack(putBlockBackInfo: IPutBlockBackInfo) {
        if (putBlockBackInfo.entityId !== this._curDragEntity) return;
        // 判定放块是否成功
        if (putBlockBackInfo.isSuccess) {
            const boardCfg = this.boardsCfg.find((b) => b.boardId === putBlockBackInfo.boardId);
            if (!boardCfg) {
                this.revertDragNode();
                return;
            }
            const cellSize = boardCfg.cellSize;
            const shapeComp = this.world.getComponent(this._curDragEntity, ShapeComponent);
            // 将中心点转换为左下角，然后计算位置
            const boardLeftBottomX = boardCfg.startPos.x - (boardCfg.cols * cellSize) / 2;
            const boardLeftBottomY = boardCfg.startPos.y - (boardCfg.rows * cellSize) / 2;
            const centerX = boardLeftBottomX + (putBlockBackInfo.x + shapeComp.shape.width / 2) * cellSize;
            const centerY = boardLeftBottomY + (boardCfg.rows - putBlockBackInfo.y - shapeComp.shape.height / 2) * cellSize;
            this.world.eventBus.emitEntityEvent(this._curDragEntity, ECSEvent.EntityEvent.PUT_BLOCK_TWEEN, { x: centerX, y: centerY });
        } else {
            // 放置失败，立即回退
            this.revertDragNode();
        }
    }

    public revertDragNode() {
        // 恢复原始zIndex
        this._curNodeComponent.zIndex = this._originalZIndex;
        console.log(`拖拽结束: 恢复zIndex到 ${this._originalZIndex}`);

        this._curNodeComponent.x = this._startOldPos.x;
        this._curNodeComponent.y = this._startOldPos.y;
        this._curNodeComponent.scaleX = this._curNodeComponent.scaleY = this.config.baseScale;
        this._curTransformComponent.width = this._curDragComponent.hotArea.width;
        this._curTransformComponent.height = this._curDragComponent.hotArea.height;
        // 发送清理预览
        this.world.eventBus.emit(ECSEvent.GameEvent.CLEAR_PREVIEW_BLOCK);
    }

    /* 逻辑通信 */
    private sendInfo(event: ECSEvent.GameEvent | ECSEvent.BaseEvent) {
        const pos = this.world.handlerCenter.boardHandler.convertToGridCoord(
            { x: this._curNodeComponent.x, y: this._curNodeComponent.y },
            this.boardsCfg,
            this._curDragEntity,
        );
        const shapeComp = this.world.getComponent(this._curDragEntity, ShapeComponent)!;
        const info: IPutBlockInfo = {
            x: pos?.gridX,
            y: pos?.gridY,
            shape: shapeComp.shape as (typeof RcShape)[number],
            entityId: this._curDragEntity,
            boardId: pos?.boardId,
        };
        if (event === ECSEvent.GameEvent.PUT_BLOCK) {
            this.world.eventBus.emit(ECSEvent.GameEvent.CLEAR_PREVIEW_BLOCK);
        }
        this.world.eventBus.emit(event, info);
    }

    private adaptScaleByPosition(pos: { x: number; y: number }) {
        for (const b of this.boardsCfg) {
            if (!b.isCanPut) continue;
            // 将中心点转换为左下角，计算棋盘范围
            const w = b.cols * b.cellSize;
            const h = b.rows * b.cellSize;
            const leftBottomX = b.startPos.x - w / 2;
            const leftBottomY = b.startPos.y - h / 2;
            if (pos.x >= leftBottomX && pos.x <= leftBottomX + w && pos.y >= leftBottomY && pos.y <= leftBottomY + h) {
                if (this._curCellSize !== b.cellSize) {
                    this._curCellSize = b.cellSize;
                    this._curNodeComponent.scaleX = this._curNodeComponent.scaleY = b.cellSize / 106;
                }
                return;
            }
        }
    }

    update(): void {}
}
