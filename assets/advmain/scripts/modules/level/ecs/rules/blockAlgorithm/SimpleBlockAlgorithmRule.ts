import { AlgoClassTypeConst } from '../../../../algorithm/list/algoSDK/core/puzzlecore';
import ProduceBlockAlgorithmComponent from '../../components/special/ProduceBlockAlgorithmComponent';
import { ECSEvent } from '../../GameEvent';
import { ECSRuleBase, IECSRuleConfig } from '../core/ECSRuleBase';

export interface ISimpleBlockAlgorithmRuleConfig extends IECSRuleConfig {
    /**生成块的算法ID */
    algorithmId: AlgoClassTypeConst;
}

/**
 * 生成块算法规则
 * 根据RuleConfig配置的算法ID生成对应的块
 * 例如：AlgoClassTypeConst.AlgoRandom
 * 生成随机块
 * 或者：AlgoClassTypeConst.algo_random_no_death
 * 生成随机无死块
 */
export class SimpleBlockAlgorithmRule extends ECSRuleBase<ISimpleBlockAlgorithmRuleConfig, ECSEvent.GameEvent.PRODUCE_BLOCK> {
    canExecute(): boolean {
        // 检查是否已经存在ProduceBlockAlgorithmComponent组件,已存在证明已有算法策略，跳过本策略执行。
        return this.world.query([ProduceBlockAlgorithmComponent]).length === 0;
    }
    execute(): void {
        const world = this.world;
        const entityId = world.createEntity();
        console.log(this.config.ruleId + ' algorithmId:', this.config.algorithmId);
        world.addComponent(entityId, ProduceBlockAlgorithmComponent, this.config.algorithmId);
    }
}
