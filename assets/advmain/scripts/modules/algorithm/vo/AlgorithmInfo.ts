import { GameMode } from "../../game/type/GameType";
import { gameInfo } from "../../game/vo/GameInfo";

class AlgorithmInfo {
    // 出块id数组
    get blockIdList(): number[] {
        return falcon.storage.getItem(`chapterBlockLists`, []) as number[];
    }
    setBlockIdList(value: number[]) {
        falcon.storage.setItem(`chapterBlockLists`, value);
    }

    //出块的最优位置信息
    get blockPosList(): any[] {
        return falcon.storage.getItem(`chapterBlockPosLists`, []) as any[];
    }
    setBlockPosList(value: any[]) {
        falcon.storage.setItem(`chapterBlockPosLists`, value);
    }

    // 出题来源
    private _algoSource: number;
    get algoSource(): number {
        return this._algoSource;
    }
}

export const algorithmInfo = new AlgorithmInfo();

