
import { ActionUtil } from '../../logicHandler/ActionUtil';
import BoardUtil from '../../logicHandler/BoardUtil';
import { CellUtil } from '../../logicHandler/CellUtil';
import { LogicWorld} from '../World';
import ReviveUtil from '../../logicHandler/ReviveUtil';

/**通用或者工具函数处理中心 */
export class UtilCenter {
    /**棋盘处理器 */
    boardUtil: BoardUtil;
    /**方块处理器 */
    cellUtil: CellUtil;
    /**行动意图处理器 */
    actionUtil: ActionUtil;
    /**复活处理器 */
    reviveUtil: ReviveUtil;

    constructor(world: LogicWorld) {
        this.boardUtil = new BoardUtil(world);
        this.cellUtil = new CellUtil(world);
        this.actionUtil = new ActionUtil(world);
        this.reviveUtil = new ReviveUtil(world);
    }
}
