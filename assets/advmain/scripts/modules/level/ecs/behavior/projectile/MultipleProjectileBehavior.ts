import { IActionContext } from '../../combat/core/ActionContext';
import { ProjectileComponent } from '../../components/combat/ProjectileComponent';
import NodeComponent from '../../components/NodeComponent';
import { BehaviorConst } from '../../cores/center/BehaviorCenter';
import { ProjectilePathBehaviorConst } from '../../registry/behavior/ProjectilePathBehaviorRegistry';
import { IProjectileBehaviorConfig, ProjectileBehaviorBase } from './ProjectileBehaviorBase';
import { PathCalculationData, IMoveParams } from '../projectilePath/ProjectilePathBehaviorBase';
import { ECSEvent } from '../../GameEvent';
import { IPoint } from '../../define/EcsDefine';

/**多段路径配置 */
export interface IMultipleProjectileConfig extends IProjectileBehaviorConfig {
    /**命中效果 */
    onHitBuffs: string[];
    /**多段路径配置 */
    pathSegments: {
        /**路径行为类型 */
        pathBehaviorType: ProjectilePathBehaviorConst;
        /**该段路径的移动参数 */
        moveParams: IMoveParams;
    }[];
}

/**多段路径数据接口 */
interface MultiplePathData {
    currentSegmentIndex: number;
    segmentStartPosition: IPoint;
    segmentStartTime: number; // 添加段起始时间
}

/**多段飞行路径投射物 */
export class MultipleProjectileBehavior extends ProjectileBehaviorBase<IMultipleProjectileConfig> {
    private readonly pathParamsCache: PathCalculationData = {
        startPos: { x: 0, y: 0 },
        targetPos: { x: 0, y: 0 },
        elapsedTime: 0,
        deltaTime: 0,
        speed: 200,
        moveTime: 0,
    };

    onCreate(entityId: number, projectileComponent: ProjectileComponent): void {
        projectileComponent.pathPrecomputedData = {
            ...projectileComponent.pathPrecomputedData,
            currentSegmentIndex: 0,
            segmentStartPosition: { x: projectileComponent.startPosition.x, y: projectileComponent.startPosition.y },
            segmentStartTime: projectileComponent.createTime, // 初始化段起始时间
        } as MultiplePathData;

        // 初始化速度管理数据
        this.resetSpeedManagement(projectileComponent);
    }

    onDestroy(entityId: number, projectileComponent: ProjectileComponent): void {}

    onHit(entityId: number, projectileComponent: ProjectileComponent, targetEntityId: number): void {
        const onHitBuffs = this.config.onHitBuffs;
        if (!onHitBuffs) {
            return;
        }
        for (let i = 0, len = onHitBuffs.length; i < len; i++) {
            this.world.eventBus.emit(ECSEvent.GameEvent.ADD_BUFF, {
                casterEntityId: entityId,
                ownerEntityId: targetEntityId,
                buffBehaviorId: onHitBuffs[i],
            });
        }
    }

    onMove(entityId: number, projectileComponent: ProjectileComponent, worldTime: number, deltaTime: number): boolean {
        const config = this.config;
        const actionContext = projectileComponent.context;
        const pathData = projectileComponent.pathPrecomputedData as MultiplePathData;

        if (!pathData || !config.pathSegments || config.pathSegments.length === 0) {
            console.error(`多段投射物 ${projectileComponent.projectileBehaviorId} 缺少路径配置`);
            return false;
        }

        // 获取当前段配置
        const currentSegment = this.getCurrentSegment(pathData.currentSegmentIndex);
        const pathBehavior = this.world.behaviorCenter.getBehaviorByType(BehaviorConst.ProjectilePath, currentSegment.pathBehaviorType);

        if (!pathBehavior) {
            console.error(`多段投射物 ${projectileComponent.projectileBehaviorId} 缺少路径行为: ${currentSegment.pathBehaviorType}`);
            return false;
        }

        // 获取目标位置
        const targetPos = this.getTargetPosition(actionContext, pathData.currentSegmentIndex, config.pathSegments.length);
        if (!targetPos) {
            console.error(`多段投射物 ${projectileComponent.projectileBehaviorId} 缺少目标位置`);
            return false;
        }

        // 使用智能速度计算
        const speed = this.getSmartSpeed(
            currentSegment.moveParams || config.moveParams,
            pathData.segmentStartPosition,
            targetPos,
            projectileComponent,
        );

        // 计算路径
        const segmentElapsedTime = worldTime - pathData.segmentStartTime; // 使用段起始时间
        const pathParamsCache = this.pathParamsCache;

        pathParamsCache.startPos.x = pathData.segmentStartPosition.x;
        pathParamsCache.startPos.y = pathData.segmentStartPosition.y;
        pathParamsCache.targetPos.x = targetPos.x;
        pathParamsCache.targetPos.y = targetPos.y;
        pathParamsCache.elapsedTime = segmentElapsedTime;
        pathParamsCache.deltaTime = deltaTime;
        pathParamsCache.speed = speed;
        pathParamsCache.moveTime = currentSegment.moveParams.moveTime;

        const pathResult = pathBehavior.calculatePath(pathParamsCache, projectileComponent);

        // 检查路径是否完成
        if (pathResult.isComplete) {
            return this.handleSegmentComplete(entityId, projectileComponent, pathData, actionContext, worldTime);
        }

        // 更新位置
        const positionComponent = this.world.getComponent(entityId, NodeComponent);
        if (positionComponent) {
            positionComponent.x = pathResult.position.x;
            positionComponent.y = pathResult.position.y;
        }
        return true;
    }

    /**
     * 获取当前段配置（处理段数不够的情况）
     */
    private getCurrentSegment(currentSegmentIndex: number): any {
        const configuredSegments = this.config.pathSegments;
        if (currentSegmentIndex < configuredSegments.length) {
            return configuredSegments[currentSegmentIndex];
        }
        // 段数不够时，重复使用最后一个段
        return configuredSegments[configuredSegments.length - 1];
    }

    /**
     * 获取目标位置
     */
    private getTargetPosition(actionContext: IActionContext, currentSegmentIndex: number, totalSegments: number): IPoint | null {
        // 单段路径或没有中间位置时，使用最终目标
        if (totalSegments === 1 || !actionContext.middlePositions) {
            return this.world.handlerCenter.actionHandler.getTargetPosition(actionContext);
        }

        // 多段路径：以中间点数量为准
        const middlePositionsCount = actionContext.middlePositions.length;

        // 如果当前段索引超出中间点数量，使用最终目标
        if (currentSegmentIndex >= middlePositionsCount) {
            return this.world.handlerCenter.actionHandler.getTargetPosition(actionContext);
        }

        // 使用对应的中间位置
        return actionContext.middlePositions[currentSegmentIndex];
    }

    /**
     * 处理段完成逻辑
     */
    private handleSegmentComplete(
        entityId: number,
        projectileComponent: ProjectileComponent,
        pathData: MultiplePathData,
        actionContext: IActionContext,
        worldTime: number,
    ): boolean {
        pathData.currentSegmentIndex++;

        // 判断是否完成所有段：以中间点数量为准
        const middlePositionsCount = actionContext.middlePositions?.length || 0;
        const requiredSegments = middlePositionsCount > 0 ? middlePositionsCount + 1 : this.config.pathSegments.length;
        const isCompleted = pathData.currentSegmentIndex >= requiredSegments;

        if (isCompleted) {
            if (actionContext.targetEntityId) {
                console.log(`多段投射物 ${projectileComponent.projectileBehaviorId} 完成所有路径，命中实体 ${actionContext.targetEntityId}`);
                this.onStartHit(entityId, projectileComponent, actionContext.targetEntityId);
            }
            return false;
        }

        // 更新下一段的起始位置和时间，重置速度管理
        const positionComponent = this.world.getComponent(entityId, NodeComponent);
        if (positionComponent) {
            pathData.segmentStartPosition.x = positionComponent.x;
            pathData.segmentStartPosition.y = positionComponent.y;
        }
        pathData.segmentStartTime = worldTime; // 更新段起始时间
        this.resetSpeedManagement(projectileComponent); // 重置速度管理，让下一段重新计算
        return true;
    }
}
