import { World } from '../cores/World';
import { SnapshotCenter } from '../cores/center/SnapshotCenter';
import { ECSEvent } from '../GameEvent';
import { IPutBlockInfo } from '../GameEventData';
import { ComponentRegistry } from '../registry/ComponentRegistry';

interface EcsSnapshot {
    entities: { [entityId: number]: ComponentData[] };
    serializableObjects: unknown[];
}

interface ComponentData {
    type: string;
    data: unknown;
}

interface GameRef {
    levelConfig?: object;
    saveSnapshot?: () => void;
    [key: string]: unknown;
}

interface SimulatedGame extends GameRef {
    levelConfig: object | null;
    saveSnapshot: () => void;
}

interface SystemWithLifecycle {
    dispose?: () => void;
    init?: () => void;
    bubbleUnoccludedBlocksToTop?: () => void;
    constructor: { name: string };
}

interface FrameCreationState {
    isProcessing: boolean;
    entityEntries: Array<[string, ComponentData[]]>;
    currentIndex: number;
    resolveCallback: ((value: boolean) => void) | null;
    rejectCallback: ((reason?: unknown) => void) | null;
}
/**
 * 模拟世界管理器
 * 创建独立的纯逻辑模拟世界，不影响真实游戏状态
 */

export class SimulationWorldManager {
    private readonly originalWorld: World;
    private readonly snapshotMgr: SnapshotCenter;
    private simulationWorld: World | null = null;
    private snapshot: EcsSnapshot | null = null;
    private simulating = false;
    private originalGameRef: GameRef | null = null;

    private frameCreationState: FrameCreationState = {
        isProcessing: false,
        entityEntries: [],
        currentIndex: 0,
        resolveCallback: null,
        rejectCallback: null,
    };

    constructor(world: World) {
        this.originalWorld = world;
        this.snapshotMgr = new SnapshotCenter(world, 'simulation');
    }
    /** 开始模拟 */
    async startSimulationAsync(): Promise<boolean> {
        if (this.simulating) {
            return false;
        }
        // this.snapshot = this.snapshotMgr.generateSnapshot();
        // this.simulationWorld = new World();
        // this.simulationWorld.init();
        // this.simulationWorld.configCenter = this.originalWorld.configCenter;
        // if (this.simulationWorld.ruleCenter) {
        //     this.simulationWorld.ruleCenter.config = this.originalWorld.configCenter;
        // }
        // this.simulationWorld.nextEntityId = this.originalWorld.nextEntityId;
        // 先恢复快照数据，确保实体和关系数据完整
        const restoreSuccess = await this.restoreSnapshotAsync(this.snapshot);
        if (!restoreSuccess) {
            this.cleanup();
            return false;
        }

        // 在数据恢复完成后再添加系统
        this.copyLogicSystems();

        this.interceptGlobalAccess();
        this.simulating = true;

        return true;
    }

    /** 结束模拟 */
    endSimulation(): boolean {
        if (!this.simulating) return false;
        this.restoreGlobalAccess();
        this.cleanup();
        return true;
    }
    /** 模拟放置块 */
    simulatePlaceBlock(info: IPutBlockInfo): boolean {
        if (!this.ensureRunning()) return false;

        this.simulationWorld!.eventBus.emit(ECSEvent.GameEvent.PUT_BLOCK, info);

        return true;
    }
    /** 模拟消除 */
    simulateElimination(): void {
        if (!this.ensureRunning()) return;
    }
    /** 模拟冒泡 */
    simulateBubble(): boolean {
        if (!this.ensureRunning()) return false;
        this.simulationWorld!.eventBus.emit(ECSEvent.GameEvent.MULTIBOARD_BLOCKS_DEPLOYED);
        const sys = this.findSystem('MultiBoardSystem');
        if (sys && typeof sys.bubbleUnoccludedBlocksToTop === 'function') {
            sys.bubbleUnoccludedBlocksToTop();
        }
        return true;
    }
    /** 模拟更新 */
    simulateUpdate(dt = 0.016): void {
        if (!this.ensureRunning()) return;
        this.simulationWorld!.update(dt);
    }
    /** 恢复到初始状态 */
    restoreToInitialState(): boolean {
        // if (!this.ensureRunning() || !this.snapshot) return false;
        // this.simulationWorld!.systems.forEach((system: SystemWithLifecycle) => {
        //     if (system.dispose) {
        //         system.dispose();
        //     }
        // });
        // this.simulationWorld!.entities.clear();
        // this.simulationWorld!.components.clear();
        // (this.simulationWorld! as unknown as { componentAddListeners: Map<unknown, unknown> }).componentAddListeners.clear();
        // (this.simulationWorld! as unknown as { componentRemoveListeners: Map<unknown, unknown> }).componentRemoveListeners.clear();
        // (this.simulationWorld! as unknown as { worldQuery: WorldQuery }).worldQuery = new WorldQuery(this.simulationWorld!);
        // (this.simulationWorld! as unknown as { worldRelation: WorldRelation }).worldRelation = new WorldRelation(this.simulationWorld!);
        // this.simulationWorld!.nextEntityId = this.originalWorld.nextEntityId;
        // const tempSM = new SnapshotCenter(this.simulationWorld!, 'temp_restore');
        // tempSM.restoreSnapshot(this.snapshot);
        // this.simulationWorld!.systems.forEach((system: SystemWithLifecycle) => {
        //     if (system.init) {
        //         system.init();
        //     }
        // });
        return true;
    }

    /** 是否在模拟模式 */
    isInSimulation(): boolean {
        return this.simulating;
    }

    /** 获取模拟世界 */
    getSimulationWorld(): World {
        if (!this.ensureRunning()) {
            throw new Error('当前不在模拟模式');
        }
        return this.simulationWorld!;
    }
    /** 获取模拟状态 */
    getSimulationState(): { entityCount: number; worldTime: number } | null {
        if (!this.ensureRunning()) return null;
        return {
            // entityCount: this.simulationWorld!.entities.size,
            entityCount: 1,
            worldTime: this.simulationWorld!.worldTime,
        };
    }
    /** 复制逻辑系统 */
    private copyLogicSystems(): void {
        const originalSystems = this.originalWorld.systems;
        for (const sys of originalSystems) {
            // 直接使用系统实例的配置
            const systemConfig = sys.config;
            if (!systemConfig) continue;
            if (!this.isSystemSafe(systemConfig.systemId)) continue;
            this.simulationWorld!.addSystemById(systemConfig.systemId);
        }
    }

    /** 判断系统是否安全 */
    private isSystemSafe(systemId: string): boolean {
        // 只排除渲染系统，其他系统都是安全的
        return !systemId.includes('system_render');
    }

    private findSystem(className: string): SystemWithLifecycle | null {
        if (!this.simulationWorld) return null;
        return this.simulationWorld.systems.find((s: SystemWithLifecycle) => s.constructor.name === className) || null;
    }
    /** 确保在模拟模式 */
    private ensureRunning(): boolean {
        if (!this.simulating || !this.simulationWorld) {
            return false;
        }
        return true;
    }
    /** 拦截全局访问 */
    private interceptGlobalAccess(): void {
        const globalWindow = typeof window !== 'undefined' ? (window as unknown as { game?: GameRef }) : undefined;
        const globalThis_ = typeof globalThis !== 'undefined' ? (globalThis as unknown as { game?: GameRef }) : undefined;

        if (globalWindow?.game) {
            this.originalGameRef = globalWindow.game;
        } else if (globalThis_?.game) {
            this.originalGameRef = globalThis_.game;
        }
        const simulatedGame: SimulatedGame = {
            levelConfig: this.originalGameRef?.levelConfig ? { ...this.originalGameRef.levelConfig } : null,
            saveSnapshot: () => {},
        };
        if (globalWindow) {
            globalWindow.game = simulatedGame;
        }
        if (globalThis_) {
            globalThis_.game = simulatedGame;
        }
    }
    /** 恢复全局访问 */
    private restoreGlobalAccess(): void {
        if (!this.originalGameRef) return;
        const globalWindow = typeof window !== 'undefined' ? (window as unknown as { game?: GameRef }) : undefined;
        const globalThis_ = typeof globalThis !== 'undefined' ? (globalThis as unknown as { game?: GameRef }) : undefined;
        if (globalWindow) {
            globalWindow.game = this.originalGameRef;
        }
        if (globalThis_) {
            globalThis_.game = this.originalGameRef;
        }
    }
    /** 清理 */
    private cleanup(): void {
        if (this.simulationWorld) {
            this.simulationWorld.dispose();
            this.simulationWorld = null;
        }
        this.snapshot = null;
        this.simulating = false;
    }
    /** 强制清理 */
    forceCleanup(): void {
        this.restoreGlobalAccess();
        this.cleanup();
    }
    /** 恢复快照 */
    private async restoreSnapshotAsync(snapshot: EcsSnapshot): Promise<boolean> {
        if (!this.simulationWorld) {
            return false;
        }

        // 先恢复实体数据
        const success = await this.restoreEntitiesAsync(snapshot);
        if (!success) {
            return false;
        }

        // 再恢复关系数据，确保所有实体都已存在
        this.deserializeObjects(snapshot.serializableObjects);

        return true;
    }
    /** 恢复实体 */
    private async restoreEntitiesAsync(snapshot: EcsSnapshot): Promise<boolean> {
        return new Promise((resolve, reject) => {
            if (this.frameCreationState.isProcessing) {
                reject(new Error('已有分帧创建进程在进行中'));
                return;
            }
            const entityEntries = Object.entries(snapshot.entities);
            this.frameCreationState = {
                isProcessing: true,
                entityEntries: entityEntries,
                currentIndex: 0,
                resolveCallback: resolve,
                rejectCallback: reject,
            };
            this.processNextEntityBatch();
        });
    }
    /** 处理下一个实体批次 */
    private processNextEntityBatch(): void {
        const state = this.frameCreationState;
        if (!state.isProcessing || !this.simulationWorld) {
            this.finishFrameCreation(false);
            return;
        }
        const frameStartTime = performance.now();
        const targetFrameTime = 16;
        while (state.currentIndex < state.entityEntries.length && performance.now() - frameStartTime < targetFrameTime * 0.8) {
            const [entityIdStr, components] = state.entityEntries[state.currentIndex];
            const entityId = Number(entityIdStr);
            // this.simulationWorld.entities.add(entityId);
            this.restoreEntityComponents(entityId, components);
            state.currentIndex++;
        }
        if (state.currentIndex >= state.entityEntries.length) {
            this.finishFrameCreation(true);
            return;
        }
        if (typeof requestAnimationFrame !== 'undefined') {
            requestAnimationFrame(() => {
                this.processNextEntityBatch();
            });
        } else {
            this.scheduleNextFrame(() => {
                this.processNextEntityBatch();
            });
        }
    }
    /** 完成分帧创建 */
    private finishFrameCreation(success: boolean): void {
        const state = this.frameCreationState;
        if (state.resolveCallback) {
            state.resolveCallback(success);
        }
        this.frameCreationState = {
            isProcessing: false,
            entityEntries: [],
            currentIndex: 0,
            resolveCallback: null,
            rejectCallback: null,
        };
    }
    /** 恢复单个实体的组件数据 */
    private restoreEntityComponents(entityId: number, components: ComponentData[]): void {
        // components.forEach((compData) => {
        //     const componentType = compData.type;
        //     if (!this.simulationWorld!.components.has(componentType)) {
        //         this.simulationWorld!.components.set(componentType, new Map());
        //     }
        //     const entityMap = this.simulationWorld!.components.get(componentType)!;
        //     const component = this.deserializeComponent(componentType, compData.data);
        //     if (component) {
        //         entityMap.set(entityId, component as never);
        //     }
        // });
    }

    /** 反序列化组件数据 */
    private deserializeComponent(type: string, data: unknown): unknown {
        const ComponentConstructor = ComponentRegistry[type];
        if (!ComponentConstructor) {
            console.error(`SimulationWorldManager: 未找到组件构造函数 ${type}`);
            return null;
        }

        const instance = new ComponentConstructor();
        if (instance) {
            Object.assign(instance, data);
        }
        return instance;
    }

    /** 反序列化所有快照对象 */
    private deserializeObjects(objects: unknown[]): void {
        if (this.simulationWorld) {
            if (objects && objects.length > 0 && objects[0]) {
                this.simulationWorld.deserialize(objects[0]);
            }
        }
    }

    /** 调度下一帧 */
    private scheduleNextFrame(callback: () => void): void {
        if (typeof MessageChannel !== 'undefined') {
            const channel = new MessageChannel();
            channel.port2.onmessage = () => callback();
            channel.port1.postMessage(null);
            return;
        }
        if (typeof Promise !== 'undefined') {
            Promise.resolve().then(callback);
            return;
        }
        callback();
    }
}
