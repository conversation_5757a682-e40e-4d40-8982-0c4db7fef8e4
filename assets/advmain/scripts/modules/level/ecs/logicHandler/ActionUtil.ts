import { IActionContext } from '../combat/core/ActionContext';
import NodeComponent from '../components/NodeComponent';
import { UtilBase } from '../cores/UtilBase';

/**行动意图处理器 */
export class ActionUtil extends UtilBase {
    /**获取行动意图中的目标位置 */
    public getTargetPosition(context: IActionContext) {
        if (context.targetPosition) {
            return context.targetPosition;
        }
        const targetEntityId = context.targetEntityId;
        if (!targetEntityId) return null;
        return this.world.utilCenter.boardUtil.getEntityRootPoint(targetEntityId);
    }
}
