const { glob } = require('glob');
const fs = require('fs');
const path = require('path');

/**
 * 加密 jsc（主要用于发布线上 jsc 时）
 * (1)：cocos2d-jsb.jsc 和 settings.jsc 不采用自定义加密，而其它的采用自定义加密
 * (2)：在原 cocos 的 基础上采用自定义加密
 */
(async () => {
    let url = process.argv[2];
    if (!url) {
        return;
    }

    const filenames = await glob(`**/*.jsc`, { nodir: true, cwd: url, ignore: ["frameworks/**"], absolute: true });

    let customEncoder = true;
    for (let i = 0; i < filenames.length; i++) {
        const filename = filenames[i];

        if (filename.indexOf("cocos2d-jsb") !== -1 ||
            filename.indexOf("settings") !== -1) {
            customEncoder = false;
        } else {
            customEncoder = true;
        }

        if (!customEncoder) {
            continue;
        }

        // 读取文件内容
        const sourceBuffers = fs.readFileSync(filename);

        // 使用自定义编码器
        const jycrypt = "jycrypt";
        const jycryptBuf = Buffer.from(jycrypt);

        const bufftext = Array.from(sourceBuffers);
        const bufflen = bufftext.length;

        for (let i = 0; i < bufflen; i++) {
            bufftext[i] = bufftext[i] ^ jycryptBuf[i % jycrypt.length];
        }

        const finalData = Buffer.concat([jycryptBuf, Buffer.from(bufftext)]);

        // 保存加密后的文件
        const noExtFilename = path.join(path.dirname(filename), path.basename(filename, path.extname(filename)));
        const encryptSaveUrl = noExtFilename + ".jsc";
        fs.writeFileSync(encryptSaveUrl, finalData);
        console.log(`加密文件：${filename}成功，保存地址：${encryptSaveUrl}`);
    }
})();