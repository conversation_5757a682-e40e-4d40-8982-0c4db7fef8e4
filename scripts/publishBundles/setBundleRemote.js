const fs = require('fs');
const path = require('path');

/**
 * 修改meta文件的isRemoteBundle.android为true
 * @param {string} filePath - meta文件路径
 */
function updateMetaFile(filePath) {
    try {
        if (!fs.existsSync(filePath)) {
            console.log(`文件不存在: ${filePath}`);
            return;
        }

        // 读取文件内容
        const content = fs.readFileSync(filePath, 'utf8');
        const metaData = JSON.parse(content);

        // 检查是否包含isRemoteBundle字段
        if (metaData.isRemoteBundle !== undefined) {
            // 如果isRemoteBundle存在但没有android属性，创建它
            if (metaData.isRemoteBundle.android === undefined) {
                metaData.isRemoteBundle.android = false;
                console.log(`为 ${filePath} 创建isRemoteBundle.android字段，初始值为false`);
            }

            const oldValue = metaData.isRemoteBundle.android;
            metaData.isRemoteBundle.android = true;

            // 写回文件
            fs.writeFileSync(filePath, JSON.stringify(metaData, null, 2));
            console.log(`已更新 ${filePath}: isRemoteBundle.android ${oldValue} -> true`);
        } else {
            // 如果isRemoteBundle字段不存在，创建完整的结构
            metaData.isRemoteBundle = {
                android: true
            };

            // 写回文件
            fs.writeFileSync(filePath, JSON.stringify(metaData, null, 2));
            console.log(`为 ${filePath} 创建isRemoteBundle字段并设置android为true`);
        }
    } catch (error) {
        console.error(`处理文件失败 ${filePath}:`, error.message);
    }
}

/**
 * 处理assets目录下的指定bundle meta文件
 */
function updateAssetsBundles() {
    console.log('=== 开始处理assets目录下的bundle meta文件 ===');

    const assetsDir = path.join(process.cwd(), 'assets');
    const bundleNames = ['advmain', 'advres'];

    bundleNames.forEach(bundleName => {
        const metaFile = path.join(assetsDir, `${bundleName}.meta`);
        updateMetaFile(metaFile);
    });
}

/**
 * 处理bundles目录下的所有bundle meta文件
 */
function updateBundlesMetas() {
    console.log('=== 开始处理bundles目录下的bundle meta文件 ===');

    const bundlesDir = path.join(process.cwd(), 'assets', 'bundles');

    if (!fs.existsSync(bundlesDir)) {
        console.log('bundles目录不存在，跳过处理');
        return;
    }

    // 读取bundles目录下的所有文件
    const files = fs.readdirSync(bundlesDir);

    files.forEach(file => {
        if (file.endsWith('.meta')) {
            const metaFile = path.join(bundlesDir, file);
            updateMetaFile(metaFile);
        }
    });
}

/**
 * 主函数
 */
function main() {
    console.log('开始设置bundle为remote模式...');

    // 处理assets目录下的bundle
    updateAssetsBundles();

    // 处理bundles目录下的bundle
    updateBundlesMetas();

    console.log('bundle remote模式设置完成！');
}

// 运行主函数
if (require.main === module) {
    main();
}

module.exports = { updateMetaFile, updateAssetsBundles, updateBundlesMetas }; 