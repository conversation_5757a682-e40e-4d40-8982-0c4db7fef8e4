import { ECSEvent, GameEventMap } from '../../../GameEvent';
import { ECSViewBase, IECSViewConfig } from '../../ECSViewBase';

const new_clear_effectFrame = {
    dragonAssetUrl: 'dragonbones/multiEliminationEffect/gameplay_moreEliminate_tilemapFrame_ske',
    dragonAtlasAssetUrl: 'dragonbones/multiEliminationEffect/gameplay_moreEliminate_tilemapFrame_tex',
};

const new_clear_effectBg = {
    dragonAssetUrl: 'dragonbones/multiEliminationEffect/gameplay_moreEliminate_bgPar_ske',
    dragonAtlasAssetUrl: 'dragonbones/multiEliminationEffect/gameplay_moreEliminate_bgPar_tex',
};

/** 盘面网格 图 */
export const BoardEffectUrlList = [
    'textures/level/multiEliminationEffect/tilemap_blue',
    'textures/level/multiEliminationEffect/tilemap_cyan',
    'textures/level/multiEliminationEffect/tilemap_blue',
    'textures/level/multiEliminationEffect/tilemap_blue',
    'textures/level/multiEliminationEffect/tilemap_purple',
];

/** 背景材质 */
export const BgMaterialList = [
    'materials/multiEliminationEffect/cyan_effect',
    'materials/multiEliminationEffect/cyan_effect',
    'materials/multiEliminationEffect/blue_3_effect',
    'materials/multiEliminationEffect/blue_4_effect',
    'materials/multiEliminationEffect/purple_effect',
];
const BoardEffect = 'prefabs/multiEliminationEffect/BoardEffect';
const BGEffect = 'prefabs/multiEliminationEffect/BGEffect';
/**
 * 同时消除多行，强化带来的爽感
 */
export class MultiEliminationSpecialEffectECSView extends ECSViewBase {
    protected initView() {
        console.log('多消特效视图初始化');
    }
    protected addEvents() {
        // 可以添加事件监听
        this.logicWorld.eventBus.on(ECSEvent.GameEvent.ELIMINATION, this.OnEliminationEvent, this);
    }
    protected removeEvents() {
        // 移除事件监听
        this.logicWorld.eventBus.off(ECSEvent.GameEvent.ELIMINATION, this.OnEliminationEvent, this);
        console.log('移除多消特效事件监听');
    }
    protected disposeView() {}

    private OnEliminationEvent(data: GameEventMap[ECSEvent.GameEvent.ELIMINATION]) {
        // 处理消除事件
        console.log('处理消除事件');
        const eliminateCount = data.clearCol.size + data.clearRow.size;
        const level = this.getEliminateEffectLevel(eliminateCount);
        this.playAnimation(level);
    }

    protected async playAnimation(level: number): Promise<void> {
        if (level < 2) {
            return;
        }
        this.playFrameEffect(level);
        this.playBgEffect(level);
        this.playScreenShake(level);
        this.playBoardEffect(level);
    }

    /** 播放盘面网格动效 */
    async playFrameEffect(level: number) {
        const node = await this.playDragonBonesAnimation(
            this.node.parent,
            'armatureName',
            `in_${level}`,
            new_clear_effectFrame.dragonAssetUrl,
            new_clear_effectFrame.dragonAtlasAssetUrl,
        );
        if (node) {
            node.scale = 2;
        }
    }

    /** 播放背景特效
     * 1，修改背景颜色
     * 2，播放背景动画
     */
    private async playBgEffect(level: number): Promise<void> {
        const boardScene = this.node.parent.parent.parent.parent.parent.parent.getChildByName('bg');
        if (!boardScene) {
            return;
        }
        if (level >= 4) {
            this.playDragonBonesAnimation(
                boardScene,
                'armatureName',
                `in_${level}`,
                new_clear_effectBg.dragonAssetUrl,
                new_clear_effectBg.dragonAtlasAssetUrl,
            );
        }
        const prefab = (await this.renderWorld.loadRes('', BGEffect, cc.Prefab)) as cc.Prefab;
        if (!prefab) {
            return;
        }
        const node = cc.instantiate(prefab);
        boardScene.addChild(node);
        const material = (await this.renderWorld.loadRes('', BgMaterialList[level - 1], cc.Material)) as cc.Material;
        node.getComponent(cc.Sprite).setMaterial(0, material);
        node.opacity = 0;
        cc.tween(node)
            .to(0.2, { opacity: 255 }, { easing: 'backOut' })
            .to(2.33, { opacity: 0 }, { easing: 'backIn' })
            .call(() => {
                node.destroy();
            })
            .start();
    }

    /** 屏幕抖动效果 */
    playScreenShake(level: number): void {
        /**
         * 振幅
         * 1，level2 -> 4。
         * 2，level3 -> 4。
         * 3，level4 -> 6。
         * 4，level5 以上 -> 8
         */
        const amplitudeList = [0, 0, 4, 4, 6, 8];
        const amplitude = amplitudeList[level] ?? 8;
        //持续时间
        const duration = 0.33;
        this.renderWorld.fullScreenShakeManager.startWithAmplitude(duration, amplitude, duration / 5);
    }
    /** 播放盘面特效 */
    private async playBoardEffect(level): Promise<void> {
        const boardComp = this.node.parent.parent.getChildByName('Bg');
        if (!boardComp) {
            return;
        }
        const prefab = (await this.renderWorld.loadRes('', BoardEffect, cc.Prefab)) as cc.Prefab;
        if (!prefab) {
            return;
        }
        const node = cc.instantiate(prefab);
        boardComp.addChild(node);
        const sprite = node.getComponent(cc.Sprite);
        const spriteFrame = (await this.renderWorld.loadRes('', BoardEffectUrlList[level - 1], cc.SpriteFrame)) as cc.SpriteFrame;
        if (sprite && spriteFrame) {
            sprite.spriteFrame = spriteFrame;
        }
        cc.tween(node)
            .to(0.2, { opacity: 255 }, { easing: 'backOut' })
            .to(2, { opacity: 0 }, { easing: 'backIn' })
            .call(() => {
                node.destroy();
            })
            .start();
    }

    /**
     * 获取消除效果强度等级
     * 1. <= 2 返回 1
     * 2. 3~5 均减1 的等级
     * 4. >= 6  是5
     * return 1、2、3、4、5
     */
    private getEliminateEffectLevel(eliminateCount: number): number {
        return eliminateCount <= 2 ? 1 : Math.min(eliminateCount - 1, 5);
    }

    /**播放龙骨动画 */
    protected async playDragonBonesAnimation(
        parentNode: cc.Node,
        armatureName: string,
        animationName: string,
        dragonAssetUrl: string,
        dragonAtlasAssetUrl: string,
    ): Promise<cc.Node> {
        const node: cc.Node = new cc.Node();
        const dragonBonesArmatureDisplay: dragonBones.ArmatureDisplay = node.addComponent(dragonBones.ArmatureDisplay);
        parentNode.addChild(node);
        dragonBonesArmatureDisplay.enableBatch = true;
        dragonBonesArmatureDisplay.updateAnimationCache(animationName);
        dragonBonesArmatureDisplay.setAnimationCacheMode(dragonBones.ArmatureDisplay.AnimationCacheMode.SHARED_CACHE);
        dragonBonesArmatureDisplay.addEventListener(dragonBones.EventObject.COMPLETE, () => {
            node.destroy();
        });
        const res = await this.renderWorld.loadRes('', dragonAssetUrl, dragonBones.DragonBonesAsset);
        const res2 = await this.renderWorld.loadRes('', dragonAtlasAssetUrl, dragonBones.DragonBonesAtlasAsset);
        dragonBonesArmatureDisplay.dragonAsset = res as dragonBones.DragonBonesAsset;
        dragonBonesArmatureDisplay.dragonAtlasAsset = res2 as dragonBones.DragonBonesAtlasAsset;
        dragonBonesArmatureDisplay.armatureName = armatureName;
        dragonBonesArmatureDisplay.playAnimation(animationName, 1);
        return node;
    }
}
