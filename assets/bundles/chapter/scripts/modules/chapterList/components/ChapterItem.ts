import { Loader } from "../../../../../../advmain/scripts/base/Loader";
import { SubGameBridge } from "../../../../../../advmain/scripts/base/SubGameBridge";
import { ChapterUrls } from "../../../../../../advmain/scripts/modules/chapterConfig/ChapterDefineds";

export interface IChapterItem {
    levelNum: number;
    color: number;
    scale: number;
    showColor: boolean;
    move: boolean;
    isThrough: boolean;
    isRemove: boolean;
}

const { ccclass, property } = cc._decorator;
/**
 * 关卡item ->马赛克
 */
@classId('ChapterItem')
@ccclass
export default class ChapterItem extends falcon.Component<Partial<IChapterItem>> {
    /**bg */
    @property(cc.Sprite)
    bg: cc.Sprite = null;

    /**颜色图 */
    @property(cc.Sprite)
    colorImg: cc.Sprite = null;

    /**高光图 */
    @property(cc.Node)
    light: cc.Node = null;

    shouldComponentUpdate(nextState: Readonly<Partial<IChapterItem>>): boolean {
        return (
            this.state.isRemove != nextState.isRemove ||
            this.state.isThrough != nextState.isThrough ||
            this.state.move != nextState.move ||
            this.state.levelNum != nextState.levelNum ||
            this.state.showColor != nextState.showColor
        );
    }

    async render() {
        if (this.state.isRemove) {
            this.removeAnimation();
        }

        this.bg.spriteFrame = await Loader.asyncLoadByBundle<cc.SpriteFrame>('chapter', ChapterUrls.commonBgPath, cc.SpriteFrame);

        if (this.state.showColor) {
            const { type, url } = this.colorInfo(this.state.color, { type: 'local', url: 'textures/chapterList/periods/block/' + this.state.color });
            if (type === 'local') {
                this.colorImg.spriteFrame = await Loader.asyncLoadByBundle<cc.SpriteFrame>('chapter', url, cc.SpriteFrame);
            } else {
                Loader.asyncLoadByBundle<cc.Texture2D>('advres', url, cc.Texture2D).then((texure2d: cc.Texture2D) => {
                    this.colorImg.spriteFrame = new cc.SpriteFrame();
                    this.colorImg.spriteFrame.setTexture(texure2d);
                });
            }
        }

        this.colorImg.node.scale = this.state.showColor ? 1 : 0;

        this.colorImg.node.active = this.state.showColor;

        if (this.state.move) {
            this.addMove();
        }
        if (this.state.isThrough) {
            this.addHeightMovie();
        }
    }

    colorInfo(color: number, info: { type: 'local' | 'remote'; url: string }): { type: 'local' | 'remote'; url: string } {
        return info;
    }

    /**
     * 方格移动
     */
    private addMove() {
        this.colorImg.node.active = true;
        this.colorImg.node.scale = 0.2;
        cc.tween(this.colorImg.node).to(0.1, { scale: 1.05 }).to(0.7, { scale: 1 }).start();
        this.light.opacity = 0;
        cc.tween(this.light).to(0.1, { opacity: 255 }).to(0.17, { opacity: 0 }).start();
    }

    removeAnimation() {
        cc.Tween.stopAllByTarget(this.colorImg.node);
        cc.Tween.stopAllByTarget(this.light);
    }
    /**
     * 添加高度的移动
     */
    addHeightMovie() {
        cc.tween(this.colorImg.node).to(0.13, { scale: 1.1, y: 10 }).to(0.3, { scale: 1, y: 0 }).start();
        this.light.opacity = 0;
        cc.tween(this.light).to(0.13, { opacity: 255 }).to(0.3, { opacity: 0 }).start();
    }

    /** */
    onClick(event: cc.Event.EventTouch, data: any) {}

    reset() {
        this.colorImg.spriteFrame = null;
    }
}
