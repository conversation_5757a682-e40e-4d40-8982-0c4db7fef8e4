import { CellComponent } from '../../../components/board/CellComponent';
import { ECSEvent } from '../../../GameEvent';
import { ECSViewBase, IECSViewConfig } from '../../ECSViewBase';

export default class CellECSView extends ECSViewBase<IECSViewConfig> {
    private lastColor: number;
    protected initView() {
        this.renderWorld
            .loadRes(
                this.config.prefabBundleName,
                `textures/level/boardBase/icon_1_${this.logicWorld.getComponent(this.logicEntityId, CellComponent).temp.color}`,
                cc.SpriteFrame,
            )
            .then((spf: cc.SpriteFrame) => {
                if (!cc.isValid(this.node)) return;

                let sp = this.node.getComponent(cc.Sprite) || this.node.addComponent(cc.Sprite);
                sp.spriteFrame = spf;
                this.lastColor = this.logicWorld.getComponent(this.logicEntityId, CellComponent).temp.color as unknown as number;
            });
    }
    protected addEvents() {
        this.logicWorld.eventBus.onEntityEvent(this.logicEntityId, ECSEvent.EntityEvent.HIGHLIGHT_CHANGED, this.onHighlightChanged, this);
    }
    protected removeEvents() {
        this.logicWorld.eventBus.offEntityEvent(this.logicEntityId, ECSEvent.EntityEvent.HIGHLIGHT_CHANGED, this.onHighlightChanged, this);
    }
    protected disposeView() {}
    onHighlightChanged(entityId: number, event: ECSEvent.EntityEvent, isHighlight: boolean) {
        const cellComp = this.logicWorld.getComponent(this.logicEntityId, CellComponent);
        if (!cellComp) return;
        const curColor = cellComp.temp.color;
        if (curColor !== this.lastColor) {
            this.lastColor = curColor;
            this.renderWorld.loadRes('', `textures/level/boardBase/icon_1_${curColor}`, cc.SpriteFrame).then((spf: cc.SpriteFrame) => {
                if (!cc.isValid(this.node)) return;
                const sp = this.node.getComponent(cc.Sprite);
                if (sp) sp.spriteFrame = spf;
            });
        }
    }
}
