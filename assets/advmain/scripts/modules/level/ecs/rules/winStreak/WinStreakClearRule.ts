import { ECSRuleBase, IECSRuleConfig } from '../core/ECSRuleBase';

export interface IWinStreakClearRuleConfig extends IECSRuleConfig {
    /**连胜字段key */
    winStreakCountKey: string;
}
export class WinStreakClearRule extends ECSRuleBase<IWinStreakClearRuleConfig> {
    canExecute(): boolean {
        if (
            this.config.winStreakCountKey === undefined ||
            this.config.winStreakCountKey === '' ||
            this.config.winStreakCountKey === null
        ) {
            return false;
        }
        return true;
    }
    execute(): void {
        const world = this.world;
        const cacheCenter = world.cacheCenter;
        cacheCenter.__winStreakData__[this.config.winStreakCountKey] = 0;
        console.log('WinStreakClearRule 连胜次数', cacheCenter.__winStreakData__[this.config.winStreakCountKey]);
    }
}