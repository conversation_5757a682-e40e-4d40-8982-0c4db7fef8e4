import { TargetType, CellColor } from '../../define/BoardDefine';
import { IPoint } from '../../define/EcsDefine';

export type BoardId = string;
/** 基础棋盘配置接口 */
export interface IBoardConfig {
    /** 棋盘ID */
    boardId: BoardId;
    /** 棋盘起始位置 */
    startPos: IPoint;
    /** 棋盘列数 */
    cols: number;
    /** 棋盘行数 */
    rows: number;
    /** 棋盘格子大小 */
    cellSize: number;
    /** 棋盘孔洞 */
    holes?: string[];
    /** 棋盘插槽 */
    slots?: { [key: string]: { [key: number]: { type: number; color: number } & { [k: string]: any } } };
    /** 是否可放置 */
    isCanPut?: boolean;
    /** 棋盘透明度 */
    opcacity?: number;
    /** 是否显示棋盘背层 */
    isShowBoardBg?: boolean;
}

export const BoardConfig: Record<string, IBoardConfig> = {
    /** 标准8x8棋盘 */
    classic: {
        boardId: 'classic',
        startPos: { x: 0, y: 0 },
        cols: 8,
        rows: 8,
        cellSize: 106,
        isCanPut: true,
    },

    /** 带目标块的8x8棋盘 */
    classic_with_target: {
        boardId: 'classic_with_target',
        startPos: { x: 0, y: 0 },
        cols: 8,
        rows: 8,
        cellSize: 106,
        slots: { '1_4': { 3: { type: TargetType.Normal, color: CellColor.Blue } } },
        isCanPut: true,
    },

    /** 宝石目标棋盘 */
    gem_target: {
        boardId: 'gem_target',
        startPos: { x: 0, y: 0 },
        cols: 8,
        rows: 8,
        cellSize: 106,
        slots: { '2_4': { 3: { type: TargetType.Gem_105, color: CellColor.Yellow } } },
        isCanPut: true,
    },

    /** 双屏上半部分 */
    dual_top: {
        boardId: 'dual_top',
        startPos: { x: -6, y: 252 },
        cols: 8,
        rows: 4,
        cellSize: 106,
        isCanPut: true,
    },

    /** 双屏下半部分 */
    dual_bottom: {
        boardId: 'dual_bottom',
        startPos: { x: -6, y: 228 },
        cols: 8,
        rows: 4,
        cellSize: 106,
        isCanPut: true,
    },

    // ===== 特殊形状棋盘 =====

    /** 大型20x20棋盘 */
    large_shape: {
        boardId: 'large_shape',
        startPos: { x: 0, y: 0 },
        cols: 20,
        rows: 20,
        cellSize: 106 * 0.08 * 5,
        isCanPut: true,
    },

    /** 镂空棋盘 */
    hollow: {
        boardId: 'hollow',
        startPos: { x: 0, y: 0 },
        cols: 8,
        rows: 8,
        cellSize: 106,
        holes: ['0_0', '0_1', '3_3', '3_4', '3_5', '4_3', '4_4', '4_5', '7_6', '7_7'],
        isCanPut: true,
    },

    // ===== 镜像棋盘配置 =====

    /** 镜像副棋盘 */
    mirror_sub: {
        boardId: 'mirror_sub',
        startPos: { x: -251, y: 599 },
        cols: 8,
        rows: 8,
        cellSize: 106 * 0.4,
        isCanPut: false,
        slots: {
            '1_4': { 3: { type: TargetType.Normal, color: CellColor.Blue } },
            '1_5': { 3: { type: TargetType.Normal, color: CellColor.Blue } },
            '2_4': { 3: { type: TargetType.Normal, color: CellColor.Blue } },
            '2_5': { 3: { type: TargetType.Normal, color: CellColor.Blue } },
        },
    },

    /** 镜像主棋盘 */
    mirror_main: {
        boardId: 'mirror_main',
        startPos: { x: 0, y: -40 },
        cols: 8,
        rows: 8,
        cellSize: 106,
        isCanPut: true,
    },

    // ===== 多层棋盘配置 =====

    /** 多层棋盘 - 底层（8x8） */
    multi_layer_0: {
        boardId: 'multi_layer_0',
        startPos: { x: 0, y: 0 },
        cols: 8,
        rows: 8,
        cellSize: 106,
        opcacity: 0,
        isShowBoardBg: false,
        slots: {
            '1_1': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '1_2': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '1_3': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '1_4': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '1_5': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '1_6': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '2_1': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '2_5': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '2_6': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '3_1': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '3_3': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '3_4': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '3_6': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '4_1': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '4_3': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '4_4': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '4_6': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '5_1': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '5_2': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '5_5': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '5_6': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '6_1': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '6_2': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '6_3': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '6_4': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '6_5': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '6_6': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
        },
    },

    /** 多层棋盘 - 第1层（7x7） */
    multi_layer_1: {
        boardId: 'multi_layer_1',
        startPos: { x: 0, y: 0 },
        cols: 7,
        rows: 7,
        cellSize: 106,
        opcacity: 0,
        isShowBoardBg: false,
        slots: {
            '0_0': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '0_1': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '0_2': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '0_4': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '0_5': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '0_6': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '1_0': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '1_6': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '2_0': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '2_6': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '3_0': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '3_6': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '4_0': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '4_6': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '5_0': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '5_6': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '6_0': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '6_1': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '6_2': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '6_4': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '6_5': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '6_6': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
        },
    },

    /** 多层棋盘 - 第2层（8x8） */
    multi_layer_2: {
        boardId: 'multi_layer_2',
        startPos: { x: 0, y: 0 },
        cols: 8,
        rows: 8,
        cellSize: 106,
        opcacity: 0,
        isShowBoardBg: false,
        slots: {
            '1_1': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '1_2': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '1_3': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '1_4': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '1_5': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '1_6': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '2_1': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '2_6': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '3_1': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '3_3': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '3_4': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '3_6': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '4_1': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '4_3': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '4_4': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '4_6': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '5_1': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '5_6': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '6_1': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '6_2': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '6_3': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '6_4': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '6_5': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '6_6': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
        },
    },

    /** 多层棋盘 - 第3层（7x7） */
    multi_layer_3: {
        boardId: 'multi_layer_3',
        startPos: { x: 0, y: 0 },
        cols: 7,
        rows: 7,
        cellSize: 106,
        opcacity: 0,
        isShowBoardBg: false,
        slots: {
            '0_1': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '0_2': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '0_4': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '0_5': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '6_1': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '6_2': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '6_4': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '6_5': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
        },
    },

    /** 多层棋盘 - 第4层（8x8） */
    multi_layer_4: {
        boardId: 'multi_layer_4',
        startPos: { x: 0, y: 0 },
        cols: 8,
        rows: 8,
        cellSize: 106,
        opcacity: 0,
        isShowBoardBg: false,
        slots: {
            '1_1': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '1_2': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '1_3': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '1_4': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '1_5': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '1_6': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '2_1': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '2_6': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '3_1': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '3_6': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '4_1': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '4_6': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '5_1': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '5_6': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '6_1': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '6_2': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '6_3': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '6_4': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '6_5': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
            '6_6': { 3: { type: TargetType.Normal, color: CellColor.Yellow } },
        },
    },

    /** 多层棋盘 - 顶层（8x8，可见） */
    multi_layer_5: {
        boardId: 'multi_layer_5',
        startPos: { x: 0, y: 0 },
        cols: 8,
        rows: 8,
        cellSize: 106,
        opcacity: 255,
        isShowBoardBg: true,
    },
};
