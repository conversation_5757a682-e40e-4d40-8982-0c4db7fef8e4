import { World } from "./World";
/**
 * 系统配置
 */
export interface ISystemConfig {
    /**系统id */
    readonly systemId: string;
    /**系统名称 */
    readonly systemName: string;
    /**系统是否关闭 */
    readonly close?: boolean;
    /**系统是否替换 */
    readonly replaceSystemId?: string;
}
/**
 * 系统基类
 */
export abstract class System<TConfig extends ISystemConfig = ISystemConfig> {
    public priority: number = 0;
    protected world: World
    public config: TConfig;
    constructor(world: World, config: TConfig) {
        this.world = world;
        this.config = config;
        this.init();
    }

    abstract init(): void;
    abstract dispose(): void;
    abstract update(dt: number): void;
}