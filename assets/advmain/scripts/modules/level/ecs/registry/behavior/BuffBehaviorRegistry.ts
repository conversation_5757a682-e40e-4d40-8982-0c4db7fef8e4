import { BuffBehaviorBase } from "../../behavior/buff/BuffBehaviorBase";
import { EliminatedBuffBehavior } from "../../behavior/buff/EliminatedBuffBehavior";

/**Buff行为配置表名 */
export const enum BuffBehaviorConst {
    /**消除buff */
    EliminatedBuffBehavior = 'EliminatedBuffBehavior',
}

/**Buff行为配置注册表 */
export const BuffBehaviorRegistry: Record<string, clzz<BuffBehaviorBase>> = {
    [BuffBehaviorConst.EliminatedBuffBehavior]: EliminatedBuffBehavior,
};
