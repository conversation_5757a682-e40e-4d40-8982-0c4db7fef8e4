//手写的配置放在这里，原则上不可引用其他脚本

import { IRCShape } from './EcsDefine';

/**块形状，以包围盒左上角为原点，从上到下从左到右  */
export const RcShape: { [k: number]: IRCShape } = {
    1: { shape: [{ r: 0, c: 0 }], width: 1, height: 1 },
    2: {
        shape: [
            { r: 0, c: 0 },
            { r: 1, c: 0 },
        ],
        width: 1,
        height: 2,
    },
    3: {
        shape: [
            { r: 0, c: 0 },
            { r: 0, c: 1 },
        ],
        width: 2,
        height: 1,
    },
    4: {
        shape: [
            { r: 0, c: 0 },
            { r: 1, c: 0 },
            { r: 2, c: 0 },
        ],
        width: 1,
        height: 3,
    },
    5: {
        shape: [
            { r: 0, c: 0 },
            { r: 0, c: 1 },
            { r: 0, c: 2 },
        ],
        width: 3,
        height: 1,
    },
    6: {
        shape: [
            { r: 0, c: 0 },
            { r: 0, c: 1 },
            { r: 1, c: 0 },
        ],
        width: 2,
        height: 2,
    },
    7: {
        shape: [
            { r: 0, c: 0 },
            { r: 1, c: 0 },
            { r: 2, c: 0 },
            { r: 3, c: 0 },
        ],
        width: 1,
        height: 4,
    },
    8: {
        shape: [
            { r: 0, c: 0 },
            { r: 1, c: 0 },
            { r: 1, c: 1 },
            { r: 1, c: 2 },
        ],
        width: 3,
        height: 2,
    },
    9: {
        shape: [
            { r: 0, c: 0 },
            { r: 0, c: 1 },
            { r: 1, c: 0 },
            { r: 1, c: 1 },
        ],
        width: 2,
        height: 2,
    },
    10: {
        shape: [
            { r: 0, c: 1 },
            { r: 1, c: 0 },
            { r: 1, c: 1 },
            { r: 1, c: 2 },
        ],
        width: 3,
        height: 2,
    },
    11: {
        shape: [
            { r: 0, c: 0 },
            { r: 0, c: 1 },
            { r: 0, c: 2 },
            { r: 0, c: 3 },
            { r: 0, c: 4 },
        ],
        width: 5,
        height: 1,
    },
    12: {
        shape: [
            { r: 0, c: 0 },
            { r: 0, c: 1 },
            { r: 0, c: 2 },
            { r: 1, c: 2 },
            { r: 2, c: 2 },
        ],
        width: 3,
        height: 3,
    },
    13: {
        shape: [
            { r: 0, c: 0 },
            { r: 0, c: 1 },
            { r: 0, c: 2 },
            { r: 1, c: 0 },
            { r: 1, c: 1 },
            { r: 1, c: 2 },
            { r: 2, c: 0 },
            { r: 2, c: 1 },
            { r: 2, c: 2 },
        ],
        width: 3,
        height: 3,
    },
    14: {
        shape: [
            { r: 0, c: 1 },
            { r: 0, c: 2 },
            { r: 1, c: 0 },
            { r: 1, c: 1 },
        ],
        width: 3,
        height: 2,
    },
    15: {
        shape: [
            { r: 0, c: 0 },
            { r: 0, c: 1 },
            { r: 1, c: 1 },
        ],
        width: 2,
        height: 2,
    },
    16: {
        shape: [
            { r: 0, c: 0 },
            { r: 1, c: 0 },
            { r: 1, c: 1 },
            { r: 2, c: 1 },
        ],
        width: 2,
        height: 3,
    },
    17: {
        shape: [
            { r: 0, c: 0 },
            { r: 0, c: 1 },
            { r: 0, c: 2 },
            { r: 0, c: 3 },
        ],
        width: 4,
        height: 1,
    },
    18: {
        shape: [
            { r: 0, c: 0 },
            { r: 0, c: 1 },
            { r: 1, c: 1 },
            { r: 1, c: 2 },
        ],
        width: 3,
        height: 2,
    },
    19: {
        shape: [
            { r: 0, c: 1 },
            { r: 1, c: 0 },
            { r: 1, c: 1 },
            { r: 2, c: 0 },
        ],
        width: 2,
        height: 3,
    },
    20: {
        shape: [
            { r: 0, c: 0 },
            { r: 1, c: 0 },
            { r: 1, c: 1 },
            { r: 2, c: 0 },
        ],
        width: 2,
        height: 3,
    },
    21: {
        shape: [
            { r: 0, c: 0 },
            { r: 0, c: 1 },
            { r: 0, c: 2 },
            { r: 1, c: 0 },
            { r: 2, c: 0 },
        ],
        width: 3,
        height: 3,
    },
    22: {
        shape: [
            { r: 0, c: 0 },
            { r: 1, c: 0 },
            { r: 2, c: 0 },
            { r: 3, c: 0 },
            { r: 4, c: 0 },
        ],
        width: 1,
        height: 5,
    },
    23: {
        shape: [
            { r: 0, c: 0 },
            { r: 1, c: 0 },
            { r: 2, c: 0 },
            { r: 2, c: 1 },
            { r: 2, c: 2 },
        ],
        width: 3,
        height: 3,
    },
    24: {
        shape: [
            { r: 0, c: 2 },
            { r: 1, c: 2 },
            { r: 2, c: 0 },
            { r: 2, c: 1 },
            { r: 2, c: 2 },
        ],
        width: 3,
        height: 3,
    },
    25: {
        shape: [
            { r: 0, c: 1 },
            { r: 1, c: 0 },
            { r: 1, c: 1 },
            { r: 2, c: 1 },
        ],
        width: 2,
        height: 3,
    },
    26: {
        shape: [
            { r: 0, c: 0 },
            { r: 0, c: 1 },
            { r: 0, c: 2 },
            { r: 1, c: 1 },
        ],
        width: 3,
        height: 2,
    },
    27: {
        shape: [
            { r: 0, c: 0 },
            { r: 1, c: 0 },
            { r: 1, c: 1 },
        ],
        width: 2,
        height: 2,
    },
    28: {
        shape: [
            { r: 0, c: 1 },
            { r: 1, c: 0 },
            { r: 1, c: 1 },
        ],
        width: 2,
        height: 2,
    },
    29: {
        shape: [
            { r: 0, c: 1 },
            { r: 1, c: 1 },
            { r: 2, c: 0 },
            { r: 2, c: 1 },
        ],
        width: 2,
        height: 3,
    },
    30: {
        shape: [
            { r: 0, c: 0 },
            { r: 0, c: 1 },
            { r: 0, c: 2 },
            { r: 1, c: 2 },
        ],
        width: 3,
        height: 2,
    },
    31: {
        shape: [
            { r: 0, c: 0 },
            { r: 0, c: 1 },
            { r: 1, c: 0 },
            { r: 2, c: 0 },
        ],
        width: 2,
        height: 3,
    },
    32: {
        shape: [
            { r: 0, c: 0 },
            { r: 0, c: 1 },
            { r: 1, c: 1 },
            { r: 2, c: 1 },
        ],
        width: 2,
        height: 3,
    },
    33: {
        shape: [
            { r: 0, c: 2 },
            { r: 1, c: 0 },
            { r: 1, c: 1 },
            { r: 1, c: 2 },
        ],
        width: 3,
        height: 2,
    },
    34: {
        shape: [
            { r: 0, c: 0 },
            { r: 0, c: 1 },
            { r: 0, c: 2 },
            { r: 1, c: 0 },
        ],
        width: 3,
        height: 2,
    },
    35: {
        shape: [
            { r: 0, c: 0 },
            { r: 0, c: 1 },
            { r: 0, c: 2 },
            { r: 1, c: 0 },
            { r: 1, c: 1 },
            { r: 1, c: 2 },
        ],
        width: 3,
        height: 2,
    },
    36: {
        shape: [
            { r: 0, c: 0 },
            { r: 0, c: 1 },
            { r: 1, c: 0 },
            { r: 1, c: 1 },
            { r: 2, c: 0 },
            { r: 2, c: 1 },
        ],
        width: 2,
        height: 3,
    },
    37: {
        shape: [
            { r: 0, c: 0 },
            { r: 1, c: 1 },
        ],
        width: 2,
        height: 2,
    },
    38: {
        shape: [
            { r: 0, c: 1 },
            { r: 1, c: 0 },
        ],
        width: 2,
        height: 2,
    },
    39: {
        shape: [
            { r: 0, c: 0 },
            { r: 1, c: 1 },
            { r: 2, c: 2 },
        ],
        width: 3,
        height: 3,
    },
    40: {
        shape: [
            { r: 0, c: 2 },
            { r: 1, c: 1 },
            { r: 2, c: 0 },
        ],
        width: 3,
        height: 3,
    },
    41: {
        shape: [
            { r: 0, c: 2 },
            { r: 1, c: 1 },
            { r: 2, c: 0 },
        ],
        width: 3,
        height: 3,
    },
    42: {
        shape: [
            { r: 0, c: 0 },
            { r: 1, c: 0 },
            { r: 2, c: 0 },
            { r: 2, c: 1 },
        ],
        width: 2,
        height: 3,
    },
};
