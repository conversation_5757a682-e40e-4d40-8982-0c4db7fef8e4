{"version": 3, "file": "falcon.js", "mappings": "wDACAA,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQE,gBAiBR,SAAyBC,QACT,IAARA,IAAkBA,EAAM,GAG5B,IAFA,IAAIC,EAAcC,KAAKC,MACnBC,EAAO,GACFC,EAAM,EAAGA,GAAOL,EAAKK,IAAO,CACjC,IAAIC,EAAO,IAAIJ,KACfI,EAAKC,QAAQN,EAAc,MAAsBI,GACjDD,EAAKI,KAAKF,EAAKG,cAAgB,IAAMH,EAAKI,WAAa,IAAMJ,EAAKK,UACtE,CACA,OAAOP,CACX,EA1BAP,EAAQe,aA+BR,WACI,IAAIC,EAAQ,IAAIX,KAChB,MAAO,GAAGY,OAAOD,EAAMJ,cAAe,KAAKK,OAAOD,EAAMH,WAAa,EAAG,KAAKI,OAAOD,EAAMF,UAC9F,EAjCAd,EAAQkB,YAmCR,SAAqBC,EAAOC,GACxB,IAAIC,EAAmB,IAAIhB,KAAKc,GAChCE,EAAiBC,SAAS,EAAG,EAAG,EAAG,GACnC,IAAIC,EAAmB,IAAIlB,KAAKe,GAChCG,EAAiBD,SAAS,EAAG,EAAG,EAAG,GACnC,IAAIE,EAAWC,KAAKC,IAAIL,EAAiBM,UAAYJ,EAAiBI,WAEtE,OADeF,KAAKG,KAAKJ,EAAW,MAExC,C,gBC9CA1B,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQ6B,iBAAc,EACtB7B,EAAQ8B,SA4BR,SAAkBC,GACd,IAAIC,EAASD,EAAOC,OAAQC,EAAeF,EAAOE,aAAcC,EAAMH,EAAOG,IAAKC,EAAWJ,EAAOI,SAChGC,EAAaC,EAASC,IAAIN,EAAOO,aAChCH,IACDA,EAAa,CAAC,EACdC,EAASG,IAAIR,EAAOO,YAAaH,IAIrCA,EADUH,EAAeC,GACPC,CACtB,EArCAnC,EAAQyC,MA+ER,WACI,OAAO,SAAUT,EAAQU,GACrB,IAAIC,EACAC,EAAQC,IAAWF,EAAK,CAAC,GAAMD,GAAeV,EAAOU,GAAcC,GAAKX,EAAQU,GACpF5C,OAAOC,eAAeiC,EAAQU,EAAa,CACvCJ,IAAK,WACD,OAAOM,EAAMF,EACjB,EACAF,IAAK,SAAUM,GACXF,EAAMF,GAAeI,CACzB,EACAC,YAAY,EACZC,cAAc,GAEtB,CACJ,EA7FAhD,EAAQiD,WAwGR,SAASA,EAAWC,EAAWC,EAAKC,EAAMC,GASlC,IAAIC,EALR,QAHa,IAATF,IAAmBA,EAAO,SACb,IAAbC,IAAuBA,EAAW,GAElCE,SACA,OAAK,EAAIC,EAAQC,UAAUN,GAEvBC,EAAKM,QAAUL,EACRF,EAEJ,IAAIQ,MAAMR,EAAK,CAClBb,IAAK,SAAUN,EAAQ4B,EAAKC,GACxB,IAAI5D,EAAQ6D,QAAQxB,IAAIN,EAAQ4B,EAAKC,GAErC,OAAI,EAAIL,EAAQC,UAAUxD,IAAUmD,EAAKM,OAAS,EAAIL,EAC3CJ,EAAWC,EAAWjD,EAAOmD,EAAKnC,OAAO8C,OAAOH,IAAOP,GAE3DpD,CACX,EACAuC,IAAK,SAAUR,EAAQ4B,EAAK3D,EAAO4D,GAE/B,GADe7B,EAAO4B,KACL3D,EAAO,CACpB+D,MAAMC,gBAAkB,GACxB,IAAIC,EAAQ,IAAIF,MAAM,IAEtB,GAAIE,EAAMC,MAAO,CACb,IACIC,EADQF,EAAMC,MAAME,MAAM,MACJC,QAAO,SAAUC,GACvC,OAAQA,EAAKC,SAAS,UAAYD,EAAKC,SAAS,YAAcD,EAAKC,SAAS,+BAAiCD,EAAKC,SAAS,gBAC/H,IACA,GAAIJ,EAAcV,OAAS,EAAG,CAC1B,IAAIe,EAAUL,EAAcM,KAAK,MAC7BD,IAAYnB,IACZA,EAAgBmB,EACZlB,UACAoB,QAAQC,IAAI,iBAAkB,mEAAoE,GAAG3D,OAAOiC,EAAW,MAAMjC,OAAOwD,IAGhJ,CACJ,CACJ,CAEA,IAAI3B,GAAW,EAAIU,EAAQC,UAAUxD,IAAUmD,EAAKM,OAAS,EAAIL,EAC3DJ,EAAWC,EAAWjD,EAAOmD,EAAKnC,OAAO8C,OAAOH,IAAOP,GACvDpD,EACN,OAAO6D,QAAQtB,IAAIR,EAAQ4B,EAAKd,EAAUe,EAC9C,IAxCOV,CA2CnB,EAxJA,IAKItB,EALA2B,EAAU,EAAQ,MAClBnB,EAAW,IAAIwC,IAyCnB,SAAShC,EAAUb,EAAQ8C,EAAapC,GACpC,KAAK,EAAIc,EAAQC,UAAUzB,GACvB,OAAOA,EACX,IAAI+C,EAAU,CACVzC,IAAK,SAAUN,EAAQ4B,EAAKC,GACxB,IAAImB,EAASlB,QAAQxB,IAAIN,EAAQ4B,EAAKC,GACtC,OAAO,EAAIL,EAAQC,UAAUuB,GAAUnC,EAAUmC,EAAQF,EAAapC,GAAesC,CACzF,EACAxC,IAAK,SAAUR,EAAQ4B,EAAK3D,EAAO4D,GAC/B,IAAIoB,EAAWjD,EAAO4B,GAClBoB,EAASlB,QAAQtB,IAAIR,EAAQ4B,EAAK3D,EAAO4D,GAC7C,GAAIoB,IAAahF,EAAO,CACpB,IAAIiF,EAAe7C,EAASC,IAAIwC,EAAYvC,aACxC2C,GACApF,OAAOqF,KAAKD,GAAcE,SAAQ,SAAUxB,GACpCA,EAAIY,SAAS9B,IACTwC,EAAatB,IACbsB,EAAatB,GAAKA,EAAKqB,EAAUhF,EAG7C,KAEA,EAAIuD,EAAQC,UAAUxD,IACtB4C,EAAU5C,EAAO6E,EAAapC,EACtC,CACA,OAAOsC,CACX,GAEJ,OAAO,IAAIrB,MAAM3B,EAAQ+C,EAC7B,EAjEA,SAAWlD,GACPA,EAAsE,yDAAI,2DAC1EA,EAAiC,oBAAI,sBACrCA,EAAgF,mEAAI,oEACvF,CAJD,CAIGA,IAAgB7B,EAAQ6B,YAAcA,EAAc,CAAC,G,gBCfxD/B,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQqF,iBAAc,EACtB,IAAIC,EAAc,EAAQ,MACtBC,EAA6B,WAC7B,SAASA,IACLC,KAAKC,QAAU,CAAC,CACpB,CAsGA,OA7FAF,EAAYG,UAAUC,kCAAoC,SAAU5D,GAChE,IAAI6D,EAAQJ,KACZ,GAAKzD,EAAL,CAGA,IAAI8D,EAAY9D,EAAO8D,UAAWC,EAAa/D,EAAO+D,WAAYC,EAAQhE,EAAOgE,MAAOC,EAAkBjE,EAAOiE,gBAAiBC,EAASlE,EAAOkE,OAClJ,KAAIF,GAAS,IAGRE,EAGL,OAAO,IAAIC,SAAQ,SAAUC,EAAGC,GAC5B,IAAIC,EAAa,SAAUC,EAAKC,GAC5B,GAAID,EACAF,EAAEE,OADN,CAIA,IAAIE,EAASZ,EAAMH,QACfgB,EAAaD,EAAOX,GACnBY,IACDD,EAAOX,GAAa,GACpBY,EAAaD,EAAOX,IAExB,IAAIa,EAAWD,EAAW/C,OAE1B,GAAIgD,EAAWX,EACX,IAAK,IAAIvF,EAAMuF,EAAOvF,EAAMkG,EAAUlG,KAC9BmG,EAAYF,EAAWjG,IACjBoG,KAAKC,QAAS,EAKhC,IADA,IAAIC,EAAQ,GACHC,EAAM,EAAGA,EAAMhB,EAAOgB,IAAO,CAClC,IAAIJ,EACJ,GADIA,EAAYF,EAAWM,GAQvBJ,EAAUC,KAAKC,QAAS,EACpBF,EAAUC,KAAKX,SAAWA,IAC1BU,EAAUC,KAAKX,OAASA,GAE5Ba,EAAMC,GAAOJ,MAXD,CACZ,IAAIC,EAAOI,GAAGC,YAAYV,GAC1BE,EAAWM,GAAOH,EAAKM,aAAalB,GACpCC,EAAOkB,SAASP,GAChBE,EAAMC,GAAON,EAAWM,EAC5B,CAQJ,CACAZ,EAAEW,EAjCF,CAkCJ,EACIhB,EACAR,EAAY8B,UAAUC,aAAavB,EAAYD,EAAWmB,GAAGM,OAAQjB,GAGrEf,EAAY8B,UAAUG,KAAK1B,EAAWmB,GAAGM,OAAQjB,EAEzD,GAtDA,CAuDJ,EAIAd,EAAYG,UAAU8B,eAAiB,WACnC,IAAK,IAAI3B,KAAaL,KAAKC,QAAS,CAChC,IAAIgB,EAAajB,KAAKC,QAAQI,GAC1BY,IAEAA,EAAWrB,SAAQ,SAAUqC,GACrBA,GAAQA,EAAKb,MAAQI,GAAGU,QAAQD,EAAKb,QACrCa,EAAKb,KAAKe,mBACVF,EAAKb,KAAKgB,UAElB,IAEAnB,EAAW/C,OAAS,EAE5B,CAEA8B,KAAKC,QAAU,CAAC,EACZlC,UACAoB,QAAQC,IAAI,wBAEpB,EAIAW,EAAYG,UAAUmC,MAAQ,WAC1BrC,KAAKgC,iBACDjE,UACAoB,QAAQC,IAAI,kCAEpB,EACOW,CACX,CA1GgC,GA2GhCvF,EAAQqF,YAAc,IAAIE,C,oBC9G1B,IAAIuC,EAAUtC,MAAQA,KAAKsC,QAAW,SAAUC,EAAGC,GAC/C,IAAIC,EAAsB,mBAAXC,QAAyBH,EAAEG,OAAOC,UACjD,IAAKF,EAAG,OAAOF,EACf,IAAmBK,EAAYhC,EAA3BiC,EAAIJ,EAAEK,KAAKP,GAAOQ,EAAK,GAC3B,IACI,WAAc,IAANP,GAAgBA,KAAM,MAAQI,EAAIC,EAAEG,QAAQC,MAAMF,EAAG5H,KAAKyH,EAAEnI,MACxE,CACA,MAAOiE,GAASkC,EAAI,CAAElC,MAAOA,EAAS,CACtC,QACI,IACQkE,IAAMA,EAAEK,OAASR,EAAII,EAAU,SAAIJ,EAAEK,KAAKD,EAClD,CACA,QAAU,GAAIjC,EAAG,MAAMA,EAAElC,KAAO,CACpC,CACA,OAAOqE,CACX,EACAzI,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQ0I,uBAKR,SAAgCC,EAAMC,GAClC,GAAID,EAAKjF,SAAWkF,EAAKlF,OACrB,OAAO,EAKX,IAHA,IAAImF,EAAaF,EAAKG,QAAQC,OAC1BC,EAAaJ,EAAKE,QAAQC,OAErBvI,EAAM,EAAGA,EAAMqI,EAAWnF,OAAQlD,IACvC,GAAIqI,EAAWrI,KAASwI,EAAWxI,GAC/B,OAAO,EAGf,OAAO,CACX,EAjBAR,EAAQiJ,aAmBR,SAAsBC,GAIlB,IAHA,IAAIvG,EAEAwG,EAAgBD,EAAMJ,QACjB/B,EAAMoC,EAAczF,OAAS,EAAGqD,EAAM,EAAGA,IAAO,CAErD,IAAIqC,EAAI3H,KAAK4H,MAAM5H,KAAK6H,UAAYvC,EAAM,IAE1CpE,EAAKmF,EAAO,CAACqB,EAAcC,GAAID,EAAcpC,IAAO,GAAIoC,EAAcpC,GAAOpE,EAAG,GAAIwG,EAAcC,GAAKzG,EAAG,EAC9G,CACA,OAAOwG,CACX,EA7BAnJ,EAAQuJ,YA+BR,SAAqBZ,EAAMC,GACvB,GAAID,EAAKjF,SAAWkF,EAAKlF,OACrB,OAAO,EAEX,IAAK,IAAI8F,EAAM,EAAGA,EAAMb,EAAKjF,OAAQ8F,IACjC,GAAIb,EAAKa,KAASZ,EAAKY,GACnB,OAAO,EAGf,OAAO,CACX,EAxCAxJ,EAAQyJ,gBA0CR,SAAyBC,EAAKC,EAAWC,GAKrC,OAJIF,EAAIhG,QAAUiG,GACdD,EAAIG,QAERH,EAAI/I,KAAKiJ,GACFF,CACX,C,sBCpEA,IACQI,EADJC,EAAavE,MAAQA,KAAKuE,YACtBD,EAAgB,SAAUE,EAAGC,GAI7B,OAHAH,EAAgBhK,OAAOoK,gBAClB,CAAEC,UAAW,cAAgBC,OAAS,SAAUJ,EAAGC,GAAKD,EAAEG,UAAYF,CAAG,GAC1E,SAAUD,EAAGC,GAAK,IAAK,IAAII,KAAKJ,EAAOnK,OAAO4F,UAAU4E,eAAehC,KAAK2B,EAAGI,KAAIL,EAAEK,GAAKJ,EAAEI,GAAI,EAC7FP,EAAcE,EAAGC,EAC5B,EACO,SAAUD,EAAGC,GAChB,GAAiB,mBAANA,GAA0B,OAANA,EAC3B,MAAM,IAAIM,UAAU,uBAAyBxG,OAAOkG,GAAK,iCAE7D,SAASO,IAAOhF,KAAKjD,YAAcyH,CAAG,CADtCF,EAAcE,EAAGC,GAEjBD,EAAEtE,UAAkB,OAANuE,EAAanK,OAAO2K,OAAOR,IAAMO,EAAG9E,UAAYuE,EAAEvE,UAAW,IAAI8E,EACnF,GAEJ1K,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQ0K,eAAY,EACpB,IACIA,EAA2B,SAAUC,GAErC,SAASD,IACL,OAAkB,OAAXC,GAAmBA,EAAOC,MAAMpF,KAAMqF,YAAcrF,IAC/D,CA0CA,OA7CAuE,EAAUW,EAAWC,GASrBD,EAAUI,QAAU,SAAUC,GAC1B,IAAIC,EAAYC,OAAOC,KAAKH,GAAKI,SAAS,UAC1C,OAAO3F,KAAK4F,KAAKJ,EACrB,EAMAN,EAAUW,QAAU,SAAUN,GAC1B,IAAIO,EAAa9F,KAAK4F,KAAKL,GAC3B,OAAOE,OAAOC,KAAKI,EAAY,UAAUH,SAAS,QACtD,EACAT,EAAUU,KAAO,SAAUL,GAOvB,IALA,IAAIQ,EAAWR,EAAIS,QAAQ,OAAQ,KAC/BC,EAAcF,EAAS7H,OACvBgI,EAAYlG,KAAKmG,UAAUjI,OAE3BkI,EAAY,GACPpL,EAAM,EAAGA,EAAMiL,EAAajL,IAAO,CAExC,IAAIqL,EAAMN,EAAS/K,GAEfsL,EAAQtG,KAAKmG,UAAUI,QAAQF,IACpB,IAAXC,IAIAF,GAFQpG,KAAKmG,UAAUD,EAAYI,EAAQ,GAInD,CAEA,OAAOF,CACX,EACAlB,EAAUiB,UAAY,oEACfjB,CACX,CA/C8B,CADd,EAAQ,MAgDZsB,SACZhM,EAAQ0K,UAAYA,C,gBC5DpB5K,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQiM,qBAAkB,EAC1B,IAAI3G,EAAc,EAAQ,MAC1B,GAAI/B,SAAU,CACV,IAAI2I,EAA0BC,YAAYC,gBAAgB1G,UAAU2G,cACpEF,YAAYC,gBAAgB1G,UAAU2G,cAAgB,SAAUC,EAAUC,GACtE,IAAI5J,EAAI6J,EAAIC,EAAIC,EAAIC,EAChBC,EAAgBpH,KAAKoH,cACrBC,EAA+C,QAA3BlK,EAAK6C,KAAKsH,mBAAgC,IAAPnK,OAAgB,EAASA,EAAGkK,iBACvF,GAAIA,EAAkB,CAClB,IAAIE,EAAuH,QAA/FN,EAAiC,QAA3BD,EAAKhH,KAAKsH,mBAAgC,IAAPN,OAAgB,EAASA,EAAGO,4BAAyC,IAAPN,EAAgBA,EAAKO,KAAKC,MAAMJ,GAC/JK,EAAoI,QAApHR,EAAKK,aAAmE,EAASA,EAAqBI,gBAA6B,IAAPT,OAAgB,EAASA,EAAGU,MAAK,SAAUC,GAAK,OAAOA,EAAEC,OAASV,CAAe,IAC7NM,GACkD,QAAjCP,EAAKO,EAAaK,iBAA8B,IAAPZ,OAAgB,EAASA,EAAGS,MAAK,SAAUC,GAAK,OAAOA,EAAEC,OAAShB,CAAU,MAElI3H,QAAQT,MAAM,yBAA2EjD,OAAOuE,KAAKoH,cAAe,sBAAqC3L,OAAOqL,IAIpK3H,QAAQT,MAAM,yBAA2EjD,OAAOuE,KAAKoH,cAAe,qBAAqG3L,OAAOqL,GAExO,CACAJ,EAAwBtB,MAAMpF,KAAM,CAAC8G,EAAUC,GACnD,CACJ,CACA,IAAIiB,EAAiC,WACjC,SAASA,IACLhI,KAAKiI,YAAc,CAAC,CACxB,CA0EA,OAhEAD,EAAgB9H,UAAUgI,KAAO,SAAUC,EAAYC,EAAcC,EAAetB,EAAWuB,GAC3F,IAAIlI,EAAQJ,KACZ,GAAKsI,GAGAF,GAGAC,EAAL,MAGkBE,IAAdxB,IACAA,EAAY,GAECuB,EAAOhI,WAAxB,IAAoCkI,EAAiBF,EAAOE,eAAgBC,EAAsBH,EAAOG,oBACzG,GAD+IH,EAAOI,oBAC/HH,IAAnBC,GAAmD,KAAnBA,QAGRD,IAAxBE,GAA6D,KAAxBA,QAGnBF,IAAlBF,GAAiD,KAAlBA,QAGdE,IAAjBH,GAA+C,KAAjBA,EAAlC,CASA,IAAIhH,EAAO,IAAII,GAAGmH,KACdC,EAA6BxH,EAAKyH,aAAalC,YAAYC,iBAmB/D,OAlBAuB,EAAWxG,SAASP,GAEpBwH,EAA2BE,aAAc,EACzCF,EAA2BG,qBAAqBV,GAChDO,EAA2BI,sBAAsBrC,YAAYC,gBAAgBqC,mBAAmBC,cAC5FN,IACIN,EAAOa,cAAgBb,EAAOc,iBAC9BR,EAA2BS,iBAAiB1C,YAAY2C,YAAYC,SAAUjB,EAAOa,aAAcb,EAAOc,iBAE1Gd,EAAOI,eACPc,YAAW,WACPpJ,EAAMqJ,MAAMnB,EAAQF,EAAcC,EAAetB,EAAW6B,EAChE,GAAG,IAGH5I,KAAKyJ,MAAMnB,EAAQF,EAAcC,EAAetB,EAAW6B,IAG5DxH,CA3BP,CAhBA,CA4CJ,EACA4G,EAAgB9H,UAAUuJ,MAAQ,SAAUnB,EAAQF,EAAcC,EAAetB,EAAW6B,GACxF,IAAItI,EAAagI,EAAOhI,WAAYkI,EAAiBF,EAAOE,eAAgBC,EAAsBH,EAAOG,oBAAsCH,EAAOI,eAClJpI,EACAR,EAAY8B,UAAU8H,0BAA0BpJ,EAAYsI,EAA4BJ,EAAgBC,EAAqBL,EAAcC,EAAetB,GAG1JjH,EAAY8B,UAAU+H,kBAAkBf,EAA4BJ,EAAgBC,EAAqBL,EAAcC,EAAetB,EAE9I,EACOiB,CACX,CA9EoC,GA+EpCxN,EAAQiM,gBAAkB,IAAIuB,C,oBC9G9B,IAAI4B,EAAa5J,MAAQA,KAAK4J,WAAc,SAAUC,EAASC,EAAYC,EAAGC,GAE1E,OAAO,IAAKD,IAAMA,EAAIrJ,WAAU,SAAUuJ,EAASC,GAC/C,SAASC,EAAU1P,GAAS,IAAM2P,EAAKJ,EAAUhH,KAAKvI,GAAS,CAAE,MAAOmG,GAAKsJ,EAAOtJ,EAAI,CAAE,CAC1F,SAASyJ,EAAS5P,GAAS,IAAM2P,EAAKJ,EAAiB,MAAEvP,GAAS,CAAE,MAAOmG,GAAKsJ,EAAOtJ,EAAI,CAAE,CAC7F,SAASwJ,EAAK5K,GAJlB,IAAe/E,EAIa+E,EAAOyD,KAAOgH,EAAQzK,EAAO/E,QAJ1CA,EAIyD+E,EAAO/E,MAJhDA,aAAiBsP,EAAItP,EAAQ,IAAIsP,GAAE,SAAUE,GAAWA,EAAQxP,EAAQ,KAIjB6P,KAAKH,EAAWE,EAAW,CAC7GD,GAAMJ,EAAYA,EAAU5E,MAAMyE,EAASC,GAAc,KAAK9G,OAClE,GACJ,EACIuH,EAAevK,MAAQA,KAAKuK,aAAgB,SAAUV,EAASW,GAC/D,IAAsGC,EAAGC,EAAGC,EAAxGC,EAAI,CAAEC,MAAO,EAAGC,KAAM,WAAa,GAAW,EAAPH,EAAE,GAAQ,MAAMA,EAAE,GAAI,OAAOA,EAAE,EAAI,EAAGI,KAAM,GAAIC,IAAK,IAAeC,EAAI3Q,OAAO2K,QAA4B,mBAAbiG,SAA0BA,SAAW5Q,QAAQ4F,WACtL,OAAO+K,EAAEjI,KAAOmI,EAAK,GAAIF,EAAS,MAAIE,EAAK,GAAIF,EAAU,OAAIE,EAAK,GAAsB,mBAAXzI,SAA0BuI,EAAEvI,OAAOC,UAAY,WAAa,OAAO3C,IAAM,GAAIiL,EAC1J,SAASE,EAAK3I,GAAK,OAAO,SAAUqF,GAAK,OACzC,SAAcuD,GACV,GAAIX,EAAG,MAAM,IAAI1F,UAAU,mCAC3B,KAAOkG,IAAMA,EAAI,EAAGG,EAAG,KAAOR,EAAI,IAAKA,OACnC,GAAIH,EAAI,EAAGC,IAAMC,EAAY,EAARS,EAAG,GAASV,EAAU,OAAIU,EAAG,GAAKV,EAAS,SAAOC,EAAID,EAAU,SAAMC,EAAE7H,KAAK4H,GAAI,GAAKA,EAAE1H,SAAW2H,EAAIA,EAAE7H,KAAK4H,EAAGU,EAAG,KAAKnI,KAAM,OAAO0H,EAE3J,OADID,EAAI,EAAGC,IAAGS,EAAK,CAAS,EAARA,EAAG,GAAQT,EAAElQ,QACzB2Q,EAAG,IACP,KAAK,EAAG,KAAK,EAAGT,EAAIS,EAAI,MACxB,KAAK,EAAc,OAAXR,EAAEC,QAAgB,CAAEpQ,MAAO2Q,EAAG,GAAInI,MAAM,GAChD,KAAK,EAAG2H,EAAEC,QAASH,EAAIU,EAAG,GAAIA,EAAK,CAAC,GAAI,SACxC,KAAK,EAAGA,EAAKR,EAAEI,IAAIK,MAAOT,EAAEG,KAAKM,MAAO,SACxC,QACI,MAAkBV,GAAZA,EAAIC,EAAEG,MAAY7M,OAAS,GAAKyM,EAAEA,EAAEzM,OAAS,KAAkB,IAAVkN,EAAG,IAAsB,IAAVA,EAAG,IAAW,CAAER,EAAI,EAAG,QAAU,CAC3G,GAAc,IAAVQ,EAAG,MAAcT,GAAMS,EAAG,GAAKT,EAAE,IAAMS,EAAG,GAAKT,EAAE,IAAM,CAAEC,EAAEC,MAAQO,EAAG,GAAI,KAAO,CACrF,GAAc,IAAVA,EAAG,IAAYR,EAAEC,MAAQF,EAAE,GAAI,CAAEC,EAAEC,MAAQF,EAAE,GAAIA,EAAIS,EAAI,KAAO,CACpE,GAAIT,GAAKC,EAAEC,MAAQF,EAAE,GAAI,CAAEC,EAAEC,MAAQF,EAAE,GAAIC,EAAEI,IAAI7P,KAAKiQ,GAAK,KAAO,CAC9DT,EAAE,IAAIC,EAAEI,IAAIK,MAChBT,EAAEG,KAAKM,MAAO,SAEtBD,EAAKZ,EAAK1H,KAAK+G,EAASe,EAC5B,CAAE,MAAOhK,GAAKwK,EAAK,CAAC,EAAGxK,GAAI8J,EAAI,CAAG,CAAE,QAAUD,EAAIE,EAAI,CAAG,CACzD,GAAY,EAARS,EAAG,GAAQ,MAAMA,EAAG,GAAI,MAAO,CAAE3Q,MAAO2Q,EAAG,GAAKA,EAAG,QAAK,EAAQnI,MAAM,EAC9E,CAtBgDmH,CAAK,CAAC5H,EAAGqF,GAAK,CAAG,CAuBrE,EACAvN,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQ8Q,QAMR,SAAiB9O,EAAQ+O,EAAaC,GAClC,IAAIC,EAAiBD,EAAW/Q,MA0BhC,OAzBA+Q,EAAW/Q,MAAQ,WAEf,IADA,IAAIiR,EAAO,GACFC,EAAK,EAAGA,EAAKtG,UAAUnH,OAAQyN,IACpCD,EAAKC,GAAMtG,UAAUsG,GAEzB,OAAO/B,EAAU5J,UAAM,OAAQ,GAAQ,WACnC,IAAI4L,EAAOpM,EAAQqM,EACnB,OAAOtB,EAAYvK,MAAM,SAAU7C,GAC/B,OAAQA,EAAG0N,OACP,KAAK,EACD,OAAK9M,UACL6N,EAAQE,YAAYhR,MACb,CAAC,EAAa2Q,EAAerG,MAAMpF,KAAM0L,KAF1B,CAAC,EAAa,GAGxC,KAAK,EAMD,OALAlM,EAASrC,EAAG2N,OACZe,EAASC,YAAYhR,MACjBiD,UACAoB,QAAQC,IAAI,GAAG3D,OAAO8P,EAAa,qBAAqB9P,OAAOoQ,EAASD,EAAO,kBAE5E,CAAC,EAAcpM,GAC1B,KAAK,EAAG,MAAO,CAAC,EAAciM,EAAerG,MAAMpF,KAAM0L,IAEjE,GACJ,GACJ,EACOF,CACX,C,cCvEAlR,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQuR,UAQR,SAAmBC,EAAKC,GACpB,OAAOhQ,KAAK4H,MAAM5H,KAAK6H,UAAYmI,EAAMD,IAAQA,CACrD,EATAxR,EAAQ0R,YAgBR,SAAqBF,EAAKC,GACtB,OAAOhQ,KAAK6H,UAAYmI,EAAMD,GAAOA,CACzC,C,uBCpBA,IAAIG,EAAcnM,MAAQA,KAAKmM,YAAe,SAAUC,EAAY5P,EAAQ4B,EAAKiO,GAC7E,IAA2H7H,EAAvH7D,EAAI0E,UAAUnH,OAAQ0E,EAAIjC,EAAI,EAAInE,EAAkB,OAAT6P,EAAgBA,EAAO/R,OAAOgS,yBAAyB9P,EAAQ4B,GAAOiO,EACrH,GAAuB,iBAAZ/N,SAAoD,mBAArBA,QAAQiO,SAAyB3J,EAAItE,QAAQiO,SAASH,EAAY5P,EAAQ4B,EAAKiO,QACpH,IAAK,IAAIxJ,EAAIuJ,EAAWlO,OAAS,EAAG2E,GAAK,EAAGA,KAAS2B,EAAI4H,EAAWvJ,MAAID,GAAKjC,EAAI,EAAI6D,EAAE5B,GAAKjC,EAAI,EAAI6D,EAAEhI,EAAQ4B,EAAKwE,GAAK4B,EAAEhI,EAAQ4B,KAASwE,GAChJ,OAAOjC,EAAI,GAAKiC,GAAKtI,OAAOC,eAAeiC,EAAQ4B,EAAKwE,GAAIA,CAChE,EACAtI,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQgS,aAAU,EAClB,IAAIC,EAAqB,EAAQ,MAI7BC,EAAyB,WACzB,SAASA,IACT,CAwKA,OAvKApS,OAAOC,eAAemS,EAAQxM,UAAW,SAAU,CAE/CpD,IAAK,WACD,OAAsB,SAAd6P,WAAsC,cAAdA,YAA8BC,aAAaC,UAC/E,EACAtP,YAAY,EACZC,cAAc,IAElBlD,OAAOC,eAAemS,EAAQxM,UAAW,aAAc,CAEnDpD,IAAK,WACD,MAAO,kBACX,EACAS,YAAY,EACZC,cAAc,IAElBlD,OAAOC,eAAemS,EAAQxM,UAAW,qBAAsB,CAI3DpD,IAAK,WACD,OAAOgQ,2BACX,EACAvP,YAAY,EACZC,cAAc,IAElBlD,OAAOC,eAAemS,EAAQxM,UAAW,eAAgB,CAKrDpD,IAAK,WACD,MAAkB,SAAd6P,WAAsC,cAAdA,UACjBG,4BAA8B,aAG9BA,4BAA8B,cAAcrR,OAAOsR,eAAgB,KAAKtR,OAAOkR,UAE9F,EACApP,YAAY,EACZC,cAAc,IAElBlD,OAAOC,eAAemS,EAAQxM,UAAW,OAAQ,CAI7CpD,IAAK,WACD,MAAO,CACHkQ,KAAM,CAEFC,YAAa,8CAEjBC,KAAM,CAEFD,YAAa,0CAGzB,EACA1P,YAAY,EACZC,cAAc,IAGlBlD,OAAOC,eAAemS,EAAQxM,UAAW,aAAc,CAInDpD,IAAK,WACD,OAAOkD,KAAKmN,KAAKnN,KAAKoN,OAC1B,EACA7P,YAAY,EACZC,cAAc,IAElBlD,OAAOC,eAAemS,EAAQxM,UAAW,KAAM,CAI3CpD,IAAK,WACD,IAAIuQ,EACJ,GAAIC,OACA,OAAQ9L,GAAG+L,IAAIF,IACX,KAAK7L,GAAG+L,IAAIC,WACRH,EAAK,UACL,MACJ,KAAK7L,GAAG+L,IAAIE,OACRJ,EAAK,MACL,MACJ,KAAK7L,GAAG+L,IAAIG,OACRL,EAAK,KACL,MACJ,QACIA,EAAK,cAOTA,EAFAM,UAAUC,UAAUrH,QAAQ,YAAc,GAC1CoH,UAAUC,UAAUrH,QAAQ,UAAY,EACnC,UAEAoH,UAAUC,UAAUrH,QAAQ,WAAa,EACzC,MAEAoH,UAAUC,UAAUrH,QAAQ,kBAAoB,EAChD,KAGA,SAGb,OAAO8G,CACX,EACA9P,YAAY,EACZC,cAAc,IAElBlD,OAAOC,eAAemS,EAAQxM,UAAW,YAAa,CAIlDpD,IAAK,WACD,IAAIK,EAAI6J,EAAIC,EAAIC,EAAIC,EAAI0G,EAAIC,EAAIC,EAAIC,EAChCC,EAAU,QACd,GAAIX,OACAW,EAAUzM,GAAG+L,IAAIW,cAEhB,CACD,IAAIC,EAAIR,UAAUC,UACdQ,EAAYD,EAAE5H,QAAQ,YAAc,GAAK4H,EAAE5H,QAAQ,UAAY,EAC/D8H,IAAUF,EAAEG,MAAM,iCAClBF,IACAH,EAA8N,QAAnN/G,EAAuK,QAAjKD,EAAiG,QAA3FD,EAA+C,QAAzC7J,EAAKwQ,UAAUC,UAAU/O,MAAM,YAAyB,IAAP1B,OAAgB,EAASA,EAAG,UAAuB,IAAP6J,OAAgB,EAASA,EAAGsH,MAAM,wBAAqC,IAAPrH,OAAgB,EAASA,EAAG,UAAuB,IAAPC,EAAgBA,EAAK,SAE3PmH,IACAJ,EAAuS,QAA5RD,EAAiO,QAA3ND,EAA8K,QAAvKD,EAAiG,QAA3FD,EAA+C,QAAzC1G,EAAKwG,UAAUC,UAAU/O,MAAM,YAAyB,IAAPsI,OAAgB,EAASA,EAAG,UAAuB,IAAP0G,OAAgB,EAASA,EAAGS,MAAM,8BAA2C,IAAPR,OAAgB,EAASA,EAAG,UAAwB,IAAPC,OAAgB,EAASA,EAAG/H,QAAQ,IAAK,YAAyB,IAAPgI,EAAgBA,EAAK,QAE5U,CACA,OAAOC,CACX,EACA1Q,YAAY,EACZC,cAAc,IAElBlD,OAAOC,eAAemS,EAAQxM,UAAW,SAAU,CAI/CpD,IAAK,WACD,OAAIkD,KAAKuO,OACE,OAGA,MAEf,EACAhR,YAAY,EACZC,cAAc,IAElB2O,EAAW,CACPM,EAAmB+B,SACpB9B,EAAQxM,UAAW,OAAQ,MAC9BiM,EAAW,CACPM,EAAmB+B,SACpB9B,EAAQxM,UAAW,aAAc,MACpCiM,EAAW,CACPM,EAAmB+B,SACpB9B,EAAQxM,UAAW,KAAM,MAC5BiM,EAAW,CACPM,EAAmB+B,SACpB9B,EAAQxM,UAAW,YAAa,MAC5BwM,CACX,CA3K4B,GA4K5BlS,EAAQgS,QAAU,IAAIE,C,eCxLtBpS,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQiU,SAYR,SAAkBC,GAEd,OAAIA,EAASxQ,OAAS,GAEXwQ,EAASC,KAAI,SAAUC,GAAO,OAAOA,EAAItL,OAAS,IAIlDoL,EAASC,KAAI,SAAUC,GAAO,OAAOhK,MAAMc,KAAKkJ,EAAM,GAErE,EArBApU,EAAQqU,kBAyBR,SAA2BH,GACvB,OAAOA,EAASC,KAAI,SAAUC,GAAO,OAAOhK,MAAMc,KAAKkJ,EAAM,GACjE,EA1BApU,EAAQsU,cA8BR,SAAuBJ,GACnB,OAAOA,EAASC,KAAI,SAAUC,GAAO,OAAOA,EAAItL,OAAS,GAC7D,EA/BA9I,EAAQuU,aAmCR,SAAsBL,GAGlB,IAFA,IAAIlP,EAAS,GACTwP,EAAMN,EAASxQ,OACVlD,EAAM,EAAGA,EAAMgU,EAAKhU,IAAO,CAIhC,IAHA,IAAI4T,EAAMF,EAAS1T,GACfiU,EAASL,EAAI1Q,OACbgR,EAAS,IAAItK,MAAMqK,GACdrL,EAAI,EAAGA,EAAIqL,EAAQrL,IACxBsL,EAAOtL,GAAKgL,EAAIhL,GAEpBpE,EAAOxE,GAAOkU,CAClB,CACA,OAAO1P,CACX,EA/CAhF,EAAQ2U,cAmDR,SAAuBT,EAAUU,GAG7B,IAFA,IAAI5P,EAAS,GACTwP,EAAMN,EAASxQ,OACVqD,EAAM,EAAGA,EAAMyN,EAAKzN,IAAO,CAIhC,IAHA,IAAI8N,EAASX,EAASnN,GAClB+N,EAAkBF,GAAaC,EAAOnR,OACtCgR,EAAS,IAAItK,MAAM0K,GACd1L,EAAI,EAAGA,EAAI0L,EAAiB1L,IACjCsL,EAAOtL,GAAKyL,EAAOzL,GAEvBpE,EAAO+B,GAAO2N,CAClB,CACA,OAAO1P,CACX,C,eCrEAlF,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQ+U,iBAAc,EACtB,IAAIA,EAA6B,WAC7B,SAASA,IACLvP,KAAKwP,UAAY,IACrB,CAgBA,OAfAlV,OAAOC,eAAegV,EAAYrP,UAAW,WAAY,CACrDpD,IAAK,WACD,OAAOkD,KAAKwP,SAChB,EACAjS,YAAY,EACZC,cAAc,IAOlB+R,EAAYrP,UAAUuP,SAAW,WAC7B,OAAOzP,KAAkB,WAC7B,EACOuP,CACX,CApBgC,GAqBhC/U,EAAQ+U,YAAcA,C,eCpBtBjV,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQkV,aAAelV,EAAQmV,aAAenV,EAAQoV,YAAcpV,EAAQqV,wBAAqB,EACjGrV,EAAQsV,eAAiBA,EACzBtV,EAAQuV,iBA0DR,WACQC,GAAgBxO,GAAGU,QAAQ8N,KAC3BA,EAAa7N,mBACb6N,EAAa5N,WAEjB4N,EAAe,KACf7Q,QAAQC,IAAI,6BAChB,EAhEA5E,EAAQyV,eAAiBA,EACzBzV,EAAQ0V,UAAYA,EAEpB1V,EAAQqV,mBAAqB,CACzB,CAAEM,KAAM,SAAUrI,KAAM,cAAesI,OAAQ,IAGnD,IAAIJ,EAAe,KAKnB,SAASF,IAML,OAJKE,GAAiBxO,GAAGU,QAAQ8N,KAC7B7Q,QAAQC,IAAI,kCACZ4Q,EAAeK,KAEZL,CACX,CAsBA,SAASK,IACL,IAAIjP,EAAO,IAAII,GAAGmH,KAGlB,GAFAvH,EAAK0G,KAAO,cAERtN,EAAQkV,aAAaY,WAAY,CACjC,IAAIhI,EAAS9N,EAAQqV,mBAAmBjI,MAAK,SAAUjH,GAAK,MAAkB,WAAXA,EAAEwP,IAAmB,IACpF7H,IACA2H,EAAe7O,EAAMkH,EAAQ9N,EAAQkV,aAAaY,YAClD9V,EAAQkV,aAAaa,aAAa,SAAUnP,GAEpD,CACA,OAAOA,CACX,CAkBA,SAAS6O,EAAe7O,EAAMkH,EAAQkI,GAClCpP,EAAK0G,KAAOQ,EAAOR,KACnB1G,EAAKgP,OAAS9H,EAAO8H,YACE7H,IAAnBD,EAAOmI,UACPrP,EAAKqP,QAAUnI,EAAOmI,SAEtBnI,EAAOoI,SACPtP,EAAKC,QAAS,GAElBmP,EAAM7O,SAASP,GACf8O,EAAU9O,EACd,CA3DA5G,EAAQoV,YAAc,IAAIzR,MAAM,CAAC,EAAG,CAChCrB,IAAK,SAAUN,EAAQmU,GACnB,IAAIC,EAAQd,IACZ,MAA2B,mBAAhBc,EAAMD,GACNC,EAAMD,GAAME,KAAKD,GAErBA,EAAMD,EACjB,EACA3T,IAAK,SAAUR,EAAQmU,EAAMlW,GAGzB,OAFYqV,IACNa,GAAQlW,GACP,CACX,IAmDJ,IAAIkV,EAA8B,WAC9B,SAASA,IACL3P,KAAK8Q,SAAW,IAAIzR,IACpBW,KAAKwQ,MAAQ,IACjB,CA8HA,OA1HAb,EAAazP,UAAU6Q,KAAO,WAC1B,IAAI3Q,EAAQJ,KACZA,KAAKwQ,MAAQhP,GAAGwP,OAAOC,SAAS7P,KAChCpB,KAAKkR,QAEL1W,EAAQqV,mBAAmBjQ,SAAQ,SAAU0I,GACrB,WAAhBA,EAAO6H,OAEPH,EAAeK,IACfjQ,EAAM0Q,SAAS9T,IAAIsL,EAAO6H,KAAMH,GAExC,GACJ,EACA1V,OAAOC,eAAeoV,EAAazP,UAAW,aAAc,CACxDpD,IAAK,WAID,OAHK0E,GAAGU,QAAQlC,KAAKwQ,SACjBxQ,KAAKwQ,MAAQhP,GAAGwP,OAAOC,SAAS7P,MAE7BpB,KAAKwQ,KAChB,EACAjT,YAAY,EACZC,cAAc,IAKlBmS,EAAazP,UAAUgR,MAAQ,WAC3BlR,KAAK8Q,SAASlR,SAAQ,SAAUwB,GAC5BA,EAAKe,mBACLf,EAAKgB,SACT,IACApC,KAAK8Q,SAASI,QAEdlB,EAAe,IACnB,EAKAL,EAAazP,UAAUiR,SAAW,SAAU7I,GACxC,IAAKtI,KAAKwQ,MACN,MAAM,IAAIhS,MAAM,gCAGpBwB,KAAKoR,YAAY9I,EAAO6H,MACxB,IAAI/O,EAAO,IAAII,GAAGmH,KAGlB,OAFAsH,EAAe7O,EAAMkH,EAAQtI,KAAKwQ,OAClCxQ,KAAK8Q,SAAS9T,IAAIsL,EAAO6H,KAAM/O,GACxBA,CACX,EAMAuO,EAAazP,UAAUqQ,aAAe,SAAUJ,EAAM/O,GAClD,IAAKpB,KAAKwQ,MACN,MAAM,IAAIhS,MAAM,gCAGpBwB,KAAKoR,YAAYjB,GAEjBnQ,KAAK8Q,SAAS9T,IAAImT,EAAM/O,EAC5B,EAKAuO,EAAazP,UAAUkR,YAAc,SAAUjB,GAC3C,IAAIS,EAAQ5Q,KAAK8Q,SAAShU,IAAIqT,GAC9B,QAAIS,IACAA,EAAMzO,mBACNyO,EAAMxO,UACNpC,KAAK8Q,SAASO,OAAOlB,IACd,EAGf,EAKAR,EAAazP,UAAUoR,SAAW,SAAUnB,GACxC,OAAOnQ,KAAK8Q,SAAShU,IAAIqT,EAC7B,EAKAR,EAAazP,UAAUqR,UAAY,SAAUpB,GACzC,IAAIS,EAAQ5Q,KAAK8Q,SAAShU,IAAIqT,GAC1BS,IACAA,EAAMvP,QAAS,EAEvB,EAKAsO,EAAazP,UAAUsR,UAAY,SAAUrB,GACzC,IAAIS,EAAQ5Q,KAAK8Q,SAAShU,IAAIqT,GAC1BS,IACAA,EAAMvP,QAAS,EAEvB,EAMAsO,EAAazP,UAAUuR,gBAAkB,SAAUtB,EAAMM,GACrD,IAAIG,EAAQ5Q,KAAK8Q,SAAShU,IAAIqT,GAC1BS,IACAA,EAAMH,QAAUA,EAExB,EAIAd,EAAazP,UAAUwR,cAAgB,WACnC,OAAO9M,MAAMc,KAAK1F,KAAK8Q,SAASnR,OACpC,EACOgQ,CACX,CAnIiC,GA2IjC,SAASO,EAAU9O,GACfA,EAAKuQ,MAAQ,IACbvQ,EAAKwQ,OAAS,OACdxQ,EAAKyQ,QAAUzQ,EAAK0Q,QAAU,EAC9B,IAAIC,EAAS3Q,EAAKyH,aAAarH,GAAGwQ,QAClCD,EAAOE,YAAa,EACpBF,EAAOG,aAAc,EACrBH,EAAOI,cAAe,EACtBJ,EAAOK,eAAgB,EACvBL,EAAOM,IAAM,EACbN,EAAOO,OAAS,EAChBP,EAAOQ,KAAO,EACdR,EAAOS,MAAQ,CACnB,CApBAhY,EAAQmV,aAAeA,EAEvBnV,EAAQkV,aAAe,IAAIC,C,iBCnO3BrV,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQiY,UAAYjY,EAAQkY,eAAY,EACxC,IAMIA,EANA5S,EAAc,EAAQ,MACtB9B,EAAU,EAAQ,MAClB2U,EAAY,EAAQ,OAKxB,SAAWD,GAEPA,EAAUA,EAAiB,MAAI,GAAK,QAEpCA,EAAUA,EAAkB,OAAI,GAAK,QACxC,CALD,CAKGA,IAAclY,EAAQkY,UAAYA,EAAY,CAAC,IAIlD,IAAIE,EAA2B,WAC3B,SAASA,IACT,CAwEA,OAnEAA,EAAU1S,UAAUgI,KAAO,SAAU3L,GACjC,IAAI6D,EAAQJ,KACZ,GAAKzD,EAAL,CAMA,IAAIgJ,EAAMhJ,EAAOgJ,IAAKsN,EAAStW,EAAOsW,OAAQ1C,EAAO5T,EAAO4T,KAAM7P,EAAa/D,EAAO+D,WACtF,GAAKiF,EAAL,EAGI,EAAIvH,EAAQ8U,aAAa3C,KACzBA,EAAOuC,EAAUK,QAErB,IAAIC,EAAcL,EAAUM,QAAQC,QAAQ,eAAe,GACvDC,EAAYR,EAAUM,QAAQC,QAAQ,aAAa,GAEnD/C,IAASuC,EAAUK,SAAWC,GAC3B7C,IAASuC,EAAUU,QAAUD,SAGrB5K,IAAXsK,IACAA,EAAS,GAERvS,EAMDR,EAAY8B,UAAUC,aAAavB,EAAYiF,EAAK/D,GAAG6R,WAAW,SAAUvS,EAAKwS,GAC7ElT,EAAMqJ,MAAM0G,EAAM0C,EAAQ/R,EAAKwS,EACnC,IAPAxT,EAAY8B,UAAUG,KAAKwD,EAAK/D,GAAG6R,WAAW,SAAUvS,EAAKwS,GACzDlT,EAAMqJ,MAAM0G,EAAM0C,EAAQ/R,EAAKwS,EACnC,IAjBJ,CAJA,MAJQvV,UACAoB,QAAQT,MAAM,kBA+B1B,EACAkU,EAAU1S,UAAUuJ,MAAQ,SAAU0G,EAAM0C,EAAQ/R,EAAKwS,GACjDxS,IAGAqP,GAAQuC,EAAUK,OAClBvR,GAAG+R,YAAYrL,KAAKoL,GAAM,EAAOT,GAGjCrR,GAAG+R,YAAYC,UAAUF,GAAM,GAEvC,EAEAV,EAAU1S,UAAUuT,KAAO,SAAUlX,GACvBA,EAAOgJ,IAAchJ,EAAOsW,OAAtC,IAA8C1C,EAAO5T,EAAO4T,MACxD,EAAInS,EAAQ8U,aAAa3C,KACzBA,EAAOuC,EAAUK,QAErB,IAAIC,EAAcL,EAAUM,QAAQC,QAAQ,eAAe,GACvDC,EAAYR,EAAUM,QAAQC,QAAQ,aAAa,GAEnD/C,IAASuC,EAAUK,SAAWC,GAC3B7C,IAASuC,EAAUU,QAAUD,IAGhChD,GAAQuC,EAAUK,OAClBvR,GAAG+R,YAAYG,iBAGflS,GAAG+R,YAAYI,YAEvB,EACOf,CACX,CA3E8B,GA4E9BpY,EAAQiY,UAAY,IAAIG,C,iBC9FxBtY,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQoZ,aAAepZ,EAAQqZ,0BAA4BrZ,EAAQsZ,mBAAqBtZ,EAAQuZ,mBAAgB,EAChHvZ,EAAQwZ,YA+BR,SAAqBC,GACjBC,EAAcD,CAClB,EAhCAzZ,EAAQ2Z,kBAkCR,SAA2BF,GACvBG,EAAoBH,CACxB,EAnCAzZ,EAAQ6Z,MA+CR,SAAeC,EAAiBjI,GAE5B,YADa,IAATA,IAAmBA,EAAO,IACvB,SAAU7P,EAAQ+X,EAAY/I,GAC5BhR,EAAQoZ,aAAaY,IAAIhY,IAC1BhC,EAAQoZ,aAAa5W,IAAIR,EAAQ,CAAC,GAEtC,IAAIiY,EAAoBja,EAAQoZ,aAAa9W,IAAIN,GAC5CiY,EAAkBF,KACnBE,EAAkBF,GAAc,CAAC,QAKIhM,IAArCiD,EAA+B,qBAC/BA,EAA+B,mBAAIA,EAAW/Q,OAKlD,IAAIia,EAAmBD,EAAkBF,GACzCG,EAAiBC,aAAc,EAC/BD,EAAiBE,YAAcC,EAC/BH,EAAiB1O,SAAU,EAC3B0O,EAAiBH,WAAaA,EAC9BG,EAAiBI,OAAStJ,EAA+B,mBAKzD,IAAIuJ,EAAkBvJ,EAAW/Q,MACjCia,EAA6B,WAAIK,EAMjCvJ,EAAW/Q,MAAQ,WAIf,IAHA,IACI0C,EADAiD,EAAQJ,KAER0L,EAAO,GACFC,EAAK,EAAGA,EAAKtG,UAAUnH,OAAQyN,IACpCD,EAAKC,GAAMtG,UAAUsG,GAOzB,GALI5N,WACKmW,GACD/U,QAAQ6V,KAAK,QAA4BvZ,OAAO6Y,EAAiB,iDAGpEW,OAA2B,mBAC5B,OAAOF,EAAgB3P,MAAMpF,KAAM0L,GAGvC,IAAIwJ,EAAiBC,EAAkBC,gBAAgBC,mBAAmBf,GAItEgB,EAAYC,EAAyBjB,GACzC,IAAKgB,EAcD,OAbIvX,WAEKqW,EAIGc,GACIhB,GACA/U,QAAQT,MAAM,MAAgBjD,OAAO6Y,EAAiB,qDAL9DnV,QAAQ6V,KAAK,QAA4BvZ,OAAO6Y,EAAiB,gDAUlES,EAAgB3P,MAAMpF,KAAM0L,GAEnCqJ,IAAoBL,EAA6B,aAQjDA,EAAiB1O,SAAU,EAE3B0O,EAAiBC,aAAc,EAE/BD,EAAiBzZ,KAAO,CAAC,EAEzByZ,EAAiBhJ,KAAO,GAExBgJ,EAAiBE,YAAcC,GAGnCH,EAAiBlY,OAASwD,KAErB0U,EAAiBc,YAClBd,EAAiBc,UAAYC,aAAazV,KAAKjD,cAEnD,IAAI2Y,EAAQR,aAAuD,EAASA,EAAeS,MAC3F,GAAID,EAAO,CACP,IAAIE,EAAWtb,OAAOqF,KAAK+V,GAAO,GAE7BG,OAAOD,GAIRF,EAAMrU,QAAS,EAHfqU,EAAQA,EAAMI,GAMbR,EAAmB,UACpBA,EAAe,IAAIJ,EAAea,GAClCT,EAAkB,OAAII,EACtBJ,EAAmB,SAAI,EACvBA,EAAoB,YAQxB,IAAIU,EAAYV,EAAsB,WACtC,GAAIU,EACA,IAAK,IAAIhb,EAAM,EAAGA,EAAMgb,EAAU9X,OAAQlD,IAAO,CAC7C,IAAIib,EAAWD,EAAUhb,GACrBkb,EAAoBf,EAAkBC,gBAAgBC,mBAAmBY,EAASE,WAClFD,IACAD,EAAiB,OAAIC,EAAkBP,MAE/C,CAER,CAQA,IAAIS,EAAuBC,EAAe/B,GACtCgC,GAA2B,EAC/B,GAAIF,EACA,IAAK,IAAI7U,EAAM,EAAGA,EAAM6U,EAAqBlY,OAAQqD,IAAO,CACxD,IAAIgV,EAA2G,QAA1FpZ,EAAKgY,EAAkBC,gBAAgBC,mBAAmBe,EAAqB7U,WAA0B,IAAPpE,OAAgB,EAASA,EAAGwY,MACnJ,GAAIY,GAAuD,IAAtCjc,OAAOqF,KAAK4W,GAAerY,OAAc,CAC1DoY,GAA2B,EAC3B,KACJ,CACJ,CA2CJ,IAtCIhB,EAAUjU,QAAUiV,KACF5B,EAAiBC,cAE/BD,EAAiBhJ,KAAOA,EAIxBgJ,EAAiB8B,eAAiB,WAC9B,IAAIrZ,EAC+B,QAAlCA,EAAKuX,EAAiBI,cAA2B,IAAP3X,GAAyBA,EAAGiI,MAAMhF,EAAOsL,EACxF,EAIA1L,KAAuB,iBAAI0U,EAI3B+B,EAAsBnB,EAAW,aAIjCA,EAAUoB,SAAShC,GAIf3W,UACA4Y,EAAoBC,KAAKtB,EAAUS,IAKvCU,EAAsBnB,EAAW,aAMrCP,IAAoBL,EAAiBI,OAAQ,CAU7C,IAAIF,OAAc,EACbF,EAAiB1O,UAClB4O,EAAcG,EAAgB3P,MAAMpF,KAAM0L,IAK9C,IAAImL,EAA8BnC,EAAiBE,YACnD,OAAIiC,IAAgChC,EACzBgC,EAIAjC,CAEf,CAEI,OAAOG,EAAgB3P,MAAMpF,KAAM0L,EAE3C,CACJ,CACJ,EA3QAlR,EAAQ+a,yBAA2BA,EACnC/a,EAAQsc,gBA+TR,SAAyBC,GACrB,OAAKA,EAEEA,EAAkBC,OAAO,GAAGC,cAAgBF,EAAkBzT,MAAM,GAAK,QADrE,EAEf,EAlUA9I,EAAQ0c,IAwVR,WACI,OAAO,SAAU1a,GACb,IAAI2a,EAAgB3a,EAChB4a,EAAiB3B,aAAa0B,GAC7B3c,EAAQuZ,cAAcS,IAAI2C,IAC3B3c,EAAQuZ,cAAc/W,IAAIR,EAAQ4a,EAE1C,CACJ,EA/VA5c,EAAQ6c,cAuWR,SAAuBC,GACnB,OAAO,SAAUva,GACb,GAAKua,EAAL,CAEA,IAAIC,EAAoB9B,aAAa1Y,GACjCwa,IACAlB,EAAekB,GAAqBD,EAH9B,CAKd,CACJ,EA/WA,IAAIE,EAAW,EAAQ,MACnBC,EAAU,EAAQ,MAClBtC,EAAoB,EAAQ,MAChC,EAAQ,MAER,IAAIuC,EAAY,IAAIrY,IAEpB7E,EAAQuZ,cAAgB,IAAI1U,IAE5B,IAAIgX,EAAiB,CAAC,EAClBM,EAAsB,IAAIa,EAASG,QAEnC9C,EAAoB,gBACxBra,EAAQsZ,mBAAqB6C,EAAoBiB,MACjD,IAAI9B,EAA0B,EAI9Btb,EAAQqZ,0BAHwB,SAAU1D,GACtC2F,EAA0B3F,CAC9B,EAEA,IAAI+D,GAAc,EAQdE,GAAoB,EAkPxB,SAASmB,EAAyBjB,GAC9B,IAAInX,EACA0a,EAAqD,QAAvC1a,EAAK8X,OAA2B,0BAAsB,IAAP9X,OAAgB,EAASA,EAAGmX,GAC7F,GAAIuD,EAAY,CACZ,IAAIvC,EAAYoC,EAAU5a,IAAI+a,GAC9B,IAAKvC,EAAW,EACZA,EAAY,IAAIuC,GAEE,OAAIvC,EAAgB,OAEtCA,EAAUwC,WAMV,IAAI9B,EAAYV,EAAUyC,oBAC1B,GAAI/B,EAAW,CAEX,IADA,IAAIgC,EAAgB,GACXhU,EAAM,EAAGA,EAAMgS,EAAU9X,OAAQ8F,IAAO,CAC7C,IAAIiU,EAAgBjC,EAAUhS,GAE9B,GADuB0T,EAAUlD,IAAIyD,GASjCD,EAAchU,GAAO0T,EAAU5a,IAAImb,OARhB,CACnB,IAAIC,EAAe,IAAID,EACvBC,EAAqB,OAAIA,EAAmB,OAC5CA,EAAuB,WACvBF,EAAchU,GAAOkU,EACrBR,EAAU1a,IAAIib,EAAeC,EACjC,CAIJ,CACA5C,EAAsB,WAAI0C,CAC9B,CAEAN,EAAU1a,IAAI6a,EAAYvC,EAC9B,CACA,OAAOA,CACX,CACA,OAAO,IACX,CAgBA,SAASmB,EAAsB0B,EAAMhI,GACjC,IACIiI,EADkBX,EAAQY,MAAMC,sBACExb,IAAIqb,EAAKpb,aAC/C,GAAIqb,EACA,IAAK,IAAIG,EAAM,EAAGA,EAAMH,EAAgBla,OAAQqa,IAAO,CACnD,IAAIC,EAAiBJ,EAAgBG,IACjCC,aAAuD,EAASA,EAAerI,KAC/EqI,EAAerI,GAAMgI,EAE7B,CAER,CA/SA3d,EAAQoZ,aAAe,IAAIvU,IAoV3B4V,OAAc,MAAI,SAAUX,GACxB,OAAOiB,EAAyBjB,EACpC,C,qBCjYA,IAAIhS,EAAUtC,MAAQA,KAAKsC,QAAW,SAAUC,EAAGC,GAC/C,IAAIC,EAAsB,mBAAXC,QAAyBH,EAAEG,OAAOC,UACjD,IAAKF,EAAG,OAAOF,EACf,IAAmBK,EAAYhC,EAA3BiC,EAAIJ,EAAEK,KAAKP,GAAOQ,EAAK,GAC3B,IACI,WAAc,IAANP,GAAgBA,KAAM,MAAQI,EAAIC,EAAEG,QAAQC,MAAMF,EAAG5H,KAAKyH,EAAEnI,MACxE,CACA,MAAOiE,GAASkC,EAAI,CAAElC,MAAOA,EAAS,CACtC,QACI,IACQkE,IAAMA,EAAEK,OAASR,EAAII,EAAU,SAAIJ,EAAEK,KAAKD,EAClD,CACA,QAAU,GAAIjC,EAAG,MAAMA,EAAElC,KAAO,CACpC,CACA,OAAOqE,CACX,EACAzI,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQyY,aAAU,EAClB,IAAIwF,EAAyB,WACzB,SAASA,IAELzY,KAAK0Y,UAAY,CAAC,CACtB,CAkIA,OA7HAD,EAAQvY,UAAUyY,WAAa,SAAUC,GACrCH,EAAQG,OAASA,CACrB,EAMAH,EAAQvY,UAAU2Y,QAAU,SAAUza,EAAK3D,GACvC,IACI,IAAIqe,EAAcL,EAAQG,OAASxa,EAC/B2a,OAAc,EACdC,SAAmBve,EAET,OAAVA,GAAgC,WAAdue,GAAwC,WAAdA,GAAwC,YAAdA,GAAyC,WAAdA,GAAwC,cAAdA,EAC3HD,EAActe,EAEK,WAAdue,EACLD,EAAcvR,KAAKyR,UAAUxe,GAG7B0E,QAAQT,MAAM,IAASjD,OAAO2C,EAAK,4BAAiH3C,OAAOhB,IAO/JuF,KAAK0Y,UAAUta,GAAO,CAAE+R,KAAM6I,EAAW/d,KAAM8d,GAC/C,IAAIG,EAAeF,EAAYP,EAAQU,eAAiBJ,EACxDK,aAAaP,QAAQC,EAAaI,EACtC,CACA,MAAOtY,GACH,MAAIA,aAAayY,eAA4B,KAAXzY,EAAE0Y,MAA0B,OAAX1Y,EAAE0Y,MAA4B,uBAAX1Y,EAAEkH,MAA4C,+BAAXlH,EAAEkH,MACjG,IAAItJ,MAAM,wBAGVoC,CAEd,CACJ,EAOA6X,EAAQvY,UAAUgT,QAAU,SAAU9U,EAAKmb,GACvC,GAAIjf,OAAO4F,UAAU4E,eAAehC,KAAK9C,KAAK0Y,UAAWta,GAAM,CAC3D,IAAIjB,EAAK6C,KAAK0Y,UAAUta,GAAM+R,EAAOhT,EAAGgT,KAAMlV,EAAOkC,EAAGlC,KACxD,OAAa,OAATA,EACOA,EAGE,WAATkV,EACO3I,KAAKC,MAAMxM,GAGfA,CACX,CAGI,IAAI6d,EAAcL,EAAQG,OAASxa,EAC/Bob,EAAOJ,aAAalG,QAAQ4F,GAC5BjR,OAAI,EACR,GAAa,OAAT2R,GAA0B,KAATA,EAAa,CAC9B,IAAIC,EAASD,EAAK3a,MAAM4Z,EAAQU,gBAChC,GAAwE,KAAnEM,aAAuC,EAASA,EAAOvb,QAiCxD,MAAM,IAAIM,MAAM,iCAAuG/C,OAAOge,aAAuC,EAASA,EAAOvb,OAAQ,MAhC7L,IAAI8I,EAAK1E,EAAOmX,EAAQ,GAAIT,EAAYhS,EAAG,GAAIvM,EAAQuM,EAAG,GACtD+R,OAAc,EAClB,OAAQC,GACJ,IAAK,SAEDD,EADAlR,EAAIpN,EAEJ,MACJ,IAAK,SACL,IAAK,SAEDse,EADAlR,GAAKpN,EAEL,MACJ,IAAK,UAEDse,EADAlR,EAAIL,KAAKC,MAAMhN,GAEf,MACJ,IAAK,YAEDse,EADAlR,OAAIU,EAEJ,MACJ,IAAK,SACDV,EAAIL,KAAKC,MAAMhN,GAEfse,EAActe,EACd,MACJ,QACI0E,QAAQT,MAAM,IAASjD,OAAO2C,EAAK,yBAG3C4B,KAAK0Y,UAAUta,GAAO,CAAE+R,KAAM6I,EAAW/d,KAAM8d,EAKvD,MAEIlR,EAAI0R,EAER,OAAO1R,CAEf,EAKA4Q,EAAQvY,UAAUwZ,OAAS,SAAUtb,GACjC,IAAI0a,EAAcL,EAAQG,OAASxa,EACnCgb,aAAaO,WAAWb,EAC5B,EAIAL,EAAQvY,UAAUgR,MAAQ,WACtBkI,aAAalI,OACjB,EACAuH,EAAQG,OAAS,eACjBH,EAAQU,eAAiB,MAClBV,CACX,CAvI4B,GAwI5Bje,EAAQyY,QAAU,IAAIwF,C,eCvJtB,SAASlM,EAASqN,GAMd,OAAO,SAAUC,EAASzb,EAAKoN,GAC3B,IAAIsO,EAAQ,KACRC,EAAK,KAST,GARgC,mBAArBvO,EAAW/Q,OAClBqf,EAAQ,QACRC,EAAKvO,EAAW/Q,OAEe,mBAAnB+Q,EAAW1O,MACvBgd,EAAQ,MACRC,EAAKvO,EAAW1O,MAEfid,IAAOD,EACR,MAAM,IAAItb,MAAM,iBAEpBgN,EAAWsO,GAASF,EAAUG,EAAI3b,EACtC,CACJ,CAzBA9D,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQ+R,SAAWA,EACnB/R,EAAQwf,SAiCR,SAAkBC,EAAO5T,GAErB,YADc,IAAV4T,IAAoBA,EAAQ,KACzB1N,GAAS,SAAUwN,EAAI3b,GAC1B,IAAI8b,EAAW,aAAaze,OAAO2C,GAC/B+b,EAAW,CAAC,EAEhB,OADAA,EAASD,IAAa,IACf,WAEH,IADA,IAAIxO,EAAO,GACFC,EAAK,EAAGA,EAAKtG,UAAUnH,OAAQyN,IACpCD,EAAKC,GAAMtG,UAAUsG,GAEzB,IAAIyO,EAAUvf,KAAKC,MAEnB,GAAIsf,EADWD,EAASD,IACED,EAEtB,OADAE,EAASD,GAAYE,EACdL,EAAG3U,MAAMpF,KAAM0L,GAGH,iBAARrF,GAAmC,iBAARA,GAClClH,QAAQ6V,KAAK3O,EAGzB,CACJ,GACJ,C,eC3DA/L,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQ6f,cACR,WACI,OAAO,SAAU7d,EAAQ+O,EAAaC,GAClC,IAAIC,EAAiBD,EAAW/Q,MAmBhC,OAlBA+Q,EAAW/Q,MAAQ,WAEf,IADA,IAAIiR,EAAO,GACFC,EAAK,EAAGA,EAAKtG,UAAUnH,OAAQyN,IACpCD,EAAKC,GAAMtG,UAAUsG,GAEzB,IAAI2O,EAAU9Y,GAAG+Y,KAAKC,iBACtB,GAAIF,EAAQ1I,OAAS0I,EAAQ3I,OAAS,KAAO,KACrC8I,EAAUjZ,GAAGwP,OAAOC,UAChByJ,WAAY,EACpBD,EAAQE,UAAW,OAElB,GAAIL,EAAQ1I,OAAS0I,EAAQ3I,MAAQ,KAAO,IAAK,CAClD,IAAI8I,KAAUjZ,GAAGwP,OAAOC,UAChByJ,WAAY,EACpBD,EAAQE,UAAW,CACvB,CACA,OAAOlP,EAAerG,MAAMpF,KAAM0L,EACtC,EACOF,CACX,CACJ,C,eCzBAlR,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQogB,WACR,SAAoBC,EAAMta,EAAOua,GAE7B,QADiB,IAAbA,IAAuBA,GAAW,GAClCA,EAAU,CAEV,IADA,IAAItb,EAAS,GACJxE,EAAM,EAAGA,EAAMuF,EAAOvF,IAAO,CAClC,IAAI8I,EAAS7H,KAAK4H,MAAM5H,KAAK6H,UAAY+W,EAAK3c,OAAS,IACvDsB,EAAOrE,KAAK0f,EAAK/W,GACrB,CACA,OAAOtE,CACX,CAEI,OAGR,SAAiBqb,EAAME,QACA,IAAfA,IAAyBA,GAAa,GACtCA,IACAF,EAAOA,EAAKpf,UAEhB,IAAK,IAAI8F,EAAMsZ,EAAK3c,OAAS,EAAGqD,EAAM,EAAGA,IAAO,CAC5C,IAAIuC,EAAS7H,KAAK4H,MAAM5H,KAAK6H,SAAWvC,GACpCyZ,EAAOH,EAAKtZ,GAChBsZ,EAAKtZ,GAAOsZ,EAAK/W,GACjB+W,EAAK/W,GAAUkX,CACnB,CACA,OAAOH,CACX,CAfeI,CAAQJ,GAAMvX,MAAM,EAAG/C,EAEtC,C,eCPA,IAAI2a,EARJ5gB,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQ2gB,cAAgB3gB,EAAQ0gB,gBAAa,EAQ7C,SAAWA,GACPA,EAAmB,OAAI,SACvBA,EAAkB,MAAI,QACtBA,EAAoB,QAAI,SAC3B,CAJD,CAIGA,IAAe1gB,EAAQ0gB,WAAaA,EAAa,CAAC,IACrD,IAAIC,EAA+B,WAC/B,SAASA,IACT,CA4EA,OA3EA7gB,OAAOC,eAAe4gB,EAAe,aAAc,CAI/Cre,IAAK,WACD,OAAOkD,KAAKob,WAChB,EACA7d,YAAY,EACZC,cAAc,IAMlB2d,EAAcE,qBAAuB,SAAUC,GAC3Ctb,KAAKob,YAAcE,CACvB,EACAhhB,OAAOC,eAAe4gB,EAAe,aAAc,CAK/Cre,IAAK,WACD,OAAOkD,KAAKub,WAChB,EACAhe,YAAY,EACZC,cAAc,IAOlB2d,EAAcK,cAAgB,SAAUC,GACpCzb,KAAK0b,SAAWD,CACpB,EAEAN,EAAcQ,YAAc,SAAUL,GAClC,IAAK,IAAItgB,EAAM,EAAG4gB,EAAI5b,KAAK0b,SAASxd,OAAQlD,EAAM4gB,EAAG5gB,IAAO,CACxD,IAAI6gB,EAAe7b,KAAK0b,SAAS1gB,GACjC,IAAKgF,KAAK8b,uBAAuBtH,IAAIqH,GAAe,CAChD7b,KAAK8b,uBAAuB9e,IAAI6e,GAAc,GAC9C,IAAIE,EAAY,IAAIF,EACpBE,EAAuB,YAAIT,EAC3Btb,KAAKub,YAAYvgB,GAAO+gB,EAExBA,EAAUC,WAAWV,EACzB,CACJ,CACJ,EAKAH,EAAc9Y,MAAQ,WAElBrC,KAAKub,YAAY3b,SAAQ,SAAUqc,GAC/B,IAEQA,GAAUA,EAAOC,eAAuD,mBAA/BD,EAAOC,cAAchL,OAC9D+K,EAAOC,cAAchL,OAE7B,CACA,MAAOxS,GACHS,QAAQT,MAAM,6BAA8BA,EAChD,CACJ,IAEAsB,KAAKub,YAAc,GACnBvb,KAAK8b,uBAAuB5K,QAC5BlR,KAAKob,YAAcF,EAAWiB,MAClC,EACAhB,EAAcI,YAAc,GAC5BJ,EAAcW,uBAAyB,IAAIzc,IAC3C8b,EAAcC,YAAcF,EAAWiB,OAChChB,CACX,CA/EkC,GAgFlC3gB,EAAQ2gB,cAAgBA,C,eC9FxB7gB,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQgU,QAOR,SAAiBhS,EAAQ4B,EAAKoN,GAC1B,IAAIsO,EAAQ,KACRC,EAAK,KAYT,GAXgC,mBAArBvO,EAAW/Q,OAClBqf,EAAQ,QAEU,KADlBC,EAAKvO,EAAW/Q,OACTyD,QACHiB,QAAQ6V,KAAK,kEAGc,mBAAnBxJ,EAAW1O,MACvBgd,EAAQ,MACRC,EAAKvO,EAAW1O,MAEfid,EACD,MAAM,IAAIvb,MAAM,iBAEpB,IAAI4d,EAAa,YAAY3gB,OAAO2C,GACpCoN,EAAWsO,GAAS,WAEhB,IADA,IAAIpO,EAAO,GACFC,EAAK,EAAGA,EAAKtG,UAAUnH,OAAQyN,IACpCD,EAAKC,GAAMtG,UAAUsG,GAUzB,OARK3L,KAAK8E,eAAesX,IACrB9hB,OAAOC,eAAeyF,KAAMoc,EAAY,CACpC5e,cAAc,EACdD,YAAY,EACZ8e,UAAU,EACV5hB,MAAOsf,EAAG3U,MAAMpF,KAAM0L,KAGvB1L,KAAKoc,EAChB,CACJ,C,eCzCA9hB,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQgM,aAAU,EAClB,IAAIA,EAAyB,WACzB,SAASA,IACT,CAeA,OAVAA,EAAQlB,QAAU,SAAUrG,GACxB,OAAOA,CACX,EAKAuH,EAAQX,QAAU,SAAU5G,GACxB,OAAOA,CACX,EACOuH,CACX,CAlB4B,GAmB5BhM,EAAQgM,QAAUA,C,eCrBlBlM,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQ8hB,oCAAiC,EAezC9hB,EAAQ8hB,+BAAiC,IATrC,WACItc,KAAKuc,UAAY,EAKrB,C,eCbJjiB,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQoS,aAAepS,EAAQgiB,oBAAiB,EAChDhiB,EAAQgiB,eAAiB,sCAKzB,IAAI5P,EAA8B,WAC9B,SAASA,IACT,CA8BA,OAzBAA,EAAaC,SAAW,WACpB,IAAI1P,EACJ,OAAyB,QAAjBA,EAAKqE,GAAG+L,WAAwB,IAAPpQ,OAAgB,EAASA,EAAG0P,QACjE,EASAD,EAAa6P,KAAO,SAAUlI,EAAYtZ,EAAMyhB,EAAiBlH,GAE7D,QADkB,IAAdA,IAAwBA,EAAYhb,EAAQgiB,gBAC3Cxc,KAAK6M,WAIV,MAAY,IAAR5R,EACO0hB,IAAIC,WAAWC,iBAAiBrH,EAAWjB,EAAYmI,GAGvDC,IAAIC,WAAWC,iBAAiBrH,EAAWjB,EAAYmI,EAAiBzhB,EAEvF,EACO2R,CACX,CAjCiC,GAkCjCpS,EAAQoS,aAAeA,C,eCzBvB,SAASkQ,EAASzW,GACd,MAAuB,iBAARA,CACnB,CAoDA,SAASyM,EAAYnV,GACjB,YAAuB,IAARA,CACnB,CAYA,SAASof,EAAkBpf,GACvB,OAAQmV,EAAYnV,IAAgB,OAARA,CAChC,CAtFArD,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQsiB,SAAWA,EACnBtiB,EAAQwiB,cAoBR,SAAuBviB,GACnB,OAAOmK,MAAMqY,QAAQxiB,IAAUA,EAAMyiB,OAAM,SAAUC,GAAQ,OAAOL,EAASK,EAAO,GACxF,EArBA3iB,EAAQyD,SAyBR,SAAkBN,GAId,QAAsB,iBAARA,GACC,OAARA,GACCiH,MAAMqY,QAAQtf,IACbA,aAAeyf,QACfzf,aAAe9C,KAC5B,EAjCAL,EAAQ6iB,aAqCR,SAAsB1f,GAClB,IAAI2f,EAAahjB,OAAOijB,eAAeC,YACvC,MAAsB,iBAAR7f,GACPA,aAAe2f,CAC1B,EAxCA9iB,EAAQijB,SA6CR,SAAkB9f,GACd,MAAuB,iBAARA,IAAqBkY,MAAMlY,EAC9C,EA9CAnD,EAAQkjB,WAmDR,SAAoB/f,GAChB,QAASA,GAAuC,mBAAzBA,EAAI+E,OAAOC,SACtC,EApDAnI,EAAQmjB,UAwDR,SAAmBhgB,GACf,OAAgB,IAARA,IAAwB,IAARA,CAC5B,EAzDAnD,EAAQsY,YAAcA,EACtBtY,EAAQojB,UAoER,SAAmBC,GACf,OAAQd,EAAkBc,EAC9B,EArEArjB,EAAQuiB,kBAAoBA,EAC5BviB,EAAQsjB,WAgFR,SAAoBngB,GAChB,MAAuB,mBAARA,CACnB,EAjFAnD,EAAQujB,QAsFR,SAAiB9iB,GAEb,OADgBX,OAAO4F,UAAUyF,SAAS7C,KAAK7H,IAE3C,IAAK,kBAUL,QACI,MAAO,SAVX,IAAK,kBAAmB,MAAO,SAC/B,IAAK,kBAAmB,MAAO,SAC/B,IAAK,mBAAoB,MAAO,UAChC,IAAK,iBAAkB,MAAO,QAC9B,IAAK,qBAAsB,MAAO,YAClC,IAAK,gBAAiB,MAAO,OAC7B,IAAK,gBAAiB,MAAO,OAC7B,IAAK,kBAAmB,MAAO,SAC/B,IAAK,oBAAqB,MAAO,WAIzC,C,iBClHAX,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQwjB,qBAAkB,EAC1B,IAAIC,EAAkB,EAAQ,MAI1BC,EAAiC,WACjC,SAASA,IACLA,EAAgBC,iBAAmBne,IACvC,CAoIA,OA/HAke,EAAgBhe,UAAUke,0BAA4B,WAC7CF,EAAgBG,+BACjBH,EAAgBG,8BAA+B,EAE/CH,EAAgBI,gCAAkC9c,GAAG+c,SAAyB,eAAEC,WAChFhd,GAAG+c,SAAyB,eAAEC,WAAa,SAAUvc,GACjD,IAAIqZ,EAAa2C,EAAgB9C,cAAcG,WAC3CmD,EAAkBxc,EAAKlF,YACvB2hB,EAAaR,EAAgBS,oBAAoB7hB,IAAI2hB,GACpDC,IACDA,EAAa,CAAC,EACdR,EAAgBS,oBAAoB3hB,IAAIyhB,EAAiBC,IAE7DA,EAAWpD,GAAcrZ,EACzBic,EAAgBI,gCAAgClZ,MAAMpF,KAAM,CAACiC,IAC7D,IAAI2c,EAAcV,EAAgBW,UAAU/hB,IAAI2hB,GAChD,GAAIG,EAAa,CACb,IAAIE,EAAWF,EAAYtD,GAC3B,GAAIwD,EAAU,CACV,IAAK,IAAI9jB,EAAM,EAAGA,EAAM8jB,EAAS5gB,OAAQlD,KAErCiP,EADc6U,EAAS9jB,IACfiH,GAEZ6c,EAAS5gB,OAAS,CACtB,CACJ,CAEI+D,EAAKb,OAASa,EAAKb,KAAKC,SACxBY,EAAKb,KAAKC,QAAS,EAE3B,EAER,EAMA6c,EAAgBhe,UAAU6e,MAAQ,SAAUC,GACxC,IAAIC,EAAaf,EAAgBS,oBAAoB7hB,IAAIkiB,GACrD1D,EAAa2C,EAAgB9C,cAAcG,WAC/C,GAAI2D,EACA,OAAOA,EAAW3D,IAAe2D,EAAWhB,EAAgB/C,WAAWiB,OAG/E,EAMA+B,EAAgBhe,UAAUgf,WAAa,SAAUF,GAC7C,OAAO,IAAIte,SAAQ,SAAUuJ,EAASrJ,GAClC,IAAIqB,EAAOic,EAAgBC,iBAAiBY,MAAMC,GAClD,GAAI/c,EACAgI,EAAQhI,OADZ,CAKI,IAAI2c,EAAcV,EAAgBW,UAAU/hB,IAAIkiB,GAC3CJ,IACDA,EAAc,CAAC,EACfV,EAAgBW,UAAU7hB,IAAIgiB,EAAWJ,IAE7C,IAAItD,EAAa2C,EAAgB9C,cAAcG,WAC3CwD,EAAWF,EAAYtD,GACtBwD,IACDA,EAAW,GACXF,EAAYtD,GAAcwD,EAC1BA,EAAS3jB,KAAK8O,IAElBiU,EAAgBC,iBAAiBC,2BACrC,CACJ,GACJ,EAIAF,EAAgBhe,UAAU8B,eAAiB,WAEvCkc,EAAgBW,UAAUjf,SAAQ,SAAUgf,EAAaO,GACrD,IAAK,IAAI7D,KAAcsD,EAAa,CAChC,IAAIE,EAAWF,EAAYtD,GAC3BwD,EAASlf,SAAQ,SAAUqK,GAQ3B,IACA6U,EAAS5gB,OAAS,CACtB,CACJ,IAEAggB,EAAgBS,oBAAoBzN,QACpCgN,EAAgBW,UAAU3N,QACtBnT,UACAoB,QAAQC,IAAI,8BAEpB,EAIA8e,EAAgBhe,UAAUkf,0BAA4B,WAC9ClB,EAAgBG,8BAAgCH,EAAgBI,kCAChE9c,GAAG+c,SAAyB,eAAEC,WAAaN,EAAgBI,gCAC3DJ,EAAgBG,8BAA+B,EAC3CtgB,UACAoB,QAAQC,IAAI,gCAGxB,EAIA8e,EAAgBhe,UAAUmC,MAAQ,WAC9BrC,KAAKgC,iBACLhC,KAAKof,4BACDrhB,UACAoB,QAAQC,IAAI,0CAEpB,EACA8e,EAAgBS,oBAAsB,IAAItf,IAC1C6e,EAAgBW,UAAY,IAAIxf,IAChC6e,EAAgBI,gCAAkC,KAC3CJ,CACX,CAxIoC,GAyIpC1jB,EAAQwjB,gBAAkB,IAAIE,C,eC/I9B5jB,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQ6kB,gBAAa,EACrB,IAAIC,EAA4B,WAC5B,SAASA,IACLtf,KAAKuf,SAAW,IAAIlgB,GACxB,CAyBA,OAnBAigB,EAAWpf,UAAUsf,6BAA+B,SAAUC,GAC1D,IAAItH,EAAOnY,KAAKuf,SAASziB,IAAI2iB,GAK7B,OAJKtH,IACDA,EAAO3W,GAAGC,YAAYge,GACtBzf,KAAKuf,SAASviB,IAAIyiB,EAAQtH,IAEvBA,CACX,EAIAmH,EAAWpf,UAAUwf,cAAgB,WACjC1f,KAAKuf,SAAS3f,SAAQ,SAAUuY,EAAMsH,GAC9Bje,GAAGU,QAAQiW,IACXA,EAAK/V,SAEb,IACApC,KAAKuf,SAASrO,OAClB,EACOoO,CACX,CA7B+B,GA8B/B9kB,EAAQ6kB,WAAa,IAAIC,C,eChCzBhlB,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQmlB,qBAAkB,EAW1B,IAAIA,EAAiC,WACjC,SAASA,IACL3f,KAAK4f,WAAY,EACjB5f,KAAK6f,cAAgB,EACzB,CAuCA,OAtCAvlB,OAAOC,eAAeolB,EAAgBzf,UAAW,YAAa,CAI1DpD,IAAK,WACD,OAAOkD,KAAK4f,SAChB,EACAriB,YAAY,EACZC,cAAc,IAMlBmiB,EAAgBzf,UAAU4f,KAAO,WAC7B,IAAI1f,EAAQJ,KACZ,OAAO,IAAIU,SAAQ,SAAUuJ,GACzB7J,EAAMyf,cAAc1kB,KAAK,CAAE8O,QAASA,GACxC,GACJ,EAIA0V,EAAgBzf,UAAUmC,MAAQ,WAC9BrC,KAAK4f,WAAY,EACjB5f,KAAK6f,cAAc3hB,OAAS,CAChC,EAKAyhB,EAAgBzf,UAAU+J,QAAU,SAAUxP,GAC1C,KAAOuF,KAAK6f,cAAc3hB,OAAS,IAE/B+L,EADcjK,KAAK6f,cAAcxb,QAAQ4F,SACjCxP,GAEZuF,KAAK4f,WAAY,CACrB,EACOD,CACX,CA5CoC,GA6CpCnlB,EAAQmlB,gBAAkBA,C,eCzD1BrlB,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQulB,cAGR,SAAuBC,EAASvlB,GAC5B,IAAK,IAAI2D,KAAO4hB,EACZ,GAAIA,EAAQ5hB,KAAS3D,EACjB,OAAO2D,CAInB,EATA5D,EAAQylB,cAWR,SAAuBxlB,EAAOulB,GAC1B,OAAO1lB,OAAOmf,OAAOuG,GAAShhB,SAASvE,EAC3C,C,eCPA,SAASylB,EAAQ1K,GACb,OAAO,SAAUhZ,GACRA,EAAO0D,UAAyB,gBACjC1D,EAAO0D,UAAyB,cAAIsV,EAC5C,CACJ,CAKA,SAASC,EAAa0K,GAClB,OAAO3e,GAAG4e,GAAG3K,aAAa0K,EAC9B,CApBA7lB,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQ0lB,QAAUA,EAClB1lB,EAAQib,aAAeA,EAmBvBR,OAAgB,QAAIiL,EACpBjL,OAAqB,aAAIQ,C,eCtBzBnb,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQ6lB,yBAKR,WACSC,WACDhmB,OAAOC,eAAeiH,GAAGmH,KAAKzI,UAAW,SAAU,CAC/CpD,IAAK,WACD,OAAOkD,KAAKugB,OAChB,EACAvjB,IAAK,SAAUvC,GAIX,GAchB,SAAoC+lB,EAAM/lB,GAEtC,IAAIgmB,EAAyBD,EAAkB,aAAKA,EAAkB,YAAE5Y,MAAK,SAAUC,GAAK,OAAOA,EAAE6Y,cAAgB,IAGrH,GAFAF,EAAK/P,QAAU,IAEXgQ,EACA,GAAIhmB,GAEA,GADAgmB,EAAsC,cAAI,GACtCD,EAAK/f,OAAQ,CACb,IAAIkgB,EAAS,SAAUnkB,GACnB,KAAIA,EAAOiE,kBAAkBe,GAAGof,QAE5BpkB,EAAOiE,OAAQ,CACf,GAAIjE,EAAOiE,OAAOogB,SAAU,CACxB,IAAIva,EAAQ9J,EAAOiE,OAAOogB,SAASta,QAAQ/J,GAEpBA,EAAOiE,OAAOogB,SAASvd,MAAM,EAAGgD,GACtC1G,SAAQ,SAAUiI,GAC3BA,EAAE4I,QAAU,GAAK5I,EAAExG,QACnBof,EAAsC,cAAEtlB,KAAK0M,GAEjDA,EAAE4I,QAAU,CAChB,GACJ,CAEAkQ,EAAOnkB,EAAOiE,OAClB,CACJ,EACAkgB,EAAOH,EACX,MAEC,CAED,IAAIM,EAAgBL,EAAsC,cAC1D,GAAIK,EAAe,CACf,IAAK,IAAI9lB,EAAM,EAAGA,EAAM8lB,EAAc5iB,OAAQlD,IAC1C8lB,EAAc9lB,GAAKyV,QAAU,IAEjCgQ,EAAsC,cAAEviB,OAAS,CACrD,CACJ,CAGR,CA1DgB6iB,CAA2B/gB,KAF3BvF,IAAUA,GAGNuF,KAAKugB,UAAY9lB,EAAO,CACxBuF,KAAKugB,QAAU9lB,EACf,IAAIumB,EAAWhhB,KAAKihB,QAChBD,GACyBA,EAASE,oBAE9B1f,GAAG+c,SAAyB,eAAE4C,aAAanhB,KAAMvF,EAG7D,CACJ,GAGZ,C,eC7BAH,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQ4mB,mBAOR,SAA4BhgB,GACxB,GAAKA,EAAL,CAGA,IAAI2Q,EAAS3Q,EAAKM,aAAaF,GAAGwQ,QAClC,GAAKD,EAAL,CAGA,IAAIsP,EAAO7f,GAAG+Y,KAAK+G,eACfC,EAAQ/f,GAAG+Y,KAAKC,iBAWpB,GAVA,SAAkB6G,EAAME,GACpB,QAAIF,EAAK1P,MAAQ0P,EAAKzP,OAAS,IAAa,MAGxCpQ,GAAG+L,IAAIF,KAAO7L,GAAG+L,IAAIC,aAAchM,GAAG+L,IAAIiU,aAClCH,EAAKzP,OAASyP,EAAK1P,MAAQ8P,GAC9BF,EAAM3P,OAAS2P,EAAM5P,MAAQ8P,GAG1C,CACIC,CAASL,EAAME,GAAQ,CACvB,IAAI3P,EAAS3V,KAAKgQ,IAAIoV,EAAKzP,OAAQ2P,EAAM3P,QACzCG,EAAOM,KAAOsP,EAA0B/P,CAC5C,CAhBA,CAJA,CAqBJ,EA9BA,IAAI6P,EAA8B,KAAO,IACrCE,EAA0B,I,qBCH9B,IAAIC,EAAY5hB,MAAQA,KAAK4hB,UAAa,SAASrf,GAC/C,IAAIsf,EAAsB,mBAAXnf,QAAyBA,OAAOC,SAAUF,EAAIof,GAAKtf,EAAEsf,GAAIhf,EAAI,EAC5E,GAAIJ,EAAG,OAAOA,EAAEK,KAAKP,GACrB,GAAIA,GAAyB,iBAAbA,EAAErE,OAAqB,MAAO,CAC1C8E,KAAM,WAEF,OADIT,GAAKM,GAAKN,EAAErE,SAAQqE,OAAI,GACrB,CAAE9H,MAAO8H,GAAKA,EAAEM,KAAMI,MAAOV,EACxC,GAEJ,MAAM,IAAIwC,UAAU8c,EAAI,0BAA4B,kCACxD,EACAvnB,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQsnB,MAOR,SAASA,EAAMC,EAAMC,EAAMC,GACvB,IAAIC,EAAK/kB,EAAIglB,EAAKnb,EAGlB,QAFgB,IAAZib,IAAsBA,EAAU,IAEhCF,IAASC,EACT,OAAO,EAEX,GAAoB,iBAATD,GAA8B,OAATA,GACZ,iBAATC,GAA8B,OAATA,EAC5B,OAAO,EAEX,IAEI,IAAK,IAAII,EAAYR,EAASK,GAAUI,EAAcD,EAAUpf,QAASqf,EAAYpf,KAAMof,EAAcD,EAAUpf,OAAQ,CACvH,IAAIsf,EAAQD,EAAY5nB,MACxB,GAAI6nB,EAAM,KAAOP,GAAQO,EAAM,KAAON,EAClC,OAAO,CACf,CACJ,CACA,MAAOO,GAASL,EAAM,CAAExjB,MAAO6jB,EAAS,CACxC,QACI,IACQF,IAAgBA,EAAYpf,OAAS9F,EAAKilB,EAAUI,SAASrlB,EAAG2F,KAAKsf,EAC7E,CACA,QAAU,GAAIF,EAAK,MAAMA,EAAIxjB,KAAO,CACxC,CAGA,GAFAujB,EAAQ9mB,KAAK,CAAC4mB,EAAMC,IAEhBD,EAAKhlB,cAAgBilB,EAAKjlB,YAC1B,OAAO,EAEX,GAAIglB,EAAKhlB,cAAgB0lB,QAAUV,EAAKhlB,cAAgBwB,QAAUwjB,EAAKhlB,cAAgB2lB,QACnF,OAAOX,EAAKY,YAAcX,EAAKW,UAGnC,GAAI/d,MAAMqY,QAAQ8E,IAASnd,MAAMqY,QAAQ+E,GAAO,CAC5C,GAAID,EAAK7jB,SAAW8jB,EAAK9jB,OACrB,OAAO,EACX,IAAK,IAAIlD,EAAM,EAAGA,EAAM+mB,EAAK7jB,OAAQlD,IACjC,IAAK8mB,EAAMC,EAAK/mB,GAAMgnB,EAAKhnB,GAAMinB,GAC7B,OAAO,EAEf,OAAO,CACX,CAEA,IAAIW,EAAQtoB,OAAOqF,KAAKoiB,GACpBc,EAAQvoB,OAAOqF,KAAKqiB,GACxB,GAAIY,EAAM1kB,SAAW2kB,EAAM3kB,OACvB,OAAO,EACX,IACI,IAAK,IAAI4kB,EAAUlB,EAASgB,GAAQG,EAAYD,EAAQ9f,QAAS+f,EAAU9f,KAAM8f,EAAYD,EAAQ9f,OAAQ,CACzG,IAAI5E,EAAM2kB,EAAUtoB,MACpB,IAAKunB,EAAKld,eAAe1G,GACrB,OAAO,EACX,IAAK0jB,EAAMC,EAAK3jB,GAAM4jB,EAAK5jB,GAAM6jB,GAC7B,OAAO,CACf,CACJ,CACA,MAAOe,GAASb,EAAM,CAAEzjB,MAAOskB,EAAS,CACxC,QACI,IACQD,IAAcA,EAAU9f,OAAS+D,EAAK8b,EAAQN,SAASxb,EAAGlE,KAAKggB,EACvE,CACA,QAAU,GAAIX,EAAK,MAAMA,EAAIzjB,KAAO,CACxC,CACA,OAAO,CACX,C,iBCrFApE,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQ4a,qBAAkB,EAC1B,IAAI6N,EAAmC,EAAQ,MAC3CtQ,EAAY,EAAQ,MA4DpBuQ,EAAiC,WACjC,SAASA,IASLljB,KAAKmjB,QAAU,CAAC,EAIhBnjB,KAAKojB,YAAc,CAAC,EASpBpjB,KAAKqjB,WAAa,CAAC,EAInBrjB,KAAKsjB,iBAAmB,CAAC,EAIzBtjB,KAAKujB,oBAAsB,CAAC,EAS5BvjB,KAAKwjB,qBAAuB,CAAC,EAC7BxjB,KAAKyjB,2BAA6B,CAAC,EACnCzjB,KAAK0jB,cAAe,EACpB1jB,KAAK2jB,QAAS,CAClB,CAsNA,OArNArpB,OAAOC,eAAe2oB,EAAgBhjB,UAAW,qBAAsB,CAInEpD,IAAK,WACD,OAAOkD,KAAKujB,mBAChB,EACAhmB,YAAY,EACZC,cAAc,IAElBlD,OAAOC,eAAe2oB,EAAgBhjB,UAAW,cAAe,CAI5DpD,IAAK,WACD,OAAOkD,KAAK0jB,YAChB,EACAnmB,YAAY,EACZC,cAAc,IAElBlD,OAAOC,eAAe2oB,EAAgBhjB,UAAW,QAAS,CAItDpD,IAAK,WACD,OAAOkD,KAAK2jB,MAChB,EACApmB,YAAY,EACZC,cAAc,IAElB0lB,EAAgBhjB,UAAU0jB,WAAa,SAAUC,GAC7C,GAAKA,EAAL,CAIA,IAAIC,EAAUD,EAAIC,QACdX,EAAUnjB,KAAKmjB,QACfC,EAAcpjB,KAAKojB,YAMvB,IAAK,IAAIjN,KAAa2N,EAElB,IADA,IAAIC,EAAoBD,EAAQ3N,GACvBnb,EAAM,EAAGA,EAAM+oB,EAAkB7lB,OAAQlD,IAAO,CACrD,IAAIgpB,EAAgBD,EAAkB/oB,GAClC+a,EAAKiO,EAAcjO,GAAIJ,EAAQqO,EAAcrO,MAC5CwN,EAAQhN,KACTgN,EAAQhN,GAAa,CAAC,GAE1BgN,EAAQhN,GAAWJ,GAAMJ,EACzByN,EAAYrN,GAAM,CAAEI,UAAWA,EAAWR,MAAOA,EACrD,CAnBJ,MAFIxW,QAAQT,MAAM,cAuBtB,EAEAwkB,EAAgBhjB,UAAU+jB,gBAAkB,SAAU3b,GAMlDtI,KAAKkkB,iBAAmB5b,EACxB,IAAInL,EAAK6C,KAAKkkB,iBAAkBC,EAAWhnB,EAAGgnB,SAAUC,EAAOjnB,EAAGinB,KAI9DC,EAA4BjL,aAAalG,QAAQ,yBACjDoR,EAAaD,EAA4BE,SAASF,GAA6BD,EACnFzR,EAAUM,QAAQ4F,QAAQ,aAAcyL,GACxC,IAAK,IAAI/iB,EAAM,EAAGA,EAAM4iB,EAASjmB,OAAQqD,IAAO,CAC5C,IAAIwU,EAAKoO,EAAS5iB,GAAKwU,GACvB/V,KAAKqjB,WAAWtN,GAAMoO,EAAS5iB,EACnC,CACJ,EAEA2hB,EAAgBhjB,UAAUskB,mBAAqB,SAAUlc,GAOrD,IADA,IAAImc,EAAiBnc,EAAO6b,SACnBngB,EAAM,EAAGA,EAAMygB,EAAevmB,OAAQ8F,IAAO,CAClD,IAAI+R,EAAK0O,EAAezgB,GAAK+R,GAC7B/V,KAAKwjB,qBAAqBzN,GAAM0O,EAAezgB,EACnD,CACJ,EAaAkf,EAAgBhjB,UAAUwkB,+BAAiC,WACvD,IAAItB,EAAcpjB,KAAKojB,YAEnBuB,EADW3kB,KAAKkkB,iBAAiBC,SACRrlB,QAAO,SAAU0a,GAAQ,OAAQyJ,EAAiC3G,+BAA+BC,UAAUqI,MAAK,SAAU7O,GAAM,OAAOA,IAAOyD,EAAKzD,EAAI,GAAI,IACpLhY,WACAoB,QAAQC,IAAI,YAAa,iCAAkCulB,GAC3DhS,EAAUM,QAAQ4F,QAAQ,iBAAkB8L,IAEhD,IAAK,IAAIpM,EAAM,EAAGA,EAAMoM,EAAczmB,OAAQqa,IAAO,CACjD,IAAIxC,EAAK4O,EAAcpM,GAAKxC,GACxB8O,EAAczB,EAAYrN,GAC9B,GAAK8O,EAAL,CAMA,IAAI1O,EAAY0O,EAAY1O,UAAWR,EAAQkP,EAAYlP,MACvDyB,EAAiBjB,EAAUa,OAAO,GAAGC,cAAgBd,EAAU7S,MAAM,GAAK,QAC9EtD,KAAKujB,oBAAoBnM,GAAkB,CAAErB,GAAIA,EAAIJ,MAAOA,EAH5D,MAJQ5X,UACAoB,QAAQT,MAAM,sBAA6EjD,OAAOsa,GAO9G,CACJ,EAEAmN,EAAgBhjB,UAAU4kB,8BAAgC,SAAUC,GAGhE,IAFA,IAAI3B,EAAcpjB,KAAKojB,YAEd4B,EAAM,EAAGA,EAAMD,EAAU7mB,OAAQ8mB,IAAO,CAC7C,IAAIjP,EAAKgP,EAAUC,GACfH,EAAczB,EAAYrN,GAC9B,GAAK8O,EAAL,CAMA,IAAI1O,EAAY0O,EAAY1O,UAAWR,EAAQkP,EAAYlP,MACvDyB,EAAiBjB,EAAUa,OAAO,GAAGC,cAAgBd,EAAU7S,MAAM,GAAK,QAC9EtD,KAAKujB,oBAAoBnM,GAAkB,CAAErB,GAAIA,EAAIJ,MAAOA,EAH5D,MAJQ5X,UACAoB,QAAQT,MAAM,sBAA6EjD,OAAOsa,GAO9G,CACIhY,UACAoB,QAAQC,IAAI,cAAe,iCAAkCY,KAAKujB,oBAE1E,EAEAL,EAAgBhjB,UAAU+kB,8BAAgC,SAAUC,GAC9CllB,KAAKojB,YAEvB,IAFA,IAES+B,EAAM,EAAGA,EAAMD,EAAUhnB,OAAQinB,IAAO,CAC7C,IAAIhoB,EAAK+nB,EAAUC,GAAMpP,EAAK5Y,EAAG4Y,GAAIqP,EAASjoB,EAAG2K,KAAM6N,EAAQxY,EAAGwY,MAClE3V,KAAKujB,oBAAoB6B,GAAU,CAAErP,GAAIA,EAAIJ,MAAOA,EACxD,CACJ,EAEAuN,EAAgBhjB,UAAUmlB,iBAAmB,SAAUC,GAEnD,IADA,IAAIlC,EAAcpjB,KAAKojB,YACdmC,EAAM,EAAGA,EAAMD,EAAiBpnB,OAAQqnB,IAAO,CACpD,IAAIxP,EAAKuP,EAAiBC,GACtBV,EAAczB,EAAYrN,GAC9B,GAAK8O,EAAL,CAMA,IAAI1O,EAAY0O,EAAY1O,UAAWR,EAAQkP,EAAYlP,MACvDyB,EAAiBjB,EAAUa,OAAO,GAAGC,cAAgBd,EAAU7S,MAAM,GAAK,QAC9EtD,KAAKujB,oBAAoBnM,GAAkB,CAAErB,GAAIA,EAAIJ,MAAOA,GAC5DxW,QAAQC,IAAI,UAA6C3D,OAAOsa,GAJhE,MAJQhY,UACAoB,QAAQT,MAAM,6BAAkHjD,OAAOsa,GAQnJ,CACJ,EAOAmN,EAAgBhjB,UAAUslB,UAAY,SAAUrP,EAAWJ,GACvD,GAAIF,MAAME,GAAK,CACX,GAAI/V,KAAKsjB,iBAAiBnN,GACtB,OAAOnW,KAAKsjB,iBAAiBnN,GAEjC,IAAIsP,EAAazlB,KAAKmjB,QAAQhN,GAC9B,IAAK,IAAIuP,KAAOD,EACZ,GAAIzlB,KAAKqjB,WAAWqC,GAEhB,OADA1lB,KAAKsjB,iBAAiBnN,GAAasP,EAAWC,GACvC1lB,KAAKsjB,iBAAiBnN,EAGzC,CACA,OAAOnW,KAAKmjB,QAAQhN,GAAWJ,EACnC,EAOAmN,EAAgBhjB,UAAUylB,oBAAsB,SAAUvnB,EAAK2X,GAC3D,GAAIF,MAAME,GAAK,CACX,GAAI/V,KAAKyjB,2BAA2BrlB,GAChC,OAAO4B,KAAKyjB,2BAA2BrlB,GAE3C,IAAIqnB,EAAazlB,KAAKmjB,QAAQ/kB,GAC9B,IAAK,IAAIsnB,KAAOD,EACZ,GAAIzlB,KAAKwjB,qBAAqBkC,GAE1B,OADA1lB,KAAKyjB,2BAA2BrlB,GAAOqnB,EAAWC,GAC3C1lB,KAAKyjB,2BAA2BrlB,EAGnD,CACA,OAAO4B,KAAKmjB,QAAQ/kB,GAAK2X,EAC7B,EACOmN,CACX,CAnQoC,GAoQpC1oB,EAAQ4a,gBAAkB,IAAI8N,C,iBCnU9B5oB,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQorB,cAOR,WAEI,IADA,IAAIC,EAAY,GACPla,EAAK,EAAGA,EAAKtG,UAAUnH,OAAQyN,IACpCka,EAAUla,GAAMtG,UAAUsG,GAE9B,OAAO,SAAUnP,EAAQ+O,EAAaC,GAClC,IAAIC,EAAiBD,EAAW/Q,MAehC,OAdA+Q,EAAW/Q,MAAQ,WAGf,IAFA,IAAI2F,EAAQJ,KACR0L,EAAO,GACFC,EAAK,EAAGA,EAAKtG,UAAUnH,OAAQyN,IACpCD,EAAKC,GAAMtG,UAAUsG,GAQzB,OANAka,EAAUjmB,SAAQ,SAAUkmB,GACxB,IAAI1kB,EAAOhB,EAAM0lB,GACb1kB,IACA,EAAI2kB,EAAgB3E,oBAAoBhgB,EAEhD,IACOqK,EAAerG,MAAMpF,KAAM0L,EACtC,EACOF,CACX,CACJ,EA7BA,IAAIua,EAAkB,EAAQ,K,qBCF9B,IAAInE,EAAY5hB,MAAQA,KAAK4hB,UAAa,SAASrf,GAC/C,IAAIsf,EAAsB,mBAAXnf,QAAyBA,OAAOC,SAAUF,EAAIof,GAAKtf,EAAEsf,GAAIhf,EAAI,EAC5E,GAAIJ,EAAG,OAAOA,EAAEK,KAAKP,GACrB,GAAIA,GAAyB,iBAAbA,EAAErE,OAAqB,MAAO,CAC1C8E,KAAM,WAEF,OADIT,GAAKM,GAAKN,EAAErE,SAAQqE,OAAI,GACrB,CAAE9H,MAAO8H,GAAKA,EAAEM,KAAMI,MAAOV,EACxC,GAEJ,MAAM,IAAIwC,UAAU8c,EAAI,0BAA4B,kCACxD,EACAvnB,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQwrB,MASR,SAAeC,EAAkBC,EAAY3M,QACtB,IAAf2M,IAAyBA,EAAa,SAAUvb,GAAK,QAASA,CAAG,QAChD,IAAjB4O,IAA2BA,EAAe,MAC9C,IAAIjT,EAAQ,EACR0I,EAAMiX,EAAiB/nB,OACvBioB,EAAO,WACP,GAAI7f,GAAS0I,EACT,OAAOtO,QAAQuJ,QAAQsP,GAE3B,IAAI6M,EAAUH,EAAiB3f,KAE/B,OADc5F,QAAQuJ,QAAQmc,KACf9b,MAAK,SAAU9K,GAC1B,OAAI0mB,EAAW1mB,GACJkB,QAAQuJ,QAAQzK,GAEpB2mB,GACX,GACJ,EACA,OAAOA,GACX,EA3BA3rB,EAAQ6rB,cAmCR,SAAuBC,EAAaJ,EAAY3M,GAG5C,QAFmB,IAAf2M,IAAyBA,EAAa,SAAUvb,GAAK,QAASA,CAAG,QAChD,IAAjB4O,IAA2BA,EAAe,MACnB,IAAvB+M,EAAYpoB,OACZ,OAAOwC,QAAQuJ,QAAQsP,GAE3B,IAAIgN,EAAOD,EAAYpoB,OACnB2N,EAAS,WACT,IAAIqW,EAAK/kB,EACL6J,EAAIC,EACRsf,GAAQ,EACR,IACI,IAAK,IAAIC,EAAgB5E,EAAS0E,GAAcG,EAAkBD,EAAcxjB,QAASyjB,EAAgBxjB,KAAMwjB,EAAkBD,EAAcxjB,OAE1G,QAAhCiE,GAAMD,EADOyf,EAAgBhsB,OACTisB,cAA2B,IAAPzf,GAAyBA,EAAGnE,KAAKkE,EAElF,CACA,MAAOub,GAASL,EAAM,CAAExjB,MAAO6jB,EAAS,CACxC,QACI,IACQkE,IAAoBA,EAAgBxjB,OAAS9F,EAAKqpB,EAAchE,SAASrlB,EAAG2F,KAAK0jB,EACzF,CACA,QAAU,GAAItE,EAAK,MAAMA,EAAIxjB,KAAO,CACxC,CACJ,EACA,OAAO,IAAIgC,SAAQ,SAAUuJ,EAASC,GAClC,IAAIiY,EAAKhlB,EACT,IACI,IAAK,IAAIwpB,EAAgB/E,EAAS0E,GAAcM,EAAkBD,EAAc3jB,QAAS4jB,EAAgB3jB,KAAM2jB,EAAkBD,EAAc3jB,OAC7H4jB,EAAgBnsB,MACtB6P,MAAK,SAAU9K,KACb+mB,GAAQ,GAAKL,EAAW1mB,IAC1BqM,IACA5B,EAAQzK,IAEM,IAAT+mB,GACLtc,EAAQsP,EAEhB,IACKsN,OAAM,SAAU/lB,KACXylB,GAAQ,IACV1a,IACA3B,EAAOpJ,GAEf,GAER,CACA,MAAOkiB,GAASb,EAAM,CAAEzjB,MAAOskB,EAAS,CACxC,QACI,IACQ4D,IAAoBA,EAAgB3jB,OAAS9F,EAAKwpB,EAAcnE,SAASrlB,EAAG2F,KAAK6jB,EACzF,CACA,QAAU,GAAIxE,EAAK,MAAMA,EAAIzjB,KAAO,CACxC,CACJ,GACJ,C,eCvGApE,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQssB,UAMR,WACI,IAAI1mB,EAAQJ,KACZ,OAAO,IAAIU,SAAQ,SAAUuJ,GACzB7J,EAAM2mB,WAAU,WACZ9c,EAAQ,KACZ,GAAG,KACP,GACJ,EAZAzP,EAAQusB,UAaR,SAAmBC,EAAMC,GACrBzlB,GAAG+c,SAAS2I,KAAK1lB,GAAG2lB,SAASC,mBAAoBJ,EAAMC,EAC3D,C,eCjBA3sB,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQ6sB,QASR,SAAiBjmB,EAAMzE,EAAU0qB,GAE7B,IADA,IAAI3b,EAAO,GACFC,EAAK,EAAGA,EAAKtG,UAAUnH,OAAQyN,IACpCD,EAAKC,EAAK,GAAKtG,UAAUsG,GAE7B,IAAIoK,EAAKvM,YAAW,WAChB8d,aAAavR,GACR3U,GAASI,GAAGU,QAAQd,GAAM,KAG/BzE,SAAoDA,EAASyI,MAAMpF,KAAM,CAAC0L,IAC9E,GAAG2b,EAAS3b,GACZ,OAAOqK,CACX,C,eCnBA,SAASxJ,EAASqN,GAMd,OAAO,SAAUC,EAASzb,EAAKoN,GAC3B,IAAIsO,EAAQ,KACRC,EAAK,KAST,GARgC,mBAArBvO,EAAW/Q,OAClBqf,EAAQ,QACRC,EAAKvO,EAAW/Q,OAEe,mBAAnB+Q,EAAW1O,MACvBgd,EAAQ,MACRC,EAAKvO,EAAW1O,MAEfid,IAAOD,EACR,MAAM,IAAItb,MAAM,iBAEpBgN,EAAWsO,GAASF,EAAUG,EAAI3b,EACtC,CACJ,CA1BA9D,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQ+R,SAAWA,EACnB/R,EAAQ+sB,SAkCR,SAAkBtN,EAAOuN,GAGrB,YAFc,IAAVvN,IAAoBA,EAAQ,UACd,IAAduN,IAAwBA,GAAY,GACjCjb,GAAS,SAAUwN,EAAI3b,GAC1B,IAAIqpB,EAAU5sB,KAAKC,MACf4sB,EAAQ,KACZ,OAAO,WAEH,IADA,IAAIhc,EAAO,GACFC,EAAK,EAAGA,EAAKtG,UAAUnH,OAAQyN,IACpCD,EAAKC,GAAMtG,UAAUsG,GAEzB,IAAIgc,EAAU3nB,KACd,GAAIwnB,EAAW,CACX,IAAII,EAAU/sB,KAAKC,MACf8sB,EAAUH,GAAWxN,IACrBF,EAAG3U,MAAMuiB,EAASjc,GAClB+b,EAAUG,EAElB,MAEIN,aAAaI,GACbA,EAAQle,YAAW,WACfuQ,EAAG3U,MAAMuiB,EAASjc,EACtB,GAAGuO,EAEX,CACJ,GACJ,EA5DAzf,EAAQqtB,aAuER,SAAsBb,EAAM/M,EAAOuN,QACjB,IAAVvN,IAAoBA,EAAQ,UACd,IAAduN,IAAwBA,GAAY,GACxC,IAAIC,EAAU5sB,KAAKC,MACf4sB,EAAQ,KACZ,OAAO,WAEH,IADA,IAAIhc,EAAO,GACFC,EAAK,EAAGA,EAAKtG,UAAUnH,OAAQyN,IACpCD,EAAKC,GAAMtG,UAAUsG,GAEzB,IAAIgc,EAAU3nB,KACd,GAAIwnB,EAAW,CACX,IAAII,EAAU/sB,KAAKC,MACf8sB,EAAUH,GAAWxN,IACrB+M,EAAK5hB,MAAMuiB,EAASjc,GACpB+b,EAAUG,EAElB,MAEIN,aAAaI,GACbA,EAAQle,YAAW,WACfwd,EAAK5hB,MAAMuiB,EAASjc,EACxB,GAAGuO,EAEX,CACJ,C,qBCnGA,IAAIrQ,EAAa5J,MAAQA,KAAK4J,WAAc,SAAUC,EAASC,EAAYC,EAAGC,GAE1E,OAAO,IAAKD,IAAMA,EAAIrJ,WAAU,SAAUuJ,EAASC,GAC/C,SAASC,EAAU1P,GAAS,IAAM2P,EAAKJ,EAAUhH,KAAKvI,GAAS,CAAE,MAAOmG,GAAKsJ,EAAOtJ,EAAI,CAAE,CAC1F,SAASyJ,EAAS5P,GAAS,IAAM2P,EAAKJ,EAAiB,MAAEvP,GAAS,CAAE,MAAOmG,GAAKsJ,EAAOtJ,EAAI,CAAE,CAC7F,SAASwJ,EAAK5K,GAJlB,IAAe/E,EAIa+E,EAAOyD,KAAOgH,EAAQzK,EAAO/E,QAJ1CA,EAIyD+E,EAAO/E,MAJhDA,aAAiBsP,EAAItP,EAAQ,IAAIsP,GAAE,SAAUE,GAAWA,EAAQxP,EAAQ,KAIjB6P,KAAKH,EAAWE,EAAW,CAC7GD,GAAMJ,EAAYA,EAAU5E,MAAMyE,EAASC,GAAc,KAAK9G,OAClE,GACJ,EACIuH,EAAevK,MAAQA,KAAKuK,aAAgB,SAAUV,EAASW,GAC/D,IAAsGC,EAAGC,EAAGC,EAAxGC,EAAI,CAAEC,MAAO,EAAGC,KAAM,WAAa,GAAW,EAAPH,EAAE,GAAQ,MAAMA,EAAE,GAAI,OAAOA,EAAE,EAAI,EAAGI,KAAM,GAAIC,IAAK,IAAeC,EAAI3Q,OAAO2K,QAA4B,mBAAbiG,SAA0BA,SAAW5Q,QAAQ4F,WACtL,OAAO+K,EAAEjI,KAAOmI,EAAK,GAAIF,EAAS,MAAIE,EAAK,GAAIF,EAAU,OAAIE,EAAK,GAAsB,mBAAXzI,SAA0BuI,EAAEvI,OAAOC,UAAY,WAAa,OAAO3C,IAAM,GAAIiL,EAC1J,SAASE,EAAK3I,GAAK,OAAO,SAAUqF,GAAK,OACzC,SAAcuD,GACV,GAAIX,EAAG,MAAM,IAAI1F,UAAU,mCAC3B,KAAOkG,IAAMA,EAAI,EAAGG,EAAG,KAAOR,EAAI,IAAKA,OACnC,GAAIH,EAAI,EAAGC,IAAMC,EAAY,EAARS,EAAG,GAASV,EAAU,OAAIU,EAAG,GAAKV,EAAS,SAAOC,EAAID,EAAU,SAAMC,EAAE7H,KAAK4H,GAAI,GAAKA,EAAE1H,SAAW2H,EAAIA,EAAE7H,KAAK4H,EAAGU,EAAG,KAAKnI,KAAM,OAAO0H,EAE3J,OADID,EAAI,EAAGC,IAAGS,EAAK,CAAS,EAARA,EAAG,GAAQT,EAAElQ,QACzB2Q,EAAG,IACP,KAAK,EAAG,KAAK,EAAGT,EAAIS,EAAI,MACxB,KAAK,EAAc,OAAXR,EAAEC,QAAgB,CAAEpQ,MAAO2Q,EAAG,GAAInI,MAAM,GAChD,KAAK,EAAG2H,EAAEC,QAASH,EAAIU,EAAG,GAAIA,EAAK,CAAC,GAAI,SACxC,KAAK,EAAGA,EAAKR,EAAEI,IAAIK,MAAOT,EAAEG,KAAKM,MAAO,SACxC,QACI,MAAkBV,GAAZA,EAAIC,EAAEG,MAAY7M,OAAS,GAAKyM,EAAEA,EAAEzM,OAAS,KAAkB,IAAVkN,EAAG,IAAsB,IAAVA,EAAG,IAAW,CAAER,EAAI,EAAG,QAAU,CAC3G,GAAc,IAAVQ,EAAG,MAAcT,GAAMS,EAAG,GAAKT,EAAE,IAAMS,EAAG,GAAKT,EAAE,IAAM,CAAEC,EAAEC,MAAQO,EAAG,GAAI,KAAO,CACrF,GAAc,IAAVA,EAAG,IAAYR,EAAEC,MAAQF,EAAE,GAAI,CAAEC,EAAEC,MAAQF,EAAE,GAAIA,EAAIS,EAAI,KAAO,CACpE,GAAIT,GAAKC,EAAEC,MAAQF,EAAE,GAAI,CAAEC,EAAEC,MAAQF,EAAE,GAAIC,EAAEI,IAAI7P,KAAKiQ,GAAK,KAAO,CAC9DT,EAAE,IAAIC,EAAEI,IAAIK,MAChBT,EAAEG,KAAKM,MAAO,SAEtBD,EAAKZ,EAAK1H,KAAK+G,EAASe,EAC5B,CAAE,MAAOhK,GAAKwK,EAAK,CAAC,EAAGxK,GAAI8J,EAAI,CAAG,CAAE,QAAUD,EAAIE,EAAI,CAAG,CACzD,GAAY,EAARS,EAAG,GAAQ,MAAMA,EAAG,GAAI,MAAO,CAAE3Q,MAAO2Q,EAAG,GAAKA,EAAG,QAAK,EAAQnI,MAAM,EAC9E,CAtBgDmH,CAAK,CAAC5H,EAAGqF,GAAK,CAAG,CAuBrE,EACAvN,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQstB,gBAAa,EAQrB,IAAIA,EAA4B,WAO5B,SAASA,EAAWC,EAASC,EAAQC,GACjCjoB,KAAK+nB,QAAUA,EACf/nB,KAAKgoB,OAASA,EACdhoB,KAAKioB,QAAUA,EACfjoB,KAAKkoB,KAAO,GACZloB,KAAKmoB,OAAS,IAAIC,IAClBpoB,KAAKqoB,OACT,CA6IA,OA5IAP,EAAW5nB,UAAUmoB,MAAQ,WACzB,OAAOze,EAAU5J,UAAM,OAAQ,GAAQ,WACnC,IAAI7C,EAAIkkB,EAAM3V,EAAM4c,EAAkB3qB,EAAK4qB,EAC3C,OAAOhe,EAAYvK,MAAM,SAAUgH,GAC/B,OAAQA,EAAG6D,OACP,KAAK,EACD,IAAK7K,KAAKioB,QAAS,MAAO,CAAC,EAAa,GACxC9qB,EAAK6C,KAAKioB,QAAS5G,EAAOlkB,EAAGkkB,KAAM3V,EAAOvO,EAAGuO,KAAM4c,EAAmBnrB,EAAGmrB,iBACrEzS,MAAMwL,KACNA,EAAO,GACXra,EAAG6D,MAAQ,EACf,KAAK,EACD7D,EAAG+D,KAAK5P,KAAK,CAAC,EAAG,EAAG,CAAE,IACtB6L,EAAG6D,MAAQ,EACf,KAAK,EACD,OAAM7K,KAAKkoB,KAAKhqB,OAASmjB,EAClB,CAAC,EAAarhB,KAAK+nB,QAAQrc,IADK,CAAC,EAAa,GAEzD,KAAK,EAID,OAHA/N,EAAMqJ,EAAG8D,OACT9K,KAAKmoB,OAAOK,IAAI7qB,GAChBqC,KAAKkoB,KAAK/sB,KAAKwC,GACR,CAAC,EAAa,GACzB,KAAK,EAID,OAHI2qB,GACAA,EAAiB,MAEd,CAAC,EAAa,GACzB,KAAK,EAKD,OAJAC,EAAUvhB,EAAG8D,OACTwd,GACAA,EAAiBC,GAEd,CAAC,EAAa,GACzB,KAAK,EAAG,MAAO,CAAC,GAExB,GACJ,GACJ,EACAjuB,OAAOC,eAAeutB,EAAW5nB,UAAW,WAAY,CAIpDpD,IAAK,WACD,OAAOkD,KAAKkoB,KAAKhqB,MACrB,EACAX,YAAY,EACZC,cAAc,IAOlBsqB,EAAW5nB,UAAUuoB,SAAW,SAAU9qB,GACtC,OAAOqC,KAAKmoB,OAAO3T,IAAI7W,EAC3B,EAIAmqB,EAAW5nB,UAAUpD,IAAM,WAEvB,IADA,IAAI4O,EAAO,GACFC,EAAK,EAAGA,EAAKtG,UAAUnH,OAAQyN,IACpCD,EAAKC,GAAMtG,UAAUsG,GAEzB,OAAO/B,EAAU5J,UAAM,OAAQ,GAAQ,WACnC,IAAIrC,EACJ,OAAO4M,EAAYvK,MAAM,SAAU7C,GAC/B,OAAQA,EAAG0N,OACP,KAAK,EACD,OAAM7K,KAAKkoB,KAAKhqB,OAAS,GACzBP,EAAMqC,KAAKkoB,KAAK7c,MAChBrL,KAAKgoB,OAAOrqB,GACL,CAAC,EAAa,IAHe,CAAC,EAAa,GAItD,KAAK,EAAG,MAAO,CAAC,EAAaqC,KAAK+nB,QAAQrc,IAC1C,KAAK,EACD/N,EAAMR,EAAG2N,OACT3N,EAAG0N,MAAQ,EACf,KAAK,EAED,OADA7K,KAAKmoB,OAAOK,IAAI7qB,GACT,CAAC,EAAcA,GAElC,GACJ,GACJ,EAMAmqB,EAAW5nB,UAAUwoB,SAAW,WAG5B,IAFA,IAAItoB,EAAQJ,KACR0L,EAAO,GACFC,EAAK,EAAGA,EAAKtG,UAAUnH,OAAQyN,IACpCD,EAAKC,GAAMtG,UAAUsG,GAEzB,OAAO,IAAIjL,SAAQ,SAAUuJ,EAASC,GAAU,OAAON,EAAUxJ,OAAO,OAAQ,GAAQ,WACpF,IAAIzC,EAAKgrB,EACT,OAAOpe,EAAYvK,MAAM,SAAU7C,GAC/B,OAAQA,EAAG0N,OACP,KAAK,EACD,OAAM7K,KAAKkoB,KAAKhqB,OAAS,GACzBP,EAAMqC,KAAKkoB,KAAK7c,MAChBrL,KAAKgoB,OAAOrqB,GACZqC,KAAKmoB,OAAOK,IAAI7qB,GAChBsM,EAAQtM,GACD,CAAC,EAAa,IALe,CAAC,EAAa,GAMtD,KAAK,EAED,OADAR,EAAG4N,KAAK5P,KAAK,CAAC,EAAG,EAAG,CAAE,IACf,CAAC,EAAa6E,KAAK+nB,QAAQrc,IACtC,KAAK,EAID,OAHA/N,EAAMR,EAAG2N,OACT9K,KAAKmoB,OAAOK,IAAI7qB,GAChBsM,EAAQtM,GACD,CAAC,EAAa,GACzB,KAAK,EAGD,OAFAgrB,EAAUxrB,EAAG2N,OACbZ,EAAOye,GACA,CAAC,EAAa,GACzB,KAAK,EAAG,MAAO,CAAC,GAExB,GACJ,GAAI,GACR,EAKAb,EAAW5nB,UAAU0oB,QAAU,SAAUjrB,GACjCqC,KAAKmoB,OAAO3T,IAAI7W,KAChBqC,KAAKmoB,OAAO9W,OAAO1T,GACnBqC,KAAKkoB,KAAK/sB,KAAKwC,GAEvB,EAIAmqB,EAAW5nB,UAAUgR,MAAQ,WACzBlR,KAAKkoB,KAAKhqB,OAAS,EACnB8B,KAAKmoB,OAAOjX,OAChB,EACO4W,CACX,CA5J+B,GA6J/BttB,EAAQstB,WAAaA,C,qBC1MrB,IACQxjB,EADJC,EAAavE,MAAQA,KAAKuE,YACtBD,EAAgB,SAAUE,EAAGC,GAI7B,OAHAH,EAAgBhK,OAAOoK,gBAClB,CAAEC,UAAW,cAAgBC,OAAS,SAAUJ,EAAGC,GAAKD,EAAEG,UAAYF,CAAG,GAC1E,SAAUD,EAAGC,GAAK,IAAK,IAAII,KAAKJ,EAAOnK,OAAO4F,UAAU4E,eAAehC,KAAK2B,EAAGI,KAAIL,EAAEK,GAAKJ,EAAEI,GAAI,EAC7FP,EAAcE,EAAGC,EAC5B,EACO,SAAUD,EAAGC,GAChB,GAAiB,mBAANA,GAA0B,OAANA,EAC3B,MAAM,IAAIM,UAAU,uBAAyBxG,OAAOkG,GAAK,iCAE7D,SAASO,IAAOhF,KAAKjD,YAAcyH,CAAG,CADtCF,EAAcE,EAAGC,GAEjBD,EAAEtE,UAAkB,OAANuE,EAAanK,OAAO2K,OAAOR,IAAMO,EAAG9E,UAAYuE,EAAEvE,UAAW,IAAI8E,EACnF,GAEJ1K,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQquB,eAAiBruB,EAAQsuB,aAAU,EAI3C,IAAIA,EAAyB,WACzB,SAASA,IACL,IAAI1oB,EAAQJ,KACZA,KAAK+oB,SAAU,EACf/oB,KAAKgpB,SAAW,IAAItoB,SAAQ,SAAUC,EAAGC,GACrCR,EAAM6oB,iBAAmBtoB,CAC7B,GACJ,CAsBA,OArBArG,OAAOC,eAAeuuB,EAAQ5oB,UAAW,SAAU,CAE/CpD,IAAK,WACD,OAAOkD,KAAK+oB,OAChB,EACAxrB,YAAY,EACZC,cAAc,IAGlBsrB,EAAQ5oB,UAAUmC,MAAQ,WACtBrC,KAAK+oB,SAAU,CACnB,EAEAD,EAAQ5oB,UAAUgpB,KAAO,WACrBlpB,KAAK+oB,SAAU,EACf/oB,KAAKipB,kBAAiB,EAC1B,EAEAH,EAAQ5oB,UAAU4f,KAAO,WACrB,OAAO9f,KAAKgpB,QAChB,EACOF,CACX,CA9B4B,GA+B5BtuB,EAAQsuB,QAAUA,EAIlB,IAAID,EAAgC,SAAU1jB,GAE1C,SAAS0jB,EAAeM,GACpB,IAAI/oB,EAAQ+E,EAAOrC,KAAK9C,OAASA,KAEjC,OADAI,EAAMgpB,SAAW5f,YAAW,WAAc,OAAOpJ,EAAM8oB,MAAQ,GAAGC,GAC3D/oB,CACX,CAKA,OAVAmE,EAAUskB,EAAgB1jB,GAM1B0jB,EAAe3oB,UAAUgpB,KAAO,WAC5B5B,aAAatnB,KAAKopB,UAClBjkB,EAAOjF,UAAUgpB,KAAKpmB,KAAK9C,KAC/B,EACO6oB,CACX,CAZmC,CAYjCC,GACFtuB,EAAQquB,eAAiBA,C,eCpEzBvuB,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQ6uB,aAAU,EAClB,IAAIA,EAAyB,WACzB,SAASA,IACLrpB,KAAKspB,IAAM,EACXD,EAAQE,WAAa,EACrBvpB,KAAKspB,IAAMD,EAAQE,SACvB,CASA,OARAjvB,OAAOC,eAAe8uB,EAAQnpB,UAAW,KAAM,CAC3CpD,IAAK,WACD,OAAOkD,KAAKspB,GAChB,EACA/rB,YAAY,EACZC,cAAc,IAElB6rB,EAAQE,UAAY,EACbF,CACX,CAf4B,GAgB5B7uB,EAAQ6uB,QAAUA,C,qBClBlB,IACQ/kB,EADJC,EAAavE,MAAQA,KAAKuE,YACtBD,EAAgB,SAAUE,EAAGC,GAI7B,OAHAH,EAAgBhK,OAAOoK,gBAClB,CAAEC,UAAW,cAAgBC,OAAS,SAAUJ,EAAGC,GAAKD,EAAEG,UAAYF,CAAG,GAC1E,SAAUD,EAAGC,GAAK,IAAK,IAAII,KAAKJ,EAAOnK,OAAO4F,UAAU4E,eAAehC,KAAK2B,EAAGI,KAAIL,EAAEK,GAAKJ,EAAEI,GAAI,EAC7FP,EAAcE,EAAGC,EAC5B,EACO,SAAUD,EAAGC,GAChB,GAAiB,mBAANA,GAA0B,OAANA,EAC3B,MAAM,IAAIM,UAAU,uBAAyBxG,OAAOkG,GAAK,iCAE7D,SAASO,IAAOhF,KAAKjD,YAAcyH,CAAG,CADtCF,EAAcE,EAAGC,GAEjBD,EAAEtE,UAAkB,OAANuE,EAAanK,OAAO2K,OAAOR,IAAMO,EAAG9E,UAAYuE,EAAEvE,UAAW,IAAI8E,EACnF,GAEAmH,EAAcnM,MAAQA,KAAKmM,YAAe,SAAUC,EAAY5P,EAAQ4B,EAAKiO,GAC7E,IAA2H7H,EAAvH7D,EAAI0E,UAAUnH,OAAQ0E,EAAIjC,EAAI,EAAInE,EAAkB,OAAT6P,EAAgBA,EAAO/R,OAAOgS,yBAAyB9P,EAAQ4B,GAAOiO,EACrH,GAAuB,iBAAZ/N,SAAoD,mBAArBA,QAAQiO,SAAyB3J,EAAItE,QAAQiO,SAASH,EAAY5P,EAAQ4B,EAAKiO,QACpH,IAAK,IAAIxJ,EAAIuJ,EAAWlO,OAAS,EAAG2E,GAAK,EAAGA,KAAS2B,EAAI4H,EAAWvJ,MAAID,GAAKjC,EAAI,EAAI6D,EAAE5B,GAAKjC,EAAI,EAAI6D,EAAEhI,EAAQ4B,EAAKwE,GAAK4B,EAAEhI,EAAQ4B,KAASwE,GAChJ,OAAOjC,EAAI,GAAKiC,GAAKtI,OAAOC,eAAeiC,EAAQ4B,EAAKwE,GAAIA,CAChE,EACIN,EAAUtC,MAAQA,KAAKsC,QAAW,SAAUC,EAAGC,GAC/C,IAAIC,EAAsB,mBAAXC,QAAyBH,EAAEG,OAAOC,UACjD,IAAKF,EAAG,OAAOF,EACf,IAAmBK,EAAYhC,EAA3BiC,EAAIJ,EAAEK,KAAKP,GAAOQ,EAAK,GAC3B,IACI,WAAc,IAANP,GAAgBA,KAAM,MAAQI,EAAIC,EAAEG,QAAQC,MAAMF,EAAG5H,KAAKyH,EAAEnI,MACxE,CACA,MAAOiE,GAASkC,EAAI,CAAElC,MAAOA,EAAS,CACtC,QACI,IACQkE,IAAMA,EAAEK,OAASR,EAAII,EAAU,SAAIJ,EAAEK,KAAKD,EAClD,CACA,QAAU,GAAIjC,EAAG,MAAMA,EAAElC,KAAO,CACpC,CACA,OAAOqE,CACX,EACIymB,EAAiBxpB,MAAQA,KAAKwpB,eAAkB,SAAUC,EAAI/jB,EAAMgkB,GACpE,GAAIA,GAA6B,IAArBrkB,UAAUnH,OAAc,IAAK,IAA4B6E,EAAxBF,EAAI,EAAG+Y,EAAIlW,EAAKxH,OAAY2E,EAAI+Y,EAAG/Y,KACxEE,GAAQF,KAAK6C,IACR3C,IAAIA,EAAK6B,MAAM1E,UAAUoD,MAAMR,KAAK4C,EAAM,EAAG7C,IAClDE,EAAGF,GAAK6C,EAAK7C,IAGrB,OAAO4mB,EAAGhuB,OAAOsH,GAAM6B,MAAM1E,UAAUoD,MAAMR,KAAK4C,GACtD,EACApL,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtD,IAAI0C,EAAKqE,GAAGmoB,WAAYC,EAAUzsB,EAAGysB,QACjCC,GADqD1sB,EAAG2sB,SAC7B,SAAU3kB,GAErC,SAAS0kB,IACL,IAAIzpB,EAAQ+E,EAAOC,MAAMpF,KAAMwpB,EAAc,GAAIlnB,EAAO+C,YAAY,KAAWrF,KAE/E,OADAI,EAAM2pB,OAAS,CAAC,EACT3pB,CACX,CAqGA,OA1GAmE,EAAUslB,EAAW1kB,GAMrB7K,OAAOC,eAAesvB,EAAU3pB,UAAW,QAAS,CAChDpD,IAAK,WACD,OAAOkD,KAAK+pB,MAChB,EACAxsB,YAAY,EACZC,cAAc,IAQlBqsB,EAAU3pB,UAAU8pB,SAAW,SAAUC,EAAOjkB,EAASrJ,GACrD,GAAIqD,KAAK+pB,SAAWE,EAAO,CAEvB,IAAIC,GAAe,EACflqB,KAAK+pB,SACLG,EAAelqB,KAAKmqB,sBAAsBF,IAEA,oBAA1C3vB,OAAO4F,UAAUyF,SAAS7C,KAAKmnB,GAC/BjqB,KAAK+pB,OAASE,GAGTjqB,KAAK+pB,SACN/pB,KAAK+pB,OAASzvB,OAAO2K,OAAO,OAE3Be,EAGDhG,KAAK+pB,OAASE,EAFdjqB,KAAKyO,SAASzO,KAAK+pB,OAAQE,IAK/BC,IACAlqB,KAAKoqB,oBAAoBpqB,KAAK+pB,QAE9B/pB,KAAKqqB,SAED1tB,GACAA,IAGZ,CACJ,EAMAktB,EAAU3pB,UAAUuO,SAAW,SAAUjS,EAAQ8tB,GAC7C,IAAK,IAAIlsB,KAAOksB,EAAQ,CACpB,IAAI7vB,EAAQ6vB,EAAOlsB,GAEnB5B,EAAO4B,GAAO3D,CAClB,CACJ,EAIAovB,EAAU3pB,UAAUqqB,mBAAqB,WACzC,EAIAV,EAAU3pB,UAAUsqB,kBAAoB,WACxC,EAIAX,EAAU3pB,UAAUuqB,qBAAuB,WAC3C,EAKAZ,EAAU3pB,UAAUiqB,sBAAwB,SAAUO,GAClD,OAAO,CACX,EAMAb,EAAU3pB,UAAUkqB,oBAAsB,SAAUM,GACpD,EAMAb,EAAU3pB,UAAUyqB,mBAAqB,SAAUC,GACnD,EAIAf,EAAU3pB,UAAUmqB,OAAS,WAC7B,EACYle,EAAW,CACnByd,GACDC,EAEP,CA5G8B,CA4G5BroB,GAAGqoB,YACLrvB,EAAA,QAAkBqvB,C,eC7JlBvvB,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQqwB,qBAMR,SAA8BC,GAC1B,IAAKC,WAAWC,SACZ,MAAO,GAIX,IAFA,IACIC,EADQF,WAAWC,SAASE,OAAOC,UAAU,GAChCtsB,MAAM,KACd7D,EAAM,EAAGA,EAAMiwB,EAAK/sB,OAAQlD,IAAO,CACxC,IAAIowB,EAAOH,EAAKjwB,GAAK6D,MAAM,KAC3B,GAAIusB,EAAK,KAAON,EACZ,OAAOO,mBAAmBD,EAAK,GAEvC,CACA,MAAO,EACX,C,eCpBA9wB,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQ8wB,UAAO,EAKf,IAAIC,EAAsB,WACtB,SAASA,IACLvrB,KAAKwrB,SAAW,EACpB,CAsDA,OAhDAD,EAAKrrB,UAAUurB,IAAM,SAAUC,GAE3B,IADA,IAAIhgB,EAAO,GACFC,EAAK,EAAGA,EAAKtG,UAAUnH,OAAQyN,IACpCD,EAAKC,EAAK,GAAKtG,UAAUsG,GAGxBsJ,OAA4B,sBAC7BA,OAAO0W,oBAAsB,SAAUhvB,GACnC,IAAIivB,EAAY/wB,KAAKC,MACrB,OAAO0O,YAAW,WACd7M,EAAS,CACLkvB,YAAY,EACZC,cAAe,WACX,OAAO7vB,KAAKgQ,IAAI,EAAG,IAAMpR,KAAKC,MAAQ8wB,GAC1C,GAER,GACJ,EACA3W,OAAO8W,mBAAqB,SAAUhW,GAClCuR,aAAavR,EACjB,GAEJ/V,KAAKwrB,SAASrwB,KAAK,CACfoE,QAASmsB,EACThgB,KAAMA,IAEL1L,KAAKgsB,aACNhsB,KAAKgsB,WAAa/W,OAAO0W,oBAAoB3rB,KAAKisB,oBAAoBpb,KAAK7Q,MAAO,CAAEqnB,QAAS,KAErG,EACAkE,EAAKrrB,UAAU+rB,oBAAsB,SAAUC,GAE3C,MAAQA,EAASJ,gBAAkB,GAAKI,EAASL,aAAe7rB,KAAKwrB,SAASttB,OAAS,GAAG,CACtF,IAAIiuB,EAASnsB,KAAKwrB,SAASnnB,QAEvB8nB,EAAO5sB,SACP4sB,EAAO5sB,QAAQ6F,MAAMpF,KAAMmsB,EAAOzgB,KAE1C,CAEI1L,KAAKwrB,SAASttB,OAAS,EACvB8B,KAAKgsB,WAAa/W,OAAO0W,oBAAoB3rB,KAAKisB,oBAAoBpb,KAAK7Q,MAAO,CAAEqnB,QAAS,MAG7F0E,mBAAmB/rB,KAAKgsB,YACxBhsB,KAAKgsB,WAAa,EAE1B,EACOT,CACX,CA1DyB,GA2DzB/wB,EAAQ8wB,KAAO,IAAIC,C,eCjEnBjxB,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQ4xB,aAAU,EAClB,IAAIC,EAAyB,WACzB,SAASA,IACLrsB,KAAKssB,UAAY,CAAC,CACtB,CAiEA,OA5DAD,EAAQnsB,UAAU0L,MAAQ,SAAUxN,GAChC,IAAImuB,EAAUC,EACdxsB,KAAKssB,UAAUluB,GAAO,CAClBwtB,UAAW/wB,KAAKC,MAChB2xB,QAAS,IAAI/rB,SAAQ,SAAUuJ,EAASC,GACpCqiB,EAAWtiB,EACXuiB,EAAUtiB,CACd,IACA+f,OAAO,GAEXjqB,KAAKssB,UAAUluB,GAAK6L,QAAUsiB,EAC9BvsB,KAAKssB,UAAUluB,GAAK8L,OAASsiB,CACjC,EAKAH,EAAQnsB,UAAUwsB,IAAM,SAAUtuB,GAC9B,IAAIuuB,EAAW3sB,KAAKssB,UAAUluB,GAC1BuuB,GACAA,EAASC,QAAU/xB,KAAKC,MACxB6xB,EAAS1C,OAAQ,GAGjB9qB,QAAQT,MAAM,WAAWjD,OAAO2C,EAAK,qBAE7C,EAKAiuB,EAAQnsB,UAAU4f,KAAO,SAAU1hB,EAAK7B,GACpC,IAAIowB,EAAW3sB,KAAKssB,UAAUluB,GAC9B,GAAIuuB,EAAU,CACV,IAAIE,EAAYF,EAAS1iB,QAAS6iB,EAAWH,EAASziB,OAuBtD,OAtBIyiB,EAAS1C,MACL4C,GACAA,EAAUF,EAAS1C,OAIvB0C,EAASI,SAAWC,aAAY,WACxBL,EAAS1C,OACTgD,cAAcN,EAASI,UACvBF,EAAUzuB,IAIN7B,IAAWsZ,MAAMtZ,EAAO8qB,UACpBxsB,KAAKC,MAAQ6xB,EAASf,UAAYrvB,EAAO8qB,UACzC4F,cAAcN,EAASI,UACvBD,EAAS,YAAYrxB,OAAO2C,EAAK,iBAIjD,GAAG,IAEAuuB,EAASF,OACpB,CACJ,EACOJ,CACX,CArE4B,GAsE5B7xB,EAAQ4xB,QAAU,IAAIC,C,qECxEtB,IAAIziB,UAAa5J,MAAQA,KAAK4J,WAAc,SAAUC,EAASC,EAAYC,EAAGC,GAE1E,OAAO,IAAKD,IAAMA,EAAIrJ,WAAU,SAAUuJ,EAASC,GAC/C,SAASC,EAAU1P,GAAS,IAAM2P,EAAKJ,EAAUhH,KAAKvI,GAAS,CAAE,MAAOmG,GAAKsJ,EAAOtJ,EAAI,CAAE,CAC1F,SAASyJ,EAAS5P,GAAS,IAAM2P,EAAKJ,EAAiB,MAAEvP,GAAS,CAAE,MAAOmG,GAAKsJ,EAAOtJ,EAAI,CAAE,CAC7F,SAASwJ,EAAK5K,GAJlB,IAAe/E,EAIa+E,EAAOyD,KAAOgH,EAAQzK,EAAO/E,QAJ1CA,EAIyD+E,EAAO/E,MAJhDA,aAAiBsP,EAAItP,EAAQ,IAAIsP,GAAE,SAAUE,GAAWA,EAAQxP,EAAQ,KAIjB6P,KAAKH,EAAWE,EAAW,CAC7GD,GAAMJ,EAAYA,EAAU5E,MAAMyE,EAASC,GAAc,KAAK9G,OAClE,GACJ,EACIuH,YAAevK,MAAQA,KAAKuK,aAAgB,SAAUV,EAASW,GAC/D,IAAsGC,EAAGC,EAAGC,EAAxGC,EAAI,CAAEC,MAAO,EAAGC,KAAM,WAAa,GAAW,EAAPH,EAAE,GAAQ,MAAMA,EAAE,GAAI,OAAOA,EAAE,EAAI,EAAGI,KAAM,GAAIC,IAAK,IAAeC,EAAI3Q,OAAO2K,QAA4B,mBAAbiG,SAA0BA,SAAW5Q,QAAQ4F,WACtL,OAAO+K,EAAEjI,KAAOmI,EAAK,GAAIF,EAAS,MAAIE,EAAK,GAAIF,EAAU,OAAIE,EAAK,GAAsB,mBAAXzI,SAA0BuI,EAAEvI,OAAOC,UAAY,WAAa,OAAO3C,IAAM,GAAIiL,EAC1J,SAASE,EAAK3I,GAAK,OAAO,SAAUqF,GAAK,OACzC,SAAcuD,GACV,GAAIX,EAAG,MAAM,IAAI1F,UAAU,mCAC3B,KAAOkG,IAAMA,EAAI,EAAGG,EAAG,KAAOR,EAAI,IAAKA,OACnC,GAAIH,EAAI,EAAGC,IAAMC,EAAY,EAARS,EAAG,GAASV,EAAU,OAAIU,EAAG,GAAKV,EAAS,SAAOC,EAAID,EAAU,SAAMC,EAAE7H,KAAK4H,GAAI,GAAKA,EAAE1H,SAAW2H,EAAIA,EAAE7H,KAAK4H,EAAGU,EAAG,KAAKnI,KAAM,OAAO0H,EAE3J,OADID,EAAI,EAAGC,IAAGS,EAAK,CAAS,EAARA,EAAG,GAAQT,EAAElQ,QACzB2Q,EAAG,IACP,KAAK,EAAG,KAAK,EAAGT,EAAIS,EAAI,MACxB,KAAK,EAAc,OAAXR,EAAEC,QAAgB,CAAEpQ,MAAO2Q,EAAG,GAAInI,MAAM,GAChD,KAAK,EAAG2H,EAAEC,QAASH,EAAIU,EAAG,GAAIA,EAAK,CAAC,GAAI,SACxC,KAAK,EAAGA,EAAKR,EAAEI,IAAIK,MAAOT,EAAEG,KAAKM,MAAO,SACxC,QACI,MAAkBV,GAAZA,EAAIC,EAAEG,MAAY7M,OAAS,GAAKyM,EAAEA,EAAEzM,OAAS,KAAkB,IAAVkN,EAAG,IAAsB,IAAVA,EAAG,IAAW,CAAER,EAAI,EAAG,QAAU,CAC3G,GAAc,IAAVQ,EAAG,MAAcT,GAAMS,EAAG,GAAKT,EAAE,IAAMS,EAAG,GAAKT,EAAE,IAAM,CAAEC,EAAEC,MAAQO,EAAG,GAAI,KAAO,CACrF,GAAc,IAAVA,EAAG,IAAYR,EAAEC,MAAQF,EAAE,GAAI,CAAEC,EAAEC,MAAQF,EAAE,GAAIA,EAAIS,EAAI,KAAO,CACpE,GAAIT,GAAKC,EAAEC,MAAQF,EAAE,GAAI,CAAEC,EAAEC,MAAQF,EAAE,GAAIC,EAAEI,IAAI7P,KAAKiQ,GAAK,KAAO,CAC9DT,EAAE,IAAIC,EAAEI,IAAIK,MAChBT,EAAEG,KAAKM,MAAO,SAEtBD,EAAKZ,EAAK1H,KAAK+G,EAASe,EAC5B,CAAE,MAAOhK,GAAKwK,EAAK,CAAC,EAAGxK,GAAI8J,EAAI,CAAG,CAAE,QAAUD,EAAIE,EAAI,CAAG,CACzD,GAAY,EAARS,EAAG,GAAQ,MAAMA,EAAG,GAAI,MAAO,CAAE3Q,MAAO2Q,EAAG,GAAKA,EAAG,QAAK,EAAQnI,MAAM,EAC9E,CAtBgDmH,CAAK,CAAC5H,EAAGqF,GAAK,CAAG,CAuBrE,EACAvN,OAAOC,eAAeC,QAAS,aAAc,CAAEC,OAAO,IACtDD,QAAQ6d,WAAQ,EAChB,IAAIlD,kBAAoB,oBAAQ,MAe5BkD,MAAuB,WACvB,SAASA,QACLrY,KAAK+pB,OAAS,CAAC,EACf/pB,KAAKktB,OAAS,CAAC,EACfltB,KAAKugB,SAAU,EACfvgB,KAAKmtB,WAAa,EACtB,CA+KA,OA9KA7yB,OAAOC,eAAe8d,MAAO,wBAAyB,CAIlDvb,IAAK,WACD,OAAOub,MAAM+U,sBACjB,EACA7vB,YAAY,EACZC,cAAc,IAElBlD,OAAOC,eAAe8d,MAAMnY,UAAW,KAAM,CAIzCpD,IAAK,WACD,OAAOkD,KAAKspB,GAChB,EACA/rB,YAAY,EACZC,cAAc,IAElBlD,OAAOC,eAAe8d,MAAMnY,UAAW,YAAa,CAKhDpD,IAAK,WACD,OAAOkD,KAAKqtB,UAChB,EACA9vB,YAAY,EACZC,cAAc,IAElBlD,OAAOC,eAAe8d,MAAMnY,UAAW,YAAa,CAIhDpD,IAAK,WACD,OAAO2Y,aAAazV,KAAKjD,YAC7B,EACAQ,YAAY,EACZC,cAAc,IAElBlD,OAAOC,eAAe8d,MAAMnY,UAAW,QAAS,CAI5CpD,IAAK,WACD,OAAOkD,KAAK+pB,MAChB,EACAxsB,YAAY,EACZC,cAAc,IAMlB6a,MAAMnY,UAAUjF,KAAO,WACnB,MAAO,CAAC,CACZ,EACAX,OAAOC,eAAe8d,MAAMnY,UAAW,QAAS,CAI5CpD,IAAK,WACD,OAAOkD,KAAKktB,MAChB,EACA3vB,YAAY,EACZC,cAAc,IAElBlD,OAAOC,eAAe8d,MAAMnY,UAAW,SAAU,CAI7CpD,IAAK,WACD,IAAIyjB,QAAUvgB,KAAK0V,OAASpb,OAAOqF,KAAKK,KAAK0V,OAAOxX,OAAS,EAC7D,IAAK8B,KAAKqtB,WACN,OAAO9M,QAEX,IAEI,OAAOA,SAAW+M,KAAKttB,KAAKqtB,WAChC,CACA,MAAO3uB,GAEH,OADAS,QAAQT,MAAM,gBAAkFA,EAAMC,OAC/F4hB,OACX,CACJ,EAIAvjB,IAAK,SAAUvC,GACX,IAAI0C,EACA6C,KAAKugB,UAAY9lB,IACjBuF,KAAKugB,QAAU9lB,EACXA,GAEAuF,KAAKktB,OAAyF,QAA/E/vB,EAAKgY,kBAAkBC,gBAAgBC,mBAAmBrV,KAAKmW,kBAA+B,IAAPhZ,OAAgB,EAASA,EAAGwY,MAClI3V,KAAKutB,aAGLvtB,KAAKktB,OAAS,CAAC,EACfltB,KAAKwtB,aAGjB,EACAjwB,YAAY,EACZC,cAAc,IASlB6a,MAAMnY,UAAU6X,kBAAoB,WAChC,OAAO,IACX,EAKAM,MAAMnY,UAAUutB,aAAe,WAC3B,OAAOztB,KAAKmtB,UAChB,EAIA9U,MAAMnY,UAAU4X,SAAW,WAC3B,EAIAO,MAAMnY,UAAUqtB,SAAW,WAC3B,EAIAlV,MAAMnY,UAAUstB,UAAY,WAC5B,EAOAnV,MAAMnY,UAAU8pB,SAAW,SAAUC,GACjC,IAAK,IAAI7rB,KAAO6rB,EAAO,CACnB,IAAIxvB,EAAQwvB,EAAM7rB,GAClB4B,KAAK+pB,OAAO3rB,GAAO3D,CACvB,CACJ,EAKA4d,MAAMnY,UAAUwW,SAAW,SAAUla,GACjC,OAAOoN,UAAU5J,UAAM,OAAQ,GAAQ,WACnC,OAAOuK,YAAYvK,MAAM,SAAU7C,GAC/B,MAAO,CAAC,EACZ,GACJ,GACJ,EAMAkb,MAAMqV,cAAgB,SAAUC,EAAkBC,EAAWC,GACpD7tB,KAAKotB,uBAAuB5Y,IAAImZ,IACjC3tB,KAAKotB,uBAAuBpwB,IAAI2wB,EAAkB,IAEtD3tB,KAAKotB,uBAAuBtwB,IAAI6wB,GAAkBxyB,KAAK,CAAEyyB,UAAWA,EAAWC,QAASA,GAC5F,EACAxV,MAAM+U,uBAAyB,IAAI/tB,IAC5BgZ,KACX,CAtL0B,GAuL1B7d,QAAQ6d,MAAQA,K,uBC5OhB,IAAIzO,EAAa5J,MAAQA,KAAK4J,WAAc,SAAUC,EAASC,EAAYC,EAAGC,GAE1E,OAAO,IAAKD,IAAMA,EAAIrJ,WAAU,SAAUuJ,EAASC,GAC/C,SAASC,EAAU1P,GAAS,IAAM2P,EAAKJ,EAAUhH,KAAKvI,GAAS,CAAE,MAAOmG,GAAKsJ,EAAOtJ,EAAI,CAAE,CAC1F,SAASyJ,EAAS5P,GAAS,IAAM2P,EAAKJ,EAAiB,MAAEvP,GAAS,CAAE,MAAOmG,GAAKsJ,EAAOtJ,EAAI,CAAE,CAC7F,SAASwJ,EAAK5K,GAJlB,IAAe/E,EAIa+E,EAAOyD,KAAOgH,EAAQzK,EAAO/E,QAJ1CA,EAIyD+E,EAAO/E,MAJhDA,aAAiBsP,EAAItP,EAAQ,IAAIsP,GAAE,SAAUE,GAAWA,EAAQxP,EAAQ,KAIjB6P,KAAKH,EAAWE,EAAW,CAC7GD,GAAMJ,EAAYA,EAAU5E,MAAMyE,EAASC,GAAc,KAAK9G,OAClE,GACJ,EACIuH,EAAevK,MAAQA,KAAKuK,aAAgB,SAAUV,EAASW,GAC/D,IAAsGC,EAAGC,EAAGC,EAAxGC,EAAI,CAAEC,MAAO,EAAGC,KAAM,WAAa,GAAW,EAAPH,EAAE,GAAQ,MAAMA,EAAE,GAAI,OAAOA,EAAE,EAAI,EAAGI,KAAM,GAAIC,IAAK,IAAeC,EAAI3Q,OAAO2K,QAA4B,mBAAbiG,SAA0BA,SAAW5Q,QAAQ4F,WACtL,OAAO+K,EAAEjI,KAAOmI,EAAK,GAAIF,EAAS,MAAIE,EAAK,GAAIF,EAAU,OAAIE,EAAK,GAAsB,mBAAXzI,SAA0BuI,EAAEvI,OAAOC,UAAY,WAAa,OAAO3C,IAAM,GAAIiL,EAC1J,SAASE,EAAK3I,GAAK,OAAO,SAAUqF,GAAK,OACzC,SAAcuD,GACV,GAAIX,EAAG,MAAM,IAAI1F,UAAU,mCAC3B,KAAOkG,IAAMA,EAAI,EAAGG,EAAG,KAAOR,EAAI,IAAKA,OACnC,GAAIH,EAAI,EAAGC,IAAMC,EAAY,EAARS,EAAG,GAASV,EAAU,OAAIU,EAAG,GAAKV,EAAS,SAAOC,EAAID,EAAU,SAAMC,EAAE7H,KAAK4H,GAAI,GAAKA,EAAE1H,SAAW2H,EAAIA,EAAE7H,KAAK4H,EAAGU,EAAG,KAAKnI,KAAM,OAAO0H,EAE3J,OADID,EAAI,EAAGC,IAAGS,EAAK,CAAS,EAARA,EAAG,GAAQT,EAAElQ,QACzB2Q,EAAG,IACP,KAAK,EAAG,KAAK,EAAGT,EAAIS,EAAI,MACxB,KAAK,EAAc,OAAXR,EAAEC,QAAgB,CAAEpQ,MAAO2Q,EAAG,GAAInI,MAAM,GAChD,KAAK,EAAG2H,EAAEC,QAASH,EAAIU,EAAG,GAAIA,EAAK,CAAC,GAAI,SACxC,KAAK,EAAGA,EAAKR,EAAEI,IAAIK,MAAOT,EAAEG,KAAKM,MAAO,SACxC,QACI,MAAkBV,GAAZA,EAAIC,EAAEG,MAAY7M,OAAS,GAAKyM,EAAEA,EAAEzM,OAAS,KAAkB,IAAVkN,EAAG,IAAsB,IAAVA,EAAG,IAAW,CAAER,EAAI,EAAG,QAAU,CAC3G,GAAc,IAAVQ,EAAG,MAAcT,GAAMS,EAAG,GAAKT,EAAE,IAAMS,EAAG,GAAKT,EAAE,IAAM,CAAEC,EAAEC,MAAQO,EAAG,GAAI,KAAO,CACrF,GAAc,IAAVA,EAAG,IAAYR,EAAEC,MAAQF,EAAE,GAAI,CAAEC,EAAEC,MAAQF,EAAE,GAAIA,EAAIS,EAAI,KAAO,CACpE,GAAIT,GAAKC,EAAEC,MAAQF,EAAE,GAAI,CAAEC,EAAEC,MAAQF,EAAE,GAAIC,EAAEI,IAAI7P,KAAKiQ,GAAK,KAAO,CAC9DT,EAAE,IAAIC,EAAEI,IAAIK,MAChBT,EAAEG,KAAKM,MAAO,SAEtBD,EAAKZ,EAAK1H,KAAK+G,EAASe,EAC5B,CAAE,MAAOhK,GAAKwK,EAAK,CAAC,EAAGxK,GAAI8J,EAAI,CAAG,CAAE,QAAUD,EAAIE,EAAI,CAAG,CACzD,GAAY,EAARS,EAAG,GAAQ,MAAMA,EAAG,GAAI,MAAO,CAAE3Q,MAAO2Q,EAAG,GAAKA,EAAG,QAAK,EAAQnI,MAAM,EAC9E,CAtBgDmH,CAAK,CAAC5H,EAAGqF,GAAK,CAAG,CAuBrE,EACAvN,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQszB,QAAK,EACb,IAAIC,EAAc,EAAQ,MACtBjuB,EAAc,EAAQ,MACtBguB,EAAoB,WACpB,SAASA,IACT,CA2aA,OAzaAA,EAAGE,eAAiB,SAAU1lB,GACJ,iBAAXA,EACPtI,KAAKiuB,YAAY1oB,IAAM+C,EAGvBtI,KAAKiuB,YAAc3lB,CAE3B,EAMAwlB,EAAGI,gBAAkB,SAAU5lB,GAC3B,OAAOsB,EAAU5J,UAAM,OAAQ,GAAQ,WACnC,IAAImuB,EAAc5F,EAClB,OAAOhe,EAAYvK,MAAM,SAAU7C,GAC/B,OAAQA,EAAG0N,OACP,KAAK,EACD,IAAKvC,IAAWA,EAAO/C,IACnB,MAAM,IAAI/G,MAAM,iBAEpBrB,EAAG0N,MAAQ,EACf,KAAK,EAED,OADA1N,EAAG4N,KAAK5P,KAAK,CAAC,EAAG,EAAG,CAAE,IACjBmN,EAAOhI,YACZ6tB,EAAe,CAAE7tB,WAAYgI,EAAOhI,YAChCgI,EAAO2F,UACPkgB,EAAalgB,QAAU3F,EAAO2F,SAE3B,CAAC,EAAanO,EAAY8B,UAAUwsB,kBAAkBD,EAAc7lB,EAAO/C,IAAK/D,GAAGM,UAL3D,CAAC,EAAa,GAMjD,KAAK,EAAG,MAAO,CAAC,EAAc3E,EAAG2N,QACjC,KAAK,EAAG,MAAO,CAAC,EAAahL,EAAY8B,UAAUysB,UAAU/lB,EAAO/C,IAAK/D,GAAGM,SAC5E,KAAK,EAAG,MAAO,CAAC,EAAc3E,EAAG2N,QACjC,KAAK,EAAG,MAAO,CAAC,EAAa,GAC7B,KAAK,EAGD,MAFAyd,EAAUprB,EAAG2N,OACb3L,QAAQT,MAAM,iBAAoDjD,OAAO6M,EAAO/C,KAAMgjB,GAChFA,EACV,KAAK,EAAG,MAAO,CAAC,GAExB,GACJ,GACJ,EAIAuF,EAAGQ,QAAU,WACT,IAAK,IAAIlwB,KAAO4B,KAAKuuB,QAAS,CAC1B,IAAItzB,EAAO6yB,EAAGS,QAAQnwB,GAClBnD,EAAKmG,OACLnG,EAAKmG,KAAKC,QAAS,EAE3B,CACJ,EAIAysB,EAAGpO,cAAgB,WACf,IAEI1f,KAAKsuB,UAELtuB,KAAKuuB,QAAU,CAAC,EAChBvuB,KAAKwuB,YAAc,CAAC,EACpBxuB,KAAKyuB,eAAiB,CAAC,EACvBzuB,KAAK8e,SAAW,CAAC,EACjB9e,KAAK0uB,SAAW,KACZ3wB,UACAoB,QAAQC,IAAI,iBAEpB,CACA,MAAOV,GACHS,QAAQT,MAAM,oBAAqBA,EACvC,CACJ,EAIAovB,EAAGa,YAAc,SAAUppB,GACvB,IAAK,IAAInH,KAAO4B,KAAKuuB,QACjB,GAAInwB,GAAOmH,EAAK,CACZ,IAAItK,EAAO6yB,EAAGS,QAAQnwB,GACtB,GAAInD,EAAKmG,KACL,OAAOnG,EAAKmG,KAAKC,MAEzB,CAEJ,OAAO,CACX,EAKAysB,EAAGc,WAAa,SAAUtmB,GACtBtI,KAAKyuB,eAAenmB,EAAO/C,MAAO,CACtC,EACAuoB,EAAGe,WAAa,SAAUtpB,UACfuoB,EAAGS,QAAQhpB,UACXvF,KAAKwuB,YAAYjpB,UACjBvF,KAAKyuB,eAAelpB,EAC/B,EAMAuoB,EAAGzkB,iBAAmB,SAAUuO,EAAOjb,GAC9BqD,KAAK8uB,OAAOlX,KACb5X,KAAK8uB,OAAOlX,GAAS,IAEzB5X,KAAK8uB,OAAOlX,GAAOzc,KAAKwB,EAC5B,EACAmxB,EAAGiB,oBAAsB,SAAUnX,EAAOjb,GACtC,GAAIqD,KAAK8uB,OAAOlX,GAAQ,CACpB,IAAItR,EAAQtG,KAAK8uB,OAAOlX,GAAOrR,QAAQ5J,IACzB,GAAV2J,GACAtG,KAAK8uB,OAAOlX,GAAOoX,OAAO1oB,EAAO,EAEzC,CACJ,EAcAwnB,EAAGmB,KAAO,SAAU3mB,EAAQ7H,GACxB,IAAIL,EAAQJ,KACZ,OAAO,IAAIU,SAAQ,SAAUC,EAAGC,GAAK,OAAOgJ,EAAUxJ,OAAO,OAAQ,GAAQ,WACzE,IAAI8uB,EAAInuB,EAAOouB,EAAYC,EAAwBC,EAAcr0B,EAAcs0B,EAAWC,EAA4BhuB,EAAciuB,EAAYxrB,EAAc2kB,EAAS8G,EACvK,OAAOllB,EAAYvK,MAAM,SAAU7C,GAC/B,OAAQA,EAAG0N,OACP,KAAK,EAWD,GAVI9M,UACoB,gBAAhBuK,EAAOR,MACP3I,QAAQC,IAAI,mBAAoBkJ,EAAOR,MAG1C9H,KAAK8e,SAASxW,EAAO/C,OACtBvF,KAAK8e,SAASxW,EAAO/C,KAAO,IAEhCvF,KAAK8e,SAASxW,EAAO/C,KAAKpK,KAAKwF,GAE3BX,KAAKwuB,YAAYlmB,EAAO/C,KACxB,MAAO,CAAC,GAEZpI,EAAG0N,MAAQ,EACf,KAAK,EAQD,OAPA1N,EAAG4N,KAAK5P,KAAK,CAAC,EAAG,EAAG,CAAE,IACtBsF,EAASA,GAAkBstB,EAAYne,YACvCsf,OAAK,EAEDpB,EAAGS,QAAQjmB,EAAO/C,OAAS/D,GAAGU,QAAQ4rB,EAAGS,QAAQjmB,EAAO/C,KAAKnE,cACtD0sB,EAAGS,QAAQjmB,EAAO/C,KAEvBuoB,EAAGS,QAAQjmB,EAAO/C,KAAa,CAAC,EAAa,IACnDvF,KAAKwuB,YAAYlmB,EAAO/C,MAAO,EACxB,CAAC,EAAavF,KAAKkuB,gBAAgB5lB,KAC9C,KAAK,EAuBD,OAtBAvH,EAAQ5D,EAAG2N,QACXokB,EAAKQ,YAAY3uB,IACF,WAAIuH,EAMfA,EAAOqnB,MAAQ3vB,KAAK0uB,UACpB1uB,KAAK4vB,KAAK5vB,KAAK0uB,UAEfpmB,EAAOqnB,OACP3vB,KAAK0uB,SAAWQ,GAGhBlvB,KAAKyuB,eAAenmB,EAAO/C,OAC3BvF,KAAKwuB,YAAYlmB,EAAO/C,MAAO,SACxBvF,KAAKyuB,eAAenmB,EAAO/C,MAEtCuoB,EAAGS,QAAQjmB,EAAO/C,KAAO,CAAC,EAC1BuoB,EAAGS,QAAQjmB,EAAO/C,KAAKnE,KAAO8tB,EAC9BpB,EAAGS,QAAQjmB,EAAO/C,KAAKsqB,KAAOvnB,EAAOunB,KAChCvnB,EAAOwnB,MACL,CAAC,EAAa9vB,KAAKkuB,gBAAgBluB,KAAKiuB,cADrB,CAAC,EAAa,GAE5C,KAAK,EACDkB,EAAahyB,EAAG2N,OAChBskB,EAAcM,YAAYP,GAC1B1uB,EAAOkB,SAASytB,GACL5yB,EAIR0yB,EAHCE,EAAYW,GAAGvuB,GAAGmH,KAAKqnB,UAAUC,aAAa,SAAUrY,GACpDkW,EAAG8B,KAAKpzB,EACZ,IAEJsxB,EAAGS,QAAQjmB,EAAO/C,KAAKuqB,MAAQV,EAC/BjyB,EAAG0N,MAAQ,EACf,KAAK,EAkBD,IAjBiB,MAAbqkB,EAAGzuB,OACHA,EAAOkB,SAASutB,GAGZA,EAAGzuB,QAAUA,GACbyuB,EAAGgB,UAAUzvB,GAGrBT,KAAKwuB,YAAYlmB,EAAO/C,MAAO,EACZ,MAAf+C,EAAOunB,MAEU,OADjBN,EAAYL,EAAGxtB,aAAa4G,EAAOunB,SAE/BN,EAAYL,EAAGrmB,aAAaP,EAAOunB,OAI3CR,EAAervB,KAAK8uB,OAAe,OAC9B9zB,EAAM,EAAGA,GAAOq0B,aAAmD,EAASA,EAAanxB,QAASlD,IACzFq0B,EAAar0B,GACfoK,MAAMpF,KAAM,CAACsI,IAEzB,MAAO,CAAC,EAAa,GACzB,KAAK,EACDtI,KAAKwuB,YAAYlmB,EAAO/C,MAAO,EAC/B2pB,EAAKpB,EAAGS,QAAQjmB,EAAO/C,KAAKnE,KAMxBkH,EAAOqnB,MAAQ3vB,KAAK0uB,UAAYQ,IAAOlvB,KAAK0uB,UAC5C1uB,KAAK4vB,KAAK5vB,KAAK0uB,UAEfpmB,EAAOqnB,OACP3vB,KAAK0uB,SAAWQ,GAEhB5mB,EAAOwnB,SACPR,EAAYxB,EAAGS,QAAQjmB,EAAO/C,KAAKuqB,OACpBrvB,QACXA,EAAOkB,SAAS2tB,IAGxBJ,EAAG7tB,QAAS,EACZlE,EAAG0N,MAAQ,EACf,KAAK,EAWD,GAVIqkB,EAAGzuB,QAAUA,GACbyuB,EAAGgB,UAAUzvB,GAEjBqtB,EAAGS,QAAQjmB,EAAO/C,KAAK9E,OAASA,EAChCyuB,EAAGiB,gBAAgB1vB,EAAO2vB,eACP,MAAf9nB,EAAOunB,OAEPN,OADAA,EAAYL,EAAGxtB,aAAa4G,EAAOunB,QACmBN,EAAUN,QAEpEQ,EAAkBzvB,KAAK8e,SAASxW,EAAO/C,KAClB,CACjB,IAAKhE,EAAM,EAAGA,EAAMkuB,EAAgBvxB,OAAQqD,KACxC0I,EAAUwlB,EAAgBluB,IAClB2tB,GAGZO,EAAgBvxB,OAAS,CAC7B,CAEA,IADAsxB,EAAaxvB,KAAK8uB,OAAa,KAC1B9qB,EAAM,EAAGA,GAAOwrB,aAA+C,EAASA,EAAWtxB,QAAS8F,IACnFwrB,EAAWxrB,GACboB,MAAMpF,KAAM,CAACsI,IAEzB,MAAO,CAAC,EAAa,GACzB,KAAK,EAWD,OAVAqgB,EAAUxrB,EAAG2N,OACb3L,QAAQT,MAAM,gBAAoCjD,OAAO6M,EAAOR,KAAM,MAAMrM,OAAO6M,EAAO/C,IAAK,KAAMojB,GACrG3oB,KAAKwuB,YAAYlmB,EAAO/C,MAAO,SAExBvF,KAAKyuB,eAAenmB,EAAO/C,MAClCkqB,EAAkBzvB,KAAK8e,SAASxW,EAAO/C,QAEnCkqB,EAAgBvxB,OAAS,GAE7B0C,EAAE+nB,GACK,CAAC,EAAa,GACzB,KAAK,EAAG,MAAO,CAAC,GAzFZ,IAAWnsB,CA2FvB,GACJ,GAAI,GACR,EAOAsxB,EAAGuC,WAAa,SAAU/nB,EAAQgoB,EAAS7vB,GACvC,IACI,IAAI8vB,EAAYb,YAAYY,GACvBxC,EAAGS,QAAQjmB,EAAO/C,OACnBuoB,EAAGS,QAAQjmB,EAAO/C,KAAO,CAAC,GAE9BuoB,EAAGS,QAAQjmB,EAAO/C,KAAKnE,KAAOmvB,EAC9B9vB,EAAOkB,SAAS4uB,GAChBzC,EAAGS,QAAQjmB,EAAO/C,KAAK9E,OAASA,EAChC8vB,EAAUJ,gBAAgBI,EAAU9vB,OAAO2vB,eAC3CG,EAAUlvB,QAAS,CACvB,CACA,MAAO3C,GACHS,QAAQT,MAAM,sBAAuBA,EACzC,CACJ,EASAovB,EAAGpsB,aAAe,SAAU4G,EAAQ7H,GAChC,OAAOmJ,EAAU5J,UAAM,OAAQ,GAAQ,WACnC,IAAI/E,EAAMmG,EAAMa,EAChB,OAAOsI,EAAYvK,MAAM,SAAU7C,GAC/B,OAAKmL,IAILrN,EAAO+E,KAAKuuB,QAAQjmB,EAAO/C,QAEvBnE,EAAOnG,EAAKmG,MAMZkH,EAAOrG,KACHb,GACAa,EAAOb,EAAKM,aAAa4G,EAAOrG,MAChCjC,KAAKuuB,QAAQjmB,EAAO/C,KAAKtD,KAAOA,EACzB,CAAC,EAAcA,IAGf,CAAC,OAAcsG,IAI1BpJ,QAAQT,MAAM,QAAiCjD,OAAO6M,EAAQ,YACvD,CAAC,OAAcC,MAvBtBpJ,QAAQT,MAAM,uBACP,CAAC,OAAc6J,GAyB9B,GACJ,GACJ,EAKAulB,EAAG8B,KAAO,SAAUpzB,GAChB,IAAI8L,GAOAA,OALAA,EADA9L,aAAkBgF,GAAGqoB,UACZrtB,EAAO4E,KAAiB,WAGxB5E,EAAmB,iBAEW,EAAS8L,EAAO/C,MACvDvF,KAAKwwB,OAAOloB,EAEpB,EACAwlB,EAAG0C,OAAS,SAAUloB,GAClB,IAAIlI,EAAQJ,KACRkvB,EAAKpB,EAAGS,QAAQjmB,EAAO/C,KAC3B,GAAK2pB,EAAL,CAII,GAAmB,MAAf5mB,EAAOunB,KAAc,CACrB,IAAIN,EAAYL,EAAG9tB,KAAKM,aAAa4G,EAAOunB,MAC3B,MAAbN,GACAA,EAAUkB,oBAAsB,WAC5BvB,EAAG9tB,KAAKC,QAAS,EACjBjB,EAAMswB,cAAcpoB,EACxB,EACAinB,EAAUK,SAGVV,EAAG9tB,KAAKC,QAAS,EACjBrB,KAAK0wB,cAAcpoB,GAE3B,MAEI4mB,EAAG9tB,KAAKC,QAAS,EACjBrB,KAAK0wB,cAAcpoB,GAEnBA,EAAOwnB,OACPZ,EAAGY,MAAMI,UAAU,KAE3B,CACJ,EACApC,EAAG4C,cAAgB,SAAUpoB,GAEzB,IADA,IAAIqoB,EAAc3wB,KAAK8uB,OAAc,MAC5BvW,EAAM,EAAGA,GAAOoY,aAAiD,EAASA,EAAYzyB,QAASqa,IACtFoY,EAAYpY,GAClBnT,MAAMpF,KAAM,CAACsI,GAE7B,EACAwlB,EAAGtc,UAAY,SAAUZ,GACrBA,EAAMiQ,SAASjhB,SAAQ,SAAUgxB,GAC7BA,EAAIvvB,QAAS,CACjB,GACJ,EAEAysB,EAAGS,QAAU,CAAC,EAEdT,EAAGU,YAAc,CAAC,EAElBV,EAAGW,eAAiB,CAAC,EACrBX,EAAGgB,OAAS,CAAC,EACbhB,EAAGhP,SAAW,CAAC,EACfgP,EAAGG,YAAc,CACbnmB,KAAM,QACNvC,IAAK,sBACLsqB,KAAM,KACNC,OAAO,GAEJhC,CACX,CA9auB,GA+avBtzB,EAAQszB,GAAKA,C,qBCvdb,IAAIlkB,EAAa5J,MAAQA,KAAK4J,WAAc,SAAUC,EAASC,EAAYC,EAAGC,GAE1E,OAAO,IAAKD,IAAMA,EAAIrJ,WAAU,SAAUuJ,EAASC,GAC/C,SAASC,EAAU1P,GAAS,IAAM2P,EAAKJ,EAAUhH,KAAKvI,GAAS,CAAE,MAAOmG,GAAKsJ,EAAOtJ,EAAI,CAAE,CAC1F,SAASyJ,EAAS5P,GAAS,IAAM2P,EAAKJ,EAAiB,MAAEvP,GAAS,CAAE,MAAOmG,GAAKsJ,EAAOtJ,EAAI,CAAE,CAC7F,SAASwJ,EAAK5K,GAJlB,IAAe/E,EAIa+E,EAAOyD,KAAOgH,EAAQzK,EAAO/E,QAJ1CA,EAIyD+E,EAAO/E,MAJhDA,aAAiBsP,EAAItP,EAAQ,IAAIsP,GAAE,SAAUE,GAAWA,EAAQxP,EAAQ,KAIjB6P,KAAKH,EAAWE,EAAW,CAC7GD,GAAMJ,EAAYA,EAAU5E,MAAMyE,EAASC,GAAc,KAAK9G,OAClE,GACJ,EACIuH,EAAevK,MAAQA,KAAKuK,aAAgB,SAAUV,EAASW,GAC/D,IAAsGC,EAAGC,EAAGC,EAAxGC,EAAI,CAAEC,MAAO,EAAGC,KAAM,WAAa,GAAW,EAAPH,EAAE,GAAQ,MAAMA,EAAE,GAAI,OAAOA,EAAE,EAAI,EAAGI,KAAM,GAAIC,IAAK,IAAeC,EAAI3Q,OAAO2K,QAA4B,mBAAbiG,SAA0BA,SAAW5Q,QAAQ4F,WACtL,OAAO+K,EAAEjI,KAAOmI,EAAK,GAAIF,EAAS,MAAIE,EAAK,GAAIF,EAAU,OAAIE,EAAK,GAAsB,mBAAXzI,SAA0BuI,EAAEvI,OAAOC,UAAY,WAAa,OAAO3C,IAAM,GAAIiL,EAC1J,SAASE,EAAK3I,GAAK,OAAO,SAAUqF,GAAK,OACzC,SAAcuD,GACV,GAAIX,EAAG,MAAM,IAAI1F,UAAU,mCAC3B,KAAOkG,IAAMA,EAAI,EAAGG,EAAG,KAAOR,EAAI,IAAKA,OACnC,GAAIH,EAAI,EAAGC,IAAMC,EAAY,EAARS,EAAG,GAASV,EAAU,OAAIU,EAAG,GAAKV,EAAS,SAAOC,EAAID,EAAU,SAAMC,EAAE7H,KAAK4H,GAAI,GAAKA,EAAE1H,SAAW2H,EAAIA,EAAE7H,KAAK4H,EAAGU,EAAG,KAAKnI,KAAM,OAAO0H,EAE3J,OADID,EAAI,EAAGC,IAAGS,EAAK,CAAS,EAARA,EAAG,GAAQT,EAAElQ,QACzB2Q,EAAG,IACP,KAAK,EAAG,KAAK,EAAGT,EAAIS,EAAI,MACxB,KAAK,EAAc,OAAXR,EAAEC,QAAgB,CAAEpQ,MAAO2Q,EAAG,GAAInI,MAAM,GAChD,KAAK,EAAG2H,EAAEC,QAASH,EAAIU,EAAG,GAAIA,EAAK,CAAC,GAAI,SACxC,KAAK,EAAGA,EAAKR,EAAEI,IAAIK,MAAOT,EAAEG,KAAKM,MAAO,SACxC,QACI,MAAkBV,GAAZA,EAAIC,EAAEG,MAAY7M,OAAS,GAAKyM,EAAEA,EAAEzM,OAAS,KAAkB,IAAVkN,EAAG,IAAsB,IAAVA,EAAG,IAAW,CAAER,EAAI,EAAG,QAAU,CAC3G,GAAc,IAAVQ,EAAG,MAAcT,GAAMS,EAAG,GAAKT,EAAE,IAAMS,EAAG,GAAKT,EAAE,IAAM,CAAEC,EAAEC,MAAQO,EAAG,GAAI,KAAO,CACrF,GAAc,IAAVA,EAAG,IAAYR,EAAEC,MAAQF,EAAE,GAAI,CAAEC,EAAEC,MAAQF,EAAE,GAAIA,EAAIS,EAAI,KAAO,CACpE,GAAIT,GAAKC,EAAEC,MAAQF,EAAE,GAAI,CAAEC,EAAEC,MAAQF,EAAE,GAAIC,EAAEI,IAAI7P,KAAKiQ,GAAK,KAAO,CAC9DT,EAAE,IAAIC,EAAEI,IAAIK,MAChBT,EAAEG,KAAKM,MAAO,SAEtBD,EAAKZ,EAAK1H,KAAK+G,EAASe,EAC5B,CAAE,MAAOhK,GAAKwK,EAAK,CAAC,EAAGxK,GAAI8J,EAAI,CAAG,CAAE,QAAUD,EAAIE,EAAI,CAAG,CACzD,GAAY,EAARS,EAAG,GAAQ,MAAMA,EAAG,GAAI,MAAO,CAAE3Q,MAAO2Q,EAAG,GAAKA,EAAG,QAAK,EAAQnI,MAAM,EAC9E,CAtBgDmH,CAAK,CAAC5H,EAAGqF,GAAK,CAAG,CAuBrE,EACIvF,EAAUtC,MAAQA,KAAKsC,QAAW,SAAUC,EAAGC,GAC/C,IAAIC,EAAsB,mBAAXC,QAAyBH,EAAEG,OAAOC,UACjD,IAAKF,EAAG,OAAOF,EACf,IAAmBK,EAAYhC,EAA3BiC,EAAIJ,EAAEK,KAAKP,GAAOQ,EAAK,GAC3B,IACI,WAAc,IAANP,GAAgBA,KAAM,MAAQI,EAAIC,EAAEG,QAAQC,MAAMF,EAAG5H,KAAKyH,EAAEnI,MACxE,CACA,MAAOiE,GAASkC,EAAI,CAAElC,MAAOA,EAAS,CACtC,QACI,IACQkE,IAAMA,EAAEK,OAASR,EAAII,EAAU,SAAIJ,EAAEK,KAAKD,EAClD,CACA,QAAU,GAAIjC,EAAG,MAAMA,EAAElC,KAAO,CACpC,CACA,OAAOqE,CACX,EACIymB,EAAiBxpB,MAAQA,KAAKwpB,eAAkB,SAAUC,EAAI/jB,EAAMgkB,GACpE,GAAIA,GAA6B,IAArBrkB,UAAUnH,OAAc,IAAK,IAA4B6E,EAAxBF,EAAI,EAAG+Y,EAAIlW,EAAKxH,OAAY2E,EAAI+Y,EAAG/Y,KACxEE,GAAQF,KAAK6C,IACR3C,IAAIA,EAAK6B,MAAM1E,UAAUoD,MAAMR,KAAK4C,EAAM,EAAG7C,IAClDE,EAAGF,GAAK6C,EAAK7C,IAGrB,OAAO4mB,EAAGhuB,OAAOsH,GAAM6B,MAAM1E,UAAUoD,MAAMR,KAAK4C,GACtD,EACApL,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQq2B,SAOR,SAAkB5K,EAAkB6K,GAChC,IAAIC,EAAU,GACVzqB,EAAQ,EACR0I,EAAMiX,EAAiB/nB,OA8C3B,OAAOwC,QAAQuJ,QAAQ,MAAMK,MAzB7B,SAAS0mB,EAAYxxB,GACjB,OAjBJ,WACI,OAAOoK,EAAU5J,UAAM,OAAQ,GAAQ,WAEnC,OAAOuK,EAAYvK,MAAM,SAAU7C,GAC/B,OAAQA,EAAG0N,OACP,KAAK,EACD,OAAKimB,EACE,CAAC,EAAaA,KADW,CAAC,EAAa,GAElD,KAAK,EAED,MAAO,CAAC,EADD3zB,EAAG2N,QAEd,KAAK,EAAG,MAAO,CAAC,GAAc,GAEtC,GACJ,GACJ,CAEWmmB,GAA0B3mB,MAAK,SAAU4mB,GAC5C,GAAIA,EAIA,OAHIliB,GAAO+hB,EAAQ7yB,QACf6yB,EAAQ51B,KAAKiK,MAAM2rB,EAASvH,EAAc,GAAIlnB,EAAO,IAAIsC,MAAMoK,EAAM+hB,EAAQ7yB,QAAQizB,UAAK5oB,KAAa,IAEpG7H,QAAQuJ,QAAQ8mB,GAEvBvxB,SACAuxB,EAAQ51B,KAAKqE,GAEjB,IAAIgD,EA7BD8D,EAAQ0I,EAAMiX,EAAiB3f,OAAa,KA8B/C,OAAI9D,EACOA,EAAE8H,KAAK0mB,GAAanK,OAAM,SAAUuK,GAEvC,OAAIF,GAGJH,EAAQ51B,KAAKiK,MAAM2rB,EAASvH,EAAc,GAAIlnB,EAAO,IAAIsC,MAAMoK,EAAM+hB,EAAQ7yB,QAAQizB,UAAK5oB,KAAa,IAF5F7H,QAAQuJ,QAAQ8mB,EAI/B,IAEGrwB,QAAQuJ,QAAQ8mB,EAC3B,GACJ,GAEJ,C,iBCvHAz2B,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQ62B,mBAAgB,EACxB72B,EAAQ82B,cA+CR,SAAuBC,EAAYC,EAAUC,EAAQC,EAAQ/0B,GAEzD,IAAI1B,EAAO,CACP02B,KAAM92B,KAAKC,OAGXwpB,EAAa3R,EAAUM,QAAQC,QAAQ,cACvC0e,EAAYjf,EAAUM,QAAQC,QAAQ,YAAa,KAElD0e,aAA6C,EAASA,EAAU1zB,QAAU,IAC3EjD,EAAK42B,SAAWD,GAGpB,IAAIE,GAAexN,EACdzO,MAAMic,KACP72B,EAAK82B,cAAgB,CACjBC,KAAM/1B,KAAK4H,MAAMiuB,EAAc,KAAKnsB,WACpC,EAAGmsB,IAINL,IACDA,EAAS,CAAC,GAEVA,IACAx2B,EAAOX,OAAO23B,OAAOR,EAAQx2B,IAG7Bs2B,IAAeF,EAAca,SAC7Bj3B,EAAK6M,KAAO0pB,GAEZE,EAoBR,SAA8Bz2B,EAAMy2B,GAChC,OAAO,IAAIhxB,SAAQ,SAAUC,EAAGC,GAC5B,IAAK,IAAI5F,EAAM,EAAGA,EAAM02B,EAAOxzB,OAAQlD,IAAO,CAC1C,IAAIm3B,EAAeT,EAAO12B,GACtBoc,EAAiB+a,EAAaC,MAC9B/d,GAAQ,EAAIge,EAAiB9c,0BAA0B6B,GAC3D,GAAI/C,EAAO,CACP,IAAIa,EAAiBC,EAAkBC,gBAAgBC,mBAAmB+B,GACtE1B,EAAQR,aAAuD,EAASA,EAAeS,MACvFD,IACArB,EAAW,IAAIa,EAAea,GAC9B1B,EAAc,OAAIqB,EAClBrB,EAAe,SAAI,EACnBA,EAAgB,YAEPA,EAAMhT,QAEX8wB,EAAaF,SACbh3B,EAAOX,OAAO23B,OAAOh3B,EAAMk3B,EAAaF,OAAO5d,EAAOpZ,IAGlE,CACJ,CACA0F,GAAE,EACN,GACJ,CA3CQ2xB,CAAqBr3B,EAAMy2B,GAAQpnB,MAAK,SAAUzC,GAC1ClL,GACAA,EAAS1B,EAEjB,IAII0B,GACAA,EAAS1B,EAGrB,EA3FA,IAGIo2B,EAHAgB,EAAmB,EAAQ,MAC3B1f,EAAY,EAAQ,MACpBwC,EAAoB,EAAQ,OAEhC,SAAWkc,GAEPA,EAAcA,EAAsB,OAAI,GAAK,SAE7CA,EAAcA,EAAuB,QAAI,GAAK,SACjD,CALD,CAKGA,IAAkB72B,EAAQ62B,cAAgBA,EAAgB,CAAC,G,iBCZ9D/2B,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQ2D,WAAQ,EAChB,IAAIo0B,EAAiB,EAAQ,MACzBC,EAAY,EAAQ,MACpBvU,EAAkB,EAAQ,MAC1B9f,EAAuB,WAKvB,SAASA,EAAMs0B,GACXzyB,KAAK0yB,uBAAyB,IAAIrzB,IAClCW,KAAK2yB,QAAUF,CACnB,CAyEA,OAxEAn4B,OAAOC,eAAe4D,EAAM+B,UAAW,aAAc,CAEjDpD,IAAK,WACD,OAAOkD,KAAKob,WAChB,EACA7d,YAAY,EACZC,cAAc,IAKlBW,EAAM+B,UAAU0yB,OAAS,WACzB,EAMAz0B,EAAM+B,UAAU2yB,eAAiB,WAC7B,OAAO,IACX,EAEA10B,EAAM+B,UAAUuP,SAAW,WACvB,OAAOzP,KAAkB,WAC7B,EAEA7B,EAAM+B,UAAU4yB,UAAY,WACxB,OAAO9yB,KAAK2yB,OAChB,EAMAx0B,EAAM+B,UAAU6yB,eAAiB,SAAUC,GAAU,EAErD70B,EAAM+B,UAAU+yB,YAAc,WAC1B,IAAIC,EAAUlzB,KAAK6yB,iBACnB,GAAgB,OAAZK,EACA,IAAK,IAAIl4B,EAAM,EAAG4gB,EAAIsX,EAAQh1B,OAAQlD,EAAM4gB,EAAG5gB,IAAO,CAClD,IAAIm4B,EAAmBD,EAAQl4B,GAC/B,IAAKgF,KAAK0yB,uBAAuBle,IAAI2e,GAAmB,CACpDnzB,KAAK0yB,uBAAuB11B,IAAIm2B,GAAkB,GAClD,IAAIC,EAAW,IAAIZ,EAAUnJ,QAC7B+J,EAASh2B,MAAQ4C,KACjBozB,EAASC,WAAaF,EACtBZ,EAAee,aAAaC,cAAcH,EAC9C,CACJ,CAEJpzB,KAAK4yB,QACT,EAMAz0B,EAAM+B,UAAUszB,oBAAsB,SAAUR,GACxChzB,KAAKsb,aAAe2C,EAAgB/C,WAAWiB,QAAUnc,KAAKsb,aAAe2C,EAAgB9C,cAAcG,YAC3GiX,EAAee,aAAaE,oBAAoBR,EAExD,EAMA70B,EAAM+B,UAAUuzB,yBAA2B,SAAUT,GACjD,GAAIhzB,KAAKsb,aAAe2C,EAAgB/C,WAAWiB,QAAUnc,KAAKsb,aAAe2C,EAAgB9C,cAAcG,WAC3G,OAAOiX,EAAee,aAAaG,yBAAyBT,EAEpE,EACO70B,CACX,CAlF0B,GAmF1B3D,EAAQ2D,MAAQA,C,eCxFhB7D,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQuyB,SAWR,SAAkB3rB,EAAMzE,EAAU0qB,GAE9B,IADA,IAAI3b,EAAO,GACFC,EAAK,EAAGA,EAAKtG,UAAUnH,OAAQyN,IACpCD,EAAKC,EAAK,GAAKtG,UAAUsG,GAE7B,IAAIoK,EAAKiX,aAAY,WACZ5rB,GAASI,GAAGU,QAAQd,GAAM,GAI/BzE,SAAoDA,EAASyI,MAAMpF,KAAM,CAAC0L,IAHtEuhB,cAAclX,EAItB,GAAGsR,EAAS3b,GACZ,OAAOqK,CACX,C,iBCzBAzb,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQk5B,sBAAmB,EAC3B,IAAInB,EAAiB,EAAQ,MACzBtU,EAAkB,EAAQ,MAC1B0V,EAAe,EAAQ,MACvBC,EAAgB,EAAQ,KACxBC,EAAoB,EAAQ,MAC5B9F,EAAc,EAAQ,MACtBjuB,EAAc,EAAQ,MACtBg0B,EAAO,EAAQ,MAKfC,EAAkC,WAClC,SAASA,IACL/zB,KAAKg0B,mBAAqB,IAAI30B,IAC9BW,KAAKi0B,cAAe,CACxB,CAiRA,OAhRA35B,OAAOC,eAAew5B,EAAkB,WAAY,CAChDj3B,IAAK,WAID,OAHKi3B,EAAiBhY,YAClBgY,EAAiBhY,UAAY,IAAIgY,GAE9BA,EAAiBhY,SAC5B,EACAxe,YAAY,EACZC,cAAc,IAOlBu2B,EAAiB7zB,UAAUg0B,mBAAqB,SAAU91B,EAAK+1B,GAC3Dn0B,KAAKg0B,mBAAmBh3B,IAAIoB,EAAK+1B,GAC7Bp2B,UACAoB,QAAQC,IAAI,+BAAkE3D,OAAO2C,GAE7F,EAKA21B,EAAiB7zB,UAAUk0B,qBAAuB,SAAUh2B,GACxD4B,KAAKg0B,mBAAmB3iB,OAAOjT,GAC3BL,UACAoB,QAAQC,IAAI,+BAAkE3D,OAAO2C,GAE7F,EAIA21B,EAAiB7zB,UAAUm0B,WAAa,WAChCt2B,UACAoB,QAAQC,IAAI,kCAGhBY,KAAKs0B,eAELt0B,KAAKu0B,oBAELv0B,KAAKw0B,kBAILx0B,KAAKy0B,oBAELz0B,KAAK00B,sBAEL10B,KAAK20B,mBAEL30B,KAAK40B,uBAEL50B,KAAK60B,mBAEL70B,KAAK80B,oBACD/2B,UACAoB,QAAQC,IAAI,8BAEpB,EAIA20B,EAAiB7zB,UAAU60B,YAAc,WACjCh3B,UACAoB,QAAQC,IAAI,qCAGhBY,KAAKg1B,yBAELh1B,KAAKi0B,cAAe,EAChBl2B,UACAoB,QAAQC,IAAI,iCAEpB,EAIA20B,EAAiB7zB,UAAUo0B,aAAe,WACtC,IAEIR,EAAKhG,GAAGpO,gBACJ3hB,UACAoB,QAAQC,IAAI,6BAEpB,CACA,MAAOV,GACHS,QAAQT,MAAM,kCAAmCA,EACrD,CACJ,EAIAq1B,EAAiB7zB,UAAUq0B,kBAAoB,WAC3C,IACIxG,EAAYre,aAAawB,SACzB,EAAI6c,EAAYhe,oBACZhS,UACAoB,QAAQC,IAAI,8BAEpB,CACA,MAAOV,GACHS,QAAQT,MAAM,mCAAoCA,EACtD,CACJ,EAIAq1B,EAAiB7zB,UAAUs0B,gBAAkB,WACzCx0B,KAAKg0B,mBAAmBp0B,SAAQ,SAAUu0B,EAAY/1B,GAClD,IACI+1B,EAAW9xB,QACPtE,UACAoB,QAAQC,IAAI,6BAAsD3D,OAAO2C,GAEjF,CACA,MAAOM,GACHS,QAAQT,MAAM,2BAA+CjD,OAAO2C,EAAK,WAAqCM,EAClH,CACJ,GACJ,EAIAq1B,EAAiB7zB,UAAU80B,uBAAyB,WAChDh1B,KAAKg0B,mBAAmBp0B,SAAQ,SAAUu0B,EAAY/1B,GAClD,IACQ+1B,EAAWc,eACXd,EAAWc,eACPl3B,UACAoB,QAAQC,IAAI,gCAAwE3D,OAAO2C,IAGvG,CACA,MAAOM,GACHS,QAAQT,MAAM,8BAAiEjD,OAAO2C,EAAK,WAAqCM,EACpI,CACJ,GACJ,EAIAq1B,EAAiB7zB,UAAUg1B,iBAAmB,WAC1C,IAEI,IAAIxd,EAAYzC,OAAOkgB,cACnBzd,GAAaA,EAAUxG,OACvBwG,EAAUxG,QAGd,IAAI0C,EAAeqB,OAAOmgB,iBACtBxhB,GAAgBA,EAAa1C,OAC7B0C,EAAa1C,QAEbnT,UACAoB,QAAQC,IAAI,6BAEpB,CACA,MAAOV,GACHS,QAAQT,MAAM,kCAAmCA,EACrD,CACJ,EAIAq1B,EAAiB7zB,UAAUu0B,kBAAoB,WAC3C,IAEQxW,EAAgB9C,eAAgE,mBAAxC8C,EAAgB9C,cAAc9Y,OACtE4b,EAAgB9C,cAAc9Y,QAE9BtE,UACAoB,QAAQC,IAAI,6BAEpB,CACA,MAAOV,GACHS,QAAQT,MAAM,kCAAmCA,EACrD,CACJ,EAIAq1B,EAAiB7zB,UAAUw0B,oBAAsB,WAC7C,IAEQ50B,EAAY8B,WAAoD,mBAAhC9B,EAAY8B,UAAUS,OACtDvC,EAAY8B,UAAUS,QAEtBtE,UACAoB,QAAQC,IAAI,8BAEpB,CACA,MAAOV,GACHS,QAAQT,MAAM,mCAAoCA,EACtD,CACJ,EAIAq1B,EAAiB7zB,UAAUy0B,iBAAmB,WAC1C,IAEQf,EAAc/zB,aAA0D,mBAApC+zB,EAAc/zB,YAAYwC,OAC9DuxB,EAAc/zB,YAAYwC,QAE1BtE,UACAoB,QAAQC,IAAI,8BAEpB,CACA,MAAOV,GACHS,QAAQT,MAAM,mCAAoCA,EACtD,CACJ,EAIAq1B,EAAiB7zB,UAAU00B,qBAAuB,WAC9C,IAEQf,EAAkB7V,iBAAsE,mBAA5C6V,EAAkB7V,gBAAgB3b,OAC9EwxB,EAAkB7V,gBAAgB3b,QAElCtE,UACAoB,QAAQC,IAAI,+BAEpB,CACA,MAAOV,GACHS,QAAQT,MAAM,oCAAqCA,EACvD,CACJ,EAIAq1B,EAAiB7zB,UAAU20B,iBAAmB,WAC1ClB,EAAatU,WAAWK,eAC5B,EAIAqU,EAAiB7zB,UAAU40B,kBAAoB,WAE3C,IACQvC,EAAee,cAA6D,mBAAtCf,EAAee,aAAajxB,OAClEkwB,EAAee,aAAajxB,OAEpC,CACA,MAAO3D,GACHS,QAAQT,MAAM,gCAAiCA,EACnD,CACIX,UACAoB,QAAQC,IAAI,2BAEpB,EACA9E,OAAOC,eAAew5B,EAAiB7zB,UAAW,cAAe,CAI7DpD,IAAK,WACD,OAAOkD,KAAKi0B,YAChB,EACA12B,YAAY,EACZC,cAAc,IAKlBu2B,EAAiB7zB,UAAUm1B,WAAa,WACpCr1B,KAAKi0B,cAAe,EACpBj0B,KAAKq0B,YACT,EACON,CACX,CAtRqC,GAwRrCv5B,EAAQk5B,iBAAmBK,EAAiB9iB,SAExClT,WACAkX,OAAOye,iBAAmBl5B,EAAQk5B,iB,eCvStC,IAAI4B,EAMAC,EARJj7B,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQg7B,KAAOh7B,EAAQ+6B,UAAY/6B,EAAQ86B,cAAW,EAEtD,SAAWA,GACPA,EAAc,IAAI,MAClBA,EAAe,KAAI,MACtB,CAHD,CAGGA,IAAa96B,EAAQ86B,SAAWA,EAAW,CAAC,IAG/C,SAAWC,GACPA,EAAe,IAAI,KACtB,CAFD,CAEGA,IAAc/6B,EAAQ+6B,UAAYA,EAAY,CAAC,IAClD,IAAIE,EAAsB,WACtB,SAASA,IACLz1B,KAAK01B,YAAc,EACnB11B,KAAK21B,UAAY,CAAC,CACtB,CA+KA,OAvKAF,EAAKv1B,UAAU01B,aAAe,SAAUrwB,EAAKtK,EAAMsB,GAC/C,IAAI6D,EAAQJ,KACZ,OAAO,IAAIU,SAAQ,SAAUC,EAAGC,GAC5BR,EAAMy1B,QAAQtwB,EAAKtK,GAAM,SAAUA,GAC/B0F,EAAE1F,EACN,IAAG,SAAU6F,GACTF,EAAEE,EACN,GAAGvE,EACP,GACJ,EASAk5B,EAAKv1B,UAAU21B,QAAU,SAAUtwB,EAAKtK,EAAM66B,EAASC,EAAMx5B,GAiHzD,IAAIuY,EAAS,OACTvY,aAAuC,EAASA,EAAO4T,QACvD2E,EAASvY,EAAO4T,MAEpB,IAAI6lB,EAAMx0B,GAAGy0B,OAAOC,oBAEhBC,EAAU3uB,KAAKyR,UAAUhe,GAC7B+6B,EAAI9M,KAAKpU,EAAQvP,GAAK,GACtB,IAAI6wB,EAAc,oBACd75B,aAAuC,EAASA,EAAO65B,eACvDA,EAAc75B,EAAO65B,aAGzBJ,EAAIK,iBAAiB,eAAgBD,GACrCJ,EAAIM,mBAAqB,WACrB,GAAuB,IAAnBN,EAAIO,WAAkB,CACtB,IAAIC,EAAWR,EAAIS,aACfT,EAAIU,QAAU,KAAOV,EAAIU,OAAS,KAE9Bn6B,EAAOo6B,SACPH,EAAWj6B,EAAOo6B,OAAgB,QAAEH,IAExCV,EAAQtuB,KAAKC,MAAM+uB,KAGnBT,EAAKS,EAEb,CACJ,EAEIj6B,EAAOo6B,SACPR,EAAU,UAAY55B,EAAOo6B,OAAgB,QAAER,IAGnDH,EAAIvZ,KAAK0Z,EACb,EACOV,CACX,CApLyB,GAqLzBj7B,EAAQg7B,KAAO,IAAIC,C,iBCjMnBn7B,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQo8B,aAAU,EAClB,IAAIpf,EAAW,EAAQ,MAKnBof,EAAyB,WACzB,SAASA,EAAQC,GACb72B,KAAK82B,MAAQ,EACb92B,KAAK+2B,aAAc,EACnB/2B,KAAK62B,uBAAyBA,EAC9B72B,KAAKg3B,oBAAsB,GAC3Bh3B,KAAKi3B,gBAAkB,EACvBj3B,KAAKk3B,WAAa,IAAI1f,EAASG,OACnC,CA6DA,OA5DArd,OAAOC,eAAeq8B,EAAQ12B,UAAW,YAAa,CAClDpD,IAAK,WACD,OAAOkD,KAAKk3B,WAAWtf,KAC3B,EACAra,YAAY,EACZC,cAAc,IAElBlD,OAAOC,eAAeq8B,EAAQ12B,UAAW,OAAQ,CAC7CpD,IAAK,WACD,OAAOkD,KAAK82B,KAChB,EACAv5B,YAAY,EACZC,cAAc,IAElBo5B,EAAQ12B,UAAUi3B,MAAQ,SAAU/Q,GAChC,IAAIhmB,EAAQJ,KACZ,GAAIA,KAAK+2B,YACL,MAAM,IAAIv4B,MAAM,4BAGpB,OADAwB,KAAK82B,QACE,IAAIp2B,SAAQ,SAAUC,EAAGC,GAC5BR,EAAM42B,oBAAoB77B,KAAK,CAAEirB,QAASA,EAASzlB,EAAGA,EAAGC,EAAGA,IAC5DR,EAAMg3B,SACV,GACJ,EACAR,EAAQ12B,UAAUk3B,QAAU,WAExB,IADA,IAAIh3B,EAAQJ,KACLA,KAAKg3B,oBAAoB94B,QAAU8B,KAAKi3B,gBAAkBj3B,KAAK62B,wBAAwB,CAC1F,IAAIQ,EAAer3B,KAAKg3B,oBAAoB3yB,QAC5CrE,KAAKi3B,kBACL,IAAIxK,EAAU4K,EAAajR,UAC3BqG,EAAQniB,KAAK+sB,EAAa12B,EAAG02B,EAAaz2B,GAC1C6rB,EAAQniB,MAAK,WAAc,OAAOlK,EAAMk3B,UAAY,IAAG,WAAc,OAAOl3B,EAAMk3B,UAAY,GAClG,CACJ,EACAV,EAAQ12B,UAAUo3B,SAAW,WACrBt3B,KAAK+2B,cAGT/2B,KAAKi3B,kBACgB,KAAfj3B,KAAK82B,OACP92B,KAAKk3B,WAAWtgB,OAEhB5W,KAAKg3B,oBAAoB94B,OAAS,GAClC8B,KAAKo3B,UAEb,EACAR,EAAQ12B,UAAUgR,MAAQ,WACtB,GAAIlR,KAAK+2B,YACL,MAAM,IAAIv4B,MAAM,4BAEpBwB,KAAKg3B,oBAAoB94B,OAAS,EAClC8B,KAAK82B,MAAQ92B,KAAKi3B,eACtB,EACAL,EAAQ12B,UAAUq3B,QAAU,WACxBv3B,KAAK+2B,aAAc,EACnB/2B,KAAKg3B,oBAAoB94B,OAAS,EAClC8B,KAAK82B,MAAQ,EACb92B,KAAKk3B,WAAWK,SACpB,EACOX,CACX,CAtE4B,GAuE5Bp8B,EAAQo8B,QAAUA,C,eC3ElB,IAAIY,EAHJl9B,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQoH,UAAYpH,EAAQg9B,gBAAa,EAGzC,SAAWA,GACPA,EAAkB,MAAI,QACtBA,EAAoB,QAAI,UACxBA,EAAuB,WAAI,aAC3BA,EAAiB,KAAI,MACxB,CALD,CAKGA,IAAeh9B,EAAQg9B,WAAaA,EAAa,CAAC,IACrD,IAAI51B,EAA2B,WAC3B,SAASA,IACT,CA6fA,OAvfAA,EAAU61B,kBAAoB,SAAUC,GACpC,MAA8B,iBAAnBA,EACA,CAAEp3B,WAAYo3B,GAEU,iBAAnBA,GAAkD,OAAnBA,GAA2B,eAAgBA,EAC/E,CACHp3B,WAAYo3B,EAAep3B,WAC3B2N,QAASypB,EAAezpB,SAKrB,CAAE3N,WAAY/B,OAAOm5B,GAEpC,EAOA91B,EAAU+1B,iBAAmB,SAAUD,EAAgB95B,GACnD,IAAIT,EAAK6C,KAAKy3B,kBAAkBC,GAAiBp3B,EAAanD,EAAGmD,WAAY2N,EAAU9Q,EAAG8Q,QACtF2pB,EAAUh6B,EAAO,GAAGnC,OAAO6E,EAAY,KAAK7E,OAAOmC,GAAQ0C,EAC/D,OAAO2N,EAAU,GAAGxS,OAAOm8B,EAAS,KAAKn8B,OAAOwS,GAAW2pB,CAC/D,EAMAh2B,EAAUyH,iBAAmB,SAAUuO,EAAOjb,GACrCqD,KAAK8uB,OAAOlX,KACb5X,KAAK8uB,OAAOlX,GAAS,IAEzB5X,KAAK8uB,OAAOlX,GAAOzc,KAAKwB,EAC5B,EAIAiF,EAAUi2B,oBAAsB,WAC5B,IAAK,IAAIC,KAAW93B,KAAK8uB,OACrB9uB,KAAK8uB,OAAOgJ,GAAW,GAE3B93B,KAAK8uB,OAAS,CAAC,EACX/wB,UACAoB,QAAQC,IAAI,yBAEpB,EAKAwC,EAAUm2B,mBAAqB,WAE3B,IAAK,IAAI35B,KAAO4B,KAAKg4B,eAAgB,CACjC,IAAIC,EAAQj4B,KAAKg4B,eAAe55B,GAC5B65B,EAAMC,cACND,EAAMC,YAAYh6B,OAAS,UAIxB+5B,EAAMl3B,KACjB,CAEAf,KAAKg4B,eAAiB,CAAC,EACnBj6B,UACAoB,QAAQC,IAAI,sBAEpB,EAKAwC,EAAUS,MAAQ,WACdrC,KAAK+3B,qBACL/3B,KAAK63B,sBACD95B,UACAoB,QAAQC,IAAI,8BAEpB,EAMAwC,EAAUu2B,SAAW,SAAU5yB,GAC3B,MAAO,iBAAiB6yB,KAAK7yB,EACjC,EACA3D,EAAUy2B,SAAW,SAAU9yB,GAC3B,IAAIpI,EACJ,OAA2C,QAAnCA,EAAK6C,KAAKg4B,eAAezyB,UAAyB,IAAPpI,OAAgB,EAASA,EAAG4D,KACnF,EAWAa,EAAUG,KAAO,SAAUu2B,EAAOnoB,EAAMtP,GACpC,GAAKb,KAAKg4B,eAAeM,GA4BjBt4B,KAAKg4B,eAAeM,GAAOv3B,MACvBf,KAAKg4B,eAAeM,GAAOv3B,MAAMmB,QAC7BrB,GACAA,EAAWuE,MAAMpF,KAAM,CAAC,KAAMA,KAAKg4B,eAAeM,GAAOv3B,gBAItDf,KAAKg4B,eAAeM,GAC3Bt4B,KAAK+B,KAAKu2B,EAAOnoB,EAAMtP,IAIvBA,GACAb,KAAKg4B,eAAeM,GAAOJ,YAAY/8B,KAAK0F,OAzCvB,CAC7Bb,KAAKg4B,eAAeM,GAAS,CAAEJ,YAAar3B,EAAa,CAACA,GAAc,IACxE,IAAI03B,EAASv4B,KACTw4B,EAAY,SAAU13B,EAAKC,GACvBD,GACAU,GAAG9C,MAAM,YAA0DoC,GAEvEy3B,EAAOP,eAAeM,GAAOv3B,MAAQA,EAErC,IADA,IAAIm3B,EAAcK,EAAOP,eAAeM,GAAOJ,YACtCl9B,EAAM,EAAGA,GAAOk9B,aAAiD,EAASA,EAAYh6B,QAASlD,IAC/Ek9B,EAAYl9B,GAClBoK,MAAMpF,KAAM,CAACc,EAAKC,IAErCw3B,EAAOP,eAAeM,GAAOJ,YAAYh6B,OAAS,EAElD,IADA,IAAIu6B,EAAaF,EAAOzJ,OAAa,KAC5BvtB,EAAM,EAAGA,GAAOk3B,aAA+C,EAASA,EAAWv6B,QAASqD,IACnFk3B,EAAWl3B,GACjB6D,MAAMmzB,EAAQ,CAAC,YAAaD,GAE5C,EACKt4B,KAAKm4B,SAASG,GAIf92B,GAAGk3B,aAAaC,WAAWL,EAAOE,GAHlCh3B,GAAGo3B,UAAU72B,KAAKu2B,EAAOnoB,EAAMqoB,EAKvC,CAmBJ,EAOA52B,EAAUysB,UAAY,SAAUiK,EAAOnoB,GACnC,IAAI/P,EAAQJ,KACZ,OAAO,IAAIU,SAAQ,SAAUC,EAAGC,GAC5BR,EAAM2B,KAAKu2B,EAAOnoB,GAAM,SAAUzR,EAAOm6B,GACjCn6B,EACAkC,EAAElC,GAGNiC,EAAEk4B,EACN,GACJ,GACJ,EAQAj3B,EAAUC,aAAe,SAAU61B,EAAgBY,EAAOnoB,EAAMtP,GAC5D,IAAIT,EAAQJ,KACR7C,EAAK6C,KAAKy3B,kBAAkBC,GAAiBp3B,EAAanD,EAAGmD,WAAsBnD,EAAG8Q,QAC1FjO,KAAK84B,WAAWpB,GAAgB,SAAU52B,EAAKi4B,GAC3C,GAAIj4B,EAKA,OAJA3B,QAAQT,MAAM,UAAUjD,OAAO6E,EAAY,YAAgDQ,QACvFD,GACAA,EAAWuE,MAAMhF,EAAO,CAACU,EAAK,QAItC,IAAI1C,EAAMgC,EAAMu3B,iBAAiBD,EAAgBY,GAC5Cl4B,EAAM43B,eAAe55B,GAwBlBgC,EAAM43B,eAAe55B,GAAK2C,MACtBX,EAAM43B,eAAe55B,GAAK2C,MAAMmB,QAC5BrB,GACAA,EAAWuE,MAAMhF,EAAO,CAAC,KAAMA,EAAM43B,eAAe55B,GAAK2C,gBAKtDX,EAAM43B,eAAe55B,GAC5BgC,EAAMyB,aAAa61B,EAAgBY,EAAOnoB,EAAMtP,IAKhDA,GACAT,EAAM43B,eAAe55B,GAAK85B,YAAY/8B,KAAK0F,IAtCnDT,EAAM43B,eAAe55B,GAAO,CACxB85B,YAAar3B,EAAa,CAACA,GAAc,IAE7Ck4B,EAAOh3B,KAAKu2B,EAAOnoB,GAAM,SAAUrP,EAAKk4B,GACpC,GAAI54B,EAAM43B,eAAe55B,GAAM,CAC3BgC,EAAM43B,eAAe55B,GAAK2C,MAAQi4B,EAClC,IAAId,EAAc93B,EAAM43B,eAAe55B,GAAK85B,YAE5C93B,EAAM43B,eAAe55B,GAAK85B,YAAc,GACxC,IAAK,IAAIl0B,EAAM,EAAGA,GAAOk0B,aAAiD,EAASA,EAAYh6B,QAAS8F,IAC/Ek0B,EAAYl0B,GAClBoB,MAAMhF,EAAO,CAACU,EAAKk4B,IAItC,IADA,IAAIP,EAAar4B,EAAM0uB,OAAa,KAC3BvW,EAAM,EAAGA,GAAOkgB,aAA+C,EAASA,EAAWv6B,QAASqa,IACnFkgB,EAAWlgB,GACjBnT,MAAMhF,EAAO,CAACE,EAAYg4B,GAE1C,CACJ,IAsBR,GACJ,EAQA12B,EAAUwsB,kBAAoB,SAAUsJ,EAAgBY,EAAOnoB,GAC3D,IAAI/P,EAAQJ,KACZ,OAAO,IAAIU,SAAQ,SAAUC,EAAGC,GAC5BR,EAAMyB,aAAa61B,EAAgBY,EAAOnoB,GAAM,SAAUrP,EAAKC,GACvDD,EACAF,EAAEE,GAGNH,EAAEI,EACN,GACJ,GACJ,EAMAa,EAAUk3B,WAAa,SAAUpB,EAAgB72B,GAC7C,IAAI2f,EAAOxgB,KACP7C,EAAK6C,KAAKy3B,kBAAkBC,GAAiBp3B,EAAanD,EAAGmD,WAAY2N,EAAU9Q,EAAG8Q,QACtFgrB,EAAWj5B,KAAK23B,iBAAiBD,GAChC13B,KAAKg4B,eAAeiB,GAmCjBj5B,KAAKg4B,eAAeiB,GAAUl4B,MAC1BF,GACAA,EAAWuE,MAAMpF,KAAM,CAAC,KAAMA,KAAKg4B,eAAeiB,GAAUl4B,QAK5DF,GACAb,KAAKg4B,eAAeiB,GAAUf,YAAY/8B,KAAK0F,IA1CvDb,KAAKg4B,eAAeiB,GAAY,CAC5Bf,YAAar3B,EAAa,CAACA,GAAc,IAGzCoN,EACAzM,GAAGk3B,aAAaI,WAAWx4B,EAAY,CAAE2N,QAASA,IAAW,SAAUnN,EAAKi4B,GACxE,GAAIvY,EAAKwX,eAAeiB,GAAW,CAC/BzY,EAAKwX,eAAeiB,GAAUl4B,MAAQg4B,EACtC,IAAIb,EAAc1X,EAAKwX,eAAeiB,GAAUf,YAEhD1X,EAAKwX,eAAeiB,GAAUf,YAAc,GAC5C,IAAK,IAAIlT,EAAM,EAAGA,GAAOkT,aAAiD,EAASA,EAAYh6B,QAAS8mB,IAC/EkT,EAAYlT,GAClB5f,MAAMpF,KAAM,CAACc,EAAKi4B,GAEzC,CACJ,IAGAv3B,GAAGk3B,aAAaI,WAAWx4B,GAAY,SAAUQ,EAAKi4B,GAClD,GAAIvY,EAAKwX,eAAeiB,GAAW,CAC/BzY,EAAKwX,eAAeiB,GAAUl4B,MAAQg4B,EACtC,IAAIb,EAAc1X,EAAKwX,eAAeiB,GAAUf,YAEhD1X,EAAKwX,eAAeiB,GAAUf,YAAc,GAC5C,IAAK,IAAI/S,EAAM,EAAGA,GAAO+S,aAAiD,EAASA,EAAYh6B,QAASinB,IAC/E+S,EAAY/S,GAClB/f,MAAMpF,KAAM,CAACc,EAAKi4B,GAEzC,CACJ,IAgBZ,EAOAn3B,EAAUs3B,mBAAqB,SAAUH,EAAQI,EAAWt4B,GACxD,IAAI2f,EAAOxgB,KACP5B,EAAM,eAAiB+6B,EACtBn5B,KAAKg4B,eAAe55B,GAcjBoiB,EAAKwX,eAAe55B,GAAmB,aACnCyC,GACAA,EAAWuE,MAAMpF,KAAM,CAAC,OAIxBa,GACAb,KAAKg4B,eAAe55B,GAAK85B,YAAY/8B,KAAK0F,IApBlDb,KAAKg4B,eAAe55B,GAAO,CAAE85B,YAAar3B,EAAa,CAACA,GAAc,IACtEk4B,EAAOK,aAAaD,GAAW,SAAUr4B,GACrC,IAAIo3B,EAAc1X,EAAKwX,eAAe55B,GAAK85B,YAC3C1X,EAAKwX,eAAe55B,GAAmB,cAAI,EAC3C,IAAK,IAAImnB,EAAM,EAAGA,GAAO2S,aAAiD,EAASA,EAAYh6B,QAASqnB,IAC/E2S,EAAY3S,GAClBngB,MAAMpF,KAAM,CAACc,IAEhC0f,EAAKwX,eAAe55B,GAAK85B,YAAYh6B,OAAS,SACvCsiB,EAAKwX,eAAe55B,GAAmB,YAClD,IAcR,EAOAwD,EAAUy3B,cAAgB,SAAUN,EAAQT,EAAOnoB,EAAMtP,GACrD,IAAI2f,EAAOxgB,KACP5B,EAAM,UAAYk6B,EACjBt4B,KAAKg4B,eAAe55B,GAajBoiB,EAAKwX,eAAe55B,GAAgB,UAChCyC,GACAA,EAAWuE,MAAMpF,KAAM,CAAC,KAAMwgB,EAAKwX,eAAe55B,GAAgB,YAIlEyC,GACAb,KAAKg4B,eAAe55B,GAAK85B,YAAY/8B,KAAK0F,IAnBlDb,KAAKg4B,eAAe55B,GAAO,CAAE85B,YAAar3B,EAAa,CAACA,GAAc,IACtEk4B,EAAOO,QAAQhB,EAAOnoB,GAAM,SAAUzR,EAAO66B,GACzC,IAAIrB,EAAc1X,EAAKwX,eAAe55B,GAAK85B,YAC3C1X,EAAKwX,eAAe55B,GAAgB,UAAIm7B,EACxC,IAAK,IAAIC,EAAM,EAAGA,GAAOtB,aAAiD,EAASA,EAAYh6B,QAASs7B,IAC/EtB,EAAYsB,GAClBp0B,MAAMpF,KAAM,CAACtB,EAAO66B,IAEvC/Y,EAAKwX,eAAe55B,GAAK85B,YAAYh6B,OAAS,CAClD,IAcR,EAMA0D,EAAU63B,aAAe,SAAUC,EAAQn0B,GACvCvF,KAAK+B,KAAKwD,EAAK/D,GAAGm4B,aAAa,SAAU74B,EAAKC,GACtCD,IAGJ44B,EAAOE,YAAc74B,EACzB,GACJ,EAMAa,EAAUi4B,qBAAuB,SAAUH,EAAQn0B,EAAKmyB,GACpD13B,KAAK6B,aAAa61B,EAAgBnyB,EAAK/D,GAAGm4B,aAAa,SAAU74B,EAAKC,GAC9DD,IAGJ44B,EAAOE,YAAc74B,EACzB,GACJ,EAUAa,EAAU+H,kBAAoB,SAAUf,EAA4BJ,EAAgBsxB,EAAqB1xB,EAActB,EAAUizB,GAC7H,IAAI35B,EAAQJ,UACM,IAAd+5B,IAAwBA,EAAY,GACxC,IAAIC,EAAY,EACZ9xB,EAAO,WACW,IAAd8xB,IACApxB,EAA2BS,iBAAiB1C,YAAY2C,YAAYC,UAAU,SAAU3I,GACpF,IAAIzD,EACAY,UACAoB,QAAQC,IAAI,+BAAgCoJ,IAED,QAA1CrL,EAAKyL,EAA2BxH,YAAyB,IAAPjE,OAAgB,EAASA,EAAGsD,SAC/EmI,EAA2BxH,KAAKX,OAAOw5B,YAAYrxB,EAA2BxH,KAEtF,GAAGhB,GACHwI,EAA2BR,aAAeA,EAC1CQ,EAA2B/B,cAAcC,EAAUizB,GAE3D,EACA/5B,KAAK+B,KAAKyG,EAAgB7B,YAAYuzB,kBAAkB,SAAUp5B,EAAKC,GAC/DD,IAGJ8H,EAA2BtB,YAAcvG,EACzCi5B,IACA9xB,IACJ,IACAlI,KAAK+B,KAAK+3B,EAAqBnzB,YAAYwzB,uBAAuB,SAAUr5B,EAAKC,GACzED,IAGJ8H,EAA2BwxB,iBAAmBr5B,EAC9Ci5B,IACA9xB,IACJ,GACJ,EAYAtG,EAAU8H,0BAA4B,SAAUpJ,EAAYsI,EAA4BJ,EAAgBsxB,EAAqB1xB,EAActB,EAAUizB,EAAWM,GAC5J,IAAIj6B,EAAQJ,UACM,IAAd+5B,IAAwBA,EAAY,QACjB,IAAnBM,IAA6BA,GAAiB,GAClDr6B,KAAK84B,WAAWx4B,GAAY,SAAUQ,EAAKi4B,GACvC,IAAIj4B,EAAJ,CAGA,IAAIk5B,EAAY,EACZ9xB,EAAO,WACW,IAAd8xB,IACApxB,EAA2BS,iBAAiB1C,YAAY2C,YAAYC,UAAU,SAAU3I,GACpF,IAAIzD,EAAI6J,EACJqzB,IAA8D,QAA1Cl9B,EAAKyL,EAA2BxH,YAAyB,IAAPjE,OAAgB,EAASA,EAAGsD,UAC9F1C,UACAoB,QAAQC,IAAI,uCAAwCoJ,GAEb,QAA1CxB,EAAK4B,EAA2BxH,YAAyB,IAAP4F,GAAyBA,EAAGvG,OAAOw5B,YAAYrxB,EAA2BxH,MAErI,GAAGhB,GACHwI,EAA2BR,aAAeA,EAC1CQ,EAA2B/B,cAAcC,EAAUizB,GAE3D,EACAhB,EAAOh3B,KAAKyG,EAAgB7B,YAAYuzB,kBAAkB,SAAUp5B,EAAKC,GACjED,IAGJ8H,EAA2BtB,YAAcvG,EACzCi5B,IACA9xB,IACJ,IACA6wB,EAAOh3B,KAAK+3B,EAAqBnzB,YAAYwzB,uBAAuB,SAAUr5B,EAAKC,GAC3ED,IAGJ8H,EAA2BwxB,iBAAmBr5B,EAC9Ci5B,IACA9xB,IACJ,GAhCA,CAiCJ,GACJ,EACAtG,EAAUktB,OAAS,CAAC,EACpBltB,EAAUo2B,eAAiB,CAAC,EACrBp2B,CACX,CAhgB8B,GAigB9BpH,EAAQoH,UAAYA,C,eC3gBpBtH,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQ8/B,YAAS,EACjB,IAAIA,EAAwB,WACxB,SAASA,IAELt6B,KAAKu6B,gBAAkB,GAEvBv6B,KAAKw6B,eAAiB,IAAIn7B,GAC9B,CA2CA,OA1CA/E,OAAOC,eAAe+/B,EAAOp6B,UAAW,gBAAiB,CAErDpD,IAAK,WACD,OAAOkD,KAAKw6B,cAChB,EACAj9B,YAAY,EACZC,cAAc,IAElBlD,OAAOC,eAAe+/B,EAAOp6B,UAAW,aAAc,CAElDpD,IAAK,WACD,OAAOkD,KAAKob,WAChB,EACA7d,YAAY,EACZC,cAAc,IAGlB88B,EAAOp6B,UAAUu6B,eAAiB,WAC9B,MAAO,EACX,EAOAH,EAAOp6B,UAAUw6B,SAAW,SAAUC,GAClC,OAAO36B,KAAKw6B,eAAe19B,IAAI69B,EACnC,EAEAL,EAAOp6B,UAAU8b,WAAa,SAAUV,GACpCtb,KAAKu6B,gBAAkBv6B,KAAKy6B,iBAC5B,IAAK,IAAIz/B,EAAM,EAAG4gB,EAAI5b,KAAKu6B,gBAAgBr8B,OAAQlD,EAAM4gB,EAAG5gB,IAAO,CAC/D,IAAI4/B,EAAa56B,KAAKu6B,gBAAgBv/B,GACtC,IAAKgF,KAAKw6B,eAAehmB,IAAIomB,GAAa,CACtC,IAAIC,EAAiB,IAAID,EAAW56B,MACpC66B,EAA4B,YAAIvf,EAChCtb,KAAKw6B,eAAex9B,IAAI49B,EAAYC,GACpCA,EAAe5H,aACnB,CACJ,CACJ,EACOqH,CACX,CAlD2B,GAmD3B9/B,EAAQ8/B,OAASA,C,uBCrDjB,IAAI1wB,EAAa5J,MAAQA,KAAK4J,WAAc,SAAUC,EAASC,EAAYC,EAAGC,GAE1E,OAAO,IAAKD,IAAMA,EAAIrJ,WAAU,SAAUuJ,EAASC,GAC/C,SAASC,EAAU1P,GAAS,IAAM2P,EAAKJ,EAAUhH,KAAKvI,GAAS,CAAE,MAAOmG,GAAKsJ,EAAOtJ,EAAI,CAAE,CAC1F,SAASyJ,EAAS5P,GAAS,IAAM2P,EAAKJ,EAAiB,MAAEvP,GAAS,CAAE,MAAOmG,GAAKsJ,EAAOtJ,EAAI,CAAE,CAC7F,SAASwJ,EAAK5K,GAJlB,IAAe/E,EAIa+E,EAAOyD,KAAOgH,EAAQzK,EAAO/E,QAJ1CA,EAIyD+E,EAAO/E,MAJhDA,aAAiBsP,EAAItP,EAAQ,IAAIsP,GAAE,SAAUE,GAAWA,EAAQxP,EAAQ,KAIjB6P,KAAKH,EAAWE,EAAW,CAC7GD,GAAMJ,EAAYA,EAAU5E,MAAMyE,EAASC,GAAc,KAAK9G,OAClE,GACJ,EACIuH,EAAevK,MAAQA,KAAKuK,aAAgB,SAAUV,EAASW,GAC/D,IAAsGC,EAAGC,EAAGC,EAAxGC,EAAI,CAAEC,MAAO,EAAGC,KAAM,WAAa,GAAW,EAAPH,EAAE,GAAQ,MAAMA,EAAE,GAAI,OAAOA,EAAE,EAAI,EAAGI,KAAM,GAAIC,IAAK,IAAeC,EAAI3Q,OAAO2K,QAA4B,mBAAbiG,SAA0BA,SAAW5Q,QAAQ4F,WACtL,OAAO+K,EAAEjI,KAAOmI,EAAK,GAAIF,EAAS,MAAIE,EAAK,GAAIF,EAAU,OAAIE,EAAK,GAAsB,mBAAXzI,SAA0BuI,EAAEvI,OAAOC,UAAY,WAAa,OAAO3C,IAAM,GAAIiL,EAC1J,SAASE,EAAK3I,GAAK,OAAO,SAAUqF,GAAK,OACzC,SAAcuD,GACV,GAAIX,EAAG,MAAM,IAAI1F,UAAU,mCAC3B,KAAOkG,IAAMA,EAAI,EAAGG,EAAG,KAAOR,EAAI,IAAKA,OACnC,GAAIH,EAAI,EAAGC,IAAMC,EAAY,EAARS,EAAG,GAASV,EAAU,OAAIU,EAAG,GAAKV,EAAS,SAAOC,EAAID,EAAU,SAAMC,EAAE7H,KAAK4H,GAAI,GAAKA,EAAE1H,SAAW2H,EAAIA,EAAE7H,KAAK4H,EAAGU,EAAG,KAAKnI,KAAM,OAAO0H,EAE3J,OADID,EAAI,EAAGC,IAAGS,EAAK,CAAS,EAARA,EAAG,GAAQT,EAAElQ,QACzB2Q,EAAG,IACP,KAAK,EAAG,KAAK,EAAGT,EAAIS,EAAI,MACxB,KAAK,EAAc,OAAXR,EAAEC,QAAgB,CAAEpQ,MAAO2Q,EAAG,GAAInI,MAAM,GAChD,KAAK,EAAG2H,EAAEC,QAASH,EAAIU,EAAG,GAAIA,EAAK,CAAC,GAAI,SACxC,KAAK,EAAGA,EAAKR,EAAEI,IAAIK,MAAOT,EAAEG,KAAKM,MAAO,SACxC,QACI,MAAkBV,GAAZA,EAAIC,EAAEG,MAAY7M,OAAS,GAAKyM,EAAEA,EAAEzM,OAAS,KAAkB,IAAVkN,EAAG,IAAsB,IAAVA,EAAG,IAAW,CAAER,EAAI,EAAG,QAAU,CAC3G,GAAc,IAAVQ,EAAG,MAAcT,GAAMS,EAAG,GAAKT,EAAE,IAAMS,EAAG,GAAKT,EAAE,IAAM,CAAEC,EAAEC,MAAQO,EAAG,GAAI,KAAO,CACrF,GAAc,IAAVA,EAAG,IAAYR,EAAEC,MAAQF,EAAE,GAAI,CAAEC,EAAEC,MAAQF,EAAE,GAAIA,EAAIS,EAAI,KAAO,CACpE,GAAIT,GAAKC,EAAEC,MAAQF,EAAE,GAAI,CAAEC,EAAEC,MAAQF,EAAE,GAAIC,EAAEI,IAAI7P,KAAKiQ,GAAK,KAAO,CAC9DT,EAAE,IAAIC,EAAEI,IAAIK,MAChBT,EAAEG,KAAKM,MAAO,SAEtBD,EAAKZ,EAAK1H,KAAK+G,EAASe,EAC5B,CAAE,MAAOhK,GAAKwK,EAAK,CAAC,EAAGxK,GAAI8J,EAAI,CAAG,CAAE,QAAUD,EAAIE,EAAI,CAAG,CACzD,GAAY,EAARS,EAAG,GAAQ,MAAMA,EAAG,GAAI,MAAO,CAAE3Q,MAAO2Q,EAAG,GAAKA,EAAG,QAAK,EAAQnI,MAAM,EAC9E,CAtBgDmH,CAAK,CAAC5H,EAAGqF,GAAK,CAAG,CAuBrE,EACIvF,EAAUtC,MAAQA,KAAKsC,QAAW,SAAUC,EAAGC,GAC/C,IAAIC,EAAsB,mBAAXC,QAAyBH,EAAEG,OAAOC,UACjD,IAAKF,EAAG,OAAOF,EACf,IAAmBK,EAAYhC,EAA3BiC,EAAIJ,EAAEK,KAAKP,GAAOQ,EAAK,GAC3B,IACI,WAAc,IAANP,GAAgBA,KAAM,MAAQI,EAAIC,EAAEG,QAAQC,MAAMF,EAAG5H,KAAKyH,EAAEnI,MACxE,CACA,MAAOiE,GAASkC,EAAI,CAAElC,MAAOA,EAAS,CACtC,QACI,IACQkE,IAAMA,EAAEK,OAASR,EAAII,EAAU,SAAIJ,EAAEK,KAAKD,EAClD,CACA,QAAU,GAAIjC,EAAG,MAAMA,EAAElC,KAAO,CACpC,CACA,OAAOqE,CACX,EACIymB,EAAiBxpB,MAAQA,KAAKwpB,eAAkB,SAAUC,EAAI/jB,EAAMgkB,GACpE,GAAIA,GAA6B,IAArBrkB,UAAUnH,OAAc,IAAK,IAA4B6E,EAAxBF,EAAI,EAAG+Y,EAAIlW,EAAKxH,OAAY2E,EAAI+Y,EAAG/Y,KACxEE,GAAQF,KAAK6C,IACR3C,IAAIA,EAAK6B,MAAM1E,UAAUoD,MAAMR,KAAK4C,EAAM,EAAG7C,IAClDE,EAAGF,GAAK6C,EAAK7C,IAGrB,OAAO4mB,EAAGhuB,OAAOsH,GAAM6B,MAAM1E,UAAUoD,MAAMR,KAAK4C,GACtD,EACApL,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQsgC,UAAYtgC,EAAQugC,uBAAyBvgC,EAAQwgC,4BAAyB,EACtF,IACIA,EADAC,EAAY,EAAQ,OAExB,SAAWD,GACPA,EAAuBA,EAAgD,wBAAI,GAAK,0BAChFA,EAAuBA,EAAgD,wBAAI,GAAK,0BAChFA,EAAuBA,EAA6C,qBAAI,GAAK,uBAC7EA,EAAuBA,EAA0C,kBAAI,GAAK,oBAC1EA,EAAuBA,EAA2C,mBAAI,GAAK,qBAC3EA,EAAuBA,EAA2C,mBAAI,GAAK,qBAC3EA,EAAuBA,EAAsC,cAAI,GAAK,gBACtEA,EAAuBA,EAAuC,eAAI,GAAK,iBACvEA,EAAuBA,EAAwC,gBAAI,GAAK,kBACxEA,EAAuBA,EAAsC,cAAI,GAAK,gBACtEA,EAAuBA,EAAyC,iBAAI,IAAM,mBAC1EA,EAAuBA,EAAwC,gBAAI,IAAM,kBAEzEA,EAAuBA,EAA8B,MAAI,IAAM,QAC/DA,EAAuBA,EAA6B,KAAI,IAAM,OAC9DA,EAAuBA,EAAwC,gBAAI,IAAM,kBACzEA,EAAuBA,EAAsD,8BAAI,IAAM,gCACvFA,EAAuBA,EAAiD,yBAAI,IAAM,0BACrF,CAnBD,CAmBGA,IAA2BxgC,EAAQwgC,uBAAyBA,EAAyB,CAAC,IAEzFxgC,EAAQugC,uBAAyB,mBACjC,IAAIG,EAA2B,WAC3B,SAASA,IACLl7B,KAAKm7B,eAAiB,EACtBn7B,KAAKo7B,iBAAmB,EACxBp7B,KAAKq7B,mBAAqB,KAC1Br7B,KAAKs7B,UAAW,EAChBt7B,KAAKu7B,UAAY,EACjBv7B,KAAKw7B,GAAK,KACVx7B,KAAKy7B,qBAAsB,EAC3Bz7B,KAAK07B,aAAe,GACpB17B,KAAK27B,WAAa,EACtB,CAghBA,OA/gBArhC,OAAOC,eAAe2gC,EAAUh7B,UAAW,cAAe,CACtDpD,IAAK,WACD,OAAOkD,KAAK07B,YAChB,EACAn+B,YAAY,EACZC,cAAc,IAElB09B,EAAUh7B,UAAU07B,MAAQ,SAAU3R,EAAOrS,GACzC5X,KAAK27B,WAAW/7B,SAAQ,SAAUi8B,GAAM,OAAOA,EAAG5R,EAAOrS,EAAQ,GACrE,EACAsjB,EAAUh7B,UAAU47B,iBAAmB,SAAUn/B,GACrB,mBAAbA,GACPqD,KAAK27B,WAAWxgC,KAAKwB,EAE7B,EAEAu+B,EAAUh7B,UAAU67B,qBAAuB,SAAUC,EAAUC,GACvDl+B,UACAoB,QAAQC,IAAI,iBAAmB48B,EAAW,kBAAoBC,GAIlE,IAFA,IAAIC,EAAKF,EAASn9B,MAAM,KACpBs9B,EAAKF,EAASp9B,MAAM,KACf7D,EAAM,EAAGA,EAAMkhC,EAAGh+B,SAAUlD,EAAK,CACtC,IAAIohC,EAAI7X,SAAS2X,EAAGlhC,IAChByJ,EAAI8f,SAAS4X,EAAGnhC,IAAQ,KAC5B,GAAIohC,IAAM33B,EAQN,OAJIA,EAAI23B,EAAI,IAAMp8B,KAAKy7B,sBACnBz7B,KAAKy7B,qBAAsB,EAC3Bz7B,KAAK47B,MAAMZ,EAAuBA,EAAuBqB,iBAAkB,CAAEC,UAAWtB,EAAuBqB,gBAAiBE,IAAK,YAElIH,EAAI33B,CAEnB,CACA,OAAI03B,EAAGj+B,OAASg+B,EAAGh+B,QACP,EAGD,CAEf,EAMAg9B,EAAUh7B,UAAUs8B,sBAAwB,SAAUj3B,GAClD,IAAInF,EAAQJ,KAEZ,OADAA,KAAKZ,IAAI,UAA8CmG,GAChD,IAAI7E,SAAQ,SAAUuJ,GACzBgL,OAAc,MAAE,CACZH,OAAQ,OACRvP,IAAKA,EACL8hB,QAAS,MACV/c,MAAK,SAAUksB,GACVA,EACAvsB,EAAQusB,EAASE,QAAU,KAAOF,EAASE,OAAS,MAGpDt2B,EAAMhB,IAAI,uBACV6K,GAAQ,GAEhB,IAAG4c,OAAM,SAAU/lB,GACfV,EAAMhB,IAAI,WAAoD0B,GAC9DmJ,GAAQ,EACZ,GACJ,GACJ,EAOAixB,EAAUh7B,UAAUu8B,gBAAkB,SAAUC,EAAUC,GACtD,OAAO/yB,EAAU5J,UAAM,OAAQ,GAAQ,WACnC,IAAIiO,EAAS2uB,EAAKC,EAAQC,EAAmBvU,EAASwU,EAAMC,EAAMC,EAClE,OAAO1yB,EAAYvK,MAAM,SAAU7C,GAC/B,OAAQA,EAAG0N,OACP,KAAK,EAGD,GAFAoD,EAAU0uB,EAAoB1uB,QAAS2uB,EAAMD,EAAoBC,IACjEC,EAASD,GACJA,EAAK,MAAO,CAAC,EAAa,GAC/BE,EAAS7B,EAAUzuB,QAAQ0wB,aAAe,SAASzhC,OAAOihC,EAASzuB,QAAS,KAAKxS,OAAOwS,EAAS,qBACjG9Q,EAAG0N,MAAQ,EACf,KAAK,EAED,OADA1N,EAAG4N,KAAK5P,KAAK,CAAC,EAAG,EAAG,CAAE,IACf,CAAC,EAAa6E,KAAKw8B,sBAAsBM,IACpD,KAAK,EASD,OARY3/B,EAAG2N,OAMX9K,KAAKZ,IAAI,sBAA6E3D,OAAOqhC,KAJ7FD,GAAS,EACT78B,KAAKZ,IAAI,yBAA+F3D,OAAOqhC,KAK5G,CAAC,EAAa,GACzB,KAAK,EAGD,OAFAvU,EAAUprB,EAAG2N,OACb9K,KAAKZ,IAAI,+BAAmD3D,OAAO8sB,GAAUA,aAAyC,EAASA,EAAQ5pB,OAChI,CAAC,EAAa,GACzB,KAAK,EAOD,OANAo+B,EAAO9B,EAAUzuB,QAAQ0wB,aAAe,KAAOL,EAAS,QAAQphC,OAAOihC,EAASzuB,QAAS,KAAKxS,OAAOwS,GAAWA,GAChH+uB,EAAOD,GAAQF,EAAS,oBAAsB,yBAC9CI,EAAOF,GAAQF,EAAS,oBAAsB,yBAC9CH,EAASK,KAAOA,EAChBL,EAASM,KAAOA,EAChBN,EAASO,KAAOA,EACT,CAAC,EAAcP,GAElC,GACJ,GACJ,EAKAxB,EAAUh7B,UAAUi9B,mBAAqB,WACrC,IAAIC,EAAqBp9B,KAAK07B,aAAe,oBAI7C,GAHI39B,UACAiC,KAAKZ,IAAI,8BAAuD3D,OAAO2hC,KAEtEzgB,IAAI0gB,UAAUC,YAAYF,GAI3B,OAHIr/B,UACAiC,KAAKZ,IAAI,2BAAoD3D,OAAO2hC,IAEjE,KAEX,IAAIG,EAAqB5gB,IAAI0gB,UAAUG,kBAAkBJ,GACzD,OAAKG,GAIDx/B,UACAiC,KAAKZ,IAAI,wBAAkC3D,OAAO8hC,IAE5B/1B,KAAKC,MAAM81B,KANjCv9B,KAAKZ,IAAI,4BAA0D3D,OAAO2hC,IACnE,KAOf,EAEAlC,EAAUh7B,UAAUu9B,uBAAyB,SAAUd,GACnD,OAAO/yB,EAAU5J,UAAM,OAAQ,GAAQ,WACnC,IAAIo9B,EAAoBM,EAAqB/U,EAASgV,EACtD,OAAOpzB,EAAYvK,MAAM,SAAU7C,GAC/B,OAAQA,EAAG0N,OACP,KAAK,EAGD,GAFAuyB,EAAqBp9B,KAAK07B,aAAe,sBACzCgC,EAAsB19B,KAAKm9B,sBAEvB,MAAO,CAAC,GAEZhgC,EAAG0N,MAAQ,EACf,KAAK,EAED,OADA1N,EAAG4N,KAAK5P,KAAK,CAAC,EAAG,EAAG,CAAE,IACf,CAAC,EAAa6E,KAAKy8B,gBAAgBiB,EAAqBf,IACnE,KAAK,EAED,OADAx/B,EAAG2N,OACI,CAAC,EAAa,GACzB,KAAK,EAGD,OAFA6d,EAAUxrB,EAAG2N,OACb9K,KAAKZ,IAAI,yBAA6C3D,OAAOktB,GAAUA,aAAyC,EAASA,EAAQhqB,OAC1H,CAAC,EAAa,GACzB,KAAK,EACGZ,UACAiC,KAAKZ,IAAI,gCAA6E3D,OAAO+L,KAAKyR,UAAUykB,KAEhH,IACI/gB,IAAI0gB,UAAUO,kBAAkBp2B,KAAKyR,UAAUykB,GAAsBN,EACzE,CACA,MAAO1+B,GACHsB,KAAKZ,IAAI,YAA0C3D,OAAO2hC,EAAoB,aAAsC3hC,OAAO+L,KAAKyR,UAAUykB,IAC9I,CAKA,OAJI3/B,WACA4/B,EAAUhhB,IAAI0gB,UAAUG,kBAAkBJ,GAC1Cp9B,KAAKZ,IAAI,yBAA0F3D,OAAOkiC,KAEvG,CAAC,GAEpB,GACJ,GACJ,EACAzC,EAAUh7B,UAAU29B,WAAa,SAAUz/B,EAAK3D,GACvB,iBAAVA,IACPA,EAAQ+M,KAAKyR,UAAUxe,IAE3B+G,GAAG+L,IAAI6L,aAAaP,QAAQza,EAAK3D,EACrC,EACAygC,EAAUh7B,UAAU49B,WAAa,SAAU1/B,EAAKmb,QACvB,IAAjBA,IAA2BA,EAAe,MAC9C,IAAI9e,EAAQ+G,GAAG+L,IAAI6L,aAAalG,QAAQ9U,GACxC,IAAK3D,EACD,OAAO8e,EAEX,IACI,OAAO/R,KAAKC,MAAMhN,EACtB,CACA,MAAO0C,GACH,OAAO1C,CACX,CACJ,EAOAygC,EAAUh7B,UAAU69B,YAAc,SAAUC,EAAiBrB,GACzD,OAAO/yB,EAAU5J,UAAM,OAAQ,GAAQ,WACnC,IAAIi+B,EAAeC,EAAYC,EAAiBC,EAAe1B,EAAU2B,EAAeC,EAAcrwB,EAASswB,EAAYC,EAAaC,EACxI,OAAOl0B,EAAYvK,MAAM,SAAU7C,GAC/B,OAAQA,EAAG0N,OACP,KAAK,EAED,OADAozB,EAAgBtB,EAAoB1uB,QAC/BzM,GAAG+L,IAAIV,UAOZ7M,KAAK47B,MAAMZ,EAAuBA,EAAuB0D,OAAQ,CAAEpC,UAAWtB,EAAuB0D,MAAOnC,IAAK,SACjHv8B,KAAK07B,cAAgB/e,IAAI0gB,UAAY1gB,IAAI0gB,UAAUsB,kBAAoB,KAAO,sBAC1E5gC,WACAiC,KAAKZ,IAAI,QAAUY,KAAK07B,cACxB17B,KAAKZ,IAAI,cAAe6+B,GACxBj+B,KAAKZ,IAAI,WAAoD67B,EAAUzuB,QAAQ0wB,cAC/El9B,KAAKZ,IAAI,kBAA2C3D,OAAO+L,KAAKyR,UAAU+kB,MAE9EE,EAAaF,EAAgB/vB,SAC7BkwB,EAAkBn+B,KAAKm9B,wBAEnBiB,EAAgBp+B,KAAK+7B,qBAAqBiC,EAAgB/vB,QAASkwB,EAAgBlwB,UAC/D,IAChBiwB,EAAaC,EAAgBlwB,QAC7BjO,KAAKZ,IAAI,UAA6C3D,OAAO0iC,EAAgBlwB,WAEjFjO,KAAKZ,IAAI,wBAAsD3D,OAAO2iC,EAAe,aAAgD3iC,OAAOyiC,EAAY,WAA+BziC,OAAOuiC,EAAgB/vB,QAAS,WAAoCxS,OAAO0iC,EAAgBlwB,WAGlRjO,KAAKZ,IAAI,WAAoC3D,OAAOuiC,EAAgB/vB,UAEpEiwB,IAAeD,GACfj+B,KAAKZ,IAAI,YAAyD3D,OAAOyiC,EAAY,UAA8BziC,OAAOwiC,EAAe,eACzIj+B,KAAK47B,MAAMZ,EAAuBA,EAAuB4D,MAAO,CAAEtC,UAAWtB,EAAuB4D,KAAMrC,IAAK,eACxG,CAAC,IAGL,CAAC,EAAav8B,KAAKy8B,gBAAgBuB,EAAiBrB,MAjCnD5+B,UACAiC,KAAKZ,IAAI,uBAAqD3D,OAAO+F,GAAG+L,IAAIV,WAEhF7M,KAAK47B,MAAMZ,EAAuBA,EAAuB4D,MAAO,CAAEtC,UAAWtB,EAAuB4D,KAAMrC,IAAK,WACxG,CAAC,IA8BhB,KAAK,EA+BD,OA7BAp/B,EAAG2N,OACH9K,KAAKq7B,mBAAqB7zB,KAAKyR,UAAU+kB,GACrCjgC,UACAiC,KAAKZ,IAAI,kBAAmBY,KAAKq7B,oBAErCr7B,KAAKw7B,GAAK,IAAI7e,IAAIkiB,cAAc,GAAI7+B,KAAK07B,aAAc17B,KAAK+7B,qBAAqBlrB,KAAK7Q,OAGtFA,KAAKw7B,GAAGsD,mBAAkB,SAAUlhC,EAAMmD,GAStC,OAPiBA,EAAMg+B,WAELh+B,EAAMi+B,IAELj+B,EAAMnD,KAEdmD,EAAMsgB,MAGN,CAMf,IACItjB,UACAiC,KAAKZ,IAAI,6CAETY,KAAKs7B,UACDv9B,UACAiC,KAAKZ,IAAI,cAEN,CAAC,KAEZY,KAAKs7B,UAAW,EACZv9B,UACAiC,KAAKZ,IAAI,yBAGN,CAAC,EAAaY,KAAKy9B,uBAAuBd,KACrD,KAAK,EA2BD,OAzBAx/B,EAAG2N,OACH9K,KAAKq+B,cAAgB,KACjBr+B,KAAKw7B,GAAGyD,aAAetiB,IAAIkiB,cAAcK,MAAMC,WAC3CphC,UACAiC,KAAKZ,IAAI,+BAEbs9B,EAAW,IAAI/f,IAAIyiB,SAASp/B,KAAKq7B,mBAAoBr7B,KAAK07B,cAC1D17B,KAAKw7B,GAAG6D,kBAAkB3C,EAAU18B,KAAK07B,cACzC17B,KAAKq+B,cAAgBr+B,KAAKw7B,GAAG8D,mBAC7Bt/B,KAAK69B,WAAWrjC,EAAQugC,uBAAwB/6B,KAAKq+B,cAAckB,eAEnExhC,WACAsgC,EAAgBr+B,KAAKq+B,cACrBC,EAAeD,EAAcmB,kBAC7BvxB,EAAUowB,EAAckB,aACxBhB,EAAaF,EAAcoB,gBAC3BjB,EAAcH,EAAcqB,qBAC5BjB,EAAaJ,EAAcsB,oBAC3B3/B,KAAKZ,IAAI,sCACTY,KAAKZ,IAAI,2BAA4Bk/B,GACrCt+B,KAAKZ,IAAI,qBAAsB6O,GAC/BjO,KAAKZ,IAAI,wBAAyBm/B,GAClCv+B,KAAKZ,IAAI,+BAAgCo/B,GACzCx+B,KAAKZ,IAAI,8BAA+Bq/B,IAEvCz+B,KAAKq+B,eAAkBr+B,KAAKq+B,cAAcuB,YAO3C7hC,UACAiC,KAAKZ,IAAI,2BAEbY,KAAKw7B,GAAGqE,iBAAiB7/B,KAAK8/B,aAAajvB,KAAK7Q,OAChDA,KAAKw7B,GAAGuE,cACD,CAAC,KAXAhiC,UACAiC,KAAKZ,IAAI,2BAEbY,KAAK47B,MAAMZ,EAAuBA,EAAuB4D,MAAO,CAAEtC,UAAWtB,EAAuB4D,KAAMrC,IAAK,gBACxG,CAAC,IASxB,GACJ,GACJ,EAEArB,EAAUh7B,UAAU4/B,aAAe,SAAUloB,GACzC,IAAIxX,EAAQJ,KACRs8B,EAAY1kB,EAAMooB,eAClBC,EAAejF,EAAuBsB,GACtC4D,GAAW,EACX3D,EAAM,GAIV,OAHIx+B,UACAiC,KAAKZ,IAAI,sBAAmE3D,OAAO6gC,EAAW,KAAK7gC,OAAOwkC,IAEtG3D,GACJ,KAAK3f,IAAIwjB,mBAAmBC,wBACxBF,GAAW,EACX3D,EAAM,2BACN,MACJ,KAAK5f,IAAIwjB,mBAAmBE,wBAC5B,KAAK1jB,IAAIwjB,mBAAmBG,qBACxBJ,GAAW,EACX3D,EAAM,0BACN,MACJ,KAAK5f,IAAIwjB,mBAAmBI,mBACxBL,GAAW,EACX3D,EAAM,sBACN,MACJ,KAAK5f,IAAIwjB,mBAAmBK,kBACpBziC,UACAiC,KAAKZ,IAAI,2BAA4F3D,OAAOuE,KAAKw7B,GAAGiF,gBAAiB,MAAMhlC,OAAO6gC,EAAW,KAAK7gC,OAAOwkC,IAE7KjgC,KAAK47B,MAAMZ,EAAuBA,EAAuBwF,mBAAoB,CAAElE,UAAWtB,EAAuBwF,kBAAmBjE,IAAK,uBAAwF9gC,OAAOuE,KAAKw7B,GAAGiF,gBAAiB,SAAUC,SAAU9oB,IACrRpO,YAAW,WACPpJ,EAAM06B,WACV,GAAG,KACH,MACJ,QACI,OAEJoF,GACAlgC,KAAK47B,MAAMZ,EAAuBA,EAAuB4D,MAAO,CAAEtC,UAAWtB,EAAuB4D,KAAMrC,IAAK,GAAG9gC,OAAO8gC,EAAK,MAAM9gC,OAAO6gC,EAAW,KAAK7gC,OAAOwkC,EAAc,KAAKxkC,OAAOmc,EAAM+oB,cAAeD,SAAU9oB,IAE/N5X,KAAKw7B,GAAGqE,iBAAiB,KAC7B,EAEA3E,EAAUh7B,UAAU46B,UAAY,WAI5B,GAHI/8B,UACAiC,KAAKZ,IAAI,uCAERY,KAAKq+B,gBAAkBr+B,KAAKq+B,cAAcuB,WAK3C,OAJI7hC,UACAiC,KAAKZ,IAAI,6BAEbY,KAAK47B,MAAMZ,EAAuBA,EAAuB4D,MAAO,CAAEtC,UAAWtB,EAAuB4D,KAAMrC,IAAK,cAAemE,SAAU,OAGxI3iC,UACAiC,KAAKZ,IAAI,sCAEbY,KAAKw7B,GAAGqE,iBAAiB7/B,KAAK4gC,SAAS/vB,KAAK7Q,OAC5CA,KAAKw7B,GAAGqF,SACR7gC,KAAKm7B,eAAiB,EACtBn7B,KAAKo7B,iBAAmB,EACxBp7B,KAAKu7B,UAAY,CACrB,EAEAL,EAAUh7B,UAAU0gC,SAAW,SAAUhpB,GACrC,IAAIkpB,GAAmB,EACnBZ,GAAW,EACX3D,EAAM,GACND,EAAY1kB,EAAMooB,eAClBC,EAAejF,EAAuBsB,GAI1C,OAHIv+B,UACAoB,QAAQC,IAAI,qBAAwD3D,OAAO6gC,EAAW,KAAK7gC,OAAOwkC,EAAc,MAE5G3D,GACJ,KAAK3f,IAAIwjB,mBAAmBY,gBACxBD,GAAmB,EACnBvE,EAAM,kBAAgD9gC,OAAOwkC,EAAc,KAAKxkC,OAAO6gC,EAAW,KAAK7gC,OAAOmc,EAAM+oB,cACpH,MACJ,KAAKhkB,IAAIwjB,mBAAmBC,wBACxBF,GAAW,EACX3D,EAAM,+BAAyH9gC,OAAO6gC,EAAW,KAAK7gC,OAAOwkC,GAC7J,MACJ,KAAKtjB,IAAIwjB,mBAAmBa,cACxBzE,EAAM,oBAAkD9gC,OAAOwkC,EAAc,KAAKxkC,OAAO6gC,EAAW,KAAK7gC,OAAOmc,EAAMqpB,aAAc,KACpI,MACJ,KAAKtkB,IAAIwjB,mBAAmBe,mBACxB,IAAIC,EAAgBvpB,EAAMwpB,qBACtBC,EAAazpB,EAAM0pB,gBACnBC,EAAgB3pB,EAAM4pB,qBACtBC,EAAa7pB,EAAM6oB,gBACvBzgC,KAAKm7B,eAAiBvjB,EAAMwpB,qBAC5BphC,KAAKo7B,iBAAmBxjB,EAAM0pB,gBAC1BvjC,UACAiC,KAAKZ,IAAI,2BAAkF3D,OAAO0lC,EAAe,KAAK1lC,OAAO4lC,EAAY,YAA0C5lC,OAAO8lC,EAAe,KAAK9lC,OAAOgmC,EAAY,OAAOhmC,OAAOwkC,EAAc,MAAMxkC,OAAO6gC,EAAW,QAEzRt8B,KAAK47B,MAAMZ,EAAuBA,EAAuBkG,oBAAqB,CAAE5E,UAAWA,EAAWC,IAAK,aAA+D9gC,OAAO0lC,EAAe,OAAO1lC,OAAO4lC,EAAY,KAAK5lC,OAAOwkC,EAAc,KAAKxkC,OAAO6gC,GAAYoE,SAAU9oB,IACtR,MACJ,KAAK+E,IAAIwjB,mBAAmBE,wBAC5B,KAAK1jB,IAAIwjB,mBAAmBG,qBACxBJ,GAAW,EACX3D,EAAM,2BAAsG9gC,OAAO6gC,EAAW,KAAK7gC,OAAOwkC,EAAc,KAAKxkC,OAAOmc,EAAM+oB,cAC1K,MACJ,KAAKhkB,IAAIwjB,mBAAmBI,mBACxBL,GAAW,EACX3D,EAAM,wBAAoF9gC,OAAO6gC,EAAW,KAAK7gC,OAAOwkC,GACxH,MACJ,KAAKtjB,IAAIwjB,mBAAmBuB,cACpB3jC,UACAoB,QAAQC,IAAI,UAAW,mEAAoE,kBAAsC3D,OAAOmc,EAAM+oB,aAAc,kBAAgDllC,OAAOuE,KAAKm7B,eAAgB,mBAAsD1/B,OAAOuE,KAAKo7B,mBAE9Sp7B,KAAKu7B,YACDv7B,KAAKu7B,WAAa,EAClBv7B,KAAKw7B,GAAGmG,wBAGRzB,GAAW,EACX3D,EAAM,8BAAmH9gC,OAAO6gC,EAAW,KAAK7gC,OAAOwkC,EAAc,KAAKxkC,OAAOmc,EAAM+oB,eAE3L,MACJ,KAAKhkB,IAAIwjB,mBAAmByB,eACxB,KAEShqB,EAAM+oB,cAAgB,IAAI3hC,SAAS,UAEpC2d,IAAI0gB,UAAUwE,WAAW,GAAGpmC,OAAOuE,KAAK07B,aAAc,UAAUjgC,OAAOmc,EAAMqpB,aAAc,QAEnG,CACA,MAAOviC,GACHS,QAAQT,MAAM,UAAW,mEAAoE,eAAiEjD,OAAO6gC,EAAW,KAAK7gC,OAAOwkC,EAAc,KAAKxkC,OAAOiD,EAAO,KACjO,CACAsB,KAAKZ,IAAI,oBAAkD3D,OAAOmc,EAAMqpB,aAAc,KAAKxlC,OAAOmc,EAAM+oB,eACxG,MACJ,KAAKhkB,IAAIwjB,mBAAmB2B,iBACpB/jC,UACAoB,QAAQC,IAAI,mBAA4C3D,OAAO6gC,EAAW,KAAK7gC,OAAOwkC,EAAc,KAAKxkC,OAAOmc,EAAM+oB,eAE1H,MACJ,KAAKhkB,IAAIwjB,mBAAmB4B,gBACxB7B,GAAW,EACX3D,EAAM,6BAA6B9gC,OAAO6gC,EAAW,KAAK7gC,OAAOwkC,EAAc,KAAKxkC,OAAOmc,EAAM+oB,cAQzG,GAHI5iC,UACAiC,KAAKZ,IAAI,UAAW,mEAAoEm9B,GAExF2D,EAKA,OAJAlgC,KAAKw7B,GAAGqE,iBAAiB,MACzB7/B,KAAK47B,MAAMZ,EAAuBA,EAAuB4D,MAAO,CAAEtC,UAAWtB,EAAuB4D,KAAMrC,IAAK,GAAG9gC,OAAO8gC,EAAK,KAAK9gC,OAAOmc,EAAM+oB,cAAeD,SAAU9oB,IACzK5X,KAAKs7B,UAAW,OAChBt7B,KAAKw7B,GAAG8D,mBAAmBC,aAG/B,GAAIuB,EAAkB,CAClB9gC,KAAKw7B,GAAGqE,iBAAiB,MAKzB,IAAImC,EAAsBhiC,KAAKw7B,GAAG8D,mBAAmB2C,iBACrDzgC,GAAG+L,IAAI6L,aAAaP,QAAQ,0BAA2BrR,KAAKyR,UAAU+oB,IAClEjkC,WACAiC,KAAKZ,IAAI,kBAA2C3D,OAAO+F,GAAG+L,IAAI6L,aAAalG,QAAQ,6BACvFlT,KAAKZ,IAAI,0BAA2F3D,OAAO+F,GAAG+L,IAAI6L,aAAalG,QAAQ,8BAE3IlT,KAAK47B,MAAMZ,EAAuBA,EAAuB+F,iBAAkB,CAAEzE,UAAWtB,EAAuB+F,gBAAiBxE,IAAK,SAA6B9gC,OAAOmc,EAAM+oB,cAAeD,SAAU9oB,IACxM5X,KAAKs7B,UAAW,CACpB,CACJ,EACAJ,EAAUh7B,UAAUd,IAAM,WAEtB,IADA,IAAIsM,EAAO,GACFC,EAAK,EAAGA,EAAKtG,UAAUnH,OAAQyN,IACpCD,EAAKC,GAAMtG,UAAUsG,GAErB5N,UAEAoB,QAAQC,IAAIgG,MAAMjG,QAASqqB,EAAc,CAAC,UAD9B,oEACiDlnB,EAAOoJ,IAAO,GAEnF,EACAwvB,EAAUh7B,UAAUxB,MAAQ,WAExB,IADA,IAAIgN,EAAO,GACFC,EAAK,EAAGA,EAAKtG,UAAUnH,OAAQyN,IACpCD,EAAKC,GAAMtG,UAAUsG,GAErB5N,UAEAoB,QAAQT,MAAM0G,MAAMjG,QAASqqB,EAAc,CAAC,UADhC,oEACmDlnB,EAAOoJ,IAAO,GAErF,EACOwvB,CACX,CA5hB8B,GA6hB9B1gC,EAAQsgC,UAAY,IAAII,C,iBCpnBxB5gC,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQ84B,kBAAe,EACvB,IAAIrV,EAAkB,EAAQ,MAC1BqV,EAA8B,WAC9B,SAASA,IACT,CA0IA,OAzIAh5B,OAAOC,eAAe+4B,EAAc,SAAU,CAI1Cx2B,IAAK,WACD,OAAOkD,KAAKkzB,OAChB,EACA31B,YAAY,EACZC,cAAc,IAOlB81B,EAAaC,cAAgB,SAAU2O,GACnC,GAAgB,OAAZA,EAAJ,CAGA,IAAI7O,EAAa6O,EAAQ7O,WACzB,GAAIA,EAAY,MACyB9qB,IAAjCvI,KAAKkzB,QAAQp2B,IAAIu2B,IACjBrzB,KAAKkzB,QAAQl2B,IAAIq2B,EAAY,IAGvB6O,EAAQ7O,WAAlB,IACIvE,EAAS9uB,KAAKkzB,QAAQp2B,IAAIolC,EAAQ7O,YAClCzrB,GAAO,EACX,GAAIknB,EAAQ,CACR,IAAK,IAAI9zB,EAAM,EAAG4gB,EAAIkT,aAAuC,EAASA,EAAO5wB,OAAQlD,EAAM4gB,EAAG5gB,IAC1F,GAAI8zB,EAAO9zB,GAAKoC,QAAU8kC,EAAQ9kC,MAAO,CACrCwK,GAAO,EACP,KACJ,CAECA,GACDknB,EAAO3zB,KAAK+mC,EAEpB,CACJ,CArBA,CAsBJ,EAOA5O,EAAaE,oBAAsB,SAAU5b,GACzC,GAAKA,EAAL,CAGA,IAAIub,EAAmBvb,EAAMnI,WACzBqf,EAAS9uB,KAAKkzB,QAAQp2B,IAAIq2B,GAC9B,GAAIrE,EACA,IAAK,IAAIvtB,EAAM,EAAGqa,EAAIkT,EAAO5wB,OAAQqD,EAAMqa,EAAGra,IAAO,CACjD,IACInE,EADU0xB,EAAOvtB,GACDnE,MAChB+kC,EAAkB/kC,EAAMke,WACxB6mB,IAAoBlkB,EAAgB/C,WAAWiB,QAAUgmB,IAAoBlkB,EAAgB9C,cAAcG,YAC3Gle,SAA8CA,EAAM21B,eAAenb,EAE3E,CAEJ,IAAIja,EAAMqC,KAAKoiC,mBAAmBtlC,IAAIq2B,GACtC,GAAIx1B,EAGA,GAFiBsgB,EAAgB9C,cAAcG,aAE5B2C,EAAgB/C,WAAWiB,OAC1C,IAAK,IAAIkmB,KAAM1kC,EAEX,IADA,IAAIg4B,EAAYh4B,EAAI0kC,GACXr+B,EAAM,EAAGA,GAAO2xB,aAA6C,EAASA,EAAUz3B,QAAS8F,KAC1FrH,EAAWg5B,EAAU3xB,KAErBrH,EAASib,OAMrB,CAAI+d,EAAYh4B,EAAIsgB,EAAgB9C,cAAcG,YAClD,IADA,IACS/C,EAAM,EAAGA,GAAOod,aAA6C,EAASA,EAAUz3B,QAASqa,IAAO,CACrG,IAAI5b,KAAWg5B,EAAUpd,KAErB5b,EAASib,EAEjB,CAN6D,CA7BrE,CAsCJ,EAMA0b,EAAaG,yBAA2B,SAAU7b,GAC9C,OAAO,IAAIlX,SAAQ,SAAUuJ,GACzB2N,EAAiB,UAAI,WAAc,OAAO3N,GAAW,EACrDqpB,EAAaE,oBAAoB5b,EACrC,GACJ,EAMA0b,EAAagP,oBAAsB,SAAUhnB,EAAY6X,EAAkBx2B,GACvE,IAAIgB,EAAMqC,KAAKoiC,mBAAmBtlC,IAAIq2B,GACjCx1B,IACDA,EAAM,CAAC,EACPqC,KAAKoiC,mBAAmBplC,IAAIm2B,EAAkBx1B,IAE7CA,EAAI2d,KACL3d,EAAI2d,GAAc,IAEtB3d,EAAI2d,GAAYngB,KAAKwB,EACzB,EAKA22B,EAAajxB,MAAQ,WAEjBrC,KAAKkzB,QAAQhiB,QAEblR,KAAKoiC,mBAAmBlxB,QACpBnT,UACAoB,QAAQC,IAAI,0BAEpB,EAIAk0B,EAAaJ,QAAU,IAAI7zB,IAI3Bi0B,EAAa8O,mBAAqB,IAAI/iC,IAC/Bi0B,CACX,CA7IiC,GA8IjC94B,EAAQ84B,aAAeA,C,eCjJvBh5B,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQmd,aAAU,EAClB,IAAIA,EAAyB,WACzB,SAASA,EAAQ4qB,GACbviC,KAAKwiC,SAAWD,CACpB,CAgDA,OA/CAjoC,OAAOC,eAAeod,EAAQzX,UAAW,QAAS,CAI9CpD,IAAK,WACD,IACIK,EADAiD,EAAQJ,KAeZ,OAbuB,QAAtB7C,EAAK6C,KAAKyiC,cAA2B,IAAPtlC,IAAsB6C,KAAKyiC,OAAS,SAAU9lC,EAAU+lC,GACnF,IAAIvlC,EAAI6J,EAAIC,EAAIC,EAAIC,EAAI0G,EACpB60B,IACA/lC,EAAWA,EAASkU,KAAK6xB,IAExBtiC,EAAMu7B,aACyF,QAA/F30B,EAA+B,QAAzB7J,EAAKiD,EAAMoiC,gBAA6B,IAAPrlC,OAAgB,EAASA,EAAGwlC,8BAA2C,IAAP37B,GAAyBA,EAAGlE,KAAK3F,EAAIiD,GAC7IA,EAAMu7B,WAAa,GAC4E,QAA9Fz0B,EAA+B,QAAzBD,EAAK7G,EAAMoiC,gBAA6B,IAAPv7B,OAAgB,EAASA,EAAG27B,6BAA0C,IAAP17B,GAAyBA,EAAGpE,KAAKmE,EAAI7G,IAEtD,QAAzFyN,EAA+B,QAAzB1G,EAAK/G,EAAMoiC,gBAA6B,IAAPr7B,OAAgB,EAASA,EAAG07B,wBAAqC,IAAPh1B,GAAyBA,EAAG/K,KAAKqE,EAAI/G,GACvIA,EAAMu7B,WAAWxgC,KAAK,CAAEwB,SAAUA,EAAU+lC,SAAUA,GAC1D,GACO1iC,KAAKyiC,MAChB,EACAllC,YAAY,EACZC,cAAc,IAOlBma,EAAQzX,UAAU0W,KAAO,SAAUgB,GAE/B,IADA,IAAI+jB,EAAa37B,KAAK27B,WACb3gC,EAAM,EAAGA,GAAO2gC,aAA+C,EAASA,EAAWz9B,QAASlD,IAAO,CACxG,IAAImC,EAAKw+B,EAAW3gC,GAAM2B,EAAWQ,EAAGR,SAAU+lC,EAAWvlC,EAAGulC,SAC5D/lC,GACAA,EAASyI,MAAMs9B,EAAU,CAAC9qB,GAElC,CACJ,EACAD,EAAQzX,UAAUq3B,QAAU,WACxB,IAAIp6B,EAAI6J,EACHhH,KAAK8iC,YACN9iC,KAAK8iC,WAAY,EACjB9iC,KAAK27B,gBAAapzB,EAC8E,QAA/FvB,EAA8B,QAAxB7J,EAAK6C,KAAKwiC,gBAA6B,IAAPrlC,OAAgB,EAASA,EAAG4lC,+BAA4C,IAAP/7B,GAAyBA,EAAGlE,KAAK3F,GAEjJ,EACOwa,CACX,CApD4B,GAqD5Bnd,EAAQmd,QAAUA,C,GCvDdqrB,yBAA2B,CAAC,EAGhC,SAASC,oBAAoBC,GAE5B,IAAIC,EAAeH,yBAAyBE,GAC5C,QAAqB36B,IAAjB46B,EACH,OAAOA,EAAa3oC,QAGrB,IAAIyhB,EAAS+mB,yBAAyBE,GAAY,CAGjD1oC,QAAS,CAAC,GAOX,OAHA4oC,oBAAoBF,GAAUpgC,KAAKmZ,EAAOzhB,QAASyhB,EAAQA,EAAOzhB,QAASyoC,qBAGpEhnB,EAAOzhB,OACf,C,2BCqCI6oC,OAzDAtd,gBACAud,kBACAC,SACAC,UACAC,kBACAC,QACAC,UACAC,WACAC,UACAC,YACAlQ,cACAC,kBACAkQ,YACAC,OACAC,YACAC,OACAC,mBACAC,oBACAC,mBACA53B,mBACA63B,kBACAC,oBACAlS,iBACAmS,MAEAC,OACAC,QACAltB,SACAmtB,YACAC,OACAC,WACA/kC,YACAglC,UACAC,wBACAC,aACAC,SACAtyB,UACAob,YACA5Y,kBACA+vB,UACAC,QACA1tB,QACAqc,KACAsR,OACAC,eACAC,MACAC,QACAhT,eACAC,UACAgT,SACAC,cACAxnB,gBACAynB,QACA1nC,QACA21B,aACAgS,mBAvDA5f,gBAAkB,oBAAQ,MAC1Bud,kBAAoB,oBAAQ,KAC5BC,SAAW,oBAAQ,KACnBC,UAAY,oBAAQ,MACpBC,kBAAoB,oBAAQ,MAC5BC,QAAU,oBAAQ,MAClBC,UAAY,oBAAQ,MACpBC,WAAa,oBAAQ,MACrBC,UAAY,oBAAQ,MACpBC,YAAc,oBAAQ,MACtBlQ,cAAgB,oBAAQ,KACxBC,kBAAoB,oBAAQ,MAC5BkQ,YAAc,oBAAQ,MACtBC,OAAS,oBAAQ,MACjBC,YAAc,oBAAQ,KACtBC,OAAS,oBAAQ,KACjBC,mBAAqB,oBAAQ,MAC7BC,oBAAsB,oBAAQ,MAC9BC,mBAAqB,oBAAQ,KAC7B53B,mBAAqB,oBAAQ,MAC7B63B,kBAAoB,oBAAQ,MAC5BC,oBAAsB,oBAAQ,MAC9BlS,iBAAmB,oBAAQ,MAC3BmS,MAAQ,oBAAQ,MAEhBC,OAAS,oBAAQ,MACjBC,QAAU,oBAAQ,MAClBltB,SAAW,oBAAQ,MACnBmtB,YAAc,oBAAQ,MACtBC,OAAS,oBAAQ,MACjBC,WAAa,oBAAQ,MACrB/kC,YAAc,oBAAQ,MACtBglC,UAAY,oBAAQ,KACpBC,wBAA0B,oBAAQ,MAClCC,aAAe,oBAAQ,MACvBC,SAAW,oBAAQ,MACnBtyB,UAAY,oBAAQ,MACpBob,YAAc,oBAAQ,MACtB5Y,kBAAoB,oBAAQ,MAC5B+vB,UAAY,oBAAQ,MACpBC,QAAU,oBAAQ,MAClB1tB,QAAU,oBAAQ,MAClBqc,KAAO,oBAAQ,MACfsR,OAAS,oBAAQ,MACjBC,eAAiB,oBAAQ,MACzBC,MAAQ,oBAAQ,MAChBC,QAAU,oBAAQ,KAClBhT,eAAiB,oBAAQ,MACzBC,UAAY,oBAAQ,MACpBgT,SAAW,oBAAQ,MACnBC,cAAgB,oBAAQ,MACxBxnB,gBAAkB,oBAAQ,MAC1BynB,QAAU,oBAAQ,MAClB1nC,QAAU,oBAAQ,MAClB21B,aAAe,oBAAQ,MACvBgS,mBAAqB,oBAAQ,MAGjC,SAAWtC,GAGPA,EAAOllC,MAAQunC,QAAQvnC,MAEvBklC,EAAO/I,OAASkL,SAASlL,OACzB+I,EAAOloB,cAAgB8C,gBAAgB9C,cAEvCkoB,EAAO1rB,QAAUH,SAASG,QAC1B0rB,EAAO/P,aAAef,eAAee,aACrC+P,EAAOha,QAAUmJ,UAAUnJ,QAC3Bga,EAAO9zB,YAAck2B,cAAcl2B,YAGnC8zB,EAAOuC,QAAU,CACbxkB,mBAAoB2E,gBAAgB3E,oBAGxCiiB,EAAO58B,gBAAkB68B,kBAAkB78B,gBAE3C48B,EAAOwC,OAAS,CACZ3iC,uBAAwBqgC,SAASrgC,uBACjCO,aAAc8/B,SAAS9/B,aACvBM,YAAaw/B,SAASx/B,YACtBE,gBAAiBs/B,SAASt/B,iBAG9Bo/B,EAAOyC,MAAQ,CACXjd,eAAgB2a,UAAU3a,eAC1BC,QAAS0a,UAAU1a,QACnBnJ,gBAAiB8jB,kBAAkB9jB,gBACnC0G,cAAeqd,QAAQrd,cACvBL,MAAO0d,QAAQ1d,MACf4Q,QAAS+M,UAAU/M,QACnB/F,SAAU+S,WAAW/S,SACrBzE,QAASyX,UAAUzX,SAGvBiX,EAAO5wB,UAAYqxB,YAAYrxB,UAE/B4wB,EAAOxjC,YAAc+zB,cAAc/zB,YAEnCwjC,EAAOrlB,gBAAkB6V,kBAAkB7V,gBAE3CqlB,EAAOxZ,UAAYka,YAAYgC,QAE/B1C,EAAO2C,KAAO,CACVv3B,SAAUu1B,OAAOv1B,SACjBI,kBAAmBm1B,OAAOn1B,kBAC1BC,cAAek1B,OAAOl1B,cACtBC,aAAci1B,OAAOj1B,aACrBI,cAAe60B,OAAO70B,eAG1Bk0B,EAAOn+B,UAAY++B,YAAY/+B,UAE/Bm+B,EAAO4C,KAAO,CACVvrC,gBAAiBwpC,OAAOxpC,gBACxBa,aAAc2oC,OAAO3oC,aACrBG,YAAawoC,OAAOxoC,aAGxB2nC,EAAOzd,cAAgBue,mBAAmBve,cAC1Cyd,EAAO9b,SAAWgd,oBAAoBhd,SACtC8b,EAAOrpB,SAAWoqB,oBAAoBpqB,SACtCqpB,EAAO/3B,QAAU+4B,mBAAmB/4B,QACpC+3B,EAAO70B,QAAU/B,mBAAmB+B,QACpC60B,EAAOhvB,MAAQge,iBAAiBhe,MAChCgvB,EAAOhsB,cAAgBgb,iBAAiBhb,cACxCgsB,EAAOhpB,cAAgBiqB,kBAAkBjqB,cAEzCgpB,EAAO/R,cAAgBkT,MAAMlT,cAE7B+R,EAAO6C,UAAY,CACfnmB,cAAe0kB,OAAO1kB,cACtBE,cAAewkB,OAAOxkB,eAG1BojB,EAAO8C,WAAa,CAChBrkB,MAAO4iB,QAAQ5iB,OAGnBuhB,EAAOvI,UAAY6J,YAAY7J,UAE/BuI,EAAO7N,KAAOoP,OAAOpP,KAErB6N,EAAO+C,cAAgB,CACnBrZ,SAAU8X,WAAW9X,UAGzBsW,EAAOz2B,aAAey4B,eAAez4B,aAErCy2B,EAAOzhC,UAAY9B,YAAY8B,UAE/ByhC,EAAO3zB,aAAeqe,YAAYre,aAClC2zB,EAAOzyB,MAAQ,CACXhB,YAAame,YAAYne,YACzBE,eAAgBie,YAAYje,eAC5BC,iBAAkBge,YAAYhe,iBAC9BG,UAAW6d,YAAY7d,UACvBD,eAAgB8d,YAAY9d,gBAGhCozB,EAAOgD,QAAU,CACbt6B,UAAW+4B,UAAU/4B,UACrBG,YAAa44B,UAAU54B,aAG3Bm3B,EAAOv3B,YAAc,CACjBuU,yBAA0B0kB,wBAAwB1kB,0BAGtDgjB,EAAOvb,WAAakd,aAAald,WAEjCub,EAAOv/B,OAAS,CACZ8W,WAAYqqB,SAASrqB,YAGzByoB,EAAOpwB,QAAUN,UAAUM,QAE3BowB,EAAOiD,aAAe,CAClBjf,QAAS6d,UAAU7d,SAGvBgc,EAAOjuB,gBAAkBD,kBAAkBC,gBAE3CiuB,EAAO/X,KAAO8Z,OAAO9Z,KAErB+X,EAAO3b,MAAQ,CACXZ,UAAWqe,QAAQre,UACnBC,UAAWoe,QAAQpe,WAGvBsc,EAAOkD,UAAY,CACfzpB,SAAU9e,QAAQ8e,SAClBE,cAAehf,QAAQgf,cACvB/e,SAAUD,QAAQC,SAClBof,aAAcrf,QAAQqf,aACtBI,SAAUzf,QAAQyf,SAClBC,WAAY1f,QAAQ0f,WACpBC,UAAW3f,QAAQ2f,UACnB7K,YAAa9U,QAAQ8U,YACrB8K,UAAW5f,QAAQ4f,UACnBb,kBAAmB/e,QAAQ+e,kBAC3Be,WAAY9f,QAAQ8f,WACpBC,QAAS/f,QAAQ+f,SAGrBslB,EAAOhrB,MAAQZ,QAAQY,MAEvBgrB,EAAOvV,GAAKgG,KAAKhG,GAEjBuV,EAAO99B,IAAM,CACTslB,qBAAsBya,MAAMza,sBAGhCwY,EAAOmD,WAAa,CAChBlqC,SAAUipC,QAAQjpC,SAClBW,MAAOsoC,QAAQtoC,OAGnBomC,EAAOhkB,WAAasU,aAAatU,WAEjCgkB,EAAO3T,YAAc,SAAUjQ,GAC3B,OAAOkU,aAAatU,WAAWG,6BAA6B1c,KAAK6wB,aAAatU,WAAYI,EAC9F,EAEA4jB,EAAO3P,iBAAmBiS,mBAAmBjS,gBAChD,CAxKD,CAwKG2P,SAAWA,OAAS,CAAC,IAExBpuB,OAAOouB,OAASA,OAEhBpuB,OAAOya,YAAc2T,OAAO3T,W", "sources": ["webpack://block-blast/./assets/scripts/base/date/Date.ts", "webpack://block-blast/./assets/scripts/base/watch/watch.ts", "webpack://block-blast/./assets/scripts/base/cache/CacheRender.ts", "webpack://block-blast/./assets/scripts/base/arrays/arrays.ts", "webpack://block-blast/./assets/scripts/base/crypto/UrlCrypto.ts", "webpack://block-blast/./assets/scripts/base/animation/DragonbonesAnim.ts", "webpack://block-blast/./assets/scripts/base/decorators/DecoratorMeasure.ts", "webpack://block-blast/./assets/scripts/base/numbers/numbers.ts", "webpack://block-blast/./assets/scripts/modules/env/vo/EnvInfo.ts", "webpack://block-blast/./assets/scripts/base/copy/Copy.ts", "webpack://block-blast/./assets/scripts/falcon/ModuleEvent.ts", "webpack://block-blast/./assets/scripts/base/layer/GameLayer.ts", "webpack://block-blast/./assets/scripts/base/audio/AudioInfo.ts", "webpack://block-blast/./assets/scripts/base/decorators/DecoratorTrait.ts", "webpack://block-blast/./assets/scripts/base/storage/Storage.ts", "webpack://block-blast/./assets/scripts/base/decorators/DecoratorDebounce.ts", "webpack://block-blast/./assets/scripts/base/decorators/DecoratorScreen.ts", "webpack://block-blast/./assets/scripts/base/random/Random.ts", "webpack://block-blast/./assets/scripts/falcon/ModuleManager.ts", "webpack://block-blast/./assets/scripts/base/decorators/DecoratorMemoize.ts", "webpack://block-blast/./assets/scripts/base/crypto/ICrypto.ts", "webpack://block-blast/./assets/scripts/modules/traitConfig/vo/TraitConfigActiveBlockListInfo.ts", "webpack://block-blast/./assets/scripts/base/native/NativeBridge.ts", "webpack://block-blast/./assets/scripts/base/types/Types.ts", "webpack://block-blast/./assets/scripts/base/components/CacheComponents.ts", "webpack://block-blast/./assets/scripts/modules/prefab/vo/PrefabInfo.ts", "webpack://block-blast/./assets/scripts/base/async/DeferredPromise.ts", "webpack://block-blast/./assets/scripts/base/enum/enum.ts", "webpack://block-blast/./assets/scripts/base/decorators/DecoratorClassId.ts", "webpack://block-blast/./assets/scripts/base/performance/RenderingOptimization.ts", "webpack://block-blast/./assets/scripts/base/adapter/AdapterFringe.ts", "webpack://block-blast/./assets/scripts/base/equal/Equal.ts", "webpack://block-blast/./assets/scripts/base/traitConfig/TraitConfigInfo.ts", "webpack://block-blast/./assets/scripts/base/decorators/DecoratorAdapter.ts", "webpack://block-blast/./assets/scripts/base/async/First.ts", "webpack://block-blast/./assets/scripts/base/timer/Timer.ts", "webpack://block-blast/./assets/scripts/base/timeout/Timeout.ts", "webpack://block-blast/./assets/scripts/base/decorators/DecoratorThrottle.ts", "webpack://block-blast/./assets/scripts/base/pool/ObjectPool.ts", "webpack://block-blast/./assets/scripts/base/async/Barrier.ts", "webpack://block-blast/./assets/scripts/falcon/EventVo.ts", "webpack://block-blast/./assets/scripts/base/components/Component.ts", "webpack://block-blast/./assets/scripts/base/url/Url.ts", "webpack://block-blast/./assets/scripts/base/task/Task.ts", "webpack://block-blast/./assets/scripts/base/async/WaitFor.ts", "webpack://block-blast/./assets/scripts/base/trait/Trait.ts", "webpack://block-blast/./assets/scripts/base/ui/UI.ts", "webpack://block-blast/./assets/scripts/base/async/Sequence.ts", "webpack://block-blast/./assets/scripts/base/dot/Dot.ts", "webpack://block-blast/./assets/scripts/falcon/Proxy.ts", "webpack://block-blast/./assets/scripts/base/interval/Interval.ts", "webpack://block-blast/./assets/scripts/base/gameState/GameStateManager.ts", "webpack://block-blast/./assets/scripts/base/http/Http.ts", "webpack://block-blast/./assets/scripts/base/async/Limiter.ts", "webpack://block-blast/./assets/scripts/base/loader/ResLoader.ts", "webpack://block-blast/./assets/scripts/falcon/Module.ts", "webpack://block-blast/./assets/scripts/base/hotUpdate/HotUpdate.ts", "webpack://block-blast/./assets/scripts/falcon/EventManager.ts", "webpack://block-blast/./assets/scripts/base/events/Events.ts", "webpack://block-blast/webpack/bootstrap", "webpack://block-blast/./sdk/falcon.ts"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getLastSomeDays = getLastSomeDays;\nexports.getTodayDate = getTodayDate;\nexports.getDiffDays = getDiffDays;\n/**\n * 获取最近几天日期\n * [\n    \"2025_2_14\",\n    \"2025_2_13\",\n    \"2025_2_12\",\n    \"2025_2_11\",\n    \"2025_2_10\",\n    \"2025_2_9\",\n    \"2025_2_8\"\n   ]\n * @param day\n * @returns\n */\nfunction getLastSomeDays(day) {\n    if (day === void 0) { day = 7; }\n    var currentTime = Date.now();\n    var days = [];\n    for (var i_1 = 1; i_1 <= day; i_1++) {\n        var data = new Date();\n        data.setTime(currentTime - 24 * 60 * 60 * 1000 * i_1);\n        days.push(data.getFullYear() + \"_\" + data.getMonth() + \"_\" + data.getDate());\n    }\n    return days;\n}\n/**\n * 获取今天日期: 2025_3_24\n * @returns\n */\nfunction getTodayDate() {\n    var today = new Date();\n    return \"\".concat(today.getFullYear(), \"_\").concat(today.getMonth() + 1, \"_\").concat(today.getDate());\n}\n//获取两个日期之间相差的天数\nfunction getDiffDays(date1, date2) {\n    var date1WithoutTime = new Date(date1);\n    date1WithoutTime.setHours(0, 0, 0, 0);\n    var date2WithoutTime = new Date(date2);\n    date2WithoutTime.setHours(0, 0, 0, 0);\n    var diffTime = Math.abs(date1WithoutTime.getTime() - date2WithoutTime.getTime());\n    var diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return diffDays;\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ReactivePos = void 0;\nexports.reactive = reactive;\nexports.watch = watch;\nexports.watchDebug = watchDebug;\nvar Types_1 = require(\"../types/Types\");\nvar watchMap = new Map();\n/**\n * Reactive 位置\n */\nvar ReactivePos;\n(function (ReactivePos) {\n    ReactivePos[\"RandomChangeScoreLbColorTraitIsComboReachTargetTraitInit\"] = \"RandomChangeScoreLbColorTraitIsComboReachTargetTraitInit\";\n    ReactivePos[\"GuideFirstLifeTrait\"] = \"GuideFirstLifeTrait\";\n    ReactivePos[\"MoreTimeToOpreateStateTraitIsBlocksProducerTouchShowGameOverStatus\"] = \"MoreTimeToOpreateStateTraitIsBlocksProducerTouchShowGameOverStatus\";\n})(ReactivePos || (exports.ReactivePos = ReactivePos = {}));\n/**\n * 监听目标属性变化\n * @param target\n * @param propertyName\n * @param callback\n * @example\n *  reactive(gameInfo, 'obj', (key, newVal, oldVal) => {\n        console.log(`key:`, key, `newVal:`, newVal, `oldVal:`, oldVal);\n    });\n\n    setInterval(() => {\n        gameInfo.obj.a += 1;\n    }, 1000);\n */\nfunction reactive(option) {\n    var target = option.target, propertyName = option.propertyName, pos = option.pos, callback = option.callback;\n    var properties = watchMap.get(target.constructor);\n    if (!properties) {\n        properties = {};\n        watchMap.set(target.constructor, properties);\n    }\n    // 回调列表\n    var key = propertyName + pos;\n    properties[key] = callback;\n}\n/**\n * 监听目标变化\n * @param id\n * @param target\n * @returns\n */\nfunction _reactive(target, watchTarget, propertykey) {\n    if (!(0, Types_1.isObject)(target))\n        return target;\n    var handler = {\n        get: function (target, key, receiver) {\n            var result = Reflect.get(target, key, receiver);\n            return (0, Types_1.isObject)(result) ? _reactive(result, watchTarget, propertykey) : result;\n        },\n        set: function (target, key, value, receiver) {\n            var oldValue = target[key];\n            var result = Reflect.set(target, key, value, receiver);\n            if (oldValue !== value) {\n                var properties_1 = watchMap.get(watchTarget.constructor);\n                if (properties_1) {\n                    Object.keys(properties_1).forEach(function (key) {\n                        if (key.includes(propertykey)) {\n                            if (properties_1[key]) {\n                                properties_1[key](key, oldValue, value);\n                            }\n                        }\n                    });\n                }\n                if ((0, Types_1.isObject)(value))\n                    _reactive(value, watchTarget, propertykey);\n            }\n            return result;\n        }\n    };\n    return new Proxy(target, handler);\n}\n/**\n * 类属性装饰器，监测属性值变化\n * @param id 唯一 id\n * @returns\n */\nfunction watch() {\n    return function (target, propertykey) {\n        var _a;\n        var proxy = _reactive((_a = {}, _a[propertykey] = target[propertykey], _a), target, propertykey);\n        Object.defineProperty(target, propertykey, {\n            get: function () {\n                return proxy[propertykey];\n            },\n            set: function (newValue) {\n                proxy[propertykey] = newValue;\n            },\n            enumerable: true,\n            configurable: true\n        });\n    };\n}\n/**\n * 监听某个目标如果发生变化，则输出代码\n * @param watchFlag 标记每个监听节点的唯一值\n * @param obj 监听对象\n * @returns\n *\n * @example\n *\n *  this.node = watchDebug('comboScoreTipNode:', this.node);\n */\nfunction watchDebug(watchFlag, obj, path, maxDepth) {\n    if (path === void 0) { path = []; }\n    if (maxDepth === void 0) { maxDepth = 2; }\n    // 只有调试状态\n    if (CC_DEBUG) {\n        if (!(0, Types_1.isObject)(obj))\n            return obj;\n        if (path.length >= maxDepth)\n            return obj; // 超过最大深度不再递归\n        var lastContent_1;\n        return new Proxy(obj, {\n            get: function (target, key, receiver) {\n                var value = Reflect.get(target, key, receiver);\n                // 只递归到 maxDepth 层\n                if ((0, Types_1.isObject)(value) && path.length + 1 < maxDepth) {\n                    return watchDebug(watchFlag, value, path.concat(String(key)), maxDepth);\n                }\n                return value;\n            },\n            set: function (target, key, value, receiver) {\n                var oldValue = target[key];\n                if (oldValue !== value) {\n                    Error.stackTraceLimit = 20;\n                    var error = new Error(\"\");\n                    // 过滤调用栈，只处理包含 Trait 或 Proxy 的行\n                    if (error.stack) {\n                        var lines = error.stack.split('\\n');\n                        var filteredLines = lines.filter(function (line) {\n                            return (line.includes('Trait') || line.includes('Proxy')) && !line.includes('cocos2d-js-for-preview.js') && !line.includes('cocos2d-js.js');\n                        });\n                        if (filteredLines.length > 0) {\n                            var content = filteredLines.join('\\n');\n                            if (content !== lastContent_1) {\n                                lastContent_1 = content;\n                                if (CC_DEBUG) {\n                                    console.log('%c[watchDebug]', 'color:#fff;background:#6a1b9a;padding:2px 6px;border-radius:4px;', \"\".concat(watchFlag, \"\\n\").concat(content));\n                                }\n                            }\n                        }\n                    }\n                }\n                // 只递归到 maxDepth 层\n                var newValue = (0, Types_1.isObject)(value) && path.length + 1 < maxDepth\n                    ? watchDebug(watchFlag, value, path.concat(String(key)), maxDepth)\n                    : value;\n                return Reflect.set(target, key, newValue, receiver);\n            }\n        });\n    }\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.cacheRender = void 0;\nvar ResLoader_1 = require(\"../loader/ResLoader\");\nvar CacheRender = /** @class */ (function () {\n    function CacheRender() {\n        this._caches = {};\n    }\n    /**\n     * 创建或更新缓存列表组件\n     * @param prefabUrl 预预制体地址\n     * @param count 缓存池长度\n     * @param compClass 缓存的组件类\n     * @param parent 父容器\n     * @returns\n     */\n    CacheRender.prototype.createOrUpdateCacheListComponents = function (option) {\n        var _this = this;\n        if (!option) {\n            return;\n        }\n        var prefabUrl = option.prefabUrl, bundleName = option.bundleName, count = option.count, typeOrClassName = option.typeOrClassName, parent = option.parent;\n        if (count <= 0) {\n            return;\n        }\n        if (!parent) {\n            return;\n        }\n        return new Promise(function (c, e) {\n            var onComplete = function (err, asset) {\n                if (err) {\n                    e(err);\n                    return;\n                }\n                var caches = _this._caches;\n                var itemCaches = caches[prefabUrl];\n                if (!itemCaches) {\n                    caches[prefabUrl] = [];\n                    itemCaches = caches[prefabUrl];\n                }\n                var cacheLen = itemCaches.length;\n                // 禁用多余的\n                if (cacheLen > count) {\n                    for (var i_1 = count; i_1 < cacheLen; i_1++) {\n                        var cacheComp = itemCaches[i_1];\n                        cacheComp.node.active = false;\n                    }\n                }\n                // 创建或者激活当前的\n                var comps = [];\n                for (var i_2 = 0; i_2 < count; i_2++) {\n                    var cacheComp = itemCaches[i_2];\n                    if (!cacheComp) {\n                        var node = cc.instantiate(asset);\n                        itemCaches[i_2] = node.getComponent(typeOrClassName);\n                        parent.addChild(node);\n                        comps[i_2] = itemCaches[i_2];\n                    }\n                    else {\n                        cacheComp.node.active = true;\n                        if (cacheComp.node.parent !== parent) {\n                            cacheComp.node.parent = parent;\n                        }\n                        comps[i_2] = cacheComp;\n                    }\n                }\n                c(comps);\n            };\n            if (bundleName) {\n                ResLoader_1.ResLoader.loadByBundle(bundleName, prefabUrl, cc.Prefab, onComplete);\n            }\n            else {\n                ResLoader_1.ResLoader.load(prefabUrl, cc.Prefab, onComplete);\n            }\n        });\n    };\n    /**\n     * 清理所有缓存组件\n     */\n    CacheRender.prototype.clearAllCaches = function () {\n        for (var prefabUrl in this._caches) {\n            var itemCaches = this._caches[prefabUrl];\n            if (itemCaches) {\n                // 销毁所有缓存的组件和节点\n                itemCaches.forEach(function (comp) {\n                    if (comp && comp.node && cc.isValid(comp.node)) {\n                        comp.node.removeFromParent();\n                        comp.node.destroy();\n                    }\n                });\n                // 清空数组\n                itemCaches.length = 0;\n            }\n        }\n        // 重置缓存对象\n        this._caches = {};\n        if (CC_DEBUG) {\n            console.log('[CacheRender] 所有缓存已清理');\n        }\n    };\n    /**\n     * 完全重置 CacheRender 状态\n     */\n    CacheRender.prototype.reset = function () {\n        this.clearAllCaches();\n        if (CC_DEBUG) {\n            console.log('[CacheRender] CacheRender 已完全重置');\n        }\n    };\n    return CacheRender;\n}());\nexports.cacheRender = new CacheRender();\n", "\"use strict\";\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.arraysHaveSameElements = arraysHaveSameElements;\nexports.shuffleArray = shuffleArray;\nexports.arraysEqual = arraysEqual;\nexports.ensureMaxLength = ensureMaxLength;\n// 定义一个函数来检查两个数组是否包含相同的元素（不考虑顺序）\nfunction arraysHaveSameElements(arr1, arr2) {\n    if (arr1.length !== arr2.length)\n        return false;\n    // 复制数组并排序\n    var sortedArr1 = arr1.slice().sort();\n    var sortedArr2 = arr2.slice().sort();\n    // 比较排序后的数组\n    for (var i_1 = 0; i_1 < sortedArr1.length; i_1++) {\n        if (sortedArr1[i_1] !== sortedArr2[i_1]) {\n            return false;\n        }\n    }\n    return true;\n}\n// 随机打乱数组\nfunction shuffleArray(array) {\n    var _a;\n    // 创建数组的副本以避免修改原始数组\n    var shuffledArray = array.slice();\n    for (var i_2 = shuffledArray.length - 1; i_2 > 0; i_2--) {\n        // 生成一个随机索引\n        var j = Math.floor(Math.random() * (i_2 + 1));\n        // 交换元素\n        _a = __read([shuffledArray[j], shuffledArray[i_2]], 2), shuffledArray[i_2] = _a[0], shuffledArray[j] = _a[1];\n    }\n    return shuffledArray;\n}\n// 检查两个数组是否相等（考虑顺序）\nfunction arraysEqual(arr1, arr2) {\n    if (arr1.length !== arr2.length) {\n        return false;\n    }\n    for (var i_3 = 0; i_3 < arr1.length; i_3++) {\n        if (arr1[i_3] !== arr2[i_3]) {\n            return false;\n        }\n    }\n    return true;\n}\n// 确保数组长度不超过maxLength，如果超过则删除第一个元素，并添加新元素到数组末尾\nfunction ensureMaxLength(arr, maxLength, newItem) {\n    if (arr.length >= maxLength) {\n        arr.shift();\n    }\n    arr.push(newItem);\n    return arr;\n}\n", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.UrlCrypto = void 0;\nvar ICrypto_1 = require(\"./ICrypto\");\nvar UrlCrypto = /** @class */ (function (_super) {\n    __extends(UrlCrypto, _super);\n    function UrlCrypto() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    /**\n     * 加密\n     * @url url\n     * @returns\n     */\n    UrlCrypto.encrypt = function (url) {\n        var base64Str = Buffer.from(url).toString('base64');\n        return this._des(base64Str);\n    };\n    /**\n     * 解密\n     * @param url\n     * @returns\n     */\n    UrlCrypto.decrypt = function (url) {\n        var decryptStr = this._des(url);\n        return Buffer.from(decryptStr, 'base64').toString('utf-8');\n    };\n    UrlCrypto._des = function (url) {\n        // 替换空格为加号\n        var paramStr = url.replace(/\\s+/g, '+');\n        var paramLength = paramStr.length;\n        var keyLength = this.encodeKey.length;\n        // 初始化一个字节数组用于存放加密后的结果\n        var encodeStr = \"\";\n        for (var i_1 = 0; i_1 < paramLength; i_1++) {\n            // 获取原始字节串中的单个字节\n            var str = paramStr[i_1];\n            // 查找字节在EncodeKey中的索引\n            var index = this.encodeKey.indexOf(str);\n            if (index !== -1) {\n                // 根据索引找到对应的加密字节\n                var t = this.encodeKey[keyLength - index - 1];\n                // 将加密字节追加到结果中\n                encodeStr += t;\n            }\n        }\n        // 返回加密后的字节序列\n        return encodeStr;\n    };\n    UrlCrypto.encodeKey = \"C1eWgtN/ZOJ=qw2TXyhxjV+0SlUL35R6ri9G4uamPfQpK78AdHbBczFnYEskMDIvo\";\n    return UrlCrypto;\n}(ICrypto_1.ICrypto));\nexports.UrlCrypto = UrlCrypto;\n", "\"use strict\";\n/**\n * 管理龙骨动画播放\n * （1）：支持播放龙骨动画，且播放完毕后是否销毁\n * （2）：支持同时播放声音，且播放完毕后是否销毁\n * （3）：支持缓存\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.dragonbonesAnim = void 0;\nvar ResLoader_1 = require(\"../loader/ResLoader\");\nif (CC_DEBUG) {\n    var originalPlayAnimation_1 = dragonBones.ArmatureDisplay.prototype.playAnimation;\n    dragonBones.ArmatureDisplay.prototype.playAnimation = function (animName, playTimes) {\n        var _a, _b, _c, _d, _e;\n        var _armatureName = this._armatureName;\n        var _dragonBonesJson = (_a = this.dragonAsset) === null || _a === void 0 ? void 0 : _a._dragonBonesJson;\n        if (_dragonBonesJson) {\n            var _dragonBonesJsonData = (_c = (_b = this.dragonAsset) === null || _b === void 0 ? void 0 : _b._dragonBonesJsonData) !== null && _c !== void 0 ? _c : JSON.parse(_dragonBonesJson);\n            var armatureInfo = (_d = _dragonBonesJsonData === null || _dragonBonesJsonData === void 0 ? void 0 : _dragonBonesJsonData.armature) === null || _d === void 0 ? void 0 : _d.find(function (v) { return v.name === _armatureName; });\n            if (armatureInfo) {\n                var animation = (_e = armatureInfo.animation) === null || _e === void 0 ? void 0 : _e.find(function (v) { return v.name === animName; });\n                if (!animation) {\n                    console.error(\"\\u9F99\\u9AA8\\u52A8\\u753B\\u64AD\\u653E\\u9519\\u8BEF\\uFF1AarmatureName\\uFF1A\".concat(this._armatureName, \" \\u4E2D\\u6CA1\\u6709 animatinName:\").concat(animName));\n                }\n            }\n            else {\n                console.error(\"\\u9F99\\u9AA8\\u52A8\\u753B\\u64AD\\u653E\\u9519\\u8BEF\\uFF1AarmatureName\\uFF1A\".concat(this._armatureName, \" \\u4E0D\\u5B58\\u5728\\uFF01\\uFF0C\\u5F53\\u524D\\u8981\\u64AD\\u653E\\u7684\\u52A8\\u753B\\u540D\\u4E3A\\uFF1A\").concat(animName));\n            }\n        }\n        originalPlayAnimation_1.apply(this, [animName, playTimes]);\n    };\n}\nvar DragonBonesAnim = /** @class */ (function () {\n    function DragonBonesAnim() {\n        this.dragonbones = {};\n    }\n    /**\n     * 播放龙骨动画\n     * @param parentNode\n     * @param armatureName\n     * @param animationName\n     * @param playTimes\n     * @param config\n     * @returns\n     */\n    DragonBonesAnim.prototype.play = function (parentNode, armatureName, animationName, playTimes, config) {\n        var _this = this;\n        if (!config) {\n            return;\n        }\n        if (!armatureName) {\n            return;\n        }\n        if (!animationName) {\n            return;\n        }\n        if (playTimes === undefined) {\n            playTimes = 1;\n        }\n        var bundleName = config.bundleName, dragonAssetUrl = config.dragonAssetUrl, dragonAtlasAssetUrl = config.dragonAtlasAssetUrl, frameSplitting = config.frameSplitting;\n        if (dragonAssetUrl === undefined || dragonAssetUrl === '') {\n            return;\n        }\n        if (dragonAtlasAssetUrl === undefined || dragonAtlasAssetUrl === '') {\n            return;\n        }\n        if (animationName === undefined || animationName === '') {\n            return;\n        }\n        if (armatureName === undefined || armatureName === '') {\n            return;\n        }\n        // const key = `${dragonAssetUrl}_${dragonAtlasAssetUrl}`;\n        // if (this.dragonbones[key]) {\n        //     this.dragonbones[key].node.opacity = 255;\n        //     this.dragonbones[key].playAnimation(animationName, playTimes);\n        //     return this.dragonbones[key].node;\n        // }\n        var node = new cc.Node();\n        var dragonBonesArmatureDisplay = node.addComponent(dragonBones.ArmatureDisplay);\n        parentNode.addChild(node);\n        // this.dragonbones[key] = dragonBonesArmatureDisplay;\n        dragonBonesArmatureDisplay.enableBatch = true;\n        dragonBonesArmatureDisplay.updateAnimationCache(animationName);\n        dragonBonesArmatureDisplay.setAnimationCacheMode(dragonBones.ArmatureDisplay.AnimationCacheMode.SHARED_CACHE);\n        if (dragonBonesArmatureDisplay) {\n            if (config.completeBack && config.completeBackObj) {\n                dragonBonesArmatureDisplay.addEventListener(dragonBones.EventObject.COMPLETE, config.completeBack, config.completeBackObj);\n            }\n            if (config.frameSplitting) {\n                setTimeout(function () {\n                    _this._play(config, armatureName, animationName, playTimes, dragonBonesArmatureDisplay);\n                }, 50);\n            }\n            else {\n                this._play(config, armatureName, animationName, playTimes, dragonBonesArmatureDisplay);\n            }\n        }\n        return node;\n    };\n    DragonBonesAnim.prototype._play = function (config, armatureName, animationName, playTimes, dragonBonesArmatureDisplay) {\n        var bundleName = config.bundleName, dragonAssetUrl = config.dragonAssetUrl, dragonAtlasAssetUrl = config.dragonAtlasAssetUrl, frameSplitting = config.frameSplitting;\n        if (bundleName) {\n            ResLoader_1.ResLoader.renderDragonbonesByBundle(bundleName, dragonBonesArmatureDisplay, dragonAssetUrl, dragonAtlasAssetUrl, armatureName, animationName, playTimes);\n        }\n        else {\n            ResLoader_1.ResLoader.renderDragonbones(dragonBonesArmatureDisplay, dragonAssetUrl, dragonAtlasAssetUrl, armatureName, animationName, playTimes);\n        }\n    };\n    return DragonBonesAnim;\n}());\nexports.dragonbonesAnim = new DragonBonesAnim();\n", "\"use strict\";\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __generator = (this && this.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\n    return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.measure = measure;\n/**\n * 时间检测\n * @param className\n * @returns\n */\nfunction measure(target, propertyKey, descriptor) {\n    var originalMethod = descriptor.value;\n    descriptor.value = function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        return __awaiter(this, void 0, void 0, function () {\n            var start, result, finish;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!CC_DEBUG) return [3 /*break*/, 2];\n                        start = performance.now();\n                        return [4 /*yield*/, originalMethod.apply(this, args)];\n                    case 1:\n                        result = _a.sent();\n                        finish = performance.now();\n                        if (CC_DEBUG) {\n                            console.log(\"\".concat(propertyKey, \" execution time: \").concat(finish - start, \" milliseconds\"));\n                        }\n                        return [2 /*return*/, result];\n                    case 2: return [2 /*return*/, originalMethod.apply(this, args)];\n                }\n            });\n        });\n    };\n    return descriptor;\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.randomInt = randomInt;\nexports.randomFloat = randomFloat;\n/**\n * 生成指定范围内的随机整数\n * @param min 最小值\n * @param max 最大值\n * @returns 随机整数\n */\nfunction randomInt(min, max) {\n    return Math.floor(Math.random() * (max - min)) + min;\n}\n/**\n * 生成指定范围内的随机数\n * @param min 最小值\n * @param max 最大值\n * @returns 随机数\n */\nfunction randomFloat(min, max) {\n    return Math.random() * (max - min) + min;\n}\n", "\"use strict\";\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.envInfo = void 0;\nvar DecoratorMemoize_1 = require(\"../../../base/decorators/DecoratorMemoize\");\n/**\n * 环境信息\n */\nvar EnvInfo = /** @class */ (function () {\n    function EnvInfo() {\n    }\n    Object.defineProperty(EnvInfo.prototype, \"isProd\", {\n        /** 是否为 Release 版 TODO 可能与主包逻辑不一致，需要细看*/\n        get: function () {\n            return (MACRO_ENV === 'prod' || MACRO_ENV === 'prod-test') && NativeBridge.isNative();\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(EnvInfo.prototype, \"bundleName\", {\n        /** 包名 */\n        get: function () {\n            return 'com.block.juggle';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(EnvInfo.prototype, \"remoteResServerUrl\", {\n        /**\n         * 远程资源服务器\n         */\n        get: function () {\n            return MACRO_REMOTE_SERVER_RES_URL;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(EnvInfo.prototype, \"hotServerUrl\", {\n        /**\n         * 热更服务器地址\n         * 注意：正式服，没有 ${MACRO_PLATFORM}/${MACRO_ENV} 前缀了\n         */\n        get: function () {\n            if (MACRO_ENV === \"prod\" || MACRO_ENV === \"prod-test\") {\n                return MACRO_REMOTE_SERVER_RES_URL + \"/hotUpdate\";\n            }\n            else {\n                return MACRO_REMOTE_SERVER_RES_URL + \"/hotUpdate/\".concat(MACRO_PLATFORM, \"/\").concat(MACRO_ENV);\n            }\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(EnvInfo.prototype, \"envs\", {\n        /**\n         * 环境信息\n         */\n        get: function () {\n            return {\n                Test: {\n                    // 测试服请求链接\n                    gameInitUrl: \"http://gametester-test.afafb.com/game_init\"\n                },\n                Prod: {\n                    // 正式服请求链接\n                    gameInitUrl: \"https://gametester.afafb.com/game_init\"\n                }\n            };\n        },\n        enumerable: false,\n        configurable: true\n    });\n    ;\n    Object.defineProperty(EnvInfo.prototype, \"curEnvInfo\", {\n        /**\n         * 当前缓存信息\n         */\n        get: function () {\n            return this.envs[this.curEnv];\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(EnvInfo.prototype, \"os\", {\n        /**\n         * 当前系统\n         */\n        get: function () {\n            var os;\n            if (CC_JSB) {\n                switch (cc.sys.os) {\n                    case cc.sys.OS_ANDROID:\n                        os = \"Android\";\n                        break;\n                    case cc.sys.OS_IOS:\n                        os = \"iOS\";\n                        break;\n                    case cc.sys.OS_WP8:\n                        os = \"WP\";\n                        break;\n                    default:\n                        os = \"Others\";\n                        break;\n                }\n            }\n            else {\n                if (navigator.userAgent.indexOf(\"Android\") > -1 ||\n                    navigator.userAgent.indexOf(\"Linux\") > -1) {\n                    os = \"Android\";\n                }\n                else if (navigator.userAgent.indexOf(\"iPhone\") > -1) {\n                    os = \"iOS\";\n                }\n                else if (navigator.userAgent.indexOf(\"Windows Phone\") > -1) {\n                    os = \"WP\";\n                }\n                else {\n                    os = \"Others\";\n                }\n            }\n            return os;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(EnvInfo.prototype, \"osVersion\", {\n        /**\n         * 系统版本\n         */\n        get: function () {\n            var _a, _b, _c, _d, _e, _f, _g, _h, _j;\n            var version = '1.0.0';\n            if (CC_JSB) {\n                version = cc.sys.osVersion;\n            }\n            else {\n                var u = navigator.userAgent;\n                var isAndroid = u.indexOf('Android') > -1 || u.indexOf('Linux') > -1; //Android\n                var isIOS = !!u.match(/\\(i[^;]+;( U;)? CPU.+Mac OS X/); //ios终端\n                if (isAndroid) {\n                    version = (_d = (_c = (_b = (_a = navigator.userAgent.split(';')) === null || _a === void 0 ? void 0 : _a[1]) === null || _b === void 0 ? void 0 : _b.match(/\\d+(\\.*\\d+)*/g)) === null || _c === void 0 ? void 0 : _c[0]) !== null && _d !== void 0 ? _d : \"1.0.0\";\n                }\n                if (isIOS) {\n                    version = (_j = (_h = ((_g = (_f = (_e = navigator.userAgent.split(';')) === null || _e === void 0 ? void 0 : _e[1]) === null || _f === void 0 ? void 0 : _f.match(/(\\d+)_(\\d+)_?(\\d+)?/)) === null || _g === void 0 ? void 0 : _g[0])) === null || _h === void 0 ? void 0 : _h.replace('_', '.')) !== null && _j !== void 0 ? _j : \"1.0.0\";\n                }\n            }\n            return version;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(EnvInfo.prototype, \"curEnv\", {\n        /**\n         * 当前环境\n         */\n        get: function () {\n            if (this.isProd) {\n                return 'Prod';\n            }\n            else {\n                return 'Test';\n            }\n        },\n        enumerable: false,\n        configurable: true\n    });\n    __decorate([\n        DecoratorMemoize_1.memoize\n    ], EnvInfo.prototype, \"envs\", null);\n    __decorate([\n        DecoratorMemoize_1.memoize\n    ], EnvInfo.prototype, \"curEnvInfo\", null);\n    __decorate([\n        DecoratorMemoize_1.memoize\n    ], EnvInfo.prototype, \"os\", null);\n    __decorate([\n        DecoratorMemoize_1.memoize\n    ], EnvInfo.prototype, \"osVersion\", null);\n    return EnvInfo;\n}());\nexports.envInfo = new EnvInfo();\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.deepCopy = deepCopy;\nexports.deepCopyArrayFrom = deepCopyArrayFrom;\nexports.deepCopySlice = deepCopySlice;\nexports.deepCopyLoop = deepCopyLoop;\nexports.deepCopyFixed = deepCopyFixed;\n// 以前gametools的拷贝方法\n/**\n * 深度拷贝二维数字数组，根据数组大小自动选择最高效的方法\n * 根据性能测试，对不同大小的数组使用不同的拷贝策略：\n * - 小型和中型数组（< 80行）：使用slice方法\n * - 大型数组（>= 80行）：使用Array.from方法\n */\nfunction deepCopy(gameData) {\n    // 根据数组大小选择最佳方法\n    if (gameData.length < 80) {\n        // 小型和中型数组使用slice方法\n        return gameData.map(function (row) { return row.slice(); });\n    }\n    else {\n        // 大型数组使用原始方法\n        return gameData.map(function (row) { return Array.from(row); });\n    }\n}\n/**\n * 使用Array.from方法的深度拷贝，在大型数组上表现最佳\n */\nfunction deepCopyArrayFrom(gameData) {\n    return gameData.map(function (row) { return Array.from(row); });\n}\n/**\n * 使用slice方法的深度拷贝，在小型和中型数组上表现最佳\n */\nfunction deepCopySlice(gameData) {\n    return gameData.map(function (row) { return row.slice(); });\n}\n/**\n * 使用循环的深度拷贝，在某些特殊场景可能有用\n */\nfunction deepCopyLoop(gameData) {\n    var result = [];\n    var len = gameData.length;\n    for (var i_1 = 0; i_1 < len; i_1++) {\n        var row = gameData[i_1];\n        var rowLen = row.length;\n        var newRow = new Array(rowLen);\n        for (var j = 0; j < rowLen; j++) {\n            newRow[j] = row[j];\n        }\n        result[i_1] = newRow;\n    }\n    return result;\n}\n/**\n * 如果知道数组大小固定，可使用定长数组深度拷贝\n */\nfunction deepCopyFixed(gameData, rowLength) {\n    var result = [];\n    var len = gameData.length;\n    for (var i_2 = 0; i_2 < len; i_2++) {\n        var srcRow = gameData[i_2];\n        var actualRowLength = rowLength || srcRow.length;\n        var newRow = new Array(actualRowLength);\n        for (var j = 0; j < actualRowLength; j++) {\n            newRow[j] = srcRow[j];\n        }\n        result[i_2] = newRow;\n    }\n    return result;\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ModuleEvent = void 0;\nvar ModuleEvent = /** @class */ (function () {\n    function ModuleEvent() {\n        this._callback = null;\n    }\n    Object.defineProperty(ModuleEvent.prototype, \"callback\", {\n        get: function () {\n            return this._callback;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * 获取类\n     * @return 当前实例的类名\n     *\n     */\n    ModuleEvent.prototype.getClass = function () {\n        return this['constructor'];\n    };\n    return ModuleEvent;\n}());\nexports.ModuleEvent = ModuleEvent;\n", "\"use strict\";\n/**\n * 游戏层级管理\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.layerManager = exports.LayerManager = exports.gameUiLayer = exports.BASE_LAYER_CONFIGS = void 0;\nexports.getGameUiLayer = getGameUiLayer;\nexports.resetGameUiLayer = resetGameUiLayer;\nexports.initNodeConfig = initNodeConfig;\nexports.addWidget = addWidget;\n/** 基础层级配置 */\nexports.BASE_LAYER_CONFIGS = [\n    { type: 'gameUI', name: 'gameUiLayer', zIndex: 2 }\n];\n// 内部变量，用于存储 gameUiLayer 实例\nvar _gameUiLayer = null;\n/**\n * 获取游戏UI层级节点，如果无效则重新创建\n * @returns 游戏UI层级节点\n */\nfunction getGameUiLayer() {\n    // 检查 _gameUiLayer 是否有效\n    if (!_gameUiLayer || !cc.isValid(_gameUiLayer)) {\n        console.log('GameLayer: gameUiLayer 无效，重新创建');\n        _gameUiLayer = _createGameUiLayer();\n    }\n    return _gameUiLayer;\n}\n/**\n * 兼容性：保持原有的 gameUiLayer 导出\n */\nexports.gameUiLayer = new Proxy({}, {\n    get: function (target, prop) {\n        var layer = getGameUiLayer();\n        if (typeof layer[prop] === 'function') {\n            return layer[prop].bind(layer);\n        }\n        return layer[prop];\n    },\n    set: function (target, prop, value) {\n        var layer = getGameUiLayer();\n        layer[prop] = value;\n        return true;\n    }\n});\n/**\n * 创建新的游戏UI层级节点\n * @returns 新的游戏UI层级节点\n */\nfunction _createGameUiLayer() {\n    var node = new cc.Node();\n    node.name = 'gameUiLayer';\n    // 如果 LayerManager 已经初始化，则添加到场景中\n    if (exports.layerManager.layerScene) {\n        var config = exports.BASE_LAYER_CONFIGS.find(function (c) { return c.type === 'gameUI'; });\n        if (config) {\n            initNodeConfig(node, config, exports.layerManager.layerScene);\n            exports.layerManager.setLayerNode('gameUI', node);\n        }\n    }\n    return node;\n}\n/**\n * 手动重置 gameUiLayer，强制在下次访问时重新创建\n */\nfunction resetGameUiLayer() {\n    if (_gameUiLayer && cc.isValid(_gameUiLayer)) {\n        _gameUiLayer.removeFromParent();\n        _gameUiLayer.destroy();\n    }\n    _gameUiLayer = null;\n    console.log('GameLayer: gameUiLayer 已重置');\n}\n/**\n * 初始化节点配置\n * @param node 目标节点\n * @param config 配置信息\n * @param scene 场景节点\n */\nfunction initNodeConfig(node, config, scene) {\n    node.name = config.name;\n    node.zIndex = config.zIndex;\n    if (config.opacity !== undefined) {\n        node.opacity = config.opacity;\n    }\n    if (config.hidden) {\n        node.active = false;\n    }\n    scene.addChild(node);\n    addWidget(node);\n}\n/**\n * 层级管理器类\n */\nvar LayerManager = /** @class */ (function () {\n    function LayerManager() {\n        this.layerMap = new Map();\n        this.scene = null;\n    }\n    /**\n     * 初始化层级管理器\n     */\n    LayerManager.prototype.init = function () {\n        var _this = this;\n        this.scene = cc.Canvas.instance.node;\n        this.clear();\n        // 初始化基础层级\n        exports.BASE_LAYER_CONFIGS.forEach(function (config) {\n            if (config.type === 'gameUI') {\n                // 强制重新创建 gameUiLayer\n                _gameUiLayer = _createGameUiLayer();\n                _this.layerMap.set(config.type, _gameUiLayer);\n            }\n        });\n    };\n    Object.defineProperty(LayerManager.prototype, \"layerScene\", {\n        get: function () {\n            if (!cc.isValid(this.scene)) {\n                this.scene = cc.Canvas.instance.node;\n            }\n            return this.scene;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * 清理所有层级\n     */\n    LayerManager.prototype.clear = function () {\n        this.layerMap.forEach(function (node) {\n            node.removeFromParent();\n            node.destroy();\n        });\n        this.layerMap.clear();\n        // 清理 gameUiLayer 引用\n        _gameUiLayer = null;\n    };\n    /**\n     * 添加新层级\n     * @param config 层级配置\n     */\n    LayerManager.prototype.addLayer = function (config) {\n        if (!this.scene) {\n            throw new Error('LayerManager not initialized');\n        }\n        // 如果已存在同类型层级，先移除\n        this.removeLayer(config.type);\n        var node = new cc.Node();\n        initNodeConfig(node, config, this.scene);\n        this.layerMap.set(config.type, node);\n        return node;\n    };\n    /**\n     * 添加预定义的层级节点\n     * @param type 层级类型\n     * @param node 预定义的节点\n     */\n    LayerManager.prototype.setLayerNode = function (type, node) {\n        if (!this.scene) {\n            throw new Error('LayerManager not initialized');\n        }\n        // 如果已存在同类型层级，先移除\n        this.removeLayer(type);\n        // 设置新节点\n        this.layerMap.set(type, node);\n    };\n    /**\n     * 移除层级\n     * @param type 层级类型\n     */\n    LayerManager.prototype.removeLayer = function (type) {\n        var layer = this.layerMap.get(type);\n        if (layer) {\n            layer.removeFromParent();\n            layer.destroy();\n            this.layerMap.delete(type);\n            return true;\n        }\n        return false;\n    };\n    /**\n     * 获取层级节点\n     * @param type 层级类型\n     */\n    LayerManager.prototype.getLayer = function (type) {\n        return this.layerMap.get(type);\n    };\n    /**\n     * 显示层级\n     * @param type 层级类型\n     */\n    LayerManager.prototype.showLayer = function (type) {\n        var layer = this.layerMap.get(type);\n        if (layer) {\n            layer.active = true;\n        }\n    };\n    /**\n     * 隐藏层级\n     * @param type 层级类型\n     */\n    LayerManager.prototype.hideLayer = function (type) {\n        var layer = this.layerMap.get(type);\n        if (layer) {\n            layer.active = false;\n        }\n    };\n    /**\n     * 设置层级透明度\n     * @param type 层级类型\n     * @param opacity 透明度(0-255)\n     */\n    LayerManager.prototype.setLayerOpacity = function (type, opacity) {\n        var layer = this.layerMap.get(type);\n        if (layer) {\n            layer.opacity = opacity;\n        }\n    };\n    /**\n     * 获取所有层级类型\n     */\n    LayerManager.prototype.getLayerTypes = function () {\n        return Array.from(this.layerMap.keys());\n    };\n    return LayerManager;\n}());\nexports.LayerManager = LayerManager;\n// 导出单例实例\nexports.layerManager = new LayerManager();\n/**\n * 添加Widget组件到节点\n * @param node 目标节点\n */\nfunction addWidget(node) {\n    node.width = 960;\n    node.height = 1707.5;\n    node.anchorX = node.anchorY = 0;\n    var widget = node.addComponent(cc.Widget);\n    widget.isAlignTop = true;\n    widget.isAlignLeft = true;\n    widget.isAlignRight = true;\n    widget.isAlignBottom = true;\n    widget.top = 0;\n    widget.bottom = 0;\n    widget.left = 0;\n    widget.right = 0;\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.audioInfo = exports.AudioType = void 0;\nvar ResLoader_1 = require(\"../loader/ResLoader\");\nvar Types_1 = require(\"../types/Types\");\nvar Storage_1 = require(\"../storage/Storage\");\n/**\n * 声音类型\n */\nvar AudioType;\n(function (AudioType) {\n    /** 声音 */\n    AudioType[AudioType[\"SOUND\"] = 0] = \"SOUND\";\n    /** 音效 */\n    AudioType[AudioType[\"EFFECT\"] = 1] = \"EFFECT\";\n})(AudioType || (exports.AudioType = AudioType = {}));\n/*\n * 普通场景，音效 管理器，（适应于 浏览器，和native ）\n */\nvar AudioInfo = /** @class */ (function () {\n    function AudioInfo() {\n    }\n    /**\n     * 播放声音或者音效\n     * @param option\n     */\n    AudioInfo.prototype.play = function (option) {\n        var _this = this;\n        if (!option) {\n            if (CC_DEBUG) {\n                console.error(\"\\u58F0\\u97F3\\u64AD\\u653E\\u65F6\\u64AD\\u653E\\u65F6\\u9009\\u9879\\u4E0D\\u80FD\\u4E3A\\u7A7A\\uFF01\");\n            }\n            return;\n        }\n        var url = option.url, volume = option.volume, type = option.type, bundleName = option.bundleName;\n        if (!url) {\n            return;\n        }\n        if ((0, Types_1.isUndefined)(type)) {\n            type = AudioType.EFFECT;\n        }\n        var audioSwitch = Storage_1.storage.getItem(\"audioSwitch\", true);\n        var bgmSwitch = Storage_1.storage.getItem(\"bgmSwitch\", false);\n        // @ts-ignore\n        if (type === AudioType.EFFECT && !audioSwitch\n            || type === AudioType.SOUND && !bgmSwitch) {\n            return;\n        }\n        if (volume === undefined) {\n            volume = 1;\n        }\n        if (!bundleName) {\n            ResLoader_1.ResLoader.load(url, cc.AudioClip, function (err, clip) {\n                _this._play(type, volume, err, clip);\n            });\n        }\n        else {\n            ResLoader_1.ResLoader.loadByBundle(bundleName, url, cc.AudioClip, function (err, clip) {\n                _this._play(type, volume, err, clip);\n            });\n        }\n    };\n    AudioInfo.prototype._play = function (type, volume, err, clip) {\n        if (err) {\n            return;\n        }\n        if (type == AudioType.EFFECT) {\n            cc.audioEngine.play(clip, false, volume);\n        }\n        else {\n            cc.audioEngine.playMusic(clip, true);\n        }\n    };\n    /**停止播放声音或者所有音效 */\n    AudioInfo.prototype.stop = function (option) {\n        var url = option.url, volume = option.volume, type = option.type;\n        if ((0, Types_1.isUndefined)(type)) {\n            type = AudioType.EFFECT;\n        }\n        var audioSwitch = Storage_1.storage.getItem(\"audioSwitch\", true);\n        var bgmSwitch = Storage_1.storage.getItem(\"bgmSwitch\", false);\n        // @ts-ignore\n        if (type === AudioType.EFFECT && !audioSwitch\n            || type === AudioType.SOUND && !bgmSwitch) {\n            return;\n        }\n        if (type == AudioType.EFFECT) {\n            cc.audioEngine.stopAllEffects();\n        }\n        else {\n            cc.audioEngine.stopMusic();\n        }\n    };\n    return AudioInfo;\n}());\nexports.audioInfo = new AudioInfo();\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.shareObjects = exports.setPerformanceTieringType = exports.onDidTraitOnActive = exports.GBMTraitsMaps = void 0;\nexports.traitsReady = traitsReady;\nexports.traitsConfigReady = traitsConfigReady;\nexports.trait = trait;\nexports.getOrCreateTraitInstance = getOrCreateTraitInstance;\nexports.getNewTraitName = getNewTraitName;\nexports.GBM = GBM;\nexports.templateTrait = templateTrait;\nvar Events_1 = require(\"../events/Events\");\nvar Trait_1 = require(\"../trait/Trait\");\nvar TraitConfigInfo_1 = require(\"../traitConfig/TraitConfigInfo\");\nrequire(\"./DecoratorClassId\");\n/** 所有特性集 */\nvar traitMaps = new Map();\n/** GBM 特性集 */\nexports.GBMTraitsMaps = new Map();\n/** 存储模板特性列表 */\nvar templateTraits = {};\nvar _onDidTraitOnActive = new Events_1.Emitter();\n/** returnState 初始化，为了避免 returnState 如果真的附值为 undefined，会导致逻辑异常 */\nvar RETURN_INIT_VALUE = \"##undefined##\";\nexports.onDidTraitOnActive = _onDidTraitOnActive.event;\nvar _performanceTieringType = 0;\nvar setPerformanceTieringType = function (type) {\n    _performanceTieringType = type;\n};\nexports.setPerformanceTieringType = setPerformanceTieringType;\nvar _traitReady = false;\n/**\n * 特性是否准备好\n * @param ready\n */\nfunction traitsReady(ready) {\n    _traitReady = ready;\n}\nvar _traitConfigReady = false;\nfunction traitsConfigReady(ready) {\n    _traitConfigReady = ready;\n}\n/**\n * 共享数据\n */\nexports.shareObjects = new Map();\n/**\n * 特性装饰器\n * 如果一个方法上有多个装饰器，装饰器的函数会从下往上执行，但是装饰器包装器（descriptor.value)的顺序是从上往下执行\n * @param traitClass 特性类\n * @param desc 特性描述\n * @returns\n */\nfunction trait($traitClassName, desc) {\n    if (desc === void 0) { desc = \"\"; }\n    return function (target, methodName, descriptor) {\n        if (!exports.shareObjects.has(target)) {\n            exports.shareObjects.set(target, {});\n        }\n        var methodShareObject = exports.shareObjects.get(target);\n        if (!methodShareObject[methodName]) {\n            methodShareObject[methodName] = {};\n        }\n        /**\n         * 存储真正装饰器的原始方法\n         */\n        if (descriptor['__originalMethod__'] === undefined) {\n            descriptor['__originalMethod__'] = descriptor.value;\n        }\n        /**\n         * 初始化共享数据状态\n         */\n        var shareTraitTarget = methodShareObject[methodName];\n        shareTraitTarget.returnState = false;\n        shareTraitTarget.returnValue = RETURN_INIT_VALUE;\n        shareTraitTarget.replace = false;\n        shareTraitTarget.methodName = methodName;\n        shareTraitTarget.method = descriptor['__originalMethod__'];\n        /**\n         * 如果一个方法上有多个装饰器，则 descriptor.value 会更新包装，并不会始终指向最原始的 method\n         * 方法最下面的装饰器 descriptor.value 首先指向原始方法\n         */\n        var decoratorMethod = descriptor.value;\n        shareTraitTarget['lastMethod'] = decoratorMethod;\n        /**\n         * 劫持装饰器绑定的方法，首先由最底下的特性装饰器劫持 target[methodName]，再将结果传递给上一层\n         * @param args\n         * @returns\n         */\n        descriptor.value = function () {\n            var _this = this;\n            var _a;\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            if (CC_DEBUG) {\n                if (!_traitReady) {\n                    console.warn(\"\\u7279\\u6027\\u811A\\u672C:\".concat($traitClassName, \" \\u672A\\u51C6\\u5907\\u597D\\uFF0C\\u53EF\\u80FD\\u4F1A\\u5BFC\\u81F4\\u7279\\u6027\\u65E0\\u6CD5\\u6B63\\u5E38\\u5DE5\\u4F5C\\uFF0C\\u8BF7\\u68C0\\u67E5\\u7279\\u6027\\u914D\\u7F6E\\u662F\\u5426\\u6B63\\u786E\\uFF0C\\u6216\\u8005\\u7279\\u6027\\u662F\\u5426\\u5DF2\\u52A0\\u8F7D\\u3002\"));\n                }\n            }\n            if (!window['__traitsClassMap__']) {\n                return decoratorMethod.apply(this, args);\n            }\n            // 只能在调用的地方获取特性类信息，因为装饰的时候数据还未拿到\n            var traitClassInfo = TraitConfigInfo_1.traitConfigInfo.traitsClassNameMap[$traitClassName];\n            /**\n             * 动态取特性实例（特性按需加载时必须通过惰性方式获取或者创建）\n             */\n            var traitInst = getOrCreateTraitInstance($traitClassName);\n            if (!traitInst) {\n                if (CC_DEBUG) {\n                    // 如果特性配置准备好，但未找到对应的特性，则表示不是当前方案下的特性。\n                    if (!_traitConfigReady) {\n                        console.warn(\"\\u7279\\u6027\\u914D\\u7F6E:\".concat($traitClassName, \" \\u672A\\u51C6\\u5907\\u597D\\uFF0C\\u53EF\\u80FD\\u4F1A\\u5BFC\\u81F4\\u7279\\u6027\\u65E0\\u6CD5\\u6B63\\u5E38\\u5DE5\\u4F5C\\uFF0C\\u8BF7\\u68C0\\u67E5\\u7279\\u6027\\u914D\\u7F6E\\u662F\\u5426\\u6B63\\u786E\\uFF0C\\u6216\\u8005\\u7279\\u6027\\u662F\\u5426\\u5DF2\\u52A0\\u8F7D\\u3002\"));\n                    }\n                    else {\n                        if (traitClassInfo) {\n                            if (_traitReady) {\n                                console.error(\"\\u7279\\u6027:\".concat($traitClassName, \" \\u672A\\u5B9E\\u4F8B\\u5316\\u6210\\u529F\\uFF0C\\u53EF\\u80FD\\u662F\\u5F53\\u524D\\u7279\\u6027\\u6240\\u6709\\u5728\\u7684 bundle \\u672A\\u4E0B\\u8F7D\\u6216\\u8005\\u7279\\u6027\\u6309\\u9700\\u52A0\\u8F7D\\u51FA\\u73B0\\u5F02\\u5E38\\uFF0C\\u8BF7\\u68C0\\u67E5\\uFF01\\uFF01\"));\n                            }\n                        }\n                    }\n                }\n                return decoratorMethod.apply(this, args);\n            }\n            if (decoratorMethod === shareTraitTarget['lastMethod']) {\n                /************************************************************\n                 *\n                 *  重置数据区\n                 * （1）：每次调用方法时，触发第一个装饰器前需要重置数据，以保持共享目标达到初始化状态\n                 *\n                 *************************************************************/\n                // 重置替换状态\n                shareTraitTarget.replace = false;\n                // 重置 return 状态\n                shareTraitTarget.returnState = false;\n                // 重置共享共数据，共享数据：复杂的耦合（使各个特性数据进行共享），彻底解决耦合问题\n                shareTraitTarget.data = {};\n                // 重置参数状态\n                shareTraitTarget.args = [];\n                // 重置 return value\n                shareTraitTarget.returnValue = RETURN_INIT_VALUE;\n            }\n            // 存储调用方法实例\n            shareTraitTarget.target = this;\n            // 为了避免因为 classId 装饰器较晚执行，放在这儿\n            if (!shareTraitTarget.className) {\n                shareTraitTarget.className = getClassName(this.constructor);\n            }\n            var props = traitClassInfo === null || traitClassInfo === void 0 ? void 0 : traitClassInfo.param;\n            if (props) {\n                var keyValue = Object.keys(props)[0];\n                // 分层渲染逻辑\n                if (!isNaN(+keyValue)) {\n                    props = props[_performanceTieringType];\n                }\n                else {\n                    props.active = true;\n                }\n                // 目前只调用一次\n                if (!traitInst['_active']) {\n                    traitInst['_id'] = traitClassInfo.id;\n                    traitInst['_props'] = props;\n                    traitInst['_active'] = true;\n                    traitInst['onEnable']();\n                }\n                /*******************************************************\n                 *\n                 *                      处理子特性\n                 *\n                 *******************************************************/\n                // TODO 后面优化性能\n                var subTraits = traitInst['_subTraits'];\n                if (subTraits) {\n                    for (var i_1 = 0; i_1 < subTraits.length; i_1++) {\n                        var subTrait = subTraits[i_1];\n                        var subTraitClassInfo = TraitConfigInfo_1.traitConfigInfo.traitsClassNameMap[subTrait.traitName];\n                        if (subTraitClassInfo) {\n                            subTrait['_props'] = subTraitClassInfo.param;\n                        }\n                    }\n                }\n            }\n            /**\n             * 计算模板特性激活状态\n             * （1）：A(模板)[开]   B[关]   C[关]   激活的特性：A\n             * （2）：A(模板)[开]   B[开]   C[关]   激活的特性：BA\n             * （3）：A(模板)[关]   B[关]   C[开]   激活的特性：CA\n             * （4）：A(模板)[关]   B[开]   C[开]   激活的特性：BCA\n             */\n            var templateTraitRefList = templateTraits[$traitClassName];\n            var templateTraitActiveState = false;\n            if (templateTraitRefList) {\n                for (var i_2 = 0; i_2 < templateTraitRefList.length; i_2++) {\n                    var templateProps = (_a = TraitConfigInfo_1.traitConfigInfo.traitsClassNameMap[templateTraitRefList[i_2]]) === null || _a === void 0 ? void 0 : _a.param;\n                    if (templateProps && Object.keys(templateProps).length !== 0) {\n                        templateTraitActiveState = true;\n                        break;\n                    }\n                }\n            }\n            /**\n             * 如果自身激活了或者模板特性激活了\n             */\n            if (traitInst.active || templateTraitActiveState) {\n                var returnState = shareTraitTarget.returnState;\n                if (!returnState) {\n                    shareTraitTarget.args = args;\n                    /**\n                     * 原始方法调用，参数由共享区数据参数维护\n                     */\n                    shareTraitTarget.originalCaller = function () {\n                        var _a;\n                        (_a = shareTraitTarget.method) === null || _a === void 0 ? void 0 : _a.apply(_this, args);\n                    };\n                    /**\n                     *  每个绑定特性的方法通过 this.shareTraitTarget 都可访问当前方法的共享区数据\n                     */\n                    this['shareTraitTarget'] = shareTraitTarget;\n                    /**\n                     * 【生命周期】监听特性被激活前\n                     */\n                    listenerTraitOnActive(traitInst, \"preActive\");\n                    /**\n                     * 【生命周期】激活特性\n                     */\n                    traitInst.onActive(shareTraitTarget);\n                    /**\n                     * DEBUG 状态下才支持特性激活事件（通常自动化需要）\n                     */\n                    if (CC_DEBUG) {\n                        _onDidTraitOnActive.fire(traitInst.id);\n                    }\n                    /**\n                     * 【生命周期】监听特性被激活后\n                     */\n                    listenerTraitOnActive(traitInst, \"actived\");\n                }\n            }\n            /**\n             * 如果当前的 decoratorMethod 为 原始装饰器方法，则如果 replace 为 true，则替换，否则不替换\n             */\n            if (decoratorMethod === shareTraitTarget.method) {\n                /*************************************************************************************************\n                 *\n                 *  最终决策：\n                 * （1）：是否执行原始方法(replace)\n                 * （2）：返回值是否为原始方法还是特性设定的（returnValue）\n                 * （3）：原始方法的执行的参数是原始方法还是特性里面修改的\n                 *\n                 *************************************************************************************************/\n                // 如果不替换原始的方法，则直接执行原始方法\n                var returnValue = void 0;\n                if (!shareTraitTarget.replace) {\n                    returnValue = decoratorMethod.apply(this, args);\n                }\n                /**\n                 * 如果设置了共享区数据返回值，则返回共享区数据返回值\n                 */\n                var shareTraitTargetReturnValue = shareTraitTarget.returnValue;\n                if (shareTraitTargetReturnValue !== RETURN_INIT_VALUE) {\n                    return shareTraitTargetReturnValue;\n                }\n                else {\n                    // 否则返回原始方法返回值\n                    return returnValue;\n                }\n            }\n            else {\n                return decoratorMethod.apply(this, args);\n            }\n        };\n    };\n}\n/**\n * 获取或者创建特性实例\n * @param $traitClassName\n * @returns 实例化的特性对象\n */\nfunction getOrCreateTraitInstance($traitClassName) {\n    var _a;\n    var traitClass = (_a = window['__traitsClassMap__']) === null || _a === void 0 ? void 0 : _a[$traitClassName];\n    if (traitClass) {\n        var traitInst = traitMaps.get(traitClass);\n        if (!traitInst) {\n            traitInst = new traitClass();\n            // 特性数据初始化到状态上\n            traitInst['_state'] = traitInst['data']();\n            // 【生命周期】特性被创建\n            traitInst.onCreate();\n            /*******************************************************\n             *\n             *                      处理子特性\n             *\n             *******************************************************/\n            var subTraits = traitInst.registerSubTraits();\n            if (subTraits) {\n                var subTraitsInst = [];\n                for (var i_3 = 0; i_3 < subTraits.length; i_3++) {\n                    var subTraitClass = subTraits[i_3];\n                    var hasSubTraitClass = traitMaps.has(subTraitClass);\n                    if (!hasSubTraitClass) {\n                        var subTraitInst = new subTraitClass();\n                        subTraitInst['_state'] = subTraitInst['data']();\n                        subTraitInst['onCreate']();\n                        subTraitsInst[i_3] = subTraitInst;\n                        traitMaps.set(subTraitClass, subTraitInst);\n                    }\n                    else {\n                        subTraitsInst[i_3] = traitMaps.get(subTraitClass);\n                    }\n                }\n                traitInst['_subTraits'] = subTraitsInst;\n            }\n            // 每一个特性只实例化一次（单例）\n            traitMaps.set(traitClass, traitInst);\n        }\n        return traitInst;\n    }\n    return null;\n}\n/**\n * 获取新的特性名\n * @param originalTraitName 配表里的特性名\n * @returns\n */\nfunction getNewTraitName(originalTraitName) {\n    if (!originalTraitName)\n        return '';\n    return originalTraitName.charAt(0).toUpperCase() + originalTraitName.slice(1) + 'Trait';\n}\n/**\n * 监听特性被激活\n * @param inst\n * @param type\n */\nfunction listenerTraitOnActive(inst, type) {\n    var listenerActives = Trait_1.Trait.activedListenerTraits;\n    var activeListeners = listenerActives.get(inst.constructor);\n    if (activeListeners) {\n        for (var i_4 = 0; i_4 < activeListeners.length; i_4++) {\n            var activeListener = activeListeners[i_4];\n            if (activeListener === null || activeListener === void 0 ? void 0 : activeListener[type]) {\n                activeListener[type](inst);\n            }\n        }\n    }\n}\n/**\n * GBM\n * @returns\n */\nfunction GBM() {\n    return function (target) {\n        var GBMTraitClass = target;\n        var traitClassName = getClassName(GBMTraitClass);\n        if (!exports.GBMTraitsMaps.has(GBMTraitClass)) {\n            exports.GBMTraitsMaps.set(target, traitClassName);\n        }\n    };\n}\n/**\n * 模板特性\n * 特性某个时候可能会坍缩成模板特性，变成模板后：\n * （1）：如果traitClassNameList中的特性有激活状态，其模板自身的激活状态将无效，该模板始终处于激活状态\n *\n * @param traitClassNameList\n */\nfunction templateTrait(traitClassNameList) {\n    return function (constructor) {\n        if (!traitClassNameList)\n            return;\n        var templateTraitName = getClassName(constructor);\n        if (templateTraitName) {\n            templateTraits[templateTraitName] = traitClassNameList;\n        }\n    };\n}\n/**\n * 获取trait实例\n * @template K 特性类名键\n * @param $traitClassName 特性类名\n * @returns 特性实例，类型由传入的特性类名决定\n */\nwindow['TRAIT'] = function ($traitClassName) {\n    return getOrCreateTraitInstance($traitClassName);\n};\n", "\"use strict\";\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.storage = void 0;\nvar Storage = /** @class */ (function () {\n    function Storage() {\n        /** 缓存数据 */\n        this.cacheData = {};\n    }\n    /**\n     * 初始化前缀,一个项目只调用一次（对于不同的项目需要不同的前缀，避免冲突，比如冒险模式应该提供新的前缀）\n     * @param prefix\n     */\n    Storage.prototype.initPrefix = function (prefix) {\n        Storage.prefix = prefix;\n    };\n    /**\n     * 存储到 localStorage\n     * @param key\n     * @param value\n     */\n    Storage.prototype.setItem = function (key, value) {\n        try {\n            var prefixedKey = Storage.prefix + key;\n            var memoryValue = void 0;\n            var valueType = typeof value;\n            // 特殊情况为 null，转化为简单数据类型\n            if (value === null || valueType === 'string' || valueType === 'number' || valueType === 'boolean' || valueType === 'bigint' || valueType === 'undefined') {\n                memoryValue = value;\n            }\n            else if (valueType === 'object') {\n                memoryValue = JSON.stringify(value);\n            }\n            else {\n                console.error(\"\\u3010\".concat(key, \"\\u3011\\u3010setItem\\u3011\\u672A\\u5B9E\\u73B0\\u7684\\u6570\\u636E\\u7C7B\\u578B\\u5B58\\u50A8\\uFF0C\\u503C\\u4E3A\\uFF1A\").concat(value));\n            }\n            /**\n             * 为了保持存储与取时数据的一致性，这里需要存储类型\n             * 这里有几个特殊情况需要处理，如果 value 是复杂数据类型，如果存之后，在取的时候，会直接取引用，而引用可能导致后面对引用的修改使 dirtyStatus 与 存储到磁盘中内容不一致\n             * 为了防止这种情况发生，我们缓存的时候，只缓存其字符串，后面取的时候再解析\n             */\n            this.cacheData[key] = { type: valueType, data: memoryValue };\n            var storageValue = valueType + Storage.valueTypeSplit + memoryValue;\n            localStorage.setItem(prefixedKey, storageValue);\n        }\n        catch (e) {\n            if (e instanceof DOMException && (e.code === 22 || e.code === 1014 || e.name === 'QuotaExceededError' || e.name === 'NS_ERROR_DOM_QUOTA_REACHED')) {\n                throw new Error('LocalStorage is full');\n            }\n            else {\n                throw e;\n            }\n        }\n    };\n    /**\n     * 获取存储的数据\n     * @param key\n     * @param defaultValue 默认值，如果存储中不存在，则使用默认值\n     * @returns\n     */\n    Storage.prototype.getItem = function (key, defaultValue) {\n        if (Object.prototype.hasOwnProperty.call(this.cacheData, key)) {\n            var _a = this.cacheData[key], type = _a.type, data = _a.data;\n            if (data === null) {\n                return data;\n            }\n            // 复杂数据类型，序列化\n            if (type === 'object') {\n                return JSON.parse(data);\n            }\n            // 简单数据类型，直接返回\n            return data;\n        }\n        else {\n            // 如果刷新了浏览器，再次读时，需要从磁盘中读写并解析，并还原真实的数据类型\n            var prefixedKey = Storage.prefix + key;\n            var item = localStorage.getItem(prefixedKey);\n            var v = void 0;\n            if (item !== null && item !== \"\") {\n                var values = item.split(Storage.valueTypeSplit);\n                if ((values === null || values === void 0 ? void 0 : values.length) === 2) {\n                    var _b = __read(values, 2), valueType = _b[0], value = _b[1];\n                    var memoryValue = void 0;\n                    switch (valueType) {\n                        case \"string\":\n                            v = value;\n                            memoryValue = v;\n                            break;\n                        case \"number\":\n                        case \"bigint\":\n                            v = +value;\n                            memoryValue = v;\n                            break;\n                        case \"boolean\":\n                            v = JSON.parse(value);\n                            memoryValue = v;\n                            break;\n                        case \"undefined\":\n                            v = undefined;\n                            memoryValue = v;\n                            break;\n                        case \"object\":\n                            v = JSON.parse(value);\n                            // 这里内存中的值存储字符串\n                            memoryValue = value;\n                            break;\n                        default:\n                            console.error(\"\\u3010\".concat(key, \"\\u3011\\u3010getItem\\u3011\\u672A\\u5B9E\\u73B0\\u7684\\u6570\\u636E\\u7C7B\\u578B\\u5B58\\u50A8\"));\n                            break;\n                    }\n                    this.cacheData[key] = { type: valueType, data: memoryValue };\n                }\n                else {\n                    throw new Error(\"\\u3010Storage-getItem\\u3011\\u5B58\\u50A8\\u957F\\u5EA6\\u5E94\\u8BE5\\u4E3A2\\uFF0C\\u5B9E\\u9645\\u4E3A\\uFF1A\".concat(values === null || values === void 0 ? void 0 : values.length, \"\\uFF01\"));\n                }\n            }\n            else {\n                v = defaultValue;\n            }\n            return v;\n        }\n    };\n    /**\n     * 移除存储的数据\n     * @param key\n     */\n    Storage.prototype.remove = function (key) {\n        var prefixedKey = Storage.prefix + key;\n        localStorage.removeItem(prefixedKey);\n    };\n    /**\n     * 清除所有存储的数据\n     */\n    Storage.prototype.clear = function () {\n        localStorage.clear();\n    };\n    Storage.prefix = \"block-blast-\";\n    Storage.valueTypeSplit = \"^_^\";\n    return Storage;\n}());\nexports.storage = new Storage();\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.decorate = decorate;\nexports.debounce = debounce;\nfunction decorate(decorator) {\n    /**\n     * @param {any} _target\n     * @param {string} key\n     * @param {any} descriptor\n     */\n    return function (_target, key, descriptor) {\n        var fnKey = null;\n        var fn = null;\n        if (typeof descriptor.value === 'function') {\n            fnKey = 'value';\n            fn = descriptor.value;\n        }\n        else if (typeof descriptor.get === 'function') {\n            fnKey = 'get';\n            fn = descriptor.get;\n        }\n        if (!fn || !fnKey) {\n            throw new Error('not supported');\n        }\n        descriptor[fnKey] = decorator(fn, key);\n    };\n}\n/**\n * 函数去抖,优化掉可能会执行非常频繁的操作，按delay时间间隔执行\n * |Click：—————————————————— delay\n   |        Click：—————————————————— delay\n   |                  Click：—————————————————— delay\n   |                              Click：—————————————————— delay | 执行动作\n * @param {number} delay 去抖动时间 单位：毫秒\n * @param {string=} str 去抖动时间期间 toast文案\n */\nfunction debounce(delay, str) {\n    if (delay === void 0) { delay = 1000; }\n    return decorate(function (fn, key) {\n        var timerKey = \"$debounce$\".concat(key);\n        var oldTimes = {};\n        oldTimes[timerKey] = -10000000;\n        return function () {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            var curTime = Date.now();\n            var $oldTime = oldTimes[timerKey];\n            if (curTime - $oldTime >= delay) {\n                oldTimes[timerKey] = curTime;\n                return fn.apply(this, args);\n            }\n            else {\n                if (typeof str === 'string' || typeof str === 'number') {\n                    console.warn(str);\n                }\n            }\n        };\n    });\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ScreenAdapter = ScreenAdapter;\nfunction ScreenAdapter() {\n    return function (target, propertyKey, descriptor) {\n        var originalMethod = descriptor.value;\n        descriptor.value = function () {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            var winsize = cc.view.getVisibleSize();\n            if (winsize.height / winsize.width >= 1334 / 750) {\n                var _canvas = cc.Canvas.instance;\n                _canvas.fitHeight = false;\n                _canvas.fitWidth = true;\n            }\n            else if (winsize.height / winsize.width < 1334 / 750) {\n                var _canvas = cc.Canvas.instance;\n                _canvas.fitHeight = true;\n                _canvas.fitWidth = false;\n            }\n            return originalMethod.apply(this, args);\n        };\n        return descriptor;\n    };\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.randomList = randomList;\nfunction randomList(list, count, repeated) {\n    if (repeated === void 0) { repeated = false; }\n    if (repeated) {\n        var result = [];\n        for (var i_1 = 0; i_1 < count; i_1++) {\n            var random = Math.floor(Math.random() * (list.length - 1));\n            result.push(list[random]);\n        }\n        return result;\n    }\n    else {\n        return shuffle(list).slice(0, count);\n    }\n}\nfunction shuffle(list, useNewList) {\n    if (useNewList === void 0) { useNewList = false; }\n    if (useNewList) {\n        list = list.concat();\n    }\n    for (var i_2 = list.length - 1; i_2 > 0; i_2--) {\n        var random = Math.floor(Math.random() * i_2);\n        var temp = list[i_2];\n        list[i_2] = list[random];\n        list[random] = temp;\n    }\n    return list;\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ModuleManager = exports.ModuleType = void 0;\n/**\n * 模块类型\n * Common 模块定义的事件：\n * （1）：如果切换到 Class 模块，只能 Class 模块收到\n * （2）：如果切换到 Chapter 模块，只能 Chapter 模块收到\n */\nvar ModuleType;\n(function (ModuleType) {\n    ModuleType[\"Common\"] = \"common\";\n    ModuleType[\"Class\"] = \"class\";\n    ModuleType[\"Chapter\"] = \"journey\";\n})(ModuleType || (exports.ModuleType = ModuleType = {}));\nvar ModuleManager = /** @class */ (function () {\n    function ModuleManager() {\n    }\n    Object.defineProperty(ModuleManager, \"moduleType\", {\n        /**\n         * 获取当前的模块类型\n         */\n        get: function () {\n            return this._moduleType;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * 设置当前运行时的模块类型\n     * @param moduleType\n     */\n    ModuleManager.setCurrentModuleType = function (moduleType) {\n        this._moduleType = moduleType;\n    };\n    Object.defineProperty(ModuleManager, \"moduleList\", {\n        /**\n         * 获取所在的模块\n         * @private\n         */\n        get: function () {\n            return this._moduleList;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * 注册模块\n     * @param $moduleClass\n     *\n     */\n    ModuleManager.resigerModule = function ($modules) {\n        this._modules = $modules;\n    };\n    /**启动各个模块*/\n    ModuleManager.startModule = function (moduleType) {\n        for (var i_1 = 0, l = this._modules.length; i_1 < l; i_1++) {\n            var $moduleClass = this._modules[i_1];\n            if (!this._moduleConstructorMaps.has($moduleClass)) {\n                this._moduleConstructorMaps.set($moduleClass, true);\n                var _instance = new $moduleClass();\n                _instance['_moduleType'] = moduleType;\n                this._moduleList[i_1] = _instance;\n                //启动所有代理\n                _instance.startProxy(moduleType);\n            }\n        }\n    };\n    /**\n     * 重置模块管理器状态\n     * 清理所有模块实例和构造器映射\n     */\n    ModuleManager.reset = function () {\n        // 清理模块实例\n        this._moduleList.forEach(function (module) {\n            try {\n                // 尝试清理模块的代理映射\n                if (module && module.proxyClassMap && typeof module.proxyClassMap.clear === 'function') {\n                    module.proxyClassMap.clear();\n                }\n            }\n            catch (error) {\n                console.error('[ModuleManager] 清理模块时发生错误:', error);\n            }\n        });\n        // 重置状态\n        this._moduleList = [];\n        this._moduleConstructorMaps.clear();\n        this._moduleType = ModuleType.Common;\n    };\n    ModuleManager._moduleList = [];\n    ModuleManager._moduleConstructorMaps = new Map();\n    ModuleManager._moduleType = ModuleType.Common;\n    return ModuleManager;\n}());\nexports.ModuleManager = ModuleManager;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.memoize = memoize;\n/**\n* 能对属性进行缓存（为了解决某些属性全局只需要调用一次）\n* @param {any} target\n* @param {string} key\n* @param {any} descriptor\n*/\nfunction memoize(target, key, descriptor) {\n    var fnKey = null;\n    var fn = null;\n    if (typeof descriptor.value === 'function') {\n        fnKey = 'value';\n        fn = descriptor.value;\n        if (fn.length !== 0) {\n            console.warn('Memoize should only be used in functions with zero parameters');\n        }\n    }\n    else if (typeof descriptor.get === 'function') {\n        fnKey = 'get';\n        fn = descriptor.get;\n    }\n    if (!fn) {\n        throw new Error('not supported');\n    }\n    var memoizeKey = \"$memoize$\".concat(key);\n    descriptor[fnKey] = function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        if (!this.hasOwnProperty(memoizeKey)) {\n            Object.defineProperty(this, memoizeKey, {\n                configurable: false,\n                enumerable: false,\n                writable: false,\n                value: fn.apply(this, args)\n            });\n        }\n        return this[memoizeKey];\n    };\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ICrypto = void 0;\nvar ICrypto = /** @class */ (function () {\n    function ICrypto() {\n    }\n    /**\n     * 加密\n     * @param content\n     */\n    ICrypto.encrypt = function (content) {\n        return content;\n    };\n    /**\n     * 解密\n     * @param content\n     */\n    ICrypto.decrypt = function (content) {\n        return content;\n    };\n    return ICrypto;\n}());\nexports.ICrypto = ICrypto;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.traitConfigActiveBlockListInfo = void 0;\n/**\n * 特性激活阻塞白名单\n * 研发过程中研发一半，有挂载不激活\n */\nvar TraitConfigActiveBlockListInfo = /** @class */ (function () {\n    function TraitConfigActiveBlockListInfo() {\n        this.blockList = [\n        // 98700001,  //高回报难题调参\n        // 99000001,  //关卡终局爽且难题\n        // 148800001  //【算法】基于空洞数变化的填空消除方案\n        ];\n    }\n    return TraitConfigActiveBlockListInfo;\n}());\nexports.traitConfigActiveBlockListInfo = new TraitConfigActiveBlockListInfo();\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.NativeBridge = exports.ANDROID_ACTIVE = void 0;\nexports.ANDROID_ACTIVE = \"org/cocos2dx/javascript/AppActivity\";\n/**\n * 与原生交互\n * 目前只处理 Android\n */\nvar NativeBridge = /** @class */ (function () {\n    function NativeBridge() {\n    }\n    /**\n     * 是否为原生平台\n     * @returns\n     */\n    NativeBridge.isNative = function () {\n        var _a;\n        return (_a = cc.sys) === null || _a === void 0 ? void 0 : _a.isNative;\n    };\n    /**\n     * 与原生交互\n     * @param methodName\n     * @param data\n     * @param methodSignature\n     * @param className TOOD: className 默认为 ANDROID_ACTIVE,等待 Native 层统一后去掉\n     * @returns\n     */\n    NativeBridge.send = function (methodName, data, methodSignature, className) {\n        if (className === void 0) { className = exports.ANDROID_ACTIVE; }\n        if (!this.isNative()) {\n            return;\n        }\n        //jsb.reflection.callStaticMethod(className, methodName, methodSignature, data == \"\" ? undefined : data);\n        if (data == \"\") {\n            return jsb.reflection.callStaticMethod(className, methodName, methodSignature);\n        }\n        else {\n            return jsb.reflection.callStaticMethod(className, methodName, methodSignature, data);\n        }\n    };\n    return NativeBridge;\n}());\nexports.NativeBridge = NativeBridge;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.isString = isString;\nexports.isStringArray = isStringArray;\nexports.isObject = isObject;\nexports.isTypedArray = isTypedArray;\nexports.isNumber = isNumber;\nexports.isIterable = isIterable;\nexports.isBoolean = isBoolean;\nexports.isUndefined = isUndefined;\nexports.isDefined = isDefined;\nexports.isUndefinedOrNull = isUndefinedOrNull;\nexports.isFunction = isFunction;\nexports.getType = getType;\n/**\n * @returns 是否为字符串.\n */\nfunction isString(str) {\n    return (typeof str === 'string');\n}\n/**\n * @returns 是否为字符串数组.\n */\nfunction isStringArray(value) {\n    return Array.isArray(value) && value.every(function (elem) { return isString(elem); });\n}\n/**\n * @returns 提供的参数是否为“object”类型，但**不是**“null”、“array”、“regexp”或“date”。\n */\nfunction isObject(obj) {\n    //该方法无法进行类型转换，因为存在类型（如字符串）\n    //是任何与函数不正匹配的put的子类。因此类型\n    //缩小范围会导致错误的结果。\n    return typeof obj === 'object'\n        && obj !== null\n        && !Array.isArray(obj)\n        && !(obj instanceof RegExp)\n        && !(obj instanceof Date);\n}\n/**\n * @returns 提供的参数是“Buffer”类型还是Uint8Array驱动类型\n */\nfunction isTypedArray(obj) {\n    var TypedArray = Object.getPrototypeOf(Uint8Array);\n    return typeof obj === 'object'\n        && obj instanceof TypedArray;\n}\n/**\n * 是否为 number 类型\n * @returns\n */\nfunction isNumber(obj) {\n    return (typeof obj === 'number' && !isNaN(obj));\n}\n/**\n * 是否为Iterable，是否转换为给定的泛型\n * @returns\n */\nfunction isIterable(obj) {\n    return !!obj && typeof obj[Symbol.iterator] === 'function';\n}\n/**\n * @returns 判断是否为 boolean 值\n */\nfunction isBoolean(obj) {\n    return (obj === true || obj === false);\n}\n/**\n * 是否为 undefined\n * @returns\n */\nfunction isUndefined(obj) {\n    return (typeof obj === 'undefined');\n}\n/**\n * 是否不为 undefined 与 null\n * @returns\n */\nfunction isDefined(arg) {\n    return !isUndefinedOrNull(arg);\n}\n/**\n * 是否为 undefined 或 null\n * @returns\n */\nfunction isUndefinedOrNull(obj) {\n    return (isUndefined(obj) || obj === null);\n}\n/**\n * 是否为函数\n * @returns\n */\nfunction isFunction(obj) {\n    return (typeof obj === 'function');\n}\n/**\n * 获取数据真实类型\n * @param {*} data\n */\nfunction getType(data) {\n    var prototype = Object.prototype.toString.call(data);\n    switch (prototype) {\n        case '[object Object]': return 'object';\n        case '[object Number]': return 'number';\n        case '[object String]': return 'string';\n        case '[object Boolean]': return 'boolean';\n        case '[object Array]': return 'array';\n        case '[object Undefined]': return 'undefined';\n        case '[object Null]': return 'null';\n        case '[object Date]': return 'date';\n        case '[object RegExp]': return 'regExp';\n        case '[object Function]': return 'function';\n        default:\n            return 'object';\n    }\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.cacheComponents = void 0;\nvar ModuleManager_1 = require(\"../../falcon/ModuleManager\");\n/**\n * 高性能组件快速获取器\n */\nvar CacheComponents = /** @class */ (function () {\n    function CacheComponents() {\n        CacheComponents._cacheComponents = this;\n    }\n    /**\n     * 组件 Enable 劫持，组件激活时存储\n     * @param callback\n     */\n    CacheComponents.prototype.componentEnabledIntercept = function () {\n        if (!CacheComponents._componentOnEnabledIntercept) {\n            CacheComponents._componentOnEnabledIntercept = true;\n            // 保存原始方法的引用\n            CacheComponents._originalCompSchedulerOnEnabled = cc.director['_compScheduler']._onEnabled;\n            cc.director['_compScheduler']._onEnabled = function (comp) {\n                var moduleType = ModuleManager_1.ModuleManager.moduleType;\n                var compConstructor = comp.constructor;\n                var componnets = CacheComponents._cacheAllComponents.get(compConstructor);\n                if (!componnets) {\n                    componnets = {};\n                    CacheComponents._cacheAllComponents.set(compConstructor, componnets);\n                }\n                componnets[moduleType] = comp;\n                CacheComponents._originalCompSchedulerOnEnabled.apply(this, [comp]);\n                var allResolves = CacheComponents._resolves.get(compConstructor);\n                if (allResolves) {\n                    var resolves = allResolves[moduleType];\n                    if (resolves) {\n                        for (var i_1 = 0; i_1 < resolves.length; i_1++) {\n                            var resolve = resolves[i_1];\n                            resolve(comp);\n                        }\n                        resolves.length = 0;\n                    }\n                }\n                // 如果组件节点未激活，则激活它\n                if (comp.node && !comp.node.active) {\n                    comp.node.active = true;\n                }\n            };\n        }\n    };\n    /**\n     * 快速同步获取实例\n     * @param CompClass\n     * @returns\n     */\n    CacheComponents.prototype.Cinst = function (CompClass) {\n        var components = CacheComponents._cacheAllComponents.get(CompClass);\n        var moduleType = ModuleManager_1.ModuleManager.moduleType;\n        if (components) {\n            return components[moduleType] || components[ModuleManager_1.ModuleType.Common];\n        }\n        return undefined;\n    };\n    /**\n     * 快速异步获取实例\n     * @param CompClass\n     * @returns\n     */\n    CacheComponents.prototype.CinstAsync = function (CompClass) {\n        return new Promise(function (resolve, e) {\n            var comp = CacheComponents._cacheComponents.Cinst(CompClass);\n            if (comp) {\n                resolve(comp);\n                return;\n            }\n            else {\n                var allResolves = CacheComponents._resolves.get(CompClass);\n                if (!allResolves) {\n                    allResolves = {};\n                    CacheComponents._resolves.set(CompClass, allResolves);\n                }\n                var moduleType = ModuleManager_1.ModuleManager.moduleType;\n                var resolves = allResolves[moduleType];\n                if (!resolves) {\n                    resolves = [];\n                    allResolves[moduleType] = resolves;\n                    resolves.push(resolve);\n                }\n                CacheComponents._cacheComponents.componentEnabledIntercept();\n            }\n        });\n    };\n    /**\n     * 清理所有组件缓存\n     */\n    CacheComponents.prototype.clearAllCaches = function () {\n        // 清理所有未完成的Promise\n        CacheComponents._resolves.forEach(function (allResolves, compClass) {\n            for (var moduleType in allResolves) {\n                var resolves = allResolves[moduleType];\n                resolves.forEach(function (resolve) {\n                    try {\n                        // 这里选择什么都不做，让Promise保持pending状态\n                        // 或者可以选择resolve(null)或reject\n                    }\n                    catch (error) {\n                        console.warn('[CacheComponents] 清理Promise时发生错误:', error);\n                    }\n                });\n                resolves.length = 0;\n            }\n        });\n        // 清空所有缓存\n        CacheComponents._cacheAllComponents.clear();\n        CacheComponents._resolves.clear();\n        if (CC_DEBUG) {\n            console.log('[CacheComponents] 所有组件缓存已清理');\n        }\n    };\n    /**\n     * 恢复组件调度器的原始方法\n     */\n    CacheComponents.prototype.restoreComponentScheduler = function () {\n        if (CacheComponents._componentOnEnabledIntercept && CacheComponents._originalCompSchedulerOnEnabled) {\n            cc.director['_compScheduler']._onEnabled = CacheComponents._originalCompSchedulerOnEnabled;\n            CacheComponents._componentOnEnabledIntercept = false;\n            if (CC_DEBUG) {\n                console.log('[CacheComponents] 组件调度器劫持已恢复');\n            }\n        }\n    };\n    /**\n     * 完全重置 CacheComponents 状态\n     */\n    CacheComponents.prototype.reset = function () {\n        this.clearAllCaches();\n        this.restoreComponentScheduler();\n        if (CC_DEBUG) {\n            console.log('[CacheComponents] CacheComponents 已完全重置');\n        }\n    };\n    CacheComponents._cacheAllComponents = new Map();\n    CacheComponents._resolves = new Map();\n    CacheComponents._originalCompSchedulerOnEnabled = null;\n    return CacheComponents;\n}());\nexports.cacheComponents = new CacheComponents();\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.prefabInfo = void 0;\nvar PrefabInfo = /** @class */ (function () {\n    function PrefabInfo() {\n        this._prefabs = new Map();\n    }\n    /**\n     * 获取或者创建预制体实例\n     * @param prefab\n     * @returns\n     */\n    PrefabInfo.prototype.getOrCreatePrefabInstantiate = function (prefab) {\n        var inst = this._prefabs.get(prefab);\n        if (!inst) {\n            inst = cc.instantiate(prefab);\n            this._prefabs.set(prefab, inst);\n        }\n        return inst;\n    };\n    /**\n     * 清理所有缓存\n     */\n    PrefabInfo.prototype.clearAllCache = function () {\n        this._prefabs.forEach(function (inst, prefab) {\n            if (cc.isValid(inst)) {\n                inst.destroy();\n            }\n        });\n        this._prefabs.clear();\n    };\n    return PrefabInfo;\n}());\nexports.prefabInfo = new PrefabInfo();\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.DeferredPromise = void 0;\n/**\n * 延时 Promise 的实现类，它的主要作用是允许在外部手动控制 Promise 的解决时机\n * 1.支持多个观察者等待同一个事件\n * 2.可以手动重置状态\n * 3.提供状态查询功能（isResolved）\n * 4.适用于需要外部控制 Promise 解决时机的场景\n * 5.暂时只支持 resolve，不支持 reject\n * 6.如果能确保顺序，不用每次都 reset @see ChapterThroughCupBone.onAnimationComplete\n * 7.如果不能确保顺序，需要每轮需要提前调用 reset @see ClassDataStatisticsInfo.deferredClearAnimPromise\n */\nvar DeferredPromise = /** @class */ (function () {\n    function DeferredPromise() {\n        this._resolved = false;\n        this._promiseQueue = [];\n    }\n    Object.defineProperty(DeferredPromise.prototype, \"isResoved\", {\n        /**\n         * 是否执行了 resolved\n         */\n        get: function () {\n            return this._resolved;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * 等待下一次 resolve。\n     * @returns 返回一个 Promise，当 resolve 被调用时，该 Promise 会被解决。\n     */\n    DeferredPromise.prototype.wait = function () {\n        var _this = this;\n        return new Promise(function (resolve) {\n            _this._promiseQueue.push({ resolve: resolve });\n        });\n    };\n    /**\n     * 重置状态\n     */\n    DeferredPromise.prototype.reset = function () {\n        this._resolved = false;\n        this._promiseQueue.length = 0;\n    };\n    /**\n     * 触发等待中的 Promise 解决。\n     * @param value - 解决的值。\n     */\n    DeferredPromise.prototype.resolve = function (value) {\n        while (this._promiseQueue.length > 0) {\n            var resolve = this._promiseQueue.shift().resolve;\n            resolve(value);\n        }\n        this._resolved = true;\n    };\n    return DeferredPromise;\n}());\nexports.DeferredPromise = DeferredPromise;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getKeyByValue = getKeyByValue;\nexports.isValueInEnum = isValueInEnum;\n// 查找enum的key值\nfunction getKeyByValue(enumObj, value) {\n    for (var key in enumObj) {\n        if (enumObj[key] === value) {\n            return key;\n        }\n    }\n    return undefined;\n}\n// enum 是否存在某\nfunction isValueInEnum(value, enumObj) {\n    return Object.values(enumObj).includes(value);\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.classId = classId;\nexports.getClassName = getClassName;\n/**\n * 绑定真实类名\n * @param className\n * @returns\n */\nfunction classId(className) {\n    return function (target) {\n        if (!target.prototype['__classname__'])\n            target.prototype['__classname__'] = className;\n    };\n}\n/**\n * 获取类名\n * @param objOrCtor\n */\nfunction getClassName(objOrCtor) {\n    return cc.js.getClassName(objOrCtor);\n}\nwindow['classId'] = classId;\nwindow['getClassName'] = getClassName;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.executeRenderingOptimize = executeRenderingOptimize;\n/**\n * 此类主要目的是执行渲染优化，结合全屏装饰器标记停止被遮挡结点的渲染\n * 以达到在某些特殊情况下降低drawcall,并降低耗电量等\n */\nfunction executeRenderingOptimize() {\n    if (!CC_EDITOR) {\n        Object.defineProperty(cc.Node.prototype, 'active', {\n            get: function () {\n                return this._active;\n            },\n            set: function (value) {\n                value = !!value;\n                //执行绑定FullScreen装饰器的组件的处理\n                executeFullScreenComponent(this, value);\n                if (this._active !== value) {\n                    this._active = value;\n                    var parent_1 = this._parent;\n                    if (parent_1) {\n                        var couldActiveInScene = parent_1._activeInHierarchy;\n                        if (couldActiveInScene) {\n                            cc.director['_nodeActivator'].activateNode(this, value);\n                        }\n                    }\n                }\n            }\n        });\n    }\n}\nfunction executeFullScreenComponent(self, value) {\n    //如果当前激活了，则其它层设置为不激活\n    var hasFullScreenComponent = self['_components'] && self['_components'].find(function (v) { return v.__fullscreen__; });\n    self.opacity = 255;\n    //全屏面板下处理其它层次面板激活\n    if (hasFullScreenComponent) {\n        if (value) {\n            hasFullScreenComponent['__inactives__'] = [];\n            if (self.parent) {\n                var hide_1 = function (target) {\n                    if (target.parent instanceof cc.Scene)\n                        return;\n                    if (target.parent) {\n                        if (target.parent.children) {\n                            var index = target.parent.children.indexOf(target);\n                            //隐藏层级低的\n                            var lowLevelChildren = target.parent.children.slice(0, index);\n                            lowLevelChildren.forEach(function (v) {\n                                if (v.opacity > 0 && v.active) {\n                                    hasFullScreenComponent['__inactives__'].push(v);\n                                }\n                                v.opacity = 0;\n                            });\n                        }\n                        //再查找父容器\n                        hide_1(target.parent);\n                    }\n                };\n                hide_1(self);\n            }\n        }\n        else {\n            //还原其它面板激活\n            var __inactives__ = hasFullScreenComponent['__inactives__'];\n            if (__inactives__) {\n                for (var i_1 = 0; i_1 < __inactives__.length; i_1++) {\n                    __inactives__[i_1].opacity = 255;\n                }\n                hasFullScreenComponent['__inactives__'].length = 0;\n            }\n        }\n    }\n    return hasFullScreenComponent;\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.applyAdapterFringe = applyAdapterFringe;\nvar IPHONE_X_HEIGHT_WIDTH_RATIO = 1280 / 716;\nvar IPHONE_X_TOP_ADJUSTMENT = 0.037;\n/**\n * 适配刘海\n * @param node\n */\nfunction applyAdapterFringe(node) {\n    if (!node) {\n        return;\n    }\n    var widget = node.getComponent(cc.Widget);\n    if (!widget) {\n        return;\n    }\n    var size = cc.view.getFrameSize();\n    var size2 = cc.view.getVisibleSize();\n    function isFringe(size, size2) {\n        if (size.width / size.height > (750 + 10) / 1334) {\n            return false;\n        }\n        if (cc.sys.os === cc.sys.OS_ANDROID || cc.sys.isBrowser) {\n            return (size.height / size.width > IPHONE_X_HEIGHT_WIDTH_RATIO) ||\n                (size2.height / size2.width > IPHONE_X_HEIGHT_WIDTH_RATIO);\n        }\n        return false;\n    }\n    if (isFringe(size, size2)) {\n        var height = Math.max(size.height, size2.height);\n        widget.top += IPHONE_X_TOP_ADJUSTMENT * height;\n    }\n}\n", "\"use strict\";\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.equal = equal;\n/**\n * 比较两个对象或者数组值是否相等\n * @param obj1\n * @param obj2\n * @returns\n */\nfunction equal(obj1, obj2, visited) {\n    var e_1, _a, e_2, _b;\n    if (visited === void 0) { visited = []; }\n    // 基本类型直接比较\n    if (obj1 === obj2)\n        return true;\n    // 检查是否为对象且不为 null\n    if (typeof obj1 !== 'object' || obj1 === null ||\n        typeof obj2 !== 'object' || obj2 === null) {\n        return false;\n    }\n    try {\n        // 处理循环引用：检查是否已比较过当前对象对\n        for (var visited_1 = __values(visited), visited_1_1 = visited_1.next(); !visited_1_1.done; visited_1_1 = visited_1.next()) {\n            var entry = visited_1_1.value;\n            if (entry[0] === obj1 && entry[1] === obj2)\n                return true;\n        }\n    }\n    catch (e_1_1) { e_1 = { error: e_1_1 }; }\n    finally {\n        try {\n            if (visited_1_1 && !visited_1_1.done && (_a = visited_1.return)) _a.call(visited_1);\n        }\n        finally { if (e_1) throw e_1.error; }\n    }\n    visited.push([obj1, obj2]);\n    // 比较构造函数\n    if (obj1.constructor !== obj2.constructor)\n        return false;\n    // 处理包装对象如 Object(123)\n    if (obj1.constructor === Number || obj1.constructor === String || obj1.constructor === Boolean) {\n        return obj1.valueOf() === obj2.valueOf();\n    }\n    // 处理数组\n    if (Array.isArray(obj1) && Array.isArray(obj2)) {\n        if (obj1.length !== obj2.length)\n            return false;\n        for (var i_1 = 0; i_1 < obj1.length; i_1++) {\n            if (!equal(obj1[i_1], obj2[i_1], visited))\n                return false;\n        }\n        return true;\n    }\n    // 处理普通对象\n    var keysA = Object.keys(obj1);\n    var keysB = Object.keys(obj2);\n    if (keysA.length !== keysB.length)\n        return false;\n    try {\n        for (var keysA_1 = __values(keysA), keysA_1_1 = keysA_1.next(); !keysA_1_1.done; keysA_1_1 = keysA_1.next()) {\n            var key = keysA_1_1.value;\n            if (!obj2.hasOwnProperty(key))\n                return false;\n            if (!equal(obj1[key], obj2[key], visited))\n                return false;\n        }\n    }\n    catch (e_2_1) { e_2 = { error: e_2_1 }; }\n    finally {\n        try {\n            if (keysA_1_1 && !keysA_1_1.done && (_b = keysA_1.return)) _b.call(keysA_1);\n        }\n        finally { if (e_2) throw e_2.error; }\n    }\n    return true;\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.traitConfigInfo = void 0;\nvar TraitConfigActiveBlockListInfo_1 = require(\"../../modules/traitConfig/vo/TraitConfigActiveBlockListInfo\");\nvar Storage_1 = require(\"../storage/Storage\");\n/**\n * P5\n *  新增：用户玩的天数小于等于 14 天\n *  活跃：用户玩的天数大于 14 天\n * P95\n *  新增：完全由服务器下发来定（天数不一定是14天，具体天数由策划配置）\n *  活跃：完全由服务器下发来定（天数不一定是14天，具体天数由策划配置）\n *  新增还是活跃由服务器控制（不一定是14天）\n *    活跃的前提条件必须拿到服务器下发数据，如果一直未拿到，则一直处于新增状态\n *    14天只是为了打点\n *\n * 扩量（目前是前端控制，要不要走服务器下发逻辑）\n * 安装时间：由原生给GameData.deviceInfo.install_time，cocos 这边记录的是第一次启动时间\n *\n * 流程\n * 1、进游戏后先请求服务器下发\n * 2、再请求热更\n * 3、如果热更新完毕之前服务下发消息收到了，则将服务器下发数据保存到本地，后面使用这个数据\n * 4、如果热更新完毕之后服务器下发消息没收到，则再等1秒，\n *  （1）：如果1秒内收到了服务器下发数据，则将其数据保存到本地，后面使用这个数据\n *  （2）：如果1秒内未收到服务器下发数据，则读取兜底配置 Config.ts 文件中的\n *\n * InterF：原先定义为 免插屏\n * Adf：原先定义为 免插屏与banner\n * 上面两个目前用户都不免了，只是原生标签未变，含义实际上变了\n *\n * 单向门\n *  指用户进入某个桶之后，后面就不会参与活跃了，游戏中没有单向门逻辑，只是策划定义的一个概念，游戏中没有逻辑\n *\n * 规范\n *  2.0：\n *  （1）：P5：纯净桶\n *  （2）：P95：非纯净桶\n *  3.0：\n *  （1）：interF：非纯净桶\n *  （2）：AdF：非纯净桶\n *\n * 桶概念\n *  指服务器为了不同流量区分的概念，本质上来讲，客户端不用关心什么桶，因为不有客户端逻辑，只是策划为了更清晰每个桶主要的流量方向标识符而已\n * （1）：流量桶\n * （2）：L5 元素桶\n * （3）：DUO 多邻国桶\n * （4）：TIANKONG 填空桶\n * （5）：广告桶\n *\n * 特性配置信息（Config.ts）\n * （1）：纯净桶（P5)\n *      新增：P5Config\n *      活跃：P5ActiveConfig\n * （2）：非纯净桶（P95)\n *      服务器配置：\n *          正常情况下先读服务器配置，服务器配置未读到时才走兜底配置\n *      兜底配置\n *          新增：Config\n *          活跃：ActiveConfig（暂时没用）\n *\n *\n *\n */\nvar TraitConfigInfo = /** @class */ (function () {\n    function TraitConfigInfo() {\n        /*************************************************************\n         *\n         *                          全特性\n         *\n         *************************************************************/\n        /**\n         * 基于特性名字存储特性信息\n         */\n        this._traits = {};\n        /**\n         * 基于特性 id 存储特性数据\n         */\n        this._traitsById = {};\n        /**********************************************************\n         *\n         *                    Config 配置\n         *\n         *********************************************************/\n        /**\n         * 当前用户特性\n         */\n        this._useTraits = {};\n        /**\n         * 缓存使用的特性名\n         */\n        this._cacheUsedTraits = {};\n        /**\n         * 基于特性类名存储特性数据\n         */\n        this._traitsClassNameMap = {};\n        /**********************************************************\n        *\n        *                    ActiveConfig 配置\n        *\n        *********************************************************/\n        /**\n         * 当前活跃用户特性\n         */\n        this._useActiveUserTraits = {};\n        this._cacheUsedActiveUserTraits = {};\n        this._configReady = false;\n        this._ready = false;\n    }\n    Object.defineProperty(TraitConfigInfo.prototype, \"traitsClassNameMap\", {\n        /**\n         * 特性类名数据映射\n         */\n        get: function () {\n            return this._traitsClassNameMap;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(TraitConfigInfo.prototype, \"configReady\", {\n        /**\n         * 配置准备好\n         */\n        get: function () {\n            return this._configReady;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(TraitConfigInfo.prototype, \"ready\", {\n        /**\n         * 特性是否准备好\n         */\n        get: function () {\n            return this._ready;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    TraitConfigInfo.prototype.initialize = function (cfg) {\n        if (!cfg) {\n            console.error(\"\\u7279\\u6027\\u521D\\u59CB\\u5316\\u914D\\u7F6E\\u53D1\\u751F\\u9519\\u8BEF\");\n            return;\n        }\n        var feature = cfg.feature;\n        var _traits = this._traits;\n        var _traitsById = this._traitsById;\n        /*************************************************************\n         *\n         *                      初始化全部特性配置\n         *\n         *************************************************************/\n        for (var traitName in feature) {\n            var traitItemInfoList = feature[traitName];\n            for (var i_1 = 0; i_1 < traitItemInfoList.length; i_1++) {\n                var traitItemInfo = traitItemInfoList[i_1];\n                var id = traitItemInfo.id, param = traitItemInfo.param;\n                if (!_traits[traitName]) {\n                    _traits[traitName] = {};\n                }\n                _traits[traitName][id] = param;\n                _traitsById[id] = { traitName: traitName, param: param };\n            }\n        }\n    };\n    // 生成新增的Traits\n    TraitConfigInfo.prototype.createUseTraits = function (config) {\n        /**********************************************************\n           *\n           *                    Config 解析的出来的配置\n           *\n           *********************************************************/\n        this._curActiveConfig = config;\n        var _a = this._curActiveConfig, features = _a.features, plan = _a.plan;\n        /**\n         * 【融合包模式下】主包传过来的下发方案\n         */\n        var stableTranslateGameWayNum = localStorage.getItem(\"blockblast.gameWayNum\");\n        var gameWayNum = stableTranslateGameWayNum ? parseInt(stableTranslateGameWayNum) : plan;\n        Storage_1.storage.setItem(\"gameWayNum\", gameWayNum);\n        for (var i_2 = 0; i_2 < features.length; i_2++) {\n            var id = features[i_2].id;\n            this._useTraits[id] = features[i_2];\n        }\n    };\n    // 生成活跃的Traits\n    TraitConfigInfo.prototype.createActiveTraits = function (config) {\n        /**********************************************************\n         *\n         *                    ActiveConfig 解析的出来的配置\n         *\n         *********************************************************/\n        var ActiveFeatures = config.features;\n        for (var i_3 = 0; i_3 < ActiveFeatures.length; i_3++) {\n            var id = ActiveFeatures[i_3].id;\n            this._useActiveUserTraits[id] = ActiveFeatures[i_3];\n        }\n    };\n    /**\n     * 创建激活特性类名映射\n     *\n     * 特性名  ---  特性类名\n     *  xxx        XxxTrait\n     * 首字母大写，然后加上 Trait 结尾\n     *\n     *\n     * 这里需要等待以下条件才能执行：\n     * （1）：等待服务器下发特性，如果 2 秒内收到服务器下发特性，则使用服务器下发特性\n     * （2）：如果 2 秒内没有收到，则使用默认的特性\n     */\n    TraitConfigInfo.prototype.createActiveTraitClassNameMaps = function () {\n        var _traitsById = this._traitsById;\n        var features = this._curActiveConfig.features;\n        var blockFeatures = features.filter(function (item) { return !TraitConfigActiveBlockListInfo_1.traitConfigActiveBlockListInfo.blockList.some(function (id) { return id === item.id; }); });\n        if (CC_DEBUG) {\n            console.log('%c[激活的特性]', \"color:#fff;background:#ff019a;\", blockFeatures);\n            Storage_1.storage.setItem('activeFeatures', blockFeatures);\n        }\n        for (var i_4 = 0; i_4 < blockFeatures.length; i_4++) {\n            var id = blockFeatures[i_4].id;\n            var traitIdInfo = _traitsById[id];\n            if (!traitIdInfo) {\n                if (CC_DEBUG) {\n                    console.error(\"\\u7279\\u6027\\u603B\\u8868 cfg.ts \\u914D\\u7F6E\\u6CA1\\u6709\\u7279\\u6027\\uFF1A\".concat(id));\n                }\n                continue;\n            }\n            var traitName = traitIdInfo.traitName, param = traitIdInfo.param;\n            var traitClassName = traitName.charAt(0).toUpperCase() + traitName.slice(1) + 'Trait';\n            this._traitsClassNameMap[traitClassName] = { id: id, param: param };\n        }\n    };\n    // 创建白名单特性剔出\n    TraitConfigInfo.prototype.createWhiteTraitClassNameMaps = function (whiteList) {\n        var _traitsById = this._traitsById;\n        // 重构包单独加的3个特性\n        for (var i_5 = 0; i_5 < whiteList.length; i_5++) {\n            var id = whiteList[i_5];\n            var traitIdInfo = _traitsById[id];\n            if (!traitIdInfo) {\n                if (CC_DEBUG) {\n                    console.error(\"\\u7279\\u6027\\u603B\\u8868 cfg.ts \\u914D\\u7F6E\\u6CA1\\u6709\\u7279\\u6027\\uFF1A\".concat(id));\n                }\n                continue;\n            }\n            var traitName = traitIdInfo.traitName, param = traitIdInfo.param;\n            var traitClassName = traitName.charAt(0).toUpperCase() + traitName.slice(1) + 'Trait';\n            this._traitsClassNameMap[traitClassName] = { id: id, param: param };\n        }\n        if (CC_DEBUG) {\n            console.log('%c[激活的特性类名]', \"color:#fff;background:#ff019a;\", this._traitsClassNameMap);\n        }\n    };\n    // 添加自定义的特性\n    TraitConfigInfo.prototype.createExtraTraitClassNameMaps = function (extraList) {\n        var _traitsById = this._traitsById;\n        // 重构包自定义的特性\n        for (var i_6 = 0; i_6 < extraList.length; i_6++) {\n            var _a = extraList[i_6], id = _a.id, name_1 = _a.name, param = _a.param;\n            this._traitsClassNameMap[name_1] = { id: id, param: param };\n        }\n    };\n    // 加载动态特性\n    TraitConfigInfo.prototype.loadDynamicTrait = function (dynamicTraitList) {\n        var _traitsById = this._traitsById;\n        for (var i_7 = 0; i_7 < dynamicTraitList.length; i_7++) {\n            var id = dynamicTraitList[i_7];\n            var traitIdInfo = _traitsById[id];\n            if (!traitIdInfo) {\n                if (CC_DEBUG) {\n                    console.error(\"\\u52A8\\u6001\\u65B0\\u52A0\\u7279\\u6027 \\u7279\\u6027\\u603B\\u8868 cfg.ts \\u914D\\u7F6E\\u6CA1\\u6709\\u7279\\u6027\\uFF1A\".concat(id));\n                }\n                continue;\n            }\n            var traitName = traitIdInfo.traitName, param = traitIdInfo.param;\n            var traitClassName = traitName.charAt(0).toUpperCase() + traitName.slice(1) + 'Trait';\n            this._traitsClassNameMap[traitClassName] = { id: id, param: param };\n            console.log(\"\\u52A8\\u6001\\u65B0\\u52A0\\u7279\\u6027\\uFF1A\".concat(id));\n        }\n    };\n    /**\n     * 获取特性数据\n     * @param traitName 特性键值\n     * @param id 如果值为空，则直接从 Config.ts->Config 里查找\n     * @returns\n     */\n    TraitConfigInfo.prototype.traitData = function (traitName, id) {\n        if (isNaN(id)) {\n            if (this._cacheUsedTraits[traitName]) {\n                return this._cacheUsedTraits[traitName];\n            }\n            var featureObj = this._traits[traitName];\n            for (var uid in featureObj) {\n                if (this._useTraits[uid]) {\n                    this._cacheUsedTraits[traitName] = featureObj[uid];\n                    return this._cacheUsedTraits[traitName];\n                }\n            }\n        }\n        return this._traits[traitName][id];\n    };\n    /**\n     * 获取活跃用户特性数据\n     * @param key 特性键值\n     * @param id 如果值为空，则直接从 Config.ts->ActiveConfig 里查找\n     * @returns\n     */\n    TraitConfigInfo.prototype.traitActiveUserData = function (key, id) {\n        if (isNaN(id)) {\n            if (this._cacheUsedActiveUserTraits[key]) {\n                return this._cacheUsedActiveUserTraits[key];\n            }\n            var featureObj = this._traits[key];\n            for (var uid in featureObj) {\n                if (this._useActiveUserTraits[uid]) {\n                    this._cacheUsedActiveUserTraits[key] = featureObj[uid];\n                    return this._cacheUsedActiveUserTraits[key];\n                }\n            }\n        }\n        return this._traits[key][id];\n    };\n    return TraitConfigInfo;\n}());\nexports.traitConfigInfo = new TraitConfigInfo();\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.adapterFringe = adapterFringe;\nvar AdapterFringe_1 = require(\"../adapter/AdapterFringe\");\n/**\n * 适配刘海\n * @param className\n * @returns\n */\nfunction adapterFringe() {\n    var nodeNames = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        nodeNames[_i] = arguments[_i];\n    }\n    return function (target, propertyKey, descriptor) {\n        var originalMethod = descriptor.value;\n        descriptor.value = function () {\n            var _this = this;\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            nodeNames.forEach(function (nodeName) {\n                var node = _this[nodeName];\n                if (node) {\n                    (0, AdapterFringe_1.applyAdapterFringe)(node);\n                }\n            });\n            return originalMethod.apply(this, args);\n        };\n        return descriptor;\n    };\n}\n", "\"use strict\";\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.first = first;\nexports.firstParallel = firstParallel;\n/**\n * 按顺序执行一个数组中的多个异步任务，直到找到一个满足特定条件的任务结果为止。如果所有任务都不满足条件，则返回一个默认值\n * @param promiseFactories\n * @param shouldStop\n * @param defaultValue\n * @returns\n */\nfunction first(promiseFactories, shouldStop, defaultValue) {\n    if (shouldStop === void 0) { shouldStop = function (t) { return !!t; }; }\n    if (defaultValue === void 0) { defaultValue = null; }\n    var index = 0;\n    var len = promiseFactories.length;\n    var loop = function () {\n        if (index >= len) {\n            return Promise.resolve(defaultValue);\n        }\n        var factory = promiseFactories[index++];\n        var promise = Promise.resolve(factory());\n        return promise.then(function (result) {\n            if (shouldStop(result)) {\n                return Promise.resolve(result);\n            }\n            return loop();\n        });\n    };\n    return loop();\n}\n/**\n * 用于并行处理多个异步任务的工具函数。它的目标是从一组并行运行的 Promise 中找到第一个满足特定条件的结果。如果没有任何一个 Promise 满足条件，则返回一个默认值。这个函数提供了两种重载形式，以支持不同的使用场景\n * @param promiseList\n * @param shouldStop\n * @param defaultValue\n * @returns\n */\nfunction firstParallel(promiseList, shouldStop, defaultValue) {\n    if (shouldStop === void 0) { shouldStop = function (t) { return !!t; }; }\n    if (defaultValue === void 0) { defaultValue = null; }\n    if (promiseList.length === 0) {\n        return Promise.resolve(defaultValue);\n    }\n    var todo = promiseList.length;\n    var finish = function () {\n        var e_1, _a;\n        var _b, _c;\n        todo = -1;\n        try {\n            for (var promiseList_1 = __values(promiseList), promiseList_1_1 = promiseList_1.next(); !promiseList_1_1.done; promiseList_1_1 = promiseList_1.next()) {\n                var promise = promiseList_1_1.value;\n                (_c = (_b = promise).cancel) === null || _c === void 0 ? void 0 : _c.call(_b);\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (promiseList_1_1 && !promiseList_1_1.done && (_a = promiseList_1.return)) _a.call(promiseList_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n    };\n    return new Promise(function (resolve, reject) {\n        var e_2, _a;\n        try {\n            for (var promiseList_2 = __values(promiseList), promiseList_2_1 = promiseList_2.next(); !promiseList_2_1.done; promiseList_2_1 = promiseList_2.next()) {\n                var promise = promiseList_2_1.value;\n                promise.then(function (result) {\n                    if (--todo >= 0 && shouldStop(result)) {\n                        finish();\n                        resolve(result);\n                    }\n                    else if (todo === 0) {\n                        resolve(defaultValue);\n                    }\n                })\n                    .catch(function (err) {\n                    if (--todo >= 0) {\n                        finish();\n                        reject(err);\n                    }\n                });\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (promiseList_2_1 && !promiseList_2_1.done && (_a = promiseList_2.return)) _a.call(promiseList_2);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n    });\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.nextFrame = nextFrame;\nexports.callLater = callLater;\n/**\n     * 等待 一帧 配合await  使用\n     * @returns\n     */\nfunction nextFrame() {\n    var _this = this;\n    return new Promise(function (resolve) {\n        _this.callLater(function () {\n            resolve(null);\n        }, null);\n    });\n}\nfunction callLater(func, thisObj) {\n    cc.director.once(cc.Director.EVENT_AFTER_UPDATE, func, thisObj);\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.timeout = timeout;\n/**\n * 一些特殊情况下，如果切换了场景，setTimeout 还会继续执行，而如果继续执行，有可能因为回调函数里有对当前节点的引用，会导致报错\n * 这里可以通过 判断该对象是否有效来兼容，取代 cc.tween.delay 这种较重的 api，提升整体性能\n * @param node\n * @param callback\n * @param timeout\n * @param args\n */\nfunction timeout(node, callback, timeout) {\n    var args = [];\n    for (var _i = 3; _i < arguments.length; _i++) {\n        args[_i - 3] = arguments[_i];\n    }\n    var id = setTimeout(function () {\n        clearTimeout(id);\n        if (!node || !cc.isValid(node, true)) {\n            return;\n        }\n        callback === null || callback === void 0 ? void 0 : callback.apply(this, [args]);\n    }, timeout, args);\n    return id;\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.decorate = decorate;\nexports.throttle = throttle;\nexports.throttleCall = throttleCall;\nfunction decorate(decorator) {\n    /**\n     * @param {any} _target\n     * @param {string} key\n     * @param {any} descriptor\n     */\n    return function (_target, key, descriptor) {\n        var fnKey = null;\n        var fn = null;\n        if (typeof descriptor.value === 'function') {\n            fnKey = 'value';\n            fn = descriptor.value;\n        }\n        else if (typeof descriptor.get === 'function') {\n            fnKey = 'get';\n            fn = descriptor.get;\n        }\n        if (!fn || !fnKey) {\n            throw new Error('not supported');\n        }\n        descriptor[fnKey] = decorator(fn, key);\n    };\n}\n/**\n * 节流 n 秒内只运行一次，若在 n 秒内重复触发，只有一次生效\n * |Click：————————————————————————————————————| delay\n   |        Click：————————————————————————————| delay\n   |                  Click：——————————————————| delay\n   |                              Click：——————| delay | 执行动作\n * @param delay\n * @returns\n */\nfunction throttle(delay, immediate) {\n    if (delay === void 0) { delay = 500; }\n    if (immediate === void 0) { immediate = true; }\n    return decorate(function (fn, key) {\n        var oldtime = Date.now();\n        var timer = null;\n        return function () {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            var context = this;\n            if (immediate) {\n                var newtime = Date.now();\n                if (newtime - oldtime >= delay) {\n                    fn.apply(context, args);\n                    oldtime = newtime;\n                }\n            }\n            else {\n                clearTimeout(timer);\n                timer = setTimeout(function () {\n                    fn.apply(context, args);\n                }, delay);\n            }\n        };\n    });\n}\n/**\n * 节流 n 秒内只运行一次，若在 n 秒内重复触发，只有一次生效\n * |Click：————————————————————————————————————| delay\n   |        Click：————————————————————————————| delay\n   |                  Click：——————————————————| delay\n   |                              Click：——————| delay | 执行动作\n * @param func\n * @param delay\n * @returns\n */\nfunction throttleCall(func, delay, immediate) {\n    if (delay === void 0) { delay = 500; }\n    if (immediate === void 0) { immediate = true; }\n    var oldtime = Date.now();\n    var timer = null;\n    return function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        var context = this;\n        if (immediate) {\n            var newtime = Date.now();\n            if (newtime - oldtime >= delay) {\n                func.apply(context, args);\n                oldtime = newtime;\n            }\n        }\n        else {\n            clearTimeout(timer);\n            timer = setTimeout(function () {\n                func.apply(context, args);\n            }, delay);\n        }\n    };\n}\n", "\"use strict\";\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __generator = (this && this.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\n    return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ObjectPool = void 0;\n/**\n * 对象池\n * 一般用于实例化对象\n * 关注：创建与恢复，恢复的目的是保证对象使用的时候为初始化状态，避免创建对象后因为修改对象属性，导致再次使用时产生副作用\n * （1）：支持初始化池大小\n * （2）：支持异步池，一些高性能场景需要通过异步池处理\n */\nvar ObjectPool = /** @class */ (function () {\n    /**\n     * 池需具备创建与恢复功能\n     * @param _create\n     * @param _reset\n     * @param _option\n     */\n    function ObjectPool(_create, _reset, _option) {\n        this._create = _create;\n        this._reset = _reset;\n        this._option = _option;\n        this.pool = [];\n        this.inUsed = new Set();\n        this._init();\n    }\n    ObjectPool.prototype._init = function () {\n        return __awaiter(this, void 0, void 0, function () {\n            var _a, size, args, onCreateComplete, obj, error_1;\n            return __generator(this, function (_b) {\n                switch (_b.label) {\n                    case 0:\n                        if (!this._option) return [3 /*break*/, 6];\n                        _a = this._option, size = _a.size, args = _a.args, onCreateComplete = _a.onCreateComplete;\n                        if (isNaN(size))\n                            size = 1;\n                        _b.label = 1;\n                    case 1:\n                        _b.trys.push([1, 5, , 6]);\n                        _b.label = 2;\n                    case 2:\n                        if (!(this.pool.length < size)) return [3 /*break*/, 4];\n                        return [4 /*yield*/, this._create(args)];\n                    case 3:\n                        obj = _b.sent();\n                        this.inUsed.add(obj);\n                        this.pool.push(obj);\n                        return [3 /*break*/, 2];\n                    case 4:\n                        if (onCreateComplete) {\n                            onCreateComplete(null);\n                        }\n                        return [3 /*break*/, 6];\n                    case 5:\n                        error_1 = _b.sent();\n                        if (onCreateComplete) {\n                            onCreateComplete(error_1);\n                        }\n                        return [3 /*break*/, 6];\n                    case 6: return [2 /*return*/];\n                }\n            });\n        });\n    };\n    Object.defineProperty(ObjectPool.prototype, \"poolSize\", {\n        /**\n         * 池大小\n         */\n        get: function () {\n            return this.pool.length;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * 当前对象是否在使用中\n     * @param obj\n     * @returns\n     */\n    ObjectPool.prototype.isInUsed = function (obj) {\n        return this.inUsed.has(obj);\n    };\n    /**\n     * 从池中获取一个对象\n     */\n    ObjectPool.prototype.get = function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        return __awaiter(this, void 0, void 0, function () {\n            var obj;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!(this.pool.length > 0)) return [3 /*break*/, 1];\n                        obj = this.pool.pop();\n                        this._reset(obj);\n                        return [3 /*break*/, 3];\n                    case 1: return [4 /*yield*/, this._create(args)];\n                    case 2:\n                        obj = _a.sent();\n                        _a.label = 3;\n                    case 3:\n                        this.inUsed.add(obj);\n                        return [2 /*return*/, obj];\n                }\n            });\n        });\n    };\n    /**\n     * 从池中异步取出一个对象\n     * @param args\n     * @returns\n     */\n    ObjectPool.prototype.asyncGet = function () {\n        var _this = this;\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        return new Promise(function (resolve, reject) { return __awaiter(_this, void 0, void 0, function () {\n            var obj, error_2;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!(this.pool.length > 0)) return [3 /*break*/, 1];\n                        obj = this.pool.pop();\n                        this._reset(obj);\n                        this.inUsed.add(obj);\n                        resolve(obj);\n                        return [3 /*break*/, 4];\n                    case 1:\n                        _a.trys.push([1, 3, , 4]);\n                        return [4 /*yield*/, this._create(args)];\n                    case 2:\n                        obj = _a.sent();\n                        this.inUsed.add(obj);\n                        resolve(obj);\n                        return [3 /*break*/, 4];\n                    case 3:\n                        error_2 = _a.sent();\n                        reject(error_2);\n                        return [3 /*break*/, 4];\n                    case 4: return [2 /*return*/];\n                }\n            });\n        }); });\n    };\n    /**\n     * 释放\n     * @param obj\n     */\n    ObjectPool.prototype.release = function (obj) {\n        if (this.inUsed.has(obj)) {\n            this.inUsed.delete(obj);\n            this.pool.push(obj);\n        }\n    };\n    /**\n     * 清空池\n     */\n    ObjectPool.prototype.clear = function () {\n        this.pool.length = 0;\n        this.inUsed.clear();\n    };\n    return ObjectPool;\n}());\nexports.ObjectPool = ObjectPool;\n/**\n * example\n * const pool = new ObjectPool<cc.Node>(\n    (prefab) => {\n        const node = cc.instantiate(prefab);\n        node.addComponent(cc.Sprite);\n        node.x = 100;\n        node.y = 100;\n        return node;\n    },\n    (node) => {\n        node.x = 0;\n        node.y = 0;\n    });\n\nlet prefab: cc.Prefab;\nconst instance = pool.get(prefab);\nsetTimeout(() => {\n    pool.release(instance);\n}, 1000);\n\nlet list=[];\nfor(let i=0;i<10;i++){\n    const inst = pool.get(prefab);\n    list[i]=inst;\n}\n\nsetTimeout(()=>{\n    for(let i=0;i<10;i++){\n        pool.release(list[i]);\n    }\n})\n */ \n", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.TimeoutBarrier = exports.Barrier = void 0;\n/**\n * 屏障控制器\n */\nvar Barrier = /** @class */ (function () {\n    function Barrier() {\n        var _this = this;\n        this._isOpen = false;\n        this._promise = new Promise(function (c, e) {\n            _this._completePromise = c;\n        });\n    }\n    Object.defineProperty(Barrier.prototype, \"isOpen\", {\n        /** 屏障是否打开 */\n        get: function () {\n            return this._isOpen;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /** 重置 */\n    Barrier.prototype.reset = function () {\n        this._isOpen = false;\n    };\n    /** 打开屏障 */\n    Barrier.prototype.open = function () {\n        this._isOpen = true;\n        this._completePromise(true);\n    };\n    /** 等待屏障打开 */\n    Barrier.prototype.wait = function () {\n        return this._promise;\n    };\n    return Barrier;\n}());\nexports.Barrier = Barrier;\n/**\n * 超时屏障控制器\n */\nvar TimeoutBarrier = /** @class */ (function (_super) {\n    __extends(TimeoutBarrier, _super);\n    function TimeoutBarrier(autoOpenTimeMs) {\n        var _this = _super.call(this) || this;\n        _this._timeout = setTimeout(function () { return _this.open(); }, autoOpenTimeMs);\n        return _this;\n    }\n    TimeoutBarrier.prototype.open = function () {\n        clearTimeout(this._timeout);\n        _super.prototype.open.call(this);\n    };\n    return TimeoutBarrier;\n}(Barrier));\nexports.TimeoutBarrier = TimeoutBarrier;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.EventVo = void 0;\nvar EventVo = /** @class */ (function () {\n    function EventVo() {\n        this._id = 0;\n        EventVo._uniqueID += 1;\n        this._id = EventVo._uniqueID;\n    }\n    Object.defineProperty(EventVo.prototype, \"id\", {\n        get: function () {\n            return this._id;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    EventVo._uniqueID = 0;\n    return EventVo;\n}());\nexports.EventVo = EventVo;\n", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;\nvar Component = /** @class */ (function (_super) {\n    __extends(Component, _super);\n    function Component() {\n        var _this = _super.apply(this, __spreadArray([], __read(arguments), false)) || this;\n        _this._state = {};\n        return _this;\n    }\n    Object.defineProperty(Component.prototype, \"state\", {\n        get: function () {\n            return this._state;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * 设置最新状态\n     * @param state\n     * @param replace\n     * @param callback\n     */\n    Component.prototype.setState = function (state, replace, callback) {\n        if (this._state !== state) {\n            //状态不能直接传过去，这样每次都需要传所有的值，否则，会出问题\n            var shouldUpdate = true;\n            if (this._state) {\n                shouldUpdate = this.shouldComponentUpdate(state);\n            }\n            if (Object.prototype.toString.call(state) !== '[object Object]') {\n                this._state = state;\n            }\n            else {\n                if (!this._state) {\n                    this._state = Object.create(null);\n                }\n                if (!replace)\n                    this.deepCopy(this._state, state);\n                else\n                    this._state = state;\n            }\n            //是否需要更新render函数\n            if (shouldUpdate) {\n                this.componentWillUpdate(this._state);\n                //开始更新\n                this.render();\n                //回调\n                if (callback) {\n                    callback();\n                }\n            }\n        }\n    };\n    /**\n     * 深度拷贝数据\n     * @param target\n     * @param source\n     */\n    Component.prototype.deepCopy = function (target, source) {\n        for (var key in source) {\n            var value = source[key];\n            //直接替换属性\n            target[key] = value;\n        }\n    };\n    /**\n     * 组件将初始化时调用\n     */\n    Component.prototype.componentWillMount = function () {\n    };\n    /**\n     * 组件初始化时调用，只会被调用一次\n     */\n    Component.prototype.componentDidMount = function () {\n    };\n    /**\n     * 组件卸载时调用\n     */\n    Component.prototype.componentWillUnmount = function () {\n    };\n    /**\n     * 是否应该更新改组件，一般通过这个函数控制可以优化性能\n     * @param nextState\n     */\n    Component.prototype.shouldComponentUpdate = function (nextState) {\n        return true;\n    };\n    /**\n     * 生命周期shouldComponentUpdate如果为真，那么，才会被调用，如果调用，会在render函数之前调用\n     * @param nextProps\n     * @param nextState\n     */\n    Component.prototype.componentWillUpdate = function (nextState) {\n    };\n    /**\n     * 组件更新完成之后，调用render函数之后执行\n     * @param prevProps\n     * @param prevState\n     */\n    Component.prototype.componentDidUpdate = function (prevState) {\n    };\n    /**\n     * 渲染ui\n     */\n    Component.prototype.render = function () {\n    };\n    Component = __decorate([\n        ccclass\n    ], Component);\n    return Component;\n}(cc.Component));\nexports.default = Component;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getUrlParameterValue = getUrlParameterValue;\n/**\n * 获取地址参数值\n * @param paramName\n * @returns\n */\nfunction getUrlParameterValue(paramName) {\n    if (!globalThis.location) {\n        return \"\";\n    }\n    var query = globalThis.location.search.substring(1);\n    var vars = query.split(\"&\");\n    for (var i_1 = 0; i_1 < vars.length; i_1++) {\n        var pair = vars[i_1].split(\"=\");\n        if (pair[0] === paramName) {\n            return decodeURIComponent(pair[1]);\n        }\n    }\n    return \"\";\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.task = void 0;\n/**\n * 高效任务系统\n * 利用系统空余时间做一些优先级不高的任务\n */\nvar Task = /** @class */ (function () {\n    function Task() {\n        this.taskList = [];\n    }\n    /**\n     * 运行任务\n     * @param taskHandler\n     * @param args\n     */\n    Task.prototype.run = function (taskHandler) {\n        var args = [];\n        for (var _i = 1; _i < arguments.length; _i++) {\n            args[_i - 1] = arguments[_i];\n        }\n        //如果不支持，直接调用\n        if (!window['requestIdleCallback']) {\n            window.requestIdleCallback = function (callback) {\n                var startTime = Date.now();\n                return setTimeout(function () {\n                    callback({\n                        didTimeout: false,\n                        timeRemaining: function () {\n                            return Math.max(0, 30 - (Date.now() - startTime));\n                        }\n                    });\n                });\n            };\n            window.cancelIdleCallback = function (id) {\n                clearTimeout(id);\n            };\n        }\n        this.taskList.push({\n            handler: taskHand<PERSON>,\n            args: args\n        });\n        if (!this.taskHandle) {\n            this.taskHandle = window.requestIdleCallback(this.idleRequestCallback.bind(this), { timeout: 20 });\n        }\n    };\n    Task.prototype.idleRequestCallback = function (deadline) {\n        /** 如果任务较轻，一次性处理了 */\n        while ((deadline.timeRemaining() > 5 || deadline.didTimeout) && this.taskList.length > 0) {\n            var task_1 = this.taskList.shift();\n            // console.log(`deadline.didTimeout:`, deadline.didTimeout);\n            if (task_1.handler) {\n                task_1.handler.apply(this, task_1.args);\n            }\n        }\n        /** 如果任务在当前帧剩余时间没有处理完，则下一个空闲时间处理 */\n        if (this.taskList.length > 0) {\n            this.taskHandle = window.requestIdleCallback(this.idleRequestCallback.bind(this), { timeout: 20 });\n        }\n        else {\n            cancelIdleCallback(this.taskHandle);\n            this.taskHandle = 0;\n        }\n    };\n    return Task;\n}());\nexports.task = new Task();\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.waitFor = void 0;\nvar WaitFor = /** @class */ (function () {\n    function WaitFor() {\n        this._waitFors = {};\n    }\n    /**\n     * 开始\n     * @param key\n     */\n    WaitFor.prototype.start = function (key) {\n        var _resolve, _reject;\n        this._waitFors[key] = {\n            startTime: Date.now(),\n            promise: new Promise(function (resolve, reject) {\n                _resolve = resolve;\n                _reject = reject;\n            }),\n            state: false\n        };\n        this._waitFors[key].resolve = _resolve;\n        this._waitFors[key].reject = _reject;\n    };\n    /**\n     * 结束\n     * @param key\n     */\n    WaitFor.prototype.end = function (key) {\n        var waitInfo = this._waitFors[key];\n        if (waitInfo) {\n            waitInfo.endTime = Date.now();\n            waitInfo.state = true;\n        }\n        else {\n            console.error(\"waitFor \".concat(key, \" end \\u65F6\\u9700\\u5148\\u6267\\u884C start!\"));\n        }\n    };\n    /**\n     * 等待某个键值的状态完成\n     * @param key\n     */\n    WaitFor.prototype.wait = function (key, option) {\n        var waitInfo = this._waitFors[key];\n        if (waitInfo) {\n            var resolve_1 = waitInfo.resolve, reject_1 = waitInfo.reject;\n            if (waitInfo.state) {\n                if (resolve_1) {\n                    resolve_1(waitInfo.state);\n                }\n            }\n            else {\n                waitInfo.interval = setInterval(function () {\n                    if (waitInfo.state) {\n                        clearInterval(waitInfo.interval);\n                        resolve_1(key);\n                    }\n                    else {\n                        //如果超时了，则直接返回\n                        if (option && !isNaN(option.timeout)) {\n                            if (Date.now() - waitInfo.startTime > option.timeout) {\n                                clearInterval(waitInfo.interval);\n                                reject_1(\"wait for \".concat(key, \" is timeout!\"));\n                            }\n                        }\n                    }\n                }, 16);\n            }\n            return waitInfo.promise;\n        }\n    };\n    return WaitFor;\n}());\nexports.waitFor = new WaitFor();\n", "\"use strict\";\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __generator = (this && this.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\n    return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Trait = void 0;\nvar TraitConfigInfo_1 = require(\"../traitConfig/TraitConfigInfo\");\n/**\n * 特性有以下特点：\n * （1）：每个特性逻辑都独立且封装，不与界面耦合\n * （2）：每个特性有自己的属性(属性从配置里获取)\n * （3）：每个特性有自身的状态变化，状态变化外部驱动\n * （4）：traits 文件夹里存储了所有的特性\n * （5）：每个特性都有自己的生命周期\n * （6）：每个特性依赖目标组件各种数据\n * （7）：基于特性可以获取其装饰的目标组件\n * （8）：特性支持分层\n * （9）：特性不能依赖其它特性，只能依赖其它特性原始数据，如果实在需要依赖，需要采用其它方法\n *\n * TODO 需要支持动态特性，不同特性可以开启可以激活另外的特性\n */\nvar Trait = /** @class */ (function () {\n    function Trait() {\n        this._state = {};\n        this._props = {};\n        this._active = false;\n        this._subTraits = [];\n    }\n    Object.defineProperty(Trait, \"activedListenerTraits\", {\n        /**\n         * 已激活的监听特性\n         */\n        get: function () {\n            return Trait._activedListenerTraits;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Trait.prototype, \"id\", {\n        /**\n         * 特性id\n         */\n        get: function () {\n            return this._id;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Trait.prototype, \"condition\", {\n        /**\n         * 特性激活条件（目前主要是用于 M*N，不排除后期会用于业务逻辑，这里先支持)，条件特性里面所有条件变量必须暴露到条件对象上才行（TODO 后面通过语法检测其有效性）\n         * 如果有条件特性\n         */\n        get: function () {\n            return this._condition;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Trait.prototype, \"traitName\", {\n        /**\n         * 特性名称\n         */\n        get: function () {\n            return getClassName(this.constructor);\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Trait.prototype, \"state\", {\n        /**\n         * 特性所持有的数据状态（外部只能访问，不能修改，如果要修改，直接通过 setState 来做）\n         */\n        get: function () {\n            return this._state;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * state 的初始化数据\n     * @returns\n     */\n    Trait.prototype.data = function () {\n        return {};\n    };\n    Object.defineProperty(Trait.prototype, \"props\", {\n        /**\n         * 特性持有的属性（不能修改，从配置或者服务器下发获取）\n         */\n        get: function () {\n            return this._props;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Trait.prototype, \"active\", {\n        /**\n         * 特性的静态激活状态（与特性静态属性有关）\n         */\n        get: function () {\n            var _active = this.props && Object.keys(this.props).length > 0;\n            if (!this._condition) {\n                return _active;\n            }\n            try {\n                // eslint-disable-next-line no-eval\n                return _active && eval(this._condition);\n            }\n            catch (error) {\n                console.error(\"\\u6267\\u884C\\u6761\\u4EF6\\u7279\\u6027\\u6FC0\\u6D3B\\u53D1\\u751F\\u9519\\u8BEF\\uFF1A\", error.stack);\n                return _active;\n            }\n        },\n        /**\n         * 设置特性的激活状态（动态特性）,一般情况下只能框架层调用，上层业务不能调用\n         */\n        set: function (value) {\n            var _a;\n            if (this._active !== value) {\n                this._active = value;\n                if (value) {\n                    //如果当前特性未绑定属性，则初始化绑定一次\n                    this._props = (_a = TraitConfigInfo_1.traitConfigInfo.traitsClassNameMap[this.traitName]) === null || _a === void 0 ? void 0 : _a.param;\n                    this.onEnable();\n                }\n                else {\n                    this._props = {};\n                    this.onDisable();\n                }\n            }\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * 注册子特性，子特性的开启受父特性影响\n     * 父特性：\n     *  开启：子特性必须在配置表里有才开启，否则，关闭\n     *  关闭：子特性不管在配置表里有没有，都关闭\n     *\n     */\n    Trait.prototype.registerSubTraits = function () {\n        return null;\n    };\n    /**\n     * 获取子特性实例\n     * @returns\n     */\n    Trait.prototype.getSubTraits = function () {\n        return this._subTraits;\n    };\n    /**\n     * 特性被创建时\n     */\n    Trait.prototype.onCreate = function () {\n    };\n    /**\n     * 特性被激活时调用\n     */\n    Trait.prototype.onEnable = function () {\n    };\n    /**\n     * 特性被禁用时调用\n     */\n    Trait.prototype.onDisable = function () {\n    };\n    /**\n     * 设置最新状态\n     * @param state\n     * @param replace\n     * @param callback\n     */\n    Trait.prototype.setState = function (state) {\n        for (var key in state) {\n            var value = state[key];\n            this._state[key] = value;\n        }\n    };\n    /**\n     * 特性装饰的目标函数被调用时触发\n     * @param target\n     */\n    Trait.prototype.onActive = function (target) {\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                return [2 /*return*/];\n            });\n        });\n    };\n    /**\n     * 监听激活的的特性\n     * @param traitConstructor\n     * @param actived\n     */\n    Trait.onTraitActive = function (traitConstructor, preActive, actived) {\n        if (!this._activedListenerTraits.has(traitConstructor)) {\n            this._activedListenerTraits.set(traitConstructor, []);\n        }\n        this._activedListenerTraits.get(traitConstructor).push({ preActive: preActive, actived: actived });\n    };\n    Trait._activedListenerTraits = new Map();\n    return Trait;\n}());\nexports.Trait = Trait;\n", "\"use strict\";\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __generator = (this && this.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\n    return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.UI = void 0;\nvar GameLayer_1 = require(\"../layer/GameLayer\");\nvar ResLoader_1 = require(\"../loader/ResLoader\");\nvar UI = /** @class */ (function () {\n    function UI() {\n    }\n    // 设置通用model prefab\n    UI.setModalPrefab = function (config) {\n        if (typeof config === 'string') {\n            this.modalConfig.url = config;\n        }\n        else {\n            this.modalConfig = config;\n        }\n    };\n    /**\n     * 统一的预制体加载方法\n     * @param config 预制体配置\n     * @returns 加载的预制体资源\n     */\n    UI.loadPrefabAsset = function (config) {\n        return __awaiter(this, void 0, void 0, function () {\n            var bundleConfig, error_1;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!config || !config.url) {\n                            throw new Error('[UI] 无效的预制体配置');\n                        }\n                        _a.label = 1;\n                    case 1:\n                        _a.trys.push([1, 6, , 7]);\n                        if (!config.bundleName) return [3 /*break*/, 3];\n                        bundleConfig = { bundleName: config.bundleName };\n                        if (config.version) {\n                            bundleConfig.version = config.version;\n                        }\n                        return [4 /*yield*/, ResLoader_1.ResLoader.asyncLoadByBundle(bundleConfig, config.url, cc.Prefab)];\n                    case 2: return [2 /*return*/, _a.sent()];\n                    case 3: return [4 /*yield*/, ResLoader_1.ResLoader.asyncLoad(config.url, cc.Prefab)];\n                    case 4: return [2 /*return*/, _a.sent()];\n                    case 5: return [3 /*break*/, 7];\n                    case 6:\n                        error_1 = _a.sent();\n                        console.error(\"[UI] \\u52A0\\u8F7D\\u9884\\u5236\\u4F53\\u5931\\u8D25: \".concat(config.url), error_1);\n                        throw error_1;\n                    case 7: return [2 /*return*/];\n                }\n            });\n        });\n    };\n    /**\n     * 隐藏所有UI\n     */\n    UI.hideAll = function () {\n        for (var key in this.uiCache) {\n            var data = UI.uiCache[key];\n            if (data.node) {\n                data.node.active = false;\n            }\n        }\n    };\n    /**\n     * 清理所有UI缓存和状态（用于子游戏重置）\n     */\n    UI.clearAllCache = function () {\n        try {\n            // 先隐藏所有UI\n            this.hideAll();\n            // 清理缓存对象\n            this.uiCache = {};\n            this.uiIsLoading = {};\n            this.uiIsCancelLoad = {};\n            this.resolves = {};\n            this.lastPage = null;\n            if (CC_DEBUG) {\n                console.log('[UI] 所有UI缓存已清理');\n            }\n        }\n        catch (error) {\n            console.error('[UI] 清理UI缓存时发生错误:', error);\n        }\n    };\n    /**\n     * UI是否处于激活状态\n     */\n    UI.activeState = function (url) {\n        for (var key in this.uiCache) {\n            if (key == url) {\n                var data = UI.uiCache[key];\n                if (data.node) {\n                    return data.node.active;\n                }\n            }\n        }\n        return false;\n    };\n    /**\n     * 取消加载后的操作\n     * @param config\n     */\n    UI.cancelLoad = function (config) {\n        this.uiIsCancelLoad[config.url] = true;\n    };\n    UI.clearCache = function (url) {\n        delete UI.uiCache[url];\n        delete this.uiIsLoading[url];\n        delete this.uiIsCancelLoad[url];\n    };\n    /**\n     * 添加面板创建、打开、关闭监听\n     * @param event\n     * @param callback\n     */\n    UI.addEventListener = function (event, callback) {\n        if (!this.events[event]) {\n            this.events[event] = [];\n        }\n        this.events[event].push(callback);\n    };\n    UI.removeEventListener = function (event, callback) {\n        if (this.events[event]) {\n            var index = this.events[event].indexOf(callback);\n            if (index != -1) {\n                this.events[event].splice(index, 1);\n            }\n        }\n    };\n    /**\n     * 打开某个预制体UI\n     * （1）：所有的预制体都放在resources文件夹下\n     * （2）：被打开的UI只会被实例化一次\n     * （3）：具备响应处理多次点击而导致的问题\n     * （4）：后打开的会放在上面\n     * （5）：增加弱网环境导致的用户多次点击而导致的多次加载预制体的BUG处理机制\n     * 打开某个UI,此处打开的是某个动态加载的预制体UI,通常情况下这些预制体都是放在resources文件夹下\n     * 被打开的UI都通过缓存管理着\n     *\n     * @param config 当前配置\n     * @param parent 父容器\n     */\n    UI.show = function (config, parent) {\n        var _this = this;\n        return new Promise(function (c, e) { return __awaiter(_this, void 0, void 0, function () {\n            var ui, asset, modalAsset, modalNode_1, panelEase, createEvents, i_1, event_1, modalNode, panelEase, currentResolves, i_2, resolve, openEvents, i_3, event_2, error_2, currentResolves;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (CC_DEBUG) {\n                            if (config.name !== 'TouchEffect') {\n                                console.log(\"ui.show() === =>\", config.name);\n                            }\n                        }\n                        if (!this.resolves[config.url]) {\n                            this.resolves[config.url] = [];\n                        }\n                        this.resolves[config.url].push(c);\n                        /** 如果当前ui正在加载，直接reject */\n                        if (this.uiIsLoading[config.url]) {\n                            return [2 /*return*/];\n                        }\n                        _a.label = 1;\n                    case 1:\n                        _a.trys.push([1, 7, , 8]);\n                        parent = parent ? parent : GameLayer_1.gameUiLayer;\n                        ui = void 0;\n                        // 容错节点被销毁时没有移除缓存\n                        if (UI.uiCache[config.url] && !cc.isValid(UI.uiCache[config.url].node)) {\n                            delete UI.uiCache[config.url];\n                        }\n                        if (!!UI.uiCache[config.url]) return [3 /*break*/, 5];\n                        this.uiIsLoading[config.url] = true;\n                        return [4 /*yield*/, this.loadPrefabAsset(config)];\n                    case 2:\n                        asset = _a.sent();\n                        ui = INSTANTIATE(asset);\n                        ui['__config__'] = config;\n                        /******************************************************\n                         *\n                         *                      隐藏上一个 page\n                         *\n                         *******************************************************/\n                        if (config.page && this.lastPage) {\n                            this.hide(this.lastPage);\n                        }\n                        if (config.page) {\n                            this.lastPage = ui;\n                        }\n                        // 判断是否需要停止，如停止就不加载\n                        if (this.uiIsCancelLoad[config.url]) {\n                            this.uiIsLoading[config.url] = false;\n                            delete this.uiIsCancelLoad[config.url];\n                        }\n                        UI.uiCache[config.url] = {};\n                        UI.uiCache[config.url].node = ui;\n                        UI.uiCache[config.url].ease = config.ease;\n                        if (!config.modal) return [3 /*break*/, 4];\n                        return [4 /*yield*/, this.loadPrefabAsset(this.modalConfig)];\n                    case 3:\n                        modalAsset = _a.sent();\n                        modalNode_1 = INSTANTIATE(modalAsset);\n                        parent.addChild(modalNode_1);\n                        (function (target) {\n                            modalNode_1.on(cc.Node.EventType.TOUCH_START, function (event) {\n                                UI.hide(target);\n                            });\n                        })(ui);\n                        UI.uiCache[config.url].modal = modalNode_1;\n                        _a.label = 4;\n                    case 4:\n                        if (ui.parent == null) {\n                            parent.addChild(ui);\n                        }\n                        else {\n                            if (ui.parent != parent) {\n                                ui.setParent(parent);\n                            }\n                        }\n                        this.uiIsLoading[config.url] = false;\n                        if (config.ease != null) {\n                            panelEase = ui.getComponent(config.ease);\n                            if (panelEase == null) {\n                                panelEase = ui.addComponent(config.ease);\n                                // panelEase.show();\n                            }\n                        }\n                        createEvents = this.events[\"create\"];\n                        for (i_1 = 0; i_1 < (createEvents === null || createEvents === void 0 ? void 0 : createEvents.length); i_1++) {\n                            event_1 = createEvents[i_1];\n                            event_1.apply(this, [config]);\n                        }\n                        return [3 /*break*/, 6];\n                    case 5:\n                        this.uiIsLoading[config.url] = false;\n                        ui = UI.uiCache[config.url].node;\n                        /******************************************************\n                         *\n                         *                      隐藏上一个 page\n                         *\n                         *******************************************************/\n                        if (config.page && this.lastPage && ui !== this.lastPage) {\n                            this.hide(this.lastPage);\n                        }\n                        if (config.page) {\n                            this.lastPage = ui;\n                        }\n                        if (config.modal) {\n                            modalNode = UI.uiCache[config.url].modal;\n                            if (!modalNode.parent) {\n                                parent.addChild(modalNode);\n                            }\n                        }\n                        ui.active = true;\n                        _a.label = 6;\n                    case 6:\n                        if (ui.parent != parent) {\n                            ui.setParent(parent);\n                        }\n                        UI.uiCache[config.url].parent = parent;\n                        ui.setSiblingIndex(parent.childrenCount);\n                        if (config.ease != null) {\n                            panelEase = ui.getComponent(config.ease);\n                            panelEase === null || panelEase === void 0 ? void 0 : panelEase.show();\n                        }\n                        currentResolves = this.resolves[config.url];\n                        if (currentResolves) {\n                            for (i_2 = 0; i_2 < currentResolves.length; i_2++) {\n                                resolve = currentResolves[i_2];\n                                resolve(ui);\n                            }\n                            //需要清理，否则会导致 C++ 层 JS: [ERROR]:  resolveAfterPromiseResolved stacktrace: 报错，且有性能问题。\n                            currentResolves.length = 0;\n                        }\n                        openEvents = this.events[\"open\"];\n                        for (i_3 = 0; i_3 < (openEvents === null || openEvents === void 0 ? void 0 : openEvents.length); i_3++) {\n                            event_2 = openEvents[i_3];\n                            event_2.apply(this, [config]);\n                        }\n                        return [3 /*break*/, 8];\n                    case 7:\n                        error_2 = _a.sent();\n                        console.error(\"[UI] \\u663E\\u793AUI\\u5931\\u8D25: \".concat(config.name, \" (\").concat(config.url, \")\"), error_2);\n                        this.uiIsLoading[config.url] = false;\n                        // 清理可能的半状态\n                        delete this.uiIsCancelLoad[config.url];\n                        currentResolves = this.resolves[config.url];\n                        if (currentResolves) {\n                            currentResolves.length = 0;\n                        }\n                        e(error_2);\n                        return [3 /*break*/, 8];\n                    case 8: return [2 /*return*/];\n                }\n            });\n        }); });\n    };\n    /**\n     * 预加载时，缓存创建的节点\n     * @param config prefab 标识路径\n     * @param cacheUI 缓存prefab\n     * @param parent 添加父节点\n     */\n    UI.addUICache = function (config, cacheUI, parent) {\n        try {\n            var cacheNode = INSTANTIATE(cacheUI);\n            if (!UI.uiCache[config.url]) {\n                UI.uiCache[config.url] = {};\n            }\n            UI.uiCache[config.url].node = cacheNode;\n            parent.addChild(cacheNode);\n            UI.uiCache[config.url].parent = parent;\n            cacheNode.setSiblingIndex(cacheNode.parent.childrenCount);\n            cacheNode.active = false;\n        }\n        catch (error) {\n            console.error(\"[UI] addUICache 失败:\", error);\n        }\n    };\n    /**\n     * 拿到某个预制体的【入口组件】\n     * 为了提升获取组件性能，这里拿过一遍的引用将会被缓存起来，下次不用再进行遍历拿取\n     * 如果预制体未被加载，则会直接加载，加载完毕之后，再拿组件\n     *\n     * @param config 预制体配置\n     * @param parent 父结点\n     */\n    UI.getComponent = function (config, parent) {\n        return __awaiter(this, void 0, void 0, function () {\n            var data, node, comp;\n            return __generator(this, function (_a) {\n                if (!config) {\n                    console.error(\"\\u83B7\\u53D6\\u7EC4\\u4EF6\\u65F6\\u4F20\\u5165\\u7684config\\u4E0D\\u80FD\\u4E3A\\u7A7A\\uFF01\");\n                    return [2 /*return*/, undefined];\n                }\n                data = this.uiCache[config.url];\n                if (data) {\n                    node = data.node;\n                    // node = node || await this.show(config, parent);\n                }\n                // else {\n                //     node = await this.show(config, parent);\n                // }\n                if (config.comp) {\n                    if (node) {\n                        comp = node.getComponent(config.comp);\n                        this.uiCache[config.url].comp = comp;\n                        return [2 /*return*/, comp];\n                    }\n                    else {\n                        return [2 /*return*/, undefined];\n                    }\n                }\n                else {\n                    console.error(\"\\u5FC5\\u987B\\u4E3A\\u914D\\u7F6E\".concat(config, \"\\u914D\\u7F6E\\u7EC4\\u4EF6\\u5F15\\u7528\\uFF01\"));\n                    return [2 /*return*/, undefined];\n                }\n                return [2 /*return*/];\n            });\n        });\n    };\n    /**\n     * 隐藏 ui\n     * @param target\n     */\n    UI.hide = function (target) {\n        var config;\n        if (target instanceof cc.Component) {\n            config = target.node['__config__'];\n        }\n        else {\n            config = target['__config__'];\n        }\n        if (config === null || config === void 0 ? void 0 : config.url) {\n            this.hideUI(config);\n        }\n    };\n    UI.hideUI = function (config) {\n        var _this = this;\n        var ui = UI.uiCache[config.url];\n        if (!ui) {\n            return;\n        }\n        else {\n            if (config.ease != null) {\n                var panelEase = ui.node.getComponent(config.ease);\n                if (panelEase != null) {\n                    panelEase.onCloseEaseComplete = function () {\n                        ui.node.active = false;\n                        _this.dispatchClose(config);\n                    };\n                    panelEase.hide();\n                }\n                else {\n                    ui.node.active = false;\n                    this.dispatchClose(config);\n                }\n            }\n            else {\n                ui.node.active = false;\n                this.dispatchClose(config);\n            }\n            if (config.modal) {\n                ui.modal.setParent(null);\n            }\n        }\n    };\n    UI.dispatchClose = function (config) {\n        var closeEvents = this.events[\"close\"];\n        for (var i_4 = 0; i_4 < (closeEvents === null || closeEvents === void 0 ? void 0 : closeEvents.length); i_4++) {\n            var event_3 = closeEvents[i_4];\n            event_3.apply(this, [config]);\n        }\n    };\n    UI.hideLayer = function (layer) {\n        layer.children.forEach(function (ele) {\n            ele.active = false;\n        });\n    };\n    /** 缓存当前某个预制体与其主引用 */\n    UI.uiCache = {};\n    /** 存储当前预制体是否正在加载 */\n    UI.uiIsLoading = {};\n    /** 存储当前预制体是否取消加载 */\n    UI.uiIsCancelLoad = {};\n    UI.events = {};\n    UI.resolves = {};\n    UI.modalConfig = {\n        name: 'Modal',\n        url: 'prefabs/modal/Modal',\n        ease: null,\n        modal: true,\n    };\n    return UI;\n}());\nexports.UI = UI;\n", "\"use strict\";\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __generator = (this && this.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\n    return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.sequence = sequence;\n/**\n * 按顺序执行提供的 Promise 工厂函数列表，并返回一个 Promise，该 Promise 将解析为每个 Promise 的结果数组\n * @param promiseFactories\n * @param interruptConfition 中断条件，每执行一次 promise 之前，需判断是否满足中断条件\n * @returns\n */\nfunction sequence(promiseFactories, interruptConfition) {\n    var results = [];\n    var index = 0;\n    var len = promiseFactories.length;\n    var isInterrupt = false;\n    function next() {\n        return index < len ? promiseFactories[index++]() : null;\n    }\n    function checkInterruptCondition() {\n        return __awaiter(this, void 0, void 0, function () {\n            var cond;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!interruptConfition) return [3 /*break*/, 2];\n                        return [4 /*yield*/, interruptConfition()];\n                    case 1:\n                        cond = _a.sent();\n                        return [2 /*return*/, cond];\n                    case 2: return [2 /*return*/, false];\n                }\n            });\n        });\n    }\n    function thenHandler(result) {\n        return checkInterruptCondition().then(function (isInterrupt) {\n            if (isInterrupt) {\n                if (len >= results.length) {\n                    results.push.apply(results, __spreadArray([], __read(new Array(len - results.length).fill(undefined)), false));\n                }\n                return Promise.resolve(results);\n            }\n            if (result !== undefined && result !== null) {\n                results.push(result);\n            }\n            var n = next();\n            if (n) {\n                return n.then(thenHandler).catch(function (reason) {\n                    // If interrupted\n                    if (isInterrupt) {\n                        return Promise.resolve(results);\n                    }\n                    results.push.apply(results, __spreadArray([], __read(new Array(len - results.length).fill(undefined)), false));\n                    return Promise.resolve(results);\n                });\n            }\n            return Promise.resolve(results);\n        });\n    }\n    return Promise.resolve(null).then(thenHandler);\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.DotUploadType = void 0;\nexports.createDotData = createDotData;\nvar DecoratorTrait_1 = require(\"../decorators/DecoratorTrait\");\nvar Storage_1 = require(\"../storage/Storage\");\nvar TraitConfigInfo_1 = require(\"../traitConfig/TraitConfigInfo\");\nvar DotUploadType;\n(function (DotUploadType) {\n    /** 数数（数数上报原生的时候同时会上报给数仓平台） */\n    DotUploadType[DotUploadType[\"SHUSHU\"] = 0] = \"SHUSHU\";\n    /** 数仓 */\n    DotUploadType[DotUploadType[\"SHUCANG\"] = 1] = \"SHUCANG\";\n})(DotUploadType || (exports.DotUploadType = DotUploadType = {}));\n;\n/**\n         * IsRemindAndAssistPlayersTrait 特性被激活时，打点数据可能需要劫持当前数据，并附加一些数据到原打点数据上\n         * 比如：\n         * （1）：在一些打点数据上添加一些新的属性\n         * （2）：修改打点数据已存在属性的值\n         * （3）：可以做一些其它操作等，比如存储数据\n         */\n// DS('usr_data_game_end',\n//     { 'GameTime': 10, 'GameType': 100 },\n//     [\n//         {\n//             class: IsRemindAndAssistPlayersTrait,\n//             assign: (trait:Trait, data) => {\n//                 return {\n//                     abc: 11\n//                 };\n//             }\n//         },\n//         {\n//             class: IsPointMarkerTrait,\n//             assign: (trait:Trait, data) => {\n//                 return {\n//                     CornerIcon: 1\n//                 }\n//             }\n//         }\n//     ]\n// );\n/**\n * 创建打点数据\n * @param uploadType\n * @param eventKey\n * @param params\n * @param traits\n */\nfunction createDotData(uploadType, eventKey, params, traits, callback) {\n    /** 通用化配置 */\n    var data = {\n        time: Date.now()\n    };\n    //const { nowWayArr, gameWayNum } = gameWayInfo;\n    var gameWayNum = Storage_1.storage.getItem(\"gameWayNum\");\n    var nowWayArr = Storage_1.storage.getItem(\"nowWayArr\", []);\n    // ABStatus 补充\n    if ((nowWayArr === null || nowWayArr === void 0 ? void 0 : nowWayArr.length) > 0) {\n        data.ABStatus = nowWayArr;\n    }\n    // active_waynum 补充 TODO 如果遇到 A_B这种形式，则可能计算有问题，需要看主包的处理逻辑\n    var _gameWayNum = +gameWayNum;\n    if (!isNaN(_gameWayNum)) {\n        data.active_waynum = {\n            pici: Math.floor(_gameWayNum / 100).toString(),\n            1: _gameWayNum\n        };\n    }\n    // TODO new_waynum 补充\n    if (!params) {\n        params = {};\n    }\n    if (params) {\n        data = Object.assign(params, data);\n    }\n    // 数仓才会加名字，因为传给原生需要\n    if (uploadType === DotUploadType.SHUSHU) {\n        data.name = eventKey;\n    }\n    if (traits) {\n        //通过特性动态劫持插入数据\n        getTraitActiveStatus(data, traits).then(function (v) {\n            if (callback) {\n                callback(data);\n            }\n        });\n    }\n    else {\n        // 正常打点数据\n        if (callback) {\n            callback(data);\n        }\n    }\n}\n/**\n *\n * @param traits\n * @returns\n */\nfunction getTraitActiveStatus(data, traits) {\n    return new Promise(function (c, e) {\n        for (var i_1 = 0; i_1 < traits.length; i_1++) {\n            var traitDotInfo = traits[i_1];\n            var traitClassName = traitDotInfo.class;\n            var trait = (0, DecoratorTrait_1.getOrCreateTraitInstance)(traitClassName);\n            if (trait) {\n                var traitClassInfo = TraitConfigInfo_1.traitConfigInfo.traitsClassNameMap[traitClassName];\n                var props = traitClassInfo === null || traitClassInfo === void 0 ? void 0 : traitClassInfo.param;\n                if (props) {\n                    trait['_id'] = traitClassInfo.id;\n                    trait['_props'] = props;\n                    trait['_active'] = true;\n                    trait['onEnable']();\n                }\n                var active = trait.active;\n                if (active) {\n                    if (traitDotInfo.assign) {\n                        data = Object.assign(data, traitDotInfo.assign(trait, data));\n                    }\n                }\n            }\n        }\n        c(true);\n    });\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Proxy = void 0;\nvar EventManager_1 = require(\"./EventManager\");\nvar EventVo_1 = require(\"./EventVo\");\nvar ModuleManager_1 = require(\"./ModuleManager\");\nvar Proxy = /** @class */ (function () {\n    /**\n     *\n     * @param $module\n     */\n    function Proxy($module) {\n        this._eventsConstructorMaps = new Map();\n        this._module = $module;\n    }\n    Object.defineProperty(Proxy.prototype, \"moduleType\", {\n        /** 模块类型 */\n        get: function () {\n            return this._moduleType;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * 初始化\n     */\n    Proxy.prototype.onInit = function () {\n    };\n    /**\n     * 获取Proxy注册的事件列表\n     * @return 返回Proxy注册的事件集\n     *\n     */\n    Proxy.prototype.registerEvents = function () {\n        return null;\n    };\n    /**获取类*/\n    Proxy.prototype.getClass = function () {\n        return this['constructor'];\n    };\n    /**获取代理所属模块*/\n    Proxy.prototype.getModule = function () {\n        return this._module;\n    };\n    /**\n     * 接收事件 子类重写此类\n     * @param $event\n     *\n     */\n    Proxy.prototype.receivedEvents = function ($event) { };\n    /**启动事件,一个代理中可能有N种事件 */\n    Proxy.prototype.startEvents = function () {\n        var _events = this.registerEvents();\n        if (_events !== null) {\n            for (var i_1 = 0, l = _events.length; i_1 < l; i_1++) {\n                var eventConstructor = _events[i_1];\n                if (!this._eventsConstructorMaps.has(eventConstructor)) {\n                    this._eventsConstructorMaps.set(eventConstructor, true);\n                    var _eventVo = new EventVo_1.EventVo();\n                    _eventVo.proxy = this;\n                    _eventVo.eventClass = eventConstructor;\n                    EventManager_1.EventManager.registerEvent(_eventVo);\n                }\n            }\n        }\n        this.onInit();\n    };\n    /**\n     * 发送事件\n     * @param $event\n     *\n     */\n    Proxy.prototype.dispatchModuleEvent = function ($event) {\n        if (this.moduleType === ModuleManager_1.ModuleType.Common || this.moduleType === ModuleManager_1.ModuleManager.moduleType) {\n            EventManager_1.EventManager.dispatchModuleEvent($event);\n        }\n    };\n    /**\n     * 以 Async 的方式分发事件，支持处理异步事件后的回调\n     * @param event\n     * @returns\n     */\n    Proxy.prototype.dispatchModuleEventAsync = function ($event) {\n        if (this.moduleType === ModuleManager_1.ModuleType.Common || this.moduleType === ModuleManager_1.ModuleManager.moduleType) {\n            return EventManager_1.EventManager.dispatchModuleEventAsync($event);\n        }\n    };\n    return Proxy;\n}());\nexports.Proxy = Proxy;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.interval = interval;\n/**\n * 一些特殊情况下，如果切换了场景，interval 还会继续执行，而如果继续执行，有可能因为回调函数里有对当前节点的引用，会导致报错\n * 这里可以通过 判断该对象是否有效来兼容，取代 cc.tween.delay 这种较重的 api，提升整体性能\n *\n * 注意：如果单纯想不受场景销毁影响，不要用这个，使用原生 setInterval 即可\n * @param node\n * @param callback\n * @param timeout\n * @param args\n */\nfunction interval(node, callback, timeout) {\n    var args = [];\n    for (var _i = 3; _i < arguments.length; _i++) {\n        args[_i - 3] = arguments[_i];\n    }\n    var id = setInterval(function () {\n        if (!node || !cc.isValid(node, true)) {\n            clearInterval(id);\n            return;\n        }\n        callback === null || callback === void 0 ? void 0 : callback.apply(this, [args]);\n    }, timeout, args);\n    return id;\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.gameStateManager = void 0;\nvar EventManager_1 = require(\"../../falcon/EventManager\");\nvar ModuleManager_1 = require(\"../../falcon/ModuleManager\");\nvar PrefabInfo_1 = require(\"../../modules/prefab/vo/PrefabInfo\");\nvar CacheRender_1 = require(\"../cache/CacheRender\");\nvar CacheComponents_1 = require(\"../components/CacheComponents\");\nvar GameLayer_1 = require(\"../layer/GameLayer\");\nvar ResLoader_1 = require(\"../loader/ResLoader\");\nvar UI_1 = require(\"../ui/UI\");\n/**\n * 游戏状态重置管理器\n * 用于管理子游戏模式下的状态重置和重新初始化\n */\nvar GameStateManager = /** @class */ (function () {\n    function GameStateManager() {\n        this._resettableObjects = new Map();\n        this._initialized = false;\n    }\n    Object.defineProperty(GameStateManager, \"instance\", {\n        get: function () {\n            if (!GameStateManager._instance) {\n                GameStateManager._instance = new GameStateManager();\n            }\n            return GameStateManager._instance;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * 注册可重置的对象\n     * @param key 唯一标识符\n     * @param resettable 可重置的对象\n     */\n    GameStateManager.prototype.registerResettable = function (key, resettable) {\n        this._resettableObjects.set(key, resettable);\n        if (CC_DEBUG) {\n            console.log(\"[GameStateManager] \\u6CE8\\u518C\\u53EF\\u91CD\\u7F6E\\u5BF9\\u8C61: \".concat(key));\n        }\n    };\n    /**\n     * 注销可重置对象\n     * @param key 唯一标识符\n     */\n    GameStateManager.prototype.unregisterResettable = function (key) {\n        this._resettableObjects.delete(key);\n        if (CC_DEBUG) {\n            console.log(\"[GameStateManager] \\u6CE8\\u9500\\u53EF\\u91CD\\u7F6E\\u5BF9\\u8C61: \".concat(key));\n        }\n    };\n    /**\n     * 游戏退出时的清理逻辑\n     */\n    GameStateManager.prototype.onGameExit = function () {\n        if (CC_DEBUG) {\n            console.log('[GameStateManager] 开始游戏退出清理...');\n        }\n        // 1. 清理UI缓存\n        this.clearUICache();\n        // 2. 清理层级管理器\n        this.clearLayerManager();\n        // 3. 清理所有注册的可重置对象\n        this.resetAllObjects();\n        // 4. 清理特性系统\n        // this.clearTraitSystem();\n        // 5. 清理模块系统\n        this.clearModuleSystem();\n        // 6. 清理资源加载器\n        this.clearResourceLoader();\n        // 7. 清理缓存渲染器\n        this.clearCacheRender();\n        // 8. 清理组件缓存系统\n        this.clearCacheComponents();\n        // 9. 清理预制体缓存\n        this.clearPrefabCache();\n        // 10. 清理事件管理器\n        this.clearEventManager();\n        if (CC_DEBUG) {\n            console.log('[GameStateManager] 游戏退出清理完成');\n        }\n    };\n    /**\n     * 游戏重新进入时的初始化逻辑\n     */\n    GameStateManager.prototype.onGameEnter = function () {\n        if (CC_DEBUG) {\n            console.log('[GameStateManager] 开始游戏重新进入初始化...');\n        }\n        // 1. 重新初始化所有注册的可重置对象\n        this.reinitializeAllObjects();\n        // 2. 标记为已初始化\n        this._initialized = true;\n        if (CC_DEBUG) {\n            console.log('[GameStateManager] 游戏重新进入初始化完成');\n        }\n    };\n    /**\n     * 清理UI缓存\n     */\n    GameStateManager.prototype.clearUICache = function () {\n        try {\n            // 使用UI类的公共清理方法\n            UI_1.UI.clearAllCache();\n            if (CC_DEBUG) {\n                console.log('[GameStateManager] UI缓存已清理');\n            }\n        }\n        catch (error) {\n            console.error('[GameStateManager] 清理UI缓存时发生错误:', error);\n        }\n    };\n    /**\n     * 清理层级管理器\n     */\n    GameStateManager.prototype.clearLayerManager = function () {\n        try {\n            GameLayer_1.layerManager.clear();\n            (0, GameLayer_1.resetGameUiLayer)();\n            if (CC_DEBUG) {\n                console.log('[GameStateManager] 层级管理器已清理');\n            }\n        }\n        catch (error) {\n            console.error('[GameStateManager] 清理层级管理器时发生错误:', error);\n        }\n    };\n    /**\n     * 重置所有注册的对象\n     */\n    GameStateManager.prototype.resetAllObjects = function () {\n        this._resettableObjects.forEach(function (resettable, key) {\n            try {\n                resettable.reset();\n                if (CC_DEBUG) {\n                    console.log(\"[GameStateManager] \\u5DF2\\u91CD\\u7F6E\\u5BF9\\u8C61: \".concat(key));\n                }\n            }\n            catch (error) {\n                console.error(\"[GameStateManager] \\u91CD\\u7F6E\\u5BF9\\u8C61 \".concat(key, \" \\u65F6\\u53D1\\u751F\\u9519\\u8BEF:\"), error);\n            }\n        });\n    };\n    /**\n     * 重新初始化所有注册的对象\n     */\n    GameStateManager.prototype.reinitializeAllObjects = function () {\n        this._resettableObjects.forEach(function (resettable, key) {\n            try {\n                if (resettable.reinitialize) {\n                    resettable.reinitialize();\n                    if (CC_DEBUG) {\n                        console.log(\"[GameStateManager] \\u5DF2\\u91CD\\u65B0\\u521D\\u59CB\\u5316\\u5BF9\\u8C61: \".concat(key));\n                    }\n                }\n            }\n            catch (error) {\n                console.error(\"[GameStateManager] \\u91CD\\u65B0\\u521D\\u59CB\\u5316\\u5BF9\\u8C61 \".concat(key, \" \\u65F6\\u53D1\\u751F\\u9519\\u8BEF:\"), error);\n            }\n        });\n    };\n    /**\n     * 清理特性系统\n     */\n    GameStateManager.prototype.clearTraitSystem = function () {\n        try {\n            // 清理特性实例缓存\n            var traitMaps = window.__traitMaps__;\n            if (traitMaps && traitMaps.clear) {\n                traitMaps.clear();\n            }\n            // 清理其他特性相关状态\n            var shareObjects = window.__shareObjects__;\n            if (shareObjects && shareObjects.clear) {\n                shareObjects.clear();\n            }\n            if (CC_DEBUG) {\n                console.log('[GameStateManager] 特性系统已清理');\n            }\n        }\n        catch (error) {\n            console.error('[GameStateManager] 清理特性系统时发生错误:', error);\n        }\n    };\n    /**\n     * 清理模块系统\n     */\n    GameStateManager.prototype.clearModuleSystem = function () {\n        try {\n            // 使用ModuleManager的重置方法\n            if (ModuleManager_1.ModuleManager && typeof ModuleManager_1.ModuleManager.reset === 'function') {\n                ModuleManager_1.ModuleManager.reset();\n            }\n            if (CC_DEBUG) {\n                console.log('[GameStateManager] 模块系统已清理');\n            }\n        }\n        catch (error) {\n            console.error('[GameStateManager] 清理模块系统时发生错误:', error);\n        }\n    };\n    /**\n     * 清理资源加载器缓存\n     */\n    GameStateManager.prototype.clearResourceLoader = function () {\n        try {\n            // 动态导入ResLoader并调用reset方法\n            if (ResLoader_1.ResLoader && typeof ResLoader_1.ResLoader.reset === 'function') {\n                ResLoader_1.ResLoader.reset();\n            }\n            if (CC_DEBUG) {\n                console.log('[GameStateManager] 资源加载器已清理');\n            }\n        }\n        catch (error) {\n            console.error('[GameStateManager] 清理资源加载器时发生错误:', error);\n        }\n    };\n    /**\n     * 清理缓存渲染器\n     */\n    GameStateManager.prototype.clearCacheRender = function () {\n        try {\n            // 动态导入cacheRender并调用reset方法\n            if (CacheRender_1.cacheRender && typeof CacheRender_1.cacheRender.reset === 'function') {\n                CacheRender_1.cacheRender.reset();\n            }\n            if (CC_DEBUG) {\n                console.log('[GameStateManager] 缓存渲染器已清理');\n            }\n        }\n        catch (error) {\n            console.error('[GameStateManager] 清理缓存渲染器时发生错误:', error);\n        }\n    };\n    /**\n     * 清理组件缓存系统\n     */\n    GameStateManager.prototype.clearCacheComponents = function () {\n        try {\n            // 动态导入cacheComponents并调用reset方法\n            if (CacheComponents_1.cacheComponents && typeof CacheComponents_1.cacheComponents.reset === 'function') {\n                CacheComponents_1.cacheComponents.reset();\n            }\n            if (CC_DEBUG) {\n                console.log('[GameStateManager] 组件缓存系统已清理');\n            }\n        }\n        catch (error) {\n            console.error('[GameStateManager] 清理组件缓存系统时发生错误:', error);\n        }\n    };\n    /**\n     * 清理预制体缓存\n     */\n    GameStateManager.prototype.clearPrefabCache = function () {\n        PrefabInfo_1.prefabInfo.clearAllCache();\n    };\n    /**\n     * 清理事件管理器\n     */\n    GameStateManager.prototype.clearEventManager = function () {\n        // 重置事件管理器\n        try {\n            if (EventManager_1.EventManager && typeof EventManager_1.EventManager.reset === 'function') {\n                EventManager_1.EventManager.reset();\n            }\n        }\n        catch (error) {\n            console.error('[ModuleManager] 重置事件管理器时发生错误:', error);\n        }\n        if (CC_DEBUG) {\n            console.log('[ModuleManager] 模块管理器已重置');\n        }\n    };\n    Object.defineProperty(GameStateManager.prototype, \"initialized\", {\n        /**\n         * 获取初始化状态\n         */\n        get: function () {\n            return this._initialized;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * 强制重置初始化状态\n     */\n    GameStateManager.prototype.forceReset = function () {\n        this._initialized = false;\n        this.onGameExit();\n    };\n    return GameStateManager;\n}());\n// 导出单例实例\nexports.gameStateManager = GameStateManager.instance;\n// 将GameStateManager挂载到全局，方便调试和外部调用\nif (CC_DEBUG) {\n    window.gameStateManager = exports.gameStateManager;\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.http = exports.CrytoType = exports.HttpType = void 0;\nvar HttpType;\n(function (HttpType) {\n    HttpType[\"GET\"] = \"GET\";\n    HttpType[\"POST\"] = \"POST\";\n})(HttpType || (exports.HttpType = HttpType = {}));\n/** 加密类型 */\nvar CrytoType;\n(function (CrytoType) {\n    CrytoType[\"Url\"] = \"Url\";\n})(CrytoType || (exports.CrytoType = CrytoType = {}));\nvar Http = /** @class */ (function () {\n    function Http() {\n        this.callbackId = -1;\n        this.callbacks = {};\n    }\n    /**\n     * 请求（promise)\n     * @param url 请求地址\n     * @param data 请求数据（GET 与 POST 格式都为对象）\n     * @param option 请求选项\n     * @returns\n     */\n    Http.prototype.requestAsync = function (url, data, option) {\n        var _this = this;\n        return new Promise(function (c, e) {\n            _this.request(url, data, function (data) {\n                c(data);\n            }, function (err) {\n                e(err);\n            }, option);\n        });\n    };\n    /**\n     * 请求\n     * @param url 请求地址\n     * @param data 请求数据（GET 与 POST 格式都为对象）\n     * @param success 请求成功后\n     * @param fail 请求失败\n     * @param option 请求选项\n     */\n    Http.prototype.request = function (url, data, success, fail, option) {\n        // if (!url) {\n        //     fail(\"url is empty\");\n        //     return;\n        // }\n        // const paramKeys = Object.keys(data);\n        // let fullUrl = url;\n        // if (paramKeys.length > 0) {\n        //     let paramStr = '';\n        //     if (data) {\n        //         paramStr += `?${paramKeys.reduce((p, c) => p + '&' + c + '=' + data[c], '').slice(1)}`;\n        //         fullUrl = url + paramStr;\n        //     }\n        // }\n        // this.callbackId++;\n        // this.callbacks[this.callbackId] = { url: fullUrl, startTime: Date.now() };\n        // if (CC_DEBUG) {\n        //     console.log('%c[C->S]', \"color:#fff;background:#fa5151;\", this.callbackId, fullUrl, data);\n        // }\n        // let contentType = \"application/json\";\n        // if (option?.contentType) {\n        //     contentType = option.contentType;\n        // }\n        // let method = \"GET\";\n        // if (option?.type) {\n        //     method = option.type;\n        // }\n        // const headers = {\n        //     'Content-Type': contentType,\n        //     'CallbackId': this.callbackId.toString()\n        // };\n        // // try {\n        // //     var xhr = cc.loader.getXMLHttpRequest();\n        // //     xhr.open(\"POST\", fullUrl, true);\n        // //     // xhr.setRequestHeader(\"Content-Type\", localConTentTypeStr);\n        // //     xhr.setRequestHeader('Content-Type', contentType);\n        // //     xhr.setRequestHeader('CallbackId', this.callbackId.toString());\n        // //     xhr.onreadystatechange = function (resp) {\n        // //         if (xhr.readyState === 4) {\n        // //             let responseText = xhr.responseText;\n        // //             if (xhr.status >= 200 && xhr.status < 300) {\n        // //                 const response = JSON.parse(responseText);\n        // //                 success?.(response as Resp);\n        // //             }\n        // //         }\n        // //     };\n        // //     xhr.send(JSON.stringify(data));\n        // // } catch (error) {\n        // //     fail?.(error);\n        // // }\n        // var xhr = new XMLHttpRequest(), errInfo = 'download failed: ' + url + ', status: ';\n        // xhr.open(method, url, true);\n        // // xhr.withCredentials = false;\n        // // if (options.responseType !== undefined) xhr.responseType = options.responseType;\n        // // if (options.withCredentials !== undefined) xhr.withCredentials = options.withCredentials;\n        // // if (options.mimeType !== undefined && xhr.overrideMimeType) xhr.overrideMimeType(options.mimeType);\n        // // if (options.timeout !== undefined) xhr.timeout = options.timeout;\n        // if (headers) {\n        //     for (var header in headers) {\n        //         xhr.setRequestHeader(header, headers[header]);\n        //     }\n        // }\n        // xhr.onload = function () {\n        //     if (xhr.status === 200 || xhr.status === 0) {\n        //         let responseText = xhr.responseText;\n        //         if (xhr.status >= 200 && xhr.status < 300) {\n        //             const response = JSON.parse(responseText);\n        //             success?.(response as Resp);\n        //         }\n        //     } else {\n        //         // onComplete && onComplete(new Error(errInfo + xhr.status + '(no response)'));\n        //         fail?.(errInfo + xhr.status + '(no response)');\n        //     }\n        // };\n        // // if (onProgress) {\n        // //     xhr.onprogress = function (e) {\n        // //         if (e.lengthComputable) {\n        // //             onProgress(e.loaded, e.total);\n        // //         }\n        // //     };\n        // // }\n        // xhr.onerror = function () {\n        //     // onComplete && onComplete(new Error(errInfo + xhr.status + '(error)'));\n        //     fail?.(errInfo + xhr.status + '(no error)');\n        // };\n        // xhr.ontimeout = function () {\n        //     // onComplete && onComplete(new Error(errInfo + xhr.status + '(time out)'));\n        //     fail?.(errInfo + xhr.status + '(time out)');\n        // };\n        // xhr.onabort = function () {\n        //     // onComplete && onComplete(new Error(errInfo + xhr.status + '(abort)'));\n        //     fail?.(errInfo + xhr.status + '(abort)');\n        // };\n        // xhr.send(JSON.stringify(data));\n        // // window['axios']({\n        // //     method,\n        // //     url: fullUrl,\n        // //     // baseURL: apiHost,\n        // //     data: method === 'POST' ? data : null,\n        // //     params: method === 'GET' ? data : null,\n        // //     withCredentials: false,\n        // //     headers\n        // // }).then(response => {\n        // //     const { headers: responseHeaders } = response.config;\n        // //     const responseCallbackId = responseHeaders.CallbackId;\n        // //     const callbackInfo = this.callbacks[responseCallbackId];\n        // //     if (CC_DEBUG) {\n        // //         console.log(`%c[S->C]`, \"color:#ffffff;background:#4b973d;\", responseCallbackId, `${Date.now() - callbackInfo.startTime}ms`, callbackInfo.url, response.data, JSON.stringify(response.data));\n        // //     }\n        // //     success?.(response.data);\n        // // }).catch(err => {\n        // //     fail?.(err);\n        // // });\n        var method = \"GET\";\n        if (option === null || option === void 0 ? void 0 : option.type) {\n            method = option.type;\n        }\n        var xhr = cc.loader.getXMLHttpRequest();\n        // 将参数转换为 JSON 字符串\n        var dataStr = JSON.stringify(data);\n        xhr.open(method, url, true);\n        var contentType = \"application/json\";\n        if (option === null || option === void 0 ? void 0 : option.contentType) {\n            contentType = option.contentType;\n        }\n        // 设置请求头为 application/json\n        xhr.setRequestHeader(\"Content-Type\", contentType);\n        xhr.onreadystatechange = function () {\n            if (xhr.readyState === 4) {\n                var response = xhr.responseText;\n                if (xhr.status >= 200 && xhr.status < 300) {\n                    // 响应解密\n                    if (option.crypto) {\n                        response = option.crypto['decrypt'](response);\n                    }\n                    success(JSON.parse(response));\n                }\n                else {\n                    fail(response);\n                }\n            }\n        };\n        // 参数加密\n        if (option.crypto) {\n            dataStr = \"params=\" + option.crypto['encrypt'](dataStr);\n        }\n        // 发送 JSON 数据\n        xhr.send(dataStr);\n    };\n    return Http;\n}());\nexports.http = new Http();\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Limiter = void 0;\nvar Events_1 = require(\"../events/Events\");\n/**\n * 用于控制并发任务数量的工具类。\n * 它的主要功能是将多个异步任务排队，并确保在任何时刻同时运行的任务数量不超过指定的最大并发度（maxDegreeOfParallelism）\n */\nvar Limiter = /** @class */ (function () {\n    function Limiter(maxDegreeOfParalellism) {\n        this._size = 0;\n        this._isDisposed = false;\n        this.maxDegreeOfParalellism = maxDegreeOfParalellism;\n        this.outstandingPromises = [];\n        this.runningPromises = 0;\n        this._onDrained = new Events_1.Emitter();\n    }\n    Object.defineProperty(Limiter.prototype, \"onDrained\", {\n        get: function () {\n            return this._onDrained.event;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Limiter.prototype, \"size\", {\n        get: function () {\n            return this._size;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Limiter.prototype.queue = function (factory) {\n        var _this = this;\n        if (this._isDisposed) {\n            throw new Error('Object has been disposed');\n        }\n        this._size++;\n        return new Promise(function (c, e) {\n            _this.outstandingPromises.push({ factory: factory, c: c, e: e });\n            _this.consume();\n        });\n    };\n    Limiter.prototype.consume = function () {\n        var _this = this;\n        while (this.outstandingPromises.length && this.runningPromises < this.maxDegreeOfParalellism) {\n            var iLimitedTask = this.outstandingPromises.shift();\n            this.runningPromises++;\n            var promise = iLimitedTask.factory();\n            promise.then(iLimitedTask.c, iLimitedTask.e);\n            promise.then(function () { return _this.consumed(); }, function () { return _this.consumed(); });\n        }\n    };\n    Limiter.prototype.consumed = function () {\n        if (this._isDisposed) {\n            return;\n        }\n        this.runningPromises--;\n        if (--this._size === 0) {\n            this._onDrained.fire();\n        }\n        if (this.outstandingPromises.length > 0) {\n            this.consume();\n        }\n    };\n    Limiter.prototype.clear = function () {\n        if (this._isDisposed) {\n            throw new Error('Object has been disposed');\n        }\n        this.outstandingPromises.length = 0;\n        this._size = this.runningPromises;\n    };\n    Limiter.prototype.dispose = function () {\n        this._isDisposed = true;\n        this.outstandingPromises.length = 0;\n        this._size = 0;\n        this._onDrained.dispose();\n    };\n    return Limiter;\n}());\nexports.Limiter = Limiter;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ResLoader = exports.BundleName = void 0;\n/**bundle类型 */\nvar BundleName;\n(function (BundleName) {\n    BundleName[\"class\"] = \"class\";\n    BundleName[\"chapter\"] = \"chapter\";\n    BundleName[\"mainTraits\"] = \"mainTraits\";\n    BundleName[\"tool\"] = \"tool\";\n})(BundleName || (exports.BundleName = BundleName = {}));\nvar ResLoader = /** @class */ (function () {\n    function ResLoader() {\n    }\n    /**\n * 解析 bundle 名称和版本信息\n * @param bundleNameType bundle 名称或配置对象\n * @returns 解析后的 bundleName 和 version\n */\n    ResLoader.parseBundleConfig = function (bundleNameType) {\n        if (typeof bundleNameType === 'string') {\n            return { bundleName: bundleNameType };\n        }\n        else if (typeof bundleNameType === 'object' && bundleNameType !== null && 'bundleName' in bundleNameType) {\n            return {\n                bundleName: bundleNameType.bundleName,\n                version: bundleNameType.version\n            };\n        }\n        else {\n            // 如果是枚举值，转换为字符串\n            return { bundleName: String(bundleNameType) };\n        }\n    };\n    /**\n     * 生成缓存键\n     * @param bundleNameType bundle 名称类型\n     * @param path 资源路径（可选）\n     * @returns 缓存键\n     */\n    ResLoader.generateCacheKey = function (bundleNameType, path) {\n        var _a = this.parseBundleConfig(bundleNameType), bundleName = _a.bundleName, version = _a.version;\n        var baseKey = path ? \"\".concat(bundleName, \"_\").concat(path) : bundleName;\n        return version ? \"\".concat(baseKey, \"_\").concat(version) : baseKey;\n    };\n    /**\n     * 添加资源事件监听\n     * @param event\n     * @param callback\n     */\n    ResLoader.addEventListener = function (event, callback) {\n        if (!this.events[event]) {\n            this.events[event] = [];\n        }\n        this.events[event].push(callback);\n    };\n    /**\n     * 清理所有事件监听器\n     */\n    ResLoader.clearEventListeners = function () {\n        for (var event_1 in this.events) {\n            this.events[event_1] = [];\n        }\n        this.events = {};\n        if (CC_DEBUG) {\n            console.log('[ResLoader] 所有事件监听器已清理');\n        }\n    };\n    /**\n     * 清理资源缓存\n     * 释放所有缓存的资源和回调函数\n     */\n    ResLoader.clearResourceCache = function () {\n        // 清理回调函数数组，防止内存泄漏\n        for (var key in this.cacheResources) {\n            var cache = this.cacheResources[key];\n            if (cache.onCompletes) {\n                cache.onCompletes.length = 0;\n            }\n            // 不主动释放 asset，让 Cocos 的资源管理系统处理\n            // 但清除我们的引用\n            delete cache.asset;\n        }\n        // 清空缓存对象\n        this.cacheResources = {};\n        if (CC_DEBUG) {\n            console.log('[ResLoader] 资源缓存已清理');\n        }\n    };\n    /**\n     * 完全重置 ResLoader 状态\n     * 清理所有缓存和事件监听器\n     */\n    ResLoader.reset = function () {\n        this.clearResourceCache();\n        this.clearEventListeners();\n        if (CC_DEBUG) {\n            console.log('[ResLoader] ResLoader 已完全重置');\n        }\n    };\n    /**\n     * 是否为远程地址，通过 http(s)://前缀来判断\n     * @param url\n     * @returns\n     */\n    ResLoader.isRemote = function (url) {\n        return /^(https?:\\/\\/)/.test(url);\n    };\n    ResLoader.getAsset = function (url) {\n        var _a;\n        return (_a = this.cacheResources[url]) === null || _a === void 0 ? void 0 : _a.asset;\n    };\n    /**\n     * 高效加载资源:\n     * （1）：多请求完成后分发\n     * （2）：支持远程支持加载（web 端可能产生跨域问题）\n     * （3）：远程资源不需要传 type\n     * （4）：目前远程支持暂时不支持重试次数\n     * @param paths\n     * @param type\n     * @param onComplete\n     */\n    ResLoader.load = function (paths, type, onComplete) {\n        if (!this.cacheResources[paths]) {\n            this.cacheResources[paths] = { onCompletes: onComplete ? [onComplete] : [] };\n            var self_1 = this;\n            var _complete = function (err, asset) {\n                if (err) {\n                    cc.error(\"\\u8D44\\u6E90\\u52A0\\u8F7D\\u53D1\\u751F\\u9519\\u8BEF\\uFF1A\", err);\n                }\n                self_1.cacheResources[paths].asset = asset;\n                var onCompletes = self_1.cacheResources[paths].onCompletes;\n                for (var i_1 = 0; i_1 < (onCompletes === null || onCompletes === void 0 ? void 0 : onCompletes.length); i_1++) {\n                    var onCompleteItem = onCompletes[i_1];\n                    onCompleteItem.apply(this, [err, asset]);\n                }\n                self_1.cacheResources[paths].onCompletes.length = 0;\n                var loadEvents = self_1.events[\"load\"];\n                for (var i_2 = 0; i_2 < (loadEvents === null || loadEvents === void 0 ? void 0 : loadEvents.length); i_2++) {\n                    var event_2 = loadEvents[i_2];\n                    event_2.apply(self_1, [\"resources\", paths]);\n                }\n            };\n            if (!this.isRemote(paths)) {\n                cc.resources.load(paths, type, _complete);\n            }\n            else {\n                cc.assetManager.loadRemote(paths, _complete);\n            }\n        }\n        else {\n            if (this.cacheResources[paths].asset) {\n                if (this.cacheResources[paths].asset.isValid) {\n                    if (onComplete) {\n                        onComplete.apply(this, [null, this.cacheResources[paths].asset]);\n                    }\n                }\n                else {\n                    delete this.cacheResources[paths];\n                    this.load(paths, type, onComplete);\n                }\n            }\n            else {\n                if (onComplete) {\n                    this.cacheResources[paths].onCompletes.push(onComplete);\n                }\n            }\n        }\n    };\n    /**\n     * 通过 promise 方式进行资源加载\n     * @param paths\n     * @param type\n     * @returns\n     */\n    ResLoader.asyncLoad = function (paths, type) {\n        var _this = this;\n        return new Promise(function (c, e) {\n            _this.load(paths, type, function (error, assets) {\n                if (error) {\n                    e(error);\n                    return;\n                }\n                c(assets);\n            });\n        });\n    };\n    /**\n     * 根据 bunlde 加载资源（支持版本配置）\n     * @param bundleNameType bundle 名称（字符串）或配置对象 {bundleName: string, version: string}\n     * @param paths 资源路径\n     * @param type 资源类型\n     * @param onComplete 完成回调\n     */\n    ResLoader.loadByBundle = function (bundleNameType, paths, type, onComplete) {\n        var _this = this;\n        var _a = this.parseBundleConfig(bundleNameType), bundleName = _a.bundleName, version = _a.version;\n        this.loadBundle(bundleNameType, function (err, bundle) {\n            if (err) {\n                console.error(\"bundle:\".concat(bundleName, \" \\u8D44\\u6E90\\u52A0\\u8F7D\\u9519\\u8BEF\\uFF1A\"), err);\n                if (onComplete) {\n                    onComplete.apply(_this, [err, null]);\n                }\n                return;\n            }\n            var key = _this.generateCacheKey(bundleNameType, paths);\n            if (!_this.cacheResources[key]) {\n                _this.cacheResources[key] = {\n                    onCompletes: onComplete ? [onComplete] : []\n                };\n                bundle.load(paths, type, function (err, res) {\n                    if (_this.cacheResources[key]) {\n                        _this.cacheResources[key].asset = res;\n                        var onCompletes = _this.cacheResources[key].onCompletes;\n                        // 清空回调数组并执行所有回调\n                        _this.cacheResources[key].onCompletes = [];\n                        for (var i_3 = 0; i_3 < (onCompletes === null || onCompletes === void 0 ? void 0 : onCompletes.length); i_3++) {\n                            var onCompleteItem = onCompletes[i_3];\n                            onCompleteItem.apply(_this, [err, res]);\n                        }\n                        // 触发加载事件\n                        var loadEvents = _this.events[\"load\"];\n                        for (var i_4 = 0; i_4 < (loadEvents === null || loadEvents === void 0 ? void 0 : loadEvents.length); i_4++) {\n                            var event_3 = loadEvents[i_4];\n                            event_3.apply(_this, [bundleName, paths]);\n                        }\n                    }\n                });\n            }\n            else {\n                if (_this.cacheResources[key].asset) {\n                    if (_this.cacheResources[key].asset.isValid) {\n                        if (onComplete) {\n                            onComplete.apply(_this, [null, _this.cacheResources[key].asset]);\n                        }\n                    }\n                    else {\n                        // 资源无效，重新加载\n                        delete _this.cacheResources[key];\n                        _this.loadByBundle(bundleNameType, paths, type, onComplete);\n                    }\n                }\n                else {\n                    // 正在加载中，添加到回调队列\n                    if (onComplete) {\n                        _this.cacheResources[key].onCompletes.push(onComplete);\n                    }\n                }\n            }\n        });\n    };\n    /**\n     * 通过 promise 方式并基于 bundle 加载资源（支持版本配置）\n     * @param bundleNameType bundle 名称（字符串）或配置对象 {bundleName: string, version: string}\n     * @param paths 资源路径\n     * @param type 资源类型\n     * @returns Promise<T>\n     */\n    ResLoader.asyncLoadByBundle = function (bundleNameType, paths, type) {\n        var _this = this;\n        return new Promise(function (c, e) {\n            _this.loadByBundle(bundleNameType, paths, type, function (err, asset) {\n                if (err) {\n                    e(err);\n                    return;\n                }\n                c(asset);\n            });\n        });\n    };\n    /**\n     * 高性能，在未加载完成时，多次请求加载，不会触发多次加载（支持版本配置）\n     * @param bundleNameType bundle 名称（字符串）或配置对象 {bundleName: string, version: string}\n     * @param onComplete 完成回调\n     */\n    ResLoader.loadBundle = function (bundleNameType, onComplete) {\n        var self = this;\n        var _a = this.parseBundleConfig(bundleNameType), bundleName = _a.bundleName, version = _a.version;\n        var cacheKey = this.generateCacheKey(bundleNameType);\n        if (!this.cacheResources[cacheKey]) {\n            this.cacheResources[cacheKey] = {\n                onCompletes: onComplete ? [onComplete] : []\n            };\n            // 根据是否有版本信息决定调用方式\n            if (version) {\n                cc.assetManager.loadBundle(bundleName, { version: version }, function (err, bundle) {\n                    if (self.cacheResources[cacheKey]) {\n                        self.cacheResources[cacheKey].asset = bundle;\n                        var onCompletes = self.cacheResources[cacheKey].onCompletes;\n                        // 清空回调数组并执行所有回调\n                        self.cacheResources[cacheKey].onCompletes = [];\n                        for (var i_5 = 0; i_5 < (onCompletes === null || onCompletes === void 0 ? void 0 : onCompletes.length); i_5++) {\n                            var onCompleteItem = onCompletes[i_5];\n                            onCompleteItem.apply(this, [err, bundle]);\n                        }\n                    }\n                });\n            }\n            else {\n                cc.assetManager.loadBundle(bundleName, function (err, bundle) {\n                    if (self.cacheResources[cacheKey]) {\n                        self.cacheResources[cacheKey].asset = bundle;\n                        var onCompletes = self.cacheResources[cacheKey].onCompletes;\n                        // 清空回调数组并执行所有回调\n                        self.cacheResources[cacheKey].onCompletes = [];\n                        for (var i_6 = 0; i_6 < (onCompletes === null || onCompletes === void 0 ? void 0 : onCompletes.length); i_6++) {\n                            var onCompleteItem = onCompletes[i_6];\n                            onCompleteItem.apply(this, [err, bundle]);\n                        }\n                    }\n                });\n            }\n        }\n        else {\n            if (this.cacheResources[cacheKey].asset) {\n                if (onComplete) {\n                    onComplete.apply(this, [null, this.cacheResources[cacheKey].asset]);\n                }\n            }\n            else {\n                // 正在加载中，添加到回调队列\n                if (onComplete) {\n                    this.cacheResources[cacheKey].onCompletes.push(onComplete);\n                }\n            }\n        }\n    };\n    /**\n     * 通过 bundle 预加载场景\n     * @param bundle\n     * @param sceneName\n     * @param onComplete\n     */\n    ResLoader.bundlePreloadScene = function (bundle, sceneName, onComplete) {\n        var self = this;\n        var key = \"preloadScene\" + sceneName;\n        if (!this.cacheResources[key]) {\n            this.cacheResources[key] = { onCompletes: onComplete ? [onComplete] : [] };\n            bundle.preloadScene(sceneName, function (err) {\n                var onCompletes = self.cacheResources[key].onCompletes;\n                self.cacheResources[key]['__complete__'] = true;\n                for (var i_7 = 0; i_7 < (onCompletes === null || onCompletes === void 0 ? void 0 : onCompletes.length); i_7++) {\n                    var onCompleteItem = onCompletes[i_7];\n                    onCompleteItem.apply(this, [err]);\n                }\n                self.cacheResources[key].onCompletes.length = 0;\n                delete self.cacheResources[key]['__complete__'];\n            });\n        }\n        else {\n            if (self.cacheResources[key]['__complete__']) {\n                if (onComplete) {\n                    onComplete.apply(this, [null]);\n                }\n            }\n            else {\n                if (onComplete) {\n                    this.cacheResources[key].onCompletes.push(onComplete);\n                }\n            }\n        }\n    };\n    /**\n     * 通过 bundle 预加载场景\n     * @param bundle\n     * @param sceneName\n     * @param onComplete\n     */\n    ResLoader.bundlePreload = function (bundle, paths, type, onComplete) {\n        var self = this;\n        var key = \"preload\" + paths;\n        if (!this.cacheResources[key]) {\n            this.cacheResources[key] = { onCompletes: onComplete ? [onComplete] : [] };\n            bundle.preload(paths, type, function (error, items) {\n                var onCompletes = self.cacheResources[key].onCompletes;\n                self.cacheResources[key]['__items__'] = items;\n                for (var i_8 = 0; i_8 < (onCompletes === null || onCompletes === void 0 ? void 0 : onCompletes.length); i_8++) {\n                    var onCompleteItem = onCompletes[i_8];\n                    onCompleteItem.apply(this, [error, items]);\n                }\n                self.cacheResources[key].onCompletes.length = 0;\n            });\n        }\n        else {\n            if (self.cacheResources[key]['__items__']) {\n                if (onComplete) {\n                    onComplete.apply(this, [null, self.cacheResources[key]['__items__']]);\n                }\n            }\n            else {\n                if (onComplete) {\n                    this.cacheResources[key].onCompletes.push(onComplete);\n                }\n            }\n        }\n    };\n    /**\n     * 渲染 sprite\n     * @param sprite\n     * @param url\n     */\n    ResLoader.renderSprite = function (sprite, url) {\n        this.load(url, cc.SpriteFrame, function (err, asset) {\n            if (err) {\n                return;\n            }\n            sprite.spriteFrame = asset;\n        });\n    };\n    /**\n     * 渲染 sprite （bundle 版本）\n     * @param sprite\n     * @param url\n     */\n    ResLoader.renderSpriteByBundle = function (sprite, url, bundleNameType) {\n        this.loadByBundle(bundleNameType, url, cc.SpriteFrame, function (err, asset) {\n            if (err) {\n                return;\n            }\n            sprite.spriteFrame = asset;\n        });\n    };\n    /**\n     * 渲染龙骨动画\n     * @param dragonBonesArmatureDisplay\n     * @param dragonAssetUrl\n     * @param dragonAssetAtlasUrl\n     * @param armatureName\n     * @param animName\n     * @param playtimes\n     */\n    ResLoader.renderDragonbones = function (dragonBonesArmatureDisplay, dragonAssetUrl, dragonAssetAtlasUrl, armatureName, animName, playtimes) {\n        var _this = this;\n        if (playtimes === void 0) { playtimes = 1; }\n        var loadCount = 0;\n        var play = function () {\n            if (loadCount === 2) {\n                dragonBonesArmatureDisplay.addEventListener(dragonBones.EventObject.COMPLETE, function (e) {\n                    var _a;\n                    if (CC_DEBUG) {\n                        console.log(\"renderDragonbones 动画播放结束,url\", dragonAssetUrl);\n                    }\n                    if ((_a = dragonBonesArmatureDisplay.node) === null || _a === void 0 ? void 0 : _a.parent) {\n                        dragonBonesArmatureDisplay.node.parent.removeChild(dragonBonesArmatureDisplay.node);\n                    }\n                }, _this);\n                dragonBonesArmatureDisplay.armatureName = armatureName;\n                dragonBonesArmatureDisplay.playAnimation(animName, playtimes);\n            }\n        };\n        this.load(dragonAssetUrl, dragonBones.DragonBonesAsset, function (err, asset) {\n            if (err) {\n                return;\n            }\n            dragonBonesArmatureDisplay.dragonAsset = asset;\n            loadCount++;\n            play();\n        });\n        this.load(dragonAssetAtlasUrl, dragonBones.DragonBonesAtlasAsset, function (err, asset) {\n            if (err) {\n                return;\n            }\n            dragonBonesArmatureDisplay.dragonAtlasAsset = asset;\n            loadCount++;\n            play();\n        });\n    };\n    /**\n    * 渲染bundle下龙骨动画\n    * @param bundleName\n    * @param dragonBonesArmatureDisplay\n    * @param dragonAssetUrl\n    * @param dragonAssetAtlasUrl\n    * @param armatureName\n    * @param animName\n    * @param playtimes\n    * @param completeRemove\n    */\n    ResLoader.renderDragonbonesByBundle = function (bundleName, dragonBonesArmatureDisplay, dragonAssetUrl, dragonAssetAtlasUrl, armatureName, animName, playtimes, completeRemove) {\n        var _this = this;\n        if (playtimes === void 0) { playtimes = 1; }\n        if (completeRemove === void 0) { completeRemove = true; }\n        this.loadBundle(bundleName, function (err, bundle) {\n            if (err) {\n                return;\n            }\n            var loadCount = 0;\n            var play = function () {\n                if (loadCount === 2) {\n                    dragonBonesArmatureDisplay.addEventListener(dragonBones.EventObject.COMPLETE, function (e) {\n                        var _a, _b;\n                        if (completeRemove && ((_a = dragonBonesArmatureDisplay.node) === null || _a === void 0 ? void 0 : _a.parent)) {\n                            if (CC_DEBUG) {\n                                console.log(\"renderDragonbonesByBundle 动画播放结束,url\", dragonAssetUrl);\n                            }\n                            (_b = dragonBonesArmatureDisplay.node) === null || _b === void 0 ? void 0 : _b.parent.removeChild(dragonBonesArmatureDisplay.node);\n                        }\n                    }, _this);\n                    dragonBonesArmatureDisplay.armatureName = armatureName;\n                    dragonBonesArmatureDisplay.playAnimation(animName, playtimes);\n                }\n            };\n            bundle.load(dragonAssetUrl, dragonBones.DragonBonesAsset, function (err, asset) {\n                if (err) {\n                    return;\n                }\n                dragonBonesArmatureDisplay.dragonAsset = asset;\n                loadCount++;\n                play();\n            });\n            bundle.load(dragonAssetAtlasUrl, dragonBones.DragonBonesAtlasAsset, function (err, asset) {\n                if (err) {\n                    return;\n                }\n                dragonBonesArmatureDisplay.dragonAtlasAsset = asset;\n                loadCount++;\n                play();\n            });\n        });\n    };\n    ResLoader.events = {};\n    ResLoader.cacheResources = {};\n    return ResLoader;\n}());\nexports.ResLoader = ResLoader;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Module = void 0;\nvar Module = /** @class */ (function () {\n    function Module() {\n        /**注册代理列表*/\n        this._registerProxys = [];\n        /**代理实例集合*/\n        this._proxyClassMap = new Map();\n    }\n    Object.defineProperty(Module.prototype, \"proxyClassMap\", {\n        /** 获取代理集合 */\n        get: function () {\n            return this._proxyClassMap;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Module.prototype, \"moduleType\", {\n        /** 模块类型 */\n        get: function () {\n            return this._moduleType;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**注册代理列表*/\n    Module.prototype.registerProxys = function () {\n        return [];\n    };\n    /**\n     * 获取代理,由于代理全局唯一，因此，只需要通过代理类名引用即可\n     * @param $proxyClass 代理名\n     * @return\n     *\n     */\n    Module.prototype.getProxy = function ($proxyClass) {\n        return this._proxyClassMap.get($proxyClass);\n    };\n    /**启动Proxy*/\n    Module.prototype.startProxy = function (moduleType) {\n        this._registerProxys = this.registerProxys();\n        for (var i_1 = 0, l = this._registerProxys.length; i_1 < l; i_1++) {\n            var proxyClass = this._registerProxys[i_1];\n            if (!this._proxyClassMap.has(proxyClass)) {\n                var _proxyInstance = new proxyClass(this);\n                _proxyInstance['_moduleType'] = moduleType;\n                this._proxyClassMap.set(proxyClass, _proxyInstance);\n                _proxyInstance.startEvents();\n            }\n        }\n    };\n    return Module;\n}());\nexports.Module = Module;\n", "\"use strict\";\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __generator = (this && this.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\n    return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.hotUpdate = exports.LOCAL_GAME_VERSION_KEY = exports.HotUpdateEventCodeEnum = void 0;\nvar EnvInfo_1 = require(\"../../modules/env/vo/EnvInfo\");\nvar HotUpdateEventCodeEnum;\n(function (HotUpdateEventCodeEnum) {\n    HotUpdateEventCodeEnum[HotUpdateEventCodeEnum[\"ERROR_NO_LOCAL_MANIFEST\"] = 0] = \"ERROR_NO_LOCAL_MANIFEST\";\n    HotUpdateEventCodeEnum[HotUpdateEventCodeEnum[\"ERROR_DOWNLOAD_MANIFEST\"] = 1] = \"ERROR_DOWNLOAD_MANIFEST\";\n    HotUpdateEventCodeEnum[HotUpdateEventCodeEnum[\"ERROR_PARSE_MANIFEST\"] = 2] = \"ERROR_PARSE_MANIFEST\";\n    HotUpdateEventCodeEnum[HotUpdateEventCodeEnum[\"NEW_VERSION_FOUND\"] = 3] = \"NEW_VERSION_FOUND\";\n    HotUpdateEventCodeEnum[HotUpdateEventCodeEnum[\"ALREADY_UP_TO_DATE\"] = 4] = \"ALREADY_UP_TO_DATE\";\n    HotUpdateEventCodeEnum[HotUpdateEventCodeEnum[\"UPDATE_PROGRESSION\"] = 5] = \"UPDATE_PROGRESSION\";\n    HotUpdateEventCodeEnum[HotUpdateEventCodeEnum[\"ASSET_UPDATED\"] = 6] = \"ASSET_UPDATED\";\n    HotUpdateEventCodeEnum[HotUpdateEventCodeEnum[\"ERROR_UPDATING\"] = 7] = \"ERROR_UPDATING\";\n    HotUpdateEventCodeEnum[HotUpdateEventCodeEnum[\"UPDATE_FINISHED\"] = 8] = \"UPDATE_FINISHED\";\n    HotUpdateEventCodeEnum[HotUpdateEventCodeEnum[\"UPDATE_FAILED\"] = 9] = \"UPDATE_FAILED\";\n    HotUpdateEventCodeEnum[HotUpdateEventCodeEnum[\"ERROR_DECOMPRESS\"] = 10] = \"ERROR_DECOMPRESS\";\n    HotUpdateEventCodeEnum[HotUpdateEventCodeEnum[\"STOP_WHENUPDATE\"] = 11] = \"STOP_WHENUPDATE\";\n    // 自定义状态\n    HotUpdateEventCodeEnum[HotUpdateEventCodeEnum[\"START\"] = 12] = \"START\";\n    HotUpdateEventCodeEnum[HotUpdateEventCodeEnum[\"FAIL\"] = 13] = \"FAIL\";\n    HotUpdateEventCodeEnum[HotUpdateEventCodeEnum[\"VERSION_COMPARE\"] = 14] = \"VERSION_COMPARE\";\n    HotUpdateEventCodeEnum[HotUpdateEventCodeEnum[\"REQUEST_REMOTE_CONFIG_TIMEOUT\"] = 15] = \"REQUEST_REMOTE_CONFIG_TIMEOUT\";\n    HotUpdateEventCodeEnum[HotUpdateEventCodeEnum[\"REMOTE_CONFIG_DATA_ERROR\"] = 16] = \"REMOTE_CONFIG_DATA_ERROR\";\n})(HotUpdateEventCodeEnum || (exports.HotUpdateEventCodeEnum = HotUpdateEventCodeEnum = {}));\n;\nexports.LOCAL_GAME_VERSION_KEY = 'localGameVersion';\nvar HotUpdate = /** @class */ (function () {\n    function HotUpdate() {\n        this.currentFileNum = 0; //当前已下载的文件数\n        this.currentFileTotal = 0; //需要下载的文件总数\n        this.projectManifestStr = null;\n        this.updating = false;\n        this.failCount = 0;\n        this.am = null;\n        this.firstVersionCompare = false;\n        this._storagePath = '';\n        this._callbacks = [];\n    }\n    Object.defineProperty(HotUpdate.prototype, \"storagePath\", {\n        get: function () {\n            return this._storagePath;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    HotUpdate.prototype._emit = function (state, event) {\n        this._callbacks.forEach(function (cb) { return cb(state, event); });\n    };\n    HotUpdate.prototype.onHotUpdateState = function (callback) {\n        if (typeof callback === 'function') {\n            this._callbacks.push(callback);\n        }\n    };\n    /** 版本比对 */\n    HotUpdate.prototype.versionCompareHandle = function (versionA, versionB) {\n        if (CC_DEBUG) {\n            console.log(' version A is ' + versionA + ', version B is ' + versionB);\n        }\n        var vA = versionA.split('.');\n        var vB = versionB.split('.');\n        for (var i_1 = 0; i_1 < vA.length; ++i_1) {\n            var a = parseInt(vA[i_1]);\n            var b = parseInt(vB[i_1] || '0');\n            if (a === b) {\n                continue;\n            }\n            else {\n                if (b - a > 0 && !this.firstVersionCompare) {\n                    this.firstVersionCompare = true;\n                    this._emit(HotUpdateEventCodeEnum[HotUpdateEventCodeEnum.VERSION_COMPARE], { eventCode: HotUpdateEventCodeEnum.VERSION_COMPARE, msg: \"\\u7248\\u672C\\u6BD4\\u5BF9\\u6210\\u529F\" });\n                }\n                return a - b;\n            }\n        }\n        if (vB.length > vA.length) {\n            return -1;\n        }\n        else {\n            return 0;\n        }\n    };\n    /**\n     * 检查远程文件是否存在\n     * @param url 文件URL\n     * @returns Promise<boolean> 文件是否存在\n     */\n    HotUpdate.prototype.checkRemoteFileExists = function (url) {\n        var _this = this;\n        this.log(\"\\u68C0\\u67E5\\u70ED\\u66F4\\u5730\\u5740\\uFF1A\", url);\n        return new Promise(function (resolve) {\n            window['axios']({\n                method: \"HEAD\",\n                url: url,\n                timeout: 10 * 1000\n            }).then(function (response) {\n                if (response) {\n                    resolve(response.status >= 200 && response.status < 300);\n                }\n                else {\n                    _this.log(\"\\u68C0\\u67E5\\u8FDC\\u7A0B\\u6587\\u4EF6 response \\u4E3A\\u7A7A\\uFF01\");\n                    resolve(false);\n                }\n            }).catch(function (err) {\n                _this.log(\"\\u68C0\\u67E5\\u8FDC\\u7A0B\\u6587\\u4EF6\\u5F02\\u5E38\", err);\n                resolve(false);\n            });\n        });\n    };\n    /**\n     * 同步至最新的热更地址\n     * @param manifest\n     * @param remoteVersionConfig\n     * @returns\n     */\n    HotUpdate.prototype.updateRemoteUrl = function (manifest, remoteVersionConfig) {\n        return __awaiter(this, void 0, void 0, function () {\n            var version, zip, useZip, zipUrl, zipExists, error_1, url1, url2, url3;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        version = remoteVersionConfig.version, zip = remoteVersionConfig.zip;\n                        useZip = zip;\n                        if (!zip) return [3 /*break*/, 4];\n                        zipUrl = EnvInfo_1.envInfo.hotServerUrl + \"/zips/\".concat(manifest.version, \"_\").concat(version, \"/project.manifest\");\n                        _a.label = 1;\n                    case 1:\n                        _a.trys.push([1, 3, , 4]);\n                        return [4 /*yield*/, this.checkRemoteFileExists(zipUrl)];\n                    case 2:\n                        zipExists = _a.sent();\n                        if (!zipExists) {\n                            useZip = false;\n                            this.log(\"\\u8FDC\\u7A0Bzip\\u6587\\u4EF6\\u4E0D\\u5B58\\u5728\\uFF0C\\u56DE\\u9000\\u5230\\u975Ezip\\u6A21\\u5F0F: \".concat(zipUrl));\n                        }\n                        else {\n                            this.log(\"\\u8FDC\\u7A0Bzip\\u6587\\u4EF6\\u5B58\\u5728\\uFF0C\\u4F7F\\u7528zip\\u6A21\\u5F0F: \".concat(zipUrl));\n                        }\n                        return [3 /*break*/, 4];\n                    case 3:\n                        error_1 = _a.sent();\n                        this.log(\"checkRemoteFileExists \\u53D1\\u751F\\u5F02\\u5E38: \".concat(error_1), error_1 === null || error_1 === void 0 ? void 0 : error_1.stack);\n                        return [3 /*break*/, 4];\n                    case 4:\n                        url1 = EnvInfo_1.envInfo.hotServerUrl + \"/\" + (useZip ? \"zips/\".concat(manifest.version, \"_\").concat(version) : version);\n                        url2 = url1 + (useZip ? \"/project.manifest\" : \"/src/project.manifest\");\n                        url3 = url1 + (useZip ? \"/version.manifest\" : \"/src/version.manifest\");\n                        manifest.url1 = url1;\n                        manifest.url2 = url2;\n                        manifest.url3 = url3;\n                        return [2 /*return*/, manifest];\n                }\n            });\n        });\n    };\n    /**\n     * 获取缓存 manifest 数据\n     * @returns\n     */\n    HotUpdate.prototype.getStorageManifest = function () {\n        var storageManifestUrl = this._storagePath + '/project.manifest';\n        if (CC_DEBUG) {\n            this.log(\"\\u5F53\\u524D\\u7F13\\u5B58 project.manifest\\u7684url: \".concat(storageManifestUrl));\n        }\n        if (!jsb.fileUtils.isFileExist(storageManifestUrl)) {\n            if (CC_DEBUG) {\n                this.log(\"project.manifest \\u6587\\u4EF6\\u4E0D\\u5B58\\u5728: \".concat(storageManifestUrl));\n            }\n            return null;\n        }\n        var storageManifestStr = jsb.fileUtils.getStringFromFile(storageManifestUrl);\n        if (!storageManifestStr) {\n            this.log(\"project.manifest \\u6587\\u4EF6\\u8BFB\\u53D6\\u5931\\u8D25: \".concat(storageManifestUrl));\n            return null;\n        }\n        if (CC_DEBUG) {\n            this.log(\"project.manifest \\u6570\\u636E: \".concat(storageManifestStr));\n        }\n        var storageManifestJson = JSON.parse(storageManifestStr);\n        return storageManifestJson;\n    };\n    /** 更新缓存目录数据 */\n    HotUpdate.prototype.updateStorageRemoteUrl = function (remoteVersionConfig) {\n        return __awaiter(this, void 0, void 0, function () {\n            var storageManifestUrl, storageManifestJson, error_2, tempStr;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        storageManifestUrl = this._storagePath + '/project.manifest';\n                        storageManifestJson = this.getStorageManifest();\n                        if (!storageManifestJson) {\n                            return [2 /*return*/];\n                        }\n                        _a.label = 1;\n                    case 1:\n                        _a.trys.push([1, 3, , 4]);\n                        return [4 /*yield*/, this.updateRemoteUrl(storageManifestJson, remoteVersionConfig)];\n                    case 2:\n                        _a.sent();\n                        return [3 /*break*/, 4];\n                    case 3:\n                        error_2 = _a.sent();\n                        this.log(\"updateRemoteUrl \\u53D1\\u751F\\u5F02\\u5E38: \".concat(error_2), error_2 === null || error_2 === void 0 ? void 0 : error_2.stack);\n                        return [3 /*break*/, 4];\n                    case 4:\n                        if (CC_DEBUG) {\n                            this.log(\"\\u540C\\u6B65\\u8FDC\\u7A0B\\u5730\\u5740\\u540E project.manifest \\u6570\\u636E: \".concat(JSON.stringify(storageManifestJson)));\n                        }\n                        try {\n                            jsb.fileUtils.writeStringToFile(JSON.stringify(storageManifestJson), storageManifestUrl);\n                        }\n                        catch (error) {\n                            this.log(\"\\u5199\\u5165\\u5931\\u8D25: \\u5730\\u5740 \".concat(storageManifestUrl, \"   \\u7F13\\u5B58\\u6570\\u636E\\uFF1A \").concat(JSON.stringify(storageManifestJson)));\n                        }\n                        if (CC_DEBUG) {\n                            tempStr = jsb.fileUtils.getStringFromFile(storageManifestUrl);\n                            this.log(\"\\u6700\\u540E\\u5F97\\u5230\\u7684\\u7F13\\u5B58\\u76EE\\u5F55\\u4E0B\\u7684project\\u6570\\u636E: \".concat(tempStr));\n                        }\n                        return [2 /*return*/];\n                }\n            });\n        });\n    };\n    HotUpdate.prototype.setStorage = function (key, value) {\n        if (typeof value === 'object') {\n            value = JSON.stringify(value);\n        }\n        cc.sys.localStorage.setItem(key, value);\n    };\n    HotUpdate.prototype.getStorage = function (key, defaultValue) {\n        if (defaultValue === void 0) { defaultValue = null; }\n        var value = cc.sys.localStorage.getItem(key);\n        if (!value) {\n            return defaultValue;\n        }\n        try {\n            return JSON.parse(value);\n        }\n        catch (_a) {\n            return value;\n        }\n    };\n    /**\n     * 开始热更 入口\n     * @param projectManifest  app 附带的 project.manifest\n     * @param remoteVersion  远程版本号\n     * @returns\n     */\n    HotUpdate.prototype.startUpdate = function (projectManifest, remoteVersionConfig) {\n        return __awaiter(this, void 0, void 0, function () {\n            var remoteVersion, useVersion, storageManifest, compareNumber, manifest, localManifest, manifestRoot, version, packageUrl, manifestUrl, versionUrl;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        remoteVersion = remoteVersionConfig.version;\n                        if (!cc.sys.isNative) {\n                            if (CC_DEBUG) {\n                                this.log(\"startUpdate \\u4E0D\\u662F\\u539F\\u751F\\u73AF\\u5883: \".concat(cc.sys.isNative));\n                            }\n                            this._emit(HotUpdateEventCodeEnum[HotUpdateEventCodeEnum.FAIL], { eventCode: HotUpdateEventCodeEnum.FAIL, msg: '不是原生环境' });\n                            return [2 /*return*/];\n                        }\n                        this._emit(HotUpdateEventCodeEnum[HotUpdateEventCodeEnum.START], { eventCode: HotUpdateEventCodeEnum.START, msg: '开始热更' });\n                        this._storagePath = (jsb.fileUtils ? jsb.fileUtils.getWritablePath() : '/') + 'blockBlastHotUpdate';\n                        if (CC_DEBUG) {\n                            this.log('缓存地址:' + this._storagePath);\n                            this.log('远程 version:', remoteVersion);\n                            this.log(\"\\u8FDC\\u7A0B\\u670D\\u52A1\\u5668\\u5730\\u5740\\uFF1A\", EnvInfo_1.envInfo.hotServerUrl);\n                            this.log(\"\\u4FEE\\u6539\\u524D project \\u6570\\u636E \".concat(JSON.stringify(projectManifest)));\n                        }\n                        useVersion = projectManifest.version;\n                        storageManifest = this.getStorageManifest();\n                        if (storageManifest) {\n                            compareNumber = this.versionCompareHandle(projectManifest.version, storageManifest.version);\n                            if (compareNumber < 0) {\n                                useVersion = storageManifest.version;\n                                this.log(\"\\u4F7F\\u7528\\u7F13\\u5B58\\u7248\\u672C\\uFF1A\".concat(storageManifest.version));\n                            }\n                            this.log(\"\\u6BD4\\u8F83\\u7248\\u672C\\uFF1A compareNumber\\uFF1A \".concat(compareNumber, \", \\u672C\\u5730\\u6700\\u65B0\\u7248\\u672C\\uFF1A\").concat(useVersion, \"\\uFF0Capp\\u7248\\u672C\\uFF1A\").concat(projectManifest.version, \", \\u7F13\\u5B58\\u7248\\u672C\\uFF1A\").concat(storageManifest.version));\n                        }\n                        else {\n                            this.log(\"\\u4F7F\\u7528app\\u7248\\u672C\\uFF1A\".concat(projectManifest.version));\n                        }\n                        if (useVersion === remoteVersion) {\n                            this.log(\"\\u7248\\u672C\\u76F8\\u540C\\uFF0C\\u65E7\\u7248\\u672C\\uFF1A\".concat(useVersion, \", \\u65B0\\u7248\\u672C\\uFF1A\").concat(remoteVersion, \"\\uFF0C\\u8DF3\\u8FC7\\u70ED\\u66F4\\u903B\\u8F91\\u6267\\u884C\\uFF01\"));\n                            this._emit(HotUpdateEventCodeEnum[HotUpdateEventCodeEnum.FAIL], { eventCode: HotUpdateEventCodeEnum.FAIL, msg: \"\\u7248\\u672C\\u76F8\\u540C\\uFF0C\\u4E0D\\u9700\\u8981\\u70ED\\u66F4\" });\n                            return [2 /*return*/];\n                        }\n                        //app自身的manifest同步服务器下发的远程地址\n                        return [4 /*yield*/, this.updateRemoteUrl(projectManifest, remoteVersionConfig)];\n                    case 1:\n                        //app自身的manifest同步服务器下发的远程地址\n                        _a.sent();\n                        this.projectManifestStr = JSON.stringify(projectManifest); //manifest 字符串\n                        if (CC_DEBUG) {\n                            this.log('修改后 project 数据:', this.projectManifestStr);\n                        }\n                        this.am = new jsb.AssetsManager('', this._storagePath, this.versionCompareHandle.bind(this));\n                        // Setup the verification callback, but we don't have md5 check function yet, so only print some message\n                        // Return true if the verification passed, otherwise return false\n                        this.am.setVerifyCallback(function (path, asset) {\n                            // When asset is compressed, we don't need to check its md5, because zip file have been deleted.\n                            var compressed = asset.compressed;\n                            // Retrieve the correct md5 value.\n                            var expectedMD5 = asset.md5;\n                            // asset.path is relative path and path is absolute.\n                            var relativePath = asset.path;\n                            // The size of asset file, but this value could be absent.\n                            var size = asset.size;\n                            if (compressed) {\n                                //this.msgLab.string = \"Verification passed : \" + relativePath;\n                                return true;\n                            }\n                            else {\n                                //this.msgLab.string = \"Verification passed:\" + relativePath + \"(\" + expectedMD5 + \")\";\n                                return true;\n                            }\n                        });\n                        if (CC_DEBUG) {\n                            this.log(\"----project\\u6570\\u636E\\u3001\\u8FDC\\u7A0Bversion\\u6570\\u636E\\u3001\\u672C\\u5730\\u7F13\\u5B58\\u76EE\\u5F55\\u5DF2\\u51C6\\u5907\\u5B8C\\u6210----\");\n                        }\n                        if (this.updating) {\n                            if (CC_DEBUG) {\n                                this.log('正在检查或更新。。。');\n                            }\n                            return [2 /*return*/];\n                        }\n                        this.updating = true;\n                        if (CC_DEBUG) {\n                            this.log(\"----\\u5F00\\u59CB\\u68C0\\u67E5\\u662F\\u5426\\u5B58\\u5728\\u65B0\\u7684\\u7248\\u672C\\u53F7----\");\n                        }\n                        //app版本号与缓存版本号比对前 修改\n                        return [4 /*yield*/, this.updateStorageRemoteUrl(remoteVersionConfig)];\n                    case 2:\n                        //app版本号与缓存版本号比对前 修改\n                        _a.sent();\n                        this.localManifest = null;\n                        if (this.am.getState() === jsb.AssetsManager.State.UNINITED) {\n                            if (CC_DEBUG) {\n                                this.log(\"checkUpdate \\u672C\\u5730\\u6E05\\u5355\\u5C1A\\u672A\\u521D\\u59CB\\u5316\\uFF0C\\u5F00\\u59CB\\u521D\\u59CB\\u5316\");\n                            }\n                            manifest = new jsb.Manifest(this.projectManifestStr, this._storagePath);\n                            this.am.loadLocalManifest(manifest, this._storagePath);\n                            this.localManifest = this.am.getLocalManifest();\n                            this.setStorage(exports.LOCAL_GAME_VERSION_KEY, this.localManifest.getVersion());\n                        }\n                        if (CC_DEBUG) {\n                            localManifest = this.localManifest;\n                            manifestRoot = localManifest.getManifestRoot();\n                            version = localManifest.getVersion();\n                            packageUrl = localManifest.getPackageUrl();\n                            manifestUrl = localManifest.getManifestFileUrl();\n                            versionUrl = localManifest.getVersionFileUrl();\n                            this.log('checkUpdate ---检查本地manifest解析内容---');\n                            this.log('checkUpdate manifest根目录:', manifestRoot);\n                            this.log('checkUpdate 本地版本号:', version);\n                            this.log('checkUpdate 远程资源根Url:', packageUrl);\n                            this.log('checkUpdate 远程资源manifestUrl:', manifestUrl);\n                            this.log('checkUpdate 远程资源versionUrl:', versionUrl);\n                        }\n                        if (!this.localManifest || !this.localManifest.isLoaded()) {\n                            if (CC_DEBUG) {\n                                this.log('checkUpdate 本地清单加载失败...');\n                            }\n                            this._emit(HotUpdateEventCodeEnum[HotUpdateEventCodeEnum.FAIL], { eventCode: HotUpdateEventCodeEnum.FAIL, msg: '本地清单加载失败...' });\n                            return [2 /*return*/];\n                        }\n                        if (CC_DEBUG) {\n                            this.log(\"checkUpdate \\u672C\\u5730\\u6E05\\u5355\\u5DF2\\u7ECF\\u521D\\u59CB\\u5316\\u6210\\u529F\");\n                        }\n                        this.am.setEventCallback(this.versionCheck.bind(this));\n                        this.am.checkUpdate();\n                        return [2 /*return*/];\n                }\n            });\n        });\n    };\n    /** 检查热更回调 */\n    HotUpdate.prototype.versionCheck = function (event) {\n        var _this = this;\n        var eventCode = event.getEventCode();\n        var eventCodeStr = HotUpdateEventCodeEnum[eventCode];\n        var isFailed = false;\n        var msg = '';\n        if (CC_DEBUG) {\n            this.log(\"checkCb \\u68C0\\u67E5\\u70ED\\u66F4\\u56DE\\u8C03\\u4E8B\\u4EF6\\u540D: \".concat(eventCode, \" \").concat(eventCodeStr));\n        }\n        switch (eventCode) {\n            case jsb.EventAssetsManager.ERROR_NO_LOCAL_MANIFEST:\n                isFailed = true;\n                msg = 'checkCb 未找到本地清单文件，跳过热更新。';\n                break;\n            case jsb.EventAssetsManager.ERROR_DOWNLOAD_MANIFEST:\n            case jsb.EventAssetsManager.ERROR_PARSE_MANIFEST:\n                isFailed = true;\n                msg = 'checkCb 下载清单文件失败，跳过热更新。';\n                break;\n            case jsb.EventAssetsManager.ALREADY_UP_TO_DATE:\n                isFailed = true;\n                msg = \"checkCb \\u5DF2\\u66F4\\u65B0\\u5230\\u6700\\u65B0\\u7684\\u8FDC\\u7A0B\\u7248\\u672C\";\n                break;\n            case jsb.EventAssetsManager.NEW_VERSION_FOUND:\n                if (CC_DEBUG) {\n                    this.log(\"checkCb \\u53D1\\u73B0\\u65B0\\u7248\\u672C, \\u9700\\u8981\\u4E0B\\u8F7D\\u5B57\\u8282\\u5927\\u5C0F \".concat(this.am.getTotalBytes(), \", \").concat(eventCode, \" \").concat(eventCodeStr));\n                }\n                this._emit(HotUpdateEventCodeEnum[HotUpdateEventCodeEnum.NEW_VERSION_FOUND], { eventCode: HotUpdateEventCodeEnum.NEW_VERSION_FOUND, msg: \"\\u53D1\\u73B0\\u65B0\\u7248\\u672C, \\u9700\\u8981\\u4E0B\\u8F7D\\u5B57\\u8282\\u5927\\u5C0F(' + \".concat(this.am.getTotalBytes(), \" + ')\"), jsbEvent: event });\n                setTimeout(function () {\n                    _this.hotUpdate();\n                }, 100);\n                break;\n            default:\n                return;\n        }\n        if (isFailed) {\n            this._emit(HotUpdateEventCodeEnum[HotUpdateEventCodeEnum.FAIL], { eventCode: HotUpdateEventCodeEnum.FAIL, msg: \"\".concat(msg, \": \").concat(eventCode, \" \").concat(eventCodeStr, \" \").concat(event.getMessage()), jsbEvent: event });\n        }\n        this.am.setEventCallback(null);\n    };\n    /** 开始下载资源 */\n    HotUpdate.prototype.hotUpdate = function () {\n        if (CC_DEBUG) {\n            this.log('hotUpdate 检测到新版本 开始执行热更,下载assets资源');\n        }\n        if (!this.localManifest || !this.localManifest.isLoaded()) {\n            if (CC_DEBUG) {\n                this.log('updateCb 本地清单加载失败...');\n            }\n            this._emit(HotUpdateEventCodeEnum[HotUpdateEventCodeEnum.FAIL], { eventCode: HotUpdateEventCodeEnum.FAIL, msg: '本地清单加载失败...', jsbEvent: null });\n            return;\n        }\n        if (CC_DEBUG) {\n            this.log('hotUpdate 本地清单初始化成功 检测到新版本 开始执行热更，');\n        }\n        this.am.setEventCallback(this.updateCb.bind(this));\n        this.am.update();\n        this.currentFileNum = 0;\n        this.currentFileTotal = 0;\n        this.failCount = 0;\n    };\n    /** 热更回调 */\n    HotUpdate.prototype.updateCb = function (event) {\n        var isUpdateFinished = false;\n        var isFailed = false;\n        var msg = '';\n        var eventCode = event.getEventCode();\n        var eventCodeStr = HotUpdateEventCodeEnum[eventCode];\n        if (CC_DEBUG) {\n            console.log(\"updateCb \\u70ED\\u66F4\\u56DE\\u8C03\\u4E8B\\u4EF6\\u540D: \".concat(eventCode, \" \").concat(eventCodeStr, \" \"));\n        }\n        switch (eventCode) {\n            case jsb.EventAssetsManager.UPDATE_FINISHED:\n                isUpdateFinished = true;\n                msg = \"updateCb \\u66F4\\u65B0\\u5DF2\\u5B8C\\u6210\\u3002\".concat(eventCodeStr, \" \").concat(eventCode, \" \").concat(event.getMessage());\n                break;\n            case jsb.EventAssetsManager.ERROR_NO_LOCAL_MANIFEST:\n                isFailed = true;\n                msg = \"updateCb  \\u6CA1\\u627E\\u5230\\u627E\\u5230\\u672C\\u5730\\u6E05\\u5355\\u6587\\u4EF6\\uFF0C\\u8DF3\\u8FC7\\u70ED\\u66F4\\u65B0\\u3002\".concat(eventCode, \" \").concat(eventCodeStr);\n                break;\n            case jsb.EventAssetsManager.ASSET_UPDATED:\n                msg = \"updateCb \\u4E0B\\u8F7D\\u6210\\u529F\\u6587\\u4EF6: \".concat(eventCodeStr, \" \").concat(eventCode, \" \").concat(event.getAssetId(), \"}\");\n                break;\n            case jsb.EventAssetsManager.UPDATE_PROGRESSION:\n                var downloadFiles = event.getDownloadedFiles();\n                var totalFiles = event.getTotalFiles();\n                var downloadBytes = event.getDownloadedBytes();\n                var totalBytes = event.getTotalBytes();\n                this.currentFileNum = event.getDownloadedFiles();\n                this.currentFileTotal = event.getTotalFiles();\n                if (CC_DEBUG) {\n                    this.log(\"updateCb \\u4E0B\\u8F7D\\u8FDB\\u5EA6 => \\u5DF2\\u4E0B\\u8F7D\\u6587\\u4EF6\\u6570\\uFF1A\".concat(downloadFiles, \"/\").concat(totalFiles, \"  \\u5DF2\\u4E0B\\u8F7D\\u5B57\\u8282\\uFF1A\").concat(downloadBytes, \"/\").concat(totalBytes, \"   \").concat(eventCodeStr, \"  \").concat(eventCode, \"  }\"));\n                }\n                this._emit(HotUpdateEventCodeEnum[HotUpdateEventCodeEnum.UPDATE_PROGRESSION], { eventCode: eventCode, msg: \"\\u4E0B\\u8F7D\\u8FDB\\u5EA6\\uFF08\\u6587\\u4EF6\\u6570\\uFF09\\uFF1A\".concat(downloadFiles, \" / \").concat(totalFiles, \" \").concat(eventCodeStr, \" \").concat(eventCode), jsbEvent: event });\n                break;\n            case jsb.EventAssetsManager.ERROR_DOWNLOAD_MANIFEST:\n            case jsb.EventAssetsManager.ERROR_PARSE_MANIFEST:\n                isFailed = true;\n                msg = \"updateCb \\u4E0B\\u8F7D\\u6E05\\u5355\\u6587\\u4EF6\\u5931\\u8D25\\uFF0C\\u8DF3\\u8FC7\\u70ED\\u66F4\\u65B0\\u3002\".concat(eventCode, \" \").concat(eventCodeStr, \" \").concat(event.getMessage());\n                break;\n            case jsb.EventAssetsManager.ALREADY_UP_TO_DATE:\n                isFailed = true;\n                msg = \"updateCb \\u5DF2\\u66F4\\u65B0\\u5230\\u6700\\u65B0\\u7684\\u8FDC\\u7A0B\\u7248\\u672C\\u3002\".concat(eventCode, \" \").concat(eventCodeStr);\n                break;\n            case jsb.EventAssetsManager.UPDATE_FAILED:\n                if (CC_DEBUG) {\n                    console.log('%c[热更新]', 'color:#fff;background:#6a1b9a;padding:2px 6px;border-radius:4px;', \"updateCb \\u66F4\\u65B0\\u5931\\u8D25: \".concat(event.getMessage(), \", ' \\u5DF2\\u4E0B\\u8F7D\\u6587\\u4EF6\\u6570:', \").concat(this.currentFileNum, \", ' \\u9700\\u8981\\u4E0B\\u8F7D\\u6587\\u4EF6\\u6570:', \").concat(this.currentFileTotal));\n                }\n                this.failCount++;\n                if (this.failCount <= 5) {\n                    this.am.downloadFailedAssets();\n                }\n                else {\n                    isFailed = true;\n                    msg = \"updateCb \\u5931\\u8D25\\u6B21\\u6570\\u8FC7\\u591A\\uFF0C\\u653E\\u5F03\\u91CD\\u65B0\\u4E0B\\u8F7D\\u5931\\u8D25\\u6587\\u4EF6 \".concat(eventCode, \" \").concat(eventCodeStr, \" \").concat(event.getMessage());\n                }\n                break;\n            case jsb.EventAssetsManager.ERROR_UPDATING:\n                try {\n                    //如果下载失败了，删除文件的临时路径\n                    if ((event.getMessage() || '').includes('(416)')) {\n                        // 416 means the server doesn't support range request, we need to remove the tmp file.\n                        jsb.fileUtils.removeFile(\"\".concat(this._storagePath, \"_temp/\").concat(event.getAssetId(), \".tmp\"));\n                    }\n                }\n                catch (error) {\n                    console.error('%c[热更新]', 'color:#fff;background:#6a1b9a;padding:2px 6px;border-radius:4px;', \"\\u5220\\u9664\\u4E34\\u65F6\\u6587\\u4EF6\\u53D1\\u751F\\u9519\\u8BEF: \".concat(eventCode, \" \").concat(eventCodeStr, \" \").concat(error, \" \"));\n                }\n                this.log(\"updateCb \\u8D44\\u6E90\\u66F4\\u65B0\\u9519\\u8BEF: \".concat(event.getAssetId(), \" \").concat(event.getMessage()));\n                break;\n            case jsb.EventAssetsManager.ERROR_DECOMPRESS:\n                if (CC_DEBUG) {\n                    console.log(\"updateCb \\u89E3\\u538B\\u7F29\\u5931\\u8D25: \".concat(eventCode, \" \").concat(eventCodeStr, \" \").concat(event.getMessage()));\n                }\n                break;\n            case jsb.EventAssetsManager.STOP_WHENUPDATE:\n                isFailed = true;\n                msg = \"updateCb STOP_WHENUPDATE: \".concat(eventCode, \" \").concat(eventCodeStr, \" \").concat(event.getMessage());\n                break;\n            default:\n                break;\n        }\n        if (CC_DEBUG) {\n            this.log('%c[热更新]', 'color:#fff;background:#6a1b9a;padding:2px 6px;border-radius:4px;', msg);\n        }\n        if (isFailed) {\n            this.am.setEventCallback(null);\n            this._emit(HotUpdateEventCodeEnum[HotUpdateEventCodeEnum.FAIL], { eventCode: HotUpdateEventCodeEnum.FAIL, msg: \"\".concat(msg, \" \").concat(event.getMessage()), jsbEvent: event });\n            this.updating = false;\n            this.am.getLocalManifest().getVersion();\n            return;\n        }\n        if (isUpdateFinished) {\n            this.am.setEventCallback(null);\n            /**\n             * 1，storagePath\n             * 2，storagePath + manifest中的searchPaths\n             */\n            var manifestSearchPaths = this.am.getLocalManifest().getSearchPaths();\n            cc.sys.localStorage.setItem('blockBlastHotUpdateData', JSON.stringify(manifestSearchPaths));\n            if (CC_DEBUG) {\n                this.log(\"updateCb \\u641C\\u7D22\\u8DEF\\u5F84\\uFF1A \".concat(cc.sys.localStorage.getItem('blockBlastHotUpdateData')));\n                this.log(\"updateCb \\u5DF2\\u66F4\\u65B0\\u5230\\u6700\\u65B0\\u7248\\u672C\\uFF0C\\u8BF7\\u91CD\\u542F\\uFF1A \".concat(cc.sys.localStorage.getItem('blockBlastHotUpdateData')));\n            }\n            this._emit(HotUpdateEventCodeEnum[HotUpdateEventCodeEnum.UPDATE_FINISHED], { eventCode: HotUpdateEventCodeEnum.UPDATE_FINISHED, msg: \"\\u66F4\\u65B0\\u6210\\u529F: \".concat(event.getMessage()), jsbEvent: event });\n            this.updating = false;\n        }\n    };\n    HotUpdate.prototype.log = function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        if (CC_DEBUG) {\n            var style = 'color:#fff;background:#6a1b9a;padding:2px 6px;border-radius:4px;';\n            console.log.apply(console, __spreadArray(['%c[热更新]', style], __read(args), false));\n        }\n    };\n    HotUpdate.prototype.error = function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        if (CC_DEBUG) {\n            var style = 'color:#fff;background:#6a1b9a;padding:2px 6px;border-radius:4px;';\n            console.error.apply(console, __spreadArray(['%c[热更新]', style], __read(args), false));\n        }\n    };\n    return HotUpdate;\n}());\nexports.hotUpdate = new HotUpdate();\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.EventManager = void 0;\nvar ModuleManager_1 = require(\"./ModuleManager\");\nvar EventManager = /** @class */ (function () {\n    function EventManager() {\n    }\n    Object.defineProperty(EventManager, \"events\", {\n        /**\n         * @private\n         */\n        get: function () {\n            return this._events;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * 注册事件，初始化的时候所有的事件都被注册了\n     * @param $event 模块事件\n     *\n     */\n    EventManager.registerEvent = function (eventVo) {\n        if (eventVo === null) {\n            return;\n        }\n        var eventClass = eventVo.eventClass;\n        if (eventClass) {\n            if (this._events.get(eventClass) === undefined) {\n                this._events.set(eventClass, []);\n            }\n            //判断是否存在代理\n            var key = eventVo.eventClass;\n            var events = this._events.get(eventVo.eventClass);\n            var find = false;\n            if (events) {\n                for (var i_1 = 0, l = events === null || events === void 0 ? void 0 : events.length; i_1 < l; i_1++) {\n                    if (events[i_1].proxy === eventVo.proxy) {\n                        find = true;\n                        break;\n                    }\n                }\n                if (!find) {\n                    events.push(eventVo);\n                }\n            }\n        }\n    };\n    /**\n     * 某个代理发送事件\n     * @param $proxy 事件代理\n     * @param event 事件\n     *\n     */\n    EventManager.dispatchModuleEvent = function (event) {\n        if (!event) {\n            return;\n        }\n        var eventConstructor = event.getClass();\n        var events = this._events.get(eventConstructor);\n        if (events) {\n            for (var i_2 = 0, l = events.length; i_2 < l; i_2++) {\n                var eventVo = events[i_2];\n                var proxy = eventVo.proxy;\n                var proxyModuleType = proxy.moduleType;\n                if (proxyModuleType === ModuleManager_1.ModuleType.Common || proxyModuleType === ModuleManager_1.ModuleManager.moduleType) {\n                    proxy === null || proxy === void 0 ? void 0 : proxy.receivedEvents(event);\n                }\n            }\n        }\n        var obj = this._eventAllCompleted.get(eventConstructor);\n        if (obj) {\n            var moduleType = ModuleManager_1.ModuleManager.moduleType;\n            // 全部发送\n            if (moduleType === ModuleManager_1.ModuleType.Common) {\n                for (var mt in obj) {\n                    var callbacks = obj[mt];\n                    for (var i_3 = 0; i_3 < (callbacks === null || callbacks === void 0 ? void 0 : callbacks.length); i_3++) {\n                        var callback = callbacks[i_3];\n                        if (callback) {\n                            callback(event);\n                        }\n                    }\n                }\n            }\n            else {\n                var callbacks = obj[ModuleManager_1.ModuleManager.moduleType];\n                for (var i_4 = 0; i_4 < (callbacks === null || callbacks === void 0 ? void 0 : callbacks.length); i_4++) {\n                    var callback = callbacks[i_4];\n                    if (callback) {\n                        callback(event);\n                    }\n                }\n            }\n        }\n    };\n    /**\n     * 以 Async 的方式分发事件，支持处理异步事件后的回调\n     * @param event\n     * @returns\n     */\n    EventManager.dispatchModuleEventAsync = function (event) {\n        return new Promise(function (resolve) {\n            event['_callback'] = function () { return resolve(); };\n            EventManager.dispatchModuleEvent(event);\n        });\n    };\n    /**\n     * 等待当前事件全部执行完毕之后回调\n     * @param eventConstructor\n     * @param callback\n     */\n    EventManager.onEventAllCompleted = function (moduleType, eventConstructor, callback) {\n        var obj = this._eventAllCompleted.get(eventConstructor);\n        if (!obj) {\n            obj = {};\n            this._eventAllCompleted.set(eventConstructor, obj);\n        }\n        if (!obj[moduleType]) {\n            obj[moduleType] = [];\n        }\n        obj[moduleType].push(callback);\n    };\n    /**\n     * 重置事件管理器状态\n     * 清理所有事件映射和回调，防止状态残留\n     */\n    EventManager.reset = function () {\n        // 清理事件映射\n        this._events.clear();\n        // 清理事件完成回调\n        this._eventAllCompleted.clear();\n        if (CC_DEBUG) {\n            console.log('[EventManager] 事件管理器已重置');\n        }\n    };\n    /**\n     * 所有的事件\n     */\n    EventManager._events = new Map();\n    /**\n     * 存储当前事件所有监听完毕\n     */\n    EventManager._eventAllCompleted = new Map();\n    return EventManager;\n}());\nexports.EventManager = EventManager;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Emitter = void 0;\nvar Emitter = /** @class */ (function () {\n    function Emitter(options) {\n        this._options = options;\n    }\n    Object.defineProperty(Emitter.prototype, \"event\", {\n        /**\n         * 获取发射器事件\n         */\n        get: function () {\n            var _this = this;\n            var _a;\n            (_a = this._event) !== null && _a !== void 0 ? _a : (this._event = function (callback, thisArgs) {\n                var _a, _b, _c, _d, _e, _f;\n                if (thisArgs) {\n                    callback = callback.bind(thisArgs);\n                }\n                if (!_this._callbacks) {\n                    (_b = (_a = _this._options) === null || _a === void 0 ? void 0 : _a.onWillAddFirstListener) === null || _b === void 0 ? void 0 : _b.call(_a, _this);\n                    _this._callbacks = [];\n                    (_d = (_c = _this._options) === null || _c === void 0 ? void 0 : _c.onDidAddFirstListener) === null || _d === void 0 ? void 0 : _d.call(_c, _this);\n                }\n                (_f = (_e = _this._options) === null || _e === void 0 ? void 0 : _e.onDidAddListener) === null || _f === void 0 ? void 0 : _f.call(_e, _this);\n                _this._callbacks.push({ callback: callback, thisArgs: thisArgs });\n            });\n            return this._event;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * 发送事件\n     * @param event\n     * @returns\n     */\n    Emitter.prototype.fire = function (event) {\n        var _callbacks = this._callbacks;\n        for (var i_1 = 0; i_1 < (_callbacks === null || _callbacks === void 0 ? void 0 : _callbacks.length); i_1++) {\n            var _a = _callbacks[i_1], callback = _a.callback, thisArgs = _a.thisArgs;\n            if (callback) {\n                callback.apply(thisArgs, [event]);\n            }\n        }\n    };\n    Emitter.prototype.dispose = function () {\n        var _a, _b;\n        if (!this._disposed) {\n            this._disposed = true;\n            this._callbacks = undefined;\n            (_b = (_a = this._options) === null || _a === void 0 ? void 0 : _a.onDidRemoveLastListener) === null || _b === void 0 ? void 0 : _b.call(_a);\n        }\n    };\n    return Emitter;\n}());\nexports.Emitter = Emitter;\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar AdapterFringe_1 = require(\"../assets/scripts/base/adapter/AdapterFringe\");\nvar DragonbonesAnim_1 = require(\"../assets/scripts/base/animation/DragonbonesAnim\");\nvar arrays_1 = require(\"../assets/scripts/base/arrays/arrays\");\nvar Barrier_1 = require(\"../assets/scripts/base/async/Barrier\");\nvar DeferredPromise_1 = require(\"../assets/scripts/base/async/DeferredPromise\");\nvar First_1 = require(\"../assets/scripts/base/async/First\");\nvar Limiter_1 = require(\"../assets/scripts/base/async/Limiter\");\nvar Sequence_1 = require(\"../assets/scripts/base/async/Sequence\");\nvar WaitFor_1 = require(\"../assets/scripts/base/async/WaitFor\");\nvar AudioInfo_1 = require(\"../assets/scripts/base/audio/AudioInfo\");\nvar CacheRender_1 = require(\"../assets/scripts/base/cache/CacheRender\");\nvar CacheComponents_1 = require(\"../assets/scripts/base/components/CacheComponents\");\nvar Component_1 = require(\"../assets/scripts/base/components/Component\");\nvar Copy_1 = require(\"../assets/scripts/base/copy/Copy\");\nvar UrlCrypto_1 = require(\"../assets/scripts/base/crypto/UrlCrypto\");\nvar Date_1 = require(\"../assets/scripts/base/date/Date\");\nvar DecoratorAdapter_1 = require(\"../assets/scripts/base/decorators/DecoratorAdapter\");\nvar DecoratorDebounce_1 = require(\"../assets/scripts/base/decorators/DecoratorDebounce\");\nvar DecoratorMeasure_1 = require(\"../assets/scripts/base/decorators/DecoratorMeasure\");\nvar DecoratorMemoize_1 = require(\"../assets/scripts/base/decorators/DecoratorMemoize\");\nvar DecoratorScreen_1 = require(\"../assets/scripts/base/decorators/DecoratorScreen\");\nvar DecoratorThrottle_1 = require(\"../assets/scripts/base/decorators/DecoratorThrottle\");\nvar DecoratorTrait_1 = require(\"../assets/scripts/base/decorators/DecoratorTrait\");\nvar Dot_1 = require(\"../assets/scripts/base/dot/Dot\");\n//import PanelScaleEase from \"../assets/scripts/base/ease/PanelScaleEase\";\nvar enum_1 = require(\"../assets/scripts/base/enum/enum\");\nvar Equal_1 = require(\"../assets/scripts/base/equal/Equal\");\nvar Events_1 = require(\"../assets/scripts/base/events/Events\");\nvar HotUpdate_1 = require(\"../assets/scripts/base/hotUpdate/HotUpdate\");\nvar Http_1 = require(\"../assets/scripts/base/http/Http\");\nvar Interval_1 = require(\"../assets/scripts/base/interval/Interval\");\nvar ResLoader_1 = require(\"../assets/scripts/base/loader/ResLoader\");\nvar numbers_1 = require(\"../assets/scripts/base/numbers/numbers\");\nvar RenderingOptimization_1 = require(\"../assets/scripts/base/performance/RenderingOptimization\");\nvar ObjectPool_1 = require(\"../assets/scripts/base/pool/ObjectPool\");\nvar Random_1 = require(\"../assets/scripts/base/random/Random\");\nvar Storage_1 = require(\"../assets/scripts/base/storage/Storage\");\nvar GameLayer_1 = require(\"../assets/scripts/base/layer/GameLayer\");\nvar TraitConfigInfo_1 = require(\"../assets/scripts/base/traitConfig/TraitConfigInfo\");\nvar Timeout_1 = require(\"../assets/scripts/base/timeout/Timeout\");\nvar Timer_1 = require(\"../assets/scripts/base/timer/Timer\");\nvar Trait_1 = require(\"../assets/scripts/base/trait/Trait\");\nvar UI_1 = require(\"../assets/scripts/base/ui/UI\");\nvar Task_1 = require(\"../assets/scripts/base/task/Task\");\nvar NativeBridge_1 = require(\"../assets/scripts/base/native/NativeBridge\");\nvar Url_1 = require(\"../assets/scripts/base/url/Url\");\nvar watch_1 = require(\"../assets/scripts/base/watch/watch\");\nvar EventManager_1 = require(\"../assets/scripts/falcon/EventManager\");\nvar EventVo_1 = require(\"../assets/scripts/falcon/EventVo\");\nvar Module_1 = require(\"../assets/scripts/falcon/Module\");\nvar ModuleEvent_1 = require(\"../assets/scripts/falcon/ModuleEvent\");\nvar ModuleManager_1 = require(\"../assets/scripts/falcon/ModuleManager\");\nvar Proxy_1 = require(\"../assets/scripts/falcon/Proxy\");\nvar Types_1 = require(\"../assets/scripts/base/types/Types\");\nvar PrefabInfo_1 = require(\"../assets/scripts/modules/prefab/vo/PrefabInfo\");\nvar GameStateManager_1 = require(\"../assets/scripts/base/gameState/GameStateManager\");\n// 创建 falcon 命名空间\nvar falcon;\n(function (falcon) {\n    ///////////////////////////////   falcon   ///////////////////////////////\n    // proxy\n    falcon.Proxy = Proxy_1.Proxy;\n    // module\n    falcon.Module = Module_1.Module;\n    falcon.ModuleManager = ModuleManager_1.ModuleManager;\n    // events\n    falcon.Emitter = Events_1.Emitter;\n    falcon.EventManager = EventManager_1.EventManager;\n    falcon.EventVo = EventVo_1.EventVo;\n    falcon.ModuleEvent = ModuleEvent_1.ModuleEvent;\n    ///////////////////////////////   base   ///////////////////////////////\n    // adapter\n    falcon.adapter = {\n        applyAdapterFringe: AdapterFringe_1.applyAdapterFringe\n    };\n    // 播放龙骨动画\n    falcon.dragonbonesAnim = DragonbonesAnim_1.dragonbonesAnim;\n    // arrays\n    falcon.arrays = {\n        arraysHaveSameElements: arrays_1.arraysHaveSameElements,\n        shuffleArray: arrays_1.shuffleArray,\n        arraysEqual: arrays_1.arraysEqual,\n        ensureMaxLength: arrays_1.ensureMaxLength\n    };\n    // async\n    falcon.async = {\n        TimeoutBarrier: Barrier_1.TimeoutBarrier,\n        Barrier: Barrier_1.Barrier,\n        DeferredPromise: DeferredPromise_1.DeferredPromise,\n        firstParallel: First_1.firstParallel,\n        first: First_1.first,\n        Limiter: Limiter_1.Limiter,\n        sequence: Sequence_1.sequence,\n        waitFor: WaitFor_1.waitFor\n    };\n    // audio\n    falcon.audioInfo = AudioInfo_1.audioInfo;\n    // cache\n    falcon.cacheRender = CacheRender_1.cacheRender;\n    // components\n    falcon.cacheComponents = CacheComponents_1.cacheComponents;\n    // component\n    falcon.Component = Component_1.default;\n    // copy\n    falcon.copy = {\n        deepCopy: Copy_1.deepCopy,\n        deepCopyArrayFrom: Copy_1.deepCopyArrayFrom,\n        deepCopySlice: Copy_1.deepCopySlice,\n        deepCopyLoop: Copy_1.deepCopyLoop,\n        deepCopyFixed: Copy_1.deepCopyFixed\n    };\n    // crypto\n    falcon.UrlCrypto = UrlCrypto_1.UrlCrypto;\n    // date\n    falcon.date = {\n        getLastSomeDays: Date_1.getLastSomeDays,\n        getTodayDate: Date_1.getTodayDate,\n        getDiffDays: Date_1.getDiffDays\n    };\n    // decorators\n    falcon.adapterFringe = DecoratorAdapter_1.adapterFringe;\n    falcon.throttle = DecoratorThrottle_1.throttle;\n    falcon.debounce = DecoratorDebounce_1.debounce;\n    falcon.measure = DecoratorMeasure_1.measure;\n    falcon.memoize = DecoratorMemoize_1.memoize;\n    falcon.trait = DecoratorTrait_1.trait;\n    falcon.templateTrait = DecoratorTrait_1.templateTrait;\n    falcon.ScreenAdapter = DecoratorScreen_1.ScreenAdapter;\n    // dot\n    falcon.createDotData = Dot_1.createDotData;\n    // enum\n    falcon.enumUtils = {\n        getKeyByValue: enum_1.getKeyByValue,\n        isValueInEnum: enum_1.isValueInEnum\n    };\n    // equal\n    falcon.equalUtils = {\n        equal: Equal_1.equal\n    };\n    // hot update\n    falcon.hotUpdate = HotUpdate_1.hotUpdate;\n    // http\n    falcon.http = Http_1.http;\n    // interval\n    falcon.intervalUtils = {\n        interval: Interval_1.interval\n    };\n    // native bridge\n    falcon.NativeBridge = NativeBridge_1.NativeBridge;\n    // resource loader\n    falcon.ResLoader = ResLoader_1.ResLoader;\n    // layer\n    falcon.layerManager = GameLayer_1.layerManager;\n    falcon.layer = {\n        gameUiLayer: GameLayer_1.gameUiLayer,\n        getGameUiLayer: GameLayer_1.getGameUiLayer,\n        resetGameUiLayer: GameLayer_1.resetGameUiLayer,\n        addWidget: GameLayer_1.addWidget,\n        initNodeConfig: GameLayer_1.initNodeConfig\n    };\n    // numbers\n    falcon.numbers = {\n        randomInt: numbers_1.randomInt,\n        randomFloat: numbers_1.randomFloat\n    };\n    // performance\n    falcon.performance = {\n        executeRenderingOptimize: RenderingOptimization_1.executeRenderingOptimize\n    };\n    // pool\n    falcon.ObjectPool = ObjectPool_1.ObjectPool;\n    // random\n    falcon.random = {\n        randomList: Random_1.randomList\n    };\n    // storage utils (重命名以避免与函数名冲突)\n    falcon.storage = Storage_1.storage;\n    // timeout utils (重命名以避免与函数名冲突)\n    falcon.timeoutUtils = {\n        timeout: Timeout_1.timeout\n    };\n    // traitConfig\n    falcon.traitConfigInfo = TraitConfigInfo_1.traitConfigInfo;\n    // traitConfig\n    falcon.task = Task_1.task;\n    // timer\n    falcon.timer = {\n        nextFrame: Timer_1.nextFrame,\n        callLater: Timer_1.callLater\n    };\n    // type\n    falcon.typeUtils = {\n        isString: Types_1.isString,\n        isStringArray: Types_1.isStringArray,\n        isObject: Types_1.isObject,\n        isTypedArray: Types_1.isTypedArray,\n        isNumber: Types_1.isNumber,\n        isIterable: Types_1.isIterable,\n        isBoolean: Types_1.isBoolean,\n        isUndefined: Types_1.isUndefined,\n        isDefined: Types_1.isDefined,\n        isUndefinedOrNull: Types_1.isUndefinedOrNull,\n        isFunction: Types_1.isFunction,\n        getType: Types_1.getType\n    };\n    // trait utils (重命名以避免与类名冲突)\n    falcon.Trait = Trait_1.Trait;\n    // ui\n    falcon.UI = UI_1.UI;\n    // url\n    falcon.url = {\n        getUrlParameterValue: Url_1.getUrlParameterValue\n    };\n    // watch utils (重命名以避免与函数名冲突)\n    falcon.watchUtils = {\n        reactive: watch_1.reactive,\n        watch: watch_1.watch\n    };\n    // prefab 实例化相关功能\n    falcon.prefabInfo = PrefabInfo_1.prefabInfo;\n    // INSTANTIATE 函数 - 提供与Main.ts中相同的全局函数\n    falcon.INSTANTIATE = function (prefab) {\n        return PrefabInfo_1.prefabInfo.getOrCreatePrefabInstantiate.call(PrefabInfo_1.prefabInfo, prefab);\n    };\n    // gameStateManager\n    falcon.gameStateManager = GameStateManager_1.gameStateManager;\n})(falcon || (falcon = {}));\n// 将 falcon 命名空间挂载到全局\nwindow.falcon = falcon;\n// 将 INSTANTIATE 函数挂载到全局，确保UI模块能正常工作\nwindow.INSTANTIATE = falcon.INSTANTIATE;\n// 导出 falcon 命名空间\nexports.default = falcon;\n"], "names": ["Object", "defineProperty", "exports", "value", "getLastSomeDays", "day", "currentTime", "Date", "now", "days", "i_1", "data", "setTime", "push", "getFullYear", "getMonth", "getDate", "getTodayDate", "today", "concat", "getDiffDays", "date1", "date2", "date1WithoutTime", "setHours", "date2WithoutTime", "diffTime", "Math", "abs", "getTime", "ceil", "ReactivePos", "reactive", "option", "target", "propertyName", "pos", "callback", "properties", "watchMap", "get", "constructor", "set", "watch", "propertykey", "_a", "proxy", "_reactive", "newValue", "enumerable", "configurable", "watchDebug", "watchFlag", "obj", "path", "max<PERSON><PERSON><PERSON>", "lastContent_1", "CC_DEBUG", "Types_1", "isObject", "length", "Proxy", "key", "receiver", "Reflect", "String", "Error", "stackTraceLimit", "error", "stack", "filteredLines", "split", "filter", "line", "includes", "content", "join", "console", "log", "Map", "watchTarget", "handler", "result", "oldValue", "properties_1", "keys", "for<PERSON>ach", "cacheRender", "ResLoader_1", "<PERSON><PERSON><PERSON><PERSON>", "this", "_caches", "prototype", "createOrUpdateCacheListComponents", "_this", "prefabUrl", "bundleName", "count", "typeOrClassName", "parent", "Promise", "c", "e", "onComplete", "err", "asset", "caches", "itemCaches", "cacheLen", "cacheComp", "node", "active", "comps", "i_2", "cc", "instantiate", "getComponent", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "loadByBundle", "Prefab", "load", "clearAllCaches", "comp", "<PERSON><PERSON><PERSON><PERSON>", "removeFromParent", "destroy", "reset", "__read", "o", "n", "m", "Symbol", "iterator", "r", "i", "call", "ar", "next", "done", "arraysHaveSameElements", "arr1", "arr2", "sortedArr1", "slice", "sort", "sortedArr2", "shuffle<PERSON><PERSON><PERSON>", "array", "<PERSON><PERSON><PERSON><PERSON>", "j", "floor", "random", "arraysEqual", "i_3", "ensureMaxLength", "arr", "max<PERSON><PERSON><PERSON>", "newItem", "shift", "extendStatics", "__extends", "d", "b", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "TypeError", "__", "create", "UrlCrypto", "_super", "apply", "arguments", "encrypt", "url", "base64Str", "<PERSON><PERSON><PERSON>", "from", "toString", "_des", "decrypt", "decryptStr", "paramStr", "replace", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "encodeKey", "encodeStr", "str", "index", "indexOf", "ICrypto", "dragonbonesAnim", "originalPlayAnimation_1", "dragonBones", "ArmatureDisplay", "playAnimation", "anim<PERSON><PERSON>", "playTimes", "_b", "_c", "_d", "_e", "_armatureName", "_dragonBones<PERSON>son", "dragonAsset", "_dragonBonesJsonData", "JSON", "parse", "armatureInfo", "armature", "find", "v", "name", "animation", "DragonBonesAnim", "dragonbones", "play", "parentNode", "armatureName", "animationName", "config", "undefined", "dragonAssetUrl", "dragonAtlasAssetUrl", "frameSplitting", "Node", "dragonBonesArmatureDisplay", "addComponent", "enableBatch", "updateAnimationCache", "setAnimationCacheMode", "AnimationCacheMode", "SHARED_CACHE", "completeBack", "completeBackObj", "addEventListener", "EventObject", "COMPLETE", "setTimeout", "_play", "renderDragonbonesByBundle", "renderDragonbones", "__awaiter", "thisArg", "_arguments", "P", "generator", "resolve", "reject", "fulfilled", "step", "rejected", "then", "__generator", "body", "f", "y", "t", "_", "label", "sent", "trys", "ops", "g", "Iterator", "verb", "op", "pop", "measure", "propertyKey", "descriptor", "originalMethod", "args", "_i", "start", "finish", "performance", "randomInt", "min", "max", "randomFloat", "__decorate", "decorators", "desc", "getOwnPropertyDescriptor", "decorate", "envInfo", "DecoratorMemoize_1", "EnvInfo", "MACRO_ENV", "NativeBridge", "isNative", "MACRO_REMOTE_SERVER_RES_URL", "MACRO_PLATFORM", "Test", "gameInitUrl", "Prod", "envs", "curEnv", "os", "CC_JSB", "sys", "OS_ANDROID", "OS_IOS", "OS_WP8", "navigator", "userAgent", "_f", "_g", "_h", "_j", "version", "osVersion", "u", "isAndroid", "isIOS", "match", "is<PERSON><PERSON>", "memoize", "deepCopy", "gameData", "map", "row", "deepCopyArrayFrom", "deepCopySlice", "deepCopyLoop", "len", "rowLen", "newRow", "deepCopyFixed", "<PERSON><PERSON><PERSON><PERSON>", "srcRow", "actualRowLength", "ModuleEvent", "_callback", "getClass", "layerManager", "LayerManager", "gameUiLayer", "BASE_LAYER_CONFIGS", "getGameUiLayer", "resetGameUiLayer", "_gameUiLayer", "initNodeConfig", "addWidget", "type", "zIndex", "_createGameUiLayer", "layerScene", "setLayerNode", "scene", "opacity", "hidden", "prop", "layer", "bind", "layerMap", "init", "<PERSON><PERSON>", "instance", "clear", "add<PERSON><PERSON>er", "<PERSON><PERSON><PERSON>er", "delete", "<PERSON><PERSON><PERSON><PERSON>", "showLayer", "<PERSON><PERSON><PERSON><PERSON>", "setLayerOpacity", "getLayerTypes", "width", "height", "anchorX", "anchorY", "widget", "Widget", "isAlignTop", "isAlignLeft", "isAlignRight", "isAlignBottom", "top", "bottom", "left", "right", "audioInfo", "AudioType", "Storage_1", "AudioInfo", "volume", "isUndefined", "EFFECT", "audioSwitch", "storage", "getItem", "bgmSwitch", "SOUND", "AudioClip", "clip", "audioEngine", "playMusic", "stop", "stopAllEffects", "stopMusic", "shareObjects", "setPerformanceTieringType", "onDidTraitOnActive", "GBMTraitsMaps", "<PERSON><PERSON><PERSON><PERSON>", "ready", "_trait<PERSON>eady", "traitsConfigReady", "_traitConfigReady", "trait", "$traitClassName", "methodName", "has", "methodShareObject", "shareTraitTarget", "returnState", "returnValue", "RETURN_INIT_VALUE", "method", "decoratorMethod", "warn", "window", "traitClassInfo", "TraitConfigInfo_1", "traitConfigInfo", "traitsClassNameMap", "traitInst", "getOrCreateTraitInstance", "className", "getClassName", "props", "param", "keyValue", "isNaN", "_performanceTieringType", "id", "subTraits", "subTrait", "subTraitClassInfo", "traitName", "templateTraitRefList", "templateTraits", "templateTraitActiveState", "templateProps", "originalCaller", "listenerTraitOnActive", "onActive", "_onDidTraitOnActive", "fire", "shareTraitTargetReturnValue", "getNewTraitName", "originalTraitName", "char<PERSON>t", "toUpperCase", "GBM", "GBMTraitClass", "traitClassName", "templateTrait", "traitClassNameList", "templateTraitName", "Events_1", "Trait_1", "traitMaps", "Emitter", "event", "traitClass", "onCreate", "registerSubTraits", "subTraitsInst", "subTraitClass", "subTraitInst", "inst", "activeListeners", "<PERSON><PERSON><PERSON>", "activedListenerTraits", "i_4", "activeListener", "Storage", "cacheData", "initPrefix", "prefix", "setItem", "prefixedKey", "memoryValue", "valueType", "stringify", "storageValue", "valueTypeSplit", "localStorage", "DOMException", "code", "defaultValue", "item", "values", "remove", "removeItem", "decorator", "_target", "fnKey", "fn", "debounce", "delay", "timer<PERSON><PERSON>", "oldTimes", "curTime", "ScreenAdapter", "winsize", "view", "getVisibleSize", "_canvas", "fitHeight", "fit<PERSON><PERSON><PERSON>", "randomList", "list", "repeated", "useNewList", "temp", "shuffle", "ModuleType", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_moduleType", "setCurrentModuleType", "moduleType", "_moduleList", "resigerModule", "$modules", "_modules", "startModule", "l", "$moduleClass", "_moduleConstructorMaps", "_instance", "startProxy", "module", "proxyClassMap", "Common", "memoize<PERSON>ey", "writable", "traitConfigActiveBlockListInfo", "blockList", "ANDROID_ACTIVE", "send", "methodSignature", "jsb", "reflection", "callStaticMethod", "isString", "isUndefinedOrNull", "isStringArray", "isArray", "every", "elem", "RegExp", "isTypedArray", "TypedArray", "getPrototypeOf", "Uint8Array", "isNumber", "isIterable", "isBoolean", "isDefined", "arg", "isFunction", "getType", "cacheComponents", "ModuleManager_1", "CacheComponents", "_cacheComponents", "componentEnabledIntercept", "_componentOnEnabledIntercept", "_originalCompSchedulerOnEnabled", "director", "_onEnabled", "compConstructor", "componnets", "_cacheAllComponents", "allResolves", "_resolves", "resolves", "Cinst", "CompClass", "components", "CinstAsync", "compClass", "restoreComponentScheduler", "prefabInfo", "PrefabInfo", "_prefabs", "getOrCreatePrefabInstantiate", "prefab", "clearAllCache", "DeferredPromise", "_resolved", "_promiseQueue", "wait", "getKeyByValue", "enumObj", "isValueInEnum", "classId", "objOrCtor", "js", "executeRenderingOptimize", "CC_EDITOR", "_active", "self", "hasFullScreenComponent", "__fullscreen__", "hide_1", "Scene", "children", "__inactives__", "executeFullScreenComponent", "parent_1", "_parent", "_activeInHierarchy", "activateNode", "applyAdapterFringe", "size", "getFrameSize", "size2", "<PERSON><PERSON><PERSON><PERSON>", "IPHONE_X_HEIGHT_WIDTH_RATIO", "is<PERSON>ringe", "IPHONE_X_TOP_ADJUSTMENT", "__values", "s", "equal", "obj1", "obj2", "visited", "e_1", "e_2", "visited_1", "visited_1_1", "entry", "e_1_1", "return", "Number", "Boolean", "valueOf", "keysA", "keysB", "keysA_1", "keysA_1_1", "e_2_1", "TraitConfigActiveBlockListInfo_1", "TraitConfigInfo", "_traits", "_traitsById", "_useTraits", "_cacheUsedTraits", "_traitsClassNameMap", "_useActiveUserTraits", "_cacheUsedActiveUserTraits", "_configReady", "_ready", "initialize", "cfg", "feature", "traitItemInfoList", "traitItemInfo", "createUseTraits", "_curActiveConfig", "features", "plan", "stableTranslateGameWayNum", "gameWayNum", "parseInt", "createActiveTraits", "ActiveFeatures", "createActiveTraitClassNameMaps", "blockFeatures", "some", "traitIdInfo", "createWhiteTraitClassNameMaps", "whiteList", "i_5", "createExtraTraitClassNameMaps", "extraList", "i_6", "name_1", "loadDynamicTrait", "dynamicTraitList", "i_7", "traitData", "featureObj", "uid", "traitActiveUserData", "adapterFringe", "nodeNames", "nodeName", "AdapterFringe_1", "first", "promiseFactories", "shouldStop", "loop", "factory", "firstParallel", "promiseList", "todo", "promiseList_1", "promiseList_1_1", "cancel", "promiseList_2", "promiseList_2_1", "catch", "next<PERSON><PERSON><PERSON>", "callLater", "func", "thisObj", "once", "Director", "EVENT_AFTER_UPDATE", "timeout", "clearTimeout", "throttle", "immediate", "oldtime", "timer", "context", "newtime", "throttleCall", "ObjectPool", "_create", "_reset", "_option", "pool", "inUsed", "Set", "_init", "onCreateComplete", "error_1", "add", "isInUsed", "asyncGet", "error_2", "release", "Timeout<PERSON><PERSON>rier", "Barrier", "_isOpen", "_promise", "_completePromise", "open", "autoOpenTimeMs", "_timeout", "EventVo", "_id", "_uniqueID", "__spread<PERSON><PERSON>y", "to", "pack", "_decorator", "ccclass", "Component", "property", "_state", "setState", "state", "shouldUpdate", "shouldComponentUpdate", "componentWillUpdate", "render", "source", "componentWillMount", "componentDidMount", "componentWillUnmount", "nextState", "componentDidUpdate", "prevState", "getUrlParameterValue", "paramName", "globalThis", "location", "vars", "search", "substring", "pair", "decodeURIComponent", "task", "Task", "taskList", "run", "<PERSON><PERSON><PERSON><PERSON>", "requestIdleCallback", "startTime", "didTimeout", "timeRemaining", "cancelIdleCallback", "taskHandle", "idleRequestCallback", "deadline", "task_1", "waitFor", "<PERSON><PERSON><PERSON>", "_waitFors", "_resolve", "_reject", "promise", "end", "waitInfo", "endTime", "resolve_1", "reject_1", "interval", "setInterval", "clearInterval", "_props", "_subTraits", "_activedListenerTraits", "_condition", "eval", "onEnable", "onDisable", "getSubTraits", "onTraitActive", "traitConstructor", "preActive", "actived", "UI", "GameLayer_1", "setModalPrefab", "modalConfig", "loadPrefabAsset", "bundleConfig", "asyncLoadByBundle", "asyncLoad", "hide<PERSON>ll", "uiCache", "uiIsLoading", "uiIsCancelLoad", "lastPage", "activeState", "cancelLoad", "clearCache", "events", "removeEventListener", "splice", "show", "ui", "modalAsset", "modalNode_1", "createEvents", "modalNode", "panelEase", "openEvents", "currentResolves", "INSTANTIATE", "page", "hide", "ease", "modal", "on", "EventType", "TOUCH_START", "setParent", "setSiblingIndex", "childrenCount", "addUICache", "cacheUI", "cacheNode", "hideUI", "onCloseEaseComplete", "dispatchClose", "closeEvents", "ele", "sequence", "interruptConfition", "results", "<PERSON><PERSON><PERSON><PERSON>", "checkInterruptCondition", "isInterrupt", "fill", "reason", "DotUploadType", "createDotData", "uploadType", "eventKey", "params", "traits", "time", "nowWayArr", "ABStatus", "_gameWayNum", "active_waynum", "pici", "assign", "SHUSHU", "traitDotInfo", "class", "DecoratorTrait_1", "getTraitActiveStatus", "EventManager_1", "EventVo_1", "$module", "_eventsConstructorMaps", "_module", "onInit", "registerEvents", "getModule", "receivedEvents", "$event", "startEvents", "_events", "eventConstructor", "_eventVo", "eventClass", "EventManager", "registerEvent", "dispatchModuleEvent", "dispatchModuleEventAsync", "gameStateManager", "PrefabInfo_1", "CacheRender_1", "CacheComponents_1", "UI_1", "GameStateManager", "_resettableObjects", "_initialized", "registerResettable", "resettable", "unregisterResettable", "onGameExit", "clearUICache", "clear<PERSON><PERSON>er<PERSON>anager", "resetAllObjects", "clearModuleSystem", "clearResourceLoader", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "clearCacheComponents", "clearPrefabCache", "clearEventManager", "onGameEnter", "reinitializeAllObjects", "reinitialize", "clearTraitSystem", "__traitMaps__", "__shareObjects__", "forceReset", "HttpType", "CrytoType", "http", "Http", "callbackId", "callbacks", "requestAsync", "request", "success", "fail", "xhr", "loader", "getXMLHttpRequest", "dataStr", "contentType", "setRequestHeader", "onreadystatechange", "readyState", "response", "responseText", "status", "crypto", "Limiter", "maxDegreeOfParalellism", "_size", "_isDisposed", "outstandingPromises", "runningPromises", "_onDrained", "queue", "consume", "iLimitedTask", "consumed", "dispose", "BundleName", "parseBundleConfig", "bundleNameType", "generate<PERSON>ache<PERSON>ey", "baseKey", "clearEventListeners", "event_1", "clearResourceCache", "cacheResources", "cache", "onCompletes", "isRemote", "test", "getAsset", "paths", "self_1", "_complete", "loadEvents", "assetManager", "loadRemote", "resources", "assets", "loadBundle", "bundle", "res", "cache<PERSON>ey", "bundlePreloadScene", "scene<PERSON><PERSON>", "preloadScene", "bundlePreload", "preload", "items", "i_8", "renderSprite", "sprite", "SpriteFrame", "spriteFrame", "renderSpriteByBundle", "dragonAssetAtlasUrl", "playtimes", "loadCount", "<PERSON><PERSON><PERSON><PERSON>", "DragonBonesAsset", "DragonBonesAtlasAsset", "dragonAtlasAsset", "completeRemove", "<PERSON><PERSON><PERSON>", "_registerProxys", "_proxyClassMap", "registerProxys", "getProxy", "$proxyClass", "proxyClass", "_proxyInstance", "hotUpdate", "LOCAL_GAME_VERSION_KEY", "HotUpdateEventCodeEnum", "EnvInfo_1", "HotUpdate", "currentFileNum", "currentFileTotal", "projectManifestStr", "updating", "failCount", "am", "firstVersionCompare", "_storagePath", "_callbacks", "_emit", "cb", "onHotUpdateState", "versionCompareHandle", "versionA", "versionB", "vA", "vB", "a", "VERSION_COMPARE", "eventCode", "msg", "checkRemoteFileExists", "updateRemoteUrl", "manifest", "remoteVersionConfig", "zip", "useZip", "zipUrl", "url1", "url2", "url3", "hotServerUrl", "getStorageManifest", "storageManifestUrl", "fileUtils", "isFileExist", "storageManifestStr", "getStringFromFile", "updateStorageRemoteUrl", "storageManifestJson", "tempStr", "writeStringToFile", "setStorage", "getStorage", "startUpdate", "projectManifest", "remoteVersion", "useVersion", "storageManifest", "compareNumber", "localManifest", "manifestRoot", "packageUrl", "manifestUrl", "versionUrl", "START", "getWritablePath", "FAIL", "<PERSON><PERSON><PERSON>ana<PERSON>", "setVerifyCallback", "compressed", "md5", "getState", "State", "UNINITED", "Manifest", "loadLocalManifest", "getLocalManifest", "getVersion", "getManifestRoot", "getPackageUrl", "getManifestFileUrl", "getVersionFileUrl", "isLoaded", "setEventCallback", "versionCheck", "checkUpdate", "getEventCode", "eventCodeStr", "isFailed", "EventAssetsManager", "ERROR_NO_LOCAL_MANIFEST", "ERROR_DOWNLOAD_MANIFEST", "ERROR_PARSE_MANIFEST", "ALREADY_UP_TO_DATE", "NEW_VERSION_FOUND", "getTotalBytes", "jsbEvent", "getMessage", "updateCb", "update", "isUpdateFinished", "UPDATE_FINISHED", "ASSET_UPDATED", "getAssetId", "UPDATE_PROGRESSION", "downloadFiles", "getDownloadedFiles", "totalFiles", "getTotalFiles", "downloadBytes", "getDownloadedBytes", "totalBytes", "UPDATE_FAILED", "downloadFailedAssets", "ERROR_UPDATING", "removeFile", "ERROR_DECOMPRESS", "STOP_WHENUPDATE", "manifestSearchPaths", "getSearchPaths", "eventVo", "proxyModuleType", "_eventAllCompleted", "mt", "onEventAllCompleted", "options", "_options", "_event", "thisArgs", "onWillAddFirstListener", "onDidAddFirstListener", "onDidAddListener", "_disposed", "onDidRemoveLastListener", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "__webpack_modules__", "falcon", "DragonbonesAnim_1", "arrays_1", "Barrier_1", "DeferredPromise_1", "First_1", "Limiter_1", "Sequence_1", "WaitFor_1", "AudioInfo_1", "Component_1", "Copy_1", "UrlCrypto_1", "Date_1", "DecoratorAdapter_1", "DecoratorDebounce_1", "DecoratorMeasure_1", "DecoratorScreen_1", "DecoratorThrottle_1", "Dot_1", "enum_1", "Equal_1", "HotUpdate_1", "Http_1", "Interval_1", "numbers_1", "RenderingOptimization_1", "ObjectPool_1", "Random_1", "Timeout_1", "Timer_1", "Task_1", "NativeBridge_1", "Url_1", "watch_1", "Module_1", "ModuleEvent_1", "Proxy_1", "GameStateManager_1", "adapter", "arrays", "async", "default", "copy", "date", "enumUtils", "equalUtils", "intervalUtils", "numbers", "timeoutUtils", "typeUtils", "watchUtils"], "sourceRoot": ""}