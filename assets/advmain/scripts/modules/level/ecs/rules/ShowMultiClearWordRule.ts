import BoardComponent from '../components/board/BoardComponent';
import { CellComponent } from '../components/board/CellComponent';
import NodeComponent from '../components/NodeComponent';
import { AudioId } from '../config/conf/EcsAudioConfig';
import { RelationName } from '../cores/center/EntityCenter/WorldRelation';
import { BoardLayer } from '../define/BoardDefine';
import { ECSEvent } from '../GameEvent';
import { TempleteType } from '../registry/templete/TempleteRegistry';
import { EcsViewId } from '../renderWorld/ECSViewBase';
import { ECSRuleBase, IECSRuleConfig } from './core/ECSRuleBase';

export interface IShowMultiClearWordRuleConfig extends IECSRuleConfig {
    /**消除特效视图id */
    ecsViewId: EcsViewId;
    /**动画名称 */
    color_name: Record<number, string>;
    /**激励词 */
    prompt: Record<number, string>;
    /**消除数量对应的音效ID映射 */
    audioMapping: Record<number, AudioId>;
    /**默认音效ID (当消除数量不在映射中时使用) */
    defaultAudioId: AudioId;
    audioId: AudioId;
}

/**多消激励词展示规则 */
export class ShowMultiClearWordRule extends ECSRuleBase<IShowMultiClearWordRuleConfig, ECSEvent.GameEvent.ELIMINATION> {
    public bindEventData() {
        return ECSEvent.GameEvent.ELIMINATION;
    }

    canExecute(): boolean {
        return true;
    }

    execute(): void {
        console.log('多消激励词展示');
        const world = this.world;
        const info = this.eventData;
        if (!info) return;

        const boardComp = world.getComponent(info.boardEntity, BoardComponent);
        if (!boardComp) return;

        // 获取颜色名称
        const colorName = this.getShapeColorName(info.putBlockBackInfo.entityId) ?? this.config.color_name[1];

        // 计算消除数量并获取对应提示词
        const clearCount = info.clearCol.size + info.clearRow.size;
        const prompt = this.getPromptByClearCount(clearCount);
        if (!prompt) return;

        // 组装动画名称
        const animationName = clearCount >= 8 ? `${prompt}` : `${prompt}${colorName}`;

        this.config.audioId = this.getAudioIdByClearCount(clearCount);

        // 创建特效
        this.createMultiClearEffect(boardComp, info.boardEntity, animationName);
    }

    /**
     * 根据消除数量获取提示词
     * @param clearCount 消除数量
     * @returns 提示词
     */
    private getPromptByClearCount(clearCount: number): string | undefined {
        const maxClearCount = Math.max(...Object.keys(this.config.prompt).map(Number));
        return this.config.prompt[Math.min(clearCount, maxClearCount)];
    }

    /**
     * 获取形状颜色名称
     * @param context 执行上下文
     * @param shapeId 形状实体ID
     * @returns 颜色名称
     */
    private getShapeColorName(shapeId: number): string | undefined {
        const world = this.world;
        const children = world.getTargets(shapeId, RelationName.PARENT_CHILD);
        if (children.length === 0) return undefined;

        const cellComp = world.getComponent(children[0], CellComponent);
        return cellComp ? this.config.color_name[cellComp.oriColor] : undefined;
    }

    private getAudioIdByClearCount(clearCount: number): AudioId | undefined {
        const sortedKeys = Object.keys(this.config.audioMapping)
            .map(Number)
            .sort((a, b) => b - a);
        for (const key of sortedKeys) {
            if (clearCount >= key) {
                return this.config.audioMapping[key];
            }
        }
        return this.config.defaultAudioId;
    }

    /**
     * 创建多消特效
     * @param context 执行上下文
     * @param boardComp 棋盘组件
     * @param boardEntity 棋盘实体
     * @param animationName 动画名称
     */
    private createMultiClearEffect(boardComp: BoardComponent, boardEntity: number, animationName: string): void {
        const info = this.eventData;
        if (!info) return;

        // 消除区域的中心位置
        const centerPos = info.clearAreaCenterPos;
        if (!centerPos) return;

        // 在消除区域中心位置创建特效
        this.createEffectAtPosition(boardComp, boardEntity, centerPos, animationName);
    }

    /**
     * 在指定位置创建特效
     * @param context 执行上下文
     * @param boardComp 棋盘组件
     * @param boardEntity 棋盘实体
     * @param position 位置坐标
     * @param animationName 动画名称
     */
    private createEffectAtPosition(boardComp: BoardComponent, boardEntity: number, position: { x: number; y: number }, animationName: string): void {
        // 计算缩放和边界约束
        const scaleFactor = Math.max(boardComp.rCount, boardComp.cCount) / 8; // 基于棋盘大小调整缩放
        const world = this.world;
        const constrainedPosition = this.constrainPositionToBoardBounds(boardComp, position, scaleFactor);

        const effectEntity = world.templeteCenter.createTempleteEntity(
            TempleteType.Render,
            {
                parentEntity: boardEntity,
                ecsViewId: this.config.ecsViewId,
            },
            {
                nodeParam: { x: constrainedPosition.x, y: constrainedPosition.y, scaleX: scaleFactor },
                renderParam: {
                    data: {
                        aniName: animationName,
                        src: 'dragonbones/eliminate/duoxiao_word_ske',
                        dragonNames: ['duoxiao_word_tex', 'Armature'],
                        audioId: this.config.audioId,
                    },
                    childrenPaths: [BoardLayer[BoardLayer.Top]],
                    isBreakLink: true,
                },
            },
        );
        world.destroyEntity(effectEntity);
    }

    /**
     * 约束激励词位置在棋盘边界内
     * @param context 执行上下文
     * @param boardComp 棋盘组件
     * @param position 原始位置
     * @param scaleFactor 缩放因子
     * @returns 约束后的位置
     */
    private constrainPositionToBoardBounds(
        boardComp: BoardComponent,
        position: { x: number; y: number },
        scaleFactor: number,
    ): { x: number; y: number } {
        // 获取棋盘边界信息
        const boardBounds = this.getBoardBounds(boardComp);
        if (!boardBounds) return position;

        // 估算激励词的实际尺寸（基于缩放因子和预设尺寸）
        const estimatedWidth = 300 * scaleFactor; // 假设激励词基础宽度为200像素
        const estimatedHeight = 100 * scaleFactor; // 假设激励词基础高度为100像素

        // 计算激励词的半宽半高
        const halfWidth = estimatedWidth / 2;
        const halfHeight = estimatedHeight / 2;

        // 约束X坐标
        let constrainedX = position.x;
        if (constrainedX - halfWidth < boardBounds.left) {
            constrainedX = boardBounds.left + halfWidth;
        } else if (constrainedX + halfWidth > boardBounds.right) {
            constrainedX = boardBounds.right - halfWidth;
        }

        // 约束Y坐标
        let constrainedY = position.y;
        if (constrainedY - halfHeight < boardBounds.bottom) {
            constrainedY = boardBounds.bottom + halfHeight;
        } else if (constrainedY + halfHeight > boardBounds.top) {
            constrainedY = boardBounds.top - halfHeight;
        }

        return { x: constrainedX, y: constrainedY };
    }

    /**
     * 获取棋盘的边界信息
     * @param context 执行上下文
     * @param boardComp 棋盘组件
     * @returns 棋盘边界坐标
     */
    private getBoardBounds(boardComp: BoardComponent): { left: number; right: number; top: number; bottom: number } | null {
        const world = this.world;
        // 获取左上角和右下角格子的位置
        const topLeftSlot = world.getTargets(boardComp.entity, RelationName.PARENT_CHILD, `0_0`)[0];
        if (!topLeftSlot) return null;
        const bottomRightSlot = world.getTargets(boardComp.entity, RelationName.PARENT_CHILD, `${boardComp.rCount - 1}_${boardComp.cCount - 1}`)[0];
        if (!bottomRightSlot) return null;

        const topLeftNode = world.getComponent(topLeftSlot, NodeComponent);
        const bottomRightNode = world.getComponent(bottomRightSlot, NodeComponent);

        if (!topLeftNode || !bottomRightNode) return null;

        const cellSize =
            boardComp.cCount > 1
                ? Math.abs(
                      world.getComponent(world.getTargets(boardComp.entity, RelationName.PARENT_CHILD, `0_1`)[0], NodeComponent)?.x - topLeftNode.x,
                  ) || 60
                : 60;

        // 计算棋盘边界（考虑格子大小的一半作为边距）
        const halfCell = cellSize / 2;

        return {
            left: topLeftNode.x - halfCell,
            right: bottomRightNode.x + halfCell,
            top: topLeftNode.y + halfCell,
            bottom: bottomRightNode.y - halfCell,
        };
    }
}
