(()=>{"use strict";var __webpack_modules__={4164:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.applyAdapterFringe=function(e){if(e){var t=e.getComponent(cc.Widget);if(t){var o=cc.view.getFrameSize(),a=cc.view.getVisibleSize();if(function(e,t){return!(e.width/e.height>760/1334||cc.sys.os!==cc.sys.OS_ANDROID&&!cc.sys.isBrowser||!(e.height/e.width>r||t.height/t.width>r))}(o,a)){var i=Math.max(o.height,a.height);t.top+=n*i}}}};var r=1280/716,n=.037},4074:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.dragonbonesAnim=void 0;var n=r(3687),o=function(){function e(){this.dragonbones={}}return e.prototype.play=function(e,t,r,n,o){var a=this;if(o&&t&&r){void 0===n&&(n=1),o.bundleName;var i=o.dragonAssetUrl,s=o.dragonAtlasAssetUrl;if(o.frameSplitting,void 0!==i&&""!==i&&void 0!==s&&""!==s&&void 0!==r&&""!==r&&void 0!==t&&""!==t){var c=new cc.Node,u=c.addComponent(dragonBones.ArmatureDisplay);return e.addChild(c),u.enableBatch=!0,u.updateAnimationCache(r),u.setAnimationCacheMode(dragonBones.ArmatureDisplay.AnimationCacheMode.SHARED_CACHE),u&&(o.completeBack&&o.completeBackObj&&u.addEventListener(dragonBones.EventObject.COMPLETE,o.completeBack,o.completeBackObj),o.frameSplitting?setTimeout((function(){a._play(o,t,r,n,u)}),50):this._play(o,t,r,n,u)),c}}},e.prototype._play=function(e,t,r,o,a){var i=e.bundleName,s=e.dragonAssetUrl,c=e.dragonAtlasAssetUrl;e.frameSplitting,i?n.ResLoader.renderDragonbonesByBundle(i,a,s,c,t,r,o):n.ResLoader.renderDragonbones(a,s,c,t,r,o)},e}();t.dragonbonesAnim=new o},9305:function(e,t){var r=this&&this.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,a=r.call(e),i=[];try{for(;(void 0===t||t-- >0)&&!(n=a.next()).done;)i.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=a.return)&&r.call(a)}finally{if(o)throw o.error}}return i};Object.defineProperty(t,"__esModule",{value:!0}),t.arraysHaveSameElements=function(e,t){if(e.length!==t.length)return!1;for(var r=e.slice().sort(),n=t.slice().sort(),o=0;o<r.length;o++)if(r[o]!==n[o])return!1;return!0},t.shuffleArray=function(e){for(var t,n=e.slice(),o=n.length-1;o>0;o--){var a=Math.floor(Math.random()*(o+1));t=r([n[a],n[o]],2),n[o]=t[0],n[a]=t[1]}return n},t.arraysEqual=function(e,t){if(e.length!==t.length)return!1;for(var r=0;r<e.length;r++)if(e[r]!==t[r])return!1;return!0},t.ensureMaxLength=function(e,t,r){return e.length>=t&&e.shift(),e.push(r),e}},2796:function(e,t){var r,n=this&&this.__extends||(r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},r(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(t,"__esModule",{value:!0}),t.TimeoutBarrier=t.Barrier=void 0;var o=function(){function e(){var e=this;this._isOpen=!1,this._promise=new Promise((function(t,r){e._completePromise=t}))}return Object.defineProperty(e.prototype,"isOpen",{get:function(){return this._isOpen},enumerable:!1,configurable:!0}),e.prototype.reset=function(){this._isOpen=!1},e.prototype.open=function(){this._isOpen=!0,this._completePromise(!0)},e.prototype.wait=function(){return this._promise},e}();t.Barrier=o;var a=function(e){function t(t){var r=e.call(this)||this;return r._timeout=setTimeout((function(){return r.open()}),t),r}return n(t,e),t.prototype.open=function(){clearTimeout(this._timeout),e.prototype.open.call(this)},t}(o);t.TimeoutBarrier=a},2963:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DeferredPromise=void 0;var r=function(){function e(){this._resolved=!1,this._promiseQueue=[]}return e.prototype.wait=function(){var e=this;return new Promise((function(t,r){if(e._resolved)return t(e._resolvedValue),void(e._resolved=!1);e._promiseQueue.push({resolve:t,reject:r})}))},e.prototype.resolve=function(e){0===this._promiseQueue.length&&this._resolved&&console.warn("DeferredPromise:  resolve 之前没有添加 wait,顺序可能有问题！"),this._resolved=!0,this._resolvedValue=e;for(var t=this._promiseQueue.length;this._promiseQueue.length>0;)(0,this._promiseQueue.shift().resolve)(e);t&&(this._resolved=!1)},e.prototype.reject=function(e){for(;this._promiseQueue.length>0;)(0,this._promiseQueue.shift().reject)(e)},e}();t.DeferredPromise=r},4157:function(e,t){var r=this&&this.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(t,"__esModule",{value:!0}),t.first=function(e,t,r){void 0===t&&(t=function(e){return!!e}),void 0===r&&(r=null);var n=0,o=e.length,a=function(){if(n>=o)return Promise.resolve(r);var i=e[n++];return Promise.resolve(i()).then((function(e){return t(e)?Promise.resolve(e):a()}))};return a()},t.firstParallel=function(e,t,n){if(void 0===t&&(t=function(e){return!!e}),void 0===n&&(n=null),0===e.length)return Promise.resolve(n);var o=e.length,a=function(){var t,n,a,i;o=-1;try{for(var s=r(e),c=s.next();!c.done;c=s.next())null===(i=(a=c.value).cancel)||void 0===i||i.call(a)}catch(e){t={error:e}}finally{try{c&&!c.done&&(n=s.return)&&n.call(s)}finally{if(t)throw t.error}}};return new Promise((function(i,s){var c,u;try{for(var l=r(e),p=l.next();!p.done;p=l.next())p.value.then((function(e){--o>=0&&t(e)?(a(),i(e)):0===o&&i(n)})).catch((function(e){--o>=0&&(a(),s(e))}))}catch(e){c={error:e}}finally{try{p&&!p.done&&(u=l.return)&&u.call(l)}finally{if(c)throw c.error}}}))}},9959:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Limiter=void 0;var n=r(1367),o=function(){function e(e){this._size=0,this._isDisposed=!1,this.maxDegreeOfParalellism=e,this.outstandingPromises=[],this.runningPromises=0,this._onDrained=new n.Emitter}return Object.defineProperty(e.prototype,"onDrained",{get:function(){return this._onDrained.event},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"size",{get:function(){return this._size},enumerable:!1,configurable:!0}),e.prototype.queue=function(e){var t=this;if(this._isDisposed)throw new Error("Object has been disposed");return this._size++,new Promise((function(r,n){t.outstandingPromises.push({factory:e,c:r,e:n}),t.consume()}))},e.prototype.consume=function(){for(var e=this;this.outstandingPromises.length&&this.runningPromises<this.maxDegreeOfParalellism;){var t=this.outstandingPromises.shift();this.runningPromises++;var r=t.factory();r.then(t.c,t.e),r.then((function(){return e.consumed()}),(function(){return e.consumed()}))}},e.prototype.consumed=function(){this._isDisposed||(this.runningPromises--,0==--this._size&&this._onDrained.fire(),this.outstandingPromises.length>0&&this.consume())},e.prototype.clear=function(){if(this._isDisposed)throw new Error("Object has been disposed");this.outstandingPromises.length=0,this._size=this.runningPromises},e.prototype.dispose=function(){this._isDisposed=!0,this.outstandingPromises.length=0,this._size=0,this._onDrained.dispose()},e}();t.Limiter=o},8278:function(e,t){var r=this&&this.__awaiter||function(e,t,r,n){return new(r||(r=Promise))((function(o,a){function i(e){try{c(n.next(e))}catch(e){a(e)}}function s(e){try{c(n.throw(e))}catch(e){a(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(i,s)}c((n=n.apply(e,t||[])).next())}))},n=this&&this.__generator||function(e,t){var r,n,o,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},i=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return i.next=s(0),i.throw=s(1),i.return=s(2),"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(c){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,c])}}},o=this&&this.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,a=r.call(e),i=[];try{for(;(void 0===t||t-- >0)&&!(n=a.next()).done;)i.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=a.return)&&r.call(a)}finally{if(o)throw o.error}}return i},a=this&&this.__spreadArray||function(e,t,r){if(r||2===arguments.length)for(var n,o=0,a=t.length;o<a;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.sequence=function(e,t){var i=[],s=0,c=e.length;return Promise.resolve(null).then((function u(l){return function(){return r(this,void 0,void 0,(function(){return n(this,(function(e){switch(e.label){case 0:return t?[4,t()]:[3,2];case 1:return[2,e.sent()];case 2:return[2,!1]}}))}))}().then((function(t){if(t)return c>=i.length&&i.push.apply(i,a([],o(new Array(c-i.length).fill(void 0)),!1)),Promise.resolve(i);null!=l&&i.push(l);var r=s<c?e[s++]():null;return r?r.then(u).catch((function(e){return t||i.push.apply(i,a([],o(new Array(c-i.length).fill(void 0)),!1)),Promise.resolve(i)})):Promise.resolve(i)}))}))}},5519:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.waitFor=void 0;var r=function(){function e(){this._waitFors={}}return e.prototype.start=function(e){var t,r;this._waitFors[e]={startTime:Date.now(),promise:new Promise((function(e,n){t=e,r=n})),state:!1},this._waitFors[e].resolve=t,this._waitFors[e].reject=r},e.prototype.end=function(e){var t=this._waitFors[e];t?(t.endTime=Date.now(),t.state=!0):console.error("waitFor ".concat(e," end 时需先执行 start!"))},e.prototype.wait=function(e,t){var r=this._waitFors[e];if(r){var n=r.resolve,o=r.reject;return r.state?n&&n(r.state):r.interval=setInterval((function(){r.state?(clearInterval(r.interval),n(e)):t&&!isNaN(t.timeout)&&Date.now()-r.startTime>t.timeout&&(clearInterval(r.interval),o("wait for ".concat(e," is timeout!")))}),16),r.promise}},e}();t.waitFor=new r},9787:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.audioInfo=t.AudioType=void 0;var n,o=r(3687),a=r(259),i=r(5447);!function(e){e[e.SOUND=0]="SOUND",e[e.EFFECT=1]="EFFECT"}(n||(t.AudioType=n={}));var s=function(){function e(){}return e.prototype.play=function(e){var t=this;if(e){var r=e.url,s=e.volume,c=e.type,u=e.bundleName;if(r){(0,a.isUndefined)(c)&&(c=n.EFFECT);var l=i.storage.getItem("audioSwitch",!0),p=i.storage.getItem("bgmSwitch",!1);c===n.EFFECT&&!l||c===n.SOUND&&!p||(void 0===s&&(s=1),u?o.ResLoader.loadByBundle(u,r,cc.AudioClip,(function(e,r){t._play(c,s,e,r)})):o.ResLoader.load(r,cc.AudioClip,(function(e,r){t._play(c,s,e,r)})))}}else CC_DEBUG&&console.error("声音播放时播放时选项不能为空！")},e.prototype._play=function(e,t,r,o){r||(e==n.EFFECT?cc.audioEngine.play(o,!1,t):cc.audioEngine.playMusic(o,!0))},e.prototype.stop=function(e){e.url,e.volume;var t=e.type;(0,a.isUndefined)(t)&&(t=n.EFFECT);var r=i.storage.getItem("audioSwitch",!0),o=i.storage.getItem("bgmSwitch",!1);t===n.EFFECT&&!r||t===n.SOUND&&!o||(t==n.EFFECT?cc.audioEngine.stopAllEffects():cc.audioEngine.stopMusic())},e}();t.audioInfo=new s},4223:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.cacheRender=void 0;var n=r(3687),o=function(){function e(){this._caches={}}return e.prototype.createOrUpdateCacheListComponents=function(e){var t=this;if(e){var r=e.prefabUrl,o=e.bundleName,a=e.count,i=e.typeOrClassName,s=e.parent;if(!(a<=0)&&s)return new Promise((function(e,c){var u=function(n,o){if(n)c(n);else{var u=t._caches,l=u[r];l||(u[r]=[],l=u[r]);var p=l.length;if(p>a)for(var f=a;f<p;f++)(_=l[f]).node.active=!1;for(var d=[],h=0;h<a;h++){var _;if(_=l[h])_.node.active=!0,_.node.parent!==s&&(_.node.parent=s),d[h]=_;else{var v=cc.instantiate(o);l[h]=v.getComponent(i),s.addChild(v),d[h]=l[h]}}e(d)}};o?n.ResLoader.loadByBundle(o,r,cc.Prefab,u):n.ResLoader.load(r,cc.Prefab,u)}))}},e}();t.cacheRender=new o},1551:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.cacheComponents=void 0;var r=function(){function e(){e._cacheComponents=this}return e.prototype.componentEnabledIntercept=function(){if(!e._componentOnEnabledIntercept){e._componentOnEnabledIntercept=!0;var t=cc.director._compScheduler._onEnabled;cc.director._compScheduler._onEnabled=function(r){e._cacheAllComponents.set(r.constructor,r),t.apply(this,[r]);var n=e._resolves.get(r.constructor);if(n){for(var o=0;o<n.length;o++)(0,n[o])(r);n.length=0}}}},e.prototype.Cinst=function(t){var r=e._cacheAllComponents.get(t);if(r)return r},e.prototype.CinstAsync=function(t){return new Promise((function(r,n){var o=e._cacheComponents.Cinst(t);if(o)r(o);else{var a=e._resolves.get(t);a?a.push(r):(a=[r],e._resolves.set(t,a)),e._cacheComponents.componentEnabledIntercept()}}))},e._cacheAllComponents=new Map,e._resolves=new Map,e}();t.cacheComponents=new r},2766:function(e,t){var r,n=this&&this.__extends||(r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},r(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),o=this&&this.__decorate||function(e,t,r,n){var o,a=arguments.length,i=a<3?t:null===n?n=Object.getOwnPropertyDescriptor(t,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)i=Reflect.decorate(e,t,r,n);else for(var s=e.length-1;s>=0;s--)(o=e[s])&&(i=(a<3?o(i):a>3?o(t,r,i):o(t,r))||i);return a>3&&i&&Object.defineProperty(t,r,i),i},a=this&&this.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,a=r.call(e),i=[];try{for(;(void 0===t||t-- >0)&&!(n=a.next()).done;)i.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=a.return)&&r.call(a)}finally{if(o)throw o.error}}return i},i=this&&this.__spreadArray||function(e,t,r){if(r||2===arguments.length)for(var n,o=0,a=t.length;o<a;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0});var s=cc._decorator,c=s.ccclass,u=(s.property,function(e){function t(){var t=e.apply(this,i([],a(arguments),!1))||this;return t._state={},t}return n(t,e),Object.defineProperty(t.prototype,"state",{get:function(){return this._state},enumerable:!1,configurable:!0}),t.prototype.setState=function(e,t,r){if(this._state!==e){var n=!0;this._state&&(n=this.shouldComponentUpdate(e)),"[object Object]"!==Object.prototype.toString.call(e)?this._state=e:(this._state||(this._state=Object.create(null)),t?this._state=e:this.deepCopy(this._state,e)),n&&(this.componentWillUpdate(this._state),this.render(),r&&r())}},t.prototype.deepCopy=function(e,t){for(var r in t){var n=t[r];e[r]=n}},t.prototype.componentWillMount=function(){},t.prototype.componentDidMount=function(){},t.prototype.componentWillUnmount=function(){},t.prototype.shouldComponentUpdate=function(e){return!0},t.prototype.componentWillUpdate=function(e){},t.prototype.componentDidUpdate=function(e){},t.prototype.render=function(){},o([c],t)}(cc.Component));t.default=u},2983:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.deepCopy=function(e){return e.length<80?e.map((function(e){return e.slice()})):e.map((function(e){return Array.from(e)}))},t.deepCopyArrayFrom=function(e){return e.map((function(e){return Array.from(e)}))},t.deepCopySlice=function(e){return e.map((function(e){return e.slice()}))},t.deepCopyLoop=function(e){for(var t=[],r=e.length,n=0;n<r;n++){for(var o=e[n],a=o.length,i=new Array(a),s=0;s<a;s++)i[s]=o[s];t[n]=i}return t},t.deepCopyFixed=function(e,t){for(var r=[],n=e.length,o=0;o<n;o++){for(var a=e[o],i=t||a.length,s=new Array(i),c=0;c<i;c++)s[c]=a[c];r[o]=s}return r}},9534:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ICrypto=void 0;var r=function(){function e(){}return e.encrypt=function(e){return e},e.decrypt=function(e){return e},e}();t.ICrypto=r},9768:function(e,t,r){var n,o=this&&this.__extends||(n=function(e,t){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},n(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)});Object.defineProperty(t,"__esModule",{value:!0}),t.UrlCrypto=void 0;var a=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return o(t,e),t.encrypt=function(e){var t=Buffer.from(e).toString("base64");return this._des(t)},t.decrypt=function(e){var t=this._des(e);return Buffer.from(t,"base64").toString("utf-8")},t._des=function(e){for(var t=e.replace(/\s+/g,"+"),r=t.length,n=this.encodeKey.length,o="",a=0;a<r;a++){var i=t[a],s=this.encodeKey.indexOf(i);-1!==s&&(o+=this.encodeKey[n-s-1])}return o},t.encodeKey="C1eWgtN/ZOJ=qw2TXyhxjV+0SlUL35R6ri9G4uamPfQpK78AdHbBczFnYEskMDIvo",t}(r(9534).ICrypto);t.UrlCrypto=a},301:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getLastSomeDays=function(e){void 0===e&&(e=7);for(var t=Date.now(),r=[],n=1;n<=e;n++){var o=new Date;o.setTime(t-864e5*n),r.push(o.getFullYear()+"_"+o.getMonth()+"_"+o.getDate())}return r},t.getTodayDate=function(){var e=new Date;return"".concat(e.getFullYear(),"_").concat(e.getMonth()+1,"_").concat(e.getDate())},t.getDiffDays=function(e,t){var r=new Date(e);r.setHours(0,0,0,0);var n=new Date(t);n.setHours(0,0,0,0);var o=Math.abs(r.getTime()-n.getTime());return Math.ceil(o/864e5)}},3571:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.adapterFringe=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return function(t,r,o){var a=o.value;return o.value=function(){for(var t=this,r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];return e.forEach((function(e){var r=t[e];r&&(0,n.applyAdapterFringe)(r)})),a.apply(this,r)},o}};var n=r(4164)},6577:(e,t)=>{function r(e){return function(t){t.prototype.__classname__||(t.prototype.__classname__=e)}}function n(e){return cc.js.getClassName(e)}Object.defineProperty(t,"__esModule",{value:!0}),t.classId=r,t.getClassName=n,window.classId=r,window.getClassName=n},8425:(e,t)=>{function r(e){return function(t,r,n){var o=null,a=null;if("function"==typeof n.value?(o="value",a=n.value):"function"==typeof n.get&&(o="get",a=n.get),!a||!o)throw new Error("not supported");n[o]=e(a,r)}}Object.defineProperty(t,"__esModule",{value:!0}),t.decorate=r,t.debounce=function(e,t){return void 0===e&&(e=1e3),r((function(r,n){var o="$debounce$".concat(n),a={};return a[o]=-1e7,function(){for(var n=[],i=0;i<arguments.length;i++)n[i]=arguments[i];var s=Date.now();if(s-a[o]>=e)return a[o]=s,r.apply(this,n);"string"!=typeof t&&"number"!=typeof t||console.warn(t)}}))}},7136:function(e,t){var r=this&&this.__awaiter||function(e,t,r,n){return new(r||(r=Promise))((function(o,a){function i(e){try{c(n.next(e))}catch(e){a(e)}}function s(e){try{c(n.throw(e))}catch(e){a(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(i,s)}c((n=n.apply(e,t||[])).next())}))},n=this&&this.__generator||function(e,t){var r,n,o,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},i=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return i.next=s(0),i.throw=s(1),i.return=s(2),"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(c){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,c])}}};Object.defineProperty(t,"__esModule",{value:!0}),t.measure=function(e,t,o){var a=o.value;return o.value=function(){for(var e=[],o=0;o<arguments.length;o++)e[o]=arguments[o];return r(this,void 0,void 0,(function(){var r,o,i;return n(this,(function(n){switch(n.label){case 0:return CC_DEBUG?(r=performance.now(),[4,a.apply(this,e)]):[3,2];case 1:return o=n.sent(),i=performance.now(),console.log("".concat(t," execution time: ").concat(i-r," milliseconds")),[2,o];case 2:return[2,a.apply(this,e)]}}))}))},o}},7084:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.memoize=function(e,t,r){var n=null,o=null;if("function"==typeof r.value?(n="value",0!==(o=r.value).length&&console.warn("Memoize should only be used in functions with zero parameters")):"function"==typeof r.get&&(n="get",o=r.get),!o)throw new Error("not supported");var a="$memoize$".concat(t);r[n]=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return this.hasOwnProperty(a)||Object.defineProperty(this,a,{configurable:!1,enumerable:!1,writable:!1,value:o.apply(this,e)}),this[a]}}},3318:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ScreenAdapter=function(){return function(e,t,r){var n=r.value;return r.value=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=cc.view.getVisibleSize();if(r.height/r.width>=1334/750)(o=cc.Canvas.instance).fitHeight=!1,o.fitWidth=!0;else if(r.height/r.width<1334/750){var o;(o=cc.Canvas.instance).fitHeight=!0,o.fitWidth=!1}return n.apply(this,e)},r}}},7202:(e,t)=>{function r(e){return function(t,r,n){var o=null,a=null;if("function"==typeof n.value?(o="value",a=n.value):"function"==typeof n.get&&(o="get",a=n.get),!a||!o)throw new Error("not supported");n[o]=e(a,r)}}Object.defineProperty(t,"__esModule",{value:!0}),t.decorate=r,t.throttle=function(e,t){return void 0===e&&(e=500),void 0===t&&(t=!0),r((function(r,n){var o=Date.now(),a=null;return function(){for(var n=[],i=0;i<arguments.length;i++)n[i]=arguments[i];var s=this;if(t){var c=Date.now();c-o>=e&&(r.apply(s,n),o=c)}else clearTimeout(a),a=setTimeout((function(){r.apply(s,n)}),e)}}))},t.throttleCall=function(e,t,r){void 0===t&&(t=500),void 0===r&&(r=!0);var n=Date.now(),o=null;return function(){for(var a=[],i=0;i<arguments.length;i++)a[i]=arguments[i];var s=this;if(r){var c=Date.now();c-n>=t&&(e.apply(s,a),n=c)}else clearTimeout(o),o=setTimeout((function(){e.apply(s,a)}),t)}}},7128:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.shareObjects=t.setPerformanceTieringType=t.onDidTraitOnActive=t.GBMTraitsMaps=t.traitMaps=t.decoratorTraitsClassNameMap=void 0,t.trait=function(e,r){return void 0===r&&(r=""),function(r,n,o){t.shareObjects.has(r)||t.shareObjects.set(r,{});var l=t.shareObjects.get(r);l[n]||(l[n]={}),void 0===o.__originalMethod__&&(o.__originalMethod__=o.value);var p=l[n];p.returnState=!1,p.replace=!1,p.methodName=n,p.method=o.__originalMethod__;var f=t.traitMaps.get(e);if(!f){(f=new e)._state=f.data(),f.onCreate();var d=f.registerSubTraits();if(d){for(var h=[],_=0;_<d.length;_++){var v=d[_];if(t.traitMaps.has(v))h[_]=t.traitMaps.get(v);else{var y=new v;y._state=y.data(),y.onCreate(),h[_]=y,t.traitMaps.set(v,y)}}f._subTraits=h}t.traitMaps.set(e,f)}var g=getClassName(e);g&&!t.decoratorTraitsClassNameMap[g]&&(t.decoratorTraitsClassNameMap[g]=f);var m=o.value;p.lastMethod=m,o.value=function(){for(var e,t=this,r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];m===p.lastMethod&&(p.replace=!1,p.returnState=!1,p.data={},p.args=[],p.returnValue=void 0),p.target=this,p.className||(p.className=getClassName(this.constructor));var l=a.traitConfigInfo.traitsClassNameMap[g],d=null==l?void 0:l.param;if(d){var h=Object.keys(d)[0];isNaN(+h)?d.active=!0:d=d[c],f._active||(f._id=l.id,f._props=d,f._active=!0,f.onEnable());var _=f._subTraits;if(_)for(var v=0;v<_.length;v++){var y=_[v],b=a.traitConfigInfo.traitsClassNameMap[y.traitName];b&&(y._props=b.param)}}var C=i[g],E=!1;if(C)for(var M=0;M<C.length;M++){var T=null===(e=a.traitConfigInfo.traitsClassNameMap[C[M]])||void 0===e?void 0:e.param;if(T&&0!==Object.keys(T).length){E=!0;break}}if(!f.active&&!E||(p.returnState||(p.args=r,p.originalCaller=function(){m.apply(t,r)},this.shareTraitTarget=p,u(f,"preActive"),f.onActive(p),(CC_DEBUG||CC_DEV)&&(activeTraits[g]=n),CC_DEBUG&&s.fire(f.id),u(f,"actived")),!p.replace)){var w=m.apply(this,r),O=p.returnValue;return void 0!==O?O:w}}}},t.GBM=function(){return function(e){var r=e;if(!t.traitMaps.has(r)){var n=new r;n._state=n.data(),n.onCreate(),t.traitMaps.set(e,n)}var o=getClassName(r);o&&!t.decoratorTraitsClassNameMap[o]&&(t.decoratorTraitsClassNameMap[o]=t.traitMaps.get(r)),t.GBMTraitsMaps.has(r)||t.GBMTraitsMaps.set(e,t.traitMaps.get(r))}},t.templateTrait=function(e){return function(t){if(e){var r=getClassName(t);r&&(i[r]=e)}}};var n=r(1367),o=r(47),a=r(3287);r(6577),t.decoratorTraitsClassNameMap={},t.traitMaps=new Map,t.GBMTraitsMaps=new Map;var i={},s=new n.Emitter;t.onDidTraitOnActive=s.event;var c=0;function u(e,t){var r=o.Trait.activedListenerTraits.get(e.contructor);if(r)for(var n=0;n<r.length;n++){var a=r[n];(null==a?void 0:a[t])&&a[t](e)}}t.setPerformanceTieringType=function(e){c=e},window.activeTraits={},t.shareObjects=new Map,window.TRAIT=function(e){return t.traitMaps.get(e)}},1479:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DotUploadType=void 0,t.createDotData=function(e,t,r,s,c){var u={time:Date.now()},l=o.storage.getItem("gameWayNum"),p=o.storage.getItem("nowWayArr",[]);(null==p?void 0:p.length)>0&&(u.ABStatus=p);var f=+l;isNaN(f)||(u.active_waynum={pici:Math.floor(f/100).toString(),1:f}),r||(r={}),r&&(u=Object.assign(r,u)),e===n.MARKET&&(u.name=t),s?function(e,t){return new Promise((function(r,n){for(var o=0;o<t.length;o++){var s=t[o],c=s.class,u=getClassName(c),l=a.decoratorTraitsClassNameMap[u];if(!l){var p=void 0;a.traitMaps.has(c)||((p=new c)._state=p.data(),p.onCreate(),a.traitMaps.set(c,p)),u&&!a.decoratorTraitsClassNameMap[u]&&(a.decoratorTraitsClassNameMap[u]=a.traitMaps.get(c)),l=a.decoratorTraitsClassNameMap[u];var f=i.traitConfigInfo.traitsClassNameMap[u],d=null==f?void 0:f.param;d&&(p._id=f.id,p._props=d,p._active=!0,p.onEnable())}l.active&&s.assign&&(e=Object.assign(e,s.assign(l,e)))}r(!0)}))}(u,s).then((function(e){c&&c(u)})):c&&c(u)};var n,o=r(5447),a=r(7128),i=r(3287);!function(e){e[e.MARKET=0]="MARKET",e[e.SHUSHU=1]="SHUSHU"}(n||(t.DotUploadType=n={}))},3055:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getKeyByValue=function(e,t){for(var r in e)if(e[r]===t)return r},t.isValueInEnum=function(e,t){return Object.values(t).includes(e)}},4591:function(e,t){var r=this&&this.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(t,"__esModule",{value:!0}),t.equal=function e(t,n,o){var a,i,s,c;if(void 0===o&&(o=[]),t===n)return!0;if("object"!=typeof t||null===t||"object"!=typeof n||null===n)return!1;try{for(var u=r(o),l=u.next();!l.done;l=u.next()){var p=l.value;if(p[0]===t&&p[1]===n)return!0}}catch(e){a={error:e}}finally{try{l&&!l.done&&(i=u.return)&&i.call(u)}finally{if(a)throw a.error}}if(o.push([t,n]),t.constructor!==n.constructor)return!1;if(t.constructor===Number||t.constructor===String||t.constructor===Boolean)return t.valueOf()===n.valueOf();if(Array.isArray(t)&&Array.isArray(n)){if(t.length!==n.length)return!1;for(var f=0;f<t.length;f++)if(!e(t[f],n[f],o))return!1;return!0}var d=Object.keys(t),h=Object.keys(n);if(d.length!==h.length)return!1;try{for(var _=r(d),v=_.next();!v.done;v=_.next()){var y=v.value;if(!n.hasOwnProperty(y))return!1;if(!e(t[y],n[y],o))return!1}}catch(e){s={error:e}}finally{try{v&&!v.done&&(c=_.return)&&c.call(_)}finally{if(s)throw s.error}}return!0}},1367:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Emitter=void 0;var r=function(){function e(e){this._options=e}return Object.defineProperty(e.prototype,"event",{get:function(){var e,t=this;return null!==(e=this._event)&&void 0!==e||(this._event=function(e,r){var n,o,a,i,s,c;r&&(e=e.bind(r)),t._callbacks||(null===(o=null===(n=t._options)||void 0===n?void 0:n.onWillAddFirstListener)||void 0===o||o.call(n,t),t._callbacks=[],null===(i=null===(a=t._options)||void 0===a?void 0:a.onDidAddFirstListener)||void 0===i||i.call(a,t)),null===(c=null===(s=t._options)||void 0===s?void 0:s.onDidAddListener)||void 0===c||c.call(s,t),t._callbacks.push({callback:e,thisArgs:r})}),this._event},enumerable:!1,configurable:!0}),e.prototype.fire=function(e){for(var t=this._callbacks,r=0;r<(null==t?void 0:t.length);r++){var n=t[r],o=n.callback,a=n.thisArgs;o&&o.apply(a,[e])}},e.prototype.dispose=function(){var e,t;this._disposed||(this._disposed=!0,this._callbacks=void 0,null===(t=null===(e=this._options)||void 0===e?void 0:e.onDidRemoveLastListener)||void 0===t||t.call(e))},e}();t.Emitter=r},1239:function(e,t){var r,n,o=this&&this.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,a=r.call(e),i=[];try{for(;(void 0===t||t-- >0)&&!(n=a.next()).done;)i.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=a.return)&&r.call(a)}finally{if(o)throw o.error}}return i},a=this&&this.__spreadArray||function(e,t,r){if(r||2===arguments.length)for(var n,o=0,a=t.length;o<a;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.hotUpdate=t.LOCAL_GAME_VERSION_KEY=t.EventCodeMap=t.EventCodeEnum=void 0,function(e){e[e.ERROR_NO_LOCAL_MANIFEST=0]="ERROR_NO_LOCAL_MANIFEST",e[e.ERROR_DOWNLOAD_MANIFEST=1]="ERROR_DOWNLOAD_MANIFEST",e[e.ERROR_PARSE_MANIFEST=2]="ERROR_PARSE_MANIFEST",e[e.NEW_VERSION_FOUND=3]="NEW_VERSION_FOUND",e[e.ALREADY_UP_TO_DATE=4]="ALREADY_UP_TO_DATE",e[e.UPDATE_PROGRESSION=5]="UPDATE_PROGRESSION",e[e.ASSET_UPDATED=6]="ASSET_UPDATED",e[e.ERROR_UPDATING=7]="ERROR_UPDATING",e[e.UPDATE_FINISHED=8]="UPDATE_FINISHED",e[e.UPDATE_FAILED=9]="UPDATE_FAILED",e[e.ERROR_DECOMPRESS=10]="ERROR_DECOMPRESS",e[e.STOP_WHENUPDATE=11]="STOP_WHENUPDATE",e[e.START=12]="START",e[e.FAIL=13]="FAIL"}(n||(t.EventCodeEnum=n={})),t.EventCodeMap=((r={})[n.ERROR_NO_LOCAL_MANIFEST]="ERROR_NO_LOCAL_MANIFEST",r[n.ERROR_DOWNLOAD_MANIFEST]="ERROR_DOWNLOAD_MANIFEST",r[n.ERROR_PARSE_MANIFEST]="ERROR_PARSE_MANIFEST",r[n.NEW_VERSION_FOUND]="NEW_VERSION_FOUND",r[n.ALREADY_UP_TO_DATE]="ALREADY_UP_TO_DATE",r[n.UPDATE_PROGRESSION]="UPDATE_PROGRESSION",r[n.ASSET_UPDATED]="ASSET_UPDATED",r[n.ERROR_UPDATING]="ERROR_UPDATING",r[n.UPDATE_FINISHED]="UPDATE_FINISHED",r[n.UPDATE_FAILED]="UPDATE_FAILED",r[n.ERROR_DECOMPRESS]="ERROR_DECOMPRESS",r[n.STOP_WHENUPDATE]="STOP_WHENUPDATE",r[n.START]="START",r[n.FAIL]="FAIL",r),t.LOCAL_GAME_VERSION_KEY="local_game_version";var i=function(){function e(){this.currentFileNum=0,this.currentFileTotal=0,this.projectManifestStr=null,this.updating=!1,this.failCount=0,this.remoteVersionManifest=null,this.am=null,this._storagePath="",this._callbacks=[]}return Object.defineProperty(e.prototype,"storagePath",{get:function(){return this._storagePath},enumerable:!1,configurable:!0}),e.prototype._emit=function(e,t){this._callbacks.forEach((function(r){return r(e,t)}))},e.prototype.onHotUpdateState=function(e){"function"==typeof e&&this._callbacks.push(e)},e.prototype.versionCompareHandle=function(e,t){console.log(" version A is "+e+", version B is "+t);for(var r=e.split("."),n=t.split("."),o=0;o<r.length;++o){var a=parseInt(r[o]),i=parseInt(n[o]||"0");if(a!==i)return a-i}return n.length>r.length?-1:0},e.prototype.updateRemoteUrl=function(e,t){return e.url1=t.url1,e.url2=t.url2,e.url3=t.url3,e},e.prototype.downgrade=function(e){var t=o(e.split(".").map(Number),3),r=t[0],n=t[1],a=t[2];return a>0?a--:n>0?(n--,a=99):r>0&&(r--,n=99,a=99),"".concat(r,".").concat(n,".").concat(a)},e.prototype.updateStorageData=function(){var e=this._storagePath+"/project.manifest";if(this.log("当前缓存 project.manifest的url: ".concat(e)),jsb.fileUtils.isFileExist(e)){var t=jsb.fileUtils.getStringFromFile(e);if(t){this.log("project.manifest 数据: ".concat(t));var r=JSON.parse(t);this.updateRemoteUrl(r,this.remoteVersionManifest),this.log("同步远程地址后 project.manifest 数据: ".concat(JSON.stringify(r))),this.versionCompareHandle(r.version,this.remoteVersionManifest.version)>0&&(r.version=this.downgrade(this.remoteVersionManifest.version));try{jsb.fileUtils.writeStringToFile(JSON.stringify(r),e)}catch(t){this.log("写入失败: 地址 ".concat(e,"   缓存数据： ").concat(JSON.stringify(r)))}if(CC_DEBUG){var n=jsb.fileUtils.getStringFromFile(e);this.log("最后得到的缓存目录下的project数据:".concat(n))}}else this.log("project.manifest 文件读取失败: ".concat(e))}else this.log("project.manifest 文件不存在:".concat(e))},e.prototype.setStorage=function(e,t){"object"==typeof t&&(t=JSON.stringify(t)),cc.sys.localStorage.setItem(e,t)},e.prototype.getStorage=function(e,t){void 0===t&&(t=null);var r=cc.sys.localStorage.getItem(e);if(!r)return t;try{return JSON.parse(r)}catch(e){return r}},e.prototype.startUpdate=function(e,r){if(!cc.sys.isNative)return CC_DEBUG&&this.log("startUpdate 不是原生环境: ".concat(cc.sys.isNative)),void this._emit(t.EventCodeMap[n.FAIL],{eventCode:n.FAIL,msg:"不是原生环境"});this._emit(t.EventCodeMap[n.START],{eventCode:n.START,msg:"开始热更"}),this.remoteVersionManifest=r,this._storagePath=(jsb.fileUtils?jsb.fileUtils.getWritablePath():"/")+"blockBlastHotUpdate",CC_DEBUG&&(this.log("缓存地址:"+this._storagePath),this.log("远程 version 数据:",JSON.stringify(r)),this.log("修改前 project 数据 ".concat(JSON.stringify(e)))),this.updateRemoteUrl(e,r),this.projectManifest=e,this.projectManifestStr=JSON.stringify(e),CC_DEBUG&&this.log("修改后 project 数据:",this.projectManifestStr),this.am=new jsb.AssetsManager("",this._storagePath,this.versionCompareHandle),this.am.setVerifyCallback((function(e,t){return t.compressed,t.md5,t.path,t.size,!0})),CC_DEBUG&&this.log("----project数据、远程version数据、本地缓存目录已准备完成----"),this.checkUpdate()},e.prototype.checkUpdate=function(){if(this.updating)CC_DEBUG&&this.log("正在检查或更新。。。");else{if(CC_DEBUG&&this.log("----开始检查是否存在新的版本号----"),this.updateStorageData(),this.am.getState()===jsb.AssetsManager.State.UNINITED){CC_DEBUG&&this.log("checkUpdate 本地清单尚未初始化，开始初始化");var e=new jsb.Manifest(this.projectManifestStr,this._storagePath);this.am.loadLocalManifest(e,this._storagePath),(o=this.getStorage(t.LOCAL_GAME_VERSION_KEY))||(o=this.am.getLocalManifest().getVersion(),this.setStorage(t.LOCAL_GAME_VERSION_KEY,o))}if(CC_DEBUG){var r=this.am.getLocalManifest().getManifestRoot(),o=this.am.getLocalManifest().getVersion(),a=this.am.getLocalManifest().getPackageUrl(),i=this.am.getLocalManifest().getManifestFileUrl(),s=this.am.getLocalManifest().getVersionFileUrl();this.log("checkUpdate ---检查本地manifest解析内容---"),this.log("checkUpdate manifest根目录:",r),this.log("checkUpdate 本地版本号:",o),this.log("checkUpdate 远程资源根Url:",a),this.log("checkUpdate 远程资源manifestUrl:",i),this.log("checkUpdate 远程资源versionUrl:",s)}if(!this.am.getLocalManifest()||!this.am.getLocalManifest().isLoaded())return CC_DEBUG&&this.log("checkUpdate 本地清单加载失败..."),void this._emit(t.EventCodeMap[n.FAIL],{eventCode:n.FAIL,msg:"本地清单加载失败..."});CC_DEBUG&&this.log("checkUpdate 本地清单已经初始化成功"),this.am.setEventCallback(this.checkCb.bind(this)),this.am.checkUpdate(),this.updating=!0}},e.prototype.checkCb=function(e){var r=this,o=e.getEventCode(),a=t.EventCodeMap[o],i=!1,s="";switch(CC_DEBUG&&this.log("checkCb 检查热更回调事件名: ".concat(o," ").concat(a)),o){case jsb.EventAssetsManager.ERROR_NO_LOCAL_MANIFEST:i=!0,s="checkCb 未找到本地清单文件，跳过热更新。";break;case jsb.EventAssetsManager.ERROR_DOWNLOAD_MANIFEST:case jsb.EventAssetsManager.ERROR_PARSE_MANIFEST:i=!0,s="checkCb 下载清单文件失败，跳过热更新。";break;case jsb.EventAssetsManager.ALREADY_UP_TO_DATE:i=!0,s="checkCb 已更新到最新的远程版本";break;case jsb.EventAssetsManager.NEW_VERSION_FOUND:CC_DEBUG&&this.log("checkCb 发现新版本,需要下载字节大小 ".concat(this.am.getTotalBytes(),", ").concat(o," ").concat(a)),this._emit(t.EventCodeMap[n.NEW_VERSION_FOUND],{eventCode:n.NEW_VERSION_FOUND,msg:"发现新版本,需要下载字节大小 (' + ".concat(this.am.getTotalBytes()," + ')"),jsbEvent:e}),setTimeout((function(){r.hotUpdate()}),100);break;default:return}i&&this._emit(t.EventCodeMap[n.FAIL],{eventCode:n.FAIL,msg:"".concat(s,":").concat(o," ").concat(a," ").concat(e.getMessage()),jsbEvent:e}),this.am.setEventCallback(null),this.updating=!1},e.prototype.hotUpdate=function(){if(!this.am||this.updating)return this.log("hotUpdate jsb.AssetsManager对象创建失败 或正在热更",this.am,this.updating),void this._emit(t.EventCodeMap[n.FAIL],{eventCode:n.FAIL,msg:"jsb.AssetsManager对象创建失败 或正在热更..."});if(CC_DEBUG&&this.log("hotUpdate 检测到新版本 开始执行热更,下载assets资源"),this.am.getState()===jsb.AssetsManager.State.UNINITED){CC_DEBUG&&this.log("updateCb 本地清单还没加载，重新加载".concat(this.projectManifestStr," ").concat(this._storagePath));var e=new jsb.Manifest(this.projectManifestStr,this._storagePath);this.am.loadLocalManifest(e,this._storagePath)}if(!this.am.getLocalManifest()||!this.am.getLocalManifest().isLoaded())return CC_DEBUG&&this.log("updateCb 本地清单加载失败..."),void this._emit(t.EventCodeMap[n.FAIL],{eventCode:n.FAIL,msg:"本地清单加载失败..."});CC_DEBUG&&this.log("hotUpdate 本地清单初始化成功 检测到新版本 开始执行热更，"),this.am.setEventCallback(this.updateCb.bind(this)),this.currentFileNum=0,this.currentFileTotal=0,this.failCount=0,this.am.update(),this.updating=!0},e.prototype.updateCb=function(e){var r=!1,o=!1,a="",i=e.getEventCode(),s=t.EventCodeMap[i];switch(CC_DEBUG&&console.log("updateCb 热更回调事件名: ".concat(i," ").concat(s," ")),i){case jsb.EventAssetsManager.ERROR_NO_LOCAL_MANIFEST:o=!0,a="updateCb  没找到找到本地清单文件，跳过热更新。".concat(i," ").concat(s);break;case jsb.EventAssetsManager.ASSET_UPDATED:a="updateCb 下载成功文件: ".concat(s," ").concat(i," ").concat(e.getAssetId(),"}");break;case jsb.EventAssetsManager.UPDATE_PROGRESSION:var c=e.getDownloadedFiles(),u=e.getTotalFiles(),l=e.getDownloadedBytes(),p=e.getTotalBytes();this.currentFileNum=e.getDownloadedFiles(),this.currentFileTotal=e.getTotalFiles(),CC_DEBUG&&(this.log("updateCb 下载进度（文件数）：".concat(c,"/").concat(u," ").concat(s," ").concat(i)),this.log("updateCb 下载进度（字节）".concat(l,"/").concat(p," ").concat(s," ").concat(i))),this._emit(t.EventCodeMap[n.UPDATE_PROGRESSION],{eventCode:i,msg:"下载进度（文件数）：".concat(c,"/").concat(u," ").concat(s," ").concat(i),jsbEvent:e});break;case jsb.EventAssetsManager.ERROR_DOWNLOAD_MANIFEST:case jsb.EventAssetsManager.ERROR_PARSE_MANIFEST:o=!0,a="updateCb 下载清单文件失败，跳过热更新。".concat(i," ").concat(s," ").concat(e.getMessage());break;case jsb.EventAssetsManager.ALREADY_UP_TO_DATE:o=!0,a="updateCb 已更新到最新的远程版本。".concat(i," ").concat(s);break;case jsb.EventAssetsManager.UPDATE_FINISHED:r=!0,a="updateCb 更新已完成。".concat(s," ").concat(i," ").concat(e.getMessage());break;case jsb.EventAssetsManager.UPDATE_FAILED:CC_DEBUG&&console.log("%c[热更新]","color:#fff;background:#6a1b9a;padding:2px 6px;border-radius:4px;","updateCb 更新失败:".concat(e.getMessage(),", ' 已下载文件数:', ").concat(this.currentFileNum,", ' 需要下载文件数:', ").concat(this.currentFileTotal)),this.failCount++,this.failCount<=5?this.am.downloadFailedAssets():(o=!0,a="updateCb 失败次数过多，放弃重新下载失败文件 ".concat(i," ").concat(s," ").concat(e.getMessage()));break;case jsb.EventAssetsManager.ERROR_UPDATING:try{(e.getMessage()||"").includes("(416)")&&jsb.fileUtils.removeFile("".concat(this._storagePath,"_temp/").concat(e.getAssetId(),".tmp"))}catch(e){console.error("%c[热更新]","color:#fff;background:#6a1b9a;padding:2px 6px;border-radius:4px;","删除临时文件发生错误:".concat(i," ").concat(s," ").concat(e," "))}this.log("updateCb 资源更新错误:".concat(e.getAssetId()," ").concat(e.getMessage()));break;case jsb.EventAssetsManager.ERROR_DECOMPRESS:CC_DEBUG&&console.log("updateCb 解压缩失败:".concat(i," ").concat(s," ").concat(e.getMessage()));break;case jsb.EventAssetsManager.STOP_WHENUPDATE:o=!0,a="updateCb STOP_WHENUPDATE:".concat(i," ").concat(s," ").concat(e.getMessage())}if(CC_DEBUG&&this.log("%c[热更新]","color:#fff;background:#6a1b9a;padding:2px 6px;border-radius:4px;",a),o)return this.am.setEventCallback(null),this.updating=!1,void this._emit(t.EventCodeMap[n.FAIL],{eventCode:n.FAIL,msg:"".concat(a," ").concat(e.getMessage()),jsbEvent:e});if(r){this.am.setEventCallback(null),this.updating=!1;var f=this.am.getLocalManifest().getSearchPaths();f.push("@assets/"),cc.sys.localStorage.setItem("blockBlastHotUpdateData",JSON.stringify(f)),CC_DEBUG&&(this.log("updateCb 搜索路径： ".concat(cc.sys.localStorage.getItem("blockBlastHotUpdateData"))),this.log("updateCb 已更新到最新版本，请重启： ".concat(cc.sys.localStorage.getItem("blockBlastHotUpdateData")))),this._emit(t.EventCodeMap[n.UPDATE_FINISHED],{eventCode:n.UPDATE_FINISHED,msg:"更新成功:".concat(e.getMessage()),jsbEvent:e})}},e.prototype.log=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];CC_DEBUG&&console.log.apply(console,a(["%c[热更新]","color:#fff;background:#6a1b9a;padding:2px 6px;border-radius:4px;"],o(e),!1))},e.prototype.error=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];CC_DEBUG&&console.error.apply(console,a(["%c[热更新]","color:#fff;background:#6a1b9a;padding:2px 6px;border-radius:4px;"],o(e),!1))},e}();t.hotUpdate=new i},4613:(e,t)=>{var r,n;Object.defineProperty(t,"__esModule",{value:!0}),t.http=t.CrytoType=t.HttpType=void 0,function(e){e.GET="GET",e.POST="POST"}(r||(t.HttpType=r={})),function(e){e.Url="Url"}(n||(t.CrytoType=n={}));var o=function(){function e(){this.callbackId=-1,this.callbacks={}}return e.prototype.requestAsync=function(e,t,r){var n=this;return new Promise((function(o,a){n.request(e,t,(function(e){o(e)}),(function(e){a(e)}),r)}))},e.prototype.request=function(e,t,r,n,o){var a="GET";(null==o?void 0:o.type)&&(a=o.type);var i=cc.loader.getXMLHttpRequest(),s=JSON.stringify(t);i.open(a,e,!0);var c="application/json";(null==o?void 0:o.contentType)&&(c=o.contentType),i.setRequestHeader("Content-Type",c),i.onreadystatechange=function(){if(4===i.readyState){var e=i.responseText;i.status>=200&&i.status<300?(o.crypto&&(e=o.crypto.decrypt(e)),r(JSON.parse(e))):n(e)}},o.crypto&&(s="params="+o.crypto.encrypt(s)),i.send(s)},e}();t.http=new o},1655:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.interval=function(e,t,r){for(var n=[],o=3;o<arguments.length;o++)n[o-3]=arguments[o];var a=setInterval((function(){e&&cc.isValid(e,!0)?null==t||t.apply(this,[n]):clearInterval(a)}),r,n);return a}},4125:(e,t)=>{function r(e,t,r){e.name=t.name,e.zIndex=t.zIndex,void 0!==t.opacity&&(e.opacity=t.opacity),t.hidden&&(e.active=!1),r.addChild(e),o(e)}Object.defineProperty(t,"__esModule",{value:!0}),t.layerManager=t.LayerManager=t.gameUiLayer=t.BASE_LAYER_CONFIGS=void 0,t.initNodeConfig=r,t.addWidget=o,t.BASE_LAYER_CONFIGS=[{type:"gameUI",name:"gameUiLayer",zIndex:2}],t.gameUiLayer=new cc.Node;var n=function(){function e(){this.layerMap=new Map,this.scene=null}return e.prototype.init=function(){var e=this;this.scene=cc.Canvas.instance.node,this.clear(),t.BASE_LAYER_CONFIGS.forEach((function(n){"gameUI"===n.type&&(r(t.gameUiLayer,n,e.scene),e.layerMap.set(n.type,t.gameUiLayer))}))},Object.defineProperty(e.prototype,"layerScene",{get:function(){return this.scene},enumerable:!1,configurable:!0}),e.prototype.clear=function(){this.layerMap.forEach((function(e){e.removeFromParent(),e.destroy()})),this.layerMap.clear()},e.prototype.addLayer=function(e){if(!this.scene)throw new Error("LayerManager not initialized");this.removeLayer(e.type);var t=new cc.Node;return r(t,e,this.scene),this.layerMap.set(e.type,t),t},e.prototype.setLayerNode=function(e,t){if(!this.scene)throw new Error("LayerManager not initialized");this.removeLayer(e),this.layerMap.set(e,t)},e.prototype.removeLayer=function(e){var t=this.layerMap.get(e);return!!t&&(t.removeFromParent(),t.destroy(),this.layerMap.delete(e),!0)},e.prototype.getLayer=function(e){return this.layerMap.get(e)},e.prototype.showLayer=function(e){var t=this.layerMap.get(e);t&&(t.active=!0)},e.prototype.hideLayer=function(e){var t=this.layerMap.get(e);t&&(t.active=!1)},e.prototype.setLayerOpacity=function(e,t){var r=this.layerMap.get(e);r&&(r.opacity=t)},e.prototype.getLayerTypes=function(){return Array.from(this.layerMap.keys())},e}();function o(e){e.width=960,e.height=1707.5,e.anchorX=e.anchorY=0;var t=e.addComponent(cc.Widget);t.isAlignTop=!0,t.isAlignLeft=!0,t.isAlignRight=!0,t.isAlignBottom=!0,t.top=0,t.bottom=0,t.left=0,t.right=0}t.LayerManager=n,t.layerManager=new n},3687:(e,t)=>{var r;Object.defineProperty(t,"__esModule",{value:!0}),t.ResLoader=t.BundleName=void 0,function(e){e.class="class",e.chapter="chapter",e.tool="tool"}(r||(t.BundleName=r={}));var n=function(){function e(){}return e.addEventListener=function(e,t){this.events[e]||(this.events[e]=[]),this.events[e].push(t)},e.isRemote=function(e){return/^(https?:\/\/)/.test(e)},e.getAsset=function(e){var t;return null===(t=this.cacheResources[e])||void 0===t?void 0:t.asset},e.load=function(e,t,r){if(this.cacheResources[e])this.cacheResources[e].asset?this.cacheResources[e].asset.isValid?r&&r.apply(this,[null,this.cacheResources[e].asset]):(delete this.cacheResources[e],this.load(e,t,r)):r&&this.cacheResources[e].onCompletes.push(r);else{this.cacheResources[e]={onCompletes:r?[r]:[]};var n=this,o=function(t,r){t&&cc.error("资源加载发生错误：",t),n.cacheResources[e].asset=r;for(var o=n.cacheResources[e].onCompletes,a=0;a<(null==o?void 0:o.length);a++)o[a].apply(this,[t,r]);n.cacheResources[e].onCompletes.length=0;for(var i=n.events.load,s=0;s<(null==i?void 0:i.length);s++)i[s].apply(n,["resources",e])};this.isRemote(e)?cc.assetManager.loadRemote(e,o):cc.resources.load(e,t,o)}},e.asyncLoad=function(e,t){var r=this;return new Promise((function(n,o){r.load(e,t,(function(e,t){e?o(e):n(t)}))}))},e.loadByBundle=function(e,t,r,n){var o=this;this.loadBundle(e,(function(a,i){if(a)return console.error("bundle:".concat(e," 资源加载错误："),a),void(n&&n.apply(o,[a,null]));var s=e+"_"+t;o.cacheResources[s]?o.cacheResources[s].asset?o.cacheResources[s].asset.isValid?n&&n.apply(o,[null,o.cacheResources[s].asset]):(delete o.cacheResources[s],o.loadByBundle(e,t,r,n)):n&&o.cacheResources[s].onCompletes.push(n):(o.cacheResources[s]={onCompletes:n?[n]:[]},i.load(t,r,(function(r,n){o.cacheResources[s].asset=n;for(var a=o.cacheResources[s].onCompletes,i=0;i<(null==a?void 0:a.length);i++)a[i].apply(o,[r,n]);for(var c=o.events.load,u=0;u<(null==c?void 0:c.length);u++)c[u].apply(o,[e,t])})))}))},e.asyncLoadByBundle=function(e,t,r){var n=this;return new Promise((function(o,a){n.loadByBundle(e,t,r,(function(e,t){e?a(e):o(t)}))}))},e.loadBundle=function(e,t){var r=this;this.cacheResources[e]?this.cacheResources[e].asset?t&&t.apply(this,[null,this.cacheResources[e].asset]):t&&this.cacheResources[e].onCompletes.push(t):(this.cacheResources[e]={onCompletes:t?[t]:[]},cc.assetManager.loadBundle(e,(function(t,n){r.cacheResources[e].asset=n;for(var o=r.cacheResources[e].onCompletes,a=0;a<(null==o?void 0:o.length);a++)o[a].apply(this,[t,n]);r.cacheResources[e].onCompletes.length=0})))},e.bundlePreloadScene=function(e,t,r){var n=this,o="preloadScene"+t;this.cacheResources[o]?n.cacheResources[o].__complete__?r&&r.apply(this,[null]):r&&this.cacheResources[o].onCompletes.push(r):(this.cacheResources[o]={onCompletes:r?[r]:[]},e.preloadScene(t,(function(e){var t=n.cacheResources[o].onCompletes;n.cacheResources[o].__complete__=!0;for(var r=0;r<(null==t?void 0:t.length);r++)t[r].apply(this,[e]);n.cacheResources[o].onCompletes.length=0,delete n.cacheResources[o].__complete__})))},e.bundlePreload=function(e,t,r,n){var o=this,a="preload"+t;this.cacheResources[a]?o.cacheResources[a].__items__?n&&n.apply(this,[null,o.cacheResources[a].__items__]):n&&this.cacheResources[a].onCompletes.push(n):(this.cacheResources[a]={onCompletes:n?[n]:[]},e.preload(t,r,(function(e,t){var r=o.cacheResources[a].onCompletes;o.cacheResources[a].__items__=t;for(var n=0;n<(null==r?void 0:r.length);n++)r[n].apply(this,[e,t]);o.cacheResources[a].onCompletes.length=0})))},e.renderSprite=function(e,t){this.load(t,cc.SpriteFrame,(function(t,r){t||(e.spriteFrame=r)}))},e.renderDragonbones=function(e,t,r,n,o,a){var i=this;void 0===a&&(a=1);var s=0,c=function(){2===s&&(e.addEventListener(dragonBones.EventObject.COMPLETE,(function(r){var n,o;CC_DEBUG&&console.log("renderDragonbones 动画播放结束,url",t),(null===(n=e.node)||void 0===n?void 0:n.parent)&&(null===(o=e.node)||void 0===o||o.parent.removeChild(e.node))}),i),e.armatureName=n,e.playAnimation(o,a))};this.load(t,dragonBones.DragonBonesAsset,(function(t,r){t||(e.dragonAsset=r,s++,c())})),this.load(r,dragonBones.DragonBonesAtlasAsset,(function(t,r){t||(e.dragonAtlasAsset=r,s++,c())}))},e.renderDragonbonesByBundle=function(e,t,r,n,o,a,i,s){var c=this;void 0===i&&(i=1),void 0===s&&(s=!0),this.loadBundle(e,(function(e,u){if(!e){var l=0,p=function(){2===l&&(t.addEventListener(dragonBones.EventObject.COMPLETE,(function(e){var n,o;s&&(null===(n=t.node)||void 0===n?void 0:n.parent)&&(CC_DEBUG&&console.log("renderDragonbonesByBundle 动画播放结束,url",r),null===(o=t.node)||void 0===o||o.parent.removeChild(t.node))}),c),t.armatureName=o,t.playAnimation(a,i))};u.load(r,dragonBones.DragonBonesAsset,(function(e,r){e||(t.dragonAsset=r,l++,p())})),u.load(n,dragonBones.DragonBonesAtlasAsset,(function(e,r){e||(t.dragonAtlasAsset=r,l++,p())}))}}))},e.events={},e.cacheResources={},e}();t.ResLoader=n},2698:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NativeBridge=t.ANDROID_ACTIVE=void 0,t.ANDROID_ACTIVE="org/cocos2dx/javascript/AppActivity";var r=function(){function e(){}return e.isNative=function(){var e;return null===(e=cc.sys)||void 0===e?void 0:e.isNative},e.send=function(e,r,n,o){if(void 0===o&&(o=t.ANDROID_ACTIVE),this.isNative())return""==r?jsb.reflection.callStaticMethod(o,e,n):jsb.reflection.callStaticMethod(o,e,n,r)},e}();t.NativeBridge=r},3019:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.randomInt=function(e,t){return Math.floor(Math.random()*(t-e))+e},t.randomFloat=function(e,t){return Math.random()*(t-e)+e}},6764:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.executeRenderingOptimize=function(){CC_EDITOR||Object.defineProperty(cc.Node.prototype,"active",{get:function(){return this._active},set:function(e){if(function(e,t){var r=e._components&&e._components.find((function(e){return e.__fullscreen__}));if(e.opacity=255,r)if(t){if(r.__inactives__=[],e.parent){var n=function(e){if(!(e.parent instanceof cc.Scene)&&e.parent){if(e.parent.children){var t=e.parent.children.indexOf(e);e.parent.children.slice(0,t).forEach((function(e){e.opacity>0&&e.active&&r.__inactives__.push(e),e.opacity=0}))}n(e.parent)}};n(e)}}else{var o=r.__inactives__;if(o){for(var a=0;a<o.length;a++)o[a].opacity=255;r.__inactives__.length=0}}}(this,e=!!e),this._active!==e){this._active=e;var t=this._parent;t&&t._activeInHierarchy&&cc.director._nodeActivator.activateNode(this,e)}}})}},6014:function(e,t){var r=this&&this.__awaiter||function(e,t,r,n){return new(r||(r=Promise))((function(o,a){function i(e){try{c(n.next(e))}catch(e){a(e)}}function s(e){try{c(n.throw(e))}catch(e){a(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(i,s)}c((n=n.apply(e,t||[])).next())}))},n=this&&this.__generator||function(e,t){var r,n,o,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},i=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return i.next=s(0),i.throw=s(1),i.return=s(2),"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(c){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,c])}}};Object.defineProperty(t,"__esModule",{value:!0}),t.ObjectPool=void 0;var o=function(){function e(e,t,r){this._create=e,this._reset=t,this._option=r,this.pool=[],this.inUsed=new Set,this._init()}return e.prototype._init=function(){return r(this,void 0,void 0,(function(){var e,t,r,o,a,i;return n(this,(function(n){switch(n.label){case 0:if(!this._option)return[3,6];e=this._option,t=e.size,r=e.args,o=e.onCreateComplete,isNaN(t)&&(t=1),n.label=1;case 1:n.trys.push([1,5,,6]),n.label=2;case 2:return this.pool.length<t?[4,this._create(r)]:[3,4];case 3:return a=n.sent(),this.inUsed.add(a),this.pool.push(a),[3,2];case 4:return o&&o(null),[3,6];case 5:return i=n.sent(),o&&o(i),[3,6];case 6:return[2]}}))}))},Object.defineProperty(e.prototype,"poolSize",{get:function(){return this.pool.length},enumerable:!1,configurable:!0}),e.prototype.isInUsed=function(e){return this.inUsed.has(e)},e.prototype.get=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return r(this,void 0,void 0,(function(){var t;return n(this,(function(r){switch(r.label){case 0:return this.pool.length>0?(t=this.pool.pop(),this._reset(t),[3,3]):[3,1];case 1:return[4,this._create(e)];case 2:t=r.sent(),r.label=3;case 3:return this.inUsed.add(t),[2,t]}}))}))},e.prototype.asyncGet=function(){for(var e=this,t=[],o=0;o<arguments.length;o++)t[o]=arguments[o];return new Promise((function(o,a){return r(e,void 0,void 0,(function(){var e,r;return n(this,(function(n){switch(n.label){case 0:return this.pool.length>0?(e=this.pool.pop(),this._reset(e),this.inUsed.add(e),o(e),[3,4]):[3,1];case 1:return n.trys.push([1,3,,4]),[4,this._create(t)];case 2:return e=n.sent(),this.inUsed.add(e),o(e),[3,4];case 3:return r=n.sent(),a(r),[3,4];case 4:return[2]}}))}))}))},e.prototype.release=function(e){this.inUsed.has(e)&&(this.inUsed.delete(e),this.pool.push(e))},e.prototype.clear=function(){this.pool.length=0,this.inUsed.clear()},e}();t.ObjectPool=o},7479:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.randomList=function(e,t,r){if(void 0===r&&(r=!1),r){for(var n=[],o=0;o<t;o++){var a=Math.floor(Math.random()*(e.length-1));n.push(e[a])}return n}return function(e,t){void 0===t&&(t=!1),t&&(e=e.concat());for(var r=e.length-1;r>0;r--){var n=Math.floor(Math.random()*r),o=e[r];e[r]=e[n],e[n]=o}return e}(e).slice(0,t)}},5447:function(e,t){var r=this&&this.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,a=r.call(e),i=[];try{for(;(void 0===t||t-- >0)&&!(n=a.next()).done;)i.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=a.return)&&r.call(a)}finally{if(o)throw o.error}}return i};Object.defineProperty(t,"__esModule",{value:!0}),t.storage=void 0;var n=function(){function e(){this.cacheData={}}return e.prototype.setItem=function(t,r){try{var n=e.prefix+t,o=void 0,a=typeof r;null===r||"string"===a||"number"===a||"boolean"===a||"bigint"===a||"undefined"===a?o=r:"object"===a?o=JSON.stringify(r):console.error("【".concat(t,"】【setItem】未实现的数据类型存储，值为：").concat(r)),this.cacheData[t]={type:a,data:o};var i=a+e.valueTypeSplit+o;localStorage.setItem(n,i)}catch(e){throw e instanceof DOMException&&(22===e.code||1014===e.code||"QuotaExceededError"===e.name||"NS_ERROR_DOM_QUOTA_REACHED"===e.name)?new Error("LocalStorage is full"):e}},e.prototype.getItem=function(t,n){if(Object.prototype.hasOwnProperty.call(this.cacheData,t)){var o=this.cacheData[t],a=o.type,i=o.data;return null===i?i:"object"===a?JSON.parse(i):i}var s=e.prefix+t,c=localStorage.getItem(s),u=void 0;if(null!==c&&""!==c){var l=c.split(e.valueTypeSplit);if(2!==(null==l?void 0:l.length))throw new Error("【Storage-getItem】存储长度应该为2，实际为：".concat(null==l?void 0:l.length,"！"));var p=r(l,2),f=p[0],d=p[1],h=void 0;switch(f){case"string":h=u=d;break;case"number":case"bigint":h=u=+d;break;case"boolean":h=u=JSON.parse(d);break;case"undefined":h=u=void 0;break;case"object":u=JSON.parse(d),h=d;break;default:console.error("【".concat(t,"】【getItem】未实现的数据类型存储"))}this.cacheData[t]={type:f,data:h}}else u=n;return u},e.prototype.remove=function(t){var r=e.prefix+t;localStorage.removeItem(r)},e.prototype.clear=function(){localStorage.clear()},e.prefix="block-blast-",e.valueTypeSplit="^_^",e}();t.storage=new n},2279:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.task=void 0;var r=function(){function e(){this.taskList=[]}return e.prototype.run=function(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];window.requestIdleCallback||(window.requestIdleCallback=function(e){var t=Date.now();return setTimeout((function(){e({didTimeout:!1,timeRemaining:function(){return Math.max(0,30-(Date.now()-t))}})}))},window.cancelIdleCallback=function(e){clearTimeout(e)}),this.taskList.push({handler:e,args:t}),this.taskHandle||(this.taskHandle=window.requestIdleCallback(this.idleRequestCallback.bind(this),{timeout:20}))},e.prototype.idleRequestCallback=function(e){for(;(e.timeRemaining()>5||e.didTimeout)&&this.taskList.length>0;){var t=this.taskList.shift();t.handler&&t.handler.apply(this,t.args)}this.taskList.length>0?this.taskHandle=window.requestIdleCallback(this.idleRequestCallback.bind(this),{timeout:20}):(cancelIdleCallback(this.taskHandle),this.taskHandle=0)},e}();t.task=new r},423:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.timeout=function(e,t,r){for(var n=[],o=3;o<arguments.length;o++)n[o-3]=arguments[o];var a=setTimeout((function(){clearTimeout(a),e&&cc.isValid(e,!0)&&(null==t||t.apply(this,[n]))}),r,n);return a}},1963:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.nextFrame=function(){var e=this;return new Promise((function(t){e.callLater((function(){t(null)}),null)}))},t.callLater=function(e,t){cc.director.once(cc.Director.EVENT_AFTER_UPDATE,e,t)}},47:function(__unused_webpack_module,exports,__webpack_require__){var __awaiter=this&&this.__awaiter||function(e,t,r,n){return new(r||(r=Promise))((function(o,a){function i(e){try{c(n.next(e))}catch(e){a(e)}}function s(e){try{c(n.throw(e))}catch(e){a(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(i,s)}c((n=n.apply(e,t||[])).next())}))},__generator=this&&this.__generator||function(e,t){var r,n,o,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},i=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return i.next=s(0),i.throw=s(1),i.return=s(2),"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(c){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,c])}}};Object.defineProperty(exports,"__esModule",{value:!0}),exports.Trait=void 0;var TraitConfigInfo_1=__webpack_require__(3287),Trait=function(){function Trait(){this._state={},this._props={},this._active=!1,this._subTraits=[]}return Object.defineProperty(Trait,"activedListenerTraits",{get:function(){return Trait._activedListenerTraits},enumerable:!1,configurable:!0}),Object.defineProperty(Trait.prototype,"id",{get:function(){return this._id},enumerable:!1,configurable:!0}),Object.defineProperty(Trait.prototype,"condition",{get:function(){return this._condition},enumerable:!1,configurable:!0}),Object.defineProperty(Trait.prototype,"traitName",{get:function(){return getClassName(this.constructor)},enumerable:!1,configurable:!0}),Object.defineProperty(Trait.prototype,"state",{get:function(){return this._state},enumerable:!1,configurable:!0}),Trait.prototype.data=function(){return{}},Object.defineProperty(Trait.prototype,"props",{get:function(){return this._props},enumerable:!1,configurable:!0}),Object.defineProperty(Trait.prototype,"active",{get:function(){var _active=this.props&&Object.keys(this.props).length>0;if(!this._condition)return _active;try{return _active&&eval(this._condition)}catch(e){return console.error("执行条件特性激活发生错误：",e.stack),_active}},set:function(e){var t;this._active!==e&&(this._active=e,e?(this._props=null===(t=TraitConfigInfo_1.traitConfigInfo.traitsClassNameMap[this.traitName])||void 0===t?void 0:t.param,this.onEnable()):(this._props={},this.onDisable()))},enumerable:!1,configurable:!0}),Trait.prototype.registerSubTraits=function(){return null},Trait.prototype.getSubTraits=function(){return this._subTraits},Trait.prototype.onCreate=function(){},Trait.prototype.onEnable=function(){},Trait.prototype.onDisable=function(){},Trait.prototype.setState=function(e){for(var t in e){var r=e[t];this._state[t]=r}},Trait.prototype.onActive=function(e){return __awaiter(this,void 0,void 0,(function(){return __generator(this,(function(e){return[2]}))}))},Trait.onTraitActive=function(e,t,r){this._activedListenerTraits.has(e)||this._activedListenerTraits.set(e,[]),this._activedListenerTraits.get(e).push({preActive:t,actived:r})},Trait._activedListenerTraits=new Map,Trait}();exports.Trait=Trait},3287:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.traitConfigInfo=void 0;var n=r(5447),o=function(){function e(){this._traits={},this._traitsById={},this._useTraits={},this._cacheUsedTraits={},this._traitsClassNameMap={},this._useActiveUserTraits={},this._cacheUsedActiveUserTraits={}}return Object.defineProperty(e.prototype,"traitsClassNameMap",{get:function(){return this._traitsClassNameMap},enumerable:!1,configurable:!0}),e.prototype.initialize=function(e){if(e){var t=e.feature,r=this._traits,n=this._traitsById;for(var o in t)for(var a=t[o],i=0;i<a.length;i++){var s=a[i],c=s.id,u=s.param;r[o]||(r[o]={}),r[o][c]=u,n[c]={traitName:o,param:u}}}else console.error("特性初始化配置发生错误")},e.prototype.createUseTraits=function(e){this._curActiveConfig=e;var t=this._curActiveConfig,r=t.features,o=t.plan;n.storage.setItem("gameWayNum",o);for(var a=0;a<r.length;a++){var i=r[a].id;this._useTraits[i]=r[a]}},e.prototype.createActiveTraits=function(e){for(var t=e.features,r=0;r<t.length;r++){var n=t[r].id;this._useActiveUserTraits[n]=t[r]}},e.prototype.createActiveTraitClassNameMaps=function(){var e=this._traitsById,t=this._curActiveConfig.features;CC_DEBUG&&(console.log("%c[激活的特性]","color:#fff;background:#ff019a;",t),n.storage.setItem("activeFeatures",t));for(var r=0;r<t.length;r++){var o=t[r].id,a=e[o];if(a){var i=a.traitName,s=a.param,c=i.charAt(0).toUpperCase()+i.slice(1)+"Trait";this._traitsClassNameMap[c]={id:o,param:s}}else CC_DEBUG&&console.error("特性总表 cfg.ts 配置没有特性：".concat(o))}},e.prototype.createWhiteTraitClassNameMaps=function(e){for(var t=this._traitsById,r=0;r<e.length;r++){var n=e[r],o=t[n];if(o){var a=o.traitName,i=o.param,s=a.charAt(0).toUpperCase()+a.slice(1)+"Trait";this._traitsClassNameMap[s]={id:n,param:i}}else CC_DEBUG&&console.error("特性总表 cfg.ts 配置没有特性：".concat(n))}CC_DEBUG&&console.log("%c[激活的特性类名]","color:#fff;background:#ff019a;",this._traitsClassNameMap)},e.prototype.loadDynamicTrait=function(e){for(var t=this._traitsById,r=0;r<e.length;r++){var n=e[r],o=t[n];if(o){var a=o.traitName,i=o.param,s=a.charAt(0).toUpperCase()+a.slice(1)+"Trait";this._traitsClassNameMap[s]={id:n,param:i},console.log("动态新加特性：".concat(n))}else CC_DEBUG&&console.error("动态新加特性 特性总表 cfg.ts 配置没有特性：".concat(n))}},e.prototype.traitData=function(e,t){if(isNaN(t)){if(this._cacheUsedTraits[e])return this._cacheUsedTraits[e];var r=this._traits[e];for(var n in r)if(this._useTraits[n])return this._cacheUsedTraits[e]=r[n],this._cacheUsedTraits[e]}return this._traits[e][t]},e.prototype.traitActiveUserData=function(e,t){if(isNaN(t)){if(this._cacheUsedActiveUserTraits[e])return this._cacheUsedActiveUserTraits[e];var r=this._traits[e];for(var n in r)if(this._useActiveUserTraits[n])return this._cacheUsedActiveUserTraits[e]=r[n],this._cacheUsedActiveUserTraits[e]}return this._traits[e][t]},e}();t.traitConfigInfo=new o},259:(e,t)=>{function r(e){return"string"==typeof e}function n(e){return void 0===e}function o(e){return n(e)||null===e}Object.defineProperty(t,"__esModule",{value:!0}),t.isString=r,t.isStringArray=function(e){return Array.isArray(e)&&e.every((function(e){return r(e)}))},t.isObject=function(e){return!("object"!=typeof e||null===e||Array.isArray(e)||e instanceof RegExp||e instanceof Date)},t.isTypedArray=function(e){var t=Object.getPrototypeOf(Uint8Array);return"object"==typeof e&&e instanceof t},t.isNumber=function(e){return"number"==typeof e&&!isNaN(e)},t.isIterable=function(e){return!!e&&"function"==typeof e[Symbol.iterator]},t.isBoolean=function(e){return!0===e||!1===e},t.isUndefined=n,t.isDefined=function(e){return!o(e)},t.isUndefinedOrNull=o,t.isFunction=function(e){return"function"==typeof e},t.getType=function(e){switch(Object.prototype.toString.call(e)){case"[object Object]":default:return"object";case"[object Number]":return"number";case"[object String]":return"string";case"[object Boolean]":return"boolean";case"[object Array]":return"array";case"[object Undefined]":return"undefined";case"[object Null]":return"null";case"[object Date]":return"date";case"[object RegExp]":return"regExp";case"[object Function]":return"function"}}},5805:function(e,t,r){var n=this&&this.__awaiter||function(e,t,r,n){return new(r||(r=Promise))((function(o,a){function i(e){try{c(n.next(e))}catch(e){a(e)}}function s(e){try{c(n.throw(e))}catch(e){a(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(i,s)}c((n=n.apply(e,t||[])).next())}))},o=this&&this.__generator||function(e,t){var r,n,o,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},i=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return i.next=s(0),i.throw=s(1),i.return=s(2),"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(c){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(r=1,n&&(o=2&s[0]?n.return:s[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,s[1])).done)return o;switch(n=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,n=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],n=0}finally{r=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,c])}}};Object.defineProperty(t,"__esModule",{value:!0}),t.UI=void 0;var a=r(4125),i=r(3687),s=function(){function e(){}return e.setModalPrefab=function(e){this.modalUrl=e},e.hideAll=function(){for(var t in this.uiCache){var r=e.uiCache[t];r.node&&(r.node.active=!1)}},e.activeState=function(t){for(var r in this.uiCache)if(r==t){var n=e.uiCache[r];if(n.node)return n.node.active}return!1},e.cancelLoad=function(e){this.uiIsCancelLoad[e.url]=!0},e.clearCache=function(t){delete e.uiCache[t],delete this.uiIsLoading[t],delete this.uiIsCancelLoad[t]},e.addEventListener=function(e,t){this.events[e]||(this.events[e]=[]),this.events[e].push(t)},e.removeEventListener=function(e,t){if(this.events[e]){var r=this.events[e].indexOf(t);-1!=r&&this.events[e].splice(r,1)}},e.show=function(t,r){var s=this;return new Promise((function(c,u){return n(s,void 0,void 0,(function(){var n,s,l,p,f,d,h,_,v,y,g,m,b;return o(this,(function(o){switch(o.label){case 0:if(CC_DEBUG&&"TouchEffect"!==t.name&&console.log("ui.show() === =>",t.name),this.resolves[t.url]||(this.resolves[t.url]=[]),this.resolves[t.url].push(c),this.uiIsLoading[t.url])return[2];o.label=1;case 1:return o.trys.push([1,10,,11]),r=r||a.gameUiLayer,n=void 0,e.uiCache[t.url]&&!cc.isValid(e.uiCache[t.url].node)&&delete e.uiCache[t.url],e.uiCache[t.url]?[3,8]:(this.uiIsLoading[t.url]=!0,s=void 0,t.bundleName?[4,i.ResLoader.asyncLoadByBundle(t.bundleName,t.url,cc.Prefab)]:[3,3]);case 2:return s=o.sent(),[3,5];case 3:return[4,i.ResLoader.asyncLoad(t.url,cc.Prefab)];case 4:s=o.sent(),o.label=5;case 5:return(n=cc.instantiate(s)).__config__=t,t.page&&this.lastPage&&this.hide(this.lastPage),t.page&&(this.lastPage=n),this.uiIsCancelLoad[t.url]&&(this.uiIsLoading[t.url]=!1,delete this.uiIsCancelLoad[t.url]),e.uiCache[t.url]={},e.uiCache[t.url].node=n,e.uiCache[t.url].ease=t.ease,t.modal?[4,i.ResLoader.asyncLoad(this.modalUrl,cc.Prefab)]:[3,7];case 6:l=o.sent(),p=cc.instantiate(l),r.addChild(p),C=n,p.on(cc.Node.EventType.TOUCH_START,(function(t){e.hide(C)})),e.uiCache[t.url].modal=p,o.label=7;case 7:for(null==n.parent?r.addChild(n):n.parent!=r&&n.setParent(r),this.uiIsLoading[t.url]=!1,null!=t.ease&&null==(_=n.getComponent(t.ease))&&(_=n.addComponent(t.ease)),f=this.events.create,d=0;d<(null==f?void 0:f.length);d++)f[d].apply(this,[t]);return[3,9];case 8:this.uiIsLoading[t.url]=!1,n=e.uiCache[t.url].node,t.page&&this.lastPage&&n!==this.lastPage&&this.hide(this.lastPage),t.page&&(this.lastPage=n),t.modal&&((h=e.uiCache[t.url].modal).parent||r.addChild(h)),n.active=!0,o.label=9;case 9:if(n.parent!=r&&n.setParent(r),e.uiCache[t.url].parent=r,n.setSiblingIndex(r.childrenCount),null!=t.ease&&(null==(_=n.getComponent(t.ease))||_.show()),v=this.resolves[t.url]){for(y=0;y<v.length;y++)(0,v[y])(n);v.length=0}for(g=this.events.open,m=0;m<(null==g?void 0:g.length);m++)g[m].apply(this,[t]);return[3,11];case 10:return b=o.sent(),this.uiIsLoading[t.url]=!1,u(b),[3,11];case 11:return[2]}var C}))}))}))},e.addUICache=function(t,r,n){try{var o=cc.instantiate(r);e.uiCache[t.url]||(e.uiCache[t.url]={}),e.uiCache[t.url].node=o,n.addChild(o),e.uiCache[t.url].parent=n,o.setSiblingIndex(o.parent.childrenCount),o.active=!1}catch(e){console.error("UI.addUICache throw error-> ",e)}},e.getComponent=function(e,t){return n(this,void 0,void 0,(function(){var t,r,n;return o(this,(function(o){return e?((t=this.uiCache[e.url])&&(r=t.node),e.comp?r?(n=r.getComponent(e.comp),this.uiCache[e.url].comp=n,[2,n]):[2,void 0]:(console.error("必须为配置".concat(e,"配置组件引用！")),[2,void 0])):(console.error("获取组件时传入的config不能为空！"),[2,void 0])}))}))},e.hide=function(e){var t;(null==(t=e instanceof cc.Component?e.node.__config__:e.__config__)?void 0:t.url)&&this.hideUI(t)},e.hideUI=function(t){var r=this,n=e.uiCache[t.url];if(n){if(null!=t.ease){var o=n.node.getComponent(t.ease);null!=o?(o.onCloseEaseComplete=function(){n.node.active=!1,r.dispatchClose(t)},o.hide()):(n.node.active=!1,this.dispatchClose(t))}else n.node.active=!1,this.dispatchClose(t);t.modal&&n.modal.setParent(null)}},e.dispatchClose=function(e){for(var t=this.events.close,r=0;r<(null==t?void 0:t.length);r++)t[r].apply(this,[e])},e.hideLayer=function(e){e.children.forEach((function(e){e.active=!1}))},e.uiCache={},e.uiIsLoading={},e.uiIsCancelLoad={},e.events={},e.resolves={},e.modalUrl="prefabs/modal/Modal",e}();t.UI=s},9435:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getUrlParameterValue=function(e){if(!globalThis.location)return"";for(var t=globalThis.location.search.substring(1).split("&"),r=0;r<t.length;r++){var n=t[r].split("=");if(n[0]===e)return decodeURIComponent(n[1])}return""}},1895:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ReactivePos=void 0,t.reactive=function(e){var t=e.target,r=e.propertyName,n=e.pos,o=e.callback,i=a.get(t.constructor);i||(i={},a.set(t.constructor,i)),i[r+n]=o},t.watch=function(){return function(e,t){var r,n=i(((r={})[t]=e[t],r),e,t);Object.defineProperty(e,t,{get:function(){return n[t]},set:function(e){n[t]=e},enumerable:!0,configurable:!0})}};var n,o=r(259),a=new Map;function i(e,t,r){if(!(0,o.isObject)(e))return e;var n={get:function(e,n,a){var s=Reflect.get(e,n,a);return(0,o.isObject)(s)?i(s,t,r):s},set:function(e,n,s,c){var u=e[n],l=Reflect.set(e,n,s,c);if(u!==s){var p=a.get(t.constructor);p&&Object.keys(p).forEach((function(e){e.includes(r)&&p[e]&&p[e](e,u,s)})),(0,o.isObject)(s)&&i(s,t,r)}return l}};return new Proxy(e,n)}!function(e){e.RandomChangeScoreLbColorTraitIsComboReachTargetTraitInit="RandomChangeScoreLbColorTraitIsComboReachTargetTraitInit",e.GuideFirstLifeTrait="GuideFirstLifeTrait",e.MoreTimeToOpreateStateTraitIsBlocksProducerTouchShowGameOverStatus="MoreTimeToOpreateStateTraitIsBlocksProducerTouchShowGameOverStatus"}(n||(t.ReactivePos=n={}))},8958:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.EventManager=void 0;var n=r(5855),o=function(){function e(){}return Object.defineProperty(e,"events",{get:function(){return this._events},enumerable:!1,configurable:!0}),e.registerEvent=function(e){if(null!==e){var t=e.eventClass;if(t){void 0===this._events.get(t)&&this._events.set(t,[]),e.eventClass;var r=this._events.get(e.eventClass),n=!1;if(r){for(var o=0,a=null==r?void 0:r.length;o<a;o++)if(r[o].proxy===e.proxy){n=!0;break}n||r.push(e)}}}},e.dispatchModuleEvent=function(e){if(e){var t=e.getClass(),r=this._events.get(t);if(r)for(var o=0,a=r.length;o<a;o++){var i=r[o].proxy,s=i.moduleType;s!==n.ModuleType.Common&&s!==n.ModuleManager.moduleType||null==i||i.receivedEvents(e)}var c=this._eventAllCompleted.get(t);if(c)if(n.ModuleManager.moduleType===n.ModuleType.Common)for(var u in c){var l;if(l=c[u])for(var p=0;p<l.length;p++)(d=l[p])&&d(e)}else if(l=c[n.ModuleManager.moduleType])for(var f=0;f<l.length;f++){var d;(d=l[f])&&d(e)}}},e.dispatchModuleEventAsync=function(t){return new Promise((function(r){t._callback=function(){return r()},e.dispatchModuleEvent(t)}))},e.onEventAllCompleted=function(e,t,r){var n=this._eventAllCompleted.get(t);n||(n={},this._eventAllCompleted.set(t,n)),n[e]||(n[e]=[]),n[e].push(r)},e._events=new Map,e._eventAllCompleted=new Map,e}();t.EventManager=o},5047:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.EventVo=void 0;var r=function(){function e(){this._id=0,e._uniqueID+=1,this._id=e._uniqueID}return Object.defineProperty(e.prototype,"id",{get:function(){return this._id},enumerable:!1,configurable:!0}),e._uniqueID=0,e}();t.EventVo=r},3478:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Module=void 0;var r=function(){function e(){this._registerProxys=[],this._proxyClassMap=new Map}return Object.defineProperty(e.prototype,"proxyClassMap",{get:function(){return this._proxyClassMap},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"moduleType",{get:function(){return this._moduleType},enumerable:!1,configurable:!0}),e.prototype.registerProxys=function(){return[]},e.prototype.getProxy=function(e){return this._proxyClassMap.get(e)},e.prototype.startProxy=function(e){this._registerProxys=this.registerProxys();for(var t=0,r=this._registerProxys.length;t<r;t++){var n=this._registerProxys[t];if(!this._proxyClassMap.has(n)){var o=new n(this);o._moduleType=e,this._proxyClassMap.set(n,o),o.startEvents()}}},e}();t.Module=r},1924:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ModuleEvent=void 0;var r=function(){function e(){this._callback=null}return Object.defineProperty(e.prototype,"callback",{get:function(){return this._callback},enumerable:!1,configurable:!0}),e.prototype.getClass=function(){return this.constructor},e}();t.ModuleEvent=r},5855:(e,t)=>{var r;Object.defineProperty(t,"__esModule",{value:!0}),t.ModuleManager=t.ModuleType=void 0,function(e){e.Common="common",e.Class="class",e.Chapter="journey"}(r||(t.ModuleType=r={}));var n=function(){function e(){}return Object.defineProperty(e,"moduleType",{get:function(){return this._moduleType},enumerable:!1,configurable:!0}),e.setCurrentModuleType=function(e){this._moduleType=e},Object.defineProperty(e,"moduleList",{get:function(){return this._moduleList},enumerable:!1,configurable:!0}),e.resigerModule=function(e){this._modules=e},e.startModule=function(e){for(var t=0,r=this._modules.length;t<r;t++){var n=this._modules[t];if(!this._moduleConstructorMaps.has(n)){this._moduleConstructorMaps.set(n,!0);var o=new n;o._moduleType=e,this._moduleList[t]=o,o.startProxy(e)}}},e._moduleList=[],e._moduleConstructorMaps=new Map,e._moduleType=r.Common,e}();t.ModuleManager=n},8852:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Proxy=void 0;var n=r(8958),o=r(5047),a=r(5855),i=function(){function e(e){this._eventsConstructorMaps=new Map,this._module=e}return Object.defineProperty(e.prototype,"moduleType",{get:function(){return this._moduleType},enumerable:!1,configurable:!0}),e.prototype.onInit=function(){},e.prototype.registerEvents=function(){return null},e.prototype.getClass=function(){return this.constructor},e.prototype.getModule=function(){return this._module},e.prototype.receivedEvents=function(e){},e.prototype.startEvents=function(){var e=this.registerEvents();if(null!==e)for(var t=0,r=e.length;t<r;t++){var a=e[t];if(!this._eventsConstructorMaps.has(a)){this._eventsConstructorMaps.set(a,!0);var i=new o.EventVo;i.proxy=this,i.eventClass=a,n.EventManager.registerEvent(i)}}this.onInit()},e.prototype.dispatchModuleEvent=function(e){this.moduleType!==a.ModuleType.Common&&this.moduleType!==a.ModuleManager.moduleType||n.EventManager.dispatchModuleEvent(e)},e.prototype.dispatchModuleEventAsync=function(e){if(this.moduleType===a.ModuleType.Common||this.moduleType===a.ModuleManager.moduleType)return n.EventManager.dispatchModuleEventAsync(e)},e}();t.Proxy=i}},__webpack_module_cache__={};function __webpack_require__(e){var t=__webpack_module_cache__[e];if(void 0!==t)return t.exports;var r=__webpack_module_cache__[e]={exports:{}};return __webpack_modules__[e].call(r.exports,r,r.exports,__webpack_require__),r.exports}var __webpack_exports__={},falcon,AdapterFringe_1,DragonbonesAnim_1,arrays_1,Barrier_1,DeferredPromise_1,First_1,Limiter_1,Sequence_1,WaitFor_1,AudioInfo_1,CacheRender_1,CacheComponents_1,Component_1,Copy_1,UrlCrypto_1,Date_1,DecoratorAdapter_1,DecoratorDebounce_1,DecoratorMeasure_1,DecoratorMemoize_1,DecoratorScreen_1,DecoratorThrottle_1,DecoratorTrait_1,Dot_1,enum_1,Equal_1,Events_1,HotUpdate_1,Http_1,Interval_1,ResLoader_1,numbers_1,RenderingOptimization_1,ObjectPool_1,Random_1,Storage_1,GameLayer_1,TraitConfigInfo_1,Timeout_1,Timer_1,Trait_1,UI_1,Task_1,NativeBridge_1,Url_1,watch_1,EventManager_1,EventVo_1,Module_1,ModuleEvent_1,ModuleManager_1,Proxy_1,Types_1;AdapterFringe_1=__webpack_require__(4164),DragonbonesAnim_1=__webpack_require__(4074),arrays_1=__webpack_require__(9305),Barrier_1=__webpack_require__(2796),DeferredPromise_1=__webpack_require__(2963),First_1=__webpack_require__(4157),Limiter_1=__webpack_require__(9959),Sequence_1=__webpack_require__(8278),WaitFor_1=__webpack_require__(5519),AudioInfo_1=__webpack_require__(9787),CacheRender_1=__webpack_require__(4223),CacheComponents_1=__webpack_require__(1551),Component_1=__webpack_require__(2766),Copy_1=__webpack_require__(2983),UrlCrypto_1=__webpack_require__(9768),Date_1=__webpack_require__(301),DecoratorAdapter_1=__webpack_require__(3571),DecoratorDebounce_1=__webpack_require__(8425),DecoratorMeasure_1=__webpack_require__(7136),DecoratorMemoize_1=__webpack_require__(7084),DecoratorScreen_1=__webpack_require__(3318),DecoratorThrottle_1=__webpack_require__(7202),DecoratorTrait_1=__webpack_require__(7128),Dot_1=__webpack_require__(1479),enum_1=__webpack_require__(3055),Equal_1=__webpack_require__(4591),Events_1=__webpack_require__(1367),HotUpdate_1=__webpack_require__(1239),Http_1=__webpack_require__(4613),Interval_1=__webpack_require__(1655),ResLoader_1=__webpack_require__(3687),numbers_1=__webpack_require__(3019),RenderingOptimization_1=__webpack_require__(6764),ObjectPool_1=__webpack_require__(6014),Random_1=__webpack_require__(7479),Storage_1=__webpack_require__(5447),GameLayer_1=__webpack_require__(4125),TraitConfigInfo_1=__webpack_require__(3287),Timeout_1=__webpack_require__(423),Timer_1=__webpack_require__(1963),Trait_1=__webpack_require__(47),UI_1=__webpack_require__(5805),Task_1=__webpack_require__(2279),NativeBridge_1=__webpack_require__(2698),Url_1=__webpack_require__(9435),watch_1=__webpack_require__(1895),EventManager_1=__webpack_require__(8958),EventVo_1=__webpack_require__(5047),Module_1=__webpack_require__(3478),ModuleEvent_1=__webpack_require__(1924),ModuleManager_1=__webpack_require__(5855),Proxy_1=__webpack_require__(8852),Types_1=__webpack_require__(259),function(e){e.Proxy=Proxy_1.Proxy,e.Module=Module_1.Module,e.ModuleManager=ModuleManager_1.ModuleManager,e.Emitter=Events_1.Emitter,e.EventManager=EventManager_1.EventManager,e.EventVo=EventVo_1.EventVo,e.ModuleEvent=ModuleEvent_1.ModuleEvent,e.adapter={applyAdapterFringe:AdapterFringe_1.applyAdapterFringe},e.dragonbonesAnim=DragonbonesAnim_1.dragonbonesAnim,e.arrays={arraysHaveSameElements:arrays_1.arraysHaveSameElements,shuffleArray:arrays_1.shuffleArray,arraysEqual:arrays_1.arraysEqual,ensureMaxLength:arrays_1.ensureMaxLength},e.async={TimeoutBarrier:Barrier_1.TimeoutBarrier,Barrier:Barrier_1.Barrier,DeferredPromise:DeferredPromise_1.DeferredPromise,firstParallel:First_1.firstParallel,first:First_1.first,Limiter:Limiter_1.Limiter,sequence:Sequence_1.sequence,waitFor:WaitFor_1.waitFor},e.audioInfo=AudioInfo_1.audioInfo,e.cacheRender=CacheRender_1.cacheRender,e.cacheComponents=CacheComponents_1.cacheComponents,e.Component=Component_1.default,e.copy={deepCopy:Copy_1.deepCopy,deepCopyArrayFrom:Copy_1.deepCopyArrayFrom,deepCopySlice:Copy_1.deepCopySlice,deepCopyLoop:Copy_1.deepCopyLoop,deepCopyFixed:Copy_1.deepCopyFixed},e.UrlCrypto=UrlCrypto_1.UrlCrypto,e.date={getLastSomeDays:Date_1.getLastSomeDays,getTodayDate:Date_1.getTodayDate,getDiffDays:Date_1.getDiffDays},e.adapterFringe=DecoratorAdapter_1.adapterFringe,e.throttle=DecoratorThrottle_1.throttle,e.debounce=DecoratorDebounce_1.debounce,e.measure=DecoratorMeasure_1.measure,e.memoize=DecoratorMemoize_1.memoize,e.trait=DecoratorTrait_1.trait,e.templateTrait=DecoratorTrait_1.templateTrait,e.ScreenAdapter=DecoratorScreen_1.ScreenAdapter,e.createDotData=Dot_1.createDotData,e.enumUtils={getKeyByValue:enum_1.getKeyByValue,isValueInEnum:enum_1.isValueInEnum},e.equalUtils={equal:Equal_1.equal},e.hotUpdate=HotUpdate_1.hotUpdate,e.http=Http_1.http,e.intervalUtils={interval:Interval_1.interval},e.NativeBridge=NativeBridge_1.NativeBridge,e.ResLoader=ResLoader_1.ResLoader,e.layerManager=GameLayer_1.layerManager,e.layer={gameUiLayer:GameLayer_1.gameUiLayer,addWidget:GameLayer_1.addWidget,initNodeConfig:GameLayer_1.initNodeConfig},e.numbers={randomInt:numbers_1.randomInt,randomFloat:numbers_1.randomFloat},e.performance={executeRenderingOptimize:RenderingOptimization_1.executeRenderingOptimize},e.ObjectPool=ObjectPool_1.ObjectPool,e.random={randomList:Random_1.randomList},e.storage=Storage_1.storage,e.timeoutUtils={timeout:Timeout_1.timeout},e.traitConfigInfo=TraitConfigInfo_1.traitConfigInfo,e.task=Task_1.task,e.timer={nextFrame:Timer_1.nextFrame,callLater:Timer_1.callLater},e.typeUtils={isString:Types_1.isString,isStringArray:Types_1.isStringArray,isObject:Types_1.isObject,isTypedArray:Types_1.isTypedArray,isNumber:Types_1.isNumber,isIterable:Types_1.isIterable,isBoolean:Types_1.isBoolean,isUndefined:Types_1.isUndefined,isDefined:Types_1.isDefined,isUndefinedOrNull:Types_1.isUndefinedOrNull,isFunction:Types_1.isFunction,getType:Types_1.getType},e.Trait=Trait_1.Trait,e.UI=UI_1.UI,e.url={getUrlParameterValue:Url_1.getUrlParameterValue},e.watchUtils={reactive:watch_1.reactive,watch:watch_1.watch}}(falcon||(falcon={})),window.falcon=falcon})();
//# sourceMappingURL=falcon.js.map