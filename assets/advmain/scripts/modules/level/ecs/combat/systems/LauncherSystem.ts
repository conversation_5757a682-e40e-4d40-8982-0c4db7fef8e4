import { System } from "../../cores/System";
import { Launcher, LauncherData } from "../component/Launcher";

/**
 * 发射器系统
 * 负责更新发射器状态和处理组件清理
 */
export class LauncherSystem extends System {
    init(): void {
        // 只监听组件移除
        this.world.onComponentRemove(Launcher, this.onLauncherComponentRemove, this);
    }

    dispose(): void {
        // 移除监听器，防止内存泄漏
        this.world.offComponentRemove(Launcher, this.onLauncherComponentRemove, this);
    }

    /**
     * 处理发射器组件移除
     * 确保所有发射器资源被正确清理
     */
    private onLauncherComponentRemove = (entityId: number, component: Launcher): void => {
        if (component.launchersData.length === 0) return;
        
        console.log(`[LauncherSystem] 清理实体 ${entityId} 的 ${component.launchersData.length} 个发射器`);
        
        // 先拷贝launchersData，防止在遍历过程中修改数组导致遍历失效
        const launchersDataCopy = [...component.launchersData];
        const launcherHandler = this.world.handlerCenter.launcherHandler;
        
        launchersDataCopy.forEach(launcherData => {
            // 调用完整的destroyLauncher方法，确保完整的移除逻辑
            launcherHandler.destroyLauncherByData(launcherData);
        });
    }

    /**
     * 更新系统
     * @param deltaTime 时间增量
     */
    update(deltaTime: number): void {
        // 获取所有有发射器组件的实体
        const entities = this.world.query([Launcher]);
        const world = this.world;
        const handler = world.handlerCenter.launcherHandler;
        const factory = handler.behavior;
        
        for (const entityId of entities) {
            const launcherComponent = world.getComponent<Launcher>(entityId, Launcher);
            if (!launcherComponent) return;

            const launchers = launcherComponent.launchersData;
            const completedLaunchers: LauncherData[] = [];

            // 更新每个发射器
            for (const launcherData of launchers) {
                const behavior = factory.getLauncherBehaviorById(launcherData.launcherId);
                if (!behavior) continue;
                
                // 直接调用行为更新，行为返回是否已完成
                const isCompleted = behavior.onUpdate ? behavior.onUpdate(launcherData, deltaTime) : false;
                
                // 如果完成了，调用完成回调
                if (isCompleted) {
                    if (behavior.onComplete) {
                        behavior.onComplete(launcherData);
                    }
                    completedLaunchers.push(launcherData);
                }
            }

            // 清理已完成的发射器
            for (const launcherData of completedLaunchers) {
                handler.destroyLauncherByData(launcherData);
            }
        }
    }
} 