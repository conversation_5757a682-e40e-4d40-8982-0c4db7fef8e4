{"frameRate": 30, "name": "gameplay_moreEliminate_tilemapFrame", "version": "5.5", "compatibleVersion": "5.5", "armature": [{"type": "Armature", "frameRate": 30, "name": "armatureName", "aabb": {"x": -240.86, "y": -242.92, "width": 481.94, "height": 486.56}, "bone": [{"name": "root"}, {"name": "zong", "parent": "root"}, {"name": "qiPan<PERSON>e_guang", "parent": "zong"}], "slot": [{"blendMode": "add", "name": "bian_guang_1", "parent": "qiPan<PERSON>e_guang", "color": {"rM": 66, "gM": 22}}, {"blendMode": "add", "name": "bian_guang_2", "parent": "qiPan<PERSON>e_guang", "color": {"rM": 66, "gM": 22}}, {"blendMode": "add", "name": "bian_guang_3", "parent": "qiPan<PERSON>e_guang", "color": {"rM": 66, "gM": 22}}, {"blendMode": "add", "name": "bian_guang_4", "parent": "qiPan<PERSON>e_guang", "color": {"rM": 66, "gM": 22}}, {"blendMode": "add", "name": "bian_guang_5", "parent": "qiPan<PERSON>e_guang", "color": {"rM": 66, "gM": 22}}], "skin": [{"slot": [{"name": "bian_guang_4", "display": [{"type": "mesh", "name": "bian_guang_2", "width": 182, "height": 303, "vertices": [237.26, 66.82, 188.16, -184.18, 237.26, -236.17, 55.26, -236.17, 188.06, 66.82, 237.26, -184.03, 55.26, -184.03, 188.11, -236.17], "uvs": [0, 0, 0.26976, 0.8284, 0, 1, 0.99999, 1, 0.27033, 0, 0, 0.82789, 0.99999, 0.82789, 0.26593, 1], "triangles": [6, 1, 3, 1, 7, 3, 5, 2, 7, 0, 5, 4, 4, 5, 1, 5, 7, 1], "edges": [0, 4, 4, 1, 2, 5, 5, 0, 1, 6, 6, 3, 3, 7, 7, 2], "userEdges": [5, 1, 1, 7]}]}, {"name": "bian_guang_1", "display": [{"name": "bian_guang_1_2", "transform": {"scX": 2, "scY": 2}}]}, {"name": "bian_guang_5", "display": [{"type": "mesh", "name": "bian_guang_3", "width": 205, "height": 42, "vertices": [-207.42, -237.44, -2.42, -237.44, -207.42, -195.44, -2.42, -195.44], "uvs": [0, 2e-05, 1, 2e-05, 0, 0.99998, 1, 0.99998], "triangles": [1, 0, 3, 0, 2, 3], "edges": [0, 1, 1, 3, 3, 2, 2, 0], "userEdges": []}]}, {"name": "bian_guang_2", "display": [{"type": "mesh", "name": "bian_guang_2", "width": 182, "height": 303, "vertices": [-237.04, -66.1, -187.94, 184.9, -237.04, 236.89, -55.04, 236.89, -187.84, -66.1, -237.04, 184.75, -55.04, 184.75, -187.89, 236.89], "uvs": [0, 0, 0.26976, 0.8284, 0, 1, 0.99999, 1, 0.27033, 0, 0, 0.82789, 0.99999, 0.82789, 0.26593, 1], "triangles": [6, 1, 3, 1, 7, 3, 5, 2, 7, 0, 5, 4, 4, 5, 1, 5, 7, 1], "edges": [0, 4, 4, 1, 2, 5, 5, 0, 1, 6, 6, 3, 3, 7, 7, 2], "userEdges": [5, 1, 1, 7]}]}, {"name": "bian_guang_3", "display": [{"type": "mesh", "name": "bian_guang_3", "width": 205, "height": 42, "vertices": [1.01, 194.57, 206.01, 194.57, 1.01, 236.57, 206.01, 236.57], "uvs": [0, 2e-05, 1, 2e-05, 0, 0.99998, 1, 0.99998], "triangles": [1, 0, 3, 0, 2, 3], "edges": [0, 1, 1, 3, 3, 2, 2, 0], "userEdges": []}]}]}], "animation": [{"duration": 25, "name": "in_2", "slot": [{"name": "bian_guang_1", "colorFrame": [{"duration": 6, "curve": [0, 0, 0.5, 1], "value": {"aM": 0, "rM": 0, "gM": 93}}, {"duration": 19, "curve": [0.5, 0, 0.5, 1], "value": {"rM": 0, "gM": 93}}, {"duration": 0, "value": {"aM": 0, "rM": 0, "gM": 93}}]}, {"name": "bian_guang_2", "displayFrame": [{"duration": 25, "value": -1}], "colorFrame": [{"duration": 25}]}, {"name": "bian_guang_3", "displayFrame": [{"duration": 25, "value": -1}], "colorFrame": [{"duration": 25}]}, {"name": "bian_guang_4", "displayFrame": [{"duration": 25, "value": -1}], "colorFrame": [{"duration": 25}]}, {"name": "bian_guang_5", "displayFrame": [{"duration": 25, "value": -1}], "colorFrame": [{"duration": 25}]}]}, {"duration": 60, "name": "in_4", "slot": [{"name": "bian_guang_1", "colorFrame": [{"duration": 6, "curve": [0, 0, 0.5, 1], "value": {"aM": 0, "rM": 41, "gM": 53}}, {"duration": 14, "value": {"rM": 41, "gM": 54}}, {"duration": 40, "curve": [0.5, 0, 0.5, 1], "value": {"rM": 41, "gM": 54}}, {"duration": 0, "value": {"aM": 0, "rM": 41, "gM": 53}}]}, {"name": "bian_guang_2", "colorFrame": [{"duration": 6, "curve": [0, 0, 0.5, 1], "value": {"aM": 0, "rM": 41, "gM": 53}}, {"duration": 14, "value": {"rM": 41, "gM": 54}}, {"duration": 40, "curve": [0.5, 0, 0.5, 1], "value": {"rM": 41, "gM": 54}}, {"duration": 0, "value": {"aM": 0, "rM": 41, "gM": 53}}]}, {"name": "bian_guang_3", "colorFrame": [{"duration": 6, "curve": [0, 0, 0.5, 1], "value": {"aM": 0, "rM": 41, "gM": 53}}, {"duration": 14, "value": {"rM": 41, "gM": 54}}, {"duration": 40, "curve": [0.5, 0, 0.5, 1], "value": {"rM": 41, "gM": 54}}, {"duration": 0, "value": {"aM": 0, "rM": 41, "gM": 53}}]}, {"name": "bian_guang_4", "colorFrame": [{"duration": 6, "curve": [0, 0, 0.5, 1], "value": {"aM": 0, "rM": 41, "gM": 53}}, {"duration": 14, "value": {"rM": 41, "gM": 54}}, {"duration": 40, "curve": [0.5, 0, 0.5, 1], "value": {"rM": 41, "gM": 54}}, {"duration": 0, "value": {"aM": 0, "rM": 41, "gM": 53}}]}, {"name": "bian_guang_5", "colorFrame": [{"duration": 6, "curve": [0, 0, 0.5, 1], "value": {"aM": 0, "rM": 41, "gM": 53}}, {"duration": 14, "value": {"rM": 41, "gM": 54}}, {"duration": 40, "curve": [0.5, 0, 0.5, 1], "value": {"rM": 41, "gM": 54}}, {"duration": 0, "value": {"aM": 0, "rM": 41, "gM": 53}}]}], "ffd": [{"name": "bian_guang_2", "slot": "bian_guang_2", "frame": [{"duration": 60, "curve": [0, 0, 0.5, 1]}, {"duration": 0, "offset": 1, "vertices": [149.1, 0, 0, 0, 0, -63.8, 0, 0, 149.1, 0, 0, -63.8]}]}, {"name": "bian_guang_3", "slot": "bian_guang_3", "frame": [{"duration": 60, "curve": [0, 0, 0.5, 1]}, {"duration": 0, "vertices": [71.75, 0, -80.65, 0, 71.75, 0, -80.65]}]}, {"name": "bian_guang_2", "slot": "bian_guang_4", "frame": [{"duration": 60, "curve": [0, 0, 0.5, 1]}, {"duration": 0, "offset": 1, "vertices": [-162.85, 0, 0, 0, 0, 78.15, 0, 0, -162.85, 0, 0, 78.15]}]}, {"name": "bian_guang_3", "slot": "bian_guang_5", "frame": [{"duration": 60, "curve": [0, 0, 0.5, 1]}, {"duration": 0, "vertices": [73.95, 0, -73.65, 0, 73.95, 0, -73.65]}]}]}, {"duration": 60, "name": "in_5", "slot": [{"name": "bian_guang_1", "colorFrame": [{"duration": 6, "curve": [0, 0, 0.5, 1], "value": {"aM": 0, "rM": 72, "gM": 38}}, {"duration": 14, "value": {"rM": 89, "gM": 75}}, {"duration": 40, "curve": [0.5, 0, 0.5, 1], "value": {"rM": 89, "gM": 75}}, {"duration": 0, "value": {"aM": 0, "rM": 72, "gM": 38}}]}, {"name": "bian_guang_2", "colorFrame": [{"duration": 6, "curve": [0, 0, 0.5, 1], "value": {"aM": 0, "rM": 72, "gM": 38}}, {"duration": 14, "value": {"rM": 89, "gM": 75}}, {"duration": 40, "curve": [0.5, 0, 0.5, 1], "value": {"rM": 89, "gM": 75}}, {"duration": 0, "value": {"aM": 0, "rM": 72, "gM": 38}}]}, {"name": "bian_guang_3", "colorFrame": [{"duration": 6, "curve": [0, 0, 0.5, 1], "value": {"aM": 0, "rM": 72, "gM": 38}}, {"duration": 14, "value": {"rM": 89, "gM": 75}}, {"duration": 40, "curve": [0.5, 0, 0.5, 1], "value": {"rM": 89, "gM": 75}}, {"duration": 0, "value": {"aM": 0, "rM": 72, "gM": 38}}]}, {"name": "bian_guang_4", "colorFrame": [{"duration": 6, "curve": [0, 0, 0.5, 1], "value": {"aM": 0, "rM": 72, "gM": 38}}, {"duration": 14, "value": {"rM": 89, "gM": 75}}, {"duration": 40, "curve": [0.5, 0, 0.5, 1], "value": {"rM": 89, "gM": 75}}, {"duration": 0, "value": {"aM": 0, "rM": 72, "gM": 38}}]}, {"name": "bian_guang_5", "colorFrame": [{"duration": 6, "curve": [0, 0, 0.5, 1], "value": {"aM": 0, "rM": 72, "gM": 38}}, {"duration": 14, "value": {"rM": 89, "gM": 75}}, {"duration": 40, "curve": [0.5, 0, 0.5, 1], "value": {"rM": 89, "gM": 75}}, {"duration": 0, "value": {"aM": 0, "rM": 72, "gM": 38}}]}], "ffd": [{"name": "bian_guang_2", "slot": "bian_guang_2", "frame": [{"duration": 60, "curve": [0, 0, 0.5, 1]}, {"duration": 0, "offset": 1, "vertices": [149.1, 0, 0, 0, 0, -63.8, 0, 0, 149.1, 0, 0, -63.8]}]}, {"name": "bian_guang_3", "slot": "bian_guang_3", "frame": [{"duration": 60, "curve": [0, 0, 0.5, 1]}, {"duration": 0, "vertices": [71.75, 0, -80.65, 0, 71.75, 0, -80.65]}]}, {"name": "bian_guang_2", "slot": "bian_guang_4", "frame": [{"duration": 60, "curve": [0, 0, 0.5, 1]}, {"duration": 0, "offset": 1, "vertices": [-162.85, 0, 0, 0, 0, 78.15, 0, 0, -162.85, 0, 0, 78.15]}]}, {"name": "bian_guang_3", "slot": "bian_guang_5", "frame": [{"duration": 60, "curve": [0, 0, 0.5, 1]}, {"duration": 0, "vertices": [73.95, 0, -73.65, 0, 73.95, 0, -73.65]}]}]}, {"duration": 60, "name": "in_3", "slot": [{"name": "bian_guang_1", "colorFrame": [{"duration": 6, "curve": [0, 0, 0.5, 1], "value": {"aM": 0, "rM": 41, "gM": 53}}, {"duration": 14, "value": {"rM": 41, "gM": 54}}, {"duration": 40, "curve": [0.5, 0, 0.5, 1], "value": {"rM": 41, "gM": 54}}, {"duration": 0, "value": {"aM": 0, "rM": 41, "gM": 53}}]}, {"name": "bian_guang_2", "colorFrame": [{"duration": 6, "curve": [0, 0, 0.5, 1], "value": {"aM": 0, "rM": 41, "gM": 53}}, {"duration": 14, "value": {"rM": 41, "gM": 54}}, {"duration": 40, "curve": [0.5, 0, 0.5, 1], "value": {"rM": 41, "gM": 54}}, {"duration": 0, "value": {"aM": 0, "rM": 41, "gM": 53}}]}, {"name": "bian_guang_3", "colorFrame": [{"duration": 6, "curve": [0, 0, 0.5, 1], "value": {"aM": 0, "rM": 41, "gM": 53}}, {"duration": 14, "value": {"rM": 41, "gM": 54}}, {"duration": 40, "curve": [0.5, 0, 0.5, 1], "value": {"rM": 41, "gM": 54}}, {"duration": 0, "value": {"aM": 0, "rM": 41, "gM": 53}}]}, {"name": "bian_guang_4", "colorFrame": [{"duration": 6, "curve": [0, 0, 0.5, 1], "value": {"aM": 0, "rM": 41, "gM": 53}}, {"duration": 14, "value": {"rM": 41, "gM": 54}}, {"duration": 40, "curve": [0.5, 0, 0.5, 1], "value": {"rM": 41, "gM": 54}}, {"duration": 0, "value": {"aM": 0, "rM": 41, "gM": 53}}]}, {"name": "bian_guang_5", "colorFrame": [{"duration": 6, "curve": [0, 0, 0.5, 1], "value": {"aM": 0, "rM": 41, "gM": 53}}, {"duration": 14, "value": {"rM": 41, "gM": 54}}, {"duration": 40, "curve": [0.5, 0, 0.5, 1], "value": {"rM": 41, "gM": 54}}, {"duration": 0, "value": {"aM": 0, "rM": 41, "gM": 53}}]}], "ffd": [{"name": "bian_guang_2", "slot": "bian_guang_2", "frame": [{"duration": 60, "curve": [0, 0, 0.5, 1]}, {"duration": 0, "offset": 1, "vertices": [149.1, 0, 0, 0, 0, -63.8, 0, 0, 149.1, 0, 0, -63.8]}]}, {"name": "bian_guang_3", "slot": "bian_guang_3", "frame": [{"duration": 60, "curve": [0, 0, 0.5, 1]}, {"duration": 0, "vertices": [71.75, 0, -80.65, 0, 71.75, 0, -80.65]}]}, {"name": "bian_guang_2", "slot": "bian_guang_4", "frame": [{"duration": 60, "curve": [0, 0, 0.5, 1]}, {"duration": 0, "offset": 1, "vertices": [-162.85, 0, 0, 0, 0, 78.15, 0, 0, -162.85, 0, 0, 78.15]}]}, {"name": "bian_guang_3", "slot": "bian_guang_5", "frame": [{"duration": 60, "curve": [0, 0, 0.5, 1]}, {"duration": 0, "vertices": [73.95, 0, -73.65, 0, 73.95, 0, -73.65]}]}]}], "defaultActions": [{"gotoAndPlay": "in_2"}]}]}