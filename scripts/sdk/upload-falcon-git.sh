#!/bin/bash

# 定义颜色
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# 输出带颜色的信息
function log_info() {
  echo -e "${GREEN}INFO: $1${NC}"
}

function log_warning() {
  echo -e "${YELLOW}WARNING: $1${NC}"
}

function log_error() {
  echo -e "${RED}ERROR: $1${NC}"
  exit 1
}

function show_usage() {
  echo -e "${YELLOW}用法: $0 <提交信息>${NC}"
  echo -e "${YELLOW}示例: $0 \"修复了某某bug\"${NC}"
  exit 1
}

# 检查是否提供了提交信息
if [ $# -eq 0 ]; then
  log_error "未提供提交信息!"
  show_usage
fi

# 获取提交信息
COMMIT_MESSAGE="$1"

# 检查文件是否存在
if [ ! -d "dist" ]; then
  log_error "dist/ 文件夹不存在，请先构建项目"
fi

if [ ! -f "typings/falcon.d.ts" ]; then
  log_error "typings/falcon.d.ts 文件不存在，请先构建项目"
fi

# 显示要上传的文件信息
log_info "将要上传以下内容:"
log_info "- dist/ 文件夹 ($(du -sh dist | cut -f1))"
log_info "- typings/falcon.d.ts ($(du -h typings/falcon.d.ts | cut -f1))"
log_info "提交信息: \"$COMMIT_MESSAGE\""

# 创建临时目录
TEMP_DIR=$(mktemp -d)
log_info "创建临时目录: $TEMP_DIR"

# 克隆仓库
REPO_URL="https://git.7k7k.com/chenyong/block-blast-falcon.git"
log_info "克隆仓库: $REPO_URL"
git clone $REPO_URL $TEMP_DIR > /dev/null 2>&1
if [ $? -ne 0 ]; then
  log_error "克隆仓库失败"
fi

# 初始化变量
CHANGES_DETECTED=false

# 检查仓库中是否存在相关文件夹和文件
if [ ! -d "$TEMP_DIR/dist" ]; then
  log_info "远程仓库中不存在dist文件夹，将执行首次上传"
  CHANGES_DETECTED=true
elif [ ! -f "$TEMP_DIR/falcon.d.ts" ]; then
  log_info "远程仓库中不存在falcon.d.ts文件，将执行首次上传"
  CHANGES_DETECTED=true
else
  log_info "检查文件差异..."
  
  # 检查dist文件夹差异
  echo -e "${BLUE}正在比较dist目录差异...${NC}"
  # 这里我们直接比较本地dist目录与远程仓库中的dist目录
  DIFF_OUTPUT=$(diff -r "dist" "$TEMP_DIR/dist" 2>&1)
  DIFF_STATUS=$?
  
  if [ $DIFF_STATUS -eq 0 ]; then
    log_warning "dist/ 文件夹内容没有变化"
  else
    CHANGES_DETECTED=true
    echo -e "${BLUE}dist/ 文件夹内容有变动${NC}"
    # 显示部分差异内容
    echo -e "${BLUE}差异摘要:${NC}"
    echo "$DIFF_OUTPUT" | head -n 5
  fi
  
  # 检查类型定义文件差异
  echo -e "${BLUE}正在比较falcon.d.ts文件差异...${NC}"
  # 这里我们直接比较本地typings/falcon.d.ts与远程仓库中的falcon.d.ts
  DIFF_TS_OUTPUT=$(diff "typings/falcon.d.ts" "$TEMP_DIR/falcon.d.ts" 2>&1)
  DIFF_TS_STATUS=$?
  
  if [ $DIFF_TS_STATUS -eq 0 ]; then
    log_warning "falcon.d.ts 文件没有变化"
  else
    CHANGES_DETECTED=true
    echo -e "${BLUE}falcon.d.ts 文件有变动${NC}"
    # 显示部分差异内容
    echo -e "${BLUE}差异摘要:${NC}"
    echo "$DIFF_TS_OUTPUT" | head -n 5
  fi
fi

# 如果没有变化，退出
if [ "$CHANGES_DETECTED" = false ]; then
  log_info "所有文件都没有变化，无需提交"
  rm -rf $TEMP_DIR
  exit 0
fi

# 询问用户是否继续
echo -e "${YELLOW}是否继续上传? (y/n)${NC}"
read -r response
if [[ ! "$response" =~ ^[Yy]$ ]]; then
  log_info "操作已取消"
  rm -rf $TEMP_DIR
  exit 0
fi

# 复制文件到临时目录
log_info "复制文件到临时目录"
# 清空目标dist目录（如果存在）
if [ -d "$TEMP_DIR/dist" ]; then
  rm -rf "$TEMP_DIR/dist"
fi
# 复制整个dist文件夹
mkdir -p "$TEMP_DIR/dist"
cp -R "dist/"* "$TEMP_DIR/dist/"
cp "typings/falcon.d.ts" "$TEMP_DIR/"

# 进入临时目录
cd $TEMP_DIR

# 添加文件到git
log_info "添加文件到git"
git add dist/ falcon.d.ts

# 提交更改
log_info "提交更改"
git commit -m "feat：$COMMIT_MESSAGE"

# 推送到远程仓库
log_info "推送到远程仓库"
git push
if [ $? -ne 0 ]; then
  log_error "推送到远程仓库失败"
fi

# 清理临时目录
log_info "清理临时目录"
cd - > /dev/null
rm -rf $TEMP_DIR

log_info "上传完成"
