/**
 * 子游戏Bundle配置接口
 */
export interface ISubGameConfig {
    // Bundle名称
    bundleName: string;
    // Bundle版本号
    version: string;
    // 首场景名称
    entryScene?: string;
}

/**
 * 远程资源配置接口
 */
export interface IRemoteConfig {
    // 基础URL
    baseUrl: string;
    // 库文件路径
    libsPath: string;
    // Bundle路径
    bundlePath: string;
    // 必需的库文件列表
    requiredLibs: string[];
}

/**
 * 消息系统状态接口
 */
export interface IMessageSystemStatus {
    mainGameListenerRegistered: boolean;
    subGameListenerRegistered: boolean;
}

/**
 * 消息处理回调函数类型
 */
export type MessageHandler = (type: string, data: any) => void;

/**
 * 子游戏桥接类
 * 负责主游戏和子游戏之间的数据流转、bundle管理、基础接口
 * 通过window对象暴露给子游戏调用
 * 设计用于不同团队协作开发，提供稳定的双向通信API接口
 */
export default class AdvGameBridge {
    // Bridge版本信息
    public static readonly BRIDGE_VERSION: string;

    // ================================
    // 核心对外接口
    // ================================

    /**
     * 获取桥接类单例实例
     */
    public static getInstance(): AdvGameBridge;

    /**
     * 初始化桥接类
     * @param config 可选的配置参数
     */
    public initialize(config?: any): Promise<boolean>;

    /**
     * 进入子游戏
     * @param bundleName bundle名称，默认为 'advmain'
     * @param sceneName 场景名称（可选，如果不指定则使用配置中的默认场景）
     * @param version 版本号（可选，如果不指定则使用配置中的版本）
     */
    public enterGame(bundleName?: string, sceneName?: string, version?: string): Promise<boolean>;

    // ================================
    // 数据管理接口
    // ================================

    /**
     * 获取基础数据
     * @param key 数据键，如果不指定则返回所有数据的副本
     */
    public getBaseData(key?: string): any;

    // ================================
    // Bundle配置管理
    // ================================

    /**
     * 获取子游戏配置
     * @param bundleName bundle名称
     */
    public getSubGameConfig(bundleName: string): ISubGameConfig | null;

    /**
     * 获取所有子游戏配置
     */
    public getAllSubGameConfigs(): Record<string, ISubGameConfig>;

    /**
     * 获取远程资源配置
     * 提供给子游戏使用，用于获取远程库文件和资源的配置信息
     */
    public getRemoteConfig(): IRemoteConfig;

    // ================================
    // 基础工具方法
    // ================================

    /**
     * 返回主游戏
     */
    public returnToMainGame(): void;

    /**
     * 检查是否已初始化
     */
    public isInitialized(): boolean;

    /**
     * 销毁桥接类
     */
    public destroy(): void;

    // ================================
    // 消息系统（团队协作接口）
    // ================================

    /**
     * 子游戏发送消息到主游戏（推荐接口）
     * 提供稳定的API给子游戏团队使用
     * @param type 消息类型
     * @param data 消息数据（可选）
     */
    public sendToMainGame(type: string, data?: any): void;

    /**
     * 主游戏发送消息到子游戏
     * 提供给主游戏团队使用
     * @param type 消息类型
     * @param data 消息数据（可选）
     */
    public sendToSubGame(type: string, data?: any): void;

    /**
     * 子游戏监听来自主游戏的消息
     * 提供给子游戏团队使用
     * @param callback 消息处理回调函数
     */
    public onMessageFromMainGame(callback: MessageHandler): void;

    /**
     * 子游戏取消监听来自主游戏的消息
     */
    public offMessageFromMainGame(): void;

    /**
     * 主游戏监听来自子游戏的消息
     * 提供给主游戏团队使用
     * @param callback 消息处理回调函数
     */
    public onMessageFromSubGame(callback: MessageHandler): void;

    /**
     * 主游戏取消监听来自子游戏的消息
     */
    public offMessageFromSubGame(): void;

    /**
     * 获取消息系统状态（调试用）
     */
    public getMessageSystemStatus(): IMessageSystemStatus;
}

/**
 * 全局桥接实例
 */
export declare const advGameBridge: AdvGameBridge;

// ================================
// 全局类型声明
// ================================

declare global {
    interface Window {
        AdvGameBridge: AdvGameBridge;
    }
}