import { basicSystems, SystemsRegistry } from "../../registry/SystemRegistry";
import { StyleOrange } from "../ConsoleStyle";
import { World } from "../World";
import { System } from "../System";

export class SystemCenter {
    private world: World;
    /** 系统列表 */
    private systems: System[];
    constructor(world: World) {
        this.world = world;
        this.systems = [];
        this.initGameSystems();
    }
    public updateSystems(dt: number): void {
        this.systems.forEach((system) => system.update(dt));
    }
    /**
     * 初始化游戏系统
     */
    private initGameSystems(): void {
        const levelData = this.world.configCenter.levelConfig;
        if (!levelData) {
            console.error('没有关卡数据');
            return;
        }

        for (const system of basicSystems) {
            this.addSystemByName(system);
        }
        for (const systemId of levelData.sys) {
            this.addSystemById(systemId);
    }
    }
    /**
     * 添加系统
     * @param systemId 系统ID
     */
    addSystemById(systemId: string): void {
        const systemConfig = this.world.configCenter.getSystemConfig(systemId);
        if (!systemConfig) {
            console.warn(`系统 ${systemId} 不存在`);
            return;
        }
        const systemClass = SystemsRegistry[systemConfig.systemName];
        if (!systemClass) {
            console.warn(`系统 ${systemId} 不存在`);
            return;
        }
        if (systemConfig.close) {
            console.log(`%c关闭底板配置系统%c:${systemConfig.systemId}\n\n描述:${systemConfig.systemName}`, StyleOrange, '');
            return;
        }
        if (systemConfig.replaceSystemId) {
            console.log(
                `%c替换底板配置系统%c:${systemConfig.systemId}\n\n原系统(${systemConfig.systemId})->新系统(${systemConfig.replaceSystemId})`,
                StyleOrange,
                '',
            );
            this.addSystemById(systemConfig.replaceSystemId);
            return;
        }
        const system = new systemClass(this.world, systemConfig);
        this.systems.push(system);
        this.systems.sort((a, b) => a.priority - b.priority);
    }
    /**
     * 添加系统
     * @param systemName 系统名称
     */
    addSystemByName(systemName: string): void {
        const systemClass = SystemsRegistry[systemName];
        if (!systemClass) {
            console.warn(`系统 ${systemName} 不存在`);
            return;
        }
        const system = new systemClass(this.world);
        this.systems.push(system);
        this.systems.sort((a, b) => a.priority - b.priority);
    }
}