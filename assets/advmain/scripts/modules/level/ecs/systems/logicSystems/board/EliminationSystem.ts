import BoardComponent from '../../../components/board/BoardComponent';
import NodeComponent from '../../../components/NodeComponent';
import ClearComponent from '../../../components/special/ClearComponent';
import { System } from '../../../cores/System';
import { ECSEvent, GameEventMap } from '../../../GameEvent';
import { IPutBlockBackInfo } from '../../../GameEventData';
import { RelationName } from '../../../cores/center/EntityCenter/WorldRelation';
import { IPoint } from '../../../define/EcsDefine';

/**消除系统 */

export class EliminationSystem extends System {
    init(): void {
        this.world.eventBus.on(ECSEvent.GameEvent.PUT_BLOCK_BACK, this.checkFullLines, this);
        this.world.eventBus.on(ECSEvent.GameEvent.CELL_ELIMINATION, this.checkCellElimination, this);
    }
    dispose(): void {
        this.world.eventBus.off(ECSEvent.GameEvent.PUT_BLOCK_BACK, this.checkFullLines, this);
        this.world.eventBus.off(ECSEvent.GameEvent.CELL_ELIMINATION, this.checkCellElimination, this);
    }

    /**检查是否触发消除 */
    checkCellElimination(d: GameEventMap[ECSEvent.GameEvent.CELL_ELIMINATION]) {
        if ((this.world.getComponent(d.entity, ClearComponent)?.code & d.clearCode) === d.clearCode) {
            this.world.eventBus.emitEntityEvent(d.entity, ECSEvent.EntityEvent.ELIMINATIONED);
        }
    }

    // PUT_BLOCK_BACK 事件回调入口
    checkFullLines(putBlockBackInfo: IPutBlockBackInfo) {
        if (!putBlockBackInfo.isSuccess) return;
        const boardEntity = this.world
            .query([BoardComponent])
            .find((eid) => this.world.getComponent(eid, BoardComponent).boardId === putBlockBackInfo.boardId);
        const board = this.world.getComponent(boardEntity, BoardComponent);
        const rowArr = Array.from({ length: board.rCount }, (_, i) => i);
        const colArr = Array.from({ length: board.cCount }, (_, i) => i);
        const clearRow = new Set<number>(rowArr);
        const clearCol = new Set<number>(colArr);
        console.log('mirror put back', putBlockBackInfo.boardId);
        this.world.utilCenter.boardUtil.boardSlotEach(board, (slot) => {
            if (this.world.utilCenter.boardUtil.getSlotThrough(slot)) return;
            clearRow.delete(slot.r);
            clearCol.delete(slot.c);
        });
        if (clearRow.size === 0 && clearCol.size === 0) return; // 无可消行列

        const clears: number[] = this.clearSlots(board, clearRow, clearCol);

        const clearAreaCenterPos = this.calculateClearAreaCenter(board, { clearRow: Array.from(clearRow), clearCol: Array.from(clearCol) });
        // 发送消除事件
        this.world.eventBus.emit(ECSEvent.GameEvent.ELIMINATION, { clearRow, clearCol, clears, putBlockBackInfo, boardEntity, clearAreaCenterPos });
    }

    /**执行消除并返回被消除的 Cell 实体列表*/
    private clearSlots(board: BoardComponent, rowSet: Set<number>, colSet: Set<number>): number[] {
        const clears: number[] = [];
        this.world.utilCenter.boardUtil.boardSlotEach(board, (slot) => {
            if (!rowSet.has(slot.r) && !colSet.has(slot.c)) return;
            const layerEntitys = this.world.getTargets(slot.entity, RelationName.SLOT_CELL);
            const cellEntity = layerEntitys[layerEntitys.length - 1];
            clears.push(cellEntity);
            this.checkCellElimination({ entity: cellEntity, clearCode: 0b1 });
        });
        return clears;
    }

    /**
     * 计算消除区域的中心位置，多消提示词、combo特效、得分特效等使用
     * @param context 执行上下文
     * @param boardComp 棋盘组件
     * @param eliminationInfo 消除信息
     * @returns 中心位置坐标
     */
    private calculateClearAreaCenter(boardComp: BoardComponent, eliminationInfo: any): IPoint {
        const positions: IPoint[] = [];
        // 收集所有消除行的格子位置
        eliminationInfo.clearRow.forEach((rowIndex: number) => {
            for (let col = 0; col < boardComp.cCount; col++) {
                const slotEntity = this.world.getTargets(boardComp.entity, RelationName.PARENT_CHILD, `${rowIndex}_${col}`)[0];
                if (slotEntity) {
                    const nodeComp = this.world.getComponent(slotEntity, NodeComponent);
                    if (nodeComp) {
                        positions.push({ x: nodeComp.x, y: nodeComp.y });
                    }
                }
            }
        });
        // 收集所有消除列的格子位置
        eliminationInfo.clearCol.forEach((colIndex: number) => {
            for (let row = 0; row < boardComp.rCount; row++) {
                const slotEntity = this.world.getTargets(boardComp.entity, RelationName.PARENT_CHILD, `${row}_${colIndex}`)[0];
                if (slotEntity) {
                    const nodeComp = this.world.getComponent(slotEntity, NodeComponent);
                    if (nodeComp) {
                        // 避免重复添加交叉点位置
                        const isDuplicate = positions.some((pos) => Math.abs(pos.x - nodeComp.x) < 1 && Math.abs(pos.y - nodeComp.y) < 1);
                        if (!isDuplicate) {
                            positions.push({ x: nodeComp.x, y: nodeComp.y });
                        }
                    }
                }
            }
        });

        // 计算所有位置的中心点
        if (positions.length === 0) return { x: 0, y: 0 };

        const centerX = positions.reduce((sum, pos) => sum + pos.x, 0) / positions.length;
        const centerY = positions.reduce((sum, pos) => sum + pos.y, 0) / positions.length;

        return { x: centerX, y: centerY };
    }

    update(dt: number) {}
}
