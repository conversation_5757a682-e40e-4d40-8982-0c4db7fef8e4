
import { Loader } from "../../../base/Loader";
import { PrefabConfig } from "../../prefab/PrefabConfig";

const { ccclass, property } = cc._decorator;
/**
 * 启动
 */
@ccclass
export default class Launch extends falcon.Component {
    onLoad() {
        if (CC_DEBUG) {
            // 加载gm模块
            Loader.showUI(PrefabConfig.ToolMain);
        }
    }

    async start() {
        console.log("Launch start")
    }
}
