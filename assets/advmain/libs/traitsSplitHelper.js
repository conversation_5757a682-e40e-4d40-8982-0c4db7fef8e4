/**
 * 因为异步依赖的关系，辅助处理特性抽离到单独文件中后的逻辑，原生纯 __require 无法支持
 */
var _global = "undefined" === typeof window ? global : window;
// 宏必须要加上 _global前缀，否则如果为非调试模式，则有可能将所有代码删除
if (CC_BUILD && false) {
    var bundles = ['main', 'class', 'chapter'];
    const originalDownloadScript = cc.assetManager.downloader._downloaders['bundle'];
    cc.assetManager.downloader._downloaders['bundle'] = function (nameOrUrl, options, onComplete) {
        // originalDownloadScript.apply(this, [nameOrUrl, options, onComplete]);
        let bundleName = cc.path.basename(nameOrUrl);
        let url = nameOrUrl;
        const REGEX = /^(?:\w+:\/\/|\.+\/).+/;
        if (!REGEX.test(url)) url = 'assets/' + bundleName;
        var version = options.version || cc.assetManager.downloader.bundleVers[bundleName];
        var count = 0;
        var config = `${url}/config.${version ? version + '.' : ''}json`;
        let out = null, error = null;

        cc.assetManager.downloader._downloaders['.json'](config, options, function (err, response) {
            if (err) {
                error = err;
            }
            out = response;
            out && (out.base = url + '/');
            count++;
            if (count === 2) {
                onComplete(error, out);
            }
        });

        var ext = CC_DEBUG ? 'js' : 'jsc';
        var js = `${url}/index.${version ? version + '.' : ''}${ext}`;
        var isSplitBundle = bundles.findIndex(v => js === `assets/${v}/index.${ext}`);

        if (isSplitBundle !== -1) {
            if (window.jsb) {
                // 原生平台
                if (CC_DEBUG) {
                    const classIndexUrl = `assets/${bundleName}/index.js`;
                    const jsonUrl = `assets/traits/${bundleName}/config.json`
                    if (jsb.fileUtils.isFileExist(classIndexUrl) && jsb.fileUtils.isFileExist(jsonUrl)) {
                        const scriptContent = jsb.fileUtils.getStringFromFile(classIndexUrl);
                        const jsonContentStr = jsb.fileUtils.getStringFromFile(jsonUrl);
                        mergeJS(bundleName, 'assets', jsonContentStr, scriptContent, function () {
                            count++;
                            if (count === 2) {
                                onComplete(error, out);
                            }
                        });
                    }
                } else {
                    const classIndexUrl = `assets/${bundleName}/index.jsc`;
                    const jsonUrl = `assets/traits/${bundleName}/config.json`;
                    if (jsb.fileUtils.isFileExist(classIndexUrl) && jsb.fileUtils.isFileExist(jsonUrl)) {
                        const jsonContentStr = jsb.fileUtils.getStringFromFile(jsonUrl);
                        const traitList = JSON.parse(jsonContentStr);
                        const traitUrls = [classIndexUrl];
                        for (let i = 0; i < traitList.length; i++) {
                            const traitName = traitList[i];
                            const turl = `assets/traits/${bundleName}/${traitName}.jsc`;
                            traitUrls.push(turl);
                        }
                        try {
                            const combileRequireUrl = traitUrls.join(',');
                            combileRequire(combileRequireUrl);
                            onComplete(error, out);
                        } catch (error) {
                            console.error(`combileRequire-error:`, error.stack);
                            onComplete(error, out);
                        }
                    }
                }
            } else {
                // web 平台支持
                const baseUrl = `${location.origin}${location.pathname}assets`;
                const classUrl = `${baseUrl}/${bundleName}/index.js`;
                //等待依赖特性加载完毕
                fetch(classUrl).then(response => response.text()).then(scriptContent => {
                    const classJsonUrl = `${baseUrl}/traits/${bundleName}/config.json`;
                    fetch(classJsonUrl).then(response => response.text()).then(jsonContentStr => {
                        mergeJS(bundleName, baseUrl, jsonContentStr, scriptContent, function () {
                            count++;
                            if (count === 2) {
                                onComplete(error, out);
                            }
                        });
                    });
                });
            }
        } else {
            cc.assetManager.downloader._downloaders['.js'](js, options, function (err) {
                if (err) {
                    error = err;
                }
                count++;
                if (count === 2) {
                    onComplete(error, out);
                }
            });
        }
    }

    function loadJS(url) {
        return new Promise((c, e) => {
            if (window.jsb) {
                if (jsb.fileUtils.isFileExist(url)) {
                    const scriptContent = jsb.fileUtils.getStringFromFile(url);
                    c(scriptContent);
                }
            } else {
                fetch(url).then(response => response.text()).then(jsonContentStr => {
                    c(jsonContentStr);
                });
            }
        });
    }

    /**
     * 基于配置文件合并 js
     * @param {string} bundleName
     * @param {string} jsonContentStr 
     * @param {string} scriptContent 
     * @param {Function} onComplete
     */
    function mergeJS(bundleName, baseUrl, jsonContentStr, scriptContent, onComplete) {
        const traitList = JSON.parse(jsonContentStr);
        const traitsPromsise = [];
        for (let i = 0; i < traitList.length; i++) {
            const traitName = traitList[i];
            traitsPromsise.push(loadJS(`${baseUrl}/traits/${bundleName}/${traitName}.js`));
        }

        let startTime = Date.now();
        Promise.all(traitsPromsise).then(data => {
            const insertContent = data.join('');
            let count = 4;
            let insertIndex = scriptContent.indexOf("})({");
            if (insertIndex === -1) {
                count = 3;
                insertIndex = scriptContent.indexOf('}({');
            }
            const combinedScriptContent = scriptContent.slice(0, insertIndex + count) + insertContent + scriptContent.slice(insertIndex + count);
            console.log(`traitsSplitHelper-loadjs:`, Date.now() - startTime);
            startTime = Date.now();
            if (window.jsb) {
                try {
                    const writablePath = jsb.fileUtils.getWritablePath();
                    const scriptPath = writablePath + `${bundleName}` + '.js';
                    const result = jsb.fileUtils.writeStringToFile(combinedScriptContent, scriptPath);
                    console.log(`traitsSplitHelper-write:`, Date.now() - startTime);
                    startTime = Date.now();
                    if (result) {
                        jsb.fileUtils.addSearchPath(writablePath);
                        window.require(scriptPath);
                        console.log(`traitsSplitHelper-require:`, Date.now() - startTime);
                        onComplete();
                    }
                } catch (error) {
                    onsole.error(`合并特性，并处理时发生错误：`, error.stack);
                }
            } else {
                // const blob = new Blob([combinedScriptContent], { type: 'application/javascript' });
                // const script = document.createElement('script');
                // const scriptUrl = URL.createObjectURL(blob);
                // script.src = scriptUrl;
                // // 将 script 元素插入到文档中，此时脚本才会开始执行
                // document.body.appendChild(script);

                eval(combinedScriptContent);
                onComplete();
            }
        });
    }
}

