import { game } from '../../../Game';
import { RenderWorld } from '../../cores/RenderWorld';
import { World } from '../../cores/World';
import { ECSEvent } from '../../GameEvent';

const { ccclass } = cc._decorator;

@ccclass
export class Drag<PERSON><PERSON> extends cc.Component {
    private logicWorld: World;
    private renderWorld: RenderWorld;
    private logicEntityId: number;

    /**在通过 addComponent 动态创建时确保持有 renderWorld/logicWorld */
    protected onLoad(): void {
        if (!this.renderWorld) {
            this.renderWorld = game['_renderWorld'];
            this.logicWorld = game['_world'] as any;
        }
    }

    protected start(): void {
        this.addEvents();
    }

    protected addEvents() {
        this.node.on(cc.Node.EventType.TOUCH_START, this.onTouchStart, this);
        this.node.on(cc.Node.EventType.TOUCH_MOVE, this.onTouchMove, this);
        this.node.on(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this);
        this.node.on(cc.Node.EventType.TOUCH_CANCEL, this.onTouchEnd, this);
    }
    protected removeEvents() {
        this.node.off(cc.Node.EventType.TOUCH_START, this.onTouchStart, this);
        this.node.off(cc.Node.EventType.TOUCH_MOVE, this.onTouchMove, this);
        this.node.off(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this);
        this.node.off(cc.Node.EventType.TOUCH_CANCEL, this.onTouchEnd, this);
    }

    private onTouchStart(e: cc.Event.EventTouch) {
        const tPos = e.getLocation();
        const wPos = this.node.parent?.convertToNodeSpaceAR(cc.v3(tPos.x, tPos.y));
        if (!wPos) return;
        this.logicWorld.pushInputCmd({
            event: ECSEvent.GameEvent.DRAG_BLOCK_START,
            data: { entityId: this.logicEntityId, pos: wPos },
        });
        this.node.getChildByName('Bg').active = false;
    }
    private onTouchMove(e: cc.Event.EventTouch) {
        const tPos = e.getLocation();
        const aPos = this.node.parent?.convertToNodeSpaceAR(cc.v3(tPos.x, tPos.y));
        if (!aPos) return;
        this.logicWorld.pushInputCmd({ event: ECSEvent.GameEvent.DRAG_BLOCK_MOVE, data: aPos });
    }
    private onTouchEnd() {
        this.logicWorld.pushInputCmd({ event: ECSEvent.GameEvent.DRAG_BLOCK_END, data: null });
    }
    protected onDestroy() {
        this.removeEvents();
    }

    setLogicEntityId(id: number) {
        this.logicEntityId = id;
    }
}
