import { System } from '../../../cores/System';
import { ECSEvent } from '../../../GameEvent';
import { IPutBlockInfo } from '../../../GameEventData';
import BoardComponent from '../../../components/board/BoardComponent';
import SlotComponent from '../../../components/board/SlotComponent';
import NodeComponent from '../../../components/NodeComponent';
import { TempleteType } from '../../../registry/templete/TempleteRegistry';
import { CellComponent } from '../../../components/board/CellComponent';
import { RelationName } from '../../../cores/center/EntityCenter/WorldRelation';
import { BoardLayer } from '../../../define/BoardDefine';

/**
 * EffectPreviewSystem
 * 当预览形状放下后将消除行/列时，在对应行列显示消除特效。
 * 与 PreviewSystem 互斥：只有检测到可消除行列时才显示本特效，否则 PreviewSystem 生效。
 */
export class EffectPreviewSystem extends System {
    /** 当前激活的行特效，key=`${boardId}_${rowIndex}` */
    private rowEffects: Map<string, number> = new Map();
    /** 当前激活的列特效，key=`${boardId}_${colIndex}` */
    private colEffects: Map<string, number> = new Map();
    /**记录上一次预览位置，key=boardId->key*/
    private lastKeyMap: Map<string, string> = new Map();

    init(): void {
        const bus = this.world.eventBus;
        bus.on(ECSEvent.GameEvent.PREVIEW_BLOCK, this.onPreview, this);
        bus.on(ECSEvent.GameEvent.PUT_BLOCK_BACK, this.clearAll, this);
    }
    dispose(): void {
        const bus = this.world.eventBus;
        bus.off(ECSEvent.GameEvent.PREVIEW_BLOCK, this.onPreview, this);
        bus.off(ECSEvent.GameEvent.PUT_BLOCK_BACK, this.clearAll, this);
    }

    private onPreview(info: IPutBlockInfo) {
        //先处理下 如果是多层棋盘的话返回上层
        info.boardId = this.world.handlerCenter.boardHandler.getTopLayerBoardId(info.boardId);
        const boardEntity = this.world.query([BoardComponent]).find((eid) => this.world.getComponent(eid, BoardComponent).boardId === info.boardId);
        if (boardEntity == null) {
            this.clearAll();
            return;
        }
        const board = this.world.getComponent(boardEntity, BoardComponent);
        if (board.boardId !== info.boardId) return;
        const previewKey = `${info.boardId}_${info.x}_${info.y}_${info.entityId}`;
        if (this.lastKeyMap.get(info.boardId) === previewKey) {
            return; // 位置未变，无需重新计算
        }
        // 位置变化，更新 key
        this.lastKeyMap.set(info.boardId, previewKey);
        // 先校验放置是否合法（含边界 & 占用）
        if (!this.isPlacementValid(board, info)) {
            // 不合法则清理本棋盘特效并退出
            this.removeEffectsOfBoard(info.boardId);
            return;
        }
        const rows = board.rCount;
        const cols = board.cCount;
        const occupancy: boolean[][] = Array.from({ length: rows }, () => Array(cols).fill(false));
        // 既有块
        this.world.handlerCenter.boardHandler.boardSlotEach(board, (slot) => {
            const layerEntitys = this.world.getTargets(slot.entity, RelationName.SLOT_CELL);
            const last = layerEntitys[layerEntitys.length - 1];
            const cell = this.world.getComponent(last, CellComponent);
            if (cell && cell.occupy && cell.through) {
                // 仅统计 through=true
                occupancy[slot.r][slot.c] = true;
            }
        });
        // 预览形状
        info.shape.shape.forEach((rc) => {
            const r = info.y + rc.r;
            const c = info.x + rc.c;
            if (r >= 0 && r < rows && c >= 0 && c < cols) occupancy[r][c] = true;
        });

        const fullRows: number[] = [];
        const fullCols: number[] = [];
        for (let r = 0; r < rows; r++) if (occupancy[r].every((v) => v)) fullRows.push(r);
        for (let c = 0; c < cols; c++) {
            let full = true;
            for (let r = 0; r < rows; r++)
                if (!occupancy[r][c]) {
                    full = false;
                    break;
                }
            if (full) fullCols.push(c);
        }

        if (fullRows.length === 0 && fullCols.length === 0) {
            // 若本棋盘无满行列，先清理本棋盘特效再退出
            this.removeEffectsOfBoard(info.boardId);
            return;
        }

        // 清理本棋盘过期特效，随后添加缺失特效
        this.removeObsoleteEffects(info.boardId, fullRows, fullCols);
        this.updateEffects(board, boardEntity, info.boardId, fullRows, fullCols);
    }

    private updateEffects(board: BoardComponent, boardEntity: number, boardId: string, rows: number[], cols: number[]) {
        // 创建缺失行特效
        rows.forEach((r) => {
            const key = `${boardId}_${r}`;
            if (this.rowEffects.has(key)) return;
            const eid = this.createLineEffect(board, boardEntity, r, true);
            if (eid) this.rowEffects.set(key, eid);
        });
        // 列
        cols.forEach((c) => {
            const key = `${boardId}_${c}`;
            if (this.colEffects.has(key)) return;
            const eid = this.createLineEffect(board, boardEntity, c, false);
            if (eid) this.colEffects.set(key, eid);
        });
    }

    private createLineEffect(board: BoardComponent, boardEntity: number, idx: number, isRow: boolean): number {
        const slotId = this.world.getTargets(board.entity, RelationName.PARENT_CHILD, `${isRow ? idx : 0}_${isRow ? 0 : idx}`)[0];
        const refNode = this.world.getComponent(slotId, NodeComponent);
        if (!refNode) return null;
        const length = isRow ? board.cCount : board.rCount;
        let x: number, y: number, rotation: number;
        if (isRow) {
            const endNode = this.world.getComponent(
                this.world.getTargets(board.entity, RelationName.PARENT_CHILD, `${idx}_${length - 1}`)[0],
                NodeComponent,
            );
            x = (refNode.x + endNode.x) / 2;
            y = refNode.y;
            rotation = 0;
        } else {
            const endNode = this.world.getComponent(
                this.world.getTargets(board.entity, RelationName.PARENT_CHILD, `${length - 1}_${idx}`)[0],
                NodeComponent,
            );
            x = refNode.x;
            y = (refNode.y + endNode.y) / 2;
            rotation = 90;
        }
        const scaleFactor = length / 8;

        // 使用模板创建特效实体
        const effectE = this.world.templeteCenter.createTempleteEntity(
            TempleteType.Render,
            {
                parentEntity: boardEntity,
                ecsViewId: 'ecsview_simple_effect',
            },
            {
                nodeParam: { x, y, angle: rotation, scaleX: scaleFactor },
                renderParam: {
                    childrenPaths: [BoardLayer[BoardLayer.Top]],
                    data: {
                        aniName: 'in_colour_2',
                        src: 'textures/level/spine/board/gameplay_eliminatePrompt_ske',
                        dragonNames: ['gameplay_eliminatePrompt_tex', 'gameplay_eliminatePrompt'],
                        timeScale: 1.3,
                    },
                },
            },
        );
        return effectE;
    }

    private clearAll() {
        this.rowEffects.forEach((eid) => this.world.destroyEntity(eid));
        this.colEffects.forEach((eid) => this.world.destroyEntity(eid));
        this.rowEffects.clear();
        this.colEffects.clear();
        this.lastKeyMap.clear();
    }

    /**
     * 删除当前棋盘已不再满足满行/列条件的特效
     */
    private removeObsoleteEffects(boardId: string, validRows: number[], validCols: number[]) {
        const validRowSet = new Set(validRows.map((r) => `${boardId}_${r}`));
        const validColSet = new Set(validCols.map((c) => `${boardId}_${c}`));

        // 行
        this.rowEffects.forEach((eid, key) => {
            if (key.startsWith(boardId + '_') && !validRowSet.has(key)) {
                this.world.destroyEntity(eid);
                this.rowEffects.delete(key);
            }
        });
        // 列
        this.colEffects.forEach((eid, key) => {
            if (key.startsWith(boardId + '_') && !validColSet.has(key)) {
                this.world.destroyEntity(eid);
                this.colEffects.delete(key);
            }
        });
    }

    /**
     * 清理指定棋盘的全部行列特效
     */
    private removeEffectsOfBoard(boardId: string) {
        // 行
        this.rowEffects.forEach((eid, key) => {
            if (key.startsWith(boardId + '_')) {
                this.world.destroyEntity(eid);
                this.rowEffects.delete(key);
            }
        });
        // 列
        this.colEffects.forEach((eid, key) => {
            if (key.startsWith(boardId + '_')) {
                this.world.destroyEntity(eid);
                this.colEffects.delete(key);
            }
        });
        // 清理该棋盘缓存key，确保下次同位置还能触发
        this.lastKeyMap.delete(boardId);
    }

    update(): void {}

    /**
     * 校验预览形状放置是否合法：
     * 1. 完全落在棋盘范围内；
     * 2. 目标 slot 均未被占用。
     */
    private isPlacementValid(board: BoardComponent, info: IPutBlockInfo): boolean {
        if (!board) return false;
        const rows = board.rCount;
        const cols = board.cCount;
        for (const rc of info.shape.shape) {
            const r = info.y + rc.r;
            const c = info.x + rc.c;
            if (r < 0 || r >= rows || c < 0 || c >= cols) return false;
            const slotId = this.world.getTargets(board.entity, RelationName.PARENT_CHILD, `${r}_${c}`)[0];
            const slotComp = this.world.getComponent(slotId, SlotComponent);
            if (!slotComp) return false;
            if (this.world.handlerCenter.boardHandler.getSlotOccupy(slotComp)) return false;
        }
        return true;
    }
}
