const os = require('os');
const fs = require('fs');
const path = require('path');
const QRCode = require('qrcode');
const networkInterfaces = os.networkInterfaces();

// 获取命令行参数
const platform = process.argv[2] || 'web';
const env = process.argv[3] || 'prod';// test、prod、prod-test
const configFilePath = process.argv[4] || null;

console.log(`configFilePath`, configFilePath);

// 读取钉钉通知配置
let notifyConfig = null;
const notifyConfigPath = path.join(__dirname, 'notify-config.json');
if (fs.existsSync(notifyConfigPath)) {
    try {
        const notifyConfigData = fs.readFileSync(notifyConfigPath, 'utf8');
        notifyConfig = JSON.parse(notifyConfigData);
        console.log('读取钉钉通知配置成功');
    } catch (error) {
        console.error('解析钉钉通知配置文件失败:', error);
    }
}

// 读取配置文件（如果存在）
let buildConfig = null;
if (configFilePath && fs.existsSync(configFilePath)) {
    try {
        const configData = fs.readFileSync(configFilePath, 'utf8');
        buildConfig = JSON.parse(configData);
        console.log('读取构建配置成功');
    } catch (error) {
        console.error('解析配置文件失败:', error);
    }
}

const buildTime = buildConfig?.BUILD_TIME || new Date().toLocaleString();
const apkFullName = buildConfig?.APK_FULL_NAME || null;

function getLocalIP() {
    const interfaces = networkInterfaces;
    for (const iface of Object.values(interfaces)) {
        for (const config of iface) {
            if (config.family === 'IPv4' && !config.internal) {
                return config.address;
            }
        }
    }
    return '127.0.0.1';
}

// 计算目录大小的函数
function getDirectorySize(dirPath) {
    let totalSize = 0;

    if (!fs.existsSync(dirPath)) {
        return 0;
    }

    const files = fs.readdirSync(dirPath);

    for (const file of files) {
        const filePath = path.join(dirPath, file);
        const stats = fs.statSync(filePath);

        if (stats.isFile()) {
            totalSize += stats.size;
        } else if (stats.isDirectory()) {
            totalSize += getDirectorySize(filePath);
        }
    }

    return totalSize;
}

// 格式化大小显示
function formatSize(bytes) {
    if (bytes === 0) return '0 B';

    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));

    return (bytes / Math.pow(1024, i)).toFixed(2) + ' ' + units[i];
}

let formattedHotUpdateSize = '';
let formattedApkOrWebSize = '';
let packageUrl = '';
let combileApkUrl = '';
let qrCodePath = path.join(__dirname, `../../build/package-qrcode-${env}.png`);
let combileQrCodePath = path.join(__dirname, `../../build/package-combile-qrcode-${env}.png`);
const localIP = getLocalIP();

// 根据平台类型处理
if (platform.toLowerCase() === 'android') {
    // Android平台
    const assetsPath = path.join(__dirname, '../../build/jsb-link/assets');
    const assetSize = getDirectorySize(assetsPath);

    const srcPath = path.join(__dirname, '../../build/jsb-link/src');
    const srcSize = getDirectorySize(srcPath);

    const mainPath = path.join(__dirname, '../../build/jsb-link/main.js');
    const mainSize = fs.statSync(mainPath).size;

    const hotDiffUrl = path.join(`/Users/<USER>/blockblast-adv/hotUpdate/${platform}/${env}`, 'hotDiff.json');
    // 计算热更大小
    if (fs.existsSync(hotDiffUrl)) {
        // 输出每次打热更包与以前的大小比对
        formattedHotUpdateSize = fs.readFileSync(hotDiffUrl, { encoding: `utf-8` });
        const formatedSizeData = JSON.parse(formattedHotUpdateSize);
        let mdFormatedSize = `\n`;
        for (let i = 0; i < formatedSizeData.length; i++) {
            const { size, version } = formatedSizeData[i];
            const [oldVersion, newVersion] = version.split('_');
            const formatSize = (size / 1024 / 1024).toFixed(2);
            mdFormatedSize += `  - ${oldVersion} → ${newVersion}: ${formatSize} MB\n`;
        }

        formattedHotUpdateSize = mdFormatedSize;

        fs.rmSync(hotDiffUrl);
    }

    if (buildConfig?.Apk) {
        formattedApkOrWebSize = buildConfig.APK_SIZE;
    }

    packageUrl = `http://**************:8867/${platform}/${env}/${localIP}/${apkFullName}`;

    if (buildConfig?.Apk && buildConfig?.combile) {
        combileApkUrl = `http://**************:8867/${platform}/${env}/${localIP}/${buildConfig.COMBILE_APK_FULL_NAME}`;
    }

} else {
    // web-mobile等平台，计算目录大小
    const packagePath = path.join(__dirname, '../../build/web-mobile');
    const packageSize = getDirectorySize(packagePath);
    formattedApkOrWebSize = formatSize(packageSize);
    packageUrl = `http://**************:8867/${platform}/${env}/${localIP}/web-mobile/`;
}

if (env === 'test' && buildConfig?.Apk) {
    // 生成二维码
    QRCode.toFile(qrCodePath, packageUrl, {
        color: {
            dark: '#000000',
            light: '#ffffff'
        },
        width: 300,
        margin: 1
    }, (err) => {
        if (err) {
            console.error('生成二维码失败:', err);
        } else {
            console.log(`二维码已生成: ${qrCodePath}`);
        }
        // 无论二维码生成成功与否，都发送通知
        sendDingTalkMessage();
    });

    if (buildConfig?.combile) {
        // 生成融合包二维码
        QRCode.toFile(combileQrCodePath, combileApkUrl, {
            color: {
                dark: '#000000',
                light: '#ffffff'
            },
            width: 300,
            margin: 1
        }, (err) => {
            if (err) {
                console.error('生成融合包二维码失败:', err);
            } else {
                console.log(`融合包二维码已生成: ${combileQrCodePath}`);
            }
        });
    }
} else {
    // 只打热更包，不需要生成二维码
    sendDingTalkMessage();
}

// 发送钉钉消息
function sendDingTalkMessage() {
    const http = require('https');

    // 获取对应环境的 webhook URL
    const webhookUrl = notifyConfig?.webhook?.[env] ||
        `https://oapi.dingtalk.com/robot/send?access_token=152274f668b16f9eeeb08422e7b0ea0f21ab1be34ab82aff2321c78ac78027f4`;

    const url = webhookUrl;

    let flag;
    if (buildConfig?.only_bundle) {
        flag = 'Bundle';
    } else if (platform === 'android') {
        flag = buildConfig?.Apk ? `Apk` : `热更`;
    } else {
        flag = platform;
    }
    // 准备消息文本
    let messageText = `## 🚀 【${buildConfig?.only_bundle ? 'Bundle' : flag}】Block Blast 冒险新架构包 🚀\n\n`;
    switch (env) {
        case 'test':
            // Bundle模式的特殊处理
            if (buildConfig?.only_bundle) {
                messageText += `- 平台：${platform}\n`;
                messageText += `- 环境：${env}\n`;
                messageText += `- 打包时间：${buildTime}\n`;

                // 显示发布的bundle列表
                if (buildConfig?.bundles && Array.isArray(buildConfig.bundles)) {
                    messageText += `- 发布Bundle：\n\n`;
                    messageText += `| Bundle名称 | MD5版本 |\n`;
                    messageText += `|-----------|--------|\n`;
                    buildConfig.bundles.forEach(bundle => {
                        messageText += `| ${bundle.name} | ${bundle.md5} |\n`;
                    });
                    messageText += `\n`;
                }

                // 显示发布的libs列表
                if (buildConfig?.libs && Array.isArray(buildConfig.libs) && buildConfig.libs.length > 0) {
                    messageText += `- 发布Libs：\n\n`;
                    messageText += `| 文件名 | 大小 |\n`;
                    messageText += `|-------|-----|\n`;
                    buildConfig.libs.forEach(lib => {
                        messageText += `| ${lib.name} | ${lib.size} |\n`;
                    });
                    messageText += `\n`;
                }

                if (buildConfig?.GIT_COMMIT) {
                    messageText += `- commitId：${buildConfig.GIT_COMMIT}\n`;
                }

                if (buildConfig?.GIT_BRANCH) {
                    messageText += `- 分支：${buildConfig.GIT_BRANCH}\n`;
                }

                if (buildConfig?.GIT_COMMIT_MSG) {
                    messageText += `- 提交日志：${buildConfig.GIT_COMMIT_MSG}\n`;
                }

                break; // 跳出switch，不执行常规逻辑
            }

            // 常规模式的处理
            messageText += `- 平台：${platform}\n`;
            messageText += `- 环境：${env}\n`;
            if (buildConfig?.version) {
                messageText += `- 版本：${buildConfig.version}\n`;
            }

            messageText += `- 开启热更：${buildConfig?.MACRO_HOTUPDATE_OPEN ? true : false}\n`;

            // Apk 才显示大小与地址
            if (formattedHotUpdateSize && buildConfig?.MACRO_HOTUPDATE_OPEN) {
                messageText += `- 热更大小：${formattedHotUpdateSize}\n`;
            }

            // apk 是否开启 devtools
            if (buildConfig?.Apk) {
                messageText += `- devtools：${buildConfig?.MACRO_DEVTOOLS ? true : false}\n`;
            }

            messageText += `- 打包时间：${buildTime}\n`;

            if (flag === '热更' && buildConfig?.hotUpdateZip && buildConfig?.MACRO_HOTUPDATE_OPEN) {
                messageText += `- 增量zip：${buildConfig.hotUpdateZip}\n`;
            }

            if (platform !== 'web') {
                if (buildConfig?.ASSETS_DIFF_SIZE) {
                    messageText += `- 修改大小：${buildConfig.ASSETS_DIFF_SIZE} 字节\n`;
                }

                if (buildConfig?.buildDuration) {
                    messageText += `- 打包时长：${buildConfig.buildDuration || '未记录'}\n`;
                }
            }

            if (buildConfig?.GIT_COMMIT) {
                messageText += `- commitId：${buildConfig.GIT_COMMIT}\n`;
            }

            if (buildConfig?.GIT_BRANCH) {
                messageText += `- 分支：${buildConfig.GIT_BRANCH}\n`;
            }

            if (buildConfig?.GIT_COMMIT_MSG) {
                messageText += `- 提交日志：${buildConfig.GIT_COMMIT_MSG}\n`;
            }

            if (buildConfig?.PUBLISH_COMMIT_ID) {
                messageText += `- 发布版本CommitId：${buildConfig.PUBLISH_COMMIT_ID}\n`;
            }

            if (buildConfig?.BUILD_NUMBER) {
                messageText += `- 构建地址：http://**************:8888/view/%E5%86%92%E9%99%A9/job/block-blast-adv-test/${buildConfig.BUILD_NUMBER}/\n`;
            }

            if (buildConfig?.BUILD_USER) {
                messageText += `- 构建用户：${buildConfig.BUILD_USER}\n`;
            }

            // 添加代码行数（如果有）
            if (buildConfig?.tsLinesCount) {
                messageText += `\n- 代码行数：${parseInt(buildConfig.tsLinesCount).toLocaleString()} 行\n`;
            }

            if (platform === 'web') {
                messageText += `- 机器人：${!!buildConfig?.robot}\n`;

                if (buildConfig?.robot) {
                    if (buildConfig?.local_robot_url) {
                        messageText += `- 机器人本地地址：${buildConfig.local_robot_url}\n`;
                    }

                    if (buildConfig?.remote_robot_url) {
                        messageText += `- 机器人远程地址：${buildConfig.remote_robot_url}\n`;
                    }
                }
            }

            break;

        case 'prod':
        case 'prod-test':
            messageText += `- 平台：${platform}\n`;
            messageText += `- 环境：${env}\n`;

            if (formattedHotUpdateSize) {
                messageText += `- 热更大小：${formattedHotUpdateSize}\n`;
            }

            // 包大小
            if (formattedApkOrWebSize) {
                messageText += `- 包大小：${formattedApkOrWebSize}\n`;
            }

            messageText += `- 打包时间：${buildTime}\n`;

            if (buildConfig?.buildDuration) {
                messageText += `- 打包时长：${buildConfig.buildDuration || '未记录'}\n`;
            }

            if (buildConfig?.GIT_COMMIT) {
                messageText += `- commitId：${buildConfig.GIT_COMMIT}\n`;
            }

            if (buildConfig?.GIT_BRANCH) {
                messageText += `- 分支：${buildConfig.GIT_BRANCH}\n`;
            }

            if (buildConfig?.GIT_COMMIT_MSG) {
                messageText += `- 提交日志：${buildConfig.GIT_COMMIT_MSG}\n`;
            }

            if (buildConfig?.PUBLISH_COMMIT_ID) {
                messageText += `- 发布版本CommitId：${buildConfig.PUBLISH_COMMIT_ID}\n`;
            }

            if (buildConfig?.ASSETS_DIFF_SIZE) {
                messageText += `- 修改大小：${buildConfig.ASSETS_DIFF_SIZE} 字节\n`;
            }

            if (buildConfig?.BUILD_NUMBER) {
                messageText += `- 构建地址：http://**************:8888/view/%E5%86%92%E9%99%A9/job/block-blast-adv/${buildConfig.BUILD_NUMBER}/\n`;
            }

            if (buildConfig?.BUILD_USER) {
                messageText += `- 构建用户：${buildConfig.BUILD_USER}\n`;
            }

            // 添加代码行数（如果有）
            if (buildConfig && buildConfig.tsLinesCount) {
                messageText += `- 代码行数：${parseInt(buildConfig.tsLinesCount).toLocaleString()} 行\n`;
            }
            break;
    }

    // 添加APK文件名（如果有）
    if (apkFullName && buildConfig?.Apk) {
        messageText += `- APK文件名：${apkFullName}\n`;
    }

    // 添加构建配置信息（如果有）
    if (buildConfig) {
        switch (env) {
            case 'test':
                if (buildConfig.Apk) {
                    messageText += `- 集成原生：${buildConfig.apkPublishType}\n`;
                }

                messageText += `- 调试模式：${buildConfig.debug}\n`;

                if (buildConfig.hotUpdateServerUrl && buildConfig.MACRO_HOTUPDATE_OPEN) {
                    messageText += `- 热更服务器：${buildConfig.hotUpdateServerUrl}\n`;
                }

                if (buildConfig.traitsSplit) {
                    messageText += `- 特性裁剪：${buildConfig.traitsSplit}\n`;
                }

                if (buildConfig.autoMode) {
                    messageText += `- 自动化：${buildConfig.autoMode}\n`;
                }

                if (buildConfig.showFPS) {
                    messageText += `- 显示FPS：${buildConfig.showFPS}\n`;
                }

                break;

            case 'prod':
            case 'prod-test':
                if (buildConfig.traitsSplit) {
                    messageText += `- 特性裁剪：${buildConfig.traitsSplit}\n`;
                }
                break;
        }
    }

    // 包大小
    if (formattedApkOrWebSize) {
        messageText += `- 新架构包大小：${formattedApkOrWebSize}\n`;
    }

    // Apk 与 web 平台才显示打包地址
    if (buildConfig?.Apk) {
        messageText += `- 新架构Apk：${packageUrl}\n`;
    }

    if (platform === 'web') {
        messageText += `- 打包地址：${packageUrl}\n`;
    }

    // 添加新架构包二维码
    if (fs.existsSync(qrCodePath) && env === 'test' && buildConfig?.Apk) {
        messageText += `\n- 新架构包二维码\n![二维码](data:image/png;base64,${fs.readFileSync(qrCodePath).toString('base64')})`;
    }

    // 融合包信息
    if (buildConfig?.Apk) {
        if (buildConfig?.combile) {
            messageText += `- 融合包大小：${buildConfig.COMBILE_APK_SIZE}\n`;
            messageText += `- 融合包Apk：${combileApkUrl}\n`;
        }
    }

    // 添加融合包二维码
    if (fs.existsSync(combileQrCodePath) && env === 'test' && buildConfig?.Apk && buildConfig?.combile) {
        messageText += `\n- 融合包二维码\n![二维码](data:image/png;base64,${fs.readFileSync(combileQrCodePath).toString('base64')})`;
    }

    // 准备 @个人的手机号列表
    const atMobiles = [];
    const atUserIds = [];
    let isAtAll = false;

    if (notifyConfig) {
        // 如果配置了 notifyAll 为 true，则 @所有人
        if (notifyConfig.notifyAll) {
            isAtAll = true;
        } else {
            // 否则只 @配置的个人
            const persons = notifyConfig.notifyPersons?.[env] || [];
            persons.forEach(person => {
                if (person.mobile) {
                    atMobiles.push(person.mobile);
                }

                if (person.name) {
                    atUserIds.push(person.name);
                }
            });
        }
    }

    // 使用markdown格式支持图片显示
    const config = {
        "msgtype": "markdown",
        "markdown": {
            "title": `Block Blast ${platform} 新架构包`,
            "text": messageText
        },
        "at": {
            "atMobiles": atMobiles,
            "atUserIds": atUserIds,
            "isAtAll": isAtAll
        }
    };

    const options = {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    };

    const req = http.request(url, options, (res) => {
        if (res.statusCode !== 200) {
            console.error(`Error: ${res.statusCode}`);
            return;
        }

        console.log('钉钉通知发送成功');
        if (atMobiles.length > 0) {
            console.log('已通知个人:', atMobiles.join(', '));
        }
        if (isAtAll) {
            console.log('已通知所有人');
        }
        process.exit(0);
    });

    req.write(JSON.stringify(config));
    req.end();
}