import { Component } from "../cores/Component";
import { GameEventMap } from "../GameEvent";
import { ICreateDelayRuleData } from "../GameEventData";

/** 延迟规则组件 
 * 用于执行延迟规则
*/
export class DelayRuleComponent extends Component {
    /**规则ID */
    public ruleId: string;
    /**规则延时 */
    public delay: number;
    /**事件 */
    public event: keyof GameEventMap;
    /**事件数据 */
    public eventData: GameEventMap[keyof GameEventMap];
    constructor(data: ICreateDelayRuleData = { ruleId: "", delay: 0, event: null, eventData: null }) {
        super();
        this.ruleId = data.ruleId;
        this.delay = data.delay;
        this.event = data.event;
        this.eventData = data.eventData;
    }
}
