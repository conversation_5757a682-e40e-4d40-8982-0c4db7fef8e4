import { FlowType } from '../../../../../base/rule/flow/FlowType';
import { IOverrideLevelConfig } from '../../cores/OverrideConfigManager';
import { ECSEvent } from '../../GameEvent';
import { IInitBoardRuleConfig } from '../../rules/InitBoardRule';
import { IInitTargetRuleConfig } from '../../rules/InitTargetRule';

export const LevelConfig6: IOverrideLevelConfig = {
    /** 底板配置 */
    baseLevelId: 'BaseConfig10000',
    /** 关卡ID */
    levelId: 'LevelConfig6',
    /** 重写配置表参数 */
    overrideTableArgs: {
        RuleConfig: {
            rule_init_target:{
                target: [[1, 200]],
                x: 200,
            } as IInitTargetRuleConfig,
            rule_init_board: {
                boardIdList: ["mirror_sub", "mirror_main"],
            } as IInitBoardRuleConfig,
        },
    },
    overrideLevelConfig: {
        sys: [
            'system_interaction',
            'system_combo',
            'system_eliminate',
            'system_game_result',

            'system_preview',
            'system_effect_preview',
            'system_mirror',
            'system_board_link',
            'system_board_switch',
            'system_target',
        ],
        initWorldRuleFlowConfig: {
            type: FlowType.List,
            children: ['rule_init_board_scene', 'rule_init_board', 'rule_init_target', 'rule_produce_block_without_death'],
        },
        initAllRuleFlowConfig: {
            [ECSEvent.GameEvent.PRODUCE_BLOCK]: {
                type: FlowType.List,
                children: ['rule_produce_block_without_death'],
            },
            [ECSEvent.GameEvent.PRODUCE_BLOCK_END]: {
                type: FlowType.List,
                children: ['rule_produce_block_end'],
            },
            [ECSEvent.GameEvent.PUT_BLOCK_BACK]: {
                type: FlowType.List,
                children: ['rule_check_dual_board_result'],
            },
        },
    },
    /** 关闭系统 */
    closeSystems: [],
    /** 关闭规则 */
    closeRules: [],
    /** 插入系统 */
    insertSystems: [],
    /** 插入规则 */
    insertRules: [],
    /** 替换系统 */
    replaceSystems: [],
    /** 替换规则 */
    replaceRules: [],
};
