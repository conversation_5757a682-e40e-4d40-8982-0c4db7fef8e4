
APK_BASE_URL=/Users/<USER>/Downloads/com.block.juggle_7320_5.8.0.1_4cce4ff_2025062720-release.apk


# 主包游戏资源内容
stable_res=${stable_res:-""}
# 新架构包资源内容
refactored_res=${refactored_res:-""}

# Apk 名称
ApkName=${ApkName:-"blockblast"}
JENKINS_FILES_PATH="./scripts/jenkins"
TEMP_DIR=${JENKINS_FILES_PATH}/temp_sign
TEMP_DIR_TEMP=${TEMP_DIR}/temp

upload_stable_res_url=${TEMP_DIR}/stable_res
upload_refactored_res_url=${TEMP_DIR}/stable_res

BUILD_START_TIME=$(date +%s)
UNSIGNED_APK="${TEMP_DIR}/block-blast-unsigned.apk"
UNSIGNED_ALIGNED_APK="${TEMP_DIR}/block-blast-unsigned-aligned.apk"
APK_FULL_NAME="${ApkName}-$(date '+%Y%m%d%H%M%S').apk"

log() {
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

rm -rf ${TEMP_DIR}

mkdir -p ${TEMP_DIR}
mkdir -p ${TEMP_DIR_TEMP}

log "反编译APK..."
/opt/homebrew/bin/apktool d -f ${APK_BASE_URL} -o ${TEMP_DIR_TEMP}

log "构建未签名APK..."
/opt/homebrew/bin/apktool b ${TEMP_DIR_TEMP} -o ${UNSIGNED_APK}


# 对齐，如果不使用这个，会报：Failure [-124: Failed parse during installPackageLI: Targeting R+ (version 30 and above) requires the resources.arsc of installed APKs to be stored uncompressed and aligned on a 4-byte boundary]
# 从 Android 11 开始，Google 对 APK 的打包格式有了更严格的要求，尤其是 resources.arsc 文件（资源索引文件）：
# 必须 uncompressed（未压缩）
# 必须 4 字节对齐
log "对齐未签名的 APK..."
~/Library/Android/sdk/build-tools/30.0.3/zipalign -v 4 ${UNSIGNED_APK} ${UNSIGNED_ALIGNED_APK}


# 签名APK
log "签名APK..."
KEYSTORE_FILE="${JENKINS_FILES_PATH}/sign/block-blast.keystore"
KEYSTORE_PASS="Md5ctAth0h"
KEYSTORE_ALIAS="9OmruqqZww"
# /opt/homebrew/opt/openjdk/bin/jarsigner -verbose \
#   -keystore ${KEYSTORE_FILE} \
#   -storepass "${KEYSTORE_PASS}" \
#   -signedjar ${APK_FULL_NAME} \
#   ${UNSIGNED_ALIGNED_APK} \
#   ${KEYSTORE_ALIAS}
# 必须用 apksigner 签名，jarsigner 签名会报错：Failure [-124: Failed parse during installPackageLI: Targeting R+ (version 30 and above) requires the resources.arsc of installed APKs to be stored uncompressed and aligned on a 4-byte boundary]
~/Library/Android/sdk/build-tools/30.0.3/apksigner sign \
--ks ${KEYSTORE_FILE} \
--ks-key-alias ${KEYSTORE_ALIAS} \
--ks-pass pass:${KEYSTORE_PASS} \
--out ${APK_FULL_NAME} \
${UNSIGNED_ALIGNED_APK}

rm -f ${UNSIGNED_APK}
rm -f ${UNSIGNED_ALIGNED_APK}
rm -rf ${TEMP_DIR}
rm -rf ${APK_FULL_NAME}.idsig

# 计算打包时长（秒）
BUILD_END_TIME=$(date +%s)
BUILD_DURATION=$((BUILD_END_TIME - BUILD_START_TIME))
BUILD_DURATION_FORMATTED=$(printf "%02d:%02d:%02d" $((BUILD_DURATION/3600)) $((BUILD_DURATION%3600/60)) $((BUILD_DURATION%60)))

log "构建完成，耗时: ${BUILD_DURATION_FORMATTED},地址：${APK_FULL_NAME}"