import { LogicWorld } from "../../cores/World";
import { ECSEvent } from "../../GameEvent";

/**
 * 行为基类
 * 所有行为类都继承自该基类
 * 提供世界和配置的访问接口
 */
export interface IBehaviorConfig {
    /**行为描述 */
    behaviorDesc: string;
    /**行为类型 */
    behaviorType: string;
}
export abstract class BehaviorBase<T extends IBehaviorConfig = IBehaviorConfig> {
    config: DeepReadonly<T>;
    world: LogicWorld;
}
/**
 * 实体行为基类
 * 所有实体行为类都继承自该基类
 */
export abstract class EntityBehaviorBase<T extends IBehaviorConfig = IBehaviorConfig> extends BehaviorBase<T> {
    /**绑定实体事件 */
    abstract bindEntityEvent(): ECSEvent.EntityEvent[];
    /**接收事件 */
    abstract receiveEntityEvent(entityId: number,event: ECSEvent.EntityEvent,eventData: any): void;
}
export interface IBehaviorData {
    [key: string]: any;
}

/**
 * 基础行动数据接口
 * 为Skill和Buff提供统一的行为数据结构
 */
export interface IBaseActionData<TBehaviorData extends IBehaviorData = IBehaviorData> {
    behaviorData: TBehaviorData;
}
