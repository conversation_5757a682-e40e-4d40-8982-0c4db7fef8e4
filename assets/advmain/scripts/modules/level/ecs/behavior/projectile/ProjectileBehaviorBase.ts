import { BehaviorBase, IBehaviorConfig } from '../../combat/core/BehaviorBase';
import { ProjectileComponent } from '../../components/combat/ProjectileComponent';
import { IMoveParams } from '../projectilePath/ProjectilePathBehaviorBase';
import { TempleteType } from '../../registry/templete/TempleteRegistry';
import NodeComponent from '../../components/NodeComponent';
import { EcsViewId } from '../../renderWorld/ECSViewBase';
import { IPoint } from '../../define/EcsDefine';

export type ProjectileBehaviorId = string;

/**
 * 投射物基础配置接口
 * 所有投射物类型的最小公共配置
 */
export interface IProjectileBehaviorConfig extends IBehaviorConfig {
    /**投射物行为ID */
    projectileBehaviorId: ProjectileBehaviorId;
    /**不要销毁实体 */
    dontDestroyEntity: boolean;
    /**移动参数 */
    moveParams: IMoveParams;
    /**命中特效 */
    hitEcsViewId: EcsViewId;
}

/**
 * 通用速度管理数据
 */
export interface ISpeedManagementData {
    /**当前速度 */
    currentSpeed: number;
    /**目标位置缓存 */
    targetPosition: IPoint;
    /**是否需要重新计算速度 */
    needsSpeedRecalculation: boolean;
}

/**
 * 投射物行为基类
 * 定义投射物的生命周期回调接口
 */
export abstract class ProjectileBehaviorBase<T extends IProjectileBehaviorConfig = IProjectileBehaviorConfig> extends BehaviorBase<T> {
    /**
     * 投射物创建回调
     * 当投射物被创建时调用，用于初始化行为数据
     * @param entityId 投射物实体ID
     * @param projectileComponent 投射物组件
     * @param context 动作上下文（包含目标、位置等信息）
     */
    abstract onCreate(entityId: number, projectileComponent: ProjectileComponent): void;

    /**
     * 投射物销毁回调
     * 当投射物被销毁时调用，用于清理资源
     * @param entityId 投射物实体ID
     * @param projectileComponent 投射物组件
     */
    abstract onDestroy(entityId: number, projectileComponent: ProjectileComponent): void;
    /**
     * 投射物移动回调
     * 当投射物移动时调用，用于更新投射物位置
     * @param entityId 投射物实体ID
     * @param projectileComponent 投射物组件
     * @param worldTime 世界时间
     * @param deltaTime 时间增量
     * @returns 是否应该销毁投射物（true表示销毁，false表示继续存在）
     */
    abstract onMove(entityId: number, projectileComponent: ProjectileComponent, worldTime: number, deltaTime: number): boolean;
    /**
     * 投射物命中目标时调用-如果战斗意图是命中目标，则调用
     *
     * @param entityId 投射物实体ID
     * @param projectileComponent 投射物组件
     * @param targetEntityId 命中的目标实体ID
     * @returns 是否应该销毁投射物（true表示销毁，false表示继续存在）
     */
    abstract onHit(entityId: number, projectileComponent: ProjectileComponent, targetEntityId: number): void;
    /**
     * 投射物开始命中目标时调用
     * @param entityId 投射物实体ID
     * @param projectileComponent 投射物组件
     * @param targetEntityId 命中的目标实体ID
     */
    onStartHit(entityId: number, projectileComponent: ProjectileComponent, targetEntityId: number): void {
        this.onHit(entityId, projectileComponent, targetEntityId);
        this.hitEffect(entityId, projectileComponent, targetEntityId);
    }
    /**命中特效 */
    hitEffect(entityId: number, projectileComponent: ProjectileComponent, targetEntityId: number): void {
        const hitEcsViewId = this.config.hitEcsViewId as EcsViewId;
        if (!hitEcsViewId) return;
        const nodeComp = this.world.getComponent(entityId, NodeComponent);
        this.world.templeteCenter.createTempleteEntity(
            TempleteType.Effect,
            {
                ecsViewId: hitEcsViewId,
                parentEntity: this.world.ECSLayerType.EffectLayerEntity,
            },
            {
                nodeParam: {
                    x: nodeComp.x,
                    y: nodeComp.y,
                },
                renderParam: projectileComponent.hitRenderParam,
            },
        );
    }
    dontDestroyEntity(entityId: number, projectileComponent: ProjectileComponent): boolean {
        return !!this.config.dontDestroyEntity;
    }

    /**
     * 计算移动速度
     * 子类可以重写此方法来实现自定义的速度计算逻辑
     * @param moveParams 移动参数
     * @param startPos 起始位置
     * @param targetPos 目标位置
     * @returns 计算出的速度（单位/秒）
     */
    protected calculateSpeed(moveParams: IMoveParams, startPos: IPoint, targetPos: IPoint): number {
        // 如果指定了速度，直接使用
        if (moveParams.speed !== undefined && moveParams.speed > 0) {
            return moveParams.speed;
        }

        // 如果指定了移动时间，根据距离计算速度
        if (moveParams.moveTime !== undefined && moveParams.moveTime > 0) {
            const distance = Math.sqrt(Math.pow(targetPos.x - startPos.x, 2) + Math.pow(targetPos.y - startPos.y, 2));
            return distance / moveParams.moveTime;
        }

        // 默认速度
        return 200;
    }

    /**
     * 获取或创建速度管理数据
     * @param projectileComponent 投射物组件
     * @returns 速度管理数据
     */
    protected getSpeedManagementData(projectileComponent: ProjectileComponent): ISpeedManagementData {
        if (!projectileComponent.pathPrecomputedData.speedManagement) {
            projectileComponent.pathPrecomputedData.speedManagement = {
                currentSpeed: 0,
                targetPosition: { x: 0, y: 0 },
                needsSpeedRecalculation: true,
            };
        }
        return projectileComponent.pathPrecomputedData.speedManagement;
    }

    /**
     * 智能速度计算
     * 只在需要时重新计算速度，避免重复计算
     * @param moveParams 移动参数
     * @param startPos 起始位置
     * @param targetPos 目标位置
     * @param projectileComponent 投射物组件
     * @returns 计算出的速度
     */
    protected getSmartSpeed(moveParams: IMoveParams, startPos: IPoint, targetPos: IPoint, projectileComponent: ProjectileComponent): number {
        const speedData = this.getSpeedManagementData(projectileComponent);

        // 检查是否需要重新计算速度
        if (speedData.needsSpeedRecalculation || this.isTargetPositionChanged(speedData.targetPosition, targetPos)) {
            speedData.currentSpeed = this.calculateSpeed(moveParams, startPos, targetPos);
            speedData.targetPosition.x = targetPos.x;
            speedData.targetPosition.y = targetPos.y;
            speedData.needsSpeedRecalculation = false;
        }

        return speedData.currentSpeed;
    }

    /**
     * 检查目标位置是否发生变化
     * @param cachedPos 缓存的位置
     * @param newPos 新的位置
     * @returns 是否发生变化
     */
    protected isTargetPositionChanged(cachedPos: IPoint, newPos: IPoint): boolean {
        const threshold = 0.1; // 位置变化阈值，避免浮点数精度问题
        return Math.abs(cachedPos.x - newPos.x) > threshold || Math.abs(cachedPos.y - newPos.y) > threshold;
    }

    /**
     * 重置速度管理数据
     * 在段切换或需要重新计算时调用
     * @param projectileComponent 投射物组件
     */
    protected resetSpeedManagement(projectileComponent: ProjectileComponent): void {
        const speedData = this.getSpeedManagementData(projectileComponent);
        speedData.currentSpeed = 0;
        speedData.targetPosition.x = 0;
        speedData.targetPosition.y = 0;
        speedData.needsSpeedRecalculation = true;
    }

    /**
     * 强制重新计算速度
     * 在需要强制更新速度时调用
     * @param projectileComponent 投射物组件
     */
    protected forceSpeedRecalculation(projectileComponent: ProjectileComponent): void {
        const speedData = this.getSpeedManagementData(projectileComponent);
        speedData.needsSpeedRecalculation = true;
    }
}
