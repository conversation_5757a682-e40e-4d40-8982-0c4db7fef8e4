import { BoardScene } from '../components/board/BoardScene';
import ReviveComponent from '../components/ReviveComponent';
import { TempleteType } from '../registry/templete/TempleteRegistry';
import { ECSExecuteContext, ECSRuleBase, IECSRuleConfig } from './core/ECSRuleBase';

export interface IGameFailedRuleConfig extends IECSRuleConfig {
    /**消除渲染命令配置id */
    viewId: string;
}

/**
 * 复活规则
 */
export class GameFailedRule extends ECSRuleBase<IGameFailedRuleConfig> {
    canExecute(): boolean {
        return true;
    }

    execute(): void {
        console.log('yjf__游戏失败');
        // const world = ctx.world;
        // const sceneEntity = world.handlerCenter.boardHandler.getSceneEntity();
        // const reviveComp = world.getComponent(sceneEntity, ReviveComponent);
        // reviveComp.nCount--;
        // const effectEntity = ctx.world.templeteCenter.createTempleteEntity(TempleteType.Render, {
        //     parentEntity: sceneEntity,
        //     ecsViewId: this.config.viewId,
        //     nodeParam: {}
        // },
        // {
        //     renderParam: {isBreakLink: true },
        // });
        // ctx.world.destroyEntity(effectEntity);
    }
}
