import { DestroyTag } from '../components/tag/DestroyTag';
import { System } from '../cores/System';
import { RelationName } from '../cores/center/EntityCenter/WorldRelation';

export class DestroySystem extends System {
    init(): void {
        this.world.onComponentAdd(DestroyTag, this.__onDestroyTagAdd, this);
    }
    dispose(): void {
        this.world.offComponentAdd(DestroyTag, this.__onDestroyTagAdd, this);
    }
    update(dt: number): void {
        let entities = this.world.query([DestroyTag]);
        for (let i: number = 0; i < entities.length; i++) {
            let destroyTag = this.world.getComponent<DestroyTag>(entities[i], DestroyTag)!;
            destroyTag.destroyInterval += dt;
            if (destroyTag.destroyInterval >= destroyTag.delayTime) {
                this.world.removeEntity(entities[i]);
            }
        }
    }
    __onDestroyTagAdd(entityId: number, destroyTag: DestroyTag) {
        const childrenEntity = this.world.getTargets(entityId, RelationName.PARENT_CHILD);
        for (let i = 0; i < childrenEntity.length; i++) {
            this.world.addComponent(childrenEntity[i], DestroyTag, destroyTag.delayTime);
        }
    }
}
