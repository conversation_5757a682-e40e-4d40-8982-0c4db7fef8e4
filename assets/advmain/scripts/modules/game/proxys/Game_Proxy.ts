
import { E_Game_EventHide } from "../events/E_Game_EventHide";
import { E_Game_EventShow } from "../events/E_Game_EventShow";

export class Game_Proxy extends falcon.Proxy {
    protected onInit(): void {
        cc.game.on(cc.game.EVENT_SHOW, () => {
            console.log(`进入前台.`);
            falcon.EventManager.dispatchModuleEvent(new E_Game_EventShow());
        });

        cc.game.on(cc.game.EVENT_HIDE, () => {
            console.log(`进入后台.`);
            falcon.EventManager.dispatchModuleEvent(new E_Game_EventHide());
        });
    }
}