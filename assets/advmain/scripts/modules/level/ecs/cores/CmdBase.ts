import { IEventCmdData } from '../cmd/EventCmd';
import { LogicWorld } from './World';
export interface ICmdConfig {
    /**命令id */
    cmdId: string;
    /**命令名称 */
    cmdName: string;
}
export type ICmdData = IEventCmdData;
/**
 * 命令基类
 */
export class CmdBase<TConfig extends ICmdConfig = ICmdConfig, TData = ICmdData> {
    world: LogicWorld;
    config: TConfig;
    data: TData;
    execute() {
        this.do();
    }
    undo() {}
    do() {}
}
