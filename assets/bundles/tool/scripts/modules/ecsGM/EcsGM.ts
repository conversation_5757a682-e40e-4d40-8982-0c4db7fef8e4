import Util from '../../../../../advmain/scripts/base/Util';
import { E_GameOver_GameEndPre } from '../../../../../advmain/scripts/modules/gameOver/events/E_GameOver_GameEndPre';
import { World } from '../../../../../advmain/scripts/modules/level/ecs/cores/World';
import { ECSEvent } from '../../../../../advmain/scripts/modules/level/ecs/GameEvent';

(function () {
    if (!CC_DEBUG) return;
    const GMFuncs = {
        close: '关闭',
        fail: '失败',
        win: '胜利',
        jump: '跳关',
        save: '保存复盘',
    } as const;
    let dragover: (this: HTMLElement, ev: DragEvent) => void;
    let drop: (this: HTMLElement, ev: DragEvent) => void;

    /**ECS游戏内的GM，只在调试环境生效，界面完全由代码生成，外部插入点通过监听通用事件来完成 */
    window['EcsGM'] = class EcsGM {
        static _ins: EcsGM;
        static get ins() {
            if (!this._ins) this._ins = new EcsGM();
            return this._ins;
        }

        private ns: { [k: string]: cc.Node } = {};
        private spf: cc.SpriteFrame;
        private world: World;

        public init(world: World) {
            this.world = world;
            this.addEvents();
            if (cc.director.getScene().getChildByName('EcsGMBtn')) return;
            this.loadUI();
        }

        private addEvents() {
            this.world.eventBus.on(ECSEvent.GameEvent.PUT_BLOCK, this.savePuts, this);
        }

        /**保存复盘 */
        save() {
            this.downLoadJson('复盘数据_冒险', {
                type: 'puts',
                puts: this.putDatas,
            });
            this.close();
        }

        /**存储最近4次放块数据 */
        savePuts() {
            this.putDatas[3] = this.putDatas[2];
            this.putDatas[2] = this.putDatas[1];
            this.putDatas[1] = this.putDatas[0];
            this.putDatas[0] = JSON.stringify(cc.sys.localStorage);
        }
        private putDatas: string[] = [];

        win() {
            this.world.eventBus.emit(ECSEvent.GameEvent.GAME_WIN);
            // falcon.EventManager.dispatchModuleEvent(new E_GameOver_GameEndPre(true));
            this.close();
        }

        fail() {
            this.world.eventBus.emit(ECSEvent.GameEvent.GAME_FAILED);
            // falcon.EventManager.dispatchModuleEvent(new E_GameOver_GameEndPre(false));
            this.close();
        }

        jump() {
            const arg = this.ns.EcsGMInput.getComponent(cc.EditBox).string;
            falcon.storage.setItem('chapterNum', (parseInt(arg) || 1) - 2);
            falcon.EventManager.dispatchModuleEvent(new E_GameOver_GameEndPre(true));
            this.close();
        }

        /**弹提示 */
        toast(txt: string) {
            const toast = this.addLbNode('EcsGMToast', txt);
            toast.addComponent(cc.LabelOutline).width = 2;
            toast.setParent(cc.Canvas.instance.node);
            setTimeout(() => toast.destroy(), 3000);
        }

        private loadUI() {
            // 生成 2x2 纹理
            const buffer = new Uint8Array(new Array(16).fill(255));
            const texture = new cc.Texture2D();
            texture.initWithData(buffer, cc.Texture2D.PixelFormat.RGBA8888, 2, 2);
            this.spf = new cc.SpriteFrame();
            this.spf.setTexture(texture);

            //GM按钮
            const gmBtn = this.addBtn('EcsGMBtn', 'GM');
            gmBtn.zIndex = cc.macro.MAX_ZINDEX - 10;
            gmBtn.setParent(cc.director.getScene());
            const btmWgt = gmBtn.addComponent(cc.Widget);
            btmWgt.isAlignTop = true;
            btmWgt.top = 280;
            btmWgt.isAlignLeft = true;
            btmWgt.left = 100;
            Util.bindTap(gmBtn, this.show.bind(this));

            //GM主界面
            const ui = this.createUI('EcsGM');
            ui.active = false;
            ui.zIndex = cc.macro.MAX_ZINDEX - 9;
            ui.setParent(cc.director.getScene());

            //GM公共输入栏
            const input = new cc.Node('EcsGMInput');
            input.width = 900;
            input.height = 60;
            const inputWgt = input.addComponent(cc.Widget);
            inputWgt.isAlignTop = inputWgt.isAlignLeft = true;
            inputWgt.top = inputWgt.left = 30;
            const inputBg = this.addSpNode('EcsGMInputBg', cc.Color.GRAY);
            inputBg.setParent(input);
            this.addFitWidget(inputBg);
            const inputLbNode = this.addLbNode('EcsGMInputLb', '');
            inputLbNode.setParent(input);
            const hint = this.addLbNode('EcsGMInputHint', '');
            hint.setParent(input);
            const eb = input.addComponent(cc.EditBox);
            eb.background = inputBg.getComponent(cc.Sprite);
            eb.textLabel = inputLbNode.getComponent(cc.Label);
            eb.placeholderLabel = hint.getComponent(cc.Label);
            eb.placeholder = 'GM功能的参数都在此输入';
            input.setParent(ui);

            //GM功能平铺
            const funcsNode = new cc.Node('EcsGMFuncs');
            this.addFitWidget(funcsNode);
            funcsNode.getComponent(cc.Widget).top = inputWgt.top + input.height + 30;
            funcsNode.setParent(ui);
            const funcsLo = funcsNode.addComponent(cc.Layout);
            funcsLo.type = cc.Layout.Type.GRID;
            funcsLo.spacingX = funcsLo.spacingY = 20;
            funcsLo.paddingLeft = funcsLo.paddingRight = inputWgt.left;
            for (const k in GMFuncs) {
                const btn = this.addBtn(`EcsGM${k}`, GMFuncs[k]);
                btn.setParent(funcsNode);
                Util.bindTap(btn, this[k].bind(this));
            }

            //上传文件提醒
            const uploadHint = this.addLbNode('uploadHint', '数据文件直接拖进来', cc.Color.WHITE);
            const uploadHintWgt = uploadHint.addComponent(cc.Widget);
            uploadHintWgt.isAlignBottom = true;
            uploadHintWgt.bottom = 20;
            uploadHint.setParent(ui);

            //次级界面
            const pop = this.createUI('EcsGMPop');
            pop.active = false;
            pop.setParent(ui);
            Util.bindTap(pop, () => (pop.active = false));
            const popMain = this.addSpNode('EcsGMPopMain', cc.Color.GRAY);
            popMain.width = 800;
            popMain.addComponent(cc.BlockInputEvents);
            const popMainWgt = popMain.addComponent(cc.Widget);
            popMainWgt.isAlignHorizontalCenter = true;
            popMainWgt.isAlignVerticalCenter = true;
            const popLo = popMain.addComponent(cc.Layout);
            popLo.type = cc.Layout.Type.GRID;
            popLo.resizeMode = cc.Layout.ResizeMode.CONTAINER;
            popLo.paddingTop = popLo.paddingBottom = popLo.paddingLeft = popLo.paddingRight = popLo.spacingX = popLo.spacingY = funcsLo.spacingX;
            const popTitle = this.addLbNode('popTitle', '次级界面提示');
            popTitle.getComponent(cc.Label).overflow = cc.Label.Overflow.SHRINK;
            popTitle.width = popMain.width - popLo.paddingLeft * 2;
            popTitle.height = 50;
            popTitle.setParent(popMain);
            popMain.setParent(pop);

            this.ns = Util.getNodes(ui);
        }

        /**显示次级界面 */
        private pop(title: string, btnTxtList: string[], btnClick: (i: number, txt: string) => void) {
            this.ns.popTitle.getComponent(cc.Label).string = title;
            for (let i = 0, len = Math.max(btnTxtList.length, this.ns.EcsGMPopMain.children.length); i < len; i++) {
                const key = `EcsGMPopBtn${i}`;
                let btn = this.ns[key];
                const show = i < btnTxtList.length;
                if (!btn) {
                    if (show) {
                        btn = this.addBtn(key, '');
                        btn.setParent(this.ns.EcsGMPopMain);
                    } else continue;
                }
                btn.active = show;
                if (show) {
                    this.ns[key] = btn;
                    btn.getComponentInChildren(cc.Label).string = btnTxtList[i];
                    Util.bindTap(btn, () => btnClick(i, btnTxtList[i]));
                }
            }
            this.ns.EcsGMPop.active = true;
        }

        private addDragFileEvent() {
            if (!cc.sys.isBrowser) return;
            dragover = this.dragover.bind(this);
            drop = this.drop.bind(this);
            const dropzone = document.getElementById('Cocos2dGameContainer');
            dropzone.addEventListener('dragover', dragover, false);
            dropzone.addEventListener('drop', drop, false);
        }

        private removeDragFileEvent() {
            if (!cc.sys.isBrowser) return;
            const dropzone = document.getElementById('Cocos2dGameContainer');
            dropzone.removeEventListener('dragover', dragover, false);
            dropzone.removeEventListener('drop', drop, false);
        }

        private dragover(event: DragEvent) {
            event.stopPropagation();
            event.preventDefault();
            event.dataTransfer.dropEffect = 'copy';
        }

        private drop(event: DragEvent) {
            event.stopPropagation();
            event.preventDefault();

            const file = event.dataTransfer.files[0];
            if (!file) return;
            const reader = new FileReader();
            reader.onload = (e) => {
                const decoder = new TextDecoder('utf-8');
                const text = decoder.decode(e.target.result as ArrayBuffer);
                const data = JSON.parse(text);
                if (data.type === 'puts') {
                    this.pop('选择复盘点', ['前一步', '前两步', '前三步', '前四步'].slice(0, data.puts.filter((v) => !!v).length), (i) => {
                        cc.sys.localStorage.clear();
                        const storage = JSON.parse(data.puts[i]);
                        for (const k in storage) {
                            cc.sys.localStorage.setItem(k, storage[k]);
                        }
                        location.reload();
                    });
                }
            };
            reader.readAsArrayBuffer(file);
        }

        private createUI(name: string) {
            //UI界面
            const ui = new cc.Node(name);
            ui.addComponent(cc.BlockInputEvents);
            this.addFitWidget(ui);

            //背景遮罩
            const bg = this.addSpNode(name + 'Bg', cc.Color.BLACK);
            bg.opacity = 180;
            this.addFitWidget(bg);
            bg.setParent(ui);

            return ui;
        }

        private addBtn(btnName: string, text: string) {
            const btn = this.addSpNode(btnName);
            btn.height = 60;
            const lo = btn.addComponent(cc.Layout);
            lo.type = cc.Layout.Type.HORIZONTAL;
            lo.resizeMode = cc.Layout.ResizeMode.CONTAINER;
            lo.paddingLeft = lo.paddingRight = 16;
            btn.addComponent(cc.Button).transition = cc.Button.Transition.SCALE;
            this.addLbNode(btnName + 'Lb', text).setParent(btn);
            lo.updateLayout();
            return btn;
        }

        /**下载json数据 */
        private downLoadJson(fileName: string, data) {
            const json = JSON.stringify(data); // 将JSON数据转换为字符串
            if (CC_JSB) {
                const path = `${jsb.fileUtils.getWritablePath()}${fileName}.json`;
                const success = jsb.fileUtils.writeStringToFile(json, path);
                this.toast(success ? `已保存至 ${path}` : '保存失败：');
                return;
            }
            const blob = new Blob([json], { type: 'application/json' }); // 创建一个Blob对象

            // 创建下载链接
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `${fileName}_${new Date().toLocaleString().replace(/[:/\s]/g, '_')}`;

            // 触发点击事件进行下载
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        private show() {
            this.ns.EcsGM.active = true;
            this.addDragFileEvent();
        }

        private close() {
            const eb = this.ns.EcsGMInput.getComponent(cc.EditBox);
            eb.string = '';
            eb.placeholder = 'GM功能的参数都在此输入';
            this.ns.EcsGM.active = this.ns.EcsGMPop.active = false;
            this.removeDragFileEvent();
        }

        private addFitWidget(targetNode: cc.Node) {
            const wgt = targetNode.addComponent(cc.Widget);
            wgt.isAlignTop = wgt.isAlignBottom = wgt.isAlignLeft = wgt.isAlignRight = true;
        }

        private addSpNode(nodeName: string, color?: cc.Color) {
            const node = new cc.Node(nodeName);
            if (color) node.color = color;
            const sp = node.addComponent(cc.Sprite);
            sp.sizeMode = cc.Sprite.SizeMode.CUSTOM;
            sp.spriteFrame = this.spf.clone();
            return node;
        }

        private addLbNode(nodeName: string, text: string, color = cc.Color.BLACK) {
            const node = new cc.Node(nodeName);
            node.color = color;
            node.addComponent(cc.Label).string = text;
            return node;
        }
    };
})();
