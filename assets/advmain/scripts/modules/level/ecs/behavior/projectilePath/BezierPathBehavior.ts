import { ProjectileComponent } from '../../components/combat/ProjectileComponent';
import { IPoint } from '../../define/EcsDefine';
import { PathCalculationResult, PathCalculationData, ProjectilePathBehaviorBase } from './ProjectilePathBehaviorBase';

const EASING_FUNCTIONS: Record<string, (t: number) => number> = {
    // 线性，无缓动
    linear: (t) => t,

    // 二次缓动
    quadIn: (t) => t * t,
    quadOut: (t) => t * (2 - t),
    quadInOut: (t) => (t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t),

    // 三次缓动
    cubicIn: (t) => t * t * t,
    cubicOut: (t) => --t * t * t + 1,
    cubicInOut: (t) => (t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1),

    // 四次缓动
    quartIn: (t) => t * t * t * t,
    quartOut: (t) => 1 - --t * t * t * t,
    quartInOut: (t) => (t < 0.5 ? 8 * t * t * t * t : 1 - 8 * --t * t * t * t),

    // 正弦缓动
    sineIn: (t) => 1 - Math.cos((t * Math.PI) / 2),
    sineOut: (t) => Math.sin((t * Math.PI) / 2),
    sineInOut: (t) => -(Math.cos(Math.PI * t) - 1) / 2,

    // 指数缓动
    expoIn: (t) => (t === 0 ? 0 : Math.pow(2, 10 * (t - 1))),
    expoOut: (t) => (t === 1 ? 1 : 1 - Math.pow(2, -10 * t)),
    expoInOut: (t) => {
        if (t === 0) return 0;
        if (t === 1) return 1;
        return t < 0.5 ? Math.pow(2, 20 * t - 10) / 2 : (2 - Math.pow(2, -20 * t + 10)) / 2;
    },

    // 弹性缓动
    elasticIn: (t) => {
        if (t === 0) return 0;
        if (t === 1) return 1;
        return -Math.pow(2, 10 * (t - 1)) * Math.sin((t - 1.1) * 5 * Math.PI);
    },
    elasticOut: (t) => {
        if (t === 0) return 0;
        if (t === 1) return 1;
        return Math.pow(2, -10 * t) * Math.sin((t - 0.1) * 5 * Math.PI) + 1;
    },
    elasticInOut: (t) => {
        if (t === 0) return 0;
        if (t === 1) return 1;
        return t < 0.5
            ? -0.5 * Math.pow(2, 20 * t - 10) * Math.sin(((20 * t - 11.125) * Math.PI * 1) / 4.5)
            : 0.5 * Math.pow(2, -20 * t + 10) * Math.sin(((20 * t - 11.125) * Math.PI * 1) / 4.5) + 1;
    },
};

/**贝塞尔路径行为 */
export class BezierPathBehavior extends ProjectilePathBehaviorBase {
    calculatePath(params: PathCalculationData, projectile: ProjectileComponent): PathCalculationResult {
        const ctrlList = projectile.pathPrecomputedData.ctrlList;

        // 动态处理1个或2个控制点的情况
        const isQuadratic = ctrlList.length === 1;
        const p1 = ctrlList[0];
        const p2 = isQuadratic ? params.startPos : ctrlList[1]; // 二次贝塞尔曲线用起点作为第二个控制点

        // 采样点数（影响精度）
        const samples = 100;

        // 计算曲线总长度
        const totalLength = isQuadratic
            ? this.calculateQuadraticBezierLength(samples, params.startPos, p1, params.targetPos)
            : this.calculateBezierLength(samples, params.startPos, p1, p2, params.targetPos);

        // 计算已移动距离
        let moveDistance: number;

        if (params.moveTime !== undefined) {
            // 使用给定的移动时间计算移动比例
            moveDistance = totalLength * Math.min(params.elapsedTime / params.moveTime, 1);
        } else if (params.speed !== undefined) {
            // 使用速度计算移动距离
            moveDistance = params.speed * params.elapsedTime;
        } else {
            // 默认情况下视为立即抵达目标位置
            return {
                position: params.targetPos,
                isComplete: true,
            };
        }

        // 检查是否已抵达终点
        const isComplete = moveDistance >= totalLength;

        // 查找移动距离对应的t值
        let t = this.findTForDistance(moveDistance, totalLength, samples, params.startPos, p1, p2, params.targetPos);

        // 应用缓动效果
        const easing = projectile.pathPrecomputedData.easing || 'linear';
        const easingFunc = EASING_FUNCTIONS[easing] || EASING_FUNCTIONS.linear;
        t = easingFunc(t);

        // 计算曲线上的位置
        const position = this.bezierPoint(t, params.startPos, p1, p2, params.targetPos);

        return { position, isComplete };
    }

    /**
     * 计算贝塞尔曲线上某点的位置
     * @param t 曲线参数 (0-1)
     * @param p0 起点
     * @param p1 控制点1
     * @param p2 控制点2
     * @param p3 终点
     * @returns 曲线上的点
     */
    private bezierPoint(t: number, p0: IPoint, p1: IPoint, p2: IPoint, p3: IPoint): IPoint {
        const u = 1 - t;
        const u2 = u * u;
        const t2 = t * t;

        const x = u * u2 * p0.x + 3 * u2 * t * p1.x + 3 * u * t2 * p2.x + t * t2 * p3.x;

        const y = u * u2 * p0.y + 3 * u2 * t * p1.y + 3 * u * t2 * p2.y + t * t2 * p3.y;

        return { x, y };
    }

    /**
     * 计算二次贝塞尔曲线上某点的位置
     * @param t 曲线参数 (0-1)
     * @param p0 起点
     * @param p1 控制点
     * @param p2 终点
     * @returns 曲线上的点
     */
    private quadraticBezierPoint(t: number, p0: IPoint, p1: IPoint, p2: IPoint): IPoint {
        const u = 1 - t;
        const u2 = u * u;
        const t2 = t * t;

        const x = u2 * p0.x + 2 * u * t * p1.x + t2 * p2.x;
        const y = u2 * p0.y + 2 * u * t * p1.y + t2 * p2.y;

        return { x, y };
    }

    /**
     * 计算两点间距离
     * @param p1 点1
     * @param p2 点2
     * @returns 距离
     */
    private distance(p1: IPoint, p2: IPoint): number {
        const dx = p2.x - p1.x;
        const dy = p2.y - p1.y;
        return Math.sqrt(dx * dx + dy * dy);
    }

    /**
     * 计算三次贝塞尔曲线长度（使用采样法）
     * @param samples 采样点数
     * @param p0 起点
     * @param p1 控制点1
     * @param p2 控制点2
     * @param p3 终点
     * @returns 曲线总长度
     */
    private calculateBezierLength(samples: number, p0: IPoint, p1: IPoint, p2: IPoint, p3: IPoint): number {
        let length = 0;
        let prevPoint = p0;

        for (let i = 1; i <= samples; i++) {
            const t = i / samples;
            const currentPoint = this.bezierPoint(t, p0, p1, p2, p3);
            length += this.distance(prevPoint, currentPoint);
            prevPoint = currentPoint;
        }

        return length;
    }

    /**
     * 计算二次贝塞尔曲线长度（使用采样法）
     * @param samples 采样点数
     * @param p0 起点
     * @param p1 控制点
     * @param p2 终点
     * @returns 曲线总长度
     */
    private calculateQuadraticBezierLength(samples: number, p0: IPoint, p1: IPoint, p2: IPoint): number {
        let length = 0;
        let prevPoint = p0;

        for (let i = 1; i <= samples; i++) {
            const t = i / samples;
            const currentPoint = this.quadraticBezierPoint(t, p0, p1, p2);
            length += this.distance(prevPoint, currentPoint);
            prevPoint = currentPoint;
        }

        return length;
    }

    /**
     * 在贝塞尔曲线上查找给定距离对应的t值
     * @param distanceAlongCurve 沿曲线移动的距离
     * @param totalLength 曲线总长度
     * @param samples 采样点数
     * @param p0 起点
     * @param p1 控制点1
     * @param p2 控制点2
     * @param p3 终点
     * @returns 曲线参数t
     */
    private findTForDistance(
        distanceAlongCurve: number,
        totalLength: number,
        samples: number,
        p0: IPoint,
        p1: IPoint,
        p2: IPoint,
        p3: IPoint,
    ): number {
        if (distanceAlongCurve <= 0) return 0;
        if (distanceAlongCurve >= totalLength) return 1;

        let currentDistance = 0;
        let prevPoint = p0;
        const isQuadratic = p3 === undefined;

        for (let i = 1; i <= samples; i++) {
            const t = i / samples;
            const currentPoint = isQuadratic ? this.quadraticBezierPoint(t, p0, p1, p2) : this.bezierPoint(t, p0, p1, p2, p3);
            const segmentLength = this.distance(prevPoint, currentPoint);

            if (currentDistance + segmentLength >= distanceAlongCurve) {
                // 找到包含目标距离的线段，进行插值
                const distanceInSegment = distanceAlongCurve - currentDistance;
                const tIncrement = (distanceInSegment / segmentLength) * (1 / samples);
                return (i - 1) / samples + tIncrement;
            }

            currentDistance += segmentLength;
            prevPoint = currentPoint;
        }

        return 1;
    }
}
