import ProduceBlockListComponent from '../../../components/ProduceBlockListComponent';
import { CellColor } from '../../../define/BoardDefine';
import { RcShape } from '../../../define/EcsConfig';
import { IRCShape } from '../../../define/EcsDefine';
import { ECSEvent } from '../../../GameEvent';
import { ECSRuleBase, IECSRuleConfig } from '../../core/ECSRuleBase';

export class ProduceBlockShapeRule extends ECSRuleBase<IECSRuleConfig, ECSEvent.GameEvent.PRODUCE_BLOCK_END> {
    public bindEventData() {
        return ECSEvent.GameEvent.PRODUCE_BLOCK_END;
    }
    canExecute(): boolean {
        return true;
    }
    execute(): void {
        const info = this.eventData;
        const blockIds = info.blockIds;
        const produceBlockList: { shape: IRCShape }[] = [];
        for (let i = 0; i < blockIds.length; i++) {
            produceBlockList.push({
                shape: RcShape[blockIds[i]],
            });
        }
        const entity = this.world.createEntity();
        this.world.addComponent(entity, ProduceBlockListComponent, produceBlockList);
    }
}
