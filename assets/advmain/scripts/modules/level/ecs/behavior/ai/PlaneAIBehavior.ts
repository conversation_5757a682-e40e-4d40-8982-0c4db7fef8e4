import BoardComponent from '../../components/board/BoardComponent';
import { CellComponent } from '../../components/board/CellComponent';
import SlotComponent from '../../components/board/SlotComponent';
import TargetComponent from '../../components/special/TargetComponent';
import { DestroyTag } from '../../components/tag/DestroyTag';
import { RelationName } from '../../cores/center/EntityCenter/WorldRelation';
import { TempleteType } from '../../registry/templete/TempleteRegistry';
import { CellAIBehavior } from './CellAIBehavior';

/**飞机块AI */
export class PlaneAIBehavior extends CellAIBehavior {
    protected removed(entityId: number) {
        super.removed(entityId);
        const targetEntityId = this.getPlaneTarget();
        const cell = this.world.getComponent(entityId, CellComponent);
        //临时写法，等可以获取相对逻辑坐标后这里应该直接取entityId的坐标
        const position = this.world.handlerCenter.boardHandler.getEntityRootPoint(this.world.getSources(entityId, RelationName.SLOT_CELL)[0]);
        if (!targetEntityId) {
            this.world.templeteCenter.createTempleteEntity(
                TempleteType.Effect,
                { parentEntity: this.world.ECSLayerType.EffectLayerEntity, ecsViewId: 'ecsview_simple_effect' },
                {
                    nodeParam: { position },
                    renderParam: {
                        data: {
                            aniName: `fly_${cell.temp.color}`,
                            src: 'cell/110/gameplay_spine_plane',
                            bundleName: 'level',
                        },
                    },
                },
            );
            return;
        }

        const targetPoint = this.world.handlerCenter.boardHandler.getEntityRootPoint(targetEntityId);
        const targetCell = this.world.getComponent(targetEntityId, CellComponent);
        let c2x = position.x / 2 + targetPoint.x / 2;
        if (Math.abs(targetCell.c - cell.c) < 2) {
            c2x = targetPoint.x + (targetCell.r > cell.c ? 200 : -200);
        }
        this.world.templeteCenter.createTempleteEntity(
            TempleteType.Bullet,
            {
                parentEntity: this.world.ECSLayerType.EffectLayerEntity,
                ecsViewId: 'ecsview_plane_spine',
                projectileBehaviorId: 'projectile_airplane',
                context: { targetEntityId },
            },
            {
                nodeParam: { position },
                renderParam: { data: cell.temp.color },
                pathPrecomputedData: {
                    ctrlList: [
                        { x: position.x, y: position.y + 0.4 * Math.max(200, Math.abs(targetPoint.y - position.y)) },
                        { x: c2x, y: Math.max(position.y, targetPoint.y) + 0.8 * Math.max(200, Math.abs(targetPoint.y - position.y)) },
                    ],
                    easing: 'quadIn',
                },
                hitRenderParam: { data: { aniName: 'BOOM', src: 'cell/110/gameplay_spine_plane', bundleName: 'level' } },
            },
        );
    }

    /**获取飞机目标 */
    private getPlaneTarget() {
        const world = this.world;
        //临时写法，先用结算数据，后续要改成使用回合数据
        let planeOrderList: { order: number; entity: number }[] = world.cacheCenter.caculateData.planeOrderList;
        if (!planeOrderList) {
            planeOrderList = [];
            //根据目标，设置优先级加成
            const orderAdd: { [k: string]: number } = {};
            const targets = world.handlerCenter.boardHandler.getUniqueComponent(TargetComponent).targets;
            for (let i = 0; i < targets.length; i++) {
                if (targets[i].current < targets[i].to && world.handlerCenter.cellHandler.isCell(targets[i].key)) orderAdd[targets[i].key] = 100;
            }
            //遍历格子，设置优先级
            world.query([SlotComponent]).forEach((slotEntity) => {
                const cellEntity = world.handlerCenter.boardHandler.getSlotOccupy(slotEntity);
                if (!cellEntity) return;
                const parentBoard = world.getComponent(world.getSources(cellEntity, RelationName.PARENT_CHILD)[0], BoardComponent);
                if (!parentBoard) return;
                const cell = world.getComponent(cellEntity, CellComponent);
                planeOrderList.push({ order: (orderAdd[cell.type] || 0) + Math.random(), entity: cellEntity });
            });
            //根据优先级排序获得最终目标数据
            planeOrderList.sort((a, b) => a.order - b.order);
            world.cacheCenter.caculateData.planeOrderList = planeOrderList;
        }
        let targetEntity = planeOrderList.pop()?.entity;
        while ((!targetEntity || world.getComponent(targetEntity, DestroyTag)) && planeOrderList.length) {
            targetEntity = planeOrderList.pop()?.entity;
        }
        return targetEntity;
    }
}
