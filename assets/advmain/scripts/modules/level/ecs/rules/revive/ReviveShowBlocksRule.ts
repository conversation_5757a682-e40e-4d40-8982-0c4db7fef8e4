import { BoardScene } from '../../components/board/BoardScene';
import { ECSRuleBase, IECSRuleConfig } from '../core/ECSRuleBase';
import { ECSEvent } from '../../GameEvent';
import { IProduceBlockEnd } from '../../GameEventData';
import { TempleteType } from '../../registry/templete/TempleteRegistry';
import { RelationName } from '../../cores/WorldRelation';
import { RcShape } from '../../define/EcsConfig';
import { ShapeMargin } from '../../define/BoardDefine';
import { CellType, TargetType, NormalColor7 } from '../../define/BoardDefine';
import TargetComponent from '../../components/special/TargetComponent';
import { MathUtil } from '../../utils/MathUtil';

export interface IReviveShowBlocksRuleConfig extends IECSRuleConfig {
    // 可以添加配置参数
}

/**
 * 复活显示块规则
 * 当玩家确认复活时，在下方操作区域显示复活块
 */
export class ReviveShowBlocksRule extends ECSRuleBase<IReviveShowBlocksRuleConfig> {
    
    bindEventData() {
        return ECSEvent.GameEvent.REVIVE_CONFIRMED;
    }
    
    canExecute(): boolean {
        return true;
    }
    
    execute(): void {
        const world = this.world;
        const reviveComp = world.utilCenter.reviveUtil.getReviveComponent();

        if (!reviveComp || !reviveComp.cachedReviveBlocks) {
            console.error('ReviveShowBlocksRule: 没有缓存的复活块数据');
            return;
        }

        try {
            // 1. 清除预览效果（如果有的话）
            this.clearPreviewBlocks(world);

            // 2. 使用缓存的数据创建实际的可拖拽块
            this.createReviveBlocks(world, reviveComp.cachedReviveBlocks);

            // 3. 扣除复活次数并清除缓存
            world.utilCenter.reviveUtil.consumeReviveCount();
            world.utilCenter.reviveUtil.clearPreviewData();

            console.log('ReviveShowBlocksRule: 复活块显示完成');

        } catch (error) {
            console.error('ReviveShowBlocksRule: 执行失败', error);
        }
    }
    
    private clearPreviewBlocks(world: any): void {
        // 清除棋盘上的预览效果
        // 具体实现根据预览显示方式而定
        // 这里可以根据需要实现预览清除逻辑
        console.log('ReviveShowBlocksRule: 清除预览块');
    }
    
    private createReviveBlocks(world: any, blockData: IProduceBlockEnd): void {
        // 复用 ProduceBlockEndRule 的逻辑创建实际的方块
        const sceneEntity = world.ECSLayerType.SceneLayerEntity;
        const boardSceneComp = world.getComponent(sceneEntity, BoardScene);
        
        if (!boardSceneComp) {
            console.error('ReviveShowBlocksRule: 找不到BoardScene组件');
            return;
        }
        
        // 清空当前待放置块
        if (boardSceneComp.waitPutCells.length > 0) {
            for (const shapeEntityId of boardSceneComp.waitPutCells) {
                const childEntities = world.getTargets(shapeEntityId, RelationName.PARENT_CHILD);
                for (const childEntityId of childEntities) {
                    world.destroyEntity(childEntityId);
                }
                world.destroyEntity(shapeEntityId);
            }
            boardSceneComp.waitPutCells = [];
        }
        
        // 创建复活块（复用现有逻辑）
        for (let i = 0; i < blockData.blockIds.length; i++) {
            const blockId = blockData.blockIds[i];
            const rcShape = RcShape[blockId];
            
            if (!rcShape) {
                console.warn('ReviveShowBlocksRule: 找不到形状配置', blockId);
                continue;
            }
            
            const shapeEntity = world.templeteCenter.createTempleteEntity(TempleteType.Shape, {
                rcShape,
                x: (i - 1) * ShapeMargin,
                parentEntity: sceneEntity,
            });
            
            // 随机颜色
            const color = this.getColor();
            
            // 获取收集目标类型
            const cellHandler = world.handlerCenter.cellHandler;
            const collectTypes = world
                .getComponent(world.query([TargetComponent])[0], TargetComponent)
                .targets.filter((t: any) => cellHandler.isCell(t.key))
                .map((t: any) => t.key as CellType);
            
            // 创建方块的每个单元格
            rcShape.shape.forEach((rc: any) => {
                let type: CellType;
                // 40% 概率生成收集块
                const isCollect = collectTypes.length > 0 && Math.random() < 0.4;
                if (isCollect) {
                    type = collectTypes[MathUtil.randomInt(collectTypes.length - 1, 0)];
                } else {
                    // 10%概率生成飞机块
                    if (Math.random() < 0.1) {
                        type = TargetType.Plane;
                    } else {
                        type = TargetType.Normal;
                    }
                }
                
                const slotEntity = world.templeteCenter.createTempleteEntity(TempleteType.Slot, {
                    rc,
                    parentSize: rcShape,
                    parentEntity: shapeEntity,
                });
                
                world.templeteCenter.createTempleteEntity(TempleteType.Cell, {
                    rc,
                    parentSize: rcShape,
                    parentEntity: shapeEntity,
                    cellOption: { color, type },
                    slotEntity,
                });
            });
            
            boardSceneComp.waitPutCells.push(shapeEntity);
        }
        
        console.log('ReviveShowBlocksRule: 创建复活块完成', blockData.blockIds);
    }
    
    private getColor(): number {
        // 随机获取颜色
        return NormalColor7[MathUtil.randomInt(6, 0)];
    }
}
