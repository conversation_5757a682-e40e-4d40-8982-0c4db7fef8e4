/**获取组件Id
 * @param compType 组件类型
 * @param idAdd 自增闭包函数
 */
function getCompId(compType, idAdd) {
    return compType * TypeGap + idAdd();
}
/**获取组件类型
 * @param compId 组件Id
 */
function getCompType(compId) {
    return Math.floor(compId / TypeGap);
}
/**id自增 */
function IdAdd() {
    var index = 0;
    function increase() {
        return ++index;
    }
    return increase;
}
/**条件组件一定生效 */
var AllwaysTrue = 1;
var TypeGap = 100000;

/**配置预制体基类 */
var BaseConfigPrefab = /** @class */ (function () {
    function BaseConfigPrefab() {
    }
    /**初始化配置预制体 */
    BaseConfigPrefab.prototype.initConfigPrefab = function (idAdd, prefabConfigData) {
        var listCompId = this.$initCompId(this.registeCompIdList(), idAdd);
        var config = this.config = { main: listCompId[0], compTvoMap: {} };
        var compTvoMap = config.compTvoMap;
        this.createConfigPrefab(listCompId, config.compTvoMap);
        if (prefabConfigData) {
            this.prefabConfigData = prefabConfigData;
            this.rewriteConfig(prefabConfigData);
            this.rewritePrefabData(listCompId, compTvoMap, prefabConfigData);
        }
    };
    BaseConfigPrefab.prototype.$initCompId = function (listCompType, idAdd) {
        return listCompType.map(function (compType) { return getCompId(compType, idAdd); });
    };
    return BaseConfigPrefab;
}());

/**
 * Mitt: Tiny (~200b) functional event emitter / pubsub.
 * @name mitt
 * @returns {Mitt}
 */
function mitt(all) {
    all = all || new Map();
    return {
        /**
         * A Map of event names to registered handler functions.
         */
        all: all,
        /**
         * Register an event handler for the given type.
         * @param {string|symbol} type Type of event to listen for, or `'*'` for all events
         * @param {Function} handler Function to call in response to given event
         * @memberOf mitt
         */
        on: function (type, handler) {
            var handlers = all.get(type);
            if (handlers) {
                handlers.push(handler);
            }
            else {
                all.set(type, [handler]);
            }
        },
        /**
         * Remove an event handler for the given type.
         * If `handler` is omitted, all handlers of the given type are removed.
         * @param {string|symbol} type Type of event to unregister `handler` from (`'*'` to remove a wildcard handler)
         * @param {Function} [handler] Handler function to remove
         * @memberOf mitt
         */
        off: function (type, handler) {
            var handlers = all.get(type);
            if (handlers) {
                if (handler) {
                    handlers.splice(handlers.indexOf(handler) >>> 0, 1);
                }
                else {
                    all.set(type, []);
                }
            }
        },
        /**
         * Invoke all handlers for the given type.
         * If present, `'*'` handlers are invoked after type-matched handlers.
         *
         * Note: Manually firing '*' handlers is not supported.
         *
         * @param {string|symbol} type The event type to invoke
         * @param {Any} [evt] Any value (object is recommended and powerful), passed to each handler
         * @memberOf mitt
         */
        emit: function (type, evt) {
            var handlers = all.get(type);
            if (handlers) {
                handlers
                    .slice()
                    .map(function (handler) {
                    handler(evt);
                });
            }
            handlers = all.get('*');
            if (handlers) {
                handlers
                    .slice()
                    .map(function (handler) {
                    handler(type, evt);
                });
            }
        }
    };
}

/**固定随机生成器 */
var FixedRandomUtil = /** @class */ (function () {
    function FixedRandomUtil() {
        this.seed = Math.floor(Math.random() * 1000000);
    }
    /**初始化种子 */
    FixedRandomUtil.prototype.initSeed = function (seed) {
        this.seed = seed;
    };
    /**获取当前种子 */
    FixedRandomUtil.prototype.getSeed = function () {
        return this.seed;
    };
    // 采用线性同余生成器
    FixedRandomUtil.prototype.originRandom = function () {
        this.seed = (this.seed * 16807) % 2147483647;
        return (this.seed - 1) / 2147483646;
    };
    /**
     * 随机数 带小数点
     * @param min 最小随机数
     * @param max 最大随机数
     */
    FixedRandomUtil.prototype.random = function (min, max) {
        if (min > max) {
            return min;
        }
        var num = min + this.originRandom() * (max - min);
        return num;
    };
    /**
    * 随机数 整数
    * @param min 最小随机数
    * @param max 最大随机数
    */
    FixedRandomUtil.prototype.randomInt = function (min, max) {
        var num = this.random(min, max);
        return Math.floor(num);
    };
    /**
    * 从列表随机出一个子列表
    * @param arr 列表
    * @param count 随机出的子列表数量
    */
    FixedRandomUtil.prototype.randomArr = function (arr, count) {
        var reservoir = [];
        reservoir.length = Math.min(count, arr.length);
        for (var i = 0; i < reservoir.length; i++) {
            reservoir[i] = arr[i];
        }
        for (var i = count; i < arr.length; i++) {
            // 随机获得一个[0, i]内的随机整数
            var randomInt = this.randomInt(0, i);
            // 如果随机整数在[0, m-1]范围内，则替换蓄水池中的元素
            if (randomInt < count) {
                reservoir[randomInt] = arr[i];
            }
        }
        return reservoir;
    };
    /**随机出列表中某个子项
     * @param arr 列表
     */
    FixedRandomUtil.prototype.randomItem = function (arr) {
        if (!arr) {
            return;
        }
        var randomIndex = this.randomInt(0, arr.length - 1);
        return arr[randomIndex];
    };
    // 洗牌函数
    FixedRandomUtil.prototype.shuffle = function (array, returnNew) {
        if (returnNew === void 0) { returnNew = true; }
        var newArr = array;
        if (returnNew) {
            newArr = array.slice(); // 不改变原数组，将数组剪切一份给newArr
        }
        for (var i = 0; i < newArr.length; i++) {
            var j = this.randomInt(0, i);
            var temp = newArr[i];
            newArr[i] = newArr[j];
            newArr[j] = temp;
        }
        return newArr;
    };
    /**
     * 概率 0- 1 看是否命中
     * @param val 命中率
     */
    FixedRandomUtil.prototype.hitRate = function (val) {
        return val > this.random(0, 1);
    };
    return FixedRandomUtil;
}());

/**数据结构工具 */
var DataStructureUtil = /** @class */ (function () {
    function DataStructureUtil() {
    }
    /**给这个数组每个元素做相同值的加法 */
    DataStructureUtil.arrAddSameValue = function (arr, num) {
        var length = arr.length;
        var newArr = [];
        for (var i = 0; i < length; i++) {
            newArr[i] = arr[i] + num;
        }
        return newArr;
    };
    /**初始化一个序列递增的数组 */
    DataStructureUtil.generateArray = function (length) {
        var sequence = [];
        for (var i = 0; i < length; i++) {
            sequence.push(i);
        }
        return sequence;
    };
    /**初始化一个序列不变的数组 */
    DataStructureUtil.generateFixedArray = function (length, num) {
        if (num === void 0) { num = 0; }
        var sequence = [];
        for (var i = 0; i < length; i++) {
            sequence.push(num);
        }
        return sequence;
    };
    /**
     * 往列表里面添加一个不重复的元素
     *
     */
    DataStructureUtil.noRepeatAdd = function (arr, item) {
        if (!arr) {
            return;
        }
        var index = arr.indexOf(item);
        if (index === -1) {
            arr.push(item);
        }
    };
    /**
     * 从列表里面删除一个不重复的元素
     *
     */
    DataStructureUtil.noRepeatRemove = function (arr, item) {
        if (!arr) {
            return;
        }
        var index = arr.indexOf(item);
        if (index >= 0) {
            arr.splice(index, 1);
        }
    };
    /**列表 */
    DataStructureUtil.hasItem = function (arr, item) {
        if (!arr) {
            return false;
        }
        var index = arr.indexOf(item);
        return index >= 0;
    };
    /**删除列表中某个子项
     * @param arr 列表
     * @param item 子项
     */
    DataStructureUtil.deleteArrayItem = function (arr, item) {
        if (!arr || !item) {
            return;
        }
        var index = arr.indexOf(item);
        if (index >= 0) {
            arr.splice(index, 1);
        }
    };
    /**将键值对的键转化成有序列表 */
    DataStructureUtil.dictKeyToArray = function (obj) {
        if (!obj) {
            return [];
        }
        var array = [];
        for (var key in obj) {
            array.push(key);
        }
        return array;
    };
    /**将键值对的值转化成有序列表 */
    DataStructureUtil.dictToArray = function (obj) {
        if (!obj) {
            return [];
        }
        var array = [];
        for (var key in obj) {
            var temp = obj[key];
            array.push(temp);
        }
        return array;
    };
    /**将键值对转化成Map */
    DataStructureUtil.dictToMap = function (obj) {
        if (!obj) {
            return new Map();
        }
        var map = new Map();
        for (var key in obj) {
            var temp = obj[key];
            map.set(key, temp);
        }
        return map;
    };
    /**将Map的键转换成列表 */
    DataStructureUtil.mapKeyToArray = function (map) {
        if (!map) {
            return [];
        }
        var array = [];
        map.forEach(function (value, key) {
            array.push(key);
        });
        return array;
    };
    /**将Map的值转换成列表 */
    DataStructureUtil.mapValToArray = function (map) {
        if (!map) {
            return [];
        }
        var array = [];
        map.forEach(function (value) {
            array.push(value);
        });
        return array;
    };
    /**
     * copy对象的值
     * 浅拷贝
     * @param sobj 源对象
     * @param tobj 目标对象
     */
    DataStructureUtil.oto = function (sobj, tobj) {
        for (var key in sobj) {
            tobj[key] = sobj[key];
        }
    };
    return DataStructureUtil;
}());

/******************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */
/* global Reflect, Promise, SuppressedError, Symbol */

var extendStatics = function(d, b) {
    extendStatics = Object.setPrototypeOf ||
        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
    return extendStatics(d, b);
};

function __extends(d, b) {
    if (typeof b !== "function" && b !== null)
        throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
    extendStatics(d, b);
    function __() { this.constructor = d; }
    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
}

function __spreadArray(to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
}

typeof SuppressedError === "function" ? SuppressedError : function (error, suppressed, message) {
    var e = new Error(message);
    return e.name = "SuppressedError", e.error = error, e.suppressed = suppressed, e;
};

/**
 * 打印工具
 *
 */
var LogUtil = /** @class */ (function () {
    function LogUtil() {
    }
    LogUtil.printLog = function (msg) {
        if (!this.showLog) {
            return;
        }
        this.print(msg);
    };
    LogUtil.print = function () {
        var args = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            args[_i] = arguments[_i];
        }
        if (this.showLog) {
            console.log.apply(console, __spreadArray([this.TAG], args, false));
        }
    };
    LogUtil.log = function () {
        var args = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            args[_i] = arguments[_i];
        }
        if (this.showLog) {
            console.log.apply(console, __spreadArray([this.TAG], args, false));
        }
    };
    LogUtil.warn = function () {
        var args = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            args[_i] = arguments[_i];
        }
        if (this.showLog) {
            console.warn.apply(console, __spreadArray([this.TAG], args, false));
        }
    };
    LogUtil.TAG = "算法SDK：";
    LogUtil.showLog = true;
    return LogUtil;
}());

var SplitFrameUtil = /** @class */ (function () {
    function SplitFrameUtil() {
    }
    /**初始化分帧工具 */
    SplitFrameUtil.openSplitFrame = function () {
        this.$isOpenSplitFrame = true;
        this.$lastFrameTime = Date.now();
    };
    /**关闭分帧 */
    SplitFrameUtil.closeSplitFrame = function () {
        this.$isOpenSplitFrame = false;
    };
    /**设置分帧间隔时间 */
    SplitFrameUtil.setFrameTime = function (frameTime) {
        this.frameTime = frameTime;
    };
    /**设置分帧间隔时间 */
    SplitFrameUtil.setOverTime = function (overTime) {
        this.overTime = overTime;
    };
    /**检查是否超时 */
    SplitFrameUtil.checkOverTime = function () {
        var now = Date.now();
        var ret = now >= this.overTime;
        if (ret) {
            LogUtil.printLog('超时');
        }
        return ret;
    };
    /**分帧计算-不要在调用很密集的地方使用该函数,这会带来额外的性能损耗*/
    SplitFrameUtil.checkNextFrame = function () {
        var _this = this;
        if (!this.$isOpenSplitFrame) {
            return;
        }
        var now = Date.now();
        if (now - this.$lastFrameTime >= this.frameTime) {
            LogUtil.printLog('执行分帧');
            return new Promise(function (resolve) {
                setTimeout(function () {
                    _this.$lastFrameTime = Date.now();
                    resolve();
                }, 0);
            });
        }
    };
    /**强制进行分帧*/
    SplitFrameUtil.forceNextFrame = function () {
        if (!this.$isOpenSplitFrame) {
            return;
        }
        LogUtil.printLog('强制分帧');
        return new Promise(function (resolve) {
            setTimeout(function () {
                resolve();
            }, 0);
        });
    };
    /**是否打开分帧 */
    SplitFrameUtil.$isOpenSplitFrame = false;
    /**分帧间隔时间 */
    SplitFrameUtil.frameTime = 10;
    /**超时时间 */
    SplitFrameUtil.overTime = 0;
    return SplitFrameUtil;
}());

var AllConditionComponent = /** @class */ (function () {
    function AllConditionComponent() {
    }
    AllConditionComponent.prototype.checkCondition = function () {
        var conditionCompIdArr = this.compTvo.conditionCompIdArr;
        var context = this.context;
        for (var i = 0, len = conditionCompIdArr.length; i < len; i++) {
            if (!context.runConditionComp(conditionCompIdArr[i])) {
                return false;
            }
        }
        return true;
    };
    return AllConditionComponent;
}());

var BasicFeatureComponent = /** @class */ (function () {
    function BasicFeatureComponent() {
    }
    /**async*/ BasicFeatureComponent.prototype.executeFeature = function () {
        var context = this.context;
        var compTvo = this.compTvo;
        if (context.runConditionComp(compTvo.conditionCompId) && (!compTvo.abilityConditionCompId || context.runConditionComp(compTvo.abilityConditionCompId))) {
            /**await*/ context.runLogicComp(compTvo.logicCompId);
            return true;
        }
        return false;
    };
    return BasicFeatureComponent;
}());

var FeatureGroupLogicComponent = /** @class */ (function () {
    function FeatureGroupLogicComponent() {
    }
    /**async*/ FeatureGroupLogicComponent.prototype.execute = function () {
        var context = this.context;
        var compTvo = this.compTvo;
        var featureCompIdArr = compTvo.featureCompIdArr;
        var strategyDefine = compTvo.featureLayerDefine;
        for (var i = 0, len = featureCompIdArr.length; i < len; i++) {
            var result = /**await*/ context.runFeatureComp(featureCompIdArr[i]);
            if (strategyDefine == FeatureLayerDefine.ExecuteOne && result) {
                return;
            }
        }
    };
    return FeatureGroupLogicComponent;
}());
/**特性层定义 */
var FeatureLayerDefine;
(function (FeatureLayerDefine) {
    /**按优先级顺序执行全部 */
    FeatureLayerDefine[FeatureLayerDefine["ExecuteAll"] = 0] = "ExecuteAll";
    /**按优先级顺序执行一个 */
    FeatureLayerDefine[FeatureLayerDefine["ExecuteOne"] = 1] = "ExecuteOne";
})(FeatureLayerDefine || (FeatureLayerDefine = {}));

var LinearGroupLogicComponent = /** @class */ (function () {
    function LinearGroupLogicComponent() {
    }
    /**async*/ LinearGroupLogicComponent.prototype.execute = function () {
        var componentArr = this.compTvo.itemTvoArr;
        var context = this.context;
        for (var i = 0, len = componentArr.length; i < len; i++) {
            var itemTvo = componentArr[i];
            if (context.runConditionComp(itemTvo.conditionCompId)) {
                /**await*/ context.runLogicComp(itemTvo.logicCompId);
            }
        }
    };
    return LinearGroupLogicComponent;
}());

var RandomLogicComponent = /** @class */ (function () {
    function RandomLogicComponent() {
    }
    /**async*/ RandomLogicComponent.prototype.execute = function () {
        var context = this.context;
        var compTvo = this.compTvo;
        var randomLogicArr = compTvo.randomLogicArr;
        var randomVal = context.fixedRandom.random(0, this.$getMaxWeight(randomLogicArr));
        var val = 0;
        for (var i = 0, len = randomLogicArr.length; i < len; i++) {
            var item = randomLogicArr[i];
            val += item.randomWeight;
            if (randomVal <= val) {
                /**await*/ context.runLogicComp(item.logicCompId);
                break;
            }
        }
    };
    /**获取总权重 */
    RandomLogicComponent.prototype.$getMaxWeight = function (randomLogicArr) {
        var maxWeight = 0;
        for (var i = 0, len = randomLogicArr.length; i < len; i++) {
            var item = randomLogicArr[i];
            maxWeight += item.randomWeight;
        }
        return maxWeight;
    };
    return RandomLogicComponent;
}());

var SelectGroupLogicComponent = /** @class */ (function () {
    function SelectGroupLogicComponent() {
    }
    /**async*/ SelectGroupLogicComponent.prototype.execute = function () {
        var componentArr = this.compTvo.itemTvoArr;
        var context = this.context;
        for (var i = 0, len = componentArr.length; i < len; i++) {
            var itemTvo = componentArr[i];
            if (context.runConditionComp(itemTvo.conditionCompId)) {
                /**await*/ context.runLogicComp(itemTvo.logicCompId);
                return;
            }
        }
    };
    return SelectGroupLogicComponent;
}());

/**注册组件 */
function $registeCoreComp(puzzle) {
    puzzle.$registeCoreComponents(1 /* CoreComponentType.AllConditionComponent */, AllConditionComponent);
    puzzle.$registeCoreComponents(2 /* CoreComponentType.SelectGroupLogicComponent */, SelectGroupLogicComponent);
    puzzle.$registeCoreComponents(3 /* CoreComponentType.LinearGroupLogicComponent */, LinearGroupLogicComponent);
    puzzle.$registeCoreComponents(4 /* CoreComponentType.RandomLogicComponent */, RandomLogicComponent);
    puzzle.$registeCoreComponents(5 /* CoreComponentType.FeatureGroupLogicComponent */, FeatureGroupLogicComponent);
    puzzle.$registeCoreComponents(6 /* CoreComponentType.BasicFeatureComponent */, BasicFeatureComponent);
}

/**全局特性数据结构 */
var GFStructure = /** @class */ (function () {
    function GFStructure() {
        this.$dataChanged = false;
        this.$dataChangedMap = {};
        this.$data = {};
        this.$formatData = [];
    }
    GFStructure.prototype.push = function (config) {
        var globalFeatureKey = this.$getGlobalFeatureKey(config.layer, config.type);
        var configArr = this.$data[globalFeatureKey];
        if (!configArr) {
            configArr = [];
            this.$data[globalFeatureKey] = configArr;
        }
        configArr.push(config);
        this.$dataChangedMap[globalFeatureKey] = true;
        this.$dataChanged = true;
    };
    /**获取格式化后的全局特性数据数组 */
    GFStructure.prototype.getDataArr = function () {
        this.$updateData();
        return this.$formatData;
    };
    /**更新顺序-从大到小排序 */
    GFStructure.prototype.$updateData = function () {
        if (!this.$dataChanged) {
            return;
        }
        this.$dataChanged = false;
        var dataChangedMap = this.$dataChangedMap;
        var formatData = DataStructureUtil.dictToArray(this.$data);
        var formatDataKeyArr = DataStructureUtil.dictKeyToArray(this.$data);
        for (var i = 0, len = formatDataKeyArr.length; i < len; i++) {
            if (dataChangedMap[formatDataKeyArr[i]]) {
                formatData[i].sort(function (a, b) { return b.priority - a.priority; });
                dataChangedMap[formatDataKeyArr[i]] = false;
            }
        }
        this.$formatData = formatData;
    };
    /**获取全局特性键值 */
    GFStructure.prototype.$getGlobalFeatureKey = function (layer, type) {
        return layer + "_" + type;
    };
    return GFStructure;
}());

/**向特性列表头部插入特性 */
var GF_InsertHead = /** @class */ (function () {
    function GF_InsertHead() {
    }
    GF_InsertHead.prototype.rewriteConfig = function (puzzleDataConfig, globalFeatureDataArr) {
        var config = puzzleDataConfig.config;
        var data = puzzleDataConfig.data;
        var layerConfig = config[globalFeatureDataArr[0].layer];
        for (var i = 0, len = globalFeatureDataArr.length; i < len; i++) {
            var globalFeatureData = globalFeatureDataArr[i];
            var configId = globalFeatureData.configId;
            var configData = data[configId];
            layerConfig.unshift(configId);
            if (Array.isArray(configData)) {
                configData.unshift(globalFeatureData.configData);
            }
            else {
                data[configId] = globalFeatureData.configData;
            }
        }
    };
    return GF_InsertHead;
}());

/**向特性列表头部插入特性 */
var GF_InsertTail = /** @class */ (function () {
    function GF_InsertTail() {
    }
    GF_InsertTail.prototype.rewriteConfig = function (puzzleDataConfig, globalFeatureDataArr) {
        var config = puzzleDataConfig.config;
        var data = puzzleDataConfig.data;
        var layerConfig = config[globalFeatureDataArr[0].layer];
        for (var i = 0, len = globalFeatureDataArr.length; i < len; i++) {
            var globalFeatureData = globalFeatureDataArr[i];
            var configId = globalFeatureData.configId;
            var configData = data[configId];
            layerConfig.push(configId);
            if (Array.isArray(configData)) {
                configData.push(globalFeatureData.configData);
            }
            else {
                data[configId] = globalFeatureData.configData;
            }
        }
    };
    return GF_InsertTail;
}());

/**注册全局特性 */
function $registeGlobalFeature(puzzle) {
    puzzle.registeGlobalFeature(1 /* CoreGlobalFeatureType.InsertHead */, new GF_InsertHead());
    puzzle.registeGlobalFeature(2 /* CoreGlobalFeatureType.InsertTail */, new GF_InsertTail());
}

var Puzzle = /** @class */ (function () {
    function Puzzle() {
    }
    /**===================拼图块接口间数据隔离End========================= */
    /**创建一个拼图块实例 */
    Puzzle.createPuzzle = function () {
        var puzzle = new Puzzle();
        puzzle.$init();
        return puzzle;
    };
    /**初始化拼图 */
    Puzzle.prototype.$init = function () {
        this.$statelessFuncMap = {};
        this.$compClzzMap = {};
        this.$featureMap = {};
        this.$globalFeaturePuzzleMap = {};
        this.$compInsMap = new Map();
        this.$compInsPuzzleMap = new Map();
        this.$compTvoPuzzleMap = new Map();
        this.$adapterMainMap = new Map();
        this.fixedRandom = new FixedRandomUtil();
        this.$dataMap = {};
        this.$staticDataMap = {};
        this.$externFuncMap = new Map();
        this.$globalFeatureMap = {};
        this.dispatcher = mitt();
        $registeCoreComp(this);
        $registeGlobalFeature(this);
    };
    Puzzle.prototype.$registeCoreComponents = function (key, component) {
        if (this.$compClzzMap[key]) {
            throw new Error("\u51FA\u73B0\u91CD\u590D\u6CE8\u518C\u7684\u7EC4\u4EF6\u7C7B\u578B:compType = ".concat(String(key)));
        }
        this.$compClzzMap[key] = component;
    };
    /**注册组件 */
    Puzzle.prototype.registeCoreComponents = function (key, component) {
        if (key < 1000) {
            throw new Error("1000\u4EE5\u4E0B\u7684\u7EC4\u4EF6\u7C7B\u578B\u662F\u6838\u5FC3\u7EC4\u4EF6\u7C7B\u578B<\u7981\u6B62\u5360\u7528>,compType = ".concat(key));
        }
        this.$registeCoreComponents(key, component);
    };
    /**无状态函数注册 */
    Puzzle.prototype.registeStateless = function (statelessType, statelessId, func) {
        var statelessKey = this.$getStatelessKey(statelessType, statelessId);
        if (this.$statelessFuncMap[statelessKey]) {
            throw new Error("\u51FA\u73B0\u91CD\u590D\u6CE8\u518C\u7684\u65E0\u72B6\u6001\u51FD\u6570\u7C7B\u578B: ".concat(String(statelessType), ",").concat(statelessId));
        }
        this.$statelessFuncMap[statelessKey] = func;
    };
    /**全局特性注册 */
    Puzzle.prototype.registeGlobalFeature = function (type, gf) {
        if (this.$globalFeatureMap[type]) {
            throw new Error("\u51FA\u73B0\u91CD\u590D\u6CE8\u518C\u7684\u5168\u5C40\u7279\u6027\u7C7B\u578B: ".concat(String(type)));
        }
        this.$globalFeatureMap[type] = gf;
    };
    /**配置预制体注册 */
    Puzzle.prototype.registeConfigPrefab = function (featureId, featureFP) {
        if (this.$featureMap[featureId]) {
            throw new Error("\u51FA\u73B0\u91CD\u590D\u6CE8\u518C\u7684\u7279\u6027\u9884\u5236\u4F53\u7C7B\u578B: ".concat(String(featureId)));
        }
        this.$featureMap[featureId] = featureFP;
    };
    /**创建配置预制体 */
    Puzzle.prototype.createConfigPrefab = function (featureId) {
        var FeatureFP = this.$featureMap[featureId];
        if (!FeatureFP) {
            throw new Error("\u8BE5\u914D\u7F6E\u9884\u5236\u4F53\u5C1A\u672A\u6CE8\u518A!featureId:".concat(featureId));
        }
        var featureFP = new FeatureFP();
        return featureFP;
    };
    /**初始化拼图块
     * @param puzzleType 拼图块类型
     * @param puzzleId 拼图块Id
     * @param config 拼图块配置 */
    Puzzle.prototype.initPuzzlePiece = function (puzzleType, puzzleId, config) {
        var puzzlePieceKey = this.$getPuzzlePieceKey(puzzleType, puzzleId);
        this.$compTvoPuzzleMap.set(puzzlePieceKey, config.compTvoMap);
        this.$adapterMainMap.set(puzzlePieceKey, config.main);
        this.$compInsPuzzleMap.set(puzzlePieceKey, new Map());
    };
    /**初始化拼图块全局特性
     * @param puzzleType 拼图块类型
     * @param config 全局特性配置 */
    Puzzle.prototype.initPuzzlePieceGlobalFeature = function (puzzleType, config) {
        var gf = this.$globalFeaturePuzzleMap[puzzleType];
        if (!gf) {
            gf = new GFStructure();
            this.$globalFeaturePuzzleMap[puzzleType] = gf;
        }
        gf.push(config);
    };
    /**获取拼图块全局特性 */
    Puzzle.prototype.getPuzzlePieceGlobalFeature = function (type) {
        return this.$globalFeatureMap[type];
    };
    /**获取拼图块全局特性集合 */
    Puzzle.prototype.getPuzzlePieceGlobalFeatureDataArr = function (puzzleType) {
        var gf = this.$globalFeaturePuzzleMap[puzzleType];
        if (gf) {
            return gf.getDataArr();
        }
        return [];
    };
    /**销毁拼图块
     * @param puzzleType 拼图块类型
     * @param puzzleId 拼图块Id
     * */
    Puzzle.prototype.disposePuzzlePiece = function (puzzleType, puzzleId) {
        var puzzlePieceKey = this.$getPuzzlePieceKey(puzzleType, puzzleId);
        var compArr = this.$getRemoveCompArr(puzzlePieceKey);
        for (var i = 0, len = compArr.length; i < len; i++) {
            this.$disposeComp(compArr[i]);
        }
        this.$compInsMap = null;
    };
    /**注册外部函数集 */
    Puzzle.prototype.registeExternFunc = function (type, func) {
        this.$externFuncMap.set(type, func);
    };
    /**运行外部函数 */
    Puzzle.prototype.executeExternFunc = function (type) {
        var args = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            args[_i - 1] = arguments[_i];
        }
        var func = this.$externFuncMap.get(type);
        if (!func) {
            throw new Error("没有注册该外部函数:" + type);
        }
        return func.apply(null, args);
    };
    /**运行拼图块
     * @param puzzleType 拼图块类型
     * @param puzzleId 拼图块Id
     * @param inputArgs 外部全局数据传参
     * @return 返回全局数据传参(内部可能会修改外部全局数据)
     */
    Puzzle.prototype.runPuzzlePiece = function (puzzleType, puzzleId, inputArgs) {
        LogUtil.printLog('执行拼图 type：' + puzzleType + ' id：' + puzzleId + '参数：' + JSON.stringify(inputArgs));
        this.resetPuzzlePieceMap(String(puzzleType), puzzleId);
        var comp = this.$getCompByCompId(this.$adapterCompId);
        comp.inputData(inputArgs);
        SplitFrameUtil.openSplitFrame();
        /**await*/ comp.execute();
        var outputArgs = comp.outputData();
        return outputArgs;
    };
    /**运行逻辑组件 */
    Puzzle.prototype.runLogicComp = function (compId) {
        LogUtil.printLog('执行逻辑组件 ' + compId);
        var comp = this.$getCompByCompId(compId);
        /**await*/ comp.execute();
    };
    /**运行特性组件 */
    Puzzle.prototype.runFeatureComp = function (compId) {
        var comp = this.$getCompByCompId(compId);
        var result = /**await*/ comp.executeFeature();
        return result;
    };
    /**运行条件组件 */
    Puzzle.prototype.runConditionComp = function (compId) {
        var result = false;
        var comp = null;
        if (compId === AllwaysTrue) { //优化,直接返回true,不再生成组件
            result = true;
        }
        else {
            comp = this.$getCompByCompId(compId);
            result = comp.checkCondition();
        }
        if (result) {
            LogUtil.printLog('执行条件组件 ' + compId + ' 返回 true');
        }
        else {
            LogUtil.printLog('执行条件组件 ' + compId + ' 返回 false');
        }
        return result;
    };
    /**运行公式组件 */
    Puzzle.prototype.runFormulaComp = function (compId, args) {
        LogUtil.printLog('执行公式组件 参数：' + JSON.stringify(args));
        var comp = this.$getCompByCompId(compId);
        var result = comp.calculate(args);
        return result;
    };
    /**运行无状态函数 */
    Puzzle.prototype.runStatelessFunc = function (statelessType, statelessId, inputArgs) {
        LogUtil.printLog('执行无状态函数 type：' + statelessType + ' id：' + statelessId);
        var func = this.$statelessFuncMap[this.$getStatelessKey(statelessType, statelessId)];
        if (!func) {
            throw new Error("\u6CA1\u6709\u6CE8\u518C\u8BE5\u65E0\u72B6\u6001\u51FD\u6570:".concat(String(statelessType), ",").concat(statelessId));
        }
        return /**await*/ func.call(null, inputArgs);
    };
    /**获取数据集 */
    Puzzle.prototype.getData = function (type) {
        return this.$dataMap[type];
    };
    /**设置数据集 */
    Puzzle.prototype.setData = function (type, val) {
        this.$dataMap[type] = val;
    };
    /**获取静态数据集 */
    Puzzle.prototype.getStaticData = function (type) {
        return this.$staticDataMap[type];
    };
    /**设置静态数据集 */
    Puzzle.prototype.setStaticData = function (type, val) {
        this.$staticDataMap[type] = val;
    };
    /**获取时间戳:s */
    Puzzle.prototype.getTime = function () {
        return Date.now() / 1000;
    };
    /**获取拼图块Id */
    Puzzle.prototype.getPuzzleId = function () {
        return this.$puzzleId;
    };
    /**重置拼图块重映射 */
    Puzzle.prototype.resetPuzzlePieceMap = function (puzzleType, puzzleId) {
        this.$puzzleId = puzzleId;
        var puzzlePieceKey = this.$getPuzzlePieceKey(puzzleType, puzzleId);
        this.$compInsMap = this.$compInsPuzzleMap.get(puzzlePieceKey);
        this.$compTvoMap = this.$compTvoPuzzleMap.get(puzzlePieceKey);
        this.$adapterCompId = this.$adapterMainMap.get(puzzlePieceKey);
        if (!this.$compInsMap) {
            throw new Error("\u8BE5\u62FC\u56FE\u5757\u6CA1\u6709\u88AB\u6CE8\u518C,puzzleType:".concat(puzzleType, ",puzzleId:").concat(puzzleId));
        }
    };
    /**生成拼图块配置 */
    Puzzle.prototype.createPuzzlePieceConfig = function (puzzleType, puzzleId, puzzleConfigPrefabId, data) {
        var puzzlePrefab = this.createConfigPrefab(puzzleConfigPrefabId);
        var idAdd = IdAdd();
        puzzlePrefab.init(puzzleType, this, idAdd);
        puzzlePrefab.initConfigPrefab(idAdd, data);
        return puzzlePrefab.config;
    };
    /**获取块接口键值 */
    Puzzle.prototype.$getPuzzlePieceKey = function (puzzleType, puzzleId) {
        return puzzleType + "_" + puzzleId;
    };
    /**获取无状态函数键值 */
    Puzzle.prototype.$getStatelessKey = function (statelessType, statelessId) {
        return statelessType + "_" + statelessId;
    };
    /**获取需要移除的组件列表 */
    Puzzle.prototype.$getRemoveCompArr = function (puzzlePieceKey) {
        var compInsMap = this.$compInsPuzzleMap.get(puzzlePieceKey);
        var compArr;
        if (this.$compInsPuzzleMap.delete(puzzlePieceKey)) {
            compArr = DataStructureUtil.mapValToArray(compInsMap);
            compInsMap.clear();
        }
        return compArr || [];
    };
    /**获取组件实例 */
    Puzzle.prototype.$getCompByCompId = function (compId) {
        var comp = this.$compInsMap.get(compId);
        if (!comp) {
            comp = this.$createComp(compId);
        }
        return comp;
    };
    /**获取组件配置 */
    Puzzle.prototype.$getCompTvo = function (compId) {
        return this.$compTvoMap[compId];
    };
    /**创建组件 */
    Puzzle.prototype.$createComp = function (compId) {
        if (!compId) {
            return null;
        }
        var compType = getCompType(compId);
        var compClzz = this.$compClzzMap[compType];
        if (!compClzz) {
            throw new Error("\u627E\u4E0D\u5230compClzz!compId=".concat(compId, ",compType=").concat(compType));
        }
        var comp = new compClzz();
        this.$compInsMap.set(compId, comp);
        comp.compId = compId;
        comp.compTvo = this.$getCompTvo(compId);
        comp.compType = compType;
        comp.context = this;
        if (comp.$create) {
            comp.$create();
        }
        return comp;
    };
    /**销毁组件 */
    Puzzle.prototype.$disposeComp = function (comp) {
        if (!comp) {
            return;
        }
        if (comp.$dispose) {
            comp.$dispose();
        }
    };
    return Puzzle;
}());

var _a;
/**算法类型枚举 */
var AlgoClassTypeConst;
(function (AlgoClassTypeConst) {
    /**===========================类型枚举============================== */
    /** 随机 */
    AlgoClassTypeConst[AlgoClassTypeConst["AlgoRandom"] = 0] = "AlgoRandom";
    /** 熵增1 */
    AlgoClassTypeConst[AlgoClassTypeConst["AlgoEntropyIncrease1"] = 1] = "AlgoEntropyIncrease1";
    /** 熵增3 */
    AlgoClassTypeConst[AlgoClassTypeConst["AlgoEntropyIncrease3"] = 2] = "AlgoEntropyIncrease3";
    /** 简单难题 */
    AlgoClassTypeConst[AlgoClassTypeConst["AlgoSimpleHard"] = 3] = "AlgoSimpleHard";
    /** 填空消除 */
    AlgoClassTypeConst[AlgoClassTypeConst["AlgoFillInTheBlank"] = 4] = "AlgoFillInTheBlank";
    /** 熵减*/
    AlgoClassTypeConst[AlgoClassTypeConst["AlgoEntropyReduction"] = 5] = "AlgoEntropyReduction";
    /** 困难难题*/
    AlgoClassTypeConst[AlgoClassTypeConst["AlgoHard"] = 6] = "AlgoHard";
    /** 随机无死题*/
    AlgoClassTypeConst[AlgoClassTypeConst["AlgoRandomNoDeath"] = 7] = "AlgoRandomNoDeath";
    /** 直觉难题*/
    AlgoClassTypeConst[AlgoClassTypeConst["AlgoInstinctHard"] = 8] = "AlgoInstinctHard";
    /** 致死题*/
    AlgoClassTypeConst[AlgoClassTypeConst["AlgoDeath"] = 9] = "AlgoDeath";
    /**===========================实例枚举============================== */
    /**底板-随机 */
    AlgoClassTypeConst[AlgoClassTypeConst["algo_random"] = 10001] = "algo_random";
    /**底板-首次随机 */
    AlgoClassTypeConst[AlgoClassTypeConst["algo_random_first"] = 10002] = "algo_random_first";
    /**底板-固定块池随机 */
    AlgoClassTypeConst[AlgoClassTypeConst["algo_random_fix_block"] = 10003] = "algo_random_fix_block";
    /**底板-真随机 */
    AlgoClassTypeConst[AlgoClassTypeConst["algo_real_random"] = 10004] = "algo_real_random";
    /**底板-熵增1 */
    AlgoClassTypeConst[AlgoClassTypeConst["algo_entropy_increase1"] = 11001] = "algo_entropy_increase1";
    /**底板-熵增3 */
    AlgoClassTypeConst[AlgoClassTypeConst["algo_entropy_increase3"] = 12001] = "algo_entropy_increase3";
    /**子母熵增3 */
    AlgoClassTypeConst[AlgoClassTypeConst["algo_entropy_increase3_zimu"] = 12002] = "algo_entropy_increase3_zimu";
    /**底板-简单难题 */
    AlgoClassTypeConst[AlgoClassTypeConst["algo_simple_hard"] = 13001] = "algo_simple_hard";
    /**底板-填空消除，边数不考虑消除 */
    AlgoClassTypeConst[AlgoClassTypeConst["algo_fill_in_the_blank"] = 14001] = "algo_fill_in_the_blank";
    /**填空消除，边数考虑消除 */
    AlgoClassTypeConst[AlgoClassTypeConst["algo_fill_in_the_blank_clear"] = 14002] = "algo_fill_in_the_blank_clear";
    /**全组合填空消除9 */
    AlgoClassTypeConst[AlgoClassTypeConst["algo_fill_in_the_blank_id_9"] = 14003] = "algo_fill_in_the_blank_id_9";
    /**全组合填空消除21 */
    AlgoClassTypeConst[AlgoClassTypeConst["algo_fill_in_the_blank_id_21"] = 14004] = "algo_fill_in_the_blank_id_21";
    /**填空消除位置连贯（填空消除连贯） 不考虑消除*/
    AlgoClassTypeConst[AlgoClassTypeConst["algo_fill_in_the_blank_by_pos"] = 14005] = "algo_fill_in_the_blank_by_pos";
    /**填空消除位置连贯（填空消除连贯） 考虑消除*/
    AlgoClassTypeConst[AlgoClassTypeConst["algo_fill_in_the_blank_clear_by_pos"] = 14006] = "algo_fill_in_the_blank_clear_by_pos";
    /**旅行填空消除*/
    AlgoClassTypeConst[AlgoClassTypeConst["algo_fill_in_the_blank_journey"] = 14007] = "algo_fill_in_the_blank_journey";
    /**高效消除*/
    AlgoClassTypeConst[AlgoClassTypeConst["algo_fill_in_the_blank_high_effect"] = 14008] = "algo_fill_in_the_blank_high_effect";
    /**全组合填空消除70 */
    AlgoClassTypeConst[AlgoClassTypeConst["algo_fill_in_the_blank_id_70"] = 14009] = "algo_fill_in_the_blank_id_70";
    /**消除爽 */
    AlgoClassTypeConst[AlgoClassTypeConst["algo_fill_in_the_blank_clear_cool"] = 14010] = "algo_fill_in_the_blank_clear_cool";
    /**连贯旅行填空消除 */
    AlgoClassTypeConst[AlgoClassTypeConst["algo_fill_in_the_blank_journey_by_pos"] = 14011] = "algo_fill_in_the_blank_journey_by_pos";
    /**空洞消除算法*/
    AlgoClassTypeConst[AlgoClassTypeConst["algo_fill_in_the_hole"] = 14012] = "algo_fill_in_the_hole";
    /**快速填空消除算法*/
    AlgoClassTypeConst[AlgoClassTypeConst["algo_quick_fill"] = 14013] = "algo_quick_fill";
    /**方块之战，冒险模式用*/
    AlgoClassTypeConst[AlgoClassTypeConst["algo_fill_in_the_blank_adv"] = 14015] = "algo_fill_in_the_blank_adv";
    /**底板-熵减 */
    AlgoClassTypeConst[AlgoClassTypeConst["algo_entropy_reduction"] = 15001] = "algo_entropy_reduction";
    /**底板-困难难题*/
    AlgoClassTypeConst[AlgoClassTypeConst["algo_hard"] = 16001] = "algo_hard";
    /**简单直觉难题*/
    AlgoClassTypeConst[AlgoClassTypeConst["algo_simple_instinct_hard"] = 16002] = "algo_simple_instinct_hard";
    /**简单死亡*/
    AlgoClassTypeConst[AlgoClassTypeConst["algo_simple_death"] = 16003] = "algo_simple_death";
    /**底板-高回报困难难题*/
    AlgoClassTypeConst[AlgoClassTypeConst["algo_hard_high_effect"] = 16004] = "algo_hard_high_effect";
    /**更困难难题*/
    AlgoClassTypeConst[AlgoClassTypeConst["algo_hard_hard"] = 16005] = "algo_hard_hard";
    /**十字解困难难题*/
    AlgoClassTypeConst[AlgoClassTypeConst["algo_hard_cross"] = 16006] = "algo_hard_cross";
    /**困难难题扩展1*/
    AlgoClassTypeConst[AlgoClassTypeConst["algo_hard_extend1"] = 16007] = "algo_hard_extend1";
    /**困难难题扩展2*/
    AlgoClassTypeConst[AlgoClassTypeConst["algo_hard_extend2"] = 16008] = "algo_hard_extend2";
    /**困难难题二进制*/
    AlgoClassTypeConst[AlgoClassTypeConst["algo_hard_binary"] = 16009] = "algo_hard_binary";
    /**底板-随机无死亡 */
    AlgoClassTypeConst[AlgoClassTypeConst["algo_random_no_death"] = 17001] = "algo_random_no_death";
    /**底板-直觉难题 */
    AlgoClassTypeConst[AlgoClassTypeConst["algo_instinct_hard"] = 18001] = "algo_instinct_hard";
    /**底板-致死题 */
    AlgoClassTypeConst[AlgoClassTypeConst["algo_death"] = 19001] = "algo_death";
    /**复活算法 */
    AlgoClassTypeConst[AlgoClassTypeConst["algo_revive"] = 20001] = "algo_revive";
    /**复活算法变种，选择可消除边数小的块 */
    AlgoClassTypeConst[AlgoClassTypeConst["algo_revive_clear"] = 20002] = "algo_revive_clear";
    /**清屏算法 */
    AlgoClassTypeConst[AlgoClassTypeConst["algo_clear_board"] = 21001] = "algo_clear_board";
    /**终止刷分算法 */
    AlgoClassTypeConst[AlgoClassTypeConst["algo_stop_refresh_record"] = 22001] = "algo_stop_refresh_record";
    /**连续两轮出块相似处理算法 */
    AlgoClassTypeConst[AlgoClassTypeConst["algo_same_more_block"] = 23001] = "algo_same_more_block";
})(AlgoClassTypeConst || (AlgoClassTypeConst = {}));
/**算法类型枚举 */
var AlgoClassTypeConstName = (_a = {},
    /**===========================类型枚举============================== */
    /** 随机 */
    _a[AlgoClassTypeConst.AlgoRandom] = '随机',
    /** 熵增1 */
    _a[AlgoClassTypeConst.AlgoEntropyIncrease1] = '熵增1',
    /** 熵增3 */
    _a[AlgoClassTypeConst.AlgoEntropyIncrease3] = '熵增3',
    /** 简单难题 */
    _a[AlgoClassTypeConst.AlgoSimpleHard] = '简单死亡',
    /** 填空消除 */
    _a[AlgoClassTypeConst.AlgoFillInTheBlank] = '填空消除',
    /** 熵减*/
    _a[AlgoClassTypeConst.AlgoEntropyReduction] = '熵减',
    /** 困难难题*/
    _a[AlgoClassTypeConst.AlgoHard] = '死亡难题',
    /** 随机无死题*/
    _a[AlgoClassTypeConst.AlgoRandomNoDeath] = '随机无死亡',
    /** 直觉难题*/
    _a[AlgoClassTypeConst.AlgoInstinctHard] = '直觉难题',
    /** 致死题*/
    _a[AlgoClassTypeConst.AlgoDeath] = '致死题',
    /**===========================实例枚举============================== */
    /**底板-随机 */
    _a[AlgoClassTypeConst.algo_random] = '随机',
    /**底板-首次随机 */
    _a[AlgoClassTypeConst.algo_random_first] = '首次随机',
    /**底板-首次随机 */
    _a[AlgoClassTypeConst.algo_random_fix_block] = '固定块池随机',
    /**底板-熵增1 */
    _a[AlgoClassTypeConst.algo_entropy_increase1] = '熵增1',
    /**底板-熵增3 */
    _a[AlgoClassTypeConst.algo_entropy_increase3] = '熵增3',
    /**子母熵增3 */
    _a[AlgoClassTypeConst.algo_entropy_increase3_zimu] = '子母熵增3',
    /**底板-简单难题 */
    _a[AlgoClassTypeConst.algo_simple_hard] = '简单死亡',
    /**底板-填空消除，边数不考虑消除 */
    _a[AlgoClassTypeConst.algo_fill_in_the_blank] = '填空消除-不考虑消除',
    /**填空消除，边数考虑消除 */
    _a[AlgoClassTypeConst.algo_fill_in_the_blank_clear] = '填空消除-考虑消除',
    /**全组合填空消除9 */
    _a[AlgoClassTypeConst.algo_fill_in_the_blank_id_9] = '全组合填空-9',
    /**全组合填空消除21 */
    _a[AlgoClassTypeConst.algo_fill_in_the_blank_id_21] = '全组合填空-21',
    /**全组合填空消除70 */
    _a[AlgoClassTypeConst.algo_fill_in_the_blank_id_70] = '全组合填空-70',
    /**填空消除位置连贯（填空消除连贯） 不考虑消除*/
    _a[AlgoClassTypeConst.algo_fill_in_the_blank_by_pos] = '连贯填空消除',
    /**填空消除位置连贯（填空消除连贯） 考虑消除*/
    _a[AlgoClassTypeConst.algo_fill_in_the_blank_clear_by_pos] = '连贯填空消除-考虑消除',
    /**旅行填空消除*/
    _a[AlgoClassTypeConst.algo_fill_in_the_blank_journey] = '旅行填空消除',
    _a[AlgoClassTypeConst.algo_fill_in_the_blank_journey_by_pos] = '旅行填空消除',
    /**旅行填空消除*/
    _a[AlgoClassTypeConst.algo_fill_in_the_blank_high_effect] = '高效填空消除',
    /**旅行填空消除*/
    _a[AlgoClassTypeConst.algo_fill_in_the_blank_clear_cool] = '消除爽',
    /**空洞填空消除*/
    _a[AlgoClassTypeConst.algo_fill_in_the_hole] = '空洞填空消除',
    /**快速填空消除算法*/
    _a[AlgoClassTypeConst.algo_quick_fill] = '快速消除',
    /**冒险模式方块之战*/
    _a[AlgoClassTypeConst.algo_fill_in_the_blank_adv] = '方块之战',
    /**底板-熵减 */
    _a[AlgoClassTypeConst.algo_entropy_reduction] = '熵减',
    /**底板-困难难题*/
    _a[AlgoClassTypeConst.algo_hard] = '死亡难题',
    /**底板-高回报困难难题*/
    _a[AlgoClassTypeConst.algo_hard_high_effect] = '高回报困难难题',
    /**更困难难题*/
    _a[AlgoClassTypeConst.algo_hard_hard] = '更困难难题',
    /**十字解困难难题*/
    _a[AlgoClassTypeConst.algo_hard_cross] = '十字解困难难题',
    /**困难难题扩展1*/
    _a[AlgoClassTypeConst.algo_hard_extend1] = '困难难题扩展1',
    /**困难难题扩展2*/
    _a[AlgoClassTypeConst.algo_hard_extend2] = '困难难题扩展2',
    /**简单直觉难题*/
    _a[AlgoClassTypeConst.algo_simple_instinct_hard] = '简单直觉题',
    /**简单难题*/
    _a[AlgoClassTypeConst.algo_simple_death] = '简单难题',
    /**底板-随机无死亡 */
    _a[AlgoClassTypeConst.algo_random_no_death] = '随机无死亡',
    /**底板-直觉难题 */
    _a[AlgoClassTypeConst.algo_instinct_hard] = '直觉难题',
    /**底板-致死题 */
    _a[AlgoClassTypeConst.algo_death] = '致死题',
    /**复活算法 */
    _a[AlgoClassTypeConst.algo_revive] = '复活题',
    /**复活算法 */
    _a[AlgoClassTypeConst.algo_revive_clear] = '复活题',
    /**清屏算法 */
    _a[AlgoClassTypeConst.algo_clear_board] = '清屏plus',
    /**终止刷分算法 */
    _a[AlgoClassTypeConst.algo_stop_refresh_record] = '终止刷分',
    /**预防相似出块算法 */
    _a[AlgoClassTypeConst.algo_same_more_block] = '预防相似出块',
    _a);
/**该枚举是否是算法实例枚举 */
function isAlgoInstance(algoClassTypeConst) {
    return algoClassTypeConst > 10000;
}

/**特性配置预制体 */
var FeatureConfigPrefab = /** @class */ (function (_super) {
    __extends(FeatureConfigPrefab, _super);
    function FeatureConfigPrefab() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    FeatureConfigPrefab.prototype.rewriteConfig = function (puzzleDataConfig) {
    };
    return FeatureConfigPrefab;
}(BaseConfigPrefab));

/**拼图块配置预制体 */
var PuzzleConfigPrefab = /** @class */ (function (_super) {
    __extends(PuzzleConfigPrefab, _super);
    function PuzzleConfigPrefab() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    PuzzleConfigPrefab.prototype.init = function (puzzleType, puzzle, idAdd) {
        this.$idAdd = idAdd;
        this.$puzzle = puzzle;
        this.puzzleType = puzzleType;
    };
    PuzzleConfigPrefab.prototype.initFeatureCompIdArr = function (featureIdList, featureCompIdArr, prefabConfigData) {
        if (!featureIdList) {
            return;
        }
        var puzzle = this.$puzzle;
        for (var i = 0, len = featureIdList.length; i < len; i++) {
            var featureId = featureIdList[i];
            var prefab = puzzle.createConfigPrefab(featureId);
            var prefabData = this.$getPrefabData(prefabConfigData[featureId]);
            prefab.initConfigPrefab(this.$idAdd, prefabData);
            DataStructureUtil.oto(prefab.config.compTvoMap, this.config.compTvoMap);
            featureCompIdArr.push(prefab.config.main);
        }
    };
    PuzzleConfigPrefab.prototype.rewriteConfig = function (puzzleDataConfig) {
        var arr = this.$puzzle.getPuzzlePieceGlobalFeatureDataArr(this.puzzleType);
        for (var i = 0, len = arr.length; i < len; i++) {
            var gfDataArr = arr[i];
            if (gfDataArr && gfDataArr.length > 0) {
                var gf = this.$puzzle.getPuzzlePieceGlobalFeature(gfDataArr[0].type);
                gf.rewriteConfig(puzzleDataConfig, arr[i]);
            }
        }
    };
    /**获取预制体数据 */
    PuzzleConfigPrefab.prototype.$getPrefabData = function (data) {
        var prefabData = data;
        if (Array.isArray(data)) {
            prefabData = data.shift();
        }
        return prefabData;
    };
    return PuzzleConfigPrefab;
}(BaseConfigPrefab));

/**公共工具类型 */
var CommonUtil = /** @class */ (function () {
    function CommonUtil() {
    }
    /**是否有值 */
    CommonUtil.hasValue = function (val) {
        return val !== undefined && val !== null;
    };
    return CommonUtil;
}());

var ShapeInfo = /** @class */ (function () {
    function ShapeInfo() {
    }
    /**生成块数据 */
    ShapeInfo.createConfig = function (info) {
        ShapeInfo.virtualID++;
        var width = info[0].length;
        var height = info.length;
        var shapes = [];
        for (var _i = 0, info_1 = info; _i < info_1.length; _i++) {
            var row = info_1[_i];
            var shape = 0;
            for (var index in row) {
                if (row[index] == 1) {
                    shape += Math.pow(2, width - 1 - Number(index));
                }
            }
            shapes.push(shape);
        }
        var offsetCol = 0, count = 0;
        for (var i in info[0]) {
            if (info[0][i] == 1) {
                break;
            }
            else {
                offsetCol++;
            }
        }
        for (var i in info) {
            for (var j in info[i]) {
                if (info[i][j] > 0) {
                    count++;
                }
            }
        }
        var config = { id: ShapeInfo.virtualID, width: width, height: height, shape: shapes, info: info, offsetCol: offsetCol, count: count, border: ShapeInfo.getBorderNum(info), };
        ShapeInfo.blockInfos.push(config);
        return ShapeInfo.virtualID;
    };
    /**获取方块数据 */
    ShapeInfo.getConfig = function (id) {
        for (var _i = 0, _a = ShapeInfo.blockInfos; _i < _a.length; _i++) {
            var data = _a[_i];
            if (data.id == id) {
                return data;
            }
        }
        return { id: id, width: 1, height: 1, shape: [1], info: [[1]], offsetCol: 0, count: 1, border: 4 };
    };
    ShapeInfo.getBorderNum = function (info) {
        var maxBoardNum = 0;
        for (var i = 0; i < info.length; ++i) {
            for (var j = 0; j < info[i].length; ++j) {
                if (info[i][j] == 1) {
                    maxBoardNum += 4;
                    if (info[i][j + 1] !== undefined && info[i][j + 1] === 1) {
                        maxBoardNum -= 2;
                    }
                    if (info[i + 1] !== undefined && info[i + 1][j] === 1) {
                        maxBoardNum -= 2;
                    }
                }
            }
        }
        return maxBoardNum;
    };
    ShapeInfo.blockInfos = [
        { id: 1, width: 1, height: 1, shape: [1], border: 4, count: 1, info: [[1]], offsetCol: 0, entroy: 0, blockBox: 1, blockBit: [1, 1] },
        { id: 2, width: 1, height: 2, shape: [1, 1], border: 6, count: 2, info: [[1], [1]], offsetCol: 0, entroy: 0, blockBox: 2, blockBit: [1, 2] },
        { id: 3, width: 2, height: 1, shape: [3], border: 6, count: 2, info: [[1, 1]], offsetCol: 0, entroy: 0, blockBox: 2, blockBit: [2, 1] },
        { id: 4, width: 1, height: 3, shape: [1, 1, 1], border: 8, count: 3, info: [[1], [1], [1]], offsetCol: 0, entroy: 0, blockBox: 3, blockBit: [1, 3] },
        { id: 5, width: 3, height: 1, shape: [7], border: 8, count: 3, info: [[1, 1, 1]], offsetCol: 0, entroy: 0, blockBox: 3, blockBit: [3, 1] },
        { id: 6, width: 2, height: 2, shape: [3, 2], border: 8, count: 3, info: [[1, 1], [1, 0]], offsetCol: 0, entroy: 0.4, blockBox: 4, blockBit: [2, 2] },
        { id: 7, width: 1, height: 4, shape: [1, 1, 1, 1], border: 10, count: 4, info: [[1], [1], [1], [1]], offsetCol: 0, entroy: 0, blockBox: 4, blockBit: [1, 4] },
        { id: 8, width: 3, height: 2, shape: [4, 7], border: 10, count: 4, info: [[1, 0, 0], [1, 1, 1]], offsetCol: 0, entroy: 0.8, blockBox: 6, blockBit: [3, 2] },
        { id: 9, width: 2, height: 2, shape: [3, 3], border: 8, count: 4, info: [[1, 1], [1, 1]], offsetCol: 0, entroy: 0, blockBox: 4, blockBit: [2, 2] },
        { id: 10, width: 3, height: 2, shape: [2, 7], border: 10, count: 4, info: [[0, 1, 0], [1, 1, 1]], offsetCol: 1, entroy: 0.8, blockBox: 6, blockBit: [3, 2] },
        { id: 11, width: 5, height: 1, shape: [31], border: 12, count: 5, info: [[1, 1, 1, 1, 1]], offsetCol: 0, entroy: 0, blockBox: 5, blockBit: [5, 1] },
        { id: 12, width: 3, height: 3, shape: [7, 1, 1], border: 12, count: 5, info: [[1, 1, 1], [0, 0, 1], [0, 0, 1]], offsetCol: 0, entroy: 1.3, blockBox: 9, blockBit: [3, 3] },
        { id: 13, width: 3, height: 3, shape: [7, 7, 7], border: 12, count: 9, info: [[1, 1, 1], [1, 1, 1], [1, 1, 1]], offsetCol: 0, entroy: 0, blockBox: 9, blockBit: [3, 3] },
        { id: 14, width: 3, height: 2, shape: [3, 6], border: 10, count: 4, info: [[0, 1, 1], [1, 1, 0]], offsetCol: 1, entroy: 0.8, blockBox: 6, blockBit: [3, 2] },
        { id: 15, width: 2, height: 2, shape: [3, 1], border: 8, count: 3, info: [[1, 1], [0, 1]], offsetCol: 0, entroy: 0.4, blockBox: 4, blockBit: [2, 2] },
        { id: 16, width: 2, height: 3, shape: [2, 3, 1], border: 10, count: 4, info: [[1, 0], [1, 1], [0, 1]], offsetCol: 0, entroy: 0.8, blockBox: 6, blockBit: [2, 3] },
        { id: 17, width: 4, height: 1, shape: [15], border: 10, count: 4, info: [[1, 1, 1, 1]], offsetCol: 0, entroy: 0, blockBox: 4, blockBit: [4, 1] },
        { id: 18, width: 3, height: 2, shape: [6, 3], border: 10, count: 4, info: [[1, 1, 0], [0, 1, 1]], offsetCol: 0, entroy: 0.8, blockBox: 6, blockBit: [3, 2] },
        { id: 19, width: 2, height: 3, shape: [1, 3, 2], border: 10, count: 4, info: [[0, 1], [1, 1], [1, 0]], offsetCol: 1, entroy: 0.8, blockBox: 6, blockBit: [2, 3] },
        { id: 20, width: 2, height: 3, shape: [2, 3, 2], border: 10, count: 4, info: [[1, 0], [1, 1], [1, 0]], offsetCol: 0, entroy: 0.8, blockBox: 6, blockBit: [2, 3] },
        { id: 21, width: 3, height: 3, shape: [7, 4, 4], border: 12, count: 5, info: [[1, 1, 1], [1, 0, 0], [1, 0, 0]], offsetCol: 0, entroy: 1.3, blockBox: 9, blockBit: [3, 3] },
        { id: 22, width: 1, height: 5, shape: [1, 1, 1, 1, 1], border: 12, count: 5, info: [[1], [1], [1], [1], [1]], offsetCol: 0, entroy: 0, blockBox: 5, blockBit: [1, 5] },
        { id: 23, width: 3, height: 3, shape: [4, 4, 7], border: 12, count: 5, info: [[1, 0, 0], [1, 0, 0], [1, 1, 1]], offsetCol: 0, entroy: 1.3, blockBox: 9, blockBit: [3, 3] },
        { id: 24, width: 3, height: 3, shape: [1, 1, 7], border: 12, count: 5, info: [[0, 0, 1], [0, 0, 1], [1, 1, 1]], offsetCol: 2, entroy: 1.3, blockBox: 9, blockBit: [3, 3] },
        { id: 25, width: 2, height: 3, shape: [1, 3, 1], border: 10, count: 4, info: [[0, 1], [1, 1], [0, 1]], offsetCol: 1, entroy: 0.8, blockBox: 6, blockBit: [2, 3] },
        { id: 26, width: 3, height: 2, shape: [7, 2], border: 10, count: 4, info: [[1, 1, 1], [0, 1, 0]], offsetCol: 0, entroy: 0.8, blockBox: 6, blockBit: [3, 2] },
        { id: 27, width: 2, height: 2, shape: [2, 3], border: 8, count: 3, info: [[1, 0], [1, 1]], offsetCol: 0, entroy: 0.4, blockBox: 4, blockBit: [2, 2] },
        { id: 28, width: 2, height: 2, shape: [1, 3], border: 8, count: 3, info: [[0, 1], [1, 1]], offsetCol: 1, entroy: 0.4, blockBox: 4, blockBit: [2, 2] },
        { id: 29, width: 2, height: 3, shape: [1, 1, 3], border: 10, count: 4, info: [[0, 1], [0, 1], [1, 1]], offsetCol: 1, entroy: 0.8, blockBox: 6, blockBit: [2, 3] },
        { id: 30, width: 3, height: 2, shape: [7, 1], border: 10, count: 4, info: [[1, 1, 1], [0, 0, 1]], offsetCol: 0, entroy: 0.8, blockBox: 6, blockBit: [3, 2] },
        { id: 31, width: 2, height: 3, shape: [3, 2, 2], border: 10, count: 4, info: [[1, 1], [1, 0], [1, 0]], offsetCol: 0, entroy: 0.8, blockBox: 6, blockBit: [2, 3] },
        { id: 32, width: 2, height: 3, shape: [3, 1, 1], border: 10, count: 4, info: [[1, 1], [0, 1], [0, 1]], offsetCol: 0, entroy: 0.8, blockBox: 6, blockBit: [2, 3] },
        { id: 33, width: 3, height: 2, shape: [1, 7], border: 10, count: 4, info: [[0, 0, 1], [1, 1, 1]], offsetCol: 2, entroy: 0.8, blockBox: 6, blockBit: [3, 2] },
        { id: 34, width: 3, height: 2, shape: [7, 4], border: 10, count: 4, info: [[1, 1, 1], [1, 0, 0]], offsetCol: 0, entroy: 0.8, blockBox: 6, blockBit: [3, 2] },
        { id: 35, width: 3, height: 2, shape: [7, 7], border: 10, count: 6, info: [[1, 1, 1], [1, 1, 1]], offsetCol: 0, entroy: 0, blockBox: 6, blockBit: [3, 2] },
        { id: 36, width: 2, height: 3, shape: [3, 3, 3], border: 10, count: 6, info: [[1, 1], [1, 1], [1, 1]], offsetCol: 0, entroy: 0, blockBox: 6, blockBit: [2, 3] },
        { id: 37, width: 2, height: 2, shape: [2, 1], border: 8, count: 2, info: [[1, 0], [0, 1]], offsetCol: 0, entroy: 0.6, blockBox: 4, blockBit: [2, 2] },
        { id: 38, width: 2, height: 2, shape: [1, 2], border: 8, count: 2, info: [[0, 1], [1, 0]], offsetCol: 1, entroy: 0.6, blockBox: 4, blockBit: [2, 2] },
        { id: 39, width: 3, height: 3, shape: [4, 2, 1], border: 12, count: 3, info: [[1, 0, 0], [0, 1, 0], [0, 0, 1]], offsetCol: 0, entroy: 1.2, blockBox: 9, blockBit: [3, 3] },
        { id: 40, width: 3, height: 3, shape: [1, 2, 4], border: 12, count: 3, info: [[0, 0, 1], [0, 1, 0], [1, 0, 0]], offsetCol: 2, entroy: 1.2, blockBox: 9, blockBit: [3, 3] },
        { id: 41, width: 3, height: 3, shape: [1, 2, 4], border: 12, count: 3, info: [[0, 0, 1], [0, 1, 0], [1, 0, 0]], offsetCol: 2, entroy: 1.2, blockBox: 9, blockBit: [3, 3] },
        { id: 42, width: 2, height: 3, shape: [2, 2, 3], border: 10, count: 4, info: [[1, 0], [1, 0], [1, 1]], offsetCol: 0, entroy: 0.8, blockBox: 6, blockBit: [2, 3] },
        { id: 43, width: 3, height: 3, shape: [1, 7, 1], border: 12, count: 5, info: [[0, 0, 1], [1, 1, 1], [0, 0, 1]], offsetCol: 2, entroy: 0, blockBox: 0, blockBit: [] },
        { id: 44, width: 3, height: 2, shape: [5, 7], border: 12, count: 5, info: [[1, 0, 1], [1, 1, 1]], offsetCol: 0, entroy: 0, blockBox: 0, blockBit: [] },
        { id: 45, width: 2, height: 3, shape: [3, 2, 3], border: 12, count: 5, info: [[1, 1], [1, 0], [1, 1]], offsetCol: 0, entroy: 0, blockBox: 0, blockBit: [] },
        { id: 46, width: 3, height: 2, shape: [7, 5], border: 12, count: 5, info: [[1, 1, 1], [1, 0, 1]], offsetCol: 0, entroy: 0, blockBox: 0, blockBit: [] },
        { id: 47, width: 2, height: 3, shape: [3, 1, 3], border: 12, count: 5, info: [[1, 1], [0, 1], [1, 1]], offsetCol: 0, entroy: 0, blockBox: 0, blockBit: [] },
        { id: 48, width: 3, height: 3, shape: [2, 2, 7], border: 12, count: 5, info: [[0, 1, 0], [0, 1, 0], [1, 1, 1]], offsetCol: 1, entroy: 0, blockBox: 0, blockBit: [] },
        { id: 49, width: 3, height: 3, shape: [4, 7, 4], border: 12, count: 5, info: [[1, 0, 0], [1, 1, 1], [1, 0, 0]], offsetCol: 0, entroy: 0, blockBox: 0, blockBit: [] },
        { id: 50, width: 3, height: 3, shape: [7, 2, 2], border: 12, count: 5, info: [[1, 1, 1], [0, 1, 0], [0, 1, 0]], offsetCol: 0, entroy: 0, blockBox: 0, blockBit: [] },
        { id: 51, width: 3, height: 3, shape: [2, 7, 2], border: 12, count: 5, info: [[0, 1, 0], [1, 1, 1], [0, 1, 0]], offsetCol: 1, entroy: 0, blockBox: 0, blockBit: [] },
        { id: 101, width: 5, height: 2, shape: [31, 31], border: 14, count: 10, info: [[1, 1, 1, 1, 1], [1, 1, 1, 1, 1]], offsetCol: 0, entroy: 0, blockBox: 0, blockBit: [] },
        { id: 102, width: 2, height: 5, shape: [3, 3, 3, 3, 3], border: 14, count: 10, info: [[1, 1], [1, 1], [1, 1], [1, 1], [1, 1]], offsetCol: 0, entroy: 0, blockBox: 0, blockBit: [] },
        { id: 103, width: 4, height: 2, shape: [15, 15], border: 12, count: 8, info: [[1, 1, 1, 1], [1, 1, 1, 1]], offsetCol: 0, entroy: 0, blockBox: 0, blockBit: [] },
        { id: 104, width: 2, height: 4, shape: [3, 3, 3, 3], border: 12, count: 8, info: [[1, 1], [1, 1], [1, 1], [1, 1]], offsetCol: 0, entroy: 0, blockBox: 0, blockBit: [] },
        { id: 105, width: 5, height: 3, shape: [31, 31, 31], border: 16, count: 15, info: [[1, 1, 1, 1, 1], [1, 1, 1, 1, 1], [1, 1, 1, 1, 1]], offsetCol: 0, entroy: 0, blockBox: 0, blockBit: [] },
        { id: 106, width: 3, height: 5, shape: [7, 7, 7, 7, 7], border: 16, count: 15, info: [[1, 1, 1], [1, 1, 1], [1, 1, 1], [1, 1, 1], [1, 1, 1]], offsetCol: 0, entroy: 0, blockBox: 0, blockBit: [] },
        { id: 107, width: 4, height: 3, shape: [15, 15, 15], border: 14, count: 12, info: [[1, 1, 1, 1], [1, 1, 1, 1], [1, 1, 1, 1]], offsetCol: 0, entroy: 0, blockBox: 0, blockBit: [] },
        { id: 108, width: 3, height: 4, shape: [7, 7, 7, 7], border: 14, count: 12, info: [[1, 1, 1], [1, 1, 1], [1, 1, 1], [1, 1, 1]], offsetCol: 0, entroy: 0, blockBox: 0, blockBit: [] },
        { id: 109, width: 5, height: 4, shape: [31, 31, 31, 31], border: 18, count: 20, info: [[1, 1, 1, 1, 1], [1, 1, 1, 1, 1], [1, 1, 1, 1, 1], [1, 1, 1, 1, 1]], offsetCol: 0, entroy: 0, blockBox: 0, blockBit: [] },
        { id: 110, width: 4, height: 5, shape: [15, 15, 15, 15, 15], border: 18, count: 20, info: [[1, 1, 1, 1], [1, 1, 1, 1], [1, 1, 1, 1], [1, 1, 1, 1], [1, 1, 1, 1]], offsetCol: 0, entroy: 0, blockBox: 0, blockBit: [] },
        { id: 111, width: 5, height: 5, shape: [31, 31, 31, 31, 31], border: 20, count: 25, info: [[1, 1, 1, 1, 1], [1, 1, 1, 1, 1], [1, 1, 1, 1, 1], [1, 1, 1, 1, 1], [1, 1, 1, 1, 1]], offsetCol: 0, entroy: 0, blockBox: 0, blockBit: [] }
    ];
    /**虚拟块ID起始 */
    ShapeInfo.virtualID = 10000; //虚拟快ID起始
    return ShapeInfo;
}());

function shuffle(array) {
    var currentIndex = array.length;
    var temporaryValue;
    var randomIndex;
    while (currentIndex !== 0) {
        randomIndex = Math.floor(Math.random() * currentIndex);
        currentIndex -= 1;
        temporaryValue = array[currentIndex];
        array[currentIndex] = array[randomIndex];
        array[randomIndex] = temporaryValue;
    }
    return array;
}
var ClearType;
(function (ClearType) {
    ClearType[ClearType["NONE"] = 0] = "NONE";
    ClearType[ClearType["ROW"] = 1] = "ROW";
    ClearType[ClearType["COL"] = 2] = "COL";
    ClearType[ClearType["CROSS"] = 3] = "CROSS";
})(ClearType || (ClearType = {}));
var BinaryBoard = /** @class */ (function () {
    function BinaryBoard() {
        this.rowCount = 8;
        this.colCount = 8;
        this.leftSide = 1 << (this.rowCount - 1);
        this.rightSide = 1;
        this.topSide = 1 << (this.colCount - 1);
        this.bottomSide = 1;
        this.bFull = 0;
        this.rowBinary = [0, 0, 0, 0, 0, 0, 0, 0];
        this._record = [];
    }
    BinaryBoard.prototype.getBlockBoardRC = function (id, leftTop) {
        var _this = this;
        var result = [];
        var config = ShapeInfo.getConfig(id);
        var target = config.shape.map(function (v) { return v << (_this.colCount - config.width - leftTop.x); });
        for (var i = 0; i < config.height; i++) {
            for (var j = 0; j < this.colCount; j++) {
                if (((target[i] >> j) & 1) === 1) {
                    result.push({ x: i + leftTop.y, y: this.colCount - j - 1 });
                }
            }
        }
        return result;
    };
    BinaryBoard.prototype.emptyAt = function (x, y) {
        return (this.rowBinary[y] & (1 << (this.colCount - x - 1))) === 0;
    };
    BinaryBoard.prototype.getCanPutIds = function (ids) {
        var ret = [];
        for (var _i = 0, ids_1 = ids; _i < ids_1.length; _i++) {
            var id = ids_1[_i];
            if (this.canPut(id)) {
                ret.push(id);
            }
        }
        return ret;
    };
    BinaryBoard.prototype.getCanPutPoss = function (id) {
        var _this = this;
        var result = [];
        var config = ShapeInfo.getConfig(id);
        if (!config) {
            return result;
        }
        var maxRow = this.colCount - config.width + 1;
        var maxCol = this.rowBinary.length - config.height + 1;
        var canPutShape = function (x, y, shape) {
            var offsetX = _this.colCount - shape.width - x;
            for (var i = 0; i < shape.height; i++) {
                if ((_this.rowBinary[i + y] & (shape.shape[i] << offsetX)) > 0)
                    return false;
            }
            return true;
        };
        for (var i = 0; i < maxRow; i++) {
            for (var j = 0; j < maxCol; j++) {
                if (canPutShape(i, j, config)) {
                    result.push({ x: i, y: j });
                }
            }
        }
        return result;
    };
    //可放位置必选在贴边率控制范围内
    BinaryBoard.prototype.getCanPutPossBetweenNear = function (id, minNear, maxNear) {
        var _this = this;
        var result = [];
        var config = ShapeInfo.getConfig(id);
        if (!config) {
            return result;
        }
        var maxRow = this.colCount - config.width + 1;
        var maxCol = this.rowBinary.length - config.height + 1;
        var canPutShape = function (x, y, shape) {
            var offsetX = _this.colCount - shape.width - x;
            for (var i = 0; i < shape.height; i++) {
                if ((_this.rowBinary[i + y] & (shape.shape[i] << offsetX)) > 0)
                    return false;
            }
            return true;
        };
        var curNear;
        for (var i = 0; i < maxRow; i++) {
            for (var j = 0; j < maxCol; j++) {
                if (canPutShape(i, j, config)) {
                    curNear = this.getNearBoarderPer(id, { x: i, y: j });
                    if (curNear >= minNear && curNear <= maxNear) {
                        result.push({ x: i, y: j });
                    }
                }
            }
        }
        return result;
    };
    /**
     * 14 w:3 h:2 shape:3、6
     * 0011
     * 0110
     * 39 w:3 h:3 shape:4、2、1
     * [0,1,0,0],
     * [0,0,1,0],
     * [0,0,0,1],
     */
    BinaryBoard.prototype.getCanPutArr = function (arr, pTag) {
        var _this = this;
        if (pTag === void 0) { pTag = -1; }
        var result = [];
        if (arr.length == 0 || arr[0].length == 0) {
            return result;
        }
        var colNum = -1;
        var tmpShape = [];
        var _loop_1 = function (colArr) {
            colNum = colNum == -1 ? colArr.length : colNum;
            if (colNum != colArr.length) {
                return { value: result };
            }
            colArr.forEach(function (val, idx) {
                colArr[idx] = val == pTag ? 0 : 1;
            });
            var binstr = colArr.join('').replace(new RegExp(",", 'g'), '');
            var tmpNum = parseInt(binstr, 2);
            tmpShape.push(tmpNum);
        };
        for (var _i = 0, arr_1 = arr; _i < arr_1.length; _i++) {
            var colArr = arr_1[_i];
            var state_1 = _loop_1(colArr);
            if (typeof state_1 === "object")
                return state_1.value;
        }
        var config = {
            id: 0,
            width: arr[0].length,
            height: arr.length,
            shape: tmpShape,
            info: [],
            count: 0,
            border: 0,
            offsetCol: 0
        };
        if (!config) {
            return result;
        }
        var maxRow = this.colCount - config.width + 1;
        var maxCol = this.rowBinary.length - config.height + 1;
        var canPutShape = function (x, y, shape) {
            var offsetX = _this.colCount - shape.width - x;
            for (var i = 0; i < shape.height; i++) {
                if ((_this.rowBinary[i + y] & (shape.shape[i] << offsetX)) > 0)
                    return false;
            }
            return true;
        };
        for (var i = 0; i < maxRow; i++) {
            for (var j = 0; j < maxCol; j++) {
                if (canPutShape(i, j, config)) {
                    result.push({ x: i, y: j });
                }
            }
        }
        return result;
    };
    BinaryBoard.prototype.canPutDontClear = function (id) {
        var poses = this.getCanPutPoss(id);
        var record = this.record();
        for (var _i = 0, poses_1 = poses; _i < poses_1.length; _i++) {
            var pos = poses_1[_i];
            this.reset(record);
            this.putBlock(id, pos);
            if (this.canClearBlockArr(false)) {
                this.reset(record);
                return false;
            }
        }
        this.reset(record);
        return poses.length > 0;
    };
    BinaryBoard.prototype.canPut = function (id) {
        var _this = this;
        var config = ShapeInfo.getConfig(id);
        var maxRow = this.colCount - config.width + 1;
        var maxCol = this.rowBinary.length - config.height + 1;
        var canPutShape = function (x, y, shape) {
            var offsetX = _this.colCount - shape.width - x;
            for (var i = 0; i < shape.height; i++) {
                if ((_this.rowBinary[i + y] & (shape.shape[i] << offsetX)) > 0)
                    return false;
            }
            return true;
        };
        for (var i = 0; i < maxRow; i++) {
            for (var j = 0; j < maxCol; j++) {
                if (canPutShape(i, j, config)) {
                    return true;
                }
            }
        }
        return false;
    };
    BinaryBoard.prototype.canClear = function (id) {
        var record = this.record();
        var poses = this.getCanPutPoss(id);
        for (var _i = 0, poses_2 = poses; _i < poses_2.length; _i++) {
            var pos = poses_2[_i];
            this.reset(record);
            this.putBlock(id, pos);
            if (this.getClearCount() > 0) {
                this.reset(record);
                return { clear: true, pos: pos };
            }
        }
        this.reset(record);
        return { clear: false };
    };
    BinaryBoard.prototype.putBlock = function (id, pos) {
        var config = ShapeInfo.getConfig(id);
        var offsetX = this.colCount - config.width - pos.x;
        for (var i = 0; i < config.height; i++) {
            this.rowBinary[i + pos.y] |= config.shape[i] << offsetX;
        }
    };
    BinaryBoard.prototype.removeBlock = function (id, pos) {
        var config = ShapeInfo.getConfig(id);
        var offsetX = this.colCount - config.width - pos.x;
        for (var i = 0; i < config.height; i++) {
            this.rowBinary[i + pos.y] &= ~(config.shape[i] << offsetX);
        }
    };
    BinaryBoard.prototype.canPutBlock = function (id, pos) {
        var config = ShapeInfo.getConfig(id);
        var offsetX = this.colCount - config.width - pos.x;
        for (var i = 0; i < config.height; i++) {
            if ((this.rowBinary[i + pos.y] & (config.shape[i] << offsetX)) > 0) {
                return false;
            }
        }
        return true;
    };
    //修复canPutBlock边界未处理问题
    BinaryBoard.prototype.canPutBlockNew = function (id, pos) {
        var config = ShapeInfo.getConfig(id);
        if (pos.x < 0 || pos.y < 0 || pos.y + config.height - 1 > this.rowCount - 1 || pos.x + config.width - 1 > this.colCount - 1) {
            return false;
        }
        return this.canPutBlock(id, pos);
    };
    /**
     * 检查是否可以将指定形状的块放置在指定位置
     * @param id 块的ID
     * @param pos 块的位置
     * @returns 如果可以放置，返回true；否则返回false
     */
    BinaryBoard.prototype.canPutBlockWithShap = function (id, pos) {
        var config = ShapeInfo.getConfig(id);
        if (pos.x + config.width > this.colCount || pos.y + config.height > this.rowCount) {
            return false;
        }
        var offsetX = this.colCount - config.width - pos.x;
        for (var i = 0; i < config.height; i++) {
            if ((this.rowBinary[i + pos.y] & (config.shape[i] << offsetX)) > 0) {
                return false;
            }
        }
        return true;
    };
    BinaryBoard.prototype.clone = function () {
        var result = new BinaryBoard();
        result.rowBinary = this.rowBinary.concat();
        result.leftSide = this.leftSide;
        result.rightSide = this.rightSide;
        result.topSide = this.topSide;
        result.bottomSide = this.bottomSide;
        result.bFull = this.bFull;
        return result;
    };
    BinaryBoard.prototype.convertToBinaryBoard = function (data) {
        this.bFull = 0;
        this.rowCount = data.length;
        this.colCount = data[0].length;
        this.leftSide = 1 << (this.rowCount - 1);
        this.topSide = 1 << (this.colCount - 1);
        this.rowBinary = [];
        for (var i = 0; i < data.length; i++) {
            this.rowBinary[i] = this.listToBinary(data[i]);
        }
        this.bFull = Math.pow(2, this.colCount) - 1;
    };
    BinaryBoard.prototype.convertToArr = function () {
        var ret = [];
        for (var _i = 0, _a = this.rowBinary; _i < _a.length; _i++) {
            var num = _a[_i];
            ret.push(this.binaryToList(num));
        }
        return ret;
    };
    /**
     * 将二进制盘面转为十进制数组
     * @returns
     */
    BinaryBoard.prototype.convertToDecimalBoard = function () {
        var result = [];
        for (var i = 0; i < this.rowBinary.length; i++) {
            var bs = this.rowBinary[i].toString(2).padStart(8, '0');
            var arr = Array.from(bs).map(function (bit) { return parseInt(bit, 10); });
            result.push(arr);
        }
        return result;
    };
    BinaryBoard.prototype.canClearBlockArr = function (isClear) {
        var _this = this;
        if (isClear === void 0) { isClear = false; }
        var result = false;
        var clearRow = [];
        var clearCol = [];
        var bAllCol = this.bFull;
        this.rowBinary.forEach(function (n, i) {
            if (n === _this.bFull) {
                result = true;
                clearRow.push(i);
            }
            bAllCol &= n;
        });
        var i = this.colCount - 1;
        while (bAllCol > 0) {
            if (bAllCol & 1) {
                result = true;
                clearCol.push(i);
            }
            bAllCol >>= 1;
            i -= 1;
        }
        if (isClear) {
            clearRow.forEach(function (row) {
                _this.rowBinary[row] = 0;
                // this.clearRow(row);
            });
            clearCol.forEach(function (col) {
                _this.rowBinary.forEach(function (n, i) {
                    _this.rowBinary[i] = _this.setBitToZero(n, col);
                });
            });
        }
        return result;
    };
    // 获取消除类型
    BinaryBoard.prototype.getClearType = function () {
        var _this = this;
        var clearRow = [];
        var clearCol = [];
        var bAllCol = this.bFull;
        this.rowBinary.forEach(function (n, i) {
            if (n === _this.bFull) {
                clearRow.push(i);
            }
            bAllCol &= n;
        });
        var i = this.colCount - 1;
        while (bAllCol > 0) {
            if (bAllCol & 1) {
                clearCol.push(i);
            }
            bAllCol >>= 1;
            i -= 1;
        }
        var clearType = ClearType.NONE;
        if (clearRow.length > 0) {
            clearType |= ClearType.ROW;
        }
        if (clearCol.length > 0) {
            clearType |= ClearType.COL;
        }
        return clearType;
    };
    /**
     * 获取在指定位置的消除类型
     * @param id 块id
     * @param pos 位置
     * @returns
     */
    BinaryBoard.prototype.getClearTypeInPos = function (id, pos) {
        var _this = this;
        var config = ShapeInfo.getConfig(id);
        var clearRow = [];
        var clearCol = [];
        var bAllCol = this.bFull;
        this.rowBinary.forEach(function (n, i) {
            if (n === _this.bFull && pos.y <= i && pos.y + config.height - 1 >= i) {
                clearRow.push(i);
            }
            bAllCol &= n;
        });
        var i = this.colCount - 1;
        while (bAllCol > 0) {
            if (bAllCol & 1 && pos.x <= i && pos.x + config.width - 1 >= i) {
                clearCol.push(i);
            }
            bAllCol >>= 1;
            i -= 1;
        }
        var clearType = ClearType.NONE;
        if (clearRow.length > 0) {
            clearType |= ClearType.ROW;
        }
        if (clearCol.length > 0) {
            clearType |= ClearType.COL;
        }
        return clearType;
    };
    BinaryBoard.prototype.getClearCount = function () {
        var _this = this;
        var result = 0;
        var bAllCol = this.bFull;
        this.rowBinary.forEach(function (n, i) {
            if ((n ^ _this.bFull) === 0) {
                result += 1;
            }
            bAllCol &= n;
        });
        this.colCount - 1;
        while (bAllCol > 0) {
            if (bAllCol & 1) {
                result += 1;
            }
            bAllCol >>= 1;
        }
        return result;
    };
    BinaryBoard.prototype.clearRow = function (row) {
        this.rowBinary[row] = 0;
    };
    BinaryBoard.prototype.clearCol = function (col) {
        var _this = this;
        this.rowBinary.forEach(function (n, i) {
            _this.rowBinary[i] = _this.setBitToZero(n, col);
        });
    };
    BinaryBoard.prototype.setBitToZero = function (binary, n) {
        var maxCount = Math.max(this.rowCount, this.colCount);
        var bFull = Math.pow(2, maxCount) - 1;
        var mask = bFull - (1 << (maxCount - n - 1));
        var result = binary & mask;
        return result;
    };
    BinaryBoard.prototype.listToBinary = function (list) {
        var binary = 0;
        for (var i = 0; i < list.length; i++) {
            if (list[i] !== -1) {
                binary = (binary << 1) | 1;
            }
            else {
                binary <<= 1;
            }
        }
        return binary;
    };
    BinaryBoard.prototype.binaryToList = function (binary) {
        var list = [];
        var nums = [];
        for (var col = this.colCount - 1; col >= 0; col--) {
            nums.push(Math.pow(2, col));
        }
        for (var index = 0; index < nums.length; index++) {
            if ((binary & nums[index]) > 0) {
                list[index] = 1;
            }
            else {
                list[index] = -1;
            }
        }
        return list;
    };
    BinaryBoard.prototype.splitBlocks = function (blockIds) {
        var ret = { all: [], noPut: [], put: [], clear: [], canPut: [] };
        for (var _i = 0, blockIds_1 = blockIds; _i < blockIds_1.length; _i++) {
            var id = blockIds_1[_i];
            ret.all.push(id);
            if (this.canClear(id).clear) {
                ret.clear.push(id);
                ret.canPut.push(id);
            }
            else if (this.canPut(id)) {
                ret.put.push(id);
                ret.canPut.push(id);
            }
            else {
                ret.noPut.push(id);
            }
        }
        return ret;
    };
    BinaryBoard.prototype.getEdgeGameNum = function (isBorderCount) {
        var _this = this;
        if (isBorderCount === void 0) { isBorderCount = false; }
        var count = 0;
        var colBinary = [];
        this.rowBinary.forEach(function (n, i) {
            count += _this.getEdgeBinary(n, isBorderCount);
            colBinary[i] = 0;
            for (var j = 0; j < _this.colCount; j++) {
                var shiftN = _this.rowBinary[j] >> (_this.rowCount - i - 1);
                colBinary[i] += (shiftN & 1) << j;
            }
        });
        colBinary.forEach(function (n) {
            count += _this.getEdgeBinary(n, isBorderCount);
        });
        return count;
    };
    /**
     * 计算并返回指定列的二进制表示。
     * @param columnIdx 列的索引，从0开始。
     * @returns 指定列的二进制表示。
     */
    BinaryBoard.prototype.calculateColumnBinary = function (columnIdx) {
        var columnBinary = 0;
        for (var rowIndex = 0; rowIndex < this.rowBinary.length; rowIndex++) {
            // 从每一行的二进制表示中提取相应列的位
            // 注意：这里我们假设每一行的二进制表示都是顶对齐的，即最左边的位对应列索引0
            var columnBit = (this.rowBinary[rowIndex] >>> columnIdx) & 1;
            // 将提取的位添加到列的二进制表示中，从最低位开始
            columnBinary |= columnBit << rowIndex;
        }
        return columnBinary;
    };
    /**
     * 获得连续边数
     * @param ori 二进制对象
     * @returns
     */
    BinaryBoard.prototype.getEdgeNumBinary = function () {
        // 生成列的二进制数据
        var colBinary = [];
        for (var j = 0; j < this.colCount; j++) {
            colBinary.push(this.calculateColumnBinary(j));
        }
        var rowEdgeCounts = this.getContEdge(this.rowBinary);
        var colEdgeCounts = this.getContEdge(colBinary);
        var sumEdgeCount = rowEdgeCounts + colEdgeCounts;
        // console.log(`连续边数计算 sum: ${sumEdgeCount} {row: ${rowEdgeCounts} , col: ${colEdgeCounts}}`);
        return sumEdgeCount;
    };
    /**
     * 获取行或列的连续边数
     * @param binaryArr 行或列的二进制数据
     * @returns
     */
    BinaryBoard.prototype.getContEdge = function (binaryArr) {
        var edgeCounts = 0;
        for (var j = 0; j < binaryArr.length - 1; j++) {
            /** j行下边情况的二进制，0无边 1有边 */
            var edgeBit = binaryArr[j] ^ binaryArr[j + 1];
            var edgeCount = 0;
            var pos = 0;
            var lowestBitIsOne = false;
            while (edgeBit) {
                if ((edgeBit & 1) == 1) {
                    if (!lowestBitIsOne) {
                        lowestBitIsOne = true;
                        edgeCount++;
                    }
                    else {
                        if (pos > 0) {
                            var isCont = ((binaryArr[j] >> pos) & 1) ^ ((binaryArr[j] >> (pos - 1)) & 1);
                            if (isCont == 1) {
                                lowestBitIsOne = true;
                                edgeCount++;
                            }
                        }
                    }
                }
                else {
                    lowestBitIsOne = false;
                }
                edgeBit >>= 1;
                pos++;
            }
            edgeCounts += edgeCount;
        }
        return edgeCounts;
    };
    BinaryBoard.prototype.getCanPut = function (filterList) {
        var result = [];
        for (var _i = 0, filterList_1 = filterList; _i < filterList_1.length; _i++) {
            var k = filterList_1[_i];
            var canputPoss = this.canPut(k);
            if (canputPoss) {
                result.push(k);
            }
        }
        return result;
    };
    BinaryBoard.prototype.getCanPutRandom = function () {
        var blocks = BinaryBoard.getIncludeBlock();
        for (var _i = 0, blocks_1 = blocks; _i < blocks_1.length; _i++) {
            var k = blocks_1[_i];
            var canputPoss = this.getCanPutPoss(k);
            if (canputPoss.length) {
                return k;
            }
        }
    };
    BinaryBoard.getIncludeBlock = function () {
        if (!BinaryBoard.excludeActive)
            return shuffle(BinaryBoard.BLOCKS.concat([]));
        return shuffle(BinaryBoard.BLOCKS.filter(function (block) { return !BinaryBoard.excludeBlock.includes(block); }));
    };
    // 选择能消除的，使盘面边数最小的块，没有能消除的，就选择边数最小的
    BinaryBoard.prototype.getCanPutEdge = function () {
        var blocks = BinaryBoard.getIncludeBlock();
        var edge = 10000;
        var result = 1;
        var bResult = new BinaryBoard();
        var curClearCount = this.getClearCount();
        var hasBlockClear = false;
        for (var _i = 0, blocks_2 = blocks; _i < blocks_2.length; _i++) {
            var k = blocks_2[_i];
            var canputPoss = this.getCanPutPoss(k);
            if (canputPoss.length !== 0) {
                for (var _a = 0, canputPoss_1 = canputPoss; _a < canputPoss_1.length; _a++) {
                    var pos = canputPoss_1[_a];
                    var bEdge = this.clone();
                    bEdge.putBlock(k, pos);
                    var putClearCount = bEdge.getClearCount();
                    var newEdge = bEdge.getEdgeGameNum();
                    if (putClearCount > curClearCount) {
                        // 之前没消除，现在有了,就直接替换
                        if (!hasBlockClear) {
                            result = k;
                            edge = newEdge;
                            bResult = bEdge;
                            // 之前有消除，就比较边数
                        }
                        else if (newEdge < edge) {
                            result = k;
                            edge = newEdge;
                            bResult = bEdge;
                        }
                        hasBlockClear = true;
                        // 没找到消除的，先比较边数
                    }
                    else if (newEdge < edge && !hasBlockClear) {
                        result = k;
                        edge = newEdge;
                        bResult = bEdge;
                    }
                }
            }
        }
        return { block: result, binary: bResult };
    };
    BinaryBoard.prototype.getEdgeBinary = function (n, isBorderCount) {
        if (isBorderCount === void 0) { isBorderCount = false; }
        var count = 0;
        var curr = n;
        var endIndex = this.rowCount - 1;
        for (var i = 0; i < this.rowCount; i++) {
            if ((curr & 1) === 1) {
                var nextVal = (n >> (i + 1)) & 1;
                var prevVal = (n >> (i - 1)) & 1;
                if ((i == 0 || i == endIndex) && isBorderCount) {
                    count += 1;
                }
                if (nextVal == 0 && i != endIndex) {
                    count += 1;
                }
                if (prevVal == 0 && i != 0) {
                    count += 1;
                }
            }
            curr >>= 1;
        }
        return count;
    };
    /**获取贴边率 */
    BinaryBoard.prototype.getNearBoarderPer = function (id, pos) {
        var near_num = 0;
        var config = ShapeInfo.getConfig(id); // width , height , shape
        var border_num = config.border;
        var shapes = config.shape.concat(); //当前块的形状
        var bitMx = this.colCount - config.width - pos.x;
        for (var i = 0; i < config.height; i++) {
            shapes[i] <<= bitMx; //将当前块移动到放置的位置
            var __row = pos.y + i;
            var __rowBinary = this.rowBinary[__row];
            var __downRowBinary = this.rowBinary[__row + 1];
            var __upRowBinary = this.rowBinary[__row - 1];
            var __bitObj = this.separateOnes(shapes[i]); //拆开每一个格子位置
            var __oneOfBitArr = __bitObj.bit;
            var __posIndex = __bitObj.pos;
            if (__row == 0 || __row == this.rowCount - 1) {
                near_num += __oneOfBitArr.length;
            } //当前遇到了上边界  或者 下边界
            for (var j = 0; j < __oneOfBitArr.length; j++) {
                var __col = __posIndex[j];
                if (__col == 0 || __col == this.colCount - 1) {
                    near_num += 1;
                } //当前遇到了左边界或者右边界
                var shape = __oneOfBitArr[j];
                var nextBit = shape >> 1;
                var prevBit = shape << 1;
                var nextNear_h = (((__rowBinary & nextBit) == nextBit) && nextBit != 0) ? 1 : 0;
                var prevNear_h = (((__rowBinary & prevBit) == prevBit) && prevBit != 0) ? 1 : 0;
                near_num += nextNear_h + prevNear_h;
                if (__downRowBinary && (__downRowBinary & shape) == shape) {
                    near_num += 1;
                }
                if (__upRowBinary && (__upRowBinary & shape) == shape) {
                    near_num += 1;
                }
            }
        }
        return near_num / border_num;
    };
    BinaryBoard.prototype.logEmptyTile = function () {
        var result = "";
        for (var i = 0; i < this.rowCount; i++) {
            result += '\n';
            for (var j = this.colCount - 1; j >= 0; j--) {
                var bit = this.rowBinary[i] >> j;
                if ((bit & 1) === 0) {
                    result += '□  ';
                }
                else {
                    result += '■  ';
                }
            }
        }
        LogUtil.printLog(result);
    };
    BinaryBoard.prototype.record = function () {
        this._record = this.rowBinary.concat();
        return this._record;
    };
    BinaryBoard.prototype.revert = function () {
        this.rowBinary = this._record.concat();
    };
    BinaryBoard.prototype.reset = function (record) {
        this.rowBinary = record.concat();
    };
    Object.defineProperty(BinaryBoard.prototype, "board", {
        get: function () {
            return this.rowBinary.concat();
        },
        set: function (boardArr) {
            this.rowBinary = boardArr.concat();
        },
        enumerable: false,
        configurable: true
    });
    BinaryBoard.prototype.isBackEyeJudge2 = function (_edge1, __id1, __canPutsInit1, _edge2, __id2, __canPutsInit2) {
        var state1 = false;
        var state2 = false;
        var firstRecord = this.record().concat();
        for (var i = 0, len = __canPutsInit1.length; i < len; i++) {
            this.reset(firstRecord);
            this.putBlock(__id1, __canPutsInit1[i]);
            var edge = this.getEdgeGameNum();
            if (edge < _edge1) {
                state1 = true;
                break;
            }
        }
        for (var i = 0, len = __canPutsInit2.length; i < len; i++) {
            this.reset(firstRecord);
            this.putBlock(__id2, __canPutsInit2[i]);
            var edge = this.getEdgeGameNum();
            if (edge < _edge2) {
                state2 = true;
                break;
            }
        }
        this.reset(firstRecord);
        if (state1 && state2) {
            return true;
        }
        else {
            return false;
        }
    };
    BinaryBoard.prototype.separateOnes = function (binaryNumber) {
        var result = [];
        var positionIndex = [];
        var index = 0;
        while (binaryNumber > 0) {
            if (binaryNumber & 1) {
                result.push(1 << index);
                positionIndex.push(7 - index);
            }
            binaryNumber >>= 1;
            index++;
        }
        return { bit: result, pos: positionIndex };
    };
    BinaryBoard.prototype.getClearRowCol = function () {
        var _this = this;
        var clearRow = [];
        var clearCol = [];
        var bAllCol = this.bFull;
        this.rowBinary.forEach(function (n, i) {
            if (n === _this.bFull) {
                clearRow.push(i);
            }
            bAllCol &= n;
        });
        var i = this.colCount - 1;
        while (bAllCol > 0) {
            if (bAllCol & 1) {
                clearCol.push(i);
            }
            bAllCol >>= 1;
            i -= 1;
        }
        return { row: clearRow, col: clearCol };
    };
    /**
     * 是否可以将数组内的id都放入盘面 ,数组必须是3个ID。
     */
    BinaryBoard.prototype.checkPutAllBlocks = function (id, putList) {
        var _this = this;
        var idCopy = id.concat();
        var logPos = function (id, idx, pos) {
            var strArr = ["第", "", "次操作第", "不知道", "个块[", "", "] 位置是: ", "", "行: ", "", "列"];
            strArr[1] = (idx + 1).toString();
            var index = idCopy.indexOf(id);
            if (index !== -1) {
                strArr[3] = (index + 1).toString();
                idCopy[index] = -1;
            }
            strArr[5] = id.toString();
            strArr[7] = pos.y.toString();
            strArr[9] = pos.x.toString();
            var logStr = strArr.join('');
            LogUtil.printLog(logStr);
            if (putList) {
                putList.push({ id: id, index: idx, pos: pos });
            }
        };
        var startTime = new Date().getTime();
        var checkPutIn = function (id, idx) {
            if (idx >= id.length) {
                LogUtil.printLog("idx 超过范围" + idx);
                return true;
            }
            var blockId = id[idx];
            if (blockId == -1) {
                return checkPutIn(id, idx + 1);
            }
            var pos = _this.getCanPutPoss(blockId);
            if (pos.length === 0) {
                return false;
            }
            if (idx === id.length - 1) {
                // 最后一位，且有可放置位置，直接返回成功。
                logPos(blockId, idx, pos[0]);
                return true;
            }
            for (var i = 0, len = pos.length; i < len; i++) {
                var temp = _this.rowBinary.concat();
                _this.putBlock(blockId, pos[i]);
                _this.canClearBlockArr(true);
                if (checkPutIn(id, idx + 1)) {
                    logPos(blockId, idx, pos[i]);
                    return true;
                }
                if (idx == 0) {
                    var newArr = [id[0], id[2], id[1]];
                    if (checkPutIn(newArr, idx + 1)) {
                        logPos(blockId, idx, pos[i]);
                        return true;
                    }
                }
                // 因为会多轮，所以此处手动保存
                _this.rowBinary = temp.concat();
                if (new Date().getTime() - startTime > 100) {
                    return false;
                }
            }
            return false;
        };
        var id1 = [id[1], id[0], id[2]];
        var id2 = [id[2], id[0], id[1]];
        return checkPutIn(id, 0)
            || checkPutIn(id1, 0)
            || checkPutIn(id2, 0);
    };
    BinaryBoard.prototype.canClearRowCols = function (isClear) {
        var _this = this;
        if (isClear === void 0) { isClear = false; }
        var clearRow = [];
        var clearCol = [];
        var bAllCol = this.bFull;
        this.rowBinary.forEach(function (n, i) {
            if (n === _this.bFull) {
                clearRow.push(i);
            }
            bAllCol &= n;
        });
        var i = this.colCount - 1;
        while (bAllCol > 0) {
            if (bAllCol & 1) {
                clearCol.push(i);
            }
            bAllCol >>= 1;
            i -= 1;
        }
        if (isClear) {
            clearRow.forEach(function (row) {
                _this.rowBinary[row] = 0;
                // this.clearRow(row);
            });
            clearCol.forEach(function (col) {
                _this.rowBinary.forEach(function (n, i) {
                    _this.rowBinary[i] = _this.setBitToZero(n, col);
                });
            });
        }
        return { row: clearRow, col: clearCol };
    };
    /**
     * 获得空洞总数
     */
    BinaryBoard.prototype.getEmptyHoleNum = function () {
        var board = this.board;
        //获取行列空洞数
        var emptyRow = 0;
        var emptyCol = 0;
        for (var i = 0; i < board.length; i++) {
            var rPre = undefined;
            var rTotal = 0;
            var cPre = undefined;
            var cTotal = 0;
            for (var j = 0; j < 8; j++) {
                var curr = board[i] >> j & 1;
                if (rPre != curr) {
                    rTotal++;
                    rPre = curr;
                }
                var curc = board[j] >> i & 1;
                if (cPre != curc) {
                    cTotal++;
                    cPre = curc;
                }
            }
            emptyRow += rPre == 0 ? Math.ceil(rTotal / 2) : Math.floor(rTotal / 2);
            emptyCol += cPre == 0 ? Math.ceil(cTotal / 2) : Math.floor(cTotal / 2);
        }
        return emptyRow + emptyCol;
    };
    BinaryBoard.prototype.getEmptyNumObj = function () {
        var emptyNum = 0;
        for (var i = 0; i < this.rowCount; i++) {
            for (var j = 0; j < this.colCount; j++) {
                if ((this.rowBinary[i] & (1 << j)) == 0) {
                    emptyNum++;
                }
            }
        }
        return emptyNum;
    };
    BinaryBoard.prototype.getWeightValueObj = function (save_Arr) {
        var _this = this;
        if (save_Arr) {
            this.convertToBinaryBoard(save_Arr);
        }
        /**
         * 行数值
         */
        var rowWeightValue = 0;
        /**
         * 列数值
         */
        var colWeightValue = 0;
        /**
         * 基础权重
         */
        var baseWeightValue = 0;
        /**
         * 消弱权重
         */
        var clearWeightValue = 0;
        /**
         * 加强权重
         */
        var strengthWeightValue = 0;
        /**
         * 结束权重
         */
        var endWeightValue = 0;
        /**
         * 九宫数量
         */
        var gong9Num = 0;
        /**
         * 行5数量
         */
        var row5Num = 0;
        /**
         * 空格数量
         */
        var emptyNum = this.getEmptyNumObj();
        /**
         * 列5数量
         */
        var col5Num = 0;
        var canPutShape = function (shape, x, y) {
            var offsetX = _this.colCount - shape.width - x;
            if (offsetX < 0)
                return false;
            if (y + shape.height > _this.rowCount)
                return false;
            for (var i = 0; i < shape.height; i++) {
                if ((_this.rowBinary[i + y] & (shape.shape[i] << offsetX)) > 0)
                    return false;
            }
            return true;
        };
        var thirteenBlockConfig = ShapeInfo.getConfig(13);
        var elevenBlockConfig = ShapeInfo.getConfig(11);
        var twentytwoBlockConfig = ShapeInfo.getConfig(22);
        var colFillvalueArr = [0, 0, 0, 0, 0, 0, 0, 0];
        var colLinevalueArr = [0, 0, 0, 0, 0, 0, 0, 0];
        for (var i = 0; i < this.rowCount; i++) {
            var rowFillValue = 0;
            var rowLineValue = 0;
            for (var j = 0; j < this.colCount; j++) {
                var colNum = this.colCount - j - 1;
                if (this.rowBinary[i] & (1 << colNum)) {
                    rowFillValue++;
                    colFillvalueArr[j]++;
                }
                else {
                    if (canPutShape(thirteenBlockConfig, j, i)) {
                        gong9Num++;
                    }
                    // 判断是否可以放九宫
                    if (canPutShape(elevenBlockConfig, j, i)) {
                        row5Num++;
                    }
                    // 判断是否可以放行5
                    if (canPutShape(twentytwoBlockConfig, j, i)) {
                        col5Num++;
                    }
                    if (j == 0) {
                        //横向判断+1
                        if (this.rowBinary[i] & (1 << (colNum - 1))) {
                            rowLineValue++;
                        }
                    }
                    else if (j == this.rowCount - 1) {
                        if (this.rowBinary[i] & (1 << (colNum + 1))) {
                            rowLineValue++;
                        }
                    }
                    else {
                        if (this.rowBinary[i] & (1 << (colNum - 1))) {
                            rowLineValue++;
                        }
                        if (this.rowBinary[i] & (1 << (colNum + 1))) {
                            rowLineValue++;
                        }
                    }
                    if (i == 0) {
                        //纵向判断+1
                        if (this.rowBinary[i + 1] & (1 << colNum)) {
                            colLinevalueArr[j]++;
                        }
                    }
                    else if (i == this.colCount - 1) {
                        if (this.rowBinary[i - 1] & (1 << colNum)) {
                            colLinevalueArr[j]++;
                        }
                    }
                    else {
                        if (this.rowBinary[i + 1] & (1 << colNum)) {
                            colLinevalueArr[j]++;
                        }
                        if (this.rowBinary[i - 1] & (1 << colNum)) {
                            colLinevalueArr[j]++;
                        }
                    }
                }
            }
            rowWeightValue += rowFillValue * rowLineValue;
        }
        for (var i = 0; i < colFillvalueArr.length; i++) {
            colWeightValue += colFillvalueArr[i] * colLinevalueArr[i];
        }
        baseWeightValue = rowWeightValue + colWeightValue;
        if (gong9Num >= 3) {
            clearWeightValue = gong9Num + 20;
        }
        else {
            clearWeightValue = gong9Num * 2 + 10 + row5Num / 3 + 3 + col5Num / 3 + 3;
        }
        if (emptyNum >= 32) {
            strengthWeightValue = baseWeightValue - clearWeightValue - (emptyNum - 32) * (emptyNum - 32) / 5;
        }
        else {
            if (gong9Num > 0) {
                strengthWeightValue = baseWeightValue - clearWeightValue + (emptyNum - 32) * (emptyNum - 32) / 5;
            }
            else {
                strengthWeightValue = baseWeightValue - clearWeightValue + (emptyNum - 32) * (emptyNum - 32) / 2;
            }
        }
        endWeightValue = strengthWeightValue + 246;
        return endWeightValue;
    };
    BinaryBoard.getWeightValue = function (board) {
        var bOri = new BinaryBoard;
        return bOri.getWeightValueObj(board);
    };
    /**
     * 是否可以将数组内的id都放入盘面,不限制数组数
     */
    BinaryBoard.prototype.checkPutIdArrInBlocks = function (id) {
        var _this = this;
        var startTime = new Date().getTime();
        var checkPutIn = function (id, idx) {
            if (idx >= id.length) {
                LogUtil.printLog("idx 超过范围" + idx);
                return true;
            }
            var blockId = id[idx];
            if (blockId == -1) {
                return true;
            }
            var pos = _this.getCanPutPoss(blockId);
            if (pos.length === 0) {
                return false;
            }
            if (idx === id.length - 1) {
                // 最后一位，且有可放置位置，直接返回成功。
                // logPos(blockId, idx, pos[0]);
                return true;
            }
            for (var i = 0, len = pos.length; i < len; i++) {
                var temp = _this.rowBinary.concat();
                _this.putBlock(blockId, pos[i]);
                _this.canClearBlockArr(true);
                if (checkPutIn(id, idx + 1)) {
                    return true;
                }
                _this.rowBinary = temp.concat();
                // 因为会多轮，所以此处手动保存
                if (new Date().getTime() - startTime > 100) {
                    return false;
                }
            }
            return false;
        };
        return checkPutIn(id, 0);
    };
    // 取最小基边数
    BinaryBoard.prototype.getMinGameEdgeNum = function (blockList) {
        var _temp = this.rowBinary.concat();
        var minEdgeNum = Infinity;
        var minEdgePos = null;
        var minEdgeId = -1;
        for (var _i = 0, blockList_1 = blockList; _i < blockList_1.length; _i++) {
            var id = blockList_1[_i];
            var canPutPos = this.getCanPutPoss(id);
            if (canPutPos.length > 0) {
                for (var _a = 0, canPutPos_1 = canPutPos; _a < canPutPos_1.length; _a++) {
                    var pos = canPutPos_1[_a];
                    this.putBlock(id, pos);
                    var edgeNum = this.getEdgeGameNum();
                    if (edgeNum < minEdgeNum) {
                        minEdgeNum = edgeNum;
                        minEdgeId = id;
                        minEdgePos = pos;
                    }
                    this.rowBinary = _temp.concat();
                }
            }
        }
        return { id: minEdgeId, pos: minEdgePos };
    };
    // 选择能消除的，使盘面边数最小的块，没有能消除的，就选择边数最小的
    BinaryBoard.prototype.getMinGameEdgeNumWithClear = function (blocks) {
        // const blocks = BinaryBoard.getIncludeBlock();
        var edge = Infinity;
        var minEdgeId = -1;
        var minEdgePos = null;
        var curClearCount = this.getClearCount();
        var hasBlockClear = false;
        for (var _i = 0, blocks_3 = blocks; _i < blocks_3.length; _i++) {
            var k = blocks_3[_i];
            var canputPoss = this.getCanPutPoss(k);
            if (canputPoss.length !== 0) {
                for (var _a = 0, canputPoss_2 = canputPoss; _a < canputPoss_2.length; _a++) {
                    var pos = canputPoss_2[_a];
                    var bEdge = this.clone();
                    bEdge.putBlock(k, pos);
                    var putClearCount = bEdge.getClearCount();
                    var newEdge = bEdge.getEdgeGameNum();
                    if (putClearCount > curClearCount) {
                        // 之前没消除，现在有了,就直接替换
                        if (!hasBlockClear) {
                            minEdgeId = k;
                            edge = newEdge;
                            minEdgePos = pos;
                            // 之前有消除，就比较边数
                        }
                        else if (newEdge < edge) {
                            minEdgeId = k;
                            edge = newEdge;
                            minEdgePos = pos;
                        }
                        hasBlockClear = true;
                        // 没找到消除的，先比较边数
                    }
                    else if (newEdge < edge && !hasBlockClear) {
                        minEdgeId = k;
                        edge = newEdge;
                        minEdgePos = pos;
                    }
                }
            }
        }
        return { id: minEdgeId, pos: minEdgePos };
    };
    /**
     * 获取当前盘面的死亡率
     * @param ids 传入计算难度系数的3个块
     * @returns
     */
    BinaryBoard.prototype.GetDeathRate = function (ids) {
        //恢复到默认盘面;
        this.revert();
        //可放置路径;
        var liveRoad = 0;
        //不可放置路径；
        var deathRoad = 0;
        var list = [[0, 1, 2], [0, 2, 1], [1, 2, 0], [1, 0, 2], [2, 0, 1], [2, 1, 0]];
        for (var i = 0, len = list.length; i < len; i++) {
            this.revert();
            var aId = ids[list[i][0]];
            var bId = ids[list[i][1]];
            var cId = ids[list[i][2]];
            if (this.canPut(aId)) {
                //a的可放置位置；
                var aIdPoss = this.getCanPutPoss(aId);
                for (var aIdIndex = 0, aIdLen = aIdPoss.length; aIdIndex < aIdLen; aIdIndex++) {
                    this.revert();
                    //放入A；
                    this.putBlock(aId, aIdPoss[aIdIndex]);
                    //能消除的话就消除;
                    this.canClearBlockArr(true);
                    var nowRowBinary = __spreadArray([], this.rowBinary, true);
                    if (this.canPut(bId)) {
                        //B的可放入位置；
                        var bIdPoss = this.getCanPutPoss(bId);
                        for (var bIdIndex = 0, bIdLen = bIdPoss.length; bIdIndex < bIdLen; bIdIndex++) {
                            this.rowBinary = __spreadArray([], nowRowBinary, true);
                            //让入B；
                            this.putBlock(bId, bIdPoss[bIdIndex]);
                            //能消除的话就消除;
                            this.canClearBlockArr(true);
                            if (this.canPut(cId)) {
                                liveRoad += 1;
                                this.getCanPutPoss(cId);
                                // console.log('生路:');
                                // console.log(aId, aIdPoss[aIdIndex].x + ',' + aIdPoss[aIdIndex].y);
                                // console.log(bId, bIdPoss[bIdIndex].x + ',' + bIdPoss[bIdIndex].y);
                                // console.log(cId, cIdPoss);
                            }
                            else {
                                deathRoad += 1;
                            }
                        }
                    }
                }
            }
        }
        this.revert();
        LogUtil.printLog("\u300A\u751F\u6B7B\u8DEF\u300B\uFF1A\u751F\u8DEF+".concat(liveRoad, "\u3001\u6B7B\u8DEF+").concat(deathRoad));
        //死亡率 = q2 / (q1 + q2)
        var deathRate = deathRoad / (liveRoad + deathRoad);
        //难度系数 = 1 - 死亡率
        // const hardCoefficient = 1 - deathRate;
        return deathRate;
    };
    /**
     * 判断某个坐标点 某个模型放进去所占用的点集合
     * @param initialX 行索引，从0开始。
     * @param initialY 列索引，从0开始。
     * @param blocks 异形块形状
     * @returns 位置是否有东西，即不是空位置。
        */
    BinaryBoard.prototype.generateCoordinates = function (initialX, initialY, blocks) {
        var coordinates = [];
        var shapeData = this.getBlockIdByPos(blocks);
        var shape = shapeData.shape;
        var width = shapeData.width;
        var height = shapeData.height;
        for (var y = 0; y < height; y++) {
            for (var x = 0; x < width; x++) {
                var bitValue = (shape[y] >> (width - 1 - x)) & 1;
                if (bitValue === 1) {
                    coordinates.push([initialY + y, initialX + x]);
                }
            }
        }
        return coordinates;
    };
    // 获取当前可放可消， 可放不可消，不可放列表
    BinaryBoard.prototype.getClearPutAndNoPut = function (blockArr, clearList, putList, noPutList) {
        // 记录之前的盘面和缓存
        var curRord = this._record.concat();
        // this._record = this.rowBinary.concat();
        this.record();
        for (var _i = 0, blockArr_1 = blockArr; _i < blockArr_1.length; _i++) {
            var id = blockArr_1[_i];
            var poss = this.getCanPutPoss(id);
            if (poss.length == 0) {
                noPutList.push(id);
                continue;
            }
            var canClear = false;
            for (var _a = 0, poss_1 = poss; _a < poss_1.length; _a++) {
                var pos = poss_1[_a];
                this.putBlock(id, pos);
                if (this.canClearBlockArr(false)) {
                    // 可消除
                    clearList.push(id);
                    canClear = true;
                    this.revert();
                    break;
                }
                this.revert();
            }
            if (canClear == false) {
                putList.push(id);
            }
        }
        // 恢复之前的缓存
        this._record = curRord.concat();
        return;
    };
    BinaryBoard.prototype.getBlockIdByPos = function (pos) {
        var binaryArray = pos;
        var width = binaryArray[0].length;
        var height = binaryArray.length;
        var shape = [];
        binaryArray.forEach(function (row) {
            var binaryRow = row.join('');
            shape.push(parseInt(binaryRow, 2));
        });
        var blockShape = {
            id: 0,
            width: width,
            height: height,
            shape: shape,
            info: [],
            count: 0,
            offsetCol: 0,
            border: 0
        };
        return blockShape;
    };
    /**获取贴边率 */
    BinaryBoard.prototype.getDynamicNearBoarderPer = function (blocks, pos) {
        var near_num = 0;
        var config = this.getBlockIdByPos(blocks); // width , height , shape
        var border_num = ShapeInfo.getBorderNum(blocks);
        var shapes = config.shape.concat(); //当前块的形状
        var bitMx = this.colCount - config.width - pos.x;
        for (var i = 0; i < config.height; i++) {
            shapes[i] <<= bitMx; //将当前块移动到放置的位置
            var __row = pos.y + i;
            var __rowBinary = this.rowBinary[__row];
            var __downRowBinary = this.rowBinary[__row + 1];
            var __upRowBinary = this.rowBinary[__row - 1];
            var __bitObj = this.separateOnes(shapes[i]); //拆开每一个格子位置
            var __oneOfBitArr = __bitObj.bit;
            var __posIndex = __bitObj.pos;
            if (__row == 0 || __row == this.rowCount - 1) {
                near_num += __oneOfBitArr.length;
            } //当前遇到了上边界  或者 下边界
            for (var j = 0; j < __oneOfBitArr.length; j++) {
                var __col = __posIndex[j];
                if (__col == 0 || __col == this.colCount - 1) {
                    near_num += 1;
                } //当前遇到了左边界或者右边界
                var shape = __oneOfBitArr[j];
                var nextBit = shape >> 1;
                var prevBit = shape << 1;
                var nextNear_h = (((__rowBinary & nextBit) == nextBit) && nextBit != 0) ? 1 : 0;
                var prevNear_h = (((__rowBinary & prevBit) == prevBit) && prevBit != 0) ? 1 : 0;
                near_num += nextNear_h + prevNear_h;
                if (__downRowBinary && (__downRowBinary & shape) == shape) {
                    near_num += 1;
                }
                if (__upRowBinary && (__upRowBinary & shape) == shape) {
                    near_num += 1;
                }
            }
        }
        return near_num / border_num;
    };
    BinaryBoard.excludeLevel = 999999;
    BinaryBoard.excludeBlock = [];
    BinaryBoard.BLOCKS = [
        2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38,
        39, 40, 41, 42,
    ];
    BinaryBoard.excludeActive = false;
    return BinaryBoard;
}());

export { $registeCoreComp, AlgoClassTypeConst, AlgoClassTypeConstName, AllConditionComponent, AllwaysTrue, BaseConfigPrefab, BasicFeatureComponent, BinaryBoard, ClearType, CommonUtil, FeatureConfigPrefab, FeatureGroupLogicComponent, FeatureLayerDefine, FixedRandomUtil, IdAdd, LinearGroupLogicComponent, LogUtil, Puzzle, PuzzleConfigPrefab, RandomLogicComponent, SelectGroupLogicComponent, ShapeInfo, SplitFrameUtil, getCompId, getCompType, isAlgoInstance, shuffle };
//# sourceMappingURL=puzzlecore.js.map
