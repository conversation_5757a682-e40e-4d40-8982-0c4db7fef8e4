import { World } from './ecs/cores/World';
import { ECSEvent } from './ecs/GameEvent';
import { RenderWorld } from './ecs/cores/RenderWorld';
import { GameConfigConst } from './ecs/registry/GameRegistry';
import { E_Algorithm_Offer_Block } from '../algorithm/events/E_Algorithm_Offer_Block';
import { IBoardData } from './ecs/GameEventData';
import { GameConfig } from './ecs/GameConfig';
import { OverrideConfigManager } from './ecs/cores/OverrideConfigManager';
import { EcsStorageData } from './ecs/cores/center/SnapshotCenter';
import { registerModule } from './ecs/registry/ModuleRegistry';
import { DualBoardModuleRegistry } from '../../../../bundles/advDualBoardBundle/script/registry/DualBoardRegistry';
import { SystemsRegistry } from './ecs/registry/SystemRegistry';
import { ECSRuleRegistry } from './ecs/registry/ECSRuleRegistry';
import { SystemConfig } from './ecs/config/conf/SystemConfig';
import { RuleConfig } from './ecs/config/conf/RuleConfig';

/**
 * 游戏主类
 * 负责管理游戏的生命周期、世界系统、快照管理等核心功能
 */
export class Game {
    // ==================== 私有属性 ====================

    /** 逻辑世界 */
    private _world: World;

    /** 渲染世界 */
    private _renderWorld: RenderWorld;

    /**重写配置数据管理 */
    private _overrideConfigMgr: OverrideConfigManager;

    /** 根节点 */
    private _rootNode: cc.Node;

    /** 当前关卡ID --这个参数后续会从其他地方获取*/
    public curLevelId: number = 6;

    // ==================== 生命周期方法 ====================

    /**
     * 初始化游戏
     * @param rootNode 根节点
     */
    public init(rootNode: cc.Node): void {
        this._rootNode = rootNode;
        this.registerGamePlayModule();
        this.initWorlds();
        this.addEventListeners();
        this.startGame();
    }
    /** 注册bundle玩法模块* */
    registerGamePlayModule() {
        // 注册双盘面模块
        registerModule(new DualBoardModuleRegistry(), SystemsRegistry, ECSRuleRegistry, SystemConfig, RuleConfig);
    }

    /**
     * 销毁游戏
     */
    public dispose(): void {
        this.removeEventListeners();
        this.disposeWorlds();
        this.resetState();
    }

    /**
     * 游戏更新
     * @param dt 帧间隔时间
     */
    public async update(dt: number): Promise<void> {
        this._world.update(dt);
        this._renderWorld.update(dt);
    }

    // ==================== 私有初始化方法 ====================

    private getLevelId(): string {
        const levelId = 'LevelConfig' + this.curLevelId;
        let overrideLevelConfig = GameConfig[GameConfigConst.OverrideLevelConfig][levelId];
        if (overrideLevelConfig) {
            return levelId;
        }
        //临时处理，循环配置
        this.curLevelId = (this.curLevelId % Object.keys(GameConfig[GameConfigConst.OverrideLevelConfig]).length) + 1;
        return this.getLevelId();
    }

    /**
     * 初始化世界系统
     */
    private initWorlds(): void {
        const levelId = this.getLevelId();
        const gameConfig = new OverrideConfigManager().buildConfig(levelId, GameConfig);
        this._world = new World(gameConfig);
        this._renderWorld = new RenderWorld();
        this._renderWorld.init(this._world, this._rootNode);
        if (CC_DEBUG) window['EcsGM']?.ins.init(this._world);
    }

    /**
     * 销毁世界系统
     */
    private disposeWorlds(): void {
        if (this._renderWorld) {
            this._renderWorld.dispose();
            this._renderWorld = null;
        }
        this._world = null;
    }

    /**
     * 重置游戏状态
     */
    private resetState(): void {
        this._rootNode = null;
    }

    // ==================== 事件管理 ====================

    /**
     * 添加事件监听器
     */
    private addEventListeners(): void {
        if (!this._world) return;

        this._world.eventBus.on(ECSEvent.BaseEvent.RENDER_ADD, this.__onRenderAdd, this);
        this._world.eventBus.on(ECSEvent.BaseEvent.RENDER_REMOVE, this.__onRenderRemove, this);
        this._world.eventBus.on(ECSEvent.BaseEvent.GAME_RESTART, this.__onGameRestart, this);
        this._world.eventBus.on(ECSEvent.BaseEvent.PRODUCE_BLOCK_START, this.__onOfferBlockStart, this);
        this._world.eventBus.on(ECSEvent.BaseEvent.SAVE_SNAPSHOT, this.__onSaveSnapshot, this);
    }

    /**
     * 移除事件监听器
     */
    private removeEventListeners(): void {
        if (!this._world) return;

        this._world.eventBus.off(ECSEvent.BaseEvent.RENDER_ADD, this.__onRenderAdd, this);
        this._world.eventBus.off(ECSEvent.BaseEvent.RENDER_REMOVE, this.__onRenderRemove, this);
        this._world.eventBus.off(ECSEvent.BaseEvent.GAME_RESTART, this.__onGameRestart, this);
        this._world.eventBus.off(ECSEvent.BaseEvent.PRODUCE_BLOCK_START, this.__onOfferBlockStart, this);
        this._world.eventBus.off(ECSEvent.BaseEvent.SAVE_SNAPSHOT, this.__onSaveSnapshot, this);
    }

    // ==================== 事件处理器 ====================

    /**
     * 渲染创建事件处理器
     * @param entityId 实体ID
     */
    private __onRenderAdd(entityId: number): void {
        if (!this._renderWorld) return;

        const newRenderCmd = this._renderWorld.createRenderCmd(entityId);
        this._renderWorld.addRenderCmd(newRenderCmd);
    }

    /**
     * 渲染移除事件处理器
     * @param entityId 实体ID
     */
    private __onRenderRemove(entityId: number): void {
        if (!this._renderWorld) return;

        this._renderWorld.destroyEntity(entityId);
    }

    /**
     * 游戏重启事件处理器
     */
    private __onGameRestart(): void {
        console.log('[Game] Restart');

        // 1. 移除事件监听，防止回调访问失效对象
        this.removeEventListeners();

        // 2. 完全销毁逻辑/渲染世界
        this.disposeWorlds();

        // 4. 创建全新逻辑与渲染世界
        this.initWorlds();

        // 5. 重新绑定事件
        this.addEventListeners();

        // 7. 启动新游戏（不检查快照，直接走新局流程）
        this.startNewGame();

        // if (CC_DEBUG) window['EcsGM'].ins.init(game);
    }

    private __onOfferBlockStart(boardData: IBoardData): void {
        console.log('开始出块');
        // 创建带数据的算法事件
        const algorithmEvent = new E_Algorithm_Offer_Block();
        algorithmEvent.boardData = boardData;

        falcon.EventManager.dispatchModuleEvent(algorithmEvent);
    }

    private __onSaveSnapshot(storageData: EcsStorageData): void {
        console.log('保存快照', storageData.storageKey);
        cc.sys.localStorage.setItem(storageData.storageKey, JSON.stringify(storageData));
    }

    /**
     * 开始游戏
     */
    private startGame(): void {
        if (this.restoreFromSnapshot()) {
            return;
        }
        console.log('没有本地快照，开始新游戏');
        this.startNewGame();
    }

    /**
     * 开始新游戏
     */
    private startNewGame(): void {
        this._world.startNewWorld();
    }

    /**
     * 从快照恢复游戏
     */
    private restoreFromSnapshot(): boolean {
        console.log('发现本地快照，正在恢复...');
        const snapshot = this.loadSnapshot();
        if (snapshot) {
            const success = this._world.snapshotCenter.restoreSnapshot(snapshot.data);
            if (success) {
                console.log('快照恢复成功');
                return true;
            }
        }
        console.warn('快照恢复失败，开始新游戏');
        return false;
    }
    /**
     * 加载快照数据
     * @returns 快照数据，如果不存在或加载失败则返回null
     */
    private loadSnapshot(): EcsStorageData {
        try {
            const storageKey = this._world.snapshotCenter.getStorageKey();
            const rawData = cc.sys.localStorage.getItem(storageKey);

            if (!rawData) {
                console.log(`未找到快照数据: ${storageKey}`);
                return null;
            }
            const parsed = JSON.parse(rawData);
            console.log(`成功加载快照: ${storageKey}`, parsed.meta);
            return parsed;
        } catch (error) {
            console.error(`加载快照失败:`, error);
            return null;
        }
    }

    // ==================== 快照管理 ====================
}

// 导出游戏实例（临时写法）
export const game = new Game();
