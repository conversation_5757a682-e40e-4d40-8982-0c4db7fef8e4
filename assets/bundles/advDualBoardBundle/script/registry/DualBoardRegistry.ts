// assets/bundles/advDualBoardBundle/script/registry/DualBoardRegistry.ts

import { BaseModuleRegistry } from '../../../../advmain/scripts/modules/level/ecs/registry/ModuleRegistry';
import { System } from '../../../../advmain/scripts/modules/level/ecs/cores/System';
import { ECSRuleBase } from '../../../../advmain/scripts/modules/level/ecs/rules/core/ECSRuleBase';
import { ISystemConfig } from '../../../../advmain/scripts/modules/level/ecs/cores/System';
import { IECSRuleConfig } from '../../../../advmain/scripts/modules/level/ecs/rules/core/ECSRuleBase';

// 双盘面系统
import MirrorSyncSystem from '../system/MirrorSyncSystem';
import BoardSwitchSystem from '../system/BoardSwitchSystem';
import BoardLinkSystem from '../system/BoardLinkSystem';

// 双盘面规则
import { CheckDualRemainShapeRule } from '../rule/CheckDualRemainShapeRule';

// 双盘面系统常量
export const DualBoardSystemConst = {
    BoardLinkSystem: 'BoardLinkSystem',
    BoardSwitchSystem: 'BoardSwitchSystem',
    MirrorSyncSystem: 'MirrorSyncSystem',
} as const;

// 双盘面规则常量
export const DualBoardRuleConst = {
    CheckDualRemainShapeRule: 'CheckDualRemainShapeRule',
} as const;

// 双盘面规则配置接口
interface ICheckDualRemainShapeRuleConfig extends IECSRuleConfig {
    enabled: boolean;
    checkInterval: number;
}

// 双盘面系统注册表
const DualBoardSystemRegistry: Record<string, clzz<System>> = {
    [DualBoardSystemConst.BoardLinkSystem]: BoardLinkSystem,
    [DualBoardSystemConst.BoardSwitchSystem]: BoardSwitchSystem,
    [DualBoardSystemConst.MirrorSyncSystem]: MirrorSyncSystem,
};

// 双盘面规则注册表
const DualBoardRuleRegistry: Record<string, clzz<ECSRuleBase>> = {
    CheckDualRemainShapeRule: CheckDualRemainShapeRule,
};

// 双盘面系统配置
const DualBoardSystemConfig: Record<string, ISystemConfig> = {
    system_board_link: {
        systemId: 'system_board_link',
        systemName: DualBoardSystemConst.BoardLinkSystem,
        close: false,
    },
    system_board_switch: {
        systemId: 'system_board_switch',
        systemName: DualBoardSystemConst.BoardSwitchSystem,
        close: false,
    },
    system_mirror: {
        systemId: 'system_mirror',
        systemName: DualBoardSystemConst.MirrorSyncSystem,
        close: false,
    },
};

// 双盘面规则配置
const DualBoardRuleConfig: Record<string, ICheckDualRemainShapeRuleConfig> = {
    rule_check_dual_board_result: {
        ruleId: 'rule_check_dual_board_result',
        ruleDesc: '检查双盘面剩余形状可放置',
        ruleType: 'CheckDualRemainShapeRule',
        delay: 0,
        enabled: true,
        checkInterval: 1000,
    },
};

/**
 * 双盘面玩法模块注册器
 */
export class DualBoardModuleRegistry extends BaseModuleRegistry {
    readonly moduleName = 'DualBoardModule';
    readonly version = '1.0.0';

    registerSystems(systemRegistry: Record<string, clzz<System>>): void {
        Object.assign(systemRegistry, DualBoardSystemRegistry);
        console.log('[DualBoardModule] 系统注册:', Object.keys(DualBoardSystemRegistry));
    }

    registerRules(ruleRegistry: Record<string, clzz<ECSRuleBase>>): void {
        Object.assign(ruleRegistry, DualBoardRuleRegistry);
        console.log('[DualBoardModule] 规则注册:', Object.keys(DualBoardRuleRegistry));
    }

    registerSystemConfigs(systemConfig: Record<string, ISystemConfig>): void {
        Object.assign(systemConfig, DualBoardSystemConfig);
        console.log('[DualBoardModule] 系统配置注册:', Object.keys(DualBoardSystemConfig));
    }

    registerRuleConfigs(ruleConfig: Record<string, IECSRuleConfig>): void {
        Object.assign(ruleConfig, DualBoardRuleConfig);
        console.log('[DualBoardModule] 规则配置注册:', Object.keys(DualBoardRuleConfig));
    }

    getSystemConstants(): Record<string, string> {
        return {
            BoardLinkSystem: DualBoardSystemConst.BoardLinkSystem,
            BoardSwitchSystem: DualBoardSystemConst.BoardSwitchSystem,
            MirrorSyncSystem: DualBoardSystemConst.MirrorSyncSystem,
        };
    }

    getRuleConstants(): Record<string, string> {
        return {
            CheckDualRemainShapeRule: DualBoardRuleConst.CheckDualRemainShapeRule,
        };
    }
}
