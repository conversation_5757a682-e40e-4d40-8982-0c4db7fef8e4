type clzz<T> = {
    new (...p: any): T;
};
/**递归只读泛型 */
type DeepReadonly<T> = {
    readonly [K in keyof T]: T[K] extends object ? DeepReadonly<T[K]> : T[K];
};
/**无状态函数接口 */
type StateLessFunction = (inputArgs: DeepReadonly<any>) => any;
type CompId = number;
type FeatureCompId = number;
type LogicCompId = number;
type ConditionCompId = number;
type FormulaCompId = number;

type EventType = string | symbol;
type Handler<T = unknown> = (event: T) => void;
type WildcardHandler<T = Record<string, unknown>> = (type: keyof T, event: T[keyof T]) => void;
type EventHandlerList<T = unknown> = Array<Handler<T>>;
type WildCardEventHandlerList<T = Record<string, unknown>> = Array<WildcardHandler<T>>;
type EventHandlerMap<Events extends Record<EventType, unknown>> = Map<keyof Events | '*', EventHandlerList<Events[keyof Events]> | WildCardEventHandlerList<Events>>;
interface Emitter<Events extends Record<EventType, unknown>> {
    all: EventHandlerMap<Events>;
    on<Key extends keyof Events>(type: Key, handler: Handler<Events[Key]>): void;
    on(type: '*', handler: WildcardHandler<Events>): void;
    off<Key extends keyof Events>(type: Key, handler?: Handler<Events[Key]>): void;
    off(type: '*', handler: WildcardHandler<Events>): void;
    emit<Key extends keyof Events>(type: Key, event: Events[Key]): void;
    emit<Key extends keyof Events>(type: undefined extends Events[Key] ? Key : never): void;
}

/**固定随机生成器 */
declare class FixedRandomUtil {
    private seed;
    /**初始化种子 */
    initSeed(seed: number): void;
    /**获取当前种子 */
    getSeed(): number;
    originRandom(): number;
    /**
     * 随机数 带小数点
     * @param min 最小随机数
     * @param max 最大随机数
     */
    random(min: number, max: number): number;
    /**
    * 随机数 整数
    * @param min 最小随机数
    * @param max 最大随机数
    */
    randomInt(min: number, max: number): number;
    /**
    * 从列表随机出一个子列表
    * @param arr 列表
    * @param count 随机出的子列表数量
    */
    randomArr<T>(arr: Array<T>, count: number): Array<T>;
    /**随机出列表中某个子项
     * @param arr 列表
     */
    randomItem<T>(arr: Array<T>): T;
    shuffle<T>(array: Array<T>, returnNew?: boolean): Array<T>;
    /**
     * 概率 0- 1 看是否命中
     * @param val 命中率
     */
    hitRate(val: number): boolean;
}

/**原始配置结构 */
interface DataConfig {
    /**入口 */
    main: CompId;
    /**组件池 */
    compTvoMap: CompTvoMap;
}
/**组件池类 */
type CompTvoMap = Record<number, any>;
/**拼图配置结构 */
interface PuzzleDataConfig {
    config: Record<string, any>;
    data: Record<number, PrefabConfigData>;
}
type PrefabConfigData = any[] | Record<string, any>;
type IDADD = () => number;

/**配置预制体基类 */
declare abstract class BaseConfigPrefab<T> {
    config: DataConfig;
    prefabConfigData: T;
    /**初始化配置预制体 */
    initConfigPrefab(idAdd: IDADD, prefabConfigData: T): void;
    private $initCompId;
    /**注册组件Id列表
     * @returns 返回组件Id对应的组件类型列表
    */
    protected abstract registeCompIdList(): number[];
    /**创建配置预制体
     * @param listCompId 组件Id列表
     */
    protected abstract createConfigPrefab(listCompId: number[], compTvoMap: CompTvoMap): any;
    /**重写配置 */
    protected abstract rewriteConfig(config: T): any;
    /**重写配置预制体数据 */
    protected abstract rewritePrefabData(listCompId: number[], compTvoMap: CompTvoMap, prefabConfigData: T): any;
}

/**配置 */
interface IGlobalFeature {
    /**改变配置预制体 */
    rewriteConfig(puzzleDataConfig: PuzzleDataConfig, globalFeatureDataArr: DeepReadonly<GlobalFeatureData[]>): any;
}
interface GlobalFeatureData {
    /**类型 */
    type: CoreGlobalFeatureType;
    /**调整层级 */
    layer: string;
    /**优先级-优先级值越小,优先级就越大 */
    priority: number;
    /**特性配置Id */
    configId: number;
    /**特性配置数据 */
    configData: any;
}
/**核心全局特性类型 */
declare const enum CoreGlobalFeatureType {
    /**某层头部插入特性 特性优先级 */
    InsertHead = 1,
    /**某层尾部插入特性 特性优先级 */
    InsertTail = 2
}

declare class Puzzle<T_PuzzleArgs extends {
    FuncMap: Record<number, StateLessFunction>;
    InputFuncMap: Record<keyof T_PuzzleArgs['FuncMap'], any>;
    OutputFuncMap: Record<keyof T_PuzzleArgs['FuncMap'], any>;
    IGlobalData: Record<string, any>;
    GlobalDataTypeMap: Record<keyof T_PuzzleArgs['IGlobalData'], any>;
    IStaticData: Record<string, any>;
    StaticMap: Record<keyof T_PuzzleArgs['IStaticData'], any>;
    SystemMap: Record<string, string>;
    InputSystemMap: Record<keyof T_PuzzleArgs['SystemMap'], any>;
    OutputSystemMap: Record<keyof T_PuzzleArgs['SystemMap'], any>;
}> {
    /**=======================================工具集Start=============================================== */
    /**确定随机数 */
    fixedRandom: FixedRandomUtil;
    /**事件派发器 */
    dispatcher: Emitter<Record<EventType, unknown>>;
    /**=======================================工具集End=============================================== */
    /**=======================================数据实例映射表Start=============================================== */
    /**拼图块-组件实例表-映射表 */
    private $compInsPuzzleMap;
    /**拼图块-拼图組件配置实例表-映射表 */
    private $compTvoPuzzleMap;
    /**拼图块-拼图适配组件配置实例表-映射表 */
    private $adapterMainMap;
    /**拼图块-全局特性实例表-映射表 */
    private $globalFeaturePuzzleMap;
    /**=======================================数据映射表End=============================================== */
    /**=======================================数据注册表Start=============================================== */
    /**组件表 */
    private $compClzzMap;
    /**无状态函数 */
    private $statelessFuncMap;
    /**特性表 */
    private $featureMap;
    /**全局特性表 */
    private $globalFeatureMap;
    /**=======================================数据注册表End=============================================== */
    /**===================拼图块接口间数据共享Start========================= */
    /**静态数据集 */
    private $staticDataMap;
    /**数据集 */
    private $dataMap;
    /**外部函数集 */
    private $externFuncMap;
    /**===================拼图块接口间数据共享End========================= */
    /**===================拼图块接口间数据隔离Start========================= */
    /**组件实例表 */
    private $compInsMap;
    /**拼图块入口组件Id */
    private $adapterCompId;
    /**拼图组件配置表 */
    private $compTvoMap;
    /**当前运行块接口Id */
    private $puzzleId;
    /**===================拼图块接口间数据隔离End========================= */
    /**创建一个拼图块实例 */
    static createPuzzle<T_PuzzleArgs extends {
        FuncMap: Record<string, StateLessFunction>;
        InputFuncMap: Record<keyof T_PuzzleArgs['FuncMap'], any>;
        OutputFuncMap: Record<keyof T_PuzzleArgs['FuncMap'], any>;
        IGlobalData: Record<string, any>;
        GlobalDataTypeMap: Record<keyof T_PuzzleArgs['IGlobalData'], any>;
        IStaticData: Record<string, any>;
        StaticMap: Record<keyof T_PuzzleArgs['IStaticData'], any>;
        SystemMap: Record<string, string>;
        InputSystemMap: Record<keyof T_PuzzleArgs['SystemMap'], any>;
        OutputSystemMap: Record<keyof T_PuzzleArgs['SystemMap'], any>;
    }>(): Puzzle<T_PuzzleArgs>;
    /**初始化拼图 */
    $init(): void;
    $registeCoreComponents(key: number, component: clzz<IBaseComponent>): void;
    /**注册组件 */
    registeCoreComponents(key: number, component: clzz<IBaseComponent>): void;
    /**无状态函数注册 */
    registeStateless<T extends keyof T_PuzzleArgs['FuncMap']>(statelessType: T, statelessId: number, func: T_PuzzleArgs['FuncMap'][T]): void;
    /**全局特性注册 */
    registeGlobalFeature(type: CoreGlobalFeatureType, gf: IGlobalFeature): void;
    /**配置预制体注册 */
    registeConfigPrefab(featureId: number, featureFP: clzz<BaseConfigPrefab<any>>): void;
    /**创建配置预制体 */
    createConfigPrefab<T extends BaseConfigPrefab<any>>(featureId: number): T;
    /**初始化拼图块
     * @param puzzleType 拼图块类型
     * @param puzzleId 拼图块Id
     * @param config 拼图块配置 */
    initPuzzlePiece(puzzleType: string, puzzleId: string, config: DataConfig): void;
    /**初始化拼图块全局特性
     * @param puzzleType 拼图块类型
     * @param config 全局特性配置 */
    initPuzzlePieceGlobalFeature(puzzleType: string, config: GlobalFeatureData): void;
    /**获取拼图块全局特性 */
    getPuzzlePieceGlobalFeature(type: CoreGlobalFeatureType): IGlobalFeature;
    /**获取拼图块全局特性集合 */
    getPuzzlePieceGlobalFeatureDataArr(puzzleType: string): readonly (readonly DeepReadonly<GlobalFeatureData>[])[];
    /**销毁拼图块
     * @param puzzleType 拼图块类型
     * @param puzzleId 拼图块Id
     * */
    disposePuzzlePiece(puzzleType: string, puzzleId: string): void;
    /**注册外部函数集 */
    registeExternFunc(type: string, func: Function): void;
    /**运行外部函数 */
    executeExternFunc(type: string, ...args: any): any;
    /**运行拼图块
     * @param puzzleType 拼图块类型
     * @param puzzleId 拼图块Id
     * @param inputArgs 外部全局数据传参
     * @return 返回全局数据传参(内部可能会修改外部全局数据)
     */
    runPuzzlePiece<T extends keyof T_PuzzleArgs['SystemMap']>(puzzleType: T, puzzleId: string, inputArgs: T_PuzzleArgs['InputSystemMap'][T]): T_PuzzleArgs['OutputSystemMap'][T];
    /**运行逻辑组件 */
    runLogicComp(compId: LogicCompId): void;
    /**运行特性组件 */
    runFeatureComp(compId: FeatureCompId): boolean;
    /**运行条件组件 */
    runConditionComp(compId: ConditionCompId): boolean;
    /**运行公式组件 */
    runFormulaComp(compId: FormulaCompId, args: number[]): number;
    /**运行无状态函数 */
    runStatelessFunc<T extends keyof T_PuzzleArgs['FuncMap']>(statelessType: T, statelessId: number, inputArgs: T_PuzzleArgs['InputFuncMap'][T]): T_PuzzleArgs['OutputFuncMap'][T];
    /**获取数据集 */
    getData<T extends keyof T_PuzzleArgs['IGlobalData']>(type: T): T_PuzzleArgs['GlobalDataTypeMap'][T];
    /**设置数据集 */
    setData<T extends keyof T_PuzzleArgs['IGlobalData']>(type: T, val: T_PuzzleArgs['GlobalDataTypeMap'][T]): void;
    /**获取静态数据集 */
    getStaticData<T extends keyof T_PuzzleArgs['IStaticData']>(type: T): DeepReadonly<T_PuzzleArgs['StaticMap'][T]>;
    /**设置静态数据集 */
    setStaticData<T extends keyof T_PuzzleArgs['IStaticData']>(type: T, val: T_PuzzleArgs['StaticMap'][T]): void;
    /**获取时间戳:s */
    getTime(): number;
    /**获取拼图块Id */
    getPuzzleId(): string;
    /**重置拼图块重映射 */
    resetPuzzlePieceMap(puzzleType: string, puzzleId: string): void;
    /**生成拼图块配置 */
    createPuzzlePieceConfig<T extends PuzzleDataConfig>(puzzleType: string, puzzleId: string, puzzleConfigPrefabId: number, data: T): DataConfig;
    /**获取块接口键值 */
    private $getPuzzlePieceKey;
    /**获取无状态函数键值 */
    private $getStatelessKey;
    /**获取需要移除的组件列表 */
    private $getRemoveCompArr;
    /**获取组件实例 */
    private $getCompByCompId;
    /**获取组件配置 */
    private $getCompTvo;
    /**创建组件 */
    private $createComp;
    /**销毁组件 */
    private $disposeComp;
}
type TPuzzleArgs<T extends {
    /**无状态函数 */
    FuncMap: Record<string, StateLessFunction>;
    /**无状态函数输入参数映射 */
    InputFuncMap: Record<keyof T['FuncMap'], any>;
    /**无状态函数输出参数映射 */
    OutputFuncMap: Record<keyof T['FuncMap'], any>;
    /**全局数据类型 */
    IGlobalData: Record<string, any>;
    /**全局数据类型映射 */
    GlobalDataTypeMap: Record<keyof T['IGlobalData'], any>;
    /**静态数据类型 */
    IStaticData: Record<string, any>;
    /**静态数据类型映射 */
    StaticMap: Record<keyof T['IStaticData'], any>;
    /**拼图块 */
    SystemMap: Record<string, string>;
    /**拼图块输入参数映射 */
    InputSystemMap: Record<keyof T['SystemMap'], any>;
    /**拼图块输出参数映射 */
    OutputSystemMap: Record<keyof T['SystemMap'], any>;
}> = T;
type CorePuzzle = TPuzzleArgs<{
    FuncMap: {};
    InputFuncMap: {};
    OutputFuncMap: {};
    IGlobalData: {};
    GlobalDataTypeMap: {};
    IStaticData: {};
    StaticMap: {};
    SystemMap: {};
    InputSystemMap: {};
    OutputSystemMap: {};
}>;

/**基础组件 */
interface IBaseComponent {
    /**上下文实例 */
    context?: Puzzle<CorePuzzle>;
    /**组件id */
    compId: number;
    /**组件类型 */
    compType: number;
    /**组件模板 */
    compTvo: any;
    /**组件创建时调用-禁止调用,除非你知道你在做什么 */
    $create?(): any;
    /**组件销毁时调用-禁止调用,除非你知道你在做什么 */
    $dispose?(): any;
}

/**逻辑组件 */
interface ILogicComponent extends IBaseComponent {
    /**逻辑执行 */
    execute(): void;
}

/**外部适配组件 */
interface IAdapterComponent extends ILogicComponent {
    /**适配输入数据 */
    inputData(args: any): any;
    /**适配输出数据 */
    outputData(): any;
}

interface IPuzzle<T_PuzzleArgs extends TPuzzleArgs<CorePuzzle>> {
    /**初始化 */
    init(): any;
    /**初始化拼图系统静态数据 */
    initStaticData(data: any): any;
    /**初始化拼图块 */
    initPuzzlePiece(puzzleType: string, puzzleId: string, config: DataConfig): any;
    /**初始化拼图系统-动态配置 */
    initPuzzlePieceDynamic(puzzleType: string, puzzleId: string, data: any, puzzleConfigPrefabId?: number): any;
    /**销毁拼图系统 */
    disposePuzzlePiece(puzzleType: string, puzzleId: string): any;
    /**注册外部函数集 */
    registeExternFunc(type: string, func: Function): any;
    /**运行拼图节点 */
    runPuzzlePiece<T extends keyof T_PuzzleArgs['SystemMap']>(puzzleType: T, puzzleId: string, inputArgs: T_PuzzleArgs['InputSystemMap'][T]): T_PuzzleArgs['OutputSystemMap'][T];
}

/**算法类型枚举 */
declare enum AlgoClassTypeConst {
    /**===========================类型枚举============================== */
    /** 随机 */
    AlgoRandom = 0,
    /** 熵增1 */
    AlgoEntropyIncrease1 = 1,
    /** 熵增3 */
    AlgoEntropyIncrease3 = 2,
    /** 简单难题 */
    AlgoSimpleHard = 3,
    /** 填空消除 */
    AlgoFillInTheBlank = 4,
    /** 熵减*/
    AlgoEntropyReduction = 5,
    /** 困难难题*/
    AlgoHard = 6,
    /** 随机无死题*/
    AlgoRandomNoDeath = 7,
    /** 直觉难题*/
    AlgoInstinctHard = 8,
    /** 致死题*/
    AlgoDeath = 9,
    /**===========================实例枚举============================== */
    /**底板-随机 */
    algo_random = 10001,
    /**底板-首次随机 */
    algo_random_first = 10002,
    /**底板-固定块池随机 */
    algo_random_fix_block = 10003,
    /**底板-真随机 */
    algo_real_random = 10004,
    /**底板-熵增1 */
    algo_entropy_increase1 = 11001,
    /**底板-熵增3 */
    algo_entropy_increase3 = 12001,
    /**子母熵增3 */
    algo_entropy_increase3_zimu = 12002,
    /**底板-简单难题 */
    algo_simple_hard = 13001,
    /**底板-填空消除，边数不考虑消除 */
    algo_fill_in_the_blank = 14001,
    /**填空消除，边数考虑消除 */
    algo_fill_in_the_blank_clear = 14002,
    /**全组合填空消除9 */
    algo_fill_in_the_blank_id_9 = 14003,
    /**全组合填空消除21 */
    algo_fill_in_the_blank_id_21 = 14004,
    /**填空消除位置连贯（填空消除连贯） 不考虑消除*/
    algo_fill_in_the_blank_by_pos = 14005,
    /**填空消除位置连贯（填空消除连贯） 考虑消除*/
    algo_fill_in_the_blank_clear_by_pos = 14006,
    /**旅行填空消除*/
    algo_fill_in_the_blank_journey = 14007,
    /**高效消除*/
    algo_fill_in_the_blank_high_effect = 14008,
    /**全组合填空消除70 */
    algo_fill_in_the_blank_id_70 = 14009,
    /**消除爽 */
    algo_fill_in_the_blank_clear_cool = 14010,
    /**连贯旅行填空消除 */
    algo_fill_in_the_blank_journey_by_pos = 14011,
    /**空洞消除算法*/
    algo_fill_in_the_hole = 14012,
    /**快速填空消除算法*/
    algo_quick_fill = 14013,
    /**方块之战，冒险模式用*/
    algo_fill_in_the_blank_adv = 14015,
    /**底板-熵减 */
    algo_entropy_reduction = 15001,
    /**底板-困难难题*/
    algo_hard = 16001,
    /**简单直觉难题*/
    algo_simple_instinct_hard = 16002,
    /**简单死亡*/
    algo_simple_death = 16003,
    /**底板-高回报困难难题*/
    algo_hard_high_effect = 16004,
    /**更困难难题*/
    algo_hard_hard = 16005,
    /**十字解困难难题*/
    algo_hard_cross = 16006,
    /**困难难题扩展1*/
    algo_hard_extend1 = 16007,
    /**困难难题扩展2*/
    algo_hard_extend2 = 16008,
    /**困难难题二进制*/
    algo_hard_binary = 16009,
    /**底板-随机无死亡 */
    algo_random_no_death = 17001,
    /**底板-直觉难题 */
    algo_instinct_hard = 18001,
    /**底板-致死题 */
    algo_death = 19001,
    /**复活算法 */
    algo_revive = 20001,
    /**复活算法变种，选择可消除边数小的块 */
    algo_revive_clear = 20002,
    /**清屏算法 */
    algo_clear_board = 21001,
    /**终止刷分算法 */
    algo_stop_refresh_record = 22001,
    /**连续两轮出块相似处理算法 */
    algo_same_more_block = 23001
}

/**坐标 */
interface Pos {
    /**行 */
    row: number;
    /**类 */
    col: number;
}
declare enum AlgoStepBlockAction {
    /** 块池增加方块*/
    ADD = 1,
    /** 块池去除方块*/
    DEL = 2,
    /** 块池重置*/
    RESET = 3,
    /** 生成序列块池*/
    SEQ = 4,
    /** 块池排序*/
    SORT = 5,
    /** 权重块池*/
    WEIGHT = 6
}
declare enum AlgoStepAction {
    /** 清屏算法检测单块是否可清屏*/
    CLEAR_BOARD_CHECK_ONE = 1,
    /** 子母熵增3算法盘面放入第一个块*/
    ZI_MU_CHECK_ONE = 2,
    /** 子母熵增3算法盘面放入第二个块*/
    ZI_MU_CHECK_TWO = 3,
    /** 子母熵增3算法盘面放入第三个块*/
    ZI_MU_CHECK_THREE = 4,
    /** 终止刷分算法盘面放入第一个块*/
    STOP_REFRESH_CHECK_ONE = 5,
    /** 终止刷分算法盘面放入第二个块*/
    STOP_REFRESH_CHECK_TWO = 6,
    /** 终止刷分算法盘面放入第三个块*/
    STOP_REFRESH_CHECK_THREE = 7,
    /** 简单直觉难题算法盘面放入第一个块*/
    SIMPLE_INSTINCT_HARD_CHECK_ONE = 8,
    /** 简单直觉难题算法盘面放入第二个块*/
    SIMPLE_INSTINCT_HARD_CHECK_TWO = 9,
    /** 简单直觉难题算法盘面放入第三个块*/
    SIMPLE_INSTINCT_HARD_CHECK_THREE = 10,
    /** 简单死亡算法盘面放入第一个块*/
    SIMPLE_DEATH_CHECK_ONE = 11,
    /** 简单死亡算法盘面放入第二个块*/
    SIMPLE_DEATH_CHECK_TWO = 12,
    /** 简单死亡算法盘面放入第三个块*/
    SIMPLE_DEATH_CHECK_THREE = 13,
    /** 随机算法根据块权重池生成块*/
    RANDOM_WEIGHT = 14,
    /** 随机算法生成块*/
    RANDOM = 15,
    /** 高效消除根据收集钻石多少来生成块*/
    HIGH_EFFECT = 16,
    /**填空消除算法根据边数来选块*/
    LESS_EQUAL_EDGE = 17,
    /**填空消除算法根据边数来选块,边数相等情况下根据位置来选择*/
    LESS_EQUAL_EDGE_POS = 18,
    /**填空消除算法根据是否可消除来选块，都可消除根据边数来选择*/
    LESS_EQUAL_EDGE_CLEAR = 19,
    /**填空消除算法根据是否可消除来选块，都可消除根据边数来选择，边数也相等根据位置来选择*/
    LESS_EQUAL_EDGE_CLEAR_POS = 20,
    /**填空消除算法根据边数来选块*/
    LESS_EDGE = 21,
    /** 直觉难题算法盘面放入第一个块*/
    INSTINCT_HARD_CHECK_ONE = 22,
    /** 直觉难题算法盘面放入第二个块*/
    INSTINCT_HARD_CHECK_TWO = 23,
    /** 直觉难题算法盘面放入第三个块*/
    INSTINCT_HARD_CHECK_THREE = 24,
    /** 困难难题算法盘面放入第一个块*/
    HARD_CHECK_ONE = 25,
    /** 困难难题算法盘面放入第二个块*/
    HARD_CHECK_TWO = 26,
    /** 困难难题算法盘面放入第三个块*/
    HARD_CHECK_THREE = 27,
    /** 全组合算法选择最优组合*/
    FILL_SORT_COM = 28,
    /** 检查组合是否可放入，第一个块放入*/
    CHECK_COM_1 = 29,
    /** 检查组合是否可放入，第二个块放入*/
    CHECK_COM_2 = 30,
    /** 检查组合是否可放入，第三个块放入*/
    CHECK_COM_3 = 31,
    /** 消除爽算法，查找最优方块和位置*/
    CLEAR_COOL = 32,
    /** 检查当前盘面可放入块*/
    CAN_PUT = 33,
    /** 空洞填空消除空洞数*/
    HOLE = 34
}
interface AlgoStepRecord {
    /**方块 */
    id: number;
    /**放置位置 */
    pos: Pos;
    /**描述 */
    action: AlgoStepAction;
    /**计算数据 */
    data: any;
}
interface AlgoStepRecordCom {
    /**方块 */
    ids: number[];
    /**放置位置 */
    poses: Pos[];
    /**描述 */
    action: AlgoStepAction;
    /**计算数据 */
    data: any;
}
interface AlgoStepsRecord {
    /**算法枚举 */
    algoType: number;
    /**方块下标 */
    indexs: number[];
    /**当前最优数据 */
    cur: number[];
    /**每步数据 */
    steps: (AlgoStepRecord | AlgoStepRecordCom)[];
    /**块池下标 */
    blockIndexs: number[];
}
interface AlgoBlockRecord {
    /**块池 */
    blocks: number[];
    /**描述 */
    action: AlgoStepBlockAction;
}

/**错误码枚举 */
declare enum ErrorCode {
    /**算法成功 */
    Success = 0,
    /**算法产生降级 */
    Down = 1,
    /**算法超时 */
    OverTime = 2
}

declare class AlgoExecuteAdapterComponent implements IAdapterComponent {
    context: Puzzle<TAlgoPuzzleArgs>;
    compId: number;
    compType: number;
    compTvo: AlgoExecuteAdapterComponentTvo;
    /**输入数据 */
    inputData(args: AlgoExecuteAdapterInputData): void;
    /**输出数据 */
    outputData(): AlgoExecuteAdapterOutputData;
    /**执行逻辑 */
    /**async*/ execute(): void;
}
interface AlgoExecuteAdapterComponentTvo {
    /**执行逻辑id */
    readonly logicCompId: LogicCompId;
}
interface AlgoExecuteAdapterInputData {
    /**0 低端机  1 中端机  2 高端机*/
    readonly device: number;
    /**历史最高分 */
    readonly highRecordGrade: number;
    /**当前分 */
    readonly currRecordGrade: number;
    /**当前模式 */
    readonly mode: number;
    /**关卡数 */
    readonly chapterNum: number;
    /**当前盘面 */
    readonly board: number[][];
    /**块池组，目前单元测试用 */
    blocksGroup: number[][];
    /**需要移除的块集合 */
    readonly filterBlocks: number[];
    /**需要增加的块集合 */
    readonly addBlocks: number[];
    /**上轮出块 */
    readonly lastBlockIds: number[];
    /**算法结果方块 */
    readonly blockIds: number[];
    /**算法实际名字 */
    readonly blockNames: string[];
    /**放置最优位置 */
    readonly blockPoses: Pos[];
    /**计算超时时间配置 */
    readonly overTime: number;
    /**扩展参数 */
    readonly extra: any;
}
interface AlgoExecuteAdapterOutputData {
    /**算法枚举 */
    algoType: AlgoClassTypeConst;
    /**算法使用块池 */
    blockGroup: number[][];
    /**算法结果方块 */
    blockIds: number[];
    /**算法实际名字 */
    blockNames: string[];
    /**放置最优位置 */
    blockPoses: Pos[];
    /**执行的算法列表 */
    algoList: AlgoClassTypeConst[];
    /** 计算过程 */
    stepsRecords: AlgoStepsRecord[];
    /** 块池改变过程 */
    blockRecords: AlgoBlockRecord[][];
    /**算法错误码 */
    errorCode: ErrorCode;
    /**额外数据，某些算法需要返回数据到业务 */
    extra: any;
}

/**拼图块类型枚举 */
declare enum AlgoPuzzleTypeConst {
    /**执行算法 */
    AlgoExecute = "AlgoExecute"
}
type AlgoSystemNodeMap = {
    [AlgoPuzzleTypeConst.AlgoExecute]: string;
};
type AlgoSystemNodeInputMap = {
    [AlgoPuzzleTypeConst.AlgoExecute]: AlgoExecuteAdapterInputData;
};
type AlgoSystemNodeOutputMap = {
    [AlgoPuzzleTypeConst.AlgoExecute]: AlgoExecuteAdapterOutputData;
};

/**权重块池表数据结构 */
interface RatioBlock {
    /**分数区间 */
    score: string;
    /**块ID */
    id: number;
    /**权重 */
    weight: number;
    /**颜色 */
    color: number;
}

interface AlgoSelectInputArgs {
    /**当前盘面 */
    board: number[][];
    /**块池 */
    readonly blocks: number[];
}
interface ZiMuAlgoSelectInputArgs {
    /**当前盘面 */
    board: number[][];
    /**块池1 */
    readonly blocks1: number[];
    /**块池2 */
    readonly blocks2: number[];
}
interface AlgoSelectOutputArgs {
    /**块id */
    id: number;
    /**块行 */
    row: number;
    /**块列 */
    col: number;
}
interface AlgoSelectComOutputArgs {
    /**块id */
    ids: number[];
    /**块行 */
    rows: number[];
    /**块列 */
    cols: number[];
}
interface RandomByWeightAlgoSelectInputArgs {
    readonly ratioBlocks: RatioBlock[];
}
interface FillAlgoSelectInputArgs {
    /**当前盘面 */
    board: number[][];
    /**块池 */
    readonly blocks: number[];
    /**边数 */
    readonly edge: number;
}
interface FillPosAlgoSelectInputArgs {
    /**当前盘面 */
    board: number[][];
    /**块池 */
    readonly blocks: number[];
    /**边数 */
    readonly edge: number;
    /**位置，下标0为y 下标1为x */
    readonly pos: number[];
}
interface FillPosAlgoSelectOutputArgs {
    /**块id */
    id: number;
    /**块行 */
    row: number;
    /**块列 */
    col: number;
    /**最优块数量 */
    betterBlockCount: number;
    /**最优块放置位置数量 */
    betterSpaceCount: number;
}
interface FillSortAlgoSelectComInputArgs {
    /**当前盘面 */
    board: number[][];
    readonly blocks: any[];
    readonly select: number;
    readonly putWay: number;
    readonly ratios: number[];
    readonly isNear: boolean;
    readonly isClear: boolean;
    readonly remainNum: number;
}

/**无状态函数类型 */
declare const enum StatelessType {
    AlgoSelect = 1,
    AlgoSelectCom = 2,
    AlgoSelectRandomByWeight = 3,
    AlgoSelectFillSort = 4,
    AlgoSelectFillPos = 5,
    AlgoSelectFill = 6,
    AlgoSelectZiMu = 7,
    AlgoSelectFillHole = 8
}
type keys = keyof StatelessInputMap;
type StatelessFuncMap = {
    [key in keys]: (inputArgs: DeepReadonly<StatelessInputMap[key]>) => StatelessOutputMap[key];
};
type StatelessInputMap = {
    [StatelessType.AlgoSelect]: AlgoSelectInputArgs;
    [StatelessType.AlgoSelectCom]: AlgoSelectInputArgs;
    [StatelessType.AlgoSelectRandomByWeight]: RandomByWeightAlgoSelectInputArgs;
    [StatelessType.AlgoSelectFillSort]: FillSortAlgoSelectComInputArgs;
    [StatelessType.AlgoSelectFillPos]: FillPosAlgoSelectInputArgs;
    [StatelessType.AlgoSelectFill]: FillAlgoSelectInputArgs;
    [StatelessType.AlgoSelectZiMu]: ZiMuAlgoSelectInputArgs;
    [StatelessType.AlgoSelectFillHole]: AlgoSelectInputArgs;
};
type StatelessOutputMap = {
    [StatelessType.AlgoSelect]: AlgoSelectOutputArgs;
    [StatelessType.AlgoSelectCom]: AlgoSelectComOutputArgs;
    [StatelessType.AlgoSelectRandomByWeight]: AlgoSelectOutputArgs;
    [StatelessType.AlgoSelectFillSort]: AlgoSelectComOutputArgs;
    [StatelessType.AlgoSelectFillPos]: FillPosAlgoSelectOutputArgs;
    [StatelessType.AlgoSelectFill]: AlgoSelectOutputArgs;
    [StatelessType.AlgoSelectZiMu]: AlgoSelectComOutputArgs;
    [StatelessType.AlgoSelectFillHole]: AlgoSelectOutputArgs;
};

/**数据类型枚举 */
interface IGlobalDataType {
    /**==================================静态数据==================================== */
    /**==================================外部==================================== */
    /**低中高机型枚举 */
    device: number;
    /**历史最高分 */
    highRecordGrade: number;
    /**当前分 */
    currRecordGrade: number;
    /**无尽还是旅行 */
    mode: number;
    /**当前关卡数 */
    chapterNum: number;
    /**当前盘面 */
    board: number[][];
    /**初始盘面 */
    initBoard: number[][];
    /**上一把实际出的块的坐标 */
    lastBlockIds: number[];
    /**当前块池组 */
    blocksGroup: number[][];
    /**需要移除的块 */
    filterBlocks: number[];
    /**需要额外增加的块 */
    addBlocks: number[];
    /**额外数据 */
    extra: any;
    /**==================================内部==================================== */
    /**当前块池 */
    blocks: number[];
    /**当前权重块池 */
    ratioBlocks: any;
    /**实际出题 */
    blockNames: string[];
    /**实际出的块 */
    blockIds: number[];
    /**实际出的块的坐标 */
    blockPoses: Pos[];
    /**计算出块中间数据 */
    algoExecuteData: any;
    /**错误码 */
    errorCode: ErrorCode;
    /**返回额外数据 */
    extraOutput: any;
}

/**===================自动生成禁止修改========================= */

declare enum GlobalDataTypeConst {
    /**==================================静态数据==================================== */
    /**==================================外部==================================== */
    /**低中高机型枚举 */
    device = "device",
    /**历史最高分 */
    highRecordGrade = "highRecordGrade",
    /**当前分 */
    currRecordGrade = "currRecordGrade",
    /**无尽还是旅行 */
    mode = "mode",
    /**当前关卡数 */
    chapterNum = "chapterNum",
    /**当前盘面 */
    board = "board",
    /**初始盘面 */
    initBoard = "initBoard",
    /**上一把实际出的块的坐标 */
    lastBlockIds = "lastBlockIds",
    /**当前块池组 */
    blocksGroup = "blocksGroup",
    /**需要移除的块 */
    filterBlocks = "filterBlocks",
    /**需要额外增加的块 */
    addBlocks = "addBlocks",
    /**额外数据 */
    extra = "extra",
    /**==================================内部==================================== */
    /**当前块池 */
    blocks = "blocks",
    /**当前权重块池 */
    ratioBlocks = "ratioBlocks",
    /**实际出题 */
    blockNames = "blockNames",
    /**实际出的块 */
    blockIds = "blockIds",
    /**实际出的块的坐标 */
    blockPoses = "blockPoses",
    /**计算出块中间数据 */
    algoExecuteData = "algoExecuteData",
    /**错误码 */
    errorCode = "errorCode",
    /**返回额外数据 */
    extraOutput = "extraOutput"
}
type GlobalDataTypeConstMap = {
    [GlobalDataTypeConst.device]: number;
    [GlobalDataTypeConst.highRecordGrade]: number;
    [GlobalDataTypeConst.currRecordGrade]: number;
    [GlobalDataTypeConst.mode]: number;
    [GlobalDataTypeConst.chapterNum]: number;
    [GlobalDataTypeConst.board]: number[][];
    [GlobalDataTypeConst.initBoard]: number[][];
    [GlobalDataTypeConst.lastBlockIds]: number[];
    [GlobalDataTypeConst.blocksGroup]: number[][];
    [GlobalDataTypeConst.filterBlocks]: number[];
    [GlobalDataTypeConst.addBlocks]: number[];
    [GlobalDataTypeConst.extra]: any;
    [GlobalDataTypeConst.blocks]: number[];
    [GlobalDataTypeConst.ratioBlocks]: any;
    [GlobalDataTypeConst.blockNames]: string[];
    [GlobalDataTypeConst.blockIds]: number[];
    [GlobalDataTypeConst.blockPoses]: Pos[];
    [GlobalDataTypeConst.algoExecuteData]: any;
    [GlobalDataTypeConst.errorCode]: ErrorCode;
    [GlobalDataTypeConst.extraOutput]: any;
};

/**配置枚举 */
declare const enum ConfigIdConst {
    /**
     * @title 框架-算法拼图块框架配置
     */
    PC_AlgoPuzzle = 1,
    /**
    * @title 预处理层-执行一个算法
    * @param algoType 应该执行的算法类型 默认值 AlgoClassTypeConst.algo_random  底板-随机
    * @description 执行一个 algoType  指定的算法
    */
    FC_ExecuteAlgoSystem = 100001,
    /**
     * @title 块池层-生成顺序块池列表
     * @param startBlockId 开始块 默认值 2
     * @param endBlockId 开始块 默认值 42
     * @description 生成顺序块池列表[startBlockId,endBlockId]闭区间 包含startBlockId和endBlockId的值
     */
    FC_CreateOrderBlocks = 200001,
    /**
     * @title 块池层-限制块池-限制小块
     * @param score 默认值 2000
     * @param chapterNum 默认值 12
     *  @param blocks 默认值 [6, 27, 28, 15, 3, 2, 5, 4, 37, 38, 39, 40, 41]
     * @description 无尽模式下当前分数>=score分执行限制小块
     * @description 关卡模式下关卡数>=chapterNum关执行限制小块
     * @description 去除 blocks 中指定的块
     */
    FC_LimitBlocks = 200002,
    /**
     * @title 块池层-将块池排序
     * @param sort 默认值 SortTypeConst.Order  按顺序排序
     * @description 将块池按 sort 排序
     */
    FC_AlgoBlockSort = 200003,
    /**
    * @title 块池层-重置块池
    * @param blocks 没有 默认值
    * @description 配置 blocks 指定的块数组 把块池重置
    */
    FC_BlocksReset = 200004,
    /**
    * @title 块池层-根据权重表生成普通块池和权重块池 满足条件的话用普通块池  不满足条件的话用权重块池
    * @param score 默认值 2000
    * @param chapter 默认值 12
    *  @param removeBlockArr 默认值 [1, 6, 27, 28, 15, 3, 2, 5, 4, 37, 38, 39, 40, 41]
    * @description 无尽模式下当前分数>= score 分移除 普通块池中 removeBlockArr 指定的块  块并使用普通块池
    * @description 关卡模式下关卡数>= chapter 关移除 普通块池中 removeBlockArr 指定的块  块并使用普通块池
    */
    FC_CreateWeightBlocks = 200005,
    /**
     * @title 块池层-将块池推入块池组
     */
    FC_PushBlocksToGroup = 200006,
    /**
     * @title 组合层层-块池按规则生成组合，因为组合层可能耗时过长，废弃
     * @param blockGroup 块池组下标 默认值 0
     * @param blockTypes 筛选块池类型 默认值 [BlockFilterType.PutNoClear, BlockFilterType.PutNoClear, BlockFilterType.NoPut]
     * @param blockGroup 截取前几个组合 默认值 Number.MAX_VALUE(全部)
     * @description 将块池按blockTypes 指定的规则生成组合
     */
    FC_BlocksComs = 300001,
    /**
     * @title 组合层层-块池按规则生成子母组合，废弃
     * @description 块池按规则生成子母组合
     */
    FC_ZiMuBlocksComs = 300002,
    /**
     * @title 组合层层-全组合填空块池组合，废弃
     * @param blockGroup 块池组下标 默认值 0
     * @param select 选择方式  默认值 FillSortComBoway.OLD_PARALLEL  老填空方式选择，并列选择
     * @param isNear 是否考虑贴边率 默认值 flase
     * @param isClear 是否考虑消除 默认值 flase
     * @param remainNum 组合数 默认值 5
     * @description 全组合填空块池组合
     */
    FC_BlockFillSortComs = 300003,
    /**
     * @title 选块层-填空消除选块 选择边数小的块
     * @param index 第几个块下标 默认值 0
     * @param blockGroup 使用块池组下标 默认值 0
     * @param statelessId 选块使用的无状态函数枚举 默认值 StatelessId.AlgoSelect.LessEdge 选择边数小的块
     * @description 按照 statelessId 指定的无状态函数 使用blockGroup 指定的块池 选择第 index 个块
     */
    FC_FillAlgoSelect = 400001,
    /**
     * @title 选块层-填空消除选块 选择边数小的块，考虑所放位置
     * @param index 第几个块下标 默认值 0
     * @param blockGroup 使用块池组下标 默认值 0
     * @param statelessId 选块使用的无状态函数枚举 默认值 StatelessId.AlgoSelect.LessEdgeAndPos 选择边数小的块，考虑所放位置
     * @description 按照 statelessId 指定的无状态函数 使用blockGroup 指定的块池 选择第 index 个块
     */
    FC_FillPosAlgoSelect = 400002,
    /**
     * @title 选块层-全组合填空消除选块
     * @param putWay 放置方式 默认值 FillSortPutWWay.A1B1C1  三个块都是基于初始盘面放置
     * @param ratios 边数权重 默认值 [1, 1, 1]
     * @param isNear 是否考虑贴边率 默认值 false
     * @param isClear 是否考虑消除 默认值 false
     * @param remainNum 组合数 默认值 5
     * @param statelessId 选块使用的无状态函数枚举 默认值 StatelessId.AlgoSelectFillSort.FillSort 选择一个全组合块
     * @description 根据规则 选择一个全组合块
     */
    FC_FillSortAlgoSelect = 400003,
    /**
     * @title 选块层-困难难题<直觉难题> 选块
     * @param statelessId 选块使用的无状态函数枚举 默认值 StatelessId.AlgoSelectCom.Hard 困难难题
     * @description 根据 statelessId 指定的无状态函数 选择困难难题或者直觉难题
     */
    FC_HardAlgoSelect = 400004,
    /**
     * @title 选块层-熵增3 选块
     * @param index 第几个块下标 默认值 0
     * @param blockGroup 使用块池组下标 默认值 0
     * @param statelessId 选块使用的无状态函数枚举 默认值 StatelessId.AlgoSelect.CanPut 选择一个可放不可消除的块
     * @description 按照 statelessId 指定的无状态函数 使用 blockGroup 指定的块池 选择第 index 个块
     */
    FC_Entropy3AlgoSelect = 400005,
    /**
     * @title 选块层-子母熵增3
     * @param statelessId 选块使用的无状态函数枚举 默认值 StatelessId.AlgoSelectCom.ZiMuCom  子母熵增3
     * @description 按照 statelessId 指定的无状态函数 选择子母熵增3
     */
    FC_ZiMuEntropy3AlgoSelect = 400006,
    /**
     * @title 选块层-随机选块
     * @param index 第几个块下标 默认值 0
     * @param blockGroup 使用块池组下标 默认值 0
     * @param statelessId 选块使用的无状态函数枚举 默认值 StatelessId.AlgoSelect.Random 选择随机块
     * @description 按照 statelessId 指定的无状态函数 使用 blockGroup 指定的块池 选择第 index 个块
     */
    FC_RandomAlgoSelect = 400007,
    /**
     * @title 选块层-底板-首次随机选块
     * @param index 第几个块下标 默认值 0
     * @param blockGroup 使用块池组下标 默认值 0
     * @param statelessId 选块使用的无状态函数枚举 默认值 StatelessId.AlgoSelect.LessEdge 选择边数小的块
     * @description 按照 statelessId 指定的无状态函数 使用 blockGroup 指定的块池 选择第 index 个块
     */
    FC_FirstRandomAlgoSelect = 400008,
    /**
     * @title 选块层-随机无死亡选块
     * @param statelessId 选块使用的无状态函数枚举 默认值 StatelessId.AlgoSelectCom.CanPut  组合可放，随机无死用到
     * @description 按照 statelessId 指定的无状态函数 选择 随机无死亡
     */
    FC_RandomNoDieSelect = 400009,
    /**
    * @title 选块层-复活算法选快
    * @param statelessId 选块使用的无状态函数枚举 默认值 StatelessId.AlgoSelect.LessEqualEdge  选择边数小的块,选块时会考虑边数相等的情况
    * @description 按照 statelessId 指定的无状态函数 选择 复活块
    */
    FC_ReviveSelect = 400010,
    /**
    * @title 清屏算法选块
    * @param statelessId 选块使用的无状态函数枚举 默认值 StatelessId.AlgoSelect.ClearBoard  选择可以清屏的单个块或者两个块
    * @description 按照 statelessId 指定的无状态函数 选择 清屏块
    */
    FC_ClearBoardAlgoSelect = 400011,
    /**
     * @title 通用算法选块
     * @param statelessId 通用选块组件，通过配置无状态函数实现横向扩展选块逻辑，选择后无后续逻辑，所以为通用算法组件
     * @description 按照 statelessId 指定的无状态函数 选择 通用算法块
     */
    FC_CommonAlgoSelect = 400012,
    /**
     * @title 通用选取组合算法选块
     * @param statelessId 通用选块组件，通过配置无状态函数实现横向扩展选块逻辑，选择后无后续逻辑，所以为通用算法组件
     * @description 按照 statelessId 指定的无状态函数 选择 通用算法块
     */
    FC_CommonComAlgoSelect = 400013,
    /**
     * @title 空洞算法选块
     * @param statelessId 选块使用的无状态函数枚举 默认值 StatelessId.AlgoSelectFillHole.maxEmptyDiff 选中空洞差值最大的块
     * @description 按照 statelessId 指定的无状态函数 选择 通用算法块
     */
    FC_FillHoleAlgoSelect = 400014,
    /**
     * @title 方块之战算法选块
     * @param statelessId 选块使用的无状态函数枚举 默认值 StatelessId.AlgoSelectFill.LessEdgeAndClear
     * @description 按照 statelessId 指定的无状态函数 选择 通用算法块
     */
    FC_FillAdvAlgoSelect = 400015,
    /**
     * @title 位置层-按规则调整生成的块位置
     * @param pos 调整该特性预制体位置类型 默认值 PosTypeConst.LMR 左中右
     * @description 按 pos 指定的规则调整生成的块位置
     */
    FC_BlockPos = 500001,
    /**
     * @title 兜底层-同样3个块处理
     * @description 中间块换成 随机可放id
     */
    FC_BlockSafetySame = 600001,
    /**
     * @title 兜底层-与上轮同样3个块处理
     * @description 中间块换成 随机可放id
     */
    FC_BlockSafetyLast = 600002,
    /**
       * @title 兜底层-3个块都不可放置处理
       * @description 中间块和第三个块换成 随机可放id
       */
    FC_BlockSafetyNoPut = 600003,
    /**
       * @title 兜底层-若存在空块，使用随机无死亡替换
       */
    FC_BlockSafetyCheck = 600004,
    /**
     * @title 打印层-打印算法出块数据
     */
    FC_AlgoPrint = 700001
}

interface PC_AlgoPuzzlePrefabData {
    /**配置 */
    config: PC_AlgoPuzzlePrefabDataConfig;
    data: any;
}
interface PC_AlgoPuzzlePrefabDataConfig {
    /**预处理层-顺序处理特性 */
    PreLayer?: ConfigIdConst[];
    /**块池层-顺序处理特性 */
    BlockLayer: ConfigIdConst[];
    /**选块层-顺序处理特性 */
    AlsoSelectLayer: ConfigIdConst[];
    /**位置层-顺序处理特性 */
    PosLayer?: ConfigIdConst[];
    /**兜底层-顺序处理特性 */
    BasicLayer?: ConfigIdConst[];
    /**打印层-顺序处理特性 */
    PrintLayer?: ConfigIdConst[];
}

declare enum StaticDataTypeConst {
    /**==================================静态数据==================================== */
    /**块池权重表 */
    ratioJson = "ratioJson"
}
type StaticDataTypeConstMap = {
    [StaticDataTypeConst.ratioJson]: RatioBlock[];
};

/**静态数据类型枚举 */
interface IStaticDataType {
    /**==================================静态数据==================================== */
    /**块池权重表 */
    ratioJson: RatioBlock[];
}

type TAlgoPuzzleArgs = TPuzzleArgs<{
    FuncMap: StatelessFuncMap;
    InputFuncMap: StatelessInputMap;
    OutputFuncMap: StatelessOutputMap;
    IGlobalData: IGlobalDataType;
    GlobalDataTypeMap: GlobalDataTypeConstMap;
    IStaticData: IStaticDataType;
    StaticMap: StaticDataTypeConstMap;
    SystemMap: AlgoSystemNodeMap;
    InputSystemMap: AlgoSystemNodeInputMap;
    OutputSystemMap: AlgoSystemNodeOutputMap;
}>;
/**算法系统实现 */
declare class AlgoPuzzle implements IPuzzle<TAlgoPuzzleArgs> {
    puzzle: Puzzle<TAlgoPuzzleArgs>;
    init(): void;
    initStaticData(data: any): void;
    /**初始化全局特性 */
    initGlobalFeature(): void;
    initPuzzlePiece(puzzleType: string, puzzleId: string, config: DataConfig): void;
    initPuzzlePieceDynamic(puzzleType: string, puzzleId: string, data: PC_AlgoPuzzlePrefabData, puzzleConfigPrefabId?: ConfigIdConst): void;
    initPuzzlePieceGlobalFeature(puzzleType: string, config: GlobalFeatureData): void;
    disposePuzzlePiece(puzzleType: string, puzzleId: string): void;
    registeExternFunc(type: string, func: Function): void;
    executeExternFunc(type: string, args: any): any;
    runPuzzlePiece<T extends keyof TAlgoPuzzleArgs['SystemMap']>(puzzleType: T, puzzleId: string, inputArgs: TAlgoPuzzleArgs['InputSystemMap'][T]): TAlgoPuzzleArgs['OutputSystemMap'][T];
}
declare const algoPuzzle: AlgoPuzzle;

export { AlgoExecuteAdapterComponent, type AlgoExecuteAdapterComponentTvo, type AlgoExecuteAdapterInputData, type AlgoExecuteAdapterOutputData, GlobalDataTypeConst, AlgoPuzzleTypeConst as PuzzleTypeConst, algoPuzzle };
