import AwardComponent from '../components/special/AwardComponent';
import TargetComponent from '../components/special/TargetComponent';
import { TargetType } from '../define/BoardDefine';
import { AwardCondition } from '../define/EcsDefine';
import { ECSEvent } from '../GameEvent';
import { ECSRuleBase, IECSRuleConfig } from './core/ECSRuleBase';

/**
 * 消除分数计算规则
 */
export class EliminationScoreCalculatorRule extends ECSRuleBase<IECSRuleConfig, ECSEvent.GameEvent.ELIMINATION> {
    public bindEventData() {
        return ECSEvent.GameEvent.ELIMINATION;
    }

    canExecute(): boolean {
        return true;
    }

    execute(): void {
        // 在此实现分数计算逻辑
        const world = this.world;
        const eliminationInfo = this.eventData;
        const boardEntity = eliminationInfo.boardEntity;
        const award = world.getComponent(boardEntity, AwardComponent);
        if (!award) return;
        const clearCounts = eliminationInfo.clearRow.size + eliminationInfo.clearCol.size;

        if (clearCounts <= 0) return;
        // 计算得分
        const score = clearCounts * (5 + clearCounts * 5);
        const targets = world.getComponent(world.query([TargetComponent])[0], TargetComponent).targets;
        award.triggerList.forEach((trigger) => {
            if (trigger.condition !== AwardCondition.FullLine || trigger.target !== TargetType.Score) return;
            //发送播放单次得分动画，通知单次得分规则
            world.eventBus.emit(ECSEvent.GameEvent.SINGLE_SCORE_EFFECT, {
                score: score,
                boardEntity,
                eliminateCount: clearCounts,
                position: eliminationInfo.clearAreaCenterPos,
            });
            const target = targets.find((t) => t.key === TargetType.Score);
            if (!target) return; //如果没有得分目标，直接返回
            const current = Math.min(target.to, target.current + score);
            const targetParam = { key: TargetType.Score, current, to: target.to };
            world.eventBus.emit(ECSEvent.GameEvent.FRESH_TARGET, targetParam);
            world.eventBus.emit(ECSEvent.GameEvent.TARGET_CHANGE, { key: TargetType.Score, change: score });
        });
    }
}
