import { ECSEvent } from '../../GameEvent';
import { ECSViewBase } from '../ECSViewBase';

/**复活视图 */
export class ReviveECSView extends ECSViewBase {

    private adState: boolean = false;
    private btnState: boolean = false;
    private numId: number = 0;
    private numTween: cc.Tween = null;

    protected initView() {
        console.log("Revive == render");

        this.adState = false;
        this.btnState = false;

        this.ui.buttonOk.opacity = 0;
        this.ui.buttonOk.scale = 0.1;
        this.numId = 0;

        if (this.ui.aniProgressBar) {
            this.ui.aniProgressBar.getComponent(cc.ProgressBar).progress = 0;
        }

        this.resetReviveView();
        if (this.ui.aniNum) {
            this.ui.aniNum.getComponent(dragonBones.ArmatureDisplay).playAnimation("newAnimation", 1);
        }
        this.setNumBoneTimeScale(1);
        cc.tween(this.ui.buttonOk).to(0.2, { opacity: 255, scale: 1 }, { easing: cc.easing.backOut }).call(() => {
            this.ui.effect.active = true;
            if (this.ui.effect) {
                this.ui.effect.getComponent(dragonBones.ArmatureDisplay).playAnimation("english", 0);
            }
        }).delay(0.6).call(() => {
            this.updateSprNum();
            this.btnState = true;
            if (this.ui.effect) {
                this.ui.effect.active = true;
            }
        }).start();
    }
    protected addEvents() {
        if (this.ui && this.ui.effect) {
            this.ui.effect.on(cc.Node.EventType.TOUCH_END, this.onClick, this);
        }
    }
    protected removeEvents() {
        if (this.ui && this.ui.effect) {
            this.ui.effect.off(cc.Node.EventType.TOUCH_END, this.onClick, this);
        }
    }
    protected disposeView() {

    }

    private updateSprNum() {
        this.numId++;
        const progressNum: number = this.ui.aniProgressBar.getComponent(cc.ProgressBar).progress;
        console.log('updateSprNum ', this.numId);
        if (this.numId >= 6) {
            this.handleNumIdExceeded();
        } else {
            this.updateProgress(progressNum);
        }
    }

    private handleNumIdExceeded() {
        if (!this.adState) {
            this.renderWorld.destroyECSView(this);
            this.logicWorld.pushInputCmd({
                event: ECSEvent.GameEvent.REVIVE_FAILED,
                data: null,
            });
            // 发送关闭事件
            // falcon.EventManager.dispatchModuleEvent(new E_Revive_Close());
        }
        this.resetReviveView();
    }

    private updateProgress(progressNum: number) {
        // 进度条音效
        // audioInfo.play(AudioConfig.time);

        this.numTween = cc.tween(this.ui.aniProgressBar.getComponent(cc.ProgressBar))
            .to(1, { progress: progressNum + 1 / 5 })
            .call(() => {
                this.updateSprNum(); // 递归调用合并后的函数
            })
            .start();
    }

    onClick() {
        console.log('yjf__点击复活11111');

        this.logicWorld.pushInputCmd({
            event: ECSEvent.GameEvent.REVIVE_SUCCESS,
            data: null,
        });

        this.adState = true;
        this.resetReviveView();
        this.renderWorld.destroyECSView(this);
        // 发送广告事件
        // falcon.EventManager.dispatchModuleEvent(new E_Revive_Click());
    }

    setNumBoneTimeScale(value: number) {
        if (this.ui.aniNum) {
            this.ui.aniNum.getComponent(dragonBones.ArmatureDisplay).timeScale = value;
        }
    }

    resetReviveView() {
        this.setNumBoneTimeScale(0);
        if (this.numTween) {
            this.numTween.stop();
            this.numTween = null;
        }
        if (this.ui.effect) {
            this.ui.effect.active = false;
        }
    }
}
