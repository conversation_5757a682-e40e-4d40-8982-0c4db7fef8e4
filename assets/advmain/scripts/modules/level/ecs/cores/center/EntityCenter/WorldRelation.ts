import { ISerializable } from '../../ISerializable';
import { EntityCenter } from './EntityCenter';
/**
 * 关系名称枚举
 * 定义具体的业务关系名称
 */
export enum RelationName {
    /** 父子关系 */
    PARENT_CHILD = 'parent_child',
    /** cell 遮罩 */
    SLOT_MASK = 'slot_mask',
    /**格子-块 */
    SLOT_CELL = 'slot_cell',
}
/**
 * 关系类型枚举
 * 定义关系的结构模式
 */
enum RelationType {
    /** 1对多关系：一个源可以有多个目标，一个目标只能有一个源 */
    ONE_TO_MANY = 'one_to_many',
}

/**
 * 关系配置
 * 定义每种关系名称对应的关系类型
 */
const RELATION_CONFIG: Record<RelationName, RelationType> = {
    [RelationName.PARENT_CHILD]: RelationType.ONE_TO_MANY, // 父子：1对多
    [RelationName.SLOT_MASK]: RelationType.ONE_TO_MANY, // 父子：1对多
    [RelationName.SLOT_CELL]: RelationType.ONE_TO_MANY, // 父子：1对多
};

/**
 * 关系数据结构
 */
interface RelationData {
    /** 关系key  */
    relationKey: string;
    /** 源实体ID */
    source: number;
    /** 目标实体ID */
    target: number;
}

/**
 * 世界关系管理器
 */
export class WorldRelation implements ISerializable {
    // ==================== 私有属性 ====================

    /** 关联的实体中心实例 */
    private entityCenter: EntityCenter;

    /** 顺序关系存储：关系名称 -> 源实体ID -> 目标实体ID数组 */
    public sourceToTargets: Record<string, Record<number, number[]>> = {} as Record<RelationName, Record<number, number[]>>;

    /** 反向顺序关系存储：关系名称 -> 目标实体ID -> 源实体ID数组 */
    public targetToSources: Record<RelationName, Record<number, number[]>> = {} as Record<RelationName, Record<number, number[]>>;

    // ==================== 构造函数 ====================

    /**
     * 构造函数
     * @param world 关联的世界实例
     */
    constructor(entityCenter: EntityCenter) {
        this.entityCenter = entityCenter;
    }

    // ==================== 关系管理 ====================
    private getRelationKey(name: RelationName, key?: string): string {
        if (!key) return name;
        return `${name}_${key}`;
    }
    /**
     * 设置关系
     * @param source 源实体ID
     * @param target 目标实体ID
     * @param name 关系名称
     * @param key 关系key-给这段关系固定一个索引key,后续可以根据索引key快速拿到
     * @returns 是否成功设置
     */
    addRelation(source: number, target: number, name: RelationName, key?: string): boolean {
        return this.addRelationInternal(source, target, name, key);
    }

    /**
     * 在指定位置设置关系
     * @param source 源实体ID
     * @param target 目标实体ID
     * @param name 关系名称
     * @param index 插入位置索引
     * @param key 关系key-给这段关系固定一个索引key,后续可以根据索引key快速拿到
     * @returns 是否成功设置
     */
    addRelationAt(source: number, target: number, name: RelationName, index: number, key?: string): boolean {
        return this.addRelationInternal(source, target, name, key, index);
    }

    /**
     * 内部关系设置方法
     * @param source 源实体ID
     * @param target 目标实体ID
     * @param name 关系名称
     * @param key 关系key
     * @param index 插入位置索引，如果不指定则追加到末尾
     * @returns 是否成功设置
     */
    private addRelationInternal(source: number, target: number, name: RelationName, key?: string, index?: number): boolean {
        // 验证实体是否存在
        if (!this.entityCenter.entities.has(source) || !this.entityCenter.entities.has(target)) {
            console.warn(`设置关系失败：实体不存在 source=${source}, target=${target}`);
            return false;
        }
        //有key的关系也会同时设置主体关系
        if (key) this.addRelationInternal(source, target, name);
        const relationKey = this.getRelationKey(name, key);

        const relationType = RELATION_CONFIG[name];

        // 对于1对多关系，如果目标实体已有其他源，先移除旧关系
        if (relationType === RelationType.ONE_TO_MANY) {
            const existingSources = this.getSources(target, name, key);
            if (existingSources.length > 0) {
                // 移除目标实体的所有现有关系
                existingSources.forEach((existingSource) => {
                    this.removeRelation(existingSource, target, name, key);
                });
            }
        }

        // 建立正向关系
        let relationMap = this.sourceToTargets[relationKey];
        if (!relationMap) {
            relationMap = {} as Record<number, number[]>;
            this.sourceToTargets[relationKey] = relationMap;
        }

        let targets = relationMap[source];
        if (!targets) {
            targets = [];
            relationMap[source] = targets;
        }

        // 根据是否指定索引来决定插入方式
        if (index !== undefined) {
            targets.splice(index, 0, target);
        } else {
            targets.push(target);
        }

        // 建立反向关系
        let reverseMap = this.targetToSources[relationKey];
        if (!reverseMap) {
            reverseMap = {} as Record<number, number[]>;
            this.targetToSources[relationKey] = reverseMap;
        }

        let sources = reverseMap[target];
        if (!sources) {
            sources = [];
            reverseMap[target] = sources;
        }
        sources.push(source);

        return true;
    }
    /**
     * 移除关系
     * @param source 源实体ID
     * @param target 目标实体ID
     * @param name 关系名称
     * @param key 关系key
     * @returns 是否成功移除
     */
    removeRelation(source: number, target: number, name: RelationName, key?: string): boolean {
        // 移除正向关系
        const relationKey = this.getRelationKey(name, key);
        const relationMap = this.sourceToTargets[relationKey];
        if (relationMap) {
            const targets = relationMap[source];
            if (targets) {
                targets.splice(targets.indexOf(target), 1);
                if (targets.length === 0) {
                    delete relationMap[source];
                }
            }
        }

        // 移除反向关系
        const reverseMap = this.targetToSources[relationKey];
        if (reverseMap) {
            const sources = reverseMap[target];
            if (sources) {
                sources.splice(sources.indexOf(source), 1);
                if (sources.length === 0) {
                    delete reverseMap[target];
                }
            }
        }

        return true;
    }

    /**
     * 移除实体的所有关系-性能待优化
     * @param entityId 实体ID
     */
    removeEntityRelations(entityId: number): void {
        // 遍历所有关系名称
        for (const relationKey in this.sourceToTargets) {
            // 移除作为源实体的关系
            const relationMap = this.sourceToTargets[relationKey];
            const targets = relationMap[entityId];
            if (targets) {
                targets.forEach((target) => {
                    const reverseMap = this.targetToSources[relationKey];
                    if (reverseMap) {
                        const sources = reverseMap[target];
                        if (sources) {
                            sources.splice(sources.indexOf(entityId), 1);
                            if (sources.length === 0) {
                                delete reverseMap[target];
                            }
                        }
                    }
                });
                delete relationMap[entityId];
            }
        }

        // 遍历所有反向关系
        for (const relationKey in this.targetToSources) {
            // 移除作为目标实体的关系
            const reverseMap = this.targetToSources[relationKey];
            const sources = reverseMap[entityId];
            if (sources) {
                sources.forEach((source) => {
                    const relationMap = this.sourceToTargets[relationKey];
                    if (relationMap) {
                        const targets = relationMap[source];
                        if (targets) {
                            targets.splice(targets.indexOf(entityId), 1);
                            if (targets.length === 0) {
                                delete relationMap[source];
                            }
                        }
                    }
                });
                delete reverseMap[entityId];
            }
        }
    }

    // ==================== 查询方法 ====================

    /**
     * 获取源实体列表（查询父亲/所有者等）
     * @param target 目标实体ID
     * @param name 关系名称
     * @param key 关系key
     * @returns 源实体ID数组
     */
    getSources(target: number, name: RelationName, key?: string): number[] {
        const relationKey = this.getRelationKey(name, key);
        const reverseMap = this.targetToSources[relationKey];
        if (!reverseMap) return [];

        const sources = reverseMap[target];
        return sources ? sources : [];
    }

    /**
     * 获取目标实体列表（查询儿子/物品等）
     * @param source 源实体ID
     * @param name 关系名称
     * @param key 关系key
     * @returns 目标实体ID数组
     */
    getTargets(source: number, name: RelationName, key?: string): number[] {
        const relationKey = this.getRelationKey(name, key);
        const relationMap = this.sourceToTargets[relationKey];
        if (!relationMap) return [];

        const targets = relationMap[source];
        return targets ? targets : [];
    }

    /**
     * 检查是否存在关系
     * @param source 源实体ID
     * @param target 目标实体ID
     * @param name 关系名称
     * @returns 是否存在关系
     */
    hasRelation(source: number, target: number, name: RelationName, key?: string): boolean {
        const relationKey = this.getRelationKey(name, key);
        const relationMap = this.sourceToTargets[relationKey];
        if (!relationMap) return false;

        const targets = relationMap[source];
        return targets ? targets.includes(target) : false;
    }

    /**
     * 检查实体是否有目标（是否有儿子/物品等）
     * @param source 源实体ID
     * @param name 关系名称
     * @returns 是否有目标
     */
    hasTargets(source: number, name: RelationName, key?: string): boolean {
        const relationKey = this.getRelationKey(name, key);
        const relationMap = this.sourceToTargets[relationKey];
        if (!relationMap) return false;

        const targets = relationMap[source];
        return targets ? targets.length > 0 : false;
    }

    /**
     * 检查实体是否有源（是否有父亲/所有者等）
     * @param target 目标实体ID
     * @param name 关系名称
     * @returns 是否有源
     */
    hasSources(target: number, name: RelationName, key?: string): boolean {
        const relationKey = this.getRelationKey(name, key);
        const reverseMap = this.targetToSources[relationKey];
        if (!reverseMap) return false;

        const sources = reverseMap[target];
        return sources ? sources.length > 0 : false;
    }

    // ==================== 生命周期管理 ====================

    // ==================== 序列化方法 ====================

    /**
     * 序列化关系数据
     * @returns 序列化后的关系数据
     */
    serializeRelations(): RelationData[] {
        const relations: RelationData[] = [];

        // 遍历所有关系名称
        for (const relationKey in this.sourceToTargets) {
            const relationMap = this.sourceToTargets[relationKey];
            // 遍历每个源实体的目标集合
            for (const source in relationMap) {
                const targets = relationMap[parseInt(source)];
                // 遍历每个目标实体
                for (const target of targets) {
                    relations.push({
                        relationKey: relationKey,
                        source: parseInt(source),
                        target: target,
                    });
                }
            }
        }

        return relations;
    }

    /**
     * 反序列化关系数据
     * @param relations 关系数据数组
     */
    deserializeRelations(relations: RelationData[]): void {
        // 先清理现有关系
        this.sourceToTargets = {} as Record<RelationName, Record<number, number[]>>;
        this.targetToSources = {} as Record<RelationName, Record<number, number[]>>;

        // 按关系名称分组
        const relationsByType = new Map<string, RelationData[]>();

        for (const relation of relations) {
            if (!relationsByType.has(relation.relationKey)) {
                relationsByType.set(relation.relationKey, []);
            }
            relationsByType.get(relation.relationKey)!.push(relation);
        }

        // 批量重建关系
        for (const [relationKey, relationList] of relationsByType) {
            const relationMap = {} as Record<number, number[]>;
            const reverseMap = {} as Record<number, number[]>;

            // 一次性处理所有同类型关系
            for (const relation of relationList) {
                // 建立正向关系
                if (!relationMap[relation.source]) {
                    relationMap[relation.source] = [];
                }
                relationMap[relation.source].push(relation.target);

                // 建立反向关系
                if (!reverseMap[relation.target]) {
                    reverseMap[relation.target] = [];
                }
                reverseMap[relation.target].push(relation.source);
            }

            // 直接设置到内部存储
            this.sourceToTargets[relationKey] = relationMap;
            this.targetToSources[relationKey] = reverseMap;
        }
    }
    // ==================== ISerializable接口实现 ====================

    /**
     * 序列化关系数据
     * @returns 可JSON序列化的关系数据
     */
    serialize(): any {
        return {
            sourceToTargets: this.sourceToTargets,
            targetToSources: this.targetToSources,
        };
    }

    /**
     * 反序列化关系数据
     * @param data 序列化数据对象
     */
    deserialize(data: any): void {
        this.sourceToTargets = data.sourceToTargets || {};
        this.targetToSources = data.targetToSources || {};
    }
}
