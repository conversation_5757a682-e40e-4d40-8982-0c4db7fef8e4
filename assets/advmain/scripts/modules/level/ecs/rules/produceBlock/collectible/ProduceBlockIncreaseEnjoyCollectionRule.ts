import BoardComponent from '../../../components/board/BoardComponent';
import ProduceBlockListComponent from '../../../components/ProduceBlockListComponent';
import TargetComponent from '../../../components/special/TargetComponent';
import { CellType, TargetType } from '../../../define/BoardDefine';
import { ECSEvent } from '../../../GameEvent';
import { TargetParam } from '../../../GameEventData';
import { ECSRuleBase, IECSRuleConfig } from '../../core/ECSRuleBase';

/**
 * 改变收集物策略，增加凑堆收集物的消除爽感
 */
export class ProduceBlockIncreaseEnjoyCollectionRule extends ECSRuleBase<IECSRuleConfig, ECSEvent.GameEvent.PRODUCE_BLOCK_END> {
    public bindEventData() {
        return ECSEvent.GameEvent.PRODUCE_BLOCK_END;
    }
    canExecute(): boolean {
        const produceBlockLists = this.world.query([ProduceBlockListComponent]);
        if (!produceBlockLists || produceBlockLists.length === 0) {
            console.warn('没有找到 ProduceBlockListComponent 实体，无法执行目标类型规则');
            return false;
        }
        return true;
    }
    execute(): void {
        const targetParams = this.getTargetParams();
        if (targetParams.length === 0) {
            // 没有可用的收集类型，直接返回
            return;
        }
        if (this.findPileUpPos() == -1) {
            return;
        }
        const needType = this.getNeedCollectTypes(targetParams);
        const produceBlockListEntity = this.world.query([ProduceBlockListComponent])[0];
        const produceBlocks = this.world.getComponent(produceBlockListEntity, ProduceBlockListComponent).produceBlockList;
        if (produceBlocks.length === 0) {
            // 没有可用的收集类型，直接返回
            return;
        }
        produceBlocks[0].shape.shape.forEach((rc, index) => {
            if (!produceBlocks[0].shape.types) {
                produceBlocks[0].shape.types = [];
            }
            produceBlocks[0].shape.types[index] = needType;
        });
    }

    private getTargetParams(): TargetParam[] {
        const world = this.world;
        const cellHandler = world.utilCenter.cellUtil;
        const targetParams = world.getComponent(world.query([TargetComponent])[0], TargetComponent).targets.filter((t) => cellHandler.isCell(t.key));
        return targetParams;
    }

    private getNeedCollectTypes(targetParams: TargetParam[]): CellType {
        const world = this.world;
        const results: { needValue: number; type: TargetType }[] = [];
        for (let i = 0, len = targetParams.length; i < len; i++) {
            const t = targetParams[i];
            const needValue = t.to - t.current;
            if (needValue > 0) {
                results.push({ needValue, type: t.key as TargetType });
            }
        }
        if (results.length === 0) {
            // 没有可用的收集类型，直接返回
            return TargetType.Normal;
        }

        // 按照需要的值从大到小排序，返回需要值最大的类型
        results.sort((a, b) => b.needValue - a.needValue);
        return results[0].type as CellType;
    }

    private findPileUpPos(): number {
        const combinedBoardOccupy = this.world.utilCenter.boardUtil.combineBoardOccupy(
            this.world.query([BoardComponent]).map((entity) => this.world.utilCenter.boardUtil.getBoardOccupy(entity)),
        );
        const rows = combinedBoardOccupy.length;
        const cols = combinedBoardOccupy[0]?.length || 0;
        const rowCondiArr = new Array(rows).fill(0);
        const colCondiArr = new Array(cols).fill(0);
        const idxArr: Record<number, number> = {};
        // 行向检测
        for (let i = 0; i < rows; i++) {
            let emptyCount = 0;
            let curY = -1;
            for (let j = 0; j < cols; j++) {
                const val = combinedBoardOccupy[i][j];
                // 获取对应的type
                if (val < 0) {
                    emptyCount++;
                    if (curY == -1) {
                        curY = j;
                    } else {
                        // 存在第二个空格
                        curY = -1;
                        break;
                    }
                } else if (val >= 100 && val <= 1000) {
                    rowCondiArr[i]++;
                }
                if (emptyCount > 1) break;
            }
            if (emptyCount === 1 && rowCondiArr[i] >= 3) {
                //满足只有一个空格，且收集物大于等于3个。
                const key = i * cols + curY;
                if (idxArr[key]) {
                    idxArr[key] += rowCondiArr[i];
                } else {
                    idxArr[key] = rowCondiArr[i];
                }
            }
        }
        // 列向检测
        for (let j = 0; j < cols; j++) {
            let emptyCount = 0;
            let curX = -1;
            for (let i = 0; i < rows; i++) {
                const val = combinedBoardOccupy[i][j];
                if (val < 0) {
                    emptyCount++;
                    if (curX == -1) {
                        curX = i;
                    } else {
                        // 存在第二个空格
                        curX = -1;
                        break;
                    }
                } else if (val >= 100 && val <= 1000) {
                    colCondiArr[i]++;
                }
            }
            if (curX != -1 && colCondiArr[j] >= 3) {
                //满足只有一个空格，且收集物大于等于3个。
                const key = curX * cols + j;
                if (idxArr[key]) {
                    idxArr[key] += colCondiArr[j];
                } else {
                    idxArr[key] = colCondiArr[j];
                }
            }
        }
        // 遍历idxArr,找出数值最大的一个。
        let maxValue = -Infinity;
        let maxKey: number = -1;

        for (const key in idxArr) {
            if (idxArr[key] > maxValue) {
                maxValue = idxArr[key];
                maxKey = parseInt(key, 10); // 将字符串key转换为数字
            } else if (idxArr[key] == maxValue) {
                //若相等，则随机一下看看是否更换id
                if (Math.random() > 0.5) {
                    maxKey = parseInt(key, 10);
                }
            }
        }
        return maxKey;
    }
}
