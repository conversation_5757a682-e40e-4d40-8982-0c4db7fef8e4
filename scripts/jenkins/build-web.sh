# cocos creator 会对编译的 cocos2d-js 文件产生缓存，正常情况下不需要删除，但是这里有自定义宏，因此，可能有问题，后面可以将这句去掉（原因是，现在已经不通过 predefine.js 文件修改 cocos2d-js 文件了）
rm -rf /Applications/Cocos/Creator/2.4.11/CocosCreator.app/Contents/Resources/engine/bin/.cache/web-mobile

# web 平台发布
excludedModules="['Collider','Dynamic Atlas','Geom Utils','Intersection','Label Effect','Mesh','MotionStreak','Physics','StudioComponent','TiledMap','VideoPlayer','3D','3D Primitive','SubContext','TypeScript Polyfill','3D Physics/cannon.js','3D Physics/Builtin','3D Physics Framework','3D Particle','SafeArea']"
build_options="platform=web-mobile;debug=true;excludedModules=$excludedModules"
/Applications/Cocos/Creator/2.4.11/CocosCreator.app/Contents/MacOS/CocosCreator \
  --path ./ \
  --build "$build_options" \
  --MACRO_MACRO_DEVTOOLS false \
  --MACRO_PLATFORM ${MACRO_PLATFORM} \
  --MACRO_ENV ${MACRO_ENV} \
  --MACRO_VERSION ${MACRO_VERSION} \
  --MACRO_AUTO false \
  --MACRO_ROBOT ${MACRO_ROBOT} \
  --MACRO_HOTUPDATE_OPEN false \
  --MACRO_TRAITS_SPLIT false \
  --MACRO_SHOW_FPS_LOG true

cp ./build-templates/jsb-link/main.refactored.js ${BUILD_OUTPUT_DIR}/web-mobile/main.js

# 重命名 assets 目录，兼容当前模式与融合模式
JSB_LINK=${BUILD_OUTPUT_DIR}/jsb-link
[ -d ${JSB_LINK}/assets/internal ] && mv ${JSB_LINK}/assets/internal ${JSB_LINK}/assets/refactoredInternal
[ -d ${JSB_LINK}/assets/main ] && mv ${JSB_LINK}/assets/main ${JSB_LINK}/assets/refactoredMain
[ -d ${JSB_LINK}/assets/resources ] && mv ${JSB_LINK}/assets/resources ${JSB_LINK}/assets/refactoredResources

# 上传到机器人
if [[ ${MACRO_ROBOT} == true ]]; then
    /usr/bin/expect ./scripts/jenkins/robot/upload-robot.exp
fi

# 复制
IP_ADDRESS=$(ifconfig | grep "inet " | grep -v 127.0.0.1 | awk '{print $2}' | head -n 1)
TARGET_URL=/Users/<USER>/blockblast-adv/${MACRO_PLATFORM}/${MACRO_ENV}/${IP_ADDRESS}
mkdir -p ${TARGET_URL}
cp -r ./build/web-mobile ${TARGET_URL}

# 统计 assets 目录下所有 *.ts 文件的代码总行数（去掉空行）
log "统计 TypeScript 代码行数..."
TS_LINES_COUNT=$(find ./assets -name "*.ts" -type f -exec grep -v '^\s*$' {} \; | wc -l | tr -d ' ')
log "TypeScript 代码总行数: ${TS_LINES_COUNT}"

# 获取当前时间
BUILD_TIME=$(date '+%Y-%m-%d %H:%M:%S')

# 计算打包时长（秒）
BUILD_END_TIME=$(date +%s)
BUILD_DURATION=$((BUILD_END_TIME - BUILD_START_TIME))

# 将打包时长转换为易读格式 (小时:分钟:秒)
BUILD_DURATION_FORMATTED=$(printf "%02d:%02d:%02d" $((BUILD_DURATION/3600)) $((BUILD_DURATION%3600/60)) $((BUILD_DURATION%60)))
GIT_COMMIT_MSG=$(git log -1 --pretty=%B)
# 创建配置信息的JSON字符串
CONFIG_JSON=$(cat << EOF
{
  "version": "${MACRO_VERSION}",
  "robot": ${MACRO_ROBOT},
  "showFPS": true,
  "buildDuration": "${BUILD_DURATION_FORMATTED}",
  "tsLinesCount": ${TS_LINES_COUNT},
  "GIT_COMMIT": "${GIT_COMMIT}",
  "GIT_BRANCH": "${GIT_BRANCH}",
  "BUILD_NUMBER": "${BUILD_NUMBER}",
  "BUILD_USER": "${BUILD_USER}",
  "ASSETS_DIFF_SIZE":"${ASSETS_DIFF_SIZE}",
  "BUILD_TIME":"${BUILD_TIME}",
  "local_robot_url":"http://**************:8867/robot/${MACRO_PLATFORM}/${MACRO_ENV}/${MACRO_VERSION}/",
  "remote_robot_url":"http://***********:8082/refactored_games/webgames/${MACRO_PLATFORM}/${MACRO_ENV}/${MACRO_VERSION}/",
  "MACRO_DEVTOOLS":"${MACRO_DEVTOOLS}"
}
EOF
)

# 将JSON字符串保存到临时文件
CONFIG_FILE=$(mktemp)
echo "${CONFIG_JSON}" > "${CONFIG_FILE}"

log "${CONFIG_FILE}"

# 通知 dingding
node ./scripts/dingtalk/dingtalk.js ${MACRO_PLATFORM} ${MACRO_ENV} "${CONFIG_FILE}"