

// import { OFFER_ALGORITHM_SDK_TYPE } from "../../config/AlgorithmSDKMatchConfig";
import { AlgoClassTypeConst } from "./core/puzzlecore";
import { AlgoExecuteAdapterInputData, AlgoExecuteAdapterOutputData, algoPuzzle, PuzzleTypeConst } from "./puzzle/algoPuzzle";
import { PuzzleCfg } from "./PuzzleCfg";

export interface InitArgsDef {
    /** 块池权重表*/
    ratioJson: any;
    /** 随机种子*/
    seed: number;
}

/**拼图工具 */
export class PuzzleUtil {

    public static init(data: InitArgsDef) {
        const algoTypes: number[] = [
            10001, 10002, 10003, 11001, 12001, 12002, 14001, 14002, 14003, 14004,
            14005, 14006, 14007, 14008, 14009, 14010, 14011, 16001, 16002, 16003,
            17001, 18001, 20001, 20002, 21001, 22001, 23001
        ];
        for (const algoType of algoTypes) {
            algoPuzzle.initPuzzlePieceDynamic(PuzzleTypeConst.AlgoExecute, '' + algoType, PuzzleCfg.getConfig(algoType));
        }
        algoPuzzle.initStaticData(data);
    }

    public static dispose() {
        // 释放算法配置
    }

    private static doFeature(args, algoList) {
        if (args.extra && args.extra.feature) {
            const {feature} = args.extra;
            if (feature.randomToRandomNoDie) {
                for (const i in algoList) {
                    if (algoList[i] == AlgoClassTypeConst.algo_random) {
                        algoList[i] = AlgoClassTypeConst.algo_random_no_death;
                    }
                }
            }
            if (feature.lianGuan) {
                for (const i in algoList) {
                    if (algoList[i] == AlgoClassTypeConst.algo_fill_in_the_blank) {
                        algoList[i] = AlgoClassTypeConst.algo_fill_in_the_blank_by_pos;
                    } else if (algoList[i] == AlgoClassTypeConst.algo_fill_in_the_blank_clear) {
                        algoList[i] = AlgoClassTypeConst.algo_fill_in_the_blank_clear_by_pos;
                    } else if (algoList[i] == AlgoClassTypeConst.algo_fill_in_the_blank_journey) {
                        algoList[i] = AlgoClassTypeConst.algo_fill_in_the_blank_journey_by_pos;
                    }
                }
            }
            if (feature.limitSmall) {

            }
        }
    }


    public static async offerTopic(args: AlgoExecuteAdapterInputData, algoList) {
        let otherArgs = JSON.parse(JSON.stringify(args));
        if (!Array.isArray(algoList)) {
            algoList = [algoList];
        }
        PuzzleUtil.doFeature(args, algoList);
        let result: AlgoExecuteAdapterOutputData;
        for (const algoType of algoList) {
            result = algoPuzzle.runPuzzlePiece(PuzzleTypeConst.AlgoExecute, '' + algoType, args);
            let emptyIndexs: number[] = [];
            for (let index in result.blockIds) {
                if (result.blockIds[index] == 0) {
                    emptyIndexs.push(Number(index));
                }
            }
            if (emptyIndexs.length > 0) {
                let algoTypes = [
                    AlgoClassTypeConst.algo_entropy_increase3, AlgoClassTypeConst.algo_fill_in_the_blank_clear_by_pos, AlgoClassTypeConst.algo_fill_in_the_hole,
                    AlgoClassTypeConst.algo_fill_in_the_blank_id_9, AlgoClassTypeConst.algo_fill_in_the_blank_id_21, AlgoClassTypeConst.algo_fill_in_the_blank_id_70,
                    AlgoClassTypeConst.algo_clear_board
                ];
                if (algoType == AlgoClassTypeConst.algo_clear_board && emptyIndexs.length == 3) {
                    break;
                }
                if (algoTypes.indexOf(algoType) != -1) {
                    if (otherArgs) {
                        if (otherArgs.extra) {
                            if (otherArgs.extra.feature) {
                                if (true) {
                                    // 无尽生效
                                    otherArgs.extra.feature.limitSmall = false;
                                }
                            }
                        }
                    }
                    let otherResult = algoPuzzle.runPuzzlePiece(PuzzleTypeConst.AlgoExecute, '' + AlgoClassTypeConst.algo_random_no_death, otherArgs);
                    let fillIndex = 0;
                    for (let index of emptyIndexs) {
                        if (algoType == AlgoClassTypeConst.algo_fill_in_the_hole) {
                            result.blockIds[index] = otherResult.blockIds[fillIndex];
                            result.blockNames[index] = otherResult.blockNames[fillIndex];
                            result.blockPoses[index].row = otherResult.blockPoses[fillIndex].row;
                            result.blockPoses[index].col = otherResult.blockPoses[fillIndex].col;
                        } else {
                            result.blockIds[index] = otherResult.blockIds[index];
                            result.blockPoses[index].row = otherResult.blockPoses[index].row;
                            result.blockPoses[index].col = otherResult.blockPoses[index].col;
                        }
                        fillIndex++;
                    }
                    for (let index in result.blockNames) {
                        if (result.blockNames[index] == '') {
                            result.blockNames[index] = '随机无死亡';
                        }
                    }
                }
            } else {
                break;
            }
        }
        if (result && result.blockIds) {
            result.blockIds = result.blockIds.filter(item => item !== 0);
        }
        result.algoList = algoList;
        return result;
    }
}