import { AlgoExecuteAdapterInputData } from "../list/algoSDK/puzzle/algoPuzzle";

class AlgorithmSDKArgsInfo {

    //获取拼图系统所需参数
    getArgs(boardOccupy: number[][]) {
        const args: AlgoExecuteAdapterInputData = {
            highRecordGrade: 0,     //最高分
            lastBlockIds: [-1, -1, -1],
            currRecordGrade: 0,  //当前分数
            mode: 1,     //当前游戏模式
            chapterNum: falcon.storage.getItem(`chapterNum`, 0) as number, //当前关卡
            board: boardOccupy,       //当前盘面
            blocksGroup: [],
            filterBlocks: [],
            blockIds: [0,0,0],
            blockNames: ['', '', ''],
            blockPoses: [{ row: 0, col: 0 }, { row: 0, col: 0 }, { row: 0, col: 0 }],
            overTime: 100,
            extra: {
                feature: {
                    // 随机无死
                    randomToRandomNoDie: false,
                    // 连贯
                    lianGuan: false,
                    // 限制小块
                    limitSmall: false
                },
                centerRow: 3.5,
                centerCol: 3.5
            },
            // 设备分层  0低端机  1中端  2高端
            device: 2

        };
        return args;
    }

    // 动态移除的块池
    private _filterBlocks: number[] = [];
    get filterBlocks(): number[] {
        return this._filterBlocks;
    }
    setFilterBlocks(value: number[]) {
        this._filterBlocks = value;
    }

    // 超时时间
    private _overTime: number = 200;
    get overTime(): number {
        return this._overTime;
    }
    setOverTime(value: number) {
        this._overTime = value;
    }

    // 预期块
    private _blockIds: number[] = [0, 0, 0];
    get blockIds(): number[] {
        return this._blockIds;
    }
    setBlockIds(value: number[]) {
        this._blockIds = value;
    }

    // 上轮出块点
    get blockCenterPos(): object {
        return falcon.storage.getItem(`chapterBlockCenterPos`, { x: 3.5, y: 3.5 });
    }
    setBlockCenterPos(value: object) {

        falcon.storage.setItem(`chapterBlockCenterPos`, value);
    }
}

export const algorithmSDKArgsInfo = new AlgorithmSDKArgsInfo();