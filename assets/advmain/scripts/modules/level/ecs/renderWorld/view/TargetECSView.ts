import TargetComponent from '../../components/special/TargetComponent';
import { TargetParam } from '../../GameEventData';
import { ECSEvent } from '../../GameEvent';
import { ECSViewBase, IECSViewConfig } from '../ECSViewBase';
import { TargetType } from '../../define/BoardDefine';

/**类型预设路径 */
const TYPE_PF_SRC = {
    [TargetType.Score]: 'prefabs/level/target/itemScore',
};

/**目标视图 */
export default class TargetECSView extends ECSViewBase<IECSViewConfig> {
    protected async initView() {
        const target = this.logicWorld.getComponent(this.logicEntityId, TargetComponent);
        let haveScore = false;
        const len = target.targets.length;
        for (let i = 0; i < len; i++) {
            const t = target.targets[i];
            const pf: cc.Prefab = await this.renderWorld.loadRes('', TYPE_PF_SRC[t.key] || 'prefabs/level/target/itemCollect', cc.Prefab);
            const node = cc.instantiate(pf);
            const key = `target${t.key}`;
            node.name = key;
            node.setParent(this.ui.targets);
            this.loopDo(this.node, (n) => (this.ui[`${key}_${n.name}`] = n));

            if (t.key === TargetType.Score) {
                haveScore = true;
                this.initScore(t);
            } else this.initCollect(t);
            this.freshTarget(t);
        }
        this.ui.targets.getComponent(cc.Layout).spacingX = haveScore ? 10 : 200 / len;
    }
    protected addEvents() {
        this.logicWorld.eventBus.on(ECSEvent.GameEvent.FRESH_TARGET, this.freshTarget, this);
        this.renderWorld.eventBus.onRenderEvent(ECSEvent.RenderEvent.FRESH_TARGET_COLLECT, this.freshCollect, this);
    }
    protected removeEvents() {
        this.logicWorld.eventBus.off(ECSEvent.GameEvent.FRESH_TARGET, this.freshTarget, this);
        this.renderWorld.eventBus.offRenderEvent(ECSEvent.RenderEvent.FRESH_TARGET_COLLECT, this.freshCollect, this);
    }
    protected disposeView() {}

    private freshTarget(t: TargetParam) {
        if (t.key === TargetType.Score) {
            this.freshScore(t);
        } else {
            this.freshCollect(t);
        }
    }

    private initScore(t: TargetParam) {
        this.ui[`target${t.key}_targetLab`].getComponent(cc.Label).string = t.to.toString();
    }
    private freshScore(t: TargetParam) {
        const key = `target${t.key}`;
        const lb = this.ui[`${key}_curScoreLab`].getComponent(cc.Label);
        const nowShow = +lb.string;
        const width = this.ui[`${key}_progressImg`].width;
        const target = +this.ui[`target${t.key}_targetLab`].getComponent(cc.Label).string;
        const sp = this.ui[`${key}_progressImg`].getComponent(cc.Sprite);
        const tag = 1000 + t.key;
        cc.Tween.stopAllByTag(tag);
        cc.tween({ now: nowShow })
            .tag(tag)
            .to(
                Math.min(0.2, (t.current - nowShow) * 0.02),
                { now: t.current },
                {
                    progress: (s, e, c, r) => {
                        const now = s + (e - s) * r;
                        const per = Math.min(1, now / target);
                        sp.fillRange = per;
                        this.ui[`${key}_curScoreBg`].x = width * per;
                        lb.string = parseInt(now) + '';
                    },
                },
            )
            .start();
    }
    private initCollect(t: TargetParam) {
        const key = `target${t.key}`;
        this.renderWorld.loadRes('level', `cell/${t.key}/img/iconBig`, cc.SpriteFrame).then((spf: cc.SpriteFrame) => {
            if (!cc.isValid(this.node)) return;
            this.ui[`${key}_gemImg`].getComponent(cc.Sprite).spriteFrame = spf;
        });
        this.ui[`${key}_numLab`].getComponent(cc.Label).string = '';
    }
    private freshCollect(t: TargetParam) {
        const key = `target${t.key}`;
        this.ui[`${key}_finishAnim`].active = false;
        const lbNode = this.ui[`${key}_numLab`];
        lbNode.getComponent(cc.Label).string = String(t.to - t.current);
        // 播放动画效果
        if (t.current < t.to) {
            lbNode.scale = 0.25;
            cc.Tween.stopAllByTarget(lbNode);
            cc.tween(lbNode).to(0.1, { scale: 0.6 }).to(0.1, { scale: 0.5 }).start();
        } else {
            lbNode.active = false;
            this.ui[`${key}_finishAnim`].active = true;
            this.ui[`${key}_finishAnim`].getComponent(dragonBones.ArmatureDisplay).playAnimation('newAnimation', 1);
        }

        const gemImg = this.ui[`${key}_gemImg`];
        gemImg.scale = 0.7;
        cc.Tween.stopAllByTarget(gemImg);
        cc.tween(gemImg)
            .to(0.1, { scale: 1.15 * 0.7 })
            .to(0.1, { scale: 0.7 })
            .start();
        const lightAnim = this.ui[`${key}_lightAnim`];
        lightAnim.active = true;
        lightAnim.angle += 90;
        lightAnim.opacity = 255;
        lightAnim.getComponent(dragonBones.ArmatureDisplay).playAnimation('HS_yellow', 1);
        cc.Tween.stopAllByTarget(lightAnim);
        cc.tween(lightAnim).delay(0.1).to(0.3, { opacity: 0 }).start();
    }
}
