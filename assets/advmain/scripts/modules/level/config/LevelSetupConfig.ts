/**设置的所有key */
export enum enSetupKeys {
    closeSelf = 'closeSelf',
    home = 'home',
    moreSettings = 'moreSettings',
    replay = 'replay',
    BGM = 'BGM',
    sound = 'sound',
}

/**设置界面内容节点位置 */
export interface ISetupInfoNodePos {
    /**整体bg宽度*/
    infoNodeWidth: number;
    /**顶部icon间距 */
    topLayout: { top: number; spaceX: number; nodeHeight: number };
    /**底部按钮layout */
    bottomLayout: { top: number; bottom: number; spaceY: number };
    /**分割线的宽度 */
    lineWidth: number;
}

export interface ISetupBaseItem {
    /**item key */
    key: enSetupKeys;
    /**顶部资源前缀*/
    topBtnPre?: string;
    /**底部资源前缀*/
    bottomBtnPre?: string;
    /**文本资源前缀*/
    txtImgPre?: string;
    /**数据状态 */
    value?: Boolean;
    /**是否显示红点 */
    redPoint?: Boolean;
}

/*
 * 设置内容配置
 */
export const SetupConfig = {
    topList: <enSetupKeys[]>['BGM', 'sound'],
    bottomList: <enSetupKeys[]>['home', 'moreSettings', 'replay'],
    /**按钮实例大小 */
    keysInstallSizes: <{ [key: string]: { width: number; height: number } }>{
        BGM: { width: 77, height: 87 },
        sound: { width: 99, height: 86 },
        home: { width: 625, height: 148 },
        moreSettings: { width: 625, height: 148 },
        replay: { width: 625, height: 148 },
    },
    /**节点位置信息 */
    nodePosData: <ISetupInfoNodePos>{
        /**整体bg宽度*/
        infoNodeWidth: 850,
        /**顶部icon间距 */
        topLayout: { top: 20, spaceX: 140, nodeHeight: 232 },
        /**底部按钮layout */
        bottomLayout: { top: 50, bottom: 105, spaceY: 25 },
        /**分割线的宽度 */
        lineWidth: 730,
    },
    /**资源路径 */
    resUrl: 'textures/level/setup/',
    /**顶部icon资源前缀 */
    topBtnPre: 'setting_btn_',
    /**底部按钮资源前缀 */
    bottomBtnPre: 'setting_icon_',
    /**文本图前缀 */
    txtImgPre: 'setting_txt_',

    /**预加载资源配置 */
    preloadResList: [
        {
            url: 'textures/level/setup/setting_btn_BGM',
            type: cc.SpriteFrame,
        },
        {
            url: 'textures/level/setup/setting_btn_sound',
            type: cc.SpriteFrame,
        },
        {
            url: 'textures/level/setup/setting_txt_BGM',
            type: cc.SpriteFrame,
        },
        {
            url: 'textures/level/setup/setting_txt_sound',
            type: cc.SpriteFrame,
        },
        {
            url: 'textures/level/setup/setting_icon_home',
            type: cc.SpriteFrame,
        },
        {
            url: 'textures/level/setup/setting_icon_moreSettings',
            type: cc.SpriteFrame,
        },
        {
            url: 'textures/level/setup/setting_icon_replay',
            type: cc.SpriteFrame,
        },
        {
            url: 'textures/level/setup/setting_txt_home',
            type: cc.SpriteFrame,
        },
        {
            url: 'textures/level/setup/setting_txt_moreSettings',
            type: cc.SpriteFrame,
        },
        {
            url: 'textures/level/setup/setting_txt_replay',
            type: cc.SpriteFrame,
        },
    ],
};
