import { ECSViewBase } from '../ECSViewBase';
import TargetComponent from '../../components/special/TargetComponent';
import { ECSEvent } from '../../GameEvent';
import { TargetType } from '../../define/BoardDefine';

/**
 * 目标信息显示视图
 * 用于在游戏开始时展示关卡目标
 */
export default class TargetInfoECSView extends ECSViewBase {
    private _itemNodes: cc.Node[] = [];

    protected async initView() {
        const { width, height } = cc.director.getWinSize();
        this.node.setContentSize(cc.size(width, height));
        const targetList = this.data.targetList || [];
        let formatTargetList: number[][] = [];
        for (let i = 0; i < targetList.length; i++) {
            const target = targetList[i];
            formatTargetList.push([target['key'], target['to']]);
        }
        const hasScoreTarget = formatTargetList.some(([targetType]) => targetType === TargetType.Score);

        await Promise.all([this.setTitle(hasScoreTarget), this.createTargetItems(formatTargetList)]);

        this.playShowAnimation();
    }

    private async setTitle(hasScoreTarget: boolean) {
        if (this.ui.tips_title) {
            // 从配置中获取标题图片路径，如果没有配置则使用默认路径
            const titleImagePath = hasScoreTarget ? 'textures/level/targetInfo/tips_score' : 'textures/level/targetInfo/tips_collection';
            const spriteFrame = (await this.renderWorld.loadRes('', titleImagePath, cc.SpriteFrame)) as cc.SpriteFrame;
            const sprite = this.ui.tips_title.getComponent(cc.Sprite);
            if (sprite && spriteFrame && cc.isValid(this.ui.tips_title)) {
                sprite.spriteFrame = spriteFrame;
            }
        }
    }

    private async createTargetItems(targets: number[][]) {
        if (!this.ui.item_bg) {
            console.warn('item_bg容器未找到');
            return;
        }

        const itemNodes = await Promise.all(targets.map(([targetType, targetCount]) => this.createTargetItem(targetType, targetCount)));

        itemNodes.filter(Boolean).forEach((itemNode) => {
            itemNode.setParent(this.ui.item_bg);
            this._itemNodes.push(itemNode);
        });
    }

    private async createTargetItem(targetType: number, targetCount: number): Promise<cc.Node> {
        const isScore = targetType === TargetType.Score;
        let resPath = isScore ? 'prefabs/level/targetInfo/itemTargetScore' : 'prefabs/level/targetInfo/itemTargetCollect';
        const prefab = (await this.renderWorld.loadRes('', resPath, cc.Prefab)) as cc.Prefab;
        if (!prefab) {
            console.warn('目标项预制体加载失败');
            return null;
        }
        let itemNode = cc.instantiate(prefab);
        if (isScore) {
            const scoreNode = itemNode.getChildByName('score');
            if (scoreNode) {
                const label = scoreNode.getComponent(cc.Label);
                if (label) {
                    label.string = `${targetCount}`;
                }
            }
        } else {
            const spriteFrame = (await this.renderWorld.loadRes('level', `cell/${targetType}/img/iconBig`, cc.SpriteFrame)) as cc.SpriteFrame;
            const gemImgNode = itemNode.getChildByName('gemImg');
            const numLabNode = itemNode.getChildByName('numLab');
            if (gemImgNode && spriteFrame) {
                const sprite = gemImgNode.getComponent(cc.Sprite);
                if (sprite) {
                    sprite.spriteFrame = spriteFrame;
                }
            }
            if (numLabNode) {
                const label = numLabNode.getComponent(cc.Label);
                if (label) {
                    label.string = `${targetCount}`;
                }
            }
        }
        const key = `item_${targetType}`;
        itemNode.name = key;
        return itemNode;
    }

    private playShowAnimation() {
        const { bg, tips_title, item_bg } = this.ui;

        // 初始化状态
        if (bg) bg.scaleY = 0.8;
        if (tips_title) tips_title.opacity = 0;

        // 设置收集物初始状态
        this._itemNodes.forEach((itemNode) => {
            Object.assign(itemNode, { scale: 0.7, opacity: 0 });
        });

        // bg动画序列
        if (bg) {
            cc.tween(bg)
                .to(0.13, { scaleY: 1 })
                .call(() => {
                    // 标题渐显
                    tips_title && cc.tween(tips_title).to(0.13, { opacity: 255 }).start();

                    // 禁用布局组件
                    const layout = item_bg?.getComponent(cc.Layout);
                    if (layout) layout.enabled = false;

                    // 收集物逐个显示
                    this._itemNodes.forEach((itemNode, index) => {
                        cc.tween(itemNode)
                            .delay(index * 0.1)
                            .to(0.13, { opacity: 255, scale: 1 })
                            .delay(0.8)
                            .call(() => this.flyToCollectPosition(itemNode, index))
                            .start();
                    });
                })
                .start();
        }
    }

    /**
     * 收集物飞到收集位置
     * @param itemNode 收集物节点
     */
    private flyToCollectPosition(itemNode: cc.Node, index: number) {
        const targets = this.logicWorld.getComponent(this.logicWorld.query([TargetComponent])[0], TargetComponent).targets;
        const curType = this.data.targetList[index]['key'];
        const target = targets.find((t) => t.key === curType);
        if (!target) return;

        const targetParam = { key: curType, current: 0, to: target.to };

        if (curType === TargetType.Score) {
            cc.tween(itemNode)
                .to(0.13, { scale: 1.5 })
                .to(0.07, { scale: 1.2 })
                .call(() => {
                    const targetEntity = this.logicWorld.query([TargetComponent])[0];
                    let targetPos = this.renderWorld.getRenderEntityWorldPosition(targetEntity, `targets/target${curType}/targetLab`);
                    targetPos = itemNode.parent.convertToNodeSpaceAR(cc.v3(targetPos.x, targetPos.y, 0));
                    const disPos = cc.v2(targetPos.x - itemNode.x, targetPos.y - itemNode.y);
                    cc.tween(itemNode)
                        .delay(0.1)
                        .parallel(
                            cc.tween().to(0.27, { scale: 0.5 }),
                            cc.tween().to(0.27, { opacity: 100 }),
                            cc.tween().bezierBy(0.27, cc.Vec2.ZERO, cc.v2(disPos.y / 2, -disPos.y), disPos),
                        )
                        .call(() => {
                            if (index === this._itemNodes.length - 1) {
                                this.playHideAnimation();
                            }
                        })
                        .start();
                })
                .start();
        } else {
            const moveNode = itemNode.getChildByName('gemImg');
            const numLabNode = itemNode.getChildByName('numLab');
            if (numLabNode) {
                numLabNode.active = false;
            }
            if (moveNode) {
                cc.tween(moveNode)
                    .to(0.13, { scale: 1.5 })
                    .to(0.07, { scale: 1.2 })
                    .call(() => {
                        const targetEntity = this.logicWorld.query([TargetComponent])[0];
                        let targetPos = this.renderWorld.getRenderEntityWorldPosition(targetEntity, `targets/target${curType}`);
                        targetPos = itemNode.convertToNodeSpaceAR(cc.v3(targetPos.x, targetPos.y, 0));
                        const startPos = moveNode.position;
                        const disPos = cc.v2(targetPos.x - startPos.x, targetPos.y - startPos.y);
                        cc.tween(moveNode)
                            .delay(0.1)
                            .parallel(
                                cc.tween().to(0.27, { scale: 0.7 }),
                                cc.tween().bezierBy(0.27, cc.Vec2.ZERO, cc.v2(disPos.y / 2, -disPos.y), disPos),
                            )
                            .call(() => {
                                this.renderWorld.eventBus.emitRenderEvent(ECSEvent.RenderEvent.FRESH_TARGET_COLLECT, targetParam);
                                if (index === this._itemNodes.length - 1) {
                                    this.playHideAnimation();
                                }
                            })
                            .start();
                    })
                    .start();
            }
        }
    }

    private playHideAnimation() {
        cc.tween(this.node)
            .to(0.3, { opacity: 0, scale: 1.0 })
            .call(() => {
                this.renderWorld.destroyECSView(this);
            })
            .start();
    }

    protected addEvents() {}
    protected removeEvents() {}

    protected disposeView() {
        this._itemNodes.length = 0;
    }
}
