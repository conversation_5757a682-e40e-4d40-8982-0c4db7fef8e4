import { TempleteType } from '../registry/templete/TempleteRegistry';
import { ECSRuleBase, IECSRuleConfig } from './core/ECSRuleBase';
export interface IInitTargetRuleConfig extends IECSRuleConfig {
    /**目标列表 */
    target: number[][];
    /**目标x坐标 */
    x: number;
}
/**初始化目标规则 */
export default class InitTargetRule extends ECSRuleBase<IInitTargetRuleConfig> {
    canExecute(): boolean {
        return true;
    }
    execute(): void {
        const world = this.world;
        world.templeteCenter.createTempleteEntity(
            TempleteType.Target,
            {
                parentEntity: world.ECSLayerType.SceneLayerEntity,
                targetList: this.config.target,
            },
            {
                x: this.config.x,
            },
        );
    }
}
