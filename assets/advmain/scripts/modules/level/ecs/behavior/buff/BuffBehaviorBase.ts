import { BuffData } from "../../components/combat/BuffComponent";
import { BehaviorBase, IBehaviorConfig } from "../../combat/core/BehaviorBase";
export type BuffBehaviorId = string;
/**
 * Buff配置接口
 */
export interface IBuffBehaviorConfig extends IBehaviorConfig{
    /**Buff标识 */
    buffBehaviorId: BuffBehaviorId;
    /**Buff数值 */
    value: number;
    /**持续时间 */
    duration: number;
    /**触发间隔（用于持续效果） */
    interval: number;
    /**叠加ID，相同stackId的buff会互相叠加 */
    stackId: string;
}
/**
 * Buff行为基类
 */
export abstract class BuffBehaviorBase<T extends IBuffBehaviorConfig = IBuffBehaviorConfig> extends BehaviorBase<T> {
    /**
     * Buff添加回调
     * 当buff被添加到实体时调用
     */
    onAdd?(buffData: BuffData): void;

    /**
     * Buff移除回调
     * 当buff从实体移除时调用
     */
    onRemove?(buffData: BuffData): void;

    /**
     * Buff激活回调
     * 当buff被激活时调用
     */
    abstract onActivate(buffData: BuffData): void;

    /**
     * Buff停用回调
     * 当buff被停用时调用
     */
    abstract onDeactivate(buffData: BuffData): void;

    /**
     * Buff更新回调
     * 在buff持续期间定时调用
     */
    onTick?(buffData: BuffData, deltaTime: number): void;

    /**
     * Buff值变化回调
     * 当buff的值发生变化时调用
     */
    onValueChanged?(buffData: BuffData, newValue: number, oldValue: number): void;
} 