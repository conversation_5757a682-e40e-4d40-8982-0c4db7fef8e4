import { LevelLoader } from '../../../../../../advmain/scripts/base/LevelLoader';
import { SubGameBridge } from '../../../../../../advmain/scripts/base/SubGameBridge';
import { AudioConfig } from '../../../../../../advmain/scripts/modules/audio/config/AudioConfig';
import { PrefabConfig } from '../../../../../../advmain/scripts/modules/prefab/PrefabConfig';
import ChapterContent from './ChapterContent';

export interface IChapterList {
    curChapter: number;
    lastChapter: number;
    isThrough: boolean;
    isForceDraw: boolean;
}

const { ccclass, property } = cc._decorator;
/**
 * 关卡列表 ->马赛克
 */
@classId('ChapterList')
@ccclass
export default class ChapterList extends falcon.Component<Partial<IChapterList>> {
    /**中间关卡信息节点 */
    @property(cc.Node)
    midContainer: cc.Node = null;

    @property(cc.Node)
    topContainer: cc.Node = null;

    /**当前关文本 level */
    @property(cc.Label)
    chapterLb: cc.Label = null;

    /**下一期提示 */
    @property(cc.Node)
    nextTip: cc.Node = null;

    /**关卡按钮 */
    @property(cc.Node)
    chapterBtn: cc.Node = null;

    /**item图集 */
    private atlas: cc.SpriteAtlas = null;
    private content: ChapterContent = null;

    @falcon.adapterFringe<ChapterList>('topContainer', 'midContainer')
    protected onLoad(): void {
        this.chapterBtn.active = false;
        falcon.audioInfo.play(AudioConfig.commonBgm);
    }

    async render() {
        let _redraw = false;
        if (!this.content || this.state.isForceDraw) {
            this.state.isForceDraw = false;
            _redraw = true;
            const node = await SubGameBridge.showUI(PrefabConfig.ChapterContent, this.midContainer);
            this.content = node.getComponent(ChapterContent);
        }
        this.content.setState({
            curChapter: this.state.curChapter,
            lastChapter: this.state.lastChapter,
            isThrough: this.state.isThrough,
            redraw: _redraw,
        });
        if (this.state.isThrough == false) {
            this.content.content.position = this.content.contentPos;
        }

        this.setChapterBtnState();
    }

    setChapterBtnState() {
        if (this.state.isThrough) {
            this.nextTip.active = true;
            this.chapterBtn.active = false;
        } else {
            this.nextTip.active = false;
            this.chapterBtn.active = true;
            this.chapterLb.node.active = true;
            // 绘制按钮等级
            this.chapterLb.string = `+${this.state.curChapter + 1}`;
        }
    }

    /**点击返回 */
    @falcon.throttle(300)
    onClickBack() {
        console.log('点击返回');
        SubGameBridge.returnToMainGame();
    }

    /**开始关卡游戏 */
    @falcon.throttle(500)
    onClickStartGame() {
        LevelLoader.enterLevel(`${this.state.curChapter + 1}`);
    }
}
