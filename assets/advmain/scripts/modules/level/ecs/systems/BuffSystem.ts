import { System } from "../cores/System";
import { BuffComponent, BuffData } from "../components/combat/BuffComponent";
import { IBuffAddResult, StackResult } from "../behavior/buffstack/BuffStackBehaviorBase";
import { BehaviorConst } from "../cores/center/BehaviorCenter";
import { IBuffBehaviorConfig } from "../behavior/buff/BuffBehaviorBase";
import { ECSEvent } from "../GameEvent";
import { IAddBuffInfo } from "../GameEventData";

/**
 * Buff系统
 * 负责更新Buff状态、处理组件清理和Buff生命周期管理
 */
export class BuffSystem extends System {
    init(): void {
        // 只监听组件移除
        this.world.onComponentRemove(BuffComponent, this.onBuffComponentRemove, this);
        this.world.eventBus.on(ECSEvent.GameEvent.ADD_BUFF, this.onAddBuff, this);
    }

    dispose(): void {
        // 移除监听器，防止内存泄漏
        this.world.offComponentRemove(BuffComponent, this.onBuffComponentRemove, this);
        this.world.eventBus.off(ECSEvent.GameEvent.ADD_BUFF, this.onAddBuff, this);
    }

    /**
     * 处理Buff组件移除
     * 确保所有Buff资源被正确清理
     */
    private onBuffComponentRemove(entityId: number, component: BuffComponent) {
        if (component.buffList.length === 0) return;

        console.log(`[BuffSystem] 清理实体 ${entityId} 的 ${component.buffList.length} 个Buff`);

        // 先拷贝buffList，防止在遍历过程中修改数组导致遍历失效
        const buffListCopy = [...component.buffList];

        buffListCopy.forEach(buffData => {
            // 调用完整的destroyBuff方法，确保完整的移除逻辑
            this.destroyBuffData(buffData);
        });
    }
    private onAddBuff(eventData: IAddBuffInfo){
        this.createBuff(eventData.casterEntityId, eventData.ownerEntityId, eventData.buffBehaviorId);
    }

    /**
     * 系统更新
     * @param dt 时间增量
     */
    update(dt: number) {
        const worldTime = this.world.worldTime;
        const entities = this.world.query([BuffComponent]);

        entities.forEach(entityId => {
            const buffComponent = this.world.getComponent<BuffComponent>(entityId, BuffComponent);
            if (!buffComponent) return;

            const buffsToDestroy: BuffData[] = [];

            // 处理生命周期
            buffComponent.buffList.forEach((buffData: BuffData) => {
                // 更新持续时间（无论是否生效）
                if (buffData.remainingTime > 0) {
                    buffData.remainingTime -= dt;
                    if (buffData.remainingTime <= 0) {
                        buffsToDestroy.push(buffData);
                    }
                }

                // 处理间隔性buff
                const buffConfig = this.world.configCenter.getBuffBehaviorConfig(buffData.buffBehaviorId);
                if (buffConfig && buffConfig.interval) {
                    const timeSinceLastTick = worldTime - buffData.lastTickTime;
                    if (timeSinceLastTick >= buffConfig.interval) {
                        // 直接执行tick逻辑
                        if (buffData.isActive) {
                            const behavior = this.world.behaviorCenter.getBehaviorById(BehaviorConst.Buff, buffData.buffBehaviorId);
                            if (behavior && behavior.onTick) {
                                behavior.onTick(buffData, dt);
                            }
                        }
                        buffData.lastTickTime = worldTime;
                    }
                }
            });

            // 销毁过期的buff
            buffsToDestroy.forEach(buffData => {
                this.destroyBuffData(buffData);
            });
        });
    }

    /**
     * 创建buff
     */
    private createBuff(casterEntityId: number, ownerEntityId: number, buffBehaviorId: string): number | null {
        const component = this.world.getComponent<BuffComponent>(ownerEntityId, BuffComponent);
        if (!component){
            console.warn(`没有找到buff组件 ${ownerEntityId}`);
            return null;
        }

        const buffConfig = this.world.configCenter.getBuffBehaviorConfig(buffBehaviorId);
        if (!buffConfig){
            console.warn(`没有找到buff配置 ${buffBehaviorId}`);
            return null;
        }

        // 处理堆叠逻辑
        const stackResult = this.processStackLogic(component, buffConfig);

        // 如果不需要创建新buff，说明叠加处理成功
        if (!stackResult.shouldCreateNew) {
            return null;
        }

        // 创建buff数据
        const buffData: BuffData = {
            casterEntityId: casterEntityId,
            ownerEntityId: ownerEntityId,
            insId: 0,
            buffBehaviorId: buffBehaviorId,
            stackId: buffConfig.stackId,
            remainingTime: buffConfig.duration,
            stackCount: 1,
            lastTickTime: this.world.worldTime,
            value: buffConfig.value,
            isActive: stackResult.shouldActivate,
            behaviorData: {}
        };
        this.addBuffData(component,buffData,buffConfig);
    }
    private addBuffData(component: BuffComponent,buffData: BuffData,buffConfig: IBuffBehaviorConfig): boolean {
        // 获取并验证行为（现在包含配置）
        const behavior = this.world.behaviorCenter.getBehaviorById(BehaviorConst.Buff, buffData.buffBehaviorId);
        if (!behavior) {
            console.warn(`没有找到buff类型 ${behavior.config.behaviorType} 的行为实现，buffId=${buffData.buffBehaviorId}`);
            return null;
        }

        this.addBuff(component, buffData);

        // 触发生命周期事件
        if (behavior.onAdd) {
            behavior.onAdd(buffData);
            
        }

        if (buffData.isActive && behavior && behavior.onActivate) {
            behavior.onActivate(buffData);
        }

        if(buffData.remainingTime===0){
            this.destroyBuffData(buffData);
        }
    }
    /**
     * 销毁buff
     */
    private destroyBuffData(buffData: BuffData): boolean {
        const component = this.world.getComponent<BuffComponent>(buffData.ownerEntityId, BuffComponent);
        if (!component || !buffData) return false;

        // 处理堆叠销毁逻辑
        this.processDestroyStackLogic(component, buffData);

        // 获取行为并触发销毁事件
        const behavior = this.world.behaviorCenter.getBehaviorById(BehaviorConst.Buff, buffData.buffBehaviorId);
        if (behavior) {
            // 如果buff处于激活状态，先停用
            if (buffData.isActive) {
                if (behavior.onDeactivate) {
                    behavior.onDeactivate(buffData);
                }
            }

            // 调用销毁回调
            if (behavior.onRemove) {
                behavior.onRemove(buffData);
            }
        }

        // 从组件中移除
        this.removeBuff(component, buffData);

        return true;
    }

    /**
     * 激活buff
     */
    activateBuff(buffData: BuffData): boolean {
        if (buffData.isActive) return true;

        const behavior = this.world.behaviorCenter.getBehaviorById(BehaviorConst.Buff, buffData.buffBehaviorId);
        if (!behavior) return false;

        buffData.isActive = true;
        if (behavior.onActivate) {
            behavior.onActivate(buffData);
        }
        return true;
    }

    /**
     * 停用buff
     */
    deactivateBuff(buffData: BuffData): boolean {
        if (!buffData.isActive) return true;

        const behavior = this.world.behaviorCenter.getBehaviorById(BehaviorConst.Buff, buffData.buffBehaviorId);
        if (!behavior) return false;

        buffData.isActive = false;
        if (behavior.onDeactivate) {
            behavior.onDeactivate(buffData);
        }
        return true;
    }
    /**
     * 处理buff创建时的堆叠逻辑
     */
    private processStackLogic(component: BuffComponent, buffConfig: IBuffBehaviorConfig): IBuffAddResult {
        // 没有叠加id，直接创建
        if (!buffConfig.stackId) {
            return StackResult.CREATE_AND_ACTIVATE;
        }

        const strategy = this.world.behaviorCenter.getBehaviorById(BehaviorConst.BuffStack, buffConfig.stackId);
        if (!strategy) {
            return StackResult.CREATE_AND_ACTIVATE;
        }

        const sameStackBuffs = this.findSameGroupBuffs(component, buffConfig.stackId);
        if (strategy.handleBuffAdd) {
            // 创建包含必要信息的配置对象
            const buffBehaviorConfig: IBuffBehaviorConfig = {
                buffBehaviorId: buffConfig.buffBehaviorId,
                behaviorDesc: '',
                behaviorType: buffConfig.behaviorType,
                value: buffConfig.value,
                duration: buffConfig.duration,
                interval: buffConfig.interval,
                stackId: buffConfig.stackId
            };
            
            return strategy.handleBuffAdd({
                existingBuffs: sameStackBuffs,
                newBuffConfig: buffBehaviorConfig
            });
        }
        return StackResult.CREATE_AND_ACTIVATE;
    }

    /**
     * 处理buff销毁时的堆叠逻辑
     */
    private processDestroyStackLogic(component: BuffComponent, buffData: BuffData): void {
        if (!buffData.stackId) return;

        const strategy = this.world.behaviorCenter.getBehaviorById(BehaviorConst.BuffStack, buffData.stackId);
        if (!strategy) return;

        const remainingBuffs = this.findOtherBuffsInGroup(component, buffData);
        if (!remainingBuffs) return;

        if (strategy.handleBuffDestroy) {
            strategy.handleBuffDestroy({
                destroyingBuff: buffData,
                remainingBuffs,
            });
        }
    }

    /**
     * 添加buff
     * @param component buff组件
     * @param buffData buff数据
     * @returns 是否添加成功
     */
    private addBuff(component: BuffComponent, buffData: BuffData): boolean {
        if (!buffData) return false;
        component.buffList.push(buffData);
        return true;
    }

    /**
     * 移除buff
     * @param component buff组件
     * @param buffData buff数据
     * @returns 被移除的buff数据,如果不存在则返回undefined
     */
    private removeBuff(component: BuffComponent, buffData: BuffData): BuffData | undefined {
        const index = component.buffList.findIndex(buff => buff === buffData);
        if (index === -1) return undefined;
        return component.buffList.splice(index, 1)[0];
    }

    /**
     * 查找buff
     * @param component buff组件
     * @param insId buff唯一标识
     * @returns buff数据,如果不存在则返回undefined
     */
    private findBuff(component: BuffComponent, insId: number): BuffData | undefined {
        return component.buffList.find(buff => buff.insId === insId);
    }

    /**
     * 查找同一组的所有buff
     * @param component buff组件
     * @param stackId 堆叠ID
     * @returns 同组的buff列表
     */
    private findSameGroupBuffs(component: BuffComponent, stackId: string): BuffData[] {
        return component.buffList.filter(buff => buff.stackId === stackId);
    }

    /**
     * 查找同一组中除指定buff外的其他buff
     * @param component buff组件
     * @param buff 指定的buff
     * @returns 同组的其他buff列表
     */
    private findOtherBuffsInGroup(component: BuffComponent, buff: BuffData): BuffData[] {
        return component.buffList.filter(b => b.stackId === buff.stackId && b !== buff);
    }

    /**
     * 更新buff的当前值
     * 注意：此方法只更新值，不会处理由值变化触发的效果
     * @param component buff组件
     * @param buffId buff配置ID
     * @param newValue 新的效果值
     * @returns 是否更新成功
     */
    private updateBuffValue(component: BuffComponent, buffId: number, newValue: number): { success: boolean, oldValue?: number } {
        const buff = this.findBuff(component, buffId);
        if (!buff) return { success: false };

        const oldValue = buff.value;
        if (oldValue === newValue) return { success: true, oldValue };

        buff.value = newValue;
        return { success: true, oldValue };
    }
} 