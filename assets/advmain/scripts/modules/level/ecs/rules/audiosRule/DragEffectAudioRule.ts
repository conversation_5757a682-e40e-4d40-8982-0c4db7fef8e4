import { AudioId } from '../../config/conf/EcsAudioConfig';
import { ECSEvent } from '../../GameEvent';
import { ECSRuleBase, IECSRuleConfig } from '../core/ECSRuleBase';
/**
 * 拖动音效播放规则配置
 */
export interface IDragEffectAudioRuleConfig extends IECSRuleConfig {
    audioId: AudioId;
}
/**
 * 拖动音效播放规则
 * 负责创建和管理播放音效的ECS视图
 */
export class DragEffectAudioRule extends ECSRuleBase<IDragEffectAudioRuleConfig> {
    /**
     * 检查是否可以执行
     */
    canExecute(): boolean {
        console.log('DragEffectAudioRule canExecute');
        return true;
    }
    /**
     * 执行规则
     */
    execute(): void {
        this.world.eventBus.emit(ECSEvent.BaseEvent.PLAY_AUDIO, { audioId: this.config.audioId });
    }
} 