import { StyleOrange } from "../../modules/level/ecs/cores/ConsoleStyle";
import { Registry } from "./registry/Registry";
import { IFlow, ExecutionContext, IExecutable, IFlowConfig, IRuleConfig } from "./types";

/**
 * 执行流模块 - 管理和执行执行流
 */
export class FlowModule {
  // 流相关
  private flows: Map<string, IFlow>;
  // 统一注册器
  public registry: Registry;

  /**
   * 构造函数 - 初始化成员变量
   */
  constructor() {
    this.flows = new Map();
    this.registry = new Registry();
  }

  /**
   * 注册执行流
   * @param name 执行流名字
   * @param flow 执行流实例
   */
  registerFlow(name: string, flow: IFlow): void {
    this.flows.set(name, flow);
  }

  /**
   * 注销执行流
   * @param name 执行流名字
   */
  unregisterFlow(name: string): void {
    this.flows.delete(name);
  }
  /**
   * 检查执行流是否存在
   * @param name 执行流名字
   * @returns 是否存在
   */
  hasFlow(name: string): boolean {
    return this.flows.has(name);
  }
  /**
   * 获取执行流
   * @param name 执行流名字
   * @returns 执行流实例
   */
  getFlow(name: string): IFlow | undefined {
    return this.flows.get(name);
  }

  /**
   * 执行流
   * @param name 执行流名字
   * @param context 执行上下文
   * @returns 执行结果
   */
  executeFlow(name: string, context: ExecutionContext): boolean {
    const flow = this.getFlow(name);
    if (!flow) {
      console.error(`执行流不存在: ${name}`);
      return false;
    }
    flow.setContext(context);
    flow.run();
    return true;
  }

  /**
   * 关闭模块
   */
  dispose(): void {
    this.flows.clear();
    this.registry.clear();
  }

  /**
   * 创建执行流实例
   * @param type 类型
   * @returns 执行流实例
   */
  buildFlow(definition: FlowNode): IFlow {
    // 检查节点类型
    if (!isFlowNode(definition)) {
      throw new Error('无法从规则节点构建执行流');
    }

    // 创建执行流实例
    const flow = this.registry.createFlow(definition.flowType, definition.flowConfig);

    // 添加子节点
    for (const child of definition.children) {
      let executable: IExecutable;

      // 处理字符串规则ID或规则节点
      if (isFlowNode(child)) {
        // 使用注册表创建规则实例
        executable = this.buildFlow(child);
      }
      // 处理流节点
      else if (isRuleNode(child)) {
        if(child.ruleConfig.close) {
          console.warn(`%c关闭底板配置规则%c:${child.ruleConfig.ruleId}\n\n描述:${child.ruleConfig.ruleDesc}`, StyleOrange, '');
          continue;
        }
        if(child.replaceRuleType) {
          console.log(`%c替换底板配置规则%c:${child.ruleConfig.ruleId}\n\n原规则(${child.ruleConfig.ruleId})->新规则(${child.replaceRuleType})`, StyleOrange, '');
          const replaceRule = this.registry.createRule(child.replaceRuleType, child.replaceRuleConfig);
          const rule = this.registry.createRule(child.ruleType, child.ruleConfig);
          if(rule['bindEventData']() === replaceRule['bindEventData']()) {// 临时处理
            executable = replaceRule;
          }
          else{
            console.error(`%c替换底板配置规则失败%c:${child.ruleConfig.ruleId}\n\n-两个规则的绑定事件不一致\n\n原规则(${child.ruleConfig.ruleId})->新规则(${child.replaceRuleType})`, StyleOrange, '');
            continue;
          }
        }
        else{
          const rule = this.registry.createRule(child.ruleType, child.ruleConfig);
          executable = rule;
        }
      }
      if (executable) {
        flow.addChild(executable);
      }
    }

    return flow;
  }
}
export interface DefNode {

}

export interface FlowNode extends DefNode {
  flowType: string;
  flowConfig: IFlowConfig;
  children: DefNode[];
}

export interface RuleNode extends DefNode {
  /** 规则类型 */
  ruleType: string;
  /** 规则配置 */
  ruleConfig: IRuleConfig;
  /** 规则替换类型 */
  replaceRuleType: string;
  /** 规则替换配置 */
  replaceRuleConfig: IRuleConfig;
}
export function isFlowNode(node: DefNode): node is FlowNode {
  return node['flowType'] !== undefined;
}

export function isRuleNode(node: DefNode): node is RuleNode {
  return node['ruleType'] !== undefined;
}