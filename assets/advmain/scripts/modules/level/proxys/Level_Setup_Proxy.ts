import { Loader } from '../../../base/Loader';
import { SubGameBridge } from '../../../base/SubGameBridge';
import { audioInfoData } from '../../audio/vo/AudioInfoData';
import { launchInfo } from '../../launch/vo/LaunchInfo';
import { gameAlertLayer } from '../../layer/vo/LayerInfo';
import { PrefabConfig } from '../../prefab/PrefabConfig';
import LevelSetup, { ISetupState } from '../component/LevelSetup';
import { enSetupKeys } from '../config/LevelSetupConfig';
import { E_Levels_SetupClick } from '../events/E_Levels_SetupClick';
import { E_Levels_SetupShow } from '../events/E_Levels_SetupShow';

export class Level_Setup_Proxy extends falcon.Proxy {
    private eventHandlers: { [key: string]: (event: E_Levels_SetupClick) => void } = {
        [enSetupKeys.closeSelf]: this.onClick_closeSelf.bind(this),
        [enSetupKeys.home]: this.onClick_home.bind(this),
        [enSetupKeys.moreSettings]: this.onClick_moreSettings.bind(this),
        [enSetupKeys.replay]: this.onClick_replay.bind(this),
        [enSetupKeys.BGM]: this.onClick_BGM.bind(this),
        [enSetupKeys.sound]: this.onClick_sound.bind(this),
    };
    registerEvents() {
        return [E_Levels_SetupClick, E_Levels_SetupShow];
    }

    receivedEvents($event: falcon.ModuleEvent): void {
        switch ($event.getClass()) {
            case E_Levels_SetupClick:
                const { key } = $event as E_Levels_SetupClick;
                const handler = this.eventHandlers[key];
                if (handler) {
                    // 播放音效
                    handler($event as E_Levels_SetupClick);
                } else {
                    console.warn(`No handler found for key: ${key}`);
                }
                break;

            case E_Levels_SetupShow: // 打开设置界面
                this.onSetupShow(($event as E_Levels_SetupShow).data);
                break;
        }
    }

    /**打开设置UI */
    private async onSetupShow(data: Partial<ISetupState>) {
        if (!data.keys) {
            return;
        }
        await Loader.showUI(PrefabConfig.Setup, gameAlertLayer);
        this.updateUI(data);
    }

    /**更新设置UI */
    private updateUI(data?: Partial<ISetupState>) {
        const setup = Cinst(LevelSetup);
        if (setup) {
            const tmpData: Partial<ISetupState> = data || {};
            const chapterPoint = launchInfo.isShowChapterRedPoint == 1;
            const values = {
                [enSetupKeys.sound]: audioInfoData.audioSwitch,
                [enSetupKeys.BGM]: audioInfoData.bgmSwitch,
                [enSetupKeys.home]: chapterPoint,
            };
            const redPoints = {
                [enSetupKeys.home]: chapterPoint,
            };
            tmpData.value = values;
            tmpData.redPoint = redPoints;
            setup.setState(tmpData);
        }
    }

    /**返回home */
    @falcon.throttle(300)
    private onClick_home(event: E_Levels_SetupClick) {
        falcon.UI.hideUI(PrefabConfig.Setup);
        console.log('点开主页');
    }

    /**点击更多设置 */
    @falcon.throttle(300)
    private onClick_moreSettings(event: E_Levels_SetupClick) {
        console.log('点开更多设置');
        falcon.UI.hideUI(PrefabConfig.Setup);
    }

    /**点击重玩 */
    @falcon.throttle(300)
    private onClick_replay(event: E_Levels_SetupClick) {
        //临时写法
        window['gameIns']._world.eventBus.emit('BaseEvent.GAME_RESTART');
        falcon.UI.hideUI(PrefabConfig.Setup);
    }

    /**点击背景音乐开关 */
    @falcon.throttle(300)
    private onClick_BGM(event: E_Levels_SetupClick) {
        console.log('点开背景音乐');
    }

    /**点击音效 */
    @falcon.throttle(300)
    private onClick_sound(event: E_Levels_SetupClick) {
        console.log('点开音效');
    }

    /**关闭界面 */
    @falcon.throttle(300)
    private onClick_closeSelf(event: E_Levels_SetupClick) {
        falcon.UI.hideUI(PrefabConfig.Setup);
    }
}
