import { ProjectileComponent } from "../../components/combat/ProjectileComponent";
import { PathCalculationData, PathCalculationResult, ProjectilePathBehaviorBase } from "./ProjectilePathBehaviorBase";

/** 暂停路径行为 */
export class PausePathBehavior extends ProjectilePathBehaviorBase {
    calculatePath(params: PathCalculationData, projectile: ProjectileComponent): PathCalculationResult {
        this.resultCache.position.x = params.startPos.x;
        this.resultCache.position.y = params.startPos.y;
        this.resultCache.isComplete = params.elapsedTime >= (params.moveTime || 0);
        return this.resultCache;
    }
}