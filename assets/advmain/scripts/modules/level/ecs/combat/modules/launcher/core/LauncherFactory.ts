import { LauncherBehaviorBase } from "./ILauncherBehavior";
import { LinearLauncherBehavior } from "../behavior/LinearLauncherBehavior";
import { BehaviorFactoryBase } from "../../../core/BehaviorFactoryBase";

/**
 * 发射器工厂
 * 负责创建和管理发射器行为实例
 */
export class LauncherFactory extends BehaviorFactoryBase {
    private behaviorMap: Map<string, LauncherBehaviorBase<IBaseLauncherConfig>> = new Map();

    initBehavior(): void {
        this.behaviorMap.set(LauncherType.Projectile, new LinearLauncherBehavior(this.world));
    }

    disposeBehavior(): void {
        this.behaviorMap.clear();
    }

    /**
     * 根据发射器ID获取发射器行为
     * @param launcherId 发射器ID
     * @returns 发射器行为实例
     */
    getLauncherBehaviorById(launcherId: string): LauncherBehaviorBase<IBaseLauncherConfig> | undefined {
        // 通过CombatConfigManager获取配置
        const config = this.world.configCenter.getLauncherConfig(launcherId);
        if (!config) {
            console.warn(`未找到launcherId=${launcherId}的配置`);
            return undefined;
        }

        // 根据配置类型获取行为实例
        const behaviorInstance = this.behaviorMap.get(config.launcherType);
        if (!behaviorInstance) {
            console.warn(`未找到类型=${config.launcherType}的发射器行为实例`);
            return undefined;
        }

        // 设置配置到行为实例
        behaviorInstance.config = config;
        return behaviorInstance;
    }
} 