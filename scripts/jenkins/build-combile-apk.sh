#!/bin/bash
# 构建融合包APK，此脚本不能单独使用，需要配合 build-test.sh 使用
# 1. 构建未签名APK
# 2. 对齐未签名APK
# 3. 签名APK

source ./scripts/jenkins/build-test.sh

# 解压主包资源
# 反编译APK
if [ "$Apk" == true ]; then
  log "反编译APK..."
  /opt/homebrew/bin/apktool d -f ${APK_BASE_URL} -o ${COMBILE_TEMP_DIR}
fi

# 清理并准备APK资源
log "清理并准备APK资源..."
rm -rf ${COMBILE_TEMP_DIR}/assets/assets
rm -rf ${COMBILE_TEMP_DIR}/assets/src
rm -rf ${COMBILE_TEMP_DIR}/assets/jsb-adapter
rm -f ${COMBILE_TEMP_DIR}/assets/main.js

# 删除文件前检查文件是否存在
[ -f ${COMBILE_TEMP_DIR}/assets/main.refactored.js ] && rm -f ${COMBILE_TEMP_DIR}/assets/main.refactored.js
[ -f ${COMBILE_TEMP_DIR}/assets/distribution.js ] && rm -f ${COMBILE_TEMP_DIR}/assets/distribution.js

# 复制新构建资源到APK
log "复制新构建资源到APK..."
cp -r ${BUILD_OUTPUT_DIR}/jsb-link/assets ${COMBILE_TEMP_DIR}/assets
cp -r ${BUILD_OUTPUT_DIR}/jsb-link/src ${COMBILE_TEMP_DIR}/assets
cp -r ${BUILD_OUTPUT_DIR}/jsb-link/jsb-adapter ${COMBILE_TEMP_DIR}/assets
cp -r ${BUILD_OUTPUT_DIR}/jsb-link/main.js ${COMBILE_TEMP_DIR}/assets

if [[ ${MACRO_HOTUPDATE_OPEN} == true ]]; then
  # 处理热更manifest文件
  log "复制热更manifest文件..."
  mkdir -p ${COMBILE_TEMP_DIR}/assets/src
  cp ${BUILD_OUTPUT_DIR}/jsb-link/src/project.manifest ${COMBILE_TEMP_DIR}/assets/src
  cp ${BUILD_OUTPUT_DIR}/jsb-link/src/version.manifest ${COMBILE_TEMP_DIR}/assets/src
fi

#########################################################
#
#             拷贝进去之后，需要改名，以避免与主包冲突
#
#########################################################
# 6. main.js 改名并移动
echo "[6/13] 处理 main.js..."
[ -f ${COMBILE_TEMP_DIR}/main.js ] && mkdir -p ${COMBILE_TEMP_DIR}/src && mv ${COMBILE_TEMP_DIR}/main.js ${COMBILE_TEMP_DIR}/src/main.refactored.js

# 7. 拷贝 distribution.js
echo "[7/13] 拷贝 distribution.js..."
cp "${BUILD_TEMPLATES_PATH}/distribution.js" "${COMBILE_TEMP_DIR}/src/distribution.js"

# 8. 拷贝 main.js
echo "[8/13] 拷贝 main.js..."
cp "${BUILD_TEMPLATES_PATH}/main.js" "${COMBILE_TEMP_DIR}/main.js"

# 9. 移动 settings.js
echo "[9/13] 移动 settings.js..."
[ -f ${COMBILE_TEMP_DIR}/src/settings.js ] && mkdir -p ${COMBILE_TEMP_DIR}/src/refactored && mv ${COMBILE_TEMP_DIR}/src/settings.js ${COMBILE_TEMP_DIR}/src/refactored/settings.js
[ -f ${COMBILE_TEMP_DIR}/src/settings.jsc ] && mkdir -p ${COMBILE_TEMP_DIR}/src/refactored && mv ${COMBILE_TEMP_DIR}/src/settings.jsc ${COMBILE_TEMP_DIR}/src/refactored/settings.jsc

# 10. 移动 cocos2d-jsb.js
echo "[10/13] 移动 cocos2d-jsb.js..."
[ -f ${COMBILE_TEMP_DIR}/src/cocos2d-jsb.js ] && mkdir -p ${COMBILE_TEMP_DIR}/src/refactored && mv ${COMBILE_TEMP_DIR}/src/cocos2d-jsb.js ${COMBILE_TEMP_DIR}/src/refactored/cocos2d-jsb.js
[ -f ${COMBILE_TEMP_DIR}/src/cocos2d-jsb.jsc ] && mkdir -p ${COMBILE_TEMP_DIR}/src/refactored && mv ${COMBILE_TEMP_DIR}/src/cocos2d-jsb.jsc ${COMBILE_TEMP_DIR}/src/refactored/cocos2d-jsb.jsc

# 11. 移动 jsb-engine.js
echo "[11/13] 移动 jsb-engine.js..."
[ -f ${BUILD_OUTPUT_DIR}/jsb-link/jsb-adapter/jsb-engine.js ] && mkdir -p ${COMBILE_TEMP_DIR}/jsb-adapter/refactored && cp ${BUILD_OUTPUT_DIR}/jsb-link/jsb-adapter/jsb-engine.js ${COMBILE_TEMP_DIR}/jsb-adapter/refactored/jsb-engine.js

#########################################################
#
#              解压主包资源，并合并
#
#########################################################
# 解压 STABLE_RES 到临时目录
log "解压 STABLE_RES..."
STABLE_RES=${BLOCK_BLAST_BASE_URL}/jenkins/stableRes/${stableRes}
mkdir -p ${COMBILE_TEMP_DIR}/stableRes
unzip -o ${STABLE_RES} -d ${COMBILE_TEMP_DIR}/stableRes

# 合并 stableRes 资源到 COMBILE_TEMP_DIR
log "合并 stableRes 资源到 COMBILE_TEMP_DIR..."
for dir in assets jsb-adapter src; do
  if [ -d ${COMBILE_TEMP_DIR}/stableRes/$dir ]; then
    cp -rf ${COMBILE_TEMP_DIR}/stableRes/$dir/* ${COMBILE_TEMP_DIR}/$dir/
  fi
done

# 合并 main.js
if [ -f ${COMBILE_TEMP_DIR}/stableRes/main.js ]; then
  cp -f ${COMBILE_TEMP_DIR}/stableRes/main.js ${COMBILE_TEMP_DIR}/main.js
fi

# 删除 stableRes 临时目录
rm -rf ${COMBILE_TEMP_DIR}/stableRes

if [[ "$Apk" == true ]]; then
  log "复制开发工具库文件..."
  for arch in "arm64-v8a" "armeabi-v7a" "x86" "x86_64"; do
    cp ${LIB_BASE_URL}/${arch}/libcocos2djs.so ${COMBILE_TEMP_DIR}/lib/${arch}/libcocos2djs.so
  done

  COMBILE_APK_FULL_NAME="${ApkName}-${version}-combile-$(date '+%Y%m%d%H%M%S').apk"

  # 构建APK
  log "构建未签名APK..."
  /opt/homebrew/bin/apktool b ${COMBILE_TEMP_DIR} -o ${COMBILE_UNSIGNED_APK}

  # 对齐，如果不使用这个，会报：Failure [-124: Failed parse during installPackageLI: Targeting R+ (version 30 and above) requires the resources.arsc of installed APKs to be stored uncompressed and aligned on a 4-byte boundary]
  # 从 Android 11 开始，Google 对 APK 的打包格式有了更严格的要求，尤其是 resources.arsc 文件（资源索引文件）：
  # 必须 uncompressed（未压缩）
  # 必须 4 字节对齐
  ~/Library/Android/sdk/build-tools/30.0.3/zipalign -v 4 ${COMBILE_UNSIGNED_APK} ${COMBILE_UNSIGNED_ALIGNED_APK}

  # 签名APK
  log "签名APK..."
  # /opt/homebrew/opt/openjdk/bin/jarsigner -verbose \
  #   -keystore ${KEYSTORE_FILE} \
  #   -storepass "${KEYSTORE_PASS}" \
  #   -signedjar ${COMBILE_APK_FULL_NAME} \
  #   ${COMBILE_UNSIGNED_ALIGNED_APK} \
  #   ${KEYSTORE_ALIAS}
  # 必须用 apksigner 签名，jarsigner 签名会报错：Failure [-124: Failed parse during installPackageLI: Targeting R+ (version 30 and above) requires the resources.arsc of installed APKs to be stored uncompressed and aligned on a 4-byte boundary]
  ~/Library/Android/sdk/build-tools/30.0.3/apksigner sign \
    --ks ${KEYSTORE_FILE} \
    --ks-key-alias ${KEYSTORE_ALIAS} \
    --ks-pass pass:${KEYSTORE_PASS} \
    --out ${COMBILE_APK_FULL_NAME} \
    ${COMBILE_UNSIGNED_ALIGNED_APK}

  # 移动最终APK到构建目录
  log "移动最终APK到构建目录..."
  mv ${COMBILE_APK_FULL_NAME} ${BUILD_OUTPUT_DIR}

  # 清理临时文件
  log "清理临时文件..."
  rm -f ${COMBILE_UNSIGNED_APK}
  rm -f ${COMBILE_UNSIGNED_ALIGNED_APK}
  rm -rf ${COMBILE_TEMP_DIR}

  log "构建完成！最终APK: ${BUILD_OUTPUT_DIR}/${COMBILE_APK_FULL_NAME}"

  # 如果是在Jenkins环境中，将构建的 apk拷贝到 ${BLOCK_BLAST_BASE_URL}/${PLATFORM}/${ENV}/${IP_ADDRESS}
  if [[ "$IS_JENKINS_ENV" = true ]]; then
    # 获取本机IP地址
    IP_ADDRESS=$(ifconfig | grep "inet " | grep -v 127.0.0.1 | awk '{print $2}' | head -n 1)
    TARGET_DIR="${BLOCK_BLAST_BASE_URL}/${PLATFORM}/${ENV}/${IP_ADDRESS}"
    
    # 创建目标目录（如果不存在）
    log "将融合包APK拷贝到 ${TARGET_DIR}..."
    mkdir -p "${TARGET_DIR}"
    
    # 拷贝APK文件到目标目录
    cp ${BUILD_OUTPUT_DIR}/${COMBILE_APK_FULL_NAME} ${TARGET_DIR}/${COMBILE_APK_FULL_NAME}
    # 移除目标文件
    rm -f ${BUILD_OUTPUT_DIR}/${COMBILE_APK_FULL_NAME}
    
    # 验证拷贝成功
    if [ $? -eq 0 ]; then
      log "combile APK已成功拷贝到 ${TARGET_DIR}/${COMBILE_APK_FULL_NAME}"
    else
      log "错误: combile APK拷贝失败" >&2
    fi
  fi

  # 获取APK大小
  COMBILE_APK_SIZE=$(du -h "${TARGET_DIR}/${COMBILE_APK_FULL_NAME}" | cut -f1)
fi