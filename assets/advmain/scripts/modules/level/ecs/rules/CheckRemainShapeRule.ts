import BoardComponent from '../components/board/BoardComponent';
import { BoardScene } from '../components/board/BoardScene';
import ShapeComponent from '../components/board/ShapeComponent';
import { ECSEvent } from '../GameEvent';
import { ECSExecuteContext, ECSRuleBase, IECSRuleConfig } from './core/ECSRuleBase';

export interface ICheckRemainShapeRuleConfig extends IECSRuleConfig {}

/** 检测待放置区是否还有可落子的形状，若无则派发 PRODUCE_BLOCK */
export class CheckRemainShapeRule extends ECSRuleBase<ICheckRemainShapeRuleConfig, ECSEvent.GameEvent.PUT_BLOCK_BACK> {
    bindEventData() {
        return ECSEvent.GameEvent.PUT_BLOCK_BACK;
    }
    canExecute(): boolean {
        const data = this.eventData;

        return data?.isSuccess;
    }
    execute(): void {
        const world = this.world;
        const sceneE = world.handlerCenter.boardHandler.getSceneEntity();
        if (sceneE === undefined) return;
        const sceneComp = world.getComponent(sceneE, BoardScene);
        if (sceneComp.waitPutCells.length === 0) {
            // 已经没有待放置块，系统会自动生成
            return;
        }
        // 收集所有棋盘
        const boards = world.query([BoardComponent]);
        const canPlaceAny = sceneComp.waitPutCells.some((shapeE) => {
            const shapeComp = world.getComponent(shapeE, ShapeComponent);
            if (!shapeComp) return false;
            return boards.some((boardE) => world.handlerCenter.boardHandler.canShapePlace(world, boardE, shapeComp));
        });
        if (!canPlaceAny) {
            world.eventBus.emit(ECSEvent.GameEvent.GAME_FAILED);
        }
    }
}
