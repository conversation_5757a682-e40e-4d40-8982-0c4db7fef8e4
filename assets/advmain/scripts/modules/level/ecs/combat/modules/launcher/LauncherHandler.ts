import { Launcher, LauncherData } from "../../component/Launcher";
import { LauncherFactory } from "./core/LauncherFactory";
import { IBehaviorData } from "../../core/BehaviorBase";
import { UtilBase } from "../../../cores/UtilBase";
import { IActionContext } from "../../core/ActionContext";

/**
 * 发射器处理器
 * 负责管理发射器的创建、更新和销毁
 * 只处理通用逻辑，不关心具体的发射器类型
 */
export class LauncherHandler extends UtilBase<LauncherFactory> {
    /**
     * 创建发射器
     * @param casterEntityId 创建者实体ID
     * @param launcherId 发射器ID
     * @param ownerEntityId 拥有者实体ID（可选，默认等于casterEntityId）
     * @param context 发射器创建上下文（可选）
     * @returns 发射器实例ID，失败返回null
     */
    createLauncher(
        casterEntityId: number,
        launcherId: string,
        ownerEntityId?: number,
        context?: IActionContext
    ): number | null {
        // 如果没有指定拥有者，默认拥有者就是创建者
        const actualOwner = ownerEntityId ?? casterEntityId;

        let launcherComponent = this.world.getComponent<Launcher>(actualOwner, Launcher);
        if (!launcherComponent) {
            return null;
        }

        const behavior = this.behavior.getLauncherBehaviorById(launcherId);
        if (!behavior || !behavior.config) {
            console.warn(`发射器行为或配置缺失: ${launcherId}`);
            return null;
        }

        // 准备发射器数据
        const launcherData: LauncherData<IBehaviorData> = {
            insId: IdManager.getInstance().getInsId(this.world),
            launcherId: launcherId,
            ownerEntityId: actualOwner,
            casterEntityId: casterEntityId,
            launchTime: this.world.worldTime,
            behaviorData: {} as IBehaviorData
        };

        launcherComponent.addLauncher(launcherData);

        // 调用行为的启动方法
        if (behavior.onLaunch) {
            behavior.onLaunch(launcherData, context);
        }

        console.log(`发射器已创建: ${launcherId}, 拥有者: ${actualOwner}, 创建者: ${casterEntityId}`);
        return launcherData.insId;
    }

    /**
     * 销毁发射器（推荐使用，性能最优）
     * @param entityId 拥有发射器的实体ID
     * @param launcherInsId 发射器实例ID
     * @returns 是否销毁成功
     */
    destroyLauncher(entityId: number, launcherInsId: number): boolean {
        const launcherComponent = this.world.getComponent<Launcher>(entityId, Launcher);
        if (!launcherComponent) {
            return false;
        }

        const launcherData = launcherComponent.launchersData.find(data => data.insId === launcherInsId);
        if (!launcherData) {
            return false;
        }

        return this.destroyLauncherByData(launcherData);
    }

    /**
     * 销毁发射器-仅限内部或者系统使用
     */
    destroyLauncherByData(launcherData: LauncherData): boolean {
        const launcherComponent = this.world.getComponent<Launcher>(launcherData.ownerEntityId, Launcher);
        if (!launcherComponent) return false;
        // 调用行为清理
        const behavior = this.behavior.getLauncherBehaviorById(launcherData.launcherId);

        if (behavior && behavior.onDestroy) {
            behavior.onDestroy(launcherData);
        }

        // 从组件中移除
        launcherComponent.removeLauncher(launcherData.insId);

        console.log(`发射器已销毁: ${launcherData.launcherId}, 实例ID: ${launcherData.insId}`);
        return true;
    }
} 