import { FlowType } from '../../../../../base/rule/flow/FlowType';
import { IOverrideLevelConfig } from '../../cores/OverrideConfigManager';
import { ECSEvent } from '../../GameEvent';
import { IInitTargetRuleConfig } from '../../rules/InitTargetRule';

export const LevelConfig8: IOverrideLevelConfig = {
    /** 底板配置 */
    baseLevelId: 'BaseConfig10000',
    /** 关卡ID */
    levelId: 'LevelConfig8',
    /** 重写配置表参数 */
    overrideTableArgs: {
        RuleConfig: {
            rule_init_target: {
                target: [[1, 11150]],
            } as IInitTargetRuleConfig,
        },
    },
    overrideLevelConfig: {
        initWorldRuleFlowConfig: {
            type: FlowType.List,
            children: [
                'rule_init_board_scene',
                'rule_init_multi_board',
                'rule_init_target',
                'rule_show_target_info',
                'rule_produce_block_algorithm_algoRandom',
            ],
        },
        sys: [
            'system_interaction',
            'system_combo',
            'system_eliminate',
            'system_game_result',

            'system_preview',
            'system_effect_preview',
            'system_target',
            'system_mulity_board',
            'system_nultity_tween',
            'system_block_algorithm',
        ],
        initAllRuleFlowConfig: {
            [ECSEvent.GameEvent.ELIMINATION]: {
                type: FlowType.List,
                children: ['rule_multiyclear_effect', 'rule_check_remain_shape', 'rule_produce_block_end'],
            },
            [ECSEvent.GameEvent.PRODUCE_BLOCK_END]: {
                type: FlowType.List,
                children: ['rule_produce_block_end'],
            },
            [ECSEvent.GameEvent.PUT_BLOCK_BACK]: {
                type: FlowType.List,
                children: ['rule_check_remain_shape'],
            },
            [ECSEvent.GameEvent.COMBO_CHANGED]: {
                type: FlowType.List,
                children: ['rule_combo_play_sound'],
            },
        },
    },

    /** 关闭系统 */
    closeSystems: [],
    /** 关闭规则 */
    closeRules: [],
    /** 插入系统 */
    insertSystems: [],
    /** 插入规则 */
    insertRules: [],
    /** 替换系统 */
    replaceSystems: [],
    /** 替换规则 */
    replaceRules: [],
};
