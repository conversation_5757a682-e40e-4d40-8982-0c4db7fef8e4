import { IActionContext } from '../combat/core/ActionContext';
import NodeComponent from '../components/NodeComponent';
import { HandlerBase } from '../cores/HandlerBase';

/**行动意图处理器 */
export class ActionHandler extends HandlerBase {
    /**获取行动意图中的目标位置 */
    public getTargetPosition(context: IActionContext) {
        if (context.targetPosition) {
            return context.targetPosition;
        }
        const targetEntityId = context.targetEntityId;
        if (!targetEntityId) return null;
        return this.world.handlerCenter.boardHandler.getEntityRootPoint(targetEntityId);
    }
}
