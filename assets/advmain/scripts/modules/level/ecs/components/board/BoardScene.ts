import { Component } from "../../cores/Component";

export interface IBoardScene {
    waitPutCells: number[];
    score: number;
    collectMap: Record<number, number>;
    targetScore?: number;
}
export class BoardScene extends Component {
    public static readonly type = 'BoardScene'
    public waitPutCells: number[] = [];
    public score: number = 0;
    public targetScore: number = 0;
    public collectMap: Record<number, number> = {};
    constructor(board: IBoardScene = { waitPutCells: [], score: 0, collectMap: {}, targetScore: 100 }) {
        super();
        this.waitPutCells = board.waitPutCells;
        this.score = board.score;
        this.collectMap = board.collectMap;
        this.targetScore = board.targetScore;
    }
}