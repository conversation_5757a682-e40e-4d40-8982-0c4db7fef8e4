import { CellComponent } from '../../../components/board/CellComponent';
import HighlightTag from '../../../components/tag/HighlightTag';
import { ECSViewBase, IECSViewConfig } from '../../ECSViewBase';
import { ECSEvent } from '../../../GameEvent';

/**宝石视图 */
export default class CollectCellECSView extends ECSViewBase<IECSViewConfig> {
    // 是否高亮
    private isHighlight: boolean = false;
    // 资源路径
    private path;
    protected initView() {
        this.path = `cell/${this.logicWorld.getComponent(this.logicEntityId, CellComponent).type}/img/`;
        this.renderWorld.loadRes('level', `${this.path}icon`, cc.SpriteFrame).then((spf: cc.SpriteFrame) => {
            if (!cc.isValid(this.node)) return;
            let sp = this.node.getComponent(cc.Sprite) || this.node.addComponent(cc.Sprite);
            sp.spriteFrame = spf;
        });
    }
    protected addEvents() {
        this.logicWorld.eventBus.onEntityEvent(this.logicEntityId, ECSEvent.EntityEvent.HIGHLIGHT_CHANGED, this.onHighlightChanged, this);
    }
    protected removeEvents() {
        this.logicWorld.eventBus.offEntityEvent(this.logicEntityId, ECSEvent.EntityEvent.HIGHLIGHT_CHANGED, this.onHighlightChanged, this);
    }
    protected disposeView() {}
    update() {}

    onHighlightChanged(entityId: number, event: ECSEvent.EntityEvent, isHighlight: boolean) {
        // 如果状态一致，直接返回
        if (this.isHighlight === isHighlight) return;
        // 记录当前高亮状态
        this.isHighlight = isHighlight;
        let path = this.path + (isHighlight ? 'iconHigh' : 'icon');
        this.renderWorld.loadRes('level', path, cc.SpriteFrame).then((spf: cc.SpriteFrame) => {
            if (!cc.isValid(this.node)) return;
            let sp = this.node.getComponent(cc.Sprite) || this.node.addComponent(cc.Sprite);
            sp.spriteFrame = spf;
        });
    }
}
