import BoardComponent from '../../../components/board/BoardComponent';
import { CellSize } from '../../../define/BoardDefine';
import { ECSViewBase, IECSViewConfig } from '../../ECSViewBase';
const { ccclass } = cc._decorator;

/**
 * 资源缓存接口
 */
interface ResourceCache {
    blockPrefab: cc.Prefab;
    blocksDarkAtlas: cc.SpriteAtlas;
    gemsAtlas: cc.SpriteAtlas;
}

/**
 * 方块刷新动画视图
 */
@ccclass
export class BoardRefurbishEffectView extends ECSViewBase<IECSViewConfig> {
    private offsetX = 0;
    private offsetY = -4;
    
    // 资源缓存
    private resourceCache: ResourceCache = {
        blockPrefab: null,
        blocksDarkAtlas: null,
        gemsAtlas: null
    };

    private blockNodes: cc.Node[][] = [];
    
    // 常量定义
    private static readonly BLOCKS_DARK_URL = "textures/blocks/blocks-dark";
    private static readonly GEMS_URL = "textures/blocks/Gems";
    private static readonly BLOCK_PREFAB_URL = "prefabs/level/block/Block";
    private static readonly COLOR_THRESHOLD = 100;

    protected async initView() {
        // 根据棋盘行列设置自身宽高
        const board = this.logicWorld.getComponent(this.data.boardEntityId, BoardComponent);
        this.node.width = board.cCount * CellSize;
        this.node.height = board.rCount * CellSize;
        // 预加载所有资源
        await this.preloadResources();
        // 创建渲染板面
        this.createRendererSplashBoard(this.node);
        console.log('BoardRefurbishEffectView 初始化完成');
        this.upAnimation(0.07, board.rCount, board.cCount);
    }

    protected addEvents() {
    }

    protected removeEvents() {
    }

    protected disposeView() {
        this.clearResources();
    }

    /**
     * 预加载所有需要的资源
     */
    private async preloadResources(): Promise<void> {
        console.log('开始预加载资源...');
        
        // 并行加载所有资源
        const [blockPrefab, blocksDarkAtlas, gemsAtlas] = await Promise.all([
            this.renderWorld.loadRes(this.config.prefabBundleName, BoardRefurbishEffectView.BLOCK_PREFAB_URL, cc.Prefab),
            this.renderWorld.loadRes(this.config.prefabBundleName, BoardRefurbishEffectView.BLOCKS_DARK_URL, cc.SpriteAtlas),
            this.renderWorld.loadRes(this.config.prefabBundleName, BoardRefurbishEffectView.GEMS_URL, cc.SpriteAtlas)
        ]);

        // 验证资源加载结果
        if (!blockPrefab) {
            throw new Error('Block预制体加载失败');
        }
        if (!blocksDarkAtlas) {
            throw new Error('Blocks-dark图集加载失败');
        }
        if (!gemsAtlas) {
            throw new Error('Gems图集加载失败');
        }

        // 缓存资源
        this.resourceCache.blockPrefab = blockPrefab as cc.Prefab;
        this.resourceCache.blocksDarkAtlas = blocksDarkAtlas as cc.SpriteAtlas;
        this.resourceCache.gemsAtlas = gemsAtlas as cc.SpriteAtlas;

        console.log('资源预加载完成');
    }

    /**
     * 创建渲染块（优化版本）
     */
    private createRendererBlock(x: number, y: number, color: number, opacity: number): cc.Node {
        let blockNode: cc.Node = cc.instantiate(this.resourceCache.blockPrefab);
        // 设置位置
        blockNode.x = x;
        blockNode.y = y;
        blockNode.opacity = 0;

        this.setBlockSpriteFrame(blockNode, color);    

        return blockNode;
    }

    /**
     * 设置块的精灵帧（优化版本）
     */
    private setBlockSpriteFrame(blockNode: cc.Node, color: number): void {
        const blockSprite = blockNode.getChildByName("block")?.getComponent(cc.Sprite);
        if (!blockSprite) {
            console.warn('未找到block精灵组件');
            return;
        }

        let spriteFrame: cc.SpriteFrame = null;

        if (color <= BoardRefurbishEffectView.COLOR_THRESHOLD) {
            // 使用blocks-dark图集
            spriteFrame = this.resourceCache.blocksDarkAtlas.getSpriteFrame(`game_cube_${color}`);
        } else {
            // 使用Gems图集
            spriteFrame = this.resourceCache.gemsAtlas.getSpriteFrame(`Gems${color}`);
        }

        if (spriteFrame) {
            blockSprite.spriteFrame = spriteFrame;
        } else {
            console.warn(`未找到颜色 ${color} 对应的精灵帧`);
        }
    }

    /**
     * 创建渲染Splash牌面
     */
    private createRendererSplashBoard(parentNode: cc.Node): void {
        const board = this.logicWorld.getComponent(this.data.boardEntityId, BoardComponent);
        const totalBlocks = board.rCount * board.cCount;
        
        console.log(`开始创建 ${totalBlocks} 个渲染块...`);

        // 批量创建所有块
        for (let row = 0; row < board.rCount; row++) {
            for (let col = 0; col < board.cCount; col++) {
                const halfWidth = board.cCount * CellSize / 2;
                const halfHeight = board.rCount * CellSize / 2;
                const x = this.offsetX - halfWidth + CellSize / 2 + CellSize * col;
                const y = this.offsetY + halfHeight - CellSize / 2 - CellSize * row;
                const cellInfo = this.data.boardOccupy[row][col];
                console.log("cellInfo", cellInfo);
                let color = 0;
                let opacity = 0;
                if (cellInfo == -1) {
                    opacity = 255;
                    color = this.data.boardConfig[row][col] || 0;
                }
                else {
                    color = cellInfo
                }
                const node = this.createRendererBlock(x, y, color, opacity);
                if (!this.blockNodes[row]) {
                    this.blockNodes[row] = [];
                }
                this.blockNodes[row][col] = node;
                parentNode.addChild(node);
            }
        }

        console.log('渲染板面创建完成');
    }

    /**
     * 清理资源
     */
    private clearResources(): void {
        // 清理资源缓存
        this.resourceCache = {
            blockPrefab: null,
            blocksDarkAtlas: null,
            gemsAtlas: null
        };

        console.log('BoardRefurbishEffectView 资源清理完成');
    }

    /**
     * 方块刷新动画
     * @param time 动画时间
     * @param rCount 行数
     * @param cCount 列数
     */
    private upAnimation(time: number, rCount: number, cCount: number) {
        const blocks = this.blockNodes;
        for (let row = 0; row < rCount; row++) {
            this.scheduleOnce(() => {
                for (let col = 0; col < cCount; col++) {
                    console.log("blocks[row][col]", blocks[row][col]);
                    cc.tween(blocks[row][col]).to(time, { opacity: 255 }).start();
                }
            }, time * (7 - row));
        }
    }
}