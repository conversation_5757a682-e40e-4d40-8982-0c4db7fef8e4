//算法相关的定义写在这里，原则上不可引用其他脚本

export enum AlgoClassTypeConst {
    /**===========================类型枚举============================== */
    /** 随机 */
    AlgoRandom = 0,
    /** 熵增1 */
    AlgoEntropyIncrease1 = 1,
    /** 熵增3 */
    AlgoEntropyIncrease3 = 2,
    /** 简单难题 */
    AlgoSimpleHard = 3,
    /** 填空消除 */
    AlgoFillInTheBlank = 4,
    /** 熵减*/
    AlgoEntropyReduction = 5,
    /** 困难难题*/
    AlgoHard = 6,
    /** 随机无死题*/
    AlgoRandomNoDeath = 7,
    /** 直觉难题*/
    AlgoInstinctHard = 8,
    /** 致死题*/
    AlgoDeath = 9,
    /**===========================实例枚举============================== */
    /**底板-随机 */
    algo_random = 10001,
    /**底板-首次随机 */
    algo_random_first = 10002,
    /**底板-固定块池随机 */
    algo_random_fix_block = 10003,
    /**底板-熵增1 */
    algo_entropy_increase1 = 11001,
    /**底板-熵增3 */
    algo_entropy_increase3 = 12001,
    /**子母熵增3 */
    algo_entropy_increase3_zimu = 12002,
    /**底板-简单难题 */
    algo_simple_hard = 13001,
    /**底板-填空消除，边数不考虑消除 */
    algo_fill_in_the_blank = 14001,
    /**填空消除，边数考虑消除 */
    algo_fill_in_the_blank_clear = 14002,
    /**全组合填空消除9 */
    algo_fill_in_the_blank_id_9 = 14003,
    /**全组合填空消除21 */
    algo_fill_in_the_blank_id_21 = 14004,
    /**填空消除位置连贯（填空消除连贯） 不考虑消除*/
    algo_fill_in_the_blank_by_pos = 14005,
    /**填空消除位置连贯（填空消除连贯） 考虑消除*/
    algo_fill_in_the_blank_clear_by_pos = 14006,
    /**旅行填空消除*/
    algo_fill_in_the_blank_journey = 14007,
    /**高效消除*/
    algo_fill_in_the_blank_high_effect = 14008,
    /**全组合填空消除70 */
    algo_fill_in_the_blank_id_70 = 14009,
    /**消除爽 */
    algo_fill_in_the_blank_clear_cool = 14010,
    /**连贯旅行填空消除 */
    algo_fill_in_the_blank_journey_by_pos = 14011,
    /**底板-熵减 */
    algo_entropy_reduction = 15001,
    /**底板-困难难题*/
    algo_hard = 16001,
    /**简单直觉难题*/
    algo_simple_instinct_hard = 16002,
    /**简单死亡*/
    algo_simple_death = 16003,
    /**底板-随机无死亡 */
    algo_random_no_death = 17001,
    /**底板-直觉难题 */
    algo_instinct_hard = 18001,
    /**底板-致死题 */
    algo_death = 19001,
    /**复活算法 */
    algo_revive = 20001,
    /**清屏算法 */
    algo_clear_board = 21001,
    /**终止刷分算法 */
    algo_stop_refresh_record = 22001,
    /**连续两轮出块相似处理算法 */
    algo_same_more_block = 23001,
}
