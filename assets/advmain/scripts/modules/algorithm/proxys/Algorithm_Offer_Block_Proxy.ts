import { E_Algorithm_Offer_Block } from "../events/E_Algorithm_Offer_Block";
import { AlgoExecuteAdapterOutputData } from "../list/algoSDK/puzzle/algoPuzzle";
import { PuzzleUtil } from "../list/algoSDK/PuzzleUtil";
import { algorithmSDKArgsInfo } from "../vo/AlgorithmSDKArgsInfo";
import { algorithmInfo } from "../vo/AlgorithmInfo";
import { algorithmName } from "../vo/AlgorithmName";
import { algorithmSource, OFFER_TYPE } from "../type/AlgorithmType";
import { E_Levels_ProduceBlockEnd } from "../../level/events/E_Levels_ProduceBlockEnd";
import { AlgoClassTypeConst } from "../list/algoSDK/core/puzzlecore";


export class Algorithm_Offer_Block_Proxy extends falcon.Proxy {
    protected onInit(): void {

    }

    registerEvents(): { new(...args: any): falcon.ModuleEvent; }[] | null {
        return [
            E_Algorithm_Offer_Block
        ];
    }

    receivedEvents($event: falcon.ModuleEvent): void {
        switch ($event.getClass()) {
            case E_Algorithm_Offer_Block:
                // 出块
                this.onOfferBlock($event as E_Algorithm_Offer_Block);
                break;
        }
    }

    async onOfferBlock(event: E_Algorithm_Offer_Block) {
        console.log('Algorithm_Offer_Block_Proxy 开始出块');
        const boardOccupy = event.boardData.boardOccupy;
        console.log('boardOccupy', boardOccupy);
        const args = algorithmSDKArgsInfo.getArgs(boardOccupy);
        let algorithmId = event.boardData.algorithmId;
        if (!algorithmId) {
            algorithmId = AlgoClassTypeConst.algo_random_no_death;
        }
        console.log('algorithmId', algorithmId);
        const ret = await PuzzleUtil.offerTopic(args, algorithmId);
        this.resetSDKArgs();
        if (ret.blockIds.length == 3) {
            algorithmInfo['_algoSource'] = algorithmSource.SUCCESS;
            console.log("成功策略 result 算法结果 == ", algorithmInfo.blockIdList);
            falcon.EventManager.dispatchModuleEvent(new E_Levels_ProduceBlockEnd(ret));
        } else {
            console.log("成功策略 result 失败 结果 == ", algorithmInfo.blockIdList);
        }
    }

    // 重置sdkargs
    resetSDKArgs() {
        algorithmSDKArgsInfo.setBlockIds([0, 0, 0]);
        algorithmSDKArgsInfo.setFilterBlocks([]);
        algorithmSDKArgsInfo.setOverTime(100);
    }

    // 算法结果处理
    triggerAlgorithmResult(result: AlgoExecuteAdapterOutputData) {
        // this.algorithmResultCheck(result.blockIds);
        algorithmInfo.setBlockIdList(result.blockIds);
        algorithmName.setAlgoActualName(result.blockNames);
        algorithmInfo.setBlockPosList(result.blockPoses);
        console.log('algorithmName.algoActualName ', algorithmName.algoActualName);
    }

    // algorithmResultCheck(result: number[]) {
    //     if (!CC_DEBUG) {
    //         return;
    //     }
    //     if (algorithmName.algoActualId == OFFER_TYPE.REVIVE) {
    //         return;
    //     }
    //     let isValid = false;
    //     if (result.includes(1)
    //         && scoreInfo.score > 2000) {
    //         isValid = true;
    //     } else if (this.containsAllElements(result, SMALL_BLOCKS)
    //         && algorithmName.algoActualId != OFFER_TYPE.SUI_JI_WU_SI) {
    //         isValid = true;
    //     }
    //     if (isValid) {
    //         const algorithmResult = <algorithmResult>{};
    //         algorithmResult.faceBlocks = boardInfo.faceBlocks;
    //         algorithmResult.blockList = result;
    //         algorithmResult.algorithmType = algorithmName.algoActualId;
    //         let resultList = algorithmDebug.algorithmCheckLists;
    //         if (resultList.length < 5) {
    //             resultList.push(algorithmResult);
    //         } else {
    //             resultList = ensureMaxLength(resultList, 5, algorithmResult);
    //         }
    //         falcon.storage.setItem("chapterAlgorithmCheck", resultList);
    //         console.error("算法结果检测有问题");
    //     }
    // }
}