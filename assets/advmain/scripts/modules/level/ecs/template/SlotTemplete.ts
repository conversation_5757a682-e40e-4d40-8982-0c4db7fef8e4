import { RelationName } from '../cores/center/EntityCenter/WorldRelation';
import SlotComponent from '../components/board/SlotComponent';
import NodeComponent from '../components/NodeComponent';
import { RenderComponent } from '../components/RenderComponent';
import { TempleteBase } from '../cores/center/TempleteCenter';
import { CellSize, BoardLayer } from '../define/BoardDefine';
import { RowCol, ISize } from '../define/EcsDefine';

/**
 * 格子模板必传参数
 */
export interface SlotTempleteMustArgs {
    /** 行列坐标 */
    rc: RowCol;
    /** 父容器尺寸 */
    parentSize: ISize;
    /** 父实体ID */
    parentEntity: number;
}

/**
 * 格子模板可选参数
 */
export interface SlotTempleteOptionalArgs {}

/**
 * 格子模板
 */
export class SlotTemplete extends TempleteBase<SlotTempleteMustArgs, SlotTempleteOptionalArgs> {
    getDefaultOptionalArgs(mustArgs: SlotTempleteMustArgs): SlotTempleteOptionalArgs {
        return {};
    }
    createTemplete(mustArgs: SlotTempleteMustArgs, optionalArgs: SlotTempleteOptionalArgs): number {
        const { rc, parentSize, parentEntity } = mustArgs;
        const world = this.world;
        const slotEntity = world.createEntity();
        world.addRelation(parentEntity, slotEntity, RelationName.PARENT_CHILD, `${rc.r}_${rc.c}`);
        world.addComponent(slotEntity, SlotComponent, rc);
        world.addComponent(slotEntity, NodeComponent, {
            x: (-parentSize.width * CellSize) / 2 + CellSize / 2 + rc.c * CellSize,
            y: (parentSize.height * CellSize) / 2 - CellSize / 2 - rc.r * CellSize,
        });
        world.addComponent(slotEntity, RenderComponent, 'ecsview_slot', { childrenPaths: [BoardLayer[BoardLayer.Bg]] });

        return slotEntity;
    }
}
