// Copyright (c) 2017-2018 Xiamen Yaji Software Co., Ltd.

CCEffect %{
  techniques:
  - passes:
    - vert: vs
      frag: fs
      blendState:
        targets:
        - blend: true
      rasterizerState:
        cullMode: none
      properties:
        texture: { value: white }
        alphaThreshold: { value: 0.5 }
        width: { 
          value: 0.2, 
          editor: {
            tooltip: "流光宽度[0-1]"
          } 
        }
        angle: {
          value: 30.0,
          editor: {
            tooltip: "角度"
          }
        }
        px: {
          value: 0.0,
          editor: {
            tooltip: "时间轴上的进度"
          }
        }
        strengthen: {
          value: 1.1,
          editor: {
            tooltip: "反射光强化度（取值要大于1）"
          }
        }
}%


CCProgram vs %{
  precision highp float;

  #include <cc-global>
  #include <cc-local>

  in vec3 a_position;
  in vec4 a_color;
  out vec4 v_color;

  #if USE_TEXTURE
  in vec2 a_uv0;
  out vec2 v_uv0;
  #endif

  void main () {
    vec4 pos = vec4(a_position, 1);

    #if CC_USE_MODEL
    pos = cc_matViewProj * cc_matWorld * pos;
    #else
    pos = cc_matViewProj * pos;
    #endif

    #if USE_TEXTURE
    v_uv0 = a_uv0;
    #endif

    v_color = a_color;

    gl_Position = pos;
  }
}%


CCProgram fs %{
  precision highp float;

  #include <alpha-test>
  #include <texture>

  in vec4 v_color;

  #if USE_TEXTURE
  in vec2 v_uv0;
  uniform sampler2D texture;
  #endif

  uniform ARGS {
    float width;
    float angle;
    float px;
    float strengthen;
  };

  const float PI = 3.1415;

  void main () {
    vec4 o = vec4(1, 1, 1, 1);

    #if USE_TEXTURE
      CCTexture(texture, v_uv0, o);
    #endif
    //angle是角度，这里需要把角度转成弧度
    //angle是垂直Y方向上的倾斜角度
    float rad =  PI * angle / 180.;
    float rid = PI * (90. - angle) / 180.;
    //透明度过滤
    if(o.a >= 1.){
      //px是未倾斜时扫光矩形的x方向的中心（不是倾斜后平行四边形的中心）
      //扫光的x边界判断
      if((1.-v_uv0.y) >= (px - width /2.) && (1.-v_uv0.y) <= (px + width / 2.+ tan(rad))){
        //扫光矩形左边倾斜后Y边界
        float leftY = (1. - tan(rid) * (1. - v_uv0.y - px + width/2.));
        //扫光矩形右边倾斜后Y边界
        float righgY = 1. - tan(rid) * (1. - v_uv0.y - (px + width/2.));
        //扫光的y边界判断
        if(v_uv0.x >= leftY && v_uv0.x <= righgY){
            //色值加强（反射加强）
            o *= strengthen;
        }
      }
    }

    o *= v_color;

    ALPHA_TEST(o);

    #if USE_BGRA
      gl_FragColor = o.bgra;
    #else
      gl_FragColor = o.rgba;
    #endif
  }
}%
