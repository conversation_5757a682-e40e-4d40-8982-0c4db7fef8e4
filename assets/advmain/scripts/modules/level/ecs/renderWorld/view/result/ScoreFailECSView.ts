import { PrefabConfig } from '../../../../../prefab/PrefabConfig';
import TargetComponent from '../../../components/special/TargetComponent';
import { TargetType } from '../../../define/BoardDefine';
import { ECSEvent } from '../../../GameEvent';
import { MathUtil } from '../../../utils/MathUtil';
import { ECSViewBase } from '../../ECSViewBase';

/**分数关卡失败视图 */
export class ScoreFailECSView extends ECSViewBase {
    private basePosX: number = 0;
    protected initView() {
        this.basePosX = this.ui.scoreBg.x;
        this.ui.scoreImg.y = 130;
        const targets = this.getTargetList();
        const target = targets.find((t) => t.key === TargetType.Score);
        this.ui.curNum.getComponent(cc.Label).string = `${target.current}`;
        this.ui.goalNum.getComponent(cc.Label).string = `${target.to}`;
        this.resetBtnState();
        this.showAction();
    }
    protected addEvents() {
        this.ui.backBtn.on(cc.Node.EventType.TOUCH_END, this.onClickBack, this);
        this.ui.playBtn.on(cc.Node.EventType.TOUCH_END, this.onClickPlay, this);
    }
    protected removeEvents() {
        this.ui.backBtn.off(cc.Node.EventType.TOUCH_END, this.onClickBack, this);
        this.ui.playBtn.off(cc.Node.EventType.TOUCH_END, this.onClickPlay, this);
    }
    protected disposeView() {
        this.resetBtnState();
    }

    resetBtnState() {
        this.ui.backBtn.active = false;
        this.ui.playBtn.active = false;
        this.ui.progress.active = false;
        this.ui.scoreImg.active = false;
        this.ui.scoreBg.x = this.basePosX;
        this.ui.backBtn.scale = 0;
        this.ui.playBtn.scale = 0;
        this.ui.boneAni_1.active = false;
    }

    /**播放动作 */
    showAction(): void {
        const targets = this.getTargetList();
        const target = targets.find((t) => t.key === TargetType.Score);
        // if (this.state.isShowDefTip) {
        // this.ui.boneAni.active = true;
        // this.ui.boneAni.getComponent(dragonBones.ArmatureDisplay).playAnimation(`newAnimation`, 1);
        // } else {
        this.ui.boneAni_1.active = false;
        // }
        cc.tween(this.node)
            .delay(0.57)
            .call(() => {
                this.ui.boneAni_1.active = true;
                this.ui.boneAni_1;
                let AniNameArr = ['KeepFighting', 'YouCanDoIt', 'notThisTime', 'TryAgain'];
                if (this.ui.boneAni_1) {
                    this.ui.boneAni_1.active = true;
                    const tagIndex = MathUtil.randomInt(0, 4);
                    let nameStr = AniNameArr[tagIndex];
                    this.ui.boneAni_1.getComponent(dragonBones.ArmatureDisplay).playAnimation(nameStr, 1);
                }
            })
            .delay(0.33)
            .call(() => {
                // this.showOtherTraitAnim();
                // 目标面板弹出音效
                // audioInfo.play(ChapterAudioConfig.travel_score_change);

                this.ui.progress.active = true;
                this.ui.mask1.width = 0.1;
                const x = this.basePosX + Math.floor((target.current / target.to) * 531);
                cc.tween(this.ui.mask1)
                    .to(0.3, { width: Math.floor((target.current / target.to) * 531) })
                    .start();
                cc.tween(this.ui.scoreBg).to(0.3, { x: x }).start();
            })
            .delay(1.1)
            .call(() => {
                this.showBtnAnim();
            })
            .start();
    }

    showBtnAnim() {
        this.ui.backBtn.active = true;
        this.ui.backBtn.scale = 0.6;
        this.ui.playBtn.active = true;
        this.ui.playBtn.scale = 0.6;
        // 按钮出现音效
        // audioInfo.play(AudioConfig.s_btnShow);

        cc.tween(this.ui.backBtn)
            .to(0.13, { scale: 1.1 })
            .to(0.07, { scale: 1 })
            .call(() => {
                this.ui.backBtn.getComponent(cc.Button).interactable = true;
            })
            .start();

        cc.tween(this.ui.playBtn)
            .to(0.13, { scale: 1.1 })
            .to(0.07, { scale: 1 })
            .call(() => {
                this.ui.playBtn.getComponent(cc.Button).interactable = true;
            })
            .start();
    }

    onClickPlay(event: cc.Event.EventTouch, data: any) {
        this.logicWorld.pushInputCmd({
            event: ECSEvent.GameEvent.GAME_RETRY,
            data: null,
        });
        this.renderWorld.destroyECSView(this);
    }

    onClickBack(event: cc.Event.EventTouch, data: any) {
        this.renderWorld.destroyECSView(this);
        this.logicWorld.pushInputCmd({
            event: ECSEvent.GameEvent.GAME_RETRY,
            data: null,
        });
        falcon.UI.show(PrefabConfig.ChapterMain);
    }

    getTargetList() {
        const targets = this.logicWorld.getComponent(this.logicWorld.query([TargetComponent])[0], TargetComponent).targets;
        return targets;
    }
}
