import { Component } from '../cores/Component';
import { IPoint } from '../define/EcsDefine';

/**节点组件 */
export default class NodeComponent extends Component {
    /**位置x */
    public x: number;
    /**位置y */
    public y: number;
    /**旋转角度 */
    public angle: number;
    /**x轴缩放 */
    public scaleX: number;
    /**y轴缩放 */
    public scaleY: number;
    /**z轴层级，用于控制同一父节点下的渲染顺序 */
    public zIndex: number;

    constructor(
        initParam: {
            /**位置，与x,y互斥 */
            position?: IPoint;
            x?: number;
            y?: number;
            /**旋转 */
            angle?: number;
            /**整体缩放，与scaleX,scaleY互斥 */
            scale?: number;
            scaleX?: number;
            scaleY?: number;
            /**z轴层级，用于控制同一父节点下的渲染顺序 */
            zIndex?: number;
        } = {},
    ) {
        super();
        if (initParam.position) {
            this.x = initParam.position.x;
            this.y = initParam.position.y;
        } else {
            this.x = initParam.x || 0;
            this.y = initParam.y || 0;
        }
        this.angle = initParam.angle || 0;
        if (initParam.scale === undefined) {
            this.scaleX = initParam.scaleX || 1;
            this.scaleY = initParam.scaleY || 1;
        } else {
            this.scaleX = initParam.scale;
            this.scaleY = initParam.scale;
        }
        this.zIndex = initParam.zIndex || 0;
    }
}
