import NodeComponent from "../components/NodeComponent";
import { RenderComponent } from "../components/RenderComponent";
import TargetComponent from "../components/special/TargetComponent";
import { RelationName } from "../cores/center/EntityCenter/WorldRelation";
import { TempleteBase } from "../cores/center/TempleteCenter";


export interface TargetTempleteMustArgs {
    /**父节点 */
    parentEntity: number;
    /**目标列表 */
    targetList: number[][];
}

export interface TargetTempleteOptionalArgs {
    /**目标x坐标 */
    x: number;
}

/**目标模板 */
export class TargetTemplate extends TempleteBase<TargetTempleteMustArgs, TargetTempleteOptionalArgs> {
    getDefaultOptionalArgs(mustArgs: TargetTempleteMustArgs): TargetTempleteOptionalArgs {
        return { x: 0 };
    }
    createTemplete(mustArgs: TargetTempleteMustArgs, optionalArgs: TargetTempleteOptionalArgs): number {
        const targetEntity = this.world.createEntity();
        this.world.addRelation(mustArgs.parentEntity, targetEntity, RelationName.PARENT_CHILD);
        this.world.addComponent(targetEntity, TargetComponent, mustArgs.targetList);
        this.world.addComponent(targetEntity, NodeComponent, { x: optionalArgs.x, y: 600, zIndex: 10 });
        this.world.addComponent(targetEntity, RenderComponent, 'ecsview_target');

        return targetEntity;
    }
}
