#环境：Mac + vscode ^1.82.0以上 + npm

#先手动安装vscode code命令行：
#打开 Visual Studio Code，并按下快捷键 `Command + Shift + P`，
#然后输入 `Shell Command` ，选择 `Shell Command : Install 'code' command in PATH`，这将会安装 `code` 命令。

#项目根目录下，存放本文件，并执行./eslint.sh

# 使用eslint做语法检查，使用prettier做格式化
code --install-extension dbaeumer.vscode-eslint
code --install-extension esbenp.prettier-vscode

npm install eslint@8.57.0 --save-dev
npm install @typescript-eslint/eslint-plugin --save-dev
npm install @typescript-eslint/parser --save-dev

# 判断.vscode文件夹是否存在
if [ ! -d ".vscode" ]; then
  mkdir .vscode
fi

echo "{
    \"eslint.run\": \"onSave\",
    \"eslint.validate\": [\"javascript\", \"javascriptreact\", \"typescript\", \"typescriptreact\"],
    \"eslint.useFlatConfig\": false,
    \"eslint.options\": {
        \"overrideConfigFile\": \".eslintrc.js\"
    },
    // 禁用 ESLint 的自动修复
    \"editor.codeActionsOnSave\": {
        \"source.removeUnused.ts\": \"never\"
    },
    \"editor.formatOnSave\": true,
    \"prettier.enable\": true,
    \"prettier.requireConfig\": true,
    \"[typescript]\": {
        \"editor.defaultFormatter\": \"esbenp.prettier-vscode\",
        \"editor.formatOnSave\": true
    },
    \"files.exclude\": {
        \"**/**.meta\": true,
    }
}
" > .vscode/settings.json

rules="{
    \"env\": {
        \"browser\": true,
        \"es2021\": true
    },
    \"extends\": [\"eslint:recommended\", \"plugin:@typescript-eslint/recommended\"],
    \"parser\": \"@typescript-eslint/parser\",
    \"parserOptions\": {
        \"ecmaVersion\": 12
    },
    \"plugins\": [\"@typescript-eslint\"],
    \"rules\": {
        // \"off\":当前规则关闭   \"warn\": 当前规则显示警告    \"error\": 当前规则显示为错误
        \"no-eq-null\": \"warn\", // 禁止使用null == or !=,必须使用 null === or !==
        \"no-case-declarations\": \"off\", //关闭
        \"camelcase\": \"off\", // 规则驼峰命名法 暂时关闭
        \"no-cond-assign\": \"error\", //禁止在条件表达式中使用赋值语句
        \"no-const-assign\": \"error\", //禁止修改const声明的变量
        \"no-constant-condition\": \"error\", //禁止在条件中使用常量表达式 if(true) if(1)
        \"no-else-return\": \"error\", //如果if语句里面有return,后面不能跟else语句
        \"no-empty\": \"warn\", //块语句中的内容不能为空
        \"no-extra-bind\": \"error\", //禁止不必要的函数绑定
        \"no-floating-decimal\": \"error\", //禁止省略浮点数中的0 .5 3.
        \"no-implicit-coercion\": \"off\", //禁止隐式转换 暂时关闭
        \"no-lonely-if\": \"error\", //禁止else语句内只有if语句
        \"no-return-assign\": \"warn\", //return 语句中不能有赋值表达式
        \"no-shadow\": \"off\", //外部作用域中的变量不能与它所包含的作用域中的变量或参数同名
        \"no-sparse-arrays\": \"error\", //禁止稀疏数组， [1,,2]
        \"no-this-before-super\": \"error\", //在调用super()之前不能使用this或super
        \"no-unused-vars\": [
            \"off\",
            {
                \"vars\": \"all\",
                \"args\": \"none\"
            }
        ], //不能有声明后未被使用的变量或参数
        \"no-var\": \"error\", //禁用var，用let和const代替
        \"complexity\": [\"off\", 4], //循环复杂度 不超过4个写在一个嵌套中 因为方法中每if也算作了一个，所以暂时关闭
        \"curly\": \"off\", //必须使用 if(){} 中的{}
        \"eqeqeq\": \"warn\", //必须使用全等
        \"max-depth\": [\"error\", 4], //嵌套块深度
        \"prefer-const\": \"warn\", // 首选const
        \"@typescript-eslint/no-inferrable-types\": \"off\", // 关闭类型推断
        \"@typescript-eslint/ban-types\": \"off\", // 关闭不能使用特定类型
        \"no-console\": \"off\", // 禁止使用console.log()打印
        \"@typescript-eslint/no-unused-vars\": \"error\", // 未使用变量
        \"@typescript-eslint/no-empty-function\": \"off\", // 不能使用空方法 暂时关闭
        \"@typescript-eslint/no-explicit-any\": \"warn\", // 不能使用any
        \"accessor-pairs\": \"error\", //强制 getter 和 setter 在对象中成对出现,
        \"@typescript-eslint/no-empty-interface\": \"off\", // 接口不能继承实体类
        \"@typescript-eslint/no-this-alias\": \"off\", // this不能赋值给临时变量
        \"max-classes-per-file\": [\"error\", 1], //强制每个文件中包含的的类的最大数量
        \"default-case\": \"error\",
        \"@typescript-eslint/dot-notation\": \"off\",
        \"@typescript-eslint/no-unsafe-function-type\": \"off\",
        \"@typescript-eslint/no-unused-expressions\": \"off\",
    }
}

"
echo "$rules" > .eslintrc.json
echo "module.exports = $rules" > .eslintrc.js

echo "module.exports = {
    printWidth: 150,
    tabWidth: 4,
    semi: true,
    singleQuote: true,
    trailingComma: 'all',
    arrowParens: 'always',
};
" > .prettierrc.js
