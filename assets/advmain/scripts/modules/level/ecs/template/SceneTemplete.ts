import { BoardScene } from "../components/board/BoardScene";
import NodeComponent from "../components/NodeComponent";
import { RenderComponent } from "../components/RenderComponent";
import ReviveComponent from "../components/ReviveComponent";
import { TempleteBase } from "../cores/center/TempleteCenter";

/**
 * 场景模板必传参数
 */
export interface SceneTempleteMustArgs {
    
}

/**
 * 场景模板可选参数
 */
export interface SceneTempleteOptionalArgs {
    
}

/**
 * 棋盘场景模板
 */
export class BoardSceneTemplete extends TempleteBase<SceneTempleteMustArgs, SceneTempleteOptionalArgs> {
    getDefaultOptionalArgs(mustArgs: SceneTempleteMustArgs): SceneTempleteOptionalArgs {
        return {};
    }
    createTemplete(mustArgs: SceneTempleteMustArgs, optionalArgs?: SceneTempleteOptionalArgs): number {
        const world = this.world;
        const entity = world.createEntity();
        world.addComponent(entity, BoardScene);
        world.addComponent(entity, NodeComponent);
        world.addComponent(entity, RenderComponent, "ecsview_init_board_scene");
        world.addComponent(entity, ReviveComponent, 2);
        
        return entity;
    }
} 