import { ECSViewBase } from '../renderWorld/ECSViewBase';
import { ECSViewRegistry } from '../registry/ECSViewRegistry';
import { World } from './World';
import { RenderCmdBase } from '../renderWorld/RenderCmdBase';
import NodeComponent from '../components/NodeComponent';
import { CreateECSViewRenderCmd } from '../renderWorld/cmd/CreateECSViewRenderCmd';
import OpacityComponent from '../components/OpacityComponent';
import { ECSEvent, GameEventMap } from '../GameEvent';
import { EventBus, RenderEventBus } from './EventBus';
import { UITransformComponent } from '../components/UITransformComponent';
import { EcsAudioConfigHelper, AudioId } from '../config/conf/EcsAudioConfig';
import { audioInfoData } from '../../../audio/vo/AudioInfoData';
import { SubGameBridge } from '../../../../base/SubGameBridge';
import { FullScreenShakeManager } from './FullScreenShakeManager';
import { IPoint } from '../define/EcsDefine';

/**
 * 渲染世界
 * 负责管理ECS视图的创建、更新、销毁和渲染命令的执行
 */
export class RenderWorld {
    // ==================== 私有属性 ====================

    /** 逻辑世界实例 */
    private _logicWorld: World;

    /** 渲染根节点 */
    private _rootNode: cc.Node;

    /** 是否已经销毁 */
    private _isDestroy: boolean;

    /** 渲染实体映射表 */
    private _ecsViewMap: Map<number, ECSViewBase>;

    /** 渲染命令映射表 */
    private _renderCmdMap: Map<number, RenderCmdBase<any>>;

    /** 渲染命令队列 */
    private _renderCmds: RenderCmdBase<any>[];

    /** 待销毁实体集合 */
    private _destroyEntitySet: Set<number>;

    /** 占位节点管理器 */
    private _placeholderManager: PlaceholderManager;

    /** 全屏震动管理器 */
    private _fullScreenShakeManager: FullScreenShakeManager;

    /** 渲染事件总线 */
    public readonly eventBus: RenderEventBus;
    // ==================== 生命周期方法 ====================

    /**
     * 初始化渲染世界
     * @param world 逻辑世界实例
     * @param _rootNode 场景节点
     */
    public init(world: World, _rootNode: cc.Node): void {
        this._logicWorld = world;
        this._rootNode = _rootNode;
        (this.eventBus as any) = new EventBus();
        this.initializeCollections();
        this.initializeManagers();
        this.initializeShakeManager();
        this.addEventListeners();
    }

    /**
     * 销毁渲染世界
     */
    public dispose(): void {
        this.destroyAllECSViews();
        this.cleanupRenderCommands();
        this.clearCollections();
        this.cleanupManagers();
        this.removeEventListeners();
        this.markAsDestroyed();
    }

    /**
     * 更新渲染世界
     * @param dt 帧间隔时间
     */
    public update(dt: number): void {
        this.executeRenderCmds();
        this.updateEcsView();
    }

    // ==================== 公共API ====================

    /**
     * 创建渲染命令
     * @param entityId 实体ID
     * @returns 创建的渲染命令
     */
    public createRenderCmd(entityId: number): RenderCmdBase {
        const renderCmd = new CreateECSViewRenderCmd();
        renderCmd.initCmd(null, entityId, this, this._logicWorld);
        return renderCmd;
    }

    /**
     * 添加渲染命令
     * @param cmd 渲染命令
     */
    public addRenderCmd(cmd: RenderCmdBase): void {
        if (this._isDestroy || !cmd) {
            if (!cmd) {
                console.error('渲染命令为空,终止添加');
            }
            return;
        }

        this._renderCmdMap.set(cmd.entityId, cmd);

        if (this.tryAddAsSubCommand(cmd)) {
            return;
        }

        this._renderCmds.push(cmd);
    }

    /**
     * 移除渲染命令
     * @param cmd 渲染命令
     */
    public removeRenderCmd(cmd: RenderCmdBase): void {
        if (this._isDestroy) {
            return;
        }

        this._renderCmdMap.delete(cmd.entityId);
        this.redistributeSubCommands(cmd);
    }

    /**
     * 销毁实体
     * @param entityId 实体ID
     */
    public destroyEntity(entityId: number): void {
        const cmd = this._renderCmdMap.get(entityId);
        if (cmd) {
            this.handleRemoveRenderCommand(cmd, entityId);
        }

        this.destroyExistingView(entityId);
    }

    /**
     * 创建ECS视图（使用占位节点保证顺序）
     * @param ecsViewConfigId 视图配置ID
     * @param logicEntityId 逻辑实体ID
     * @param parentEntityId 父实体ID
     * @param children 子节点路径
     * @returns 创建的ECS视图
     */
    public async createECSView(
        ecsViewConfigId: string,
        logicEntityId: number,
        parentEntityId: number,
        children: string[],
    ): Promise<ECSViewBase | null> {
        if (this._isDestroy) {
            return null;
        }

        const ecsViewConfig = this.getECSViewConfig(ecsViewConfigId);
        if (!ecsViewConfig) {
            return null;
        }

        return await this.createViewWithPlaceholder(ecsViewConfig, logicEntityId, parentEntityId, children);
    }

    /**
     * 销毁ECS视图
     * @param view ECS视图
     */
    public destroyECSView(view: ECSViewBase): void {
        if (this._isDestroy || !view) {
            return;
        }

        this._ecsViewMap.delete(view.logicEntityId);
        view.dispose();
        this.destroyViewNode(view.node);
    }

    /**
     * 加载资源
     * @param bundleName 资源包名
     * @param paths 资源路径
     * @param type 资源类型
     * @returns 加载的资源
     */
    public async loadRes<T extends cc.Asset>(bundleName: string, paths: string, type?: typeof cc.Asset): Promise<T> {
        let res: T = null;
        if (paths) {
            res = await SubGameBridge.asyncLoadByBundle(bundleName, paths, type);
            if (this._isDestroy) return Promise.reject('RenderWorld已经被销毁');
        }
        return res;
    }

    // ==================== 私有初始化方法 ====================

    /**
     * 初始化集合
     */
    private initializeCollections(): void {
        this._ecsViewMap = new Map();
        this._renderCmdMap = new Map();
        this._destroyEntitySet = new Set();
        this._renderCmds = [];
        this._isDestroy = false;
    }

    /**
     * 初始化管理器
     */
    private initializeManagers(): void {
        this._placeholderManager = new PlaceholderManager(this._ecsViewMap, this._rootNode);
    }

    private initializeShakeManager(): void {
        this._fullScreenShakeManager = new FullScreenShakeManager();
    }

    /**
     * 标记为已销毁
     */
    private markAsDestroyed(): void {
        this._isDestroy = true;
        this._placeholderManager = null;
    }

    // ==================== 事件管理 ====================

    /**
     * 添加事件监听器
     */
    private addEventListeners(): void {
        this._logicWorld.eventBus.on(ECSEvent.BaseEvent.SNAPSHOT_RESTORE_RENDER_DONE, this.__onSnapshotDone, this);
        this._logicWorld.eventBus.on(ECSEvent.BaseEvent.PLAY_AUDIO, this.__onPlayAudio, this);
        this._logicWorld.eventBus.on(ECSEvent.BaseEvent.STOP_AUDIO, this.__onStopAudio, this);
        this._logicWorld.eventBus.on(ECSEvent.BaseEvent.FULL_SCREEN_SHAKE, this.__onFullScreenShake, this);
    }

    /**
     * 移除事件监听器
     */
    private removeEventListeners(): void {
        if (this._logicWorld) {
            this._logicWorld.eventBus.off(ECSEvent.BaseEvent.SNAPSHOT_RESTORE_RENDER_DONE, this.__onSnapshotDone, this);
            this._logicWorld.eventBus.off(ECSEvent.BaseEvent.PLAY_AUDIO, this.__onPlayAudio, this);
            this._logicWorld.eventBus.off(ECSEvent.BaseEvent.STOP_AUDIO, this.__onStopAudio, this);
            this._logicWorld.eventBus.off(ECSEvent.BaseEvent.FULL_SCREEN_SHAKE, this.__onFullScreenShake, this);
        }
    }

    /**
     * 快照恢复完成事件处理器
     */
    private __onSnapshotDone(): void {
        this._ecsViewMap.forEach((view, entityId) => {
            const node = view.node;
            if (!cc.isValid(node)) {
                console.error(`节点不存在: ${entityId}, view: ${view?.constructor?.name}`);
                this._ecsViewMap.delete(entityId);
                return;
            }
            this.updateEcsViewNodeComponent(node, entityId);
            this.updateEcsViewOpacityComponent(node, entityId);
        });
    }

    // ==================== 渲染命令管理 ====================

    /**
     * 逐帧执行渲染命令，控制在可配置的时间预算内，避免瞬时卡顿。
     * 默认预算 3ms，可按需调整 frameBudgetMs。
     */
    private _frameBudgetMs = 3;
    public executeRenderCmds(): void {
        if (this._isDestroy || this._renderCmds.length === 0) return;

        const start = performance.now ? performance.now() : Date.now();
        // 在预算时间内尽可能多执行命令
        while (this._renderCmds.length > 0) {
            const cmd = this._renderCmds.shift();
            cmd.execute();

            // 超出预算立即停
            const now = performance.now ? performance.now() : Date.now();
            if (now - start >= this._frameBudgetMs) break;
        }
    }

    /**
     * 尝试添加为子命令
     */
    private tryAddAsSubCommand(cmd: RenderCmdBase): boolean {
        if (!cmd.parentEntityId) {
            return false;
        }

        const parentCmd = this._renderCmdMap.get(cmd.parentEntityId);
        if (parentCmd) {
            parentCmd.subCmds.push(cmd);
            return true;
        }

        return false;
    }

    /**
     * 重新分配子命令
     */
    private redistributeSubCommands(cmd: RenderCmdBase): void {
        if (cmd.subCmds) {
            for (const subCmd of cmd.subCmds) {
                this.addRenderCmd(subCmd);
            }
        }
    }

    /**
     *  处理移除渲染命令
     */
    private handleRemoveRenderCommand(cmd: RenderCmdBase, entityId: number): void {
        if (!cmd.isExecuting) {
            this.removeCommandFromQueue(cmd);
        } else {
            this._destroyEntitySet.add(entityId);
        }
    }

    /**
     * 从队列中移除命令
     */
    private removeCommandFromQueue(cmd: RenderCmdBase): void {
        this._renderCmdMap.delete(cmd.entityId);
        const index = this._renderCmds.indexOf(cmd);
        if (index !== -1) {
            this._renderCmds.splice(index, 1);
        }
    }

    // ==================== ECS视图管理 ====================

    /**
     * 获取ECS视图配置
     */
    private getECSViewConfig(ecsViewConfigId: string): any {
        const ecsViewConfig = this._logicWorld.configCenter.getEcsViewConfig(ecsViewConfigId);
        if (!ecsViewConfig) {
            console.error(`ecsViewConfigId:${ecsViewConfigId} 不存在`);
            return null;
        }

        if (!ECSViewRegistry[ecsViewConfig.ecsViewName]) {
            console.error(`ecsViewName:${ecsViewConfig.ecsViewName} 不存在`);
            return null;
        }

        return ecsViewConfig;
    }

    /**
     * 使用占位节点创建视图
     */
    private async createViewWithPlaceholder(
        ecsViewConfig: any,
        logicEntityId: number,
        parentEntityId: number,
        children: string[],
    ): Promise<ECSViewBase | null> {
        // 创建占位节点
        this._placeholderManager.createPlaceholder(logicEntityId, parentEntityId, children);

        // 异步加载预制体
        const prefab = await this.loadViewPrefab(ecsViewConfig);
        if (!prefab) {
            this.handleViewCreationFailure(logicEntityId);
            return null;
        }

        // 检查是否在加载期间被标记为销毁
        if (this.isMarkedForDestroy(logicEntityId)) {
            this.handleViewCreationFailure(logicEntityId);
            return null;
        }
        // 创建真正的视图
        return this.createRealView(prefab, ecsViewConfig, logicEntityId);
    }

    /**
     * 加载视图预制体
     */
    private async loadViewPrefab(ecsViewConfig: any): Promise<cc.Prefab | null> {
        const prefab: cc.Prefab = await this.loadRes(ecsViewConfig.prefabBundleName, ecsViewConfig.prefabUrl, cc.Prefab);

        if (!prefab) {
            console.error(`prefab:${ecsViewConfig.prefabUrl} 不存在`);
        }

        return prefab;
    }

    /**
     * 检查是否被标记为销毁
     */
    private isMarkedForDestroy(logicEntityId: number): boolean {
        return this._destroyEntitySet.has(logicEntityId);
    }

    /**
     * 创建真正的视图
     */
    private createRealView(prefab: cc.Prefab, ecsViewConfig: any, logicEntityId: number): ECSViewBase {
        const node: cc.Node = cc.instantiate(prefab);
        if (node.name === 'emptyNode') node.name = ecsViewConfig.ecsViewId;
        const view: ECSViewBase = node.addComponent(ECSViewRegistry[ecsViewConfig.ecsViewName]);

        view.init(this._logicWorld, this, logicEntityId, ecsViewConfig);

        // 如果配置了蒙版，创建蒙版
        this.createMaskIfNeeded(node, ecsViewConfig);

        // 用真实节点替换占位节点
        this._placeholderManager.replacePlaceholder(logicEntityId, node);

        this._ecsViewMap.set(logicEntityId, view);

        // 通知视图创建完成
        this._logicWorld.eventBus.emit(ECSEvent.BaseEvent.RENDER_CREATED, logicEntityId);

        // 初始化视图状态
        this.initializeViewState(node, logicEntityId);

        return view;
    }

    /**
     * 创建蒙版（如果需要的话）
     */
    private async createMaskIfNeeded(node: cc.Node, ecsViewConfig: any): Promise<void> {
        if (!ecsViewConfig.mask) {
            return;
        }

        try {
            const maskPrefab: cc.Prefab = await this.loadRes(
                ecsViewConfig.mask.prefabBundleName || '',
                ecsViewConfig.mask.prefabUrl || 'prefabs/modal/Modal',
                cc.Prefab,
            );

            if (maskPrefab) {
                const maskNode = cc.instantiate(maskPrefab);
                maskNode.name = 'mask';

                // 设置蒙版透明度
                if (ecsViewConfig.mask.opacity !== undefined) {
                    maskNode.opacity = (ecsViewConfig.mask.opacity || 0.5) * 255;
                }

                // 将蒙版添加为子节点
                node.addChild(maskNode);

                // 确保蒙版在最后面
                maskNode.zIndex = -1000;
            }
        } catch (error) {
            console.error(`创建蒙版失败: ${ecsViewConfig.mask.prefabUrl}`, error);
        }
    }

    /**
     * 初始化视图状态
     */
    private initializeViewState(node: cc.Node, logicEntityId: number): void {
        this.updateEcsViewNodeComponent(node, logicEntityId);
        this.updateEcsViewOpacityComponent(node, logicEntityId);
    }

    /**
     * 处理视图创建失败
     */
    private handleViewCreationFailure(logicEntityId: number): void {
        this._destroyEntitySet.delete(logicEntityId);
        this._placeholderManager.removePlaceholder(logicEntityId);
        this._logicWorld.eventBus.emit(ECSEvent.BaseEvent.RENDER_CREATED_FAILED, logicEntityId);
    }

    /**
     * 销毁现有视图
     */
    private destroyExistingView(entityId: number): void {
        const view = this._ecsViewMap.get(entityId);
        if (view) {
            this.destroyECSView(view);
        }
    }

    /**
     * 销毁视图节点
     */
    private destroyViewNode(node: cc.Node): void {
        if (!cc.isValid(node)) {
            return;
        }
        node.removeFromParent();
        node.destroy();
    }

    /**获取渲染实体的世界坐标 */
    public getRenderEntityWorldPosition(entity: number, path?: string): IPoint {
        const node = this._ecsViewMap.get(entity)?.node;
        if (!node) return cc.Vec2.ZERO;
        const targetNode = path ? cc.find(path, node) : node;
        targetNode.convertToWorldSpaceAR(cc.Vec2.ZERO, this.tempOutV2);
        return { x: this.tempOutV2.x, y: this.tempOutV2.y };
    }

    /**获取渲染实体相对渲染根节点的坐标 */
    public getRenderEntityRootPosition(entity: number, path?: string): IPoint {
        const worldPos = this.getRenderEntityWorldPosition(entity, path);
        this.tempOutV2.x = worldPos.x;
        this.tempOutV2.y = worldPos.y;
        this._rootNode.convertToNodeSpaceAR(this.tempOutV2, this.tempOutV2);
        return { x: this.tempOutV2.x, y: this.tempOutV2.y };
    }
    private tempOutV2 = cc.v2();

    // ==================== 清理方法 ====================

    /**
     * 销毁所有ECS视图
     */
    private destroyAllECSViews(): void {
        this._ecsViewMap.forEach((view) => {
            this.destroyECSView(view);
        });
    }

    /**
     * 清理渲染命令
     */
    private cleanupRenderCommands(): void {
        this._renderCmds.forEach((cmd) => {
            if (cmd && cmd.isExecuting) {
                this._destroyEntitySet.add(cmd.entityId);
            }
        });
    }

    /**
     * 清理集合
     */
    private clearCollections(): void {
        this._ecsViewMap.clear();
        this._renderCmdMap.clear();
        this._renderCmds.length = 0;
    }

    /**
     * 清理管理器
     */
    private cleanupManagers(): void {
        if (this._placeholderManager) {
            this._placeholderManager.clearPool();
        }
    }

    // ==================== 组件更新 ====================

    /**
     * 更新ECS视图关键节点数据
     */
    private updateEcsView(): void {
        this._ecsViewMap.forEach((view, entityId) => {
            if (view.isIndependent()) {
                return;
            }

            const node = view.node;
            if (!cc.isValid(node)) {
                console.error(`节点不存在: ${entityId}, view: ${view?.constructor?.name}`);
                return;
            }

            this.updateEcsViewNodeComponent(node, entityId);
            this.updateEcsViewOpacityComponent(node, entityId);
        });
    }

    /**
     * 更新节点组件数据
     * @param node 节点
     * @param entityId 实体ID
     */
    updateEcsViewNodeComponent(node: cc.Node, entityId: number): void {
        const nodeComp = this._logicWorld.getComponent(entityId, NodeComponent);
        const transform = this._logicWorld.getComponent(entityId, UITransformComponent);
        if (!nodeComp) {
            return;
        }

        this.updateNodePosition(node, nodeComp);
        this.updateNodeScale(node, nodeComp);
        this.updateNodeRotation(node, nodeComp);
        this.updateNodeZIndex(node, nodeComp);
        if (transform) {
            this.updateTransform(node, transform);
        }
    }

    updateNodeZIndex(node: cc.Node, nodeComp: NodeComponent): void {
        if (node.zIndex !== nodeComp.zIndex) {
            node.zIndex = nodeComp.zIndex;
            // 触发父节点重新排序子节点
            if (node.parent) {
                node.parent.sortAllChildren();
            }
        }
    }

    updateTransform(node: cc.Node, nodeComp: UITransformComponent): void {
        if (node.width !== nodeComp.width || node.height !== nodeComp.height) {
            node.width = nodeComp.width;
            node.height = nodeComp.height;
        }
    }

    /**
     * 更新透明度组件数据
     * @param node 节点
     * @param entityId 实体ID
     */
    updateEcsViewOpacityComponent(node: cc.Node, entityId: number): void {
        const opacityComp = this._logicWorld.getComponent(entityId, OpacityComponent);
        if (!opacityComp) {
            return;
        }

        if (node.opacity !== opacityComp.opacity) {
            node.opacity = opacityComp.opacity;
        }
    }

    // ==================== 节点更新辅助方法 ====================

    /**
     * 更新节点位置
     */
    private updateNodePosition(node: cc.Node, nodeComp: any): void {
        const oldPos = node.position;
        if (oldPos.x !== nodeComp.x || oldPos.y !== nodeComp.y) {
            node.setPosition(nodeComp.x, nodeComp.y);
        }
    }

    /**
     * 更新节点缩放
     */
    private updateNodeScale(node: cc.Node, nodeComp: any): void {
        if (node.scaleX !== nodeComp.scaleX || node.scaleY !== nodeComp.scaleY) {
            node.setScale(nodeComp.scaleX, nodeComp.scaleY);
        }
    }

    /**
     * 更新节点旋转
     */
    private updateNodeRotation(node: cc.Node, nodeComp: any): void {
        if (node.angle !== nodeComp.angle) {
            node.angle = nodeComp.angle;
        }
    }

    // ==================== 工具方法 ====================

    /**
     * 等待指定时间（测试用）
     * @param time 等待时间（毫秒）
     * @returns Promise
     */
    private waitTime(time: number): Promise<void> {
        return new Promise((resolve) => {
            setTimeout(resolve, time);
        });
    }

    // ==================== 音效方法 ====================

    private __onPlayAudio(data: { audioId: AudioId | string, options?: Partial<falcon.IAudioInfoOption & { forcePlay?: boolean }> }): void {
        this.playAudio(data.audioId, data.options);
    }
    private __onStopAudio(data: { audioId: AudioId | string }): void {
        this.stopAudio(data.audioId);
    }
    /**
     * 播放音效
     * @param audioId 音效配置ID
     * @param options 覆盖选项
     * @returns 是否成功播放
     */
    public playAudio(audioId: AudioId | string, options?: Partial<falcon.IAudioInfoOption & { forcePlay?: boolean }>): boolean {
        // 获取音效配置
        const config = EcsAudioConfigHelper.getAudioConfig(audioId);
        if (!config) {
            console.warn(`音效配置不存在: ${audioId}`);
            return false;
        }
        // 检查音效开关
        if (!audioInfoData.audioSwitch && !options?.forcePlay) {
            console.log(`音效已关闭，跳过播放: ${audioId}`);
            return false;
        }
        // 合并配置选项
        const finalConfig = { ...config, ...options };
        try {
            // 构建falcon音效播放参数
            const audioOptions = {
                url: finalConfig.url,
                volume: finalConfig.volume || 1,
                type: finalConfig.type,
                bundleName: finalConfig.bundleName,
            };
            // 播放音效
            falcon.audioInfo.play(audioOptions);
            console.log(`播放音效成功: ${audioId}`);
            return true;
        } catch (error) {
            console.error(`播放音效失败: ${audioId}`, error);
            return false;
        }
    }

    /**
     * 停止音效
     * @param audioId 音效配置ID
     * @returns 是否成功停止
     */
    public stopAudio(audioId: AudioId | string): boolean {
        const config = EcsAudioConfigHelper.getAudioConfig(audioId);
        if (!config) {
            console.warn(`音效配置不存在: ${audioId}`);
            return false;
        }
        try {
            falcon.audioInfo.stop({
                url: config.url,
                type: config.type,
            });
            console.log(`停止音效成功: ${audioId}`);
            return true;
        } catch (error) {
            console.error(`停止音效失败: ${audioId}`, error);
            return false;
        }
    }

    // ==================== 全屏震动管理 ====================
    private __onFullScreenShake(data: GameEventMap[ECSEvent.BaseEvent.FULL_SCREEN_SHAKE]): void {
        this._fullScreenShakeManager?.startWithAmplitude(data.duration, data.amplitude, data.frequency);
    }
}

// ==================== 占位节点管理器 ====================

/**
 * 占位节点管理器
 * 负责管理异步加载过程中的占位节点，确保渲染顺序的正确性
 */
class PlaceholderManager {
    // ==================== 私有属性 ====================

    /** 实体ID -> 占位节点映射 */
    private _placeholders: Map<number, cc.Node> = new Map();

    /** ECS视图映射表引用 */
    private _ecsViewMap: Map<number, ECSViewBase>;

    /** 节点池 */
    private _nodePool: cc.NodePool;

    /** 渲染根节点 */
    private _rootNode: cc.Node;

    /** 路径占位节点映射：路径 -> 占位节点 */
    private _pathPlaceholders: Map<string, cc.Node> = new Map();

    // ==================== 构造函数 ====================

    /**
     * 构造占位节点管理器
     * @param ecsViewMap ECS视图映射表
     * @param rootNode 渲染根节点
     */
    constructor(ecsViewMap: Map<number, ECSViewBase>, rootNode: cc.Node) {
        this._ecsViewMap = ecsViewMap;
        this._nodePool = new cc.NodePool();
        this._rootNode = rootNode;
    }

    // ==================== 公共API ====================

    /**
     * 创建占位节点
     * @param entityId 实体ID
     * @param parentEntityId 父实体ID
     * @param children 子节点路径
     * @returns 占位节点
     */
    public createPlaceholder(entityId: number, parentEntityId: number, children: string[]): cc.Node {
        const parentNode = this.findParentNode(parentEntityId, children);
        if (!parentNode) {
            console.error(`父节点不存在: ${parentEntityId}`);
            return null;
        }

        // 检查目标位置是否已经有路径占位节点
        const targetPath = this.buildTargetPath(parentEntityId, children);
        const existingPathPlaceholder = this._pathPlaceholders.get(targetPath);

        if (existingPathPlaceholder && cc.isValid(existingPathPlaceholder)) {
            // 复用已存在的路径占位节点
            console.log(`复用路径占位节点作为实体 ${entityId} 的占位节点: ${targetPath}`);
            this._placeholders.set(entityId, existingPathPlaceholder);
            // 从路径占位映射中移除，因为现在它成为了实体占位节点
            this._pathPlaceholders.delete(targetPath);
            return existingPathPlaceholder;
        }

        // 没有现有的路径占位节点，创建新的实体占位节点
        const placeholder = this.getOrCreatePlaceholderNode();
        parentNode.addChild(placeholder);
        this._placeholders.set(entityId, placeholder);

        return placeholder;
    }

    /**
     * 构建目标路径
     * @param parentEntityId 父实体ID
     * @param children 子节点路径
     * @returns 完整的目标路径
     */
    private buildTargetPath(parentEntityId: number, children: string[]): string {
        let basePath: string;

        if (parentEntityId === 0) {
            basePath = this._rootNode.name;
        } else {
            const parentView = this.getParentView(parentEntityId);
            if (parentView) {
                basePath = `entity_${parentEntityId}`;
            } else {
                basePath = this._rootNode.name; // 父实体不存在，使用根节点
            }
        }

        if (!children || children.length === 0) {
            return basePath;
        }

        return `${basePath}/${children.join('/')}`;
    }

    /**
     * 替换占位节点
     * @param entityId 实体ID
     * @param realNode 真实节点
     */
    public replacePlaceholder(entityId: number, realNode: cc.Node): void {
        const placeholder = this._placeholders.get(entityId);
        if (!placeholder || !cc.isValid(placeholder)) {
            return;
        }

        const parent = placeholder.parent;
        if (!parent) {
            return;
        }

        // 检查是否有路径占位节点需要被这个真实节点替换
        this.checkAndReplacePathPlaceholders(realNode, entityId);

        this.replaceNodeAtSameIndex(placeholder, realNode, parent);
        this.recyclePlaceholder(placeholder);
        this._placeholders.delete(entityId);
    }

    /**
     * 检查并替换相关的路径占位节点
     * @param realNode 真实节点
     * @param entityId 实体ID
     */
    private checkAndReplacePathPlaceholders(realNode: cc.Node, entityId: number): void {
        const entityPath = `entity_${entityId}`;

        // 查找所有以这个实体为起始路径的路径占位节点
        const pathsToReplace: string[] = [];
        this._pathPlaceholders.forEach((pathPlaceholder, path) => {
            if (path.startsWith(entityPath + '/')) {
                pathsToReplace.push(path);
            }
        });

        // 替换这些路径占位节点
        pathsToReplace.forEach((path) => {
            const pathPlaceholder = this._pathPlaceholders.get(path);
            if (pathPlaceholder && cc.isValid(pathPlaceholder)) {
                // 计算相对路径
                const relativePath = path.substring(entityPath.length + 1);
                const targetNode = this.findNodeByPath(realNode, relativePath);

                if (targetNode) {
                    this.replacePathPlaceholder(targetNode, path);
                } else {
                    console.warn(`无法在真实节点中找到路径: ${relativePath}`);
                }
            }
        });
    }

    /**
     * 根据路径查找节点
     * @param startNode 起始节点
     * @param path 相对路径
     * @returns 找到的节点或null
     */
    private findNodeByPath(startNode: cc.Node, path: string): cc.Node | null {
        if (!path) {
            return startNode;
        }

        const pathParts = path.split('/');
        let currentNode = startNode;

        for (const part of pathParts) {
            currentNode = currentNode.getChildByName(part);
            if (!currentNode) {
                return null;
            }
        }

        return currentNode;
    }

    /**
     * 移除占位节点
     * @param entityId 实体ID
     */
    public removePlaceholder(entityId: number): void {
        const placeholder = this._placeholders.get(entityId);
        if (placeholder && cc.isValid(placeholder)) {
            placeholder.removeFromParent();
            this._nodePool.put(placeholder);
        }
        this._placeholders.delete(entityId);
    }

    /**
     * 清理节点池
     */
    public clearPool(): void {
        this._nodePool.clear();
        this.clearPathPlaceholders();
    }

    // ==================== 私有方法 ====================

    /**
     * 查找父节点
     */
    private findParentNode(parentEntityId: number, children: string[]): cc.Node {
        const parentView = this.getParentView(parentEntityId);
        let parentNode: cc.Node = (parentView && parentView.node) || this._rootNode;

        if (!parentNode) {
            return null;
        }

        return this.navigateToChildNode(parentNode, children);
    }

    /**
     * 获取或创建占位节点
     */
    private getOrCreatePlaceholderNode(): cc.Node {
        if (this._nodePool.size() > 0) {
            return this._nodePool.get();
        }

        return this.createNewPlaceholderNode();
    }

    /**
     * 创建新的占位节点
     */
    private createNewPlaceholderNode(): cc.Node {
        const placeholder = new cc.Node('Placeholder');
        placeholder.width = 0;
        placeholder.height = 0;
        placeholder.opacity = 0; // 完全透明
        return placeholder;
    }

    /**
     * 在相同索引位置替换节点（包含子节点迁移）
     */
    private replaceNodeAtSameIndex(placeholder: cc.Node, realNode: cc.Node, parent: cc.Node): void {
        const index = parent.children.indexOf(placeholder);

        // 将占位节点的所有子节点迁移到真实节点
        this.migrateChildrenNodes(placeholder, realNode);

        // 替换节点
        placeholder.removeFromParent();
        parent.insertChild(realNode, index);
    }

    /**
     * 迁移子节点
     * @param fromNode 源节点（占位节点）
     * @param toNode 目标节点（真实节点）
     */
    private migrateChildrenNodes(fromNode: cc.Node, toNode: cc.Node): void {
        if (!fromNode || !toNode) {
            return;
        }

        // 获取所有子节点的副本，避免在迭代过程中修改数组
        const children = [...fromNode.children];

        if (children.length > 0) {
            console.log(`迁移 ${children.length} 个子节点从占位节点到真实节点`);
        }

        // 直接迁移所有子节点
        children.forEach((child) => {
            if (cc.isValid(child)) {
                child.removeFromParent();
                toNode.addChild(child);
            }
        });
    }

    /**
     * 回收占位节点
     */
    private recyclePlaceholder(placeholder: cc.Node): void {
        this._nodePool.put(placeholder);
    }

    /**
     * 获取父视图
     * @param parentEntityId 父实体ID
     * @returns 父视图或null
     */
    private getParentView(parentEntityId: number): ECSViewBase | null {
        return this._ecsViewMap.get(parentEntityId);
    }

    /**
     * 导航到子节点（支持路径占位创建）
     */
    private navigateToChildNode(parentNode: cc.Node, children: string[]): cc.Node {
        if (!children || children.length === 0) {
            return parentNode;
        }

        let currentNode = parentNode;
        let currentPath = this.getNodePath(parentNode);

        for (let i = 0; i < children.length; i++) {
            const childName = children[i];
            if (!currentNode) {
                console.error(`父节点为空，无法继续查找子节点路径: ${children.slice(0, i).join('/')}`);
                return null;
            }

            // 构建当前子节点的完整路径
            const childPath = `${currentPath}/${childName}`;

            // 先尝试找到真实的子节点
            let nextNode = currentNode.getChildByName(childName);

            if (!nextNode) {
                // 检查是否已经有路径占位节点
                nextNode = this._pathPlaceholders.get(childPath);

                if (!nextNode) {
                    // 创建新的路径占位节点
                    console.log(`创建路径占位节点: ${childPath}`);
                    nextNode = this.createPathPlaceholderNode(childName);
                    currentNode.addChild(nextNode);
                    this._pathPlaceholders.set(childPath, nextNode);
                } else {
                    console.log(`使用已存在的路径占位节点: ${childPath}`);
                }
            }

            currentNode = nextNode;
            currentPath = childPath;
        }

        return currentNode;
    }

    /**
     * 获取节点的路径标识
     */
    private getNodePath(node: cc.Node): string {
        if (node === this._rootNode) {
            return 'root';
        }

        // 尝试从ECS视图映射中找到对应的实体ID
        for (const [entityId, view] of this._ecsViewMap) {
            if (view.node === node) {
                return `entity_${entityId}`;
            }
        }

        // 如果找不到，使用节点名称
        return node.name || 'unknown';
    }

    /**
     * 创建路径占位节点
     */
    private createPathPlaceholderNode(name: string): cc.Node {
        const placeholder = new cc.Node(`PathPlaceholder_${name}`);
        placeholder.width = 0;
        placeholder.height = 0;
        placeholder.opacity = 0; // 完全透明
        // 添加标记，表示这是路径占位节点
        placeholder['__isPathPlaceholder'] = true;
        return placeholder;
    }

    /**
     * 替换路径占位节点
     * @param realNode 真实节点
     * @param targetPath 目标路径
     */
    public replacePathPlaceholder(realNode: cc.Node, targetPath: string): void {
        const placeholder = this._pathPlaceholders.get(targetPath);
        if (!placeholder || !cc.isValid(placeholder)) {
            return;
        }

        const parent = placeholder.parent;
        if (!parent) {
            return;
        }

        console.log(`替换路径占位节点: ${targetPath}`);

        // 将占位节点的所有子节点转移到真实节点
        const children = [...placeholder.children];
        children.forEach((child) => {
            child.removeFromParent();
            realNode.addChild(child);
        });

        // 替换占位节点
        this.replaceNodeAtSameIndex(placeholder, realNode, parent);

        // 清理占位节点记录
        this._pathPlaceholders.delete(targetPath);
        placeholder.destroy();
    }

    /**
     * 清理路径占位节点
     */
    public clearPathPlaceholders(): void {
        this._pathPlaceholders.forEach((placeholder) => {
            if (cc.isValid(placeholder)) {
                placeholder.destroy();
            }
        });
        this._pathPlaceholders.clear();
    }
}
