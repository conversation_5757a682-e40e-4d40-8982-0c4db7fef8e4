import { 
    IRemoteUrlConfig, 
    ISubGameConfig, 
    IAdvGameBridgeBaseData,
    IAdvGameBridgeGameData,
} from '../../../../typings/AdvGameBridge';
import AdvGameBridge from '../../../../typings/AdvGameBridge';
import { game } from '../modules/level/Game';

// 获取 BundleNameType 类型
type BundleNameType = falcon.BundleNameType;

/**
 * AdvGameBridge 在子游戏中使用桥接类
 * 提供子游戏与主游戏之间的通信接口
 */
export class SubGameBridge {
    private static bridge: AdvGameBridge | null = null;
    private static isInitialized: boolean = false;
    private static remoteConfig: IRemoteUrlConfig = null;
    /**默认bundle名称 */
    public static readonly DEFAULT_BUNDLE_NAME = 'advres';
    public static readonly NEED_REMOTE = false;
    
    /**
     * 获取桥接实例
     */
    private static getBridge(): AdvGameBridge | null {
        if (!this.bridge) {
            // 从window对象获取桥接类实例
            this.bridge = (window as any).AdvGameBridge;
            
            if (!this.bridge) {
                if (this.NEED_REMOTE) {
                    console.error('[SubGameBridge] AdvGameBridge not found on window object!');
                }
                return null;
            }
        }
        
        return this.bridge;
    }
    
    /**
     * 子游戏初始化时调用
     */
    public static async onSubGameStart(): Promise<boolean> {
        const bridge = this.getBridge();
        if (!bridge) {
            return false;
        }
        
        try {
            // 获取主游戏传递的基础数据
            const baseData = bridge.getBaseData();
            console.log('[SubGameBridge] Received base data from main game:', baseData);
            
            // 获取远程配置
            this.remoteConfig = bridge.getRemoteUrlConfig();
            console.log('[SubGameBridge] Remote config:', this.remoteConfig);
            
            // 设置主游戏消息监听
            this.setupMainGameMessageListener();
            
            // 使用主游戏数据初始化子游戏
            this.initSubGameWithMainData(baseData);
            
            this.isInitialized = true;
            return true;
            
        } catch (error) {
            console.error('[SubGameBridge] Initialization failed:', error);
            return false;
        }
    }
    
    /**
     * 设置主游戏消息监听
     */
    private static setupMainGameMessageListener(): void {
        // 使用类型安全的消息监听器
        this.setMessageListener((type: string, data: any) => {
            if (type === 'test') {
                // todo 有需求可以添加
            }
        });
        console.log('[SubGameBridge] Main game message listener set up successfully');
    }
    
    /**
     * 使用主游戏数据初始化子游戏
     */
    private static initSubGameWithMainData(data: IAdvGameBridgeBaseData): void {
        console.log('[SubGameBridge] Initializing sub game with main data:', data);
        // todo 有需求可以添加
    }
    
    /**
     * 子游戏向主游戏发送消息
     */
    public static sendToMainGame(type: string, data?: any): void {
        const bridge = this.getBridge();
        
        if (bridge && typeof bridge.sendToMainGame === 'function') {
            bridge.sendToMainGame(type, data);
            console.log(`[SubGameBridge] Sent message to main game:`, { type, data });
        } else {
            console.warn('[SubGameBridge] Cannot send message to main game - bridge not available');
        }
    }
    
    /**
     * 返回主游戏
     */
    public static returnToMainGame(): void {
        const bridge = this.getBridge();
        if (bridge && typeof bridge.returnToMainGame === 'function') {
            // 发送返回通知
            this.sendToMainGame('beforeReturn', { timestamp: Date.now() });
            // 场景销毁前，先释放掉逻辑世界和渲染世界
            game.dispose();
            falcon.gameStateManager.onGameExit();
            // 执行返回
            bridge.returnToMainGame();
            console.log('[SubGameBridge] Returning to main game');
        } else {
            console.warn('[SubGameBridge] Cannot return to main game - bridge not available');
        }
    }
    
    /**
     * 检查是否已初始化
     */
    public static isReady(): boolean {
        return this.isInitialized && this.getBridge() !== null;
    }
    
    /**
     * 获取桥接版本信息
     */
    public static getBridgeVersion(): string {
        const bridge = this.getBridge();
        if (bridge && typeof bridge.getBridgeVersion === 'function') {
            return bridge.getBridgeVersion();
        }
        return 'unknown';
    }
    
    /**
     * 销毁桥接连接
     */
    public static destroy(): void {
        const bridge = this.getBridge();
        if (bridge && typeof bridge.offMessageFromMainGame === 'function') {
            bridge.offMessageFromMainGame();
        }
        
        this.bridge = null;
        this.isInitialized = false;
        this.remoteConfig = null;
        console.log('[SubGameBridge] Bridge destroyed');
    }

    // ================================
    // 类型安全的数据访问方法
    // ================================

    /**
     * 获取基础数据
     * @returns 基础数据对象或null
     */
    public static getBaseData(): IAdvGameBridgeBaseData | null {
        const bridge = this.getBridge();
        if (bridge && typeof bridge.getBaseData === 'function') {
            return bridge.getBaseData();
        }
        return null;
    }

    /**
     * 获取当前游戏数据
     * @returns 游戏数据对象或null
     */
    public static getCurrentGameData(): IAdvGameBridgeGameData | null {
        const bridge = this.getBridge();
        if (bridge && typeof bridge.getCurrentGameData === 'function') {
            return bridge.getCurrentGameData();
        }
        return null;
    }

    /**
     * 设置消息监听器（类型安全版本）
     * @param callback 消息回调函数
     */
    public static setMessageListener(callback: (type: string, data: any) => void): void {
        const bridge = this.getBridge();
        if (bridge && typeof bridge.onMessageFromMainGame === 'function') {
            bridge.onMessageFromMainGame(callback);
        }
    }

    /**
     * 移除消息监听器
     */
    public static removeMessageListener(): void {
        const bridge = this.getBridge();
        if (bridge && typeof bridge.offMessageFromMainGame === 'function') {
            bridge.offMessageFromMainGame();
        }
    }

    /**
     * 获取远程配置
     */
    public static getRemoteConfig(): IRemoteUrlConfig {
        if (!this.remoteConfig) {
            const bridge = this.getBridge();
            if (bridge && typeof bridge.getRemoteUrlConfig === 'function') {
                this.remoteConfig = bridge.getRemoteUrlConfig();
            }
        }
        return this.remoteConfig;
    }


    /**
     * 提取并标准化bundle配置对象
     * 
     * @param bundleNameType 可选的bundle名称或配置对象
     * @returns falcon.BundleConfig 标准化的bundle配置对象
     * @private
     */
    private static extractBundleConfig(bundleNameType?: BundleNameType): falcon.BundleConfig {
        // 如果没有传入参数或为空字符串，使用默认值
        if (!bundleNameType || bundleNameType === '') {
            return { bundleName: SubGameBridge.DEFAULT_BUNDLE_NAME };
        }
        
        // 如果是字符串，直接返回
        if (typeof bundleNameType === 'string') {
            return { bundleName: bundleNameType };
        }
        
        // bundleName
        const result: falcon.BundleConfig = {
            bundleName: bundleNameType.bundleName || SubGameBridge.DEFAULT_BUNDLE_NAME
        };
        
        // 如果包含version信息，填充到结果中
        if (bundleNameType.version) {
            result.version = bundleNameType.version;
        }
        return result;
    }


    /**
     * 获取子游戏配置
     */
    public static getSubGameConfig(bundleName: string): ISubGameConfig | null {
        const bridge = this.getBridge();
        if (bridge && typeof bridge.getSubGameConfig === 'function') {
            return bridge.getSubGameConfig(bundleName);
        }
        return null;
    }

   /**
    * 获取子游戏配置
    * @param bundleNameType bundle名称，如果为空则使用默认值
    * @returns ISubGameConfig 配置对象
    */
   public static getBundleConfig(bundleNameType?: BundleNameType): falcon.BundleConfig {
        const bundleCfg = this.extractBundleConfig(bundleNameType);
        
        // 如果不需要远程，直接返回本地配置
        if (!SubGameBridge.NEED_REMOTE) {
            return {
                bundleName: bundleCfg.bundleName,
                version: bundleCfg.version,
            };
        }
        // 尝试获取远程配置
        const remoteConfig = SubGameBridge.getRemoteConfig();
        const bundleConfig = SubGameBridge.getSubGameConfig(bundleCfg.bundleName);
        
        return {
            bundleName: remoteConfig 
                ? `${remoteConfig.baseUrl}${bundleCfg.bundleName}` 
                : bundleCfg.bundleName,
            version: bundleCfg.version || bundleConfig?.version,
        };
    }

    /**
     * 封装的异步加载资源方法（通过bundle）
     * @param bundleName bundle名称
     * @param url 资源路径
     * @param type 资源类型（可选）
     * @param options bundle选项（包含MD5版本等）
     * @returns Promise<T>
     */
    public static asyncLoadByBundle<T extends cc.Asset = cc.Asset>(bundleNameType: BundleNameType, url: string, type?: typeof cc.Asset): Promise<T> {
        // console.log(`asyncLoadByBundle bundleNameType = ${bundleNameType} url = ${url}`);
        if (bundleNameType == '') {
            console.log(`asyncLoadByBundle url = ${url} bundleNameType == '' need to check`);
        }
        const bundleConfig = this.getBundleConfig(bundleNameType);
        if (type) {
            return falcon.ResLoader.asyncLoadByBundle<T>(bundleConfig, url, type);
        }
        return falcon.ResLoader.asyncLoadByBundle<T>(bundleConfig, url, undefined);
    }

    public static async asyncLoad<T extends cc.Asset>(paths: string, type: typeof cc.Asset): Promise<T> {
        console.error(`SubGameBridge.asyncLoad: bundleName is not specified for file '${paths}'. Please verify the file path and use asyncLoadByBundle instead.`);
        return new Promise<T>((resolve, reject) => {
            reject();
        });
    }

    /**
     * 封装的同步加载资源方法（通过bundle）
     * @param bundleName bundle名称
     * @param url 资源路径
     * @param type 资源类型
     * @param callback 回调函数
     * @param options bundle选项（包含MD5版本等）
     */
    public static loadByBundle<T extends cc.Asset = cc.Asset>(
        bundleName: BundleNameType,
        url: string,
        type: typeof cc.Asset,
        callback: (err: Error, resource: T) => void,
    ): void {
        // console.log(`loadByBundle bundleNameType = ${bundleName} url = ${url}`);
        if (bundleName == '') {
            console.log(`loadByBundle bundleNameType == '' need to check`);
        }
        const bundleConfig = this.getBundleConfig(bundleName);
        falcon.ResLoader.loadByBundle(bundleConfig, url, type, (err: Error, asset: cc.Asset) => {
            callback(err, asset as T);
        });
    }

    /**
     * 封装的加载bundle方法
     * @param bundleName bundle名称
     * @param optionsOrCallback 选项或回调函数
     * @param callback 回调函数（当第二个参数是options时使用）
     */
    public static loadBundle(bundleName: BundleNameType, callback?: (err: Error, bundle: cc.AssetManager.Bundle) => void): void {
        const bundleConfig = this.getBundleConfig(bundleName);
        falcon.ResLoader.loadBundle(bundleConfig, callback);
    }

    /** 
     * 封装的渲染sprite方法，自动处理bundleName的远程地址和版本信息
     * @param sprite sprite组件
     * @param url 资源路径
     * @param bundleNameType bundle名称
     */
    static renderSpriteByBundle(sprite: cc.Sprite, url: string, bundleNameType: BundleNameType) {
        falcon.ResLoader.renderSpriteByBundle(sprite, url, bundleNameType);
    }

    /**
     * 封装的bundle预加载方法
     * @param bundle bundle对象
     * @param url 资源路径
     * @param type 资源类型
     * @param callback 回调函数
     */
    public static bundlePreload(bundle: cc.AssetManager.Bundle, url: string, type: typeof cc.Asset, callback: (err: Error) => void): void {
        falcon.ResLoader.bundlePreload(bundle, url, type, callback);
    }

    /**
     * 封装的UI显示方法，自动处理bundleName的远程地址
     * @param config 预制体配置对象
     * @param parent 父节点（可选）
     * @returns Promise<cc.Node>
     */
    public static async showUI(config: any, parent?: cc.Node): Promise<cc.Node> {
        const bundleConfig = this.getBundleConfig(config.bundleName);
        const processedConfig = {
            ...config,
        };
        if (bundleConfig.version) {
            processedConfig.version = bundleConfig.version;
        }
        if (bundleConfig.bundleName) {
            processedConfig.bundleName = bundleConfig.bundleName;
        }
        return falcon.UI.show(processedConfig, parent);
    }

    /**
     * 封装的龙骨动画渲染方法，自动处理bundleName的远程地址和版本信息
     * @param bundleName bundle名称
     * @param dragonBonesArmatureDisplay 龙骨显示组件
     * @param dragonAssetUrl 龙骨资源URL
     * @param dragonAssetAtlasUrl 龙骨图集URL
     * @param armatureName 骨架名称
     * @param animName 动画名称
     * @param playtimes 播放次数
     * @param completeRemove 完成后是否移除
     * @param options bundle选项（包含MD5版本等）
     */
    public static renderDragonbonesByBundle(
        bundleName: BundleNameType,
        dragonBonesArmatureDisplay: dragonBones.ArmatureDisplay,
        dragonAssetUrl: string,
        dragonAssetAtlasUrl: string,
        armatureName: string,
        animName: string,
        playtimes: number = 1,
        completeRemove: boolean = true,
    ): void {
        const bundleConfig = this.getBundleConfig(bundleName);
        falcon.ResLoader.renderDragonbonesByBundle(bundleConfig, dragonBonesArmatureDisplay, dragonAssetUrl, dragonAssetAtlasUrl, armatureName, animName, playtimes, completeRemove);
    }
}
