/**关卡配置信息 */
class ChapterConfigInfo {
    /**关卡选择列表配置数据 马赛克*/
    private _listData: number[][];

    /**获取关卡选择列表配置 */
    get chapterListCfg() {
        return this._listData || [];
    }

    // 是否需要强制绘制
    private _isForceDraw: boolean = false;
    get isForceDraw() {
        return this._isForceDraw;
    }

    /**
     * 是否远程读取配置
     */
    get isRemote() {
        return true;
    }

    /** 获取总关卡数 */
    get chapterAllNum(): number {
        return this._chapterAllNum;
    }

    /**设置配置数据 */
    setListData(data: number[][]) {
        this._listData = data;
        this._chapterAllNum = 0;
        this.chapterListCfg.forEach((v) => v.forEach((v2) => this._chapterAllNum++));
    }
    private _chapterAllNum: number;

    /**是否通关 */
    get isThroughAll(): boolean {
        return falcon.storage.getItem('chapterNum', 0) >= this.chapterAllNum;
    }
}
export const chapterConfigInfo = new ChapterConfigInfo();
