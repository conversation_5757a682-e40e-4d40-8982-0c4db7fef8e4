import { BoardScene } from '../components/board/BoardScene';
import TargetComponent from '../components/special/TargetComponent';
import BoardComponent from '../components/board/BoardComponent';
import { TempleteType } from '../registry/templete/TempleteRegistry';
import { MathUtil } from '../utils/MathUtil';
import { ECSRuleBase } from './core/ECSRuleBase';
import { SimulationWorldManager } from '../simulation/SimulationWorldManager';
import { World } from '../cores/World';
import { RowCol, ShapeMargin } from '../define/EcsDefine';
import { CellType, NormalColor7, TargetType } from '../define/BoardDefine';
import { RcShape } from '../define/EcsConfig';

interface ShapeData {
    shape: RowCol[];
    width: number;
    height: number;
}

interface BlockInfo {
    rcShape: ShapeData;
    predictedScore: number;
    simulationResult: unknown;
}

/**
 * 智能出块规则
 * 负责生成游戏方块，支持模拟测试优化
 */
export class SmartProduceBlockRule extends ECSRuleBase {
    private simulationManager: SimulationWorldManager;
    private readonly CANDIDATE_SHAPES = [1, 2, 3, 7, 9];

    /** 检查是否可以执行 */
    canExecute(): boolean {
        return true;
    }

    /** 执行规则，生成方块 */
    execute(): void {
        const world = this.world;
        const sceneEntity = world.ECSLayerType.SceneLayerEntity;
        const collectTypes = this.getCollectTypes(world);

        // 先生成默认方块，然后异步优化
        this.generateQuickBlocks(world, sceneEntity, collectTypes);
        this.optimizeBlocksAsync(world, sceneEntity, collectTypes);
    }

    /** 获取收集目标类型 */
    private getCollectTypes(world: World): CellType[] {
        const cellHandler = world.handlerCenter.cellHandler;
        return world
            .getComponent(world.query([TargetComponent])[0], TargetComponent)
            .targets.filter((t) => cellHandler.isCell(t.key))
            .map((t) => t.key as CellType);
    }

    /** 快速生成方块 */
    private generateQuickBlocks(world: World, sceneEntity: number, collectTypes: CellType[]): void {
        for (let i = 0; i < 3; i++) {
            const randomIndex = MathUtil.randomInt(this.CANDIDATE_SHAPES.length - 1, 0);
            const randomShape = RcShape[this.CANDIDATE_SHAPES[randomIndex]];

            const blockInfo: BlockInfo = {
                rcShape: randomShape,
                predictedScore: 100,
                simulationResult: null,
            };

            this.createBlockEntity(world, sceneEntity, blockInfo, i, collectTypes);
        }
    }

    /** 异步优化方块选择 */
    private async optimizeBlocksAsync(world: World, sceneEntity: number, collectTypes: CellType[]): Promise<void> {
        // 创建模拟世界
        if (!this.simulationManager) {
            this.simulationManager = new SimulationWorldManager(world);
        }
        const simStarted = await this.simulationManager.startSimulationAsync();
        if (!simStarted) return;
        // 测试并选择最佳方块组合
        const optimizedBlocks = await this.findBestBlockCombination(collectTypes);
        // 清理模拟世界
        this.simulationManager.endSimulation();
    }

    /** 寻找最佳方块组合 */
    private async findBestBlockCombination(collectTypes: CellType[]): Promise<BlockInfo[]> {
        const results: BlockInfo[] = [];
        for (let i = 0; i < 3; i++) {
            let bestShape: ShapeData | null = null;
            let bestScore = -1;
            // 测试每个候选形状
            for (const shapeIndex of this.CANDIDATE_SHAPES) {
                const shape = RcShape[shapeIndex];
                const score = this.testShapeScore(shape);

                if (score > bestScore) {
                    bestScore = score;
                    bestShape = shape;
                }
            }
            if (bestShape) {
                results.push({
                    rcShape: bestShape,
                    predictedScore: bestScore,
                    simulationResult: null,
                });
            }
            // 恢复状态用于下一个方块测试
            if (i < 2) {
                this.simulationManager.restoreToInitialState();
            }
        }

        return results;
    }

    /** 测试形状得分 */
    private testShapeScore(shape: ShapeData): number {
        const placableBoardId = this.getPlacableBoardId();
        if (!placableBoardId) return -1000;
        // 测试几个关键位置
        const testPositions = this.getTestPositions(shape);
        for (const pos of testPositions) {
            const placed = this.simulationManager.simulatePlaceBlock({
                shape: shape,
                x: pos.x,
                y: pos.y,
                boardId: placableBoardId,
                entityId: 0,
            });

            if (placed) {
                return 100 + Math.random() * 50; // 基础分数加随机变化
            }
        }

        return -1000; // 无法放置
    }

    /** 获取测试位置 */
    private getTestPositions(shape: ShapeData): Array<{ x: number; y: number }> {
        const positions = [];
        // 优先测试中心区域
        for (let row = 2; row < 6 && positions.length < 8; row++) {
            for (let col = 2; col < 6 && positions.length < 8; col++) {
                if (row + shape.height <= 8 && col + shape.width <= 8) {
                    positions.push({ x: col, y: row });
                }
            }
        }
        return positions;
    }

    /** 获取可放置的棋盘名称 */
    private getPlacableBoardId(): string | null {
        if (!this.simulationManager?.isInSimulation()) return null;
        const simulationWorld = this.simulationManager.getSimulationWorld();
        const boardEntities = simulationWorld.query([BoardComponent]);
        for (const boardEntity of boardEntities) {
            const boardComp = simulationWorld.getComponent(boardEntity, BoardComponent);
            if (boardComp?.isCanPut) {
                return boardComp.boardId;
            }
        }
        return null;
    }

    /** 创建方块实体 */
    private createBlockEntity(world: World, sceneEntity: number, blockInfo: BlockInfo, index: number, collectTypes: CellType[]): void {
        const rcShape = blockInfo.rcShape;
        // 创建形状实体
        const shapeEntity = world.templeteCenter.createTempleteEntity(TempleteType.Shape, {
            rcShape: rcShape,
            x: (index - 1) * ShapeMargin,
            parentEntity: sceneEntity,
        });

        const color = NormalColor7[MathUtil.randomInt(6, 0)];

        // 创建方块的每个单元格
        rcShape.shape.forEach((rc) => {
            const isCollect = this.shouldGenerateCollectCell(collectTypes, blockInfo);
            let type: CellType;
            if (isCollect) {
                type = collectTypes[MathUtil.randomInt(collectTypes.length - 1, 0)];
            } else if (Math.random() < 0.1) {
                type = TargetType.Plane;
            } else {
                type = TargetType.Normal;
            }
            const slotEntity = world.templeteCenter.createTempleteEntity(TempleteType.Slot, {
                rc,
                parentSize: rcShape,
                parentEntity: shapeEntity,
            });
            world.templeteCenter.createTempleteEntity(TempleteType.Cell, {
                rc,
                parentSize: rcShape,
                parentEntity: shapeEntity,
                cellOption: { color, type },
                slotEntity,
            });
        });

        // 将方块添加到待放置列表
        let boardSceneComp = world.getComponent(sceneEntity, BoardScene)!;
        boardSceneComp.waitPutCells.push(shapeEntity);
    }

    /** 判断是否生成收集块 */
    private shouldGenerateCollectCell(collectTypes: CellType[], blockInfo: BlockInfo): boolean {
        if (collectTypes.length === 0) return false;
        const baseProbability = 0.4;
        const bonusProbability = Math.min(blockInfo.predictedScore / 1000, 0.3);
        const finalProbability = Math.min(baseProbability + bonusProbability, 0.8);
        return Math.random() < finalProbability;
    }
}
