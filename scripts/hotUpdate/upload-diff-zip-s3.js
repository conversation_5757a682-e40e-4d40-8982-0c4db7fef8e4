const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 获取命令行参数
const platform = process.argv[2];
const env = process.argv[3];
const s3Bucket = process.argv[4] || 'afafb-and';
const s3Root = process.argv[5] || 'blockblast-adv';

if (!platform || !env) {
    console.error('使用方法: node upload-diff-zip-s3.js <platform> <env> [s3-bucket]');
    console.error('示例: node upload-diff-zip-s3.js android prod afafb-and');
    process.exit(1);
}

// 构建路径
const BLOCK_BLAST_BASE_URL = '/Users/<USER>/blockblast-adv';
const HOT_UPDATE_DIR = path.join(BLOCK_BLAST_BASE_URL, 'hotUpdate', platform, env);
const hotDiffPath = path.join(HOT_UPDATE_DIR, 'hotDiff.json');

console.log(`[${new Date().toISOString()}] 开始上传增量包到 S3...`);
console.log(`- 平台: ${platform}`);
console.log(`- 环境: ${env}`);
console.log(`- S3 存储桶: ${s3Bucket}`);
console.log(`- 热更新目录: ${HOT_UPDATE_DIR}`);

/**
 * 上传增量包到S3
 */
function uploadDiffZipsToS3() {
    // 检查 hotDiff.json 文件是否存在
    if (!fs.existsSync(hotDiffPath)) {
        console.warn(`警告: hotDiff.json 文件不存在: ${hotDiffPath}`);
        console.log('跳过增量包上传');
        return;
    }

    try {
        // 读取并解析 hotDiff.json
        const hotDiffData = JSON.parse(fs.readFileSync(hotDiffPath, 'utf8'));

        if (!Array.isArray(hotDiffData) || hotDiffData.length === 0) {
            console.log('hotDiff.json 中没有增量包数据，跳过上传');
            return;
        }

        console.log(`发现 ${hotDiffData.length} 个增量包需要上传`);

        let successCount = 0;
        let failCount = 0;

        // 遍历每个增量包
        hotDiffData.forEach((item, index) => {
            if (!item || !item.version) {
                console.warn(`跳过无效的增量包数据 [${index}]:`, item);
                return;
            }

            const versionKey = item.version;
            const zipDir = path.join(HOT_UPDATE_DIR, 'zips', versionKey);

            // 检查增量包目录是否存在
            if (!fs.existsSync(zipDir)) {
                console.warn(`警告: 增量包目录不存在: ${zipDir}`);
                failCount++;
                return;
            }

            // 构建S3路径
            const s3Path = `s3://${s3Bucket}/${s3Bucket}/hotUpdate/zips/${versionKey}/`;
            const cmd = `/opt/homebrew/bin/aws s3 sync "${zipDir}/" "${s3Path}"`;

            console.log(`[${index + 1}/${hotDiffData.length}] 上传增量包: ${versionKey}`);
            console.log(`  本地路径: ${zipDir}`);
            console.log(`  S3路径: ${s3Path}`);

            try {
                // 检查目录内容
                const files = fs.readdirSync(zipDir);
                console.log(`  包含文件: ${files.join(', ')}`);

                // 执行上传命令
                execSync(cmd, { stdio: 'inherit' });

                console.log(`  ✓ 增量包 ${versionKey} 上传成功`);
                successCount++;

            } catch (error) {
                console.error(`  ✗ 增量包 ${versionKey} 上传失败:`, error.message);
                failCount++;
            }
        });

        // 输出统计信息
        console.log('\n================= 上传统计 =================');
        console.log(`总计: ${hotDiffData.length} 个增量包`);
        console.log(`成功: ${successCount} 个`);
        console.log(`失败: ${failCount} 个`);

        if (failCount > 0) {
            console.log('存在上传失败的增量包，请检查日志');
            process.exit(1);
        } else {
            console.log('所有增量包上传成功！');
        }

    } catch (error) {
        console.error('解析 hotDiff.json 文件失败:', error.message);
        process.exit(1);
    }
}

// 主函数
function main() {
    try {
        uploadDiffZipsToS3();
        console.log(`[${new Date().toISOString()}] 增量包上传完成`);
    } catch (error) {
        console.error('增量包上传过程中发生错误:', error.message);
        process.exit(1);
    }
}

// 运行主函数
main(); 