import { E_Launch_Start } from "../../launch/events/E_Launch_Start";
import { algorithmDataInfoKeys } from "../config/AlgorithmConfig";
import { ratioConfig } from "../data/RatioConfig";
import { PuzzleUtil } from "../list/algoSDK/PuzzleUtil";
import { algorithmDataStatistics } from "../vo/AlgorithmDataStatistics";

export class Algorithm_Init_Proxy extends falcon.Proxy {
    protected onInit(): void {
        // 初始化数量
        this.addEventListener();
    }

    addEventListener() {
        falcon.UI.addEventListener('open', (type) => {
            if (type.name === "ClassGame" || type.name === "ChapterGame") {
                this.initDataStatistics();
            }
        });
    }

    registerEvents(): { new(...args: any): falcon.ModuleEvent; }[] | null {
        return [
            E_Launch_Start
        ];
    }

    receivedEvents($event: falcon.ModuleEvent): void {
        switch ($event.getClass()) {
            case E_Launch_Start:
                // 初始化算法SDK
                this.onAlgorithmInit($event as E_Launch_Start);
                break;
        }
    }

    onAlgorithmInit(event: E_Launch_Start) {
        PuzzleUtil.init({ ratioJson: ratioConfig, seed: Math.floor(Math.random() * 1000000) });
    }

    // 初始化数据
    initDataStatistics() {
        let dataInfo = algorithmDataStatistics.algorithmDataStatistics;
        let isSaveData = false;
        if (!dataInfo) {
            dataInfo = [];
        }
        algorithmDataInfoKeys.forEach(key => {
            if (dataInfo[key] === undefined) {
                isSaveData = true;
                dataInfo[key] = [];
            }
        });
        if (isSaveData) {
            algorithmDataStatistics.setAlgorithmDataStatistics(dataInfo);
        }
    }
}